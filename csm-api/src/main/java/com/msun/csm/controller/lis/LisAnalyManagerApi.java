package com.msun.csm.controller.lis;

import java.util.Map;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.msun.csm.common.model.Result;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/10/31/13:56
 */
@Api (tags = "对外开放接口")
@RequestMapping (value = "/lisAnalyManagerApi")
public interface LisAnalyManagerApi {

    /**
     * LIS开放平台审核设备申请回调
     *
     * @param param
     * @return
     */
    @ApiOperation (value = "LIS开放平台审核设备申请回调", notes = "LIS开放平台审核设备申请回调")
    @PostMapping (value = "/updateEquipStatus")
    @ResponseBody
    Result updateEquipStatus(@RequestBody Map<String, Object> param);

}
