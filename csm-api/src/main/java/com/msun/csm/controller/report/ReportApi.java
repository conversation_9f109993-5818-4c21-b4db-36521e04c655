package com.msun.csm.controller.report;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.msun.csm.common.model.Result;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/9/10
 */
@Api(tags = "报表平台调用接口")
@RequestMapping("/report")
public interface ReportApi {

    /**
     * 查询项目阶段时间数据
     *
     * @param
     * @param token
     * @return
     */
    @ApiOperation(value = "校验code时效性", notes = "校验code时效性")
    @RequestMapping(value = "/verifyToken")
    @ResponseBody
    Result verifyToken(@RequestParam("token") String token);

}
