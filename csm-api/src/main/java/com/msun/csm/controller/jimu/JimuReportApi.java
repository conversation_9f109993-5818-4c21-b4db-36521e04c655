package com.msun.csm.controller.jimu;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.msun.csm.common.model.Result;
import com.msun.csm.model.jimu.dto.JmReportCustomInfoDTO;
import com.msun.csm.model.jimu.dto.ProjectDataParamerDTO;
import com.msun.csm.model.jimu.dto.ProjectPeriodDTO;
import com.msun.csm.model.jimu.req.ProjectDurationStatisticsReq;
import com.msun.csm.model.jimu.req.QueryProductAuthDataReq;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/8/23
 */
@Api(tags = "积木报表接口")
@RequestMapping("/jimu")
public interface JimuReportApi {

    /**
     * 查询项目阶段时间数据
     *
     * @param
     * @return
     */
    @ApiOperation(value = "查询项目阶段时间数据", notes = "查询项目阶段时间数据")
    @GetMapping(value = "/findProjectPeriod")
    @ResponseBody
    Result findProjectPeriod(
            @RequestParam(name = "onlinetime_begin", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd")
            Date onlinetimeBegin,
            @RequestParam(name = "onlinetime_end", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd")
            Date onlinetimeEnd,
            @RequestParam(name = "worktime_begin", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd")
            Date worktimeBegin,
            @RequestParam(name = "worktime_end", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd")
            Date worktimeEnd,
            @RequestParam(name = "customname", required = false) String customname,
            @RequestParam(name = "upgradationType", required = false) String upgradationType,
            @RequestParam(name = "customDeliverStatus", required = false) Integer customDeliverStatus
    );

    /**
     * 查询项目阶段时间数据
     *
     * @param
     * @return
     */
    @ApiOperation(value = "查询项目阶段时间数据", notes = "查询项目阶段时间数据")
    @PostMapping(value = "/findProjectPeriodPost")
    @ResponseBody
    Result findProjectPeriodPost(@RequestBody ProjectPeriodDTO projectPeriodDTO);

    /**
     * 说明:查询客户项目阶段信息统计
     *
     * @param reportCustomInfoDTO
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/9/30 14:11
     * @remark: Copyright
     */
    @ApiOperation(value = "查询客户项目阶段信息统计", notes = "查询客户项目阶段信息统计")
    @PostMapping(value = "/findReportCustomInfo")
    @ResponseBody
    Result findReportCustomInfo(@RequestBody JmReportCustomInfoDTO reportCustomInfoDTO);


    /**
     * 查询项目交付统计数据
     *
     * @param reportCustomInfoDTO
     * @return
     */
    @ApiOperation(value = "查询项目交付统计数据", notes = "查询项目交付统计数据")
    @PostMapping(value = "/findProjectData")
    @ResponseBody
    Result
    findProjectData(@RequestBody JmReportCustomInfoDTO reportCustomInfoDTO);

    /**
     * 查询项目交付统计明细数据(调研后新增数量: 设备)
     *
     * @param reportCustomInfoDTO
     * @return
     */
    @ApiOperation(value = "查询项目交付统计明细数据(调研后新增数量: 设备)",
            notes = "查询项目交付统计明细数据(调研后新增数量: 设备)")
    @PostMapping(value = "/findProjectEquipDataDetail")
    @ResponseBody
    Result findProjectEquipDataDetail(@RequestBody ProjectDataParamerDTO reportCustomInfoDTO);

    /**
     * 调研后新增数量: 接口
     *
     * @param reportCustomInfoDTO
     * @return
     */
    @ApiOperation(value = "查询项目交付统计明细数据(调研后新增数量: 接口)",
            notes = "查询项目交付统计明细数据(调研后新增数量: 接口)")
    @PostMapping(value = "/findProjectThirdDataDetail")
    @ResponseBody
    Result findProjectThirdDataDetail(@RequestBody ProjectDataParamerDTO reportCustomInfoDTO);

    /**
     * 需求
     *
     * @param reportCustomInfoDTO
     * @return
     */
    @ApiOperation(value = "查询项目交付统计明细数据(调研后新增数量: 需求)",
            notes = "查询项目交付统计明细数据(调研后新增数量: 需求)")
    @PostMapping(value = "/findProjectSpecialDataDetail")
    @ResponseBody
    Result findProjectSpecialDataDetail(@RequestBody ProjectDataParamerDTO reportCustomInfoDTO);

    /**
     * 小硬件
     *
     * @param reportCustomInfoDTO
     * @return
     */
    @ApiOperation(value = "查询项目交付统计明细数据(调研后新增数量: 小硬件)",
            notes = "查询项目交付统计明细数据(调研后新增数量: 小硬件)")
    @PostMapping(value = "/findProjectHardWareDetail")
    @ResponseBody
    Result findProjectHardWareDetail(@RequestBody ProjectDataParamerDTO reportCustomInfoDTO);

    /**
     * 产品数量
     *
     * @param reportCustomInfoDTO
     * @return
     */
    @ApiOperation(value = "查询项目交付统计明细数据(产品数量)", notes = "查询项目交付统计明细数据(产品数量)")
    @PostMapping(value = "/findProjectProductDetail")
    @ResponseBody
    Result findProjectProductDetail(@RequestBody ProjectDataParamerDTO reportCustomInfoDTO);

    /**
     * 授权数量
     *
     * @param reportCustomInfoDTO
     * @return
     */
    @ApiOperation(value = "查询项目交付统计明细数据(授权数量)", notes = "查询项目交付统计明细数据(授权数量)")
    @PostMapping(value = "/findProjectAuthorizationDetail")
    @ResponseBody
    Result findProjectAuthorizationDetail(@RequestBody ProjectDataParamerDTO reportCustomInfoDTO);

    /**
     * 查询项目交付统计明细数据(产品准备)
     *
     * @param reportCustomInfoDTO
     * @return
     */
    @ApiOperation(value = "查询项目交付统计明细数据(产品准备)", notes = "查询项目交付统计明细数据(产品准备)")
    @PostMapping(value = "/findProjectProductTaskDetail")
    @ResponseBody
    Result findProjectProductTaskDetail(@RequestBody ProjectDataParamerDTO reportCustomInfoDTO);

    /**
     * 查询项目交付统计明细数据(必备接口)
     *
     * @param reportCustomInfoDTO
     * @return
     */
    @ApiOperation(value = "查询项目交付统计明细数据(必备接口)", notes = "查询项目交付统计明细数据(必备接口)")
    @PostMapping(value = "/findProjectThirdDetail")
    @ResponseBody
    Result findProjectThirdDetail(@RequestBody ProjectDataParamerDTO reportCustomInfoDTO);


    /**
     * 查询项目交付统计明细数据(必备设备)
     *
     * @param reportCustomInfoDTO
     * @return
     */
    @ApiOperation(value = "查询项目交付统计明细数据(必备设备)", notes = "查询项目交付统计明细数据(必备设备)")
    @PostMapping(value = "/findProjectEquipDetail")
    @ResponseBody
    Result findProjectEquipDetail(@RequestBody ProjectDataParamerDTO reportCustomInfoDTO);

    /**
     * 查询项目交付统计明细数据(必备表单)
     *
     * @param reportCustomInfoDTO
     * @return
     */
    @ApiOperation(value = "查询项目交付统计明细数据(必备表单)", notes = "查询项目交付统计明细数据(必备表单)")
    @PostMapping(value = "/findProjectFormDetail")
    @ResponseBody
    Result findProjectFormDetail(@RequestBody ProjectDataParamerDTO reportCustomInfoDTO);

    /**
     * 查询项目交付统计明细数据(必备打印报表)
     *
     * @param reportCustomInfoDTO
     * @return
     */
    @ApiOperation(value = "查询项目交付统计明细数据(必备打印报表)", notes = "查询项目交付统计明细数据(必备打印报表)")
    @PostMapping(value = "/findProjectReportDetail")
    @ResponseBody
    Result findProjectReportDetail(@RequestBody ProjectDataParamerDTO reportCustomInfoDTO);

    /**
     * 查询项目基础数据
     *
     * @param reportCustomInfoDTO
     * @return
     */
    @ApiOperation(value = "查询项目基础数据", notes = "查询项目基础数据")
    @PostMapping(value = "/findProjectBaseData")
    @ResponseBody
    Result findProjectBaseData(@RequestBody ProjectDataParamerDTO reportCustomInfoDTO);


    /**
     * 查询云健康升级进度s
     *
     * @param paramer
     * @return
     */
    @ApiOperation(value = "查询云健康升级进度", notes = "查询云健康升级进度")
    @PostMapping(value = "/findCloudUpgradeProgress")
    @ResponseBody
    Result findCloudUpgradeProgress(@RequestBody ProjectPeriodDTO paramer);

    /**
     * 项目工期统计
     *
     * @return
     */
    @ApiOperation(value = "项目工期统计", notes = "项目工期统计")
    @PostMapping(value = "/projectDurationStatistics")
    @ResponseBody
    Result projectDurationStatistics(@RequestBody ProjectDurationStatisticsReq req);

    /**
     * 项目数据统计
     *
     * @param
     * @return
     */
    @ApiOperation(value = "项目数据统计", notes = "项目数据统计")
    @PostMapping(value = "/findProjectDataPost")
    @ResponseBody
    Result findProjectDataPost(@RequestBody ProjectPeriodDTO projectPeriodDTO);

    /**
     * 客户医院授权数据查询
     *
     * @return
     */
    @ApiOperation(value = "客户医院授权数据查询", notes = "客户医院授权数据查询")
    @PostMapping(value = "/queryProductAuthData")
    @ResponseBody
    Result queryProductAuthData(@RequestBody QueryProductAuthDataReq req);

}
