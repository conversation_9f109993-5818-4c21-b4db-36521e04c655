package com.msun.csm.controller.openapiplatform;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.msun.csm.common.model.Result;
import com.msun.csm.model.openapi.OpenApiDTO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/09/18/15:44
 */
@Api (tags = "能力开放平台调用交付接口")
@RequestMapping ("/interfaceOpenApi")
public interface OpenApi {

    /**
     * 能力开放平台修改接口授权状态
     *
     * @param dto
     * @return
     */
    @ApiOperation (value = "能力开放平台修改接口授权状态", notes = "能力开放平台修改接口授权状态")
    @RequestMapping (value = "/updateInterfaceAuthorStatus")
    @ResponseBody
    Result updateInterfaceAuthorStatus(@RequestBody OpenApiDTO dto);
}
