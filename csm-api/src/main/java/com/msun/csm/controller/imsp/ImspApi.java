package com.msun.csm.controller.imsp;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.msun.csm.common.model.ResponseData;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.imsp.CustomerParamerDTO;
import com.msun.csm.model.imsp.ProjCustomInfoResp;
import com.msun.csm.model.imsp.SyncAcceptResult;
import com.msun.csm.model.imsp.SyncYunRenewDTO;
import com.msun.csm.model.imsp.UpdateMilestoneReq;
import com.msun.csm.model.imsp.UpdateProjectUserRelationReq;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @Description:
 * @Author: duxu
 * @Date: 2024/4/22
 */
@Api(tags = "老系统调用新系统")
@RequestMapping(value = "/imsp")
public interface ImspApi {

    /**
     * 运维平台回调
     *
     * @param param
     * @return
     */
    @ApiOperation(value = "运维平台回调", notes = "运维平台回调")
    @PostMapping(value = "/saveApplyHistoryLog")
    @ResponseBody
    Result<String> saveApplyHistoryLog(@RequestBody Map<String, Object> param);


    /**
     * 修改申请验收的状态
     *
     * @param result
     * @return
     */
    @ApiOperation(value = "同步申请验收的状态", notes = "同步申请验收的状态")
    @PostMapping(value = "/syncAcceptResult")
    @ResponseBody
    Result syncAcceptResult(@RequestBody @Valid SyncAcceptResult result);

    /**
     * 保存项目用户关系
     *
     * @param req
     * @return
     */
    @ApiOperation(value = "保存项目用户关系", notes = "保存项目用户关系")
    @PostMapping(value = "/updateProjectUserRelation")
    @ResponseBody
    Result updateProjectUserRelation(@Valid @RequestBody UpdateProjectUserRelationReq req);

    /**
     * 更新里程碑节点状态
     *
     * @param req
     * @return
     */
    @ApiOperation(value = "更新里程碑节点状态", notes = "更新里程碑节点状态")
    @PostMapping(value = "/updateMilestone")
    @ResponseBody
    Result updateMilestone(@RequestBody UpdateMilestoneReq req);

    /**
     * 云资源续签
     *
     * @param dto 请求
     * @return ResponseData<String>
     */
    @ApiOperation(value = "云资源续签", notes = "云资源续签")
    @PostMapping(value = "/syncYunRenew")
    @ResponseBody
    ResponseData<String> syncYunRenew(@Valid @RequestBody SyncYunRenewDTO dto);

    @ApiOperation(value = "查询客户及实施团队信息", notes = "查询客户及实施团队信息")
    @PostMapping(value = "/getCustomerList")
    @ResponseBody
    List<ProjCustomInfoResp> getCustomerList(@Valid @RequestBody CustomerParamerDTO dto);

    /**
     * 手动处理三方接口及报表状态
     *
     * @param param
     * @return
     */
    @ApiOperation(value = "手动处理三方接口及报表限制", notes = "手动处理三方接口及报表限制")
    @PostMapping(value = "/saveTmpHospitalLimit")
    @ResponseBody
    Result saveTmpHospitalLimit(@RequestBody @Valid Map<String, Object> param);


}
