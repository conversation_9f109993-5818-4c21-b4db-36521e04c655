package com.msun.csm.controller.interfacedevelop;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.msun.csm.common.model.Result;
import com.msun.csm.model.interfacedevelop.CustomerRoleVO;
import com.msun.csm.model.yunying.ThirdInterfaceForInterfaceDevelopDTO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @Description:
 * @Author: 杜旭
 * @Date: 2024/9/18
 */
@Api (tags = "接口开发平台调用交付接口")
@RequestMapping ("/interfaceDevelop")
public interface InterfaceDevelopApi {

    /**
     * 接口实施流程修改三方接口状态
     *
     * @param dto
     * @return
     */
    @ApiOperation (value = "接口开发平台修改接口状态", notes = "接口开发平台修改接口状态")
    @RequestMapping (value = "/updateThirdInterfaceStatus")
    @ResponseBody
    Result updateThirdInterfaceStatus(@RequestBody ThirdInterfaceForInterfaceDevelopDTO dto);

    /**
     * 接口开发平台查询某个人有哪些客户的权限
     * @param account
     * @return
     */
    @ApiOperation("接口开发平台查询某个人有哪些客户的权限")
    @GetMapping("/selectInterfaceRoleByAccount")
    @ResponseBody
    Result<List<CustomerRoleVO>> selectInterfaceRoleByAccount(String account);
}

