package com.msun.csm.controller.yunying;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

import javax.validation.Valid;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.SimpleId;
import com.msun.csm.model.imsp.ChangeCustomTeamDTO;
import com.msun.csm.model.imsp.ChangeCustomTeamResultDTO;
import com.msun.csm.model.imsp.SyncYunInfoDTO;
import com.msun.csm.model.imsp.SyncYunRenewDTO;
import com.msun.csm.model.yunying.ConvertContractDTO;
import com.msun.csm.model.yunying.HospitalInfoDTO;
import com.msun.csm.model.yunying.HospitalReminderResult;
import com.msun.csm.model.yunying.HospitalReminderSwapDTO;
import com.msun.csm.model.yunying.ReplaceOrderProductDTO;
import com.msun.csm.model.yunying.SyncContractDTO;
import com.msun.csm.model.yunying.SyncDisasterRecoveryCheckDTO;
import com.msun.csm.model.yunying.SyncMidOrderAuditDTO;
import com.msun.csm.model.yunying.SyncRiskAuditDTO;
import com.msun.csm.model.yunying.SyncYunAuditDto;
import com.msun.csm.model.yunying.ThirdInterfaceForYunyingDTO;
import com.msun.csm.model.yunying.UpdateCustomInfoDTO;
import com.msun.csm.model.yunying.YunyingPaysignageReq;
import com.msun.csm.model.yunying.resp.HospitalInfo;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/22
 */
@Api(tags = "运营平台调用交付接口")
@RequestMapping("/yunying")
public interface YunyingApi {

    /**
     * 派工单同步
     *
     * @param syncContractDTO
     * @return
     */
    @ApiOperation(value = "派工单同步", notes = "运管平台派工单到交付平台")
    @PostMapping(value = "/syncWorkOrder")
    @ResponseBody
    Result syncWorkOrder(@Valid @RequestBody SyncContractDTO syncContractDTO);

    /**
     * 调用运维平台, 向系统管理发送指令, 设置是否要对医院进行到期提醒。
     *
     * @param hospitalReminderSwapDTO 请求参数
     * @return 返回值
     */
    @ApiOperation(value = "设置医院是否到期提醒", notes = "设置医院是否到期提醒")
    @PostMapping(value = "/setHospitalReminder")
    @ResponseBody
    Result<List<HospitalReminderResult>> setHospitalReminder(@Valid @RequestBody HospitalReminderSwapDTO hospitalReminderSwapDTO);


    /**
     * 部门修改接口
     *
     * @param syncContractDTO
     * @return
     */
    @ApiOperation(value = "部门修改接口", notes = "部门修改接口")
    @PostMapping(value = "/modifyDept")
    @ResponseBody
    Result modifyDept(@Valid @RequestBody SyncContractDTO syncContractDTO);


    /**
     * 人员修改接口
     *
     * @param syncContractDTO
     * @return
     */
    @ApiOperation(value = "人员修改接口", notes = "人员修改接口")
    @PostMapping(value = "/modifyUser")
    @ResponseBody
    Result modifyUser(@Valid @RequestBody SyncContractDTO syncContractDTO);


    /**
     * 客户修改接口
     *
     * @param syncContractDTO
     * @return
     */
    @ApiOperation(value = "客户修改接口", notes = "客户修改接口")
    @PostMapping(value = "/modifyCustomer")
    @ResponseBody
    Result modifyCustomer(@Valid @RequestBody SyncContractDTO syncContractDTO);

    @ApiOperation(value = "风控部门反馈入驻申请结果", notes = "风控部门反馈入驻申请结果")
    @ResponseBody
    @PostMapping("/syncRiskAudit")
    Result syncRiskAudit(@Valid @RequestBody SyncRiskAuditDTO syncRiskAuditDTO);

    @Log(operName = "云容灾反馈验收结果", operDetail = "云容灾反馈验收结果", intLogType = Log.IntLogType.SELF_SYS, cnName = "云容灾反馈验收结果-运营平台触发", saveParam = true)
    @ApiOperation(value = "云容灾反馈验收结果", notes = "云容灾反馈验收结果")
    @ResponseBody
    @PostMapping("/syncDisasterRecoveryCheck")
    Result<String> syncDisasterRecoveryCheck(@Valid @RequestBody SyncDisasterRecoveryCheckDTO dto);

    /**
     * 风控审核,云资源开通时间
     *
     * @param dto 请求参数
     * @return
     */
    @ApiOperation(value = "运营部审核,云资源开通时间", notes = "风控审核,云资源开通时间")
    @RequestMapping(value = "/syncYunAudit")
    @ResponseBody
    Result<String> syncYunAudit(@Valid @RequestBody SyncYunAuditDto dto);

    /**
     * 风控审核免中间件派工单
     *
     * @param dto 请求参数
     * @return Result<String>
     */
    @ApiOperation(value = "风控审核免中间件派工单", notes = "风控审核免中间件派工单")
    @RequestMapping(value = "/syncMidOrderAudit")
    @ResponseBody
    Result<String> syncMidOrderAudit(@Valid @RequestBody SyncMidOrderAuditDTO dto);

    /**
     * 云资源到期发函到自助运维平台
     *
     * @param dto 请求参数
     * @return
     */
    @ApiOperation(value = "云资源到期发函到自助运维平台", notes = "云资源到期发函到自助运维平台")
    @RequestMapping(value = "/syncYunLetterDate")
    @ResponseBody
    Result<String> syncYunLetterDate(@Valid @RequestBody SyncYunRenewDTO dto);

    /**
     * 接口实施流程修改三方接口状态
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "接口实施流程修改三方接口状态", notes = "接口实施流程修改三方接口状态")
    @RequestMapping(value = "/updateThirdInterface")
    @ResponseBody
    Result updateThirdInterface(@RequestBody ThirdInterfaceForYunyingDTO dto);

    /**
     * 更新云资源台账信息
     *
     * @param dto 请求参数
     * @return result<String>
     */
    @Log(operName = "更新云资源台账信息", operDetail = "更新云资源台账信息", intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "更新云资源台账信息-运营平台触发", saveParam = true)
    @ApiOperation(value = "更新云资源台账信息", notes = "更新云资源台账信息")
    @RequestMapping(value = "/syncCloudInfo")
    @ResponseBody
    Result<String> syncCloudInfo(@Valid @RequestBody SyncYunInfoDTO dto);

    /**
     * 作废工单
     *
     * @param simpleId 请求参数
     * @return result
     */
    @Log(operName = "作废工单", operDetail = "作废工单", intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "作废工单-运营平台触发", saveParam = true)
    @ApiOperation(value = "作废工单", notes = "作废工单")
    @RequestMapping(value = "/deleteOrder")
    @ResponseBody
    Result deleteOrder(@Valid @RequestBody SimpleId simpleId);

    /**
     * 实施地变更
     *
     * @param updateCustomInfoDTO 请求参数
     * @return result
     */
    @Log(operName = "实施地变更", operDetail = "实施地变更", intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "实施地变更-运营平台触发", saveParam = true)
    @ApiOperation(value = "实施地变更", notes = "实施地变更")
    @RequestMapping(value = "/updateCustomInfo")
    @ResponseBody
    Result updateCustomInfo(@Valid @RequestBody UpdateCustomInfoDTO updateCustomInfoDTO);

    /**
     * 置换工单产品
     *
     * @param replaceOrderProductDTO
     * @return result
     */
    @Log(operName = "置换工单产品", operDetail = "置换工单产品", intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "置换工单产品-运营平台触发", saveParam = true)
    @ApiOperation(value = "置换工单产品", notes = "置换工单产品")
    @RequestMapping(value = "/replaceOrderProduct")
    @ResponseBody
    Result replaceOrderProduct(@Valid @RequestBody ReplaceOrderProductDTO replaceOrderProductDTO);

    /**
     * 客户团队变更
     *
     * @param dto 请求参数
     * @return result<String>
     */
    @Log(operName = "客户团队变更", operDetail = "客户团队变更", intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "客户团队变更-运营平台触发", saveParam = true)
    @ApiOperation(value = "客户团队变更", notes = "客户团队变更")
    @RequestMapping(value = "/changeCustomTeam")
    @ResponseBody
    Result<ChangeCustomTeamResultDTO> changeCustomTeam(@Valid @RequestBody ChangeCustomTeamDTO dto);

    /**
     * 合同转正式
     *
     * @param dto 请求参数
     * @return result<String>
     */
    @Log(operName = "合同转正式", operDetail = "合同转正式", intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "合同转正式-运营平台触发", saveParam = true)
    @ApiOperation(value = "合同转正式", notes = "合同转正式")
    @RequestMapping(value = "/convertFormalContract")
    @ResponseBody
    Result convertFormalContract(@Valid @RequestBody ConvertContractDTO dto);

    /**
     * 获取云健康医院信息
     *
     * @param dto 请求参数
     * @return result<List < HospitalInfo>>
     */
    @Log(operName = "获取云健康医院信息", operDetail = "获取云健康医院信息", intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "获取云健康医院信息", saveParam = true)
    @ApiOperation(value = "获取云健康医院信息", notes = "获取云健康医院信息")
    @RequestMapping(value = "/findHospitalInfo")
    @ResponseBody
    Result<List<HospitalInfo>> findHospitalInfo(@Valid @RequestBody HospitalInfoDTO dto);


    /**
     * 首付款是否满足回调-
     *
     * @param dto 请求参数
     * @return Result<String>
     */
    @ApiOperation(value = "首付款是否满足回调", notes = "首付款是否满足回调")
    @RequestMapping(value = "/paySignageIsEnoughFuction")
    @ResponseBody
    Result<String> paySignageIsEnoughFuction(@Valid @RequestBody YunyingPaysignageReq dto);
}
