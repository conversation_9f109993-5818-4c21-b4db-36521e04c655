package com.msun.csm.controller.autocheck;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.msun.csm.common.model.Result;
import com.msun.csm.model.csm.UpdateTestRecordDTO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @description:
 * @fileName:
 * @author:z<PERSON><PERSON><PERSON><PERSON>
 * @updateBy:
 * @Date:Created in 17:44 2024/7/11
 * @remark:
 */
@Api(tags = "自动化平台调用新平台")
@RequestMapping(value = "/autoCheck")
public interface AutoCheckApi {

    /**
     * 更新测试结果. 单个医院测试结果的更新, 自动化测试平台回调使用
     *
     * @param updateTestRecordDTO
     * @return
     */
    @ApiOperation(value = "更新测试结果", notes = "更新测试结果")
    @PostMapping(value = "/updateTestRecord")
    default Result<String> updateTestRecord(@RequestBody UpdateTestRecordDTO updateTestRecordDTO) {
        return null;
    }


}
