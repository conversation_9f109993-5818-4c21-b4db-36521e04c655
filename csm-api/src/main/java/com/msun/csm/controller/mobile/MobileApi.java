package com.msun.csm.controller.mobile;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.msun.csm.common.model.Result;
import com.msun.csm.model.csm.SysUserToMobileVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/11/08/15:39
 */
@Api (tags = "移动端程序调用")
@RequestMapping ("/mobileApi")
public interface MobileApi {

    /**
     * 根据登录用户名获取人员信息
     *
     * @param account
     * @return
     */
    @ApiOperation (value = "根据登录用户名获取人员信息", notes = "根据登录用户名获取人员信息")
    @RequestMapping (value = "/getUserInfo")
    @ResponseBody
    Result<SysUserToMobileVO> getUserInfo(@RequestParam ("account") String account);
}
