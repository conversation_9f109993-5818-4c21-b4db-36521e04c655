package com.msun.csm.model.imsp;

import lombok.Data;

/**
 * 延期云资源参数
 */
@Data
public class SyncYunRenewDTO {

    /**
     * 运营平台工单id
     */
    private Long yyWoId;
    /**
     * 运营平台实施地id
     */
    private Long customerId;

    /**
     * 解决方案. 1单体医院,2区域,3医共体,4全县域医共体,5疾控,6医保,7卫健,8基层版单体医院,9市平台,10云健康升级,11嵌入式软件,12运营服务
     */
    private Integer pemcusssolType;

    /**
     * 工单类型1、自研软件；2、硬件；3、耗材；4、接口；5、软件服务费；6、硬件服务费；7、容灾；8、外采软件；9、云资源
     */
    private Integer type;


    /**
     * 时长（单位 天）
     */
    private Integer len;

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 内容反馈：1、已与客户沟通且达成一致，承诺按时签回；
     * 2、已与客户沟通，尚未达成共识；
     * 3、未和医院沟通；
     * 4、其他
     */
    private Integer cloudFeedback;

    /**
     * 发函时间
     * 云资源到期提醒必填	yyyy-MM-dd
     */
    private String sendLetterDate;

    /**
     * 到期前提醒总开关 0：不提醒，1：提醒
     */
    private String mainRemindFlag;
}
