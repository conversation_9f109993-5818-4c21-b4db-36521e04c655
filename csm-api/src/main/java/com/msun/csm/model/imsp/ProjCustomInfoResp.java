package com.msun.csm.model.imsp;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/24
 */

/**
 * 实施地客户信息
 */
@ApiModel(description = "实施地客户信息")
@Data
@TableName(schema = "csm")
public class ProjCustomInfoResp implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 运营平台实施客户ID
     */
    @ApiModelProperty(value = "运营平台实施客户ID")
    private Long yyCustomerId;
    /**
     * 实施地客户名称
     */
    @ApiModelProperty(value = "实施地客户名称")
    private String customName;

    @ApiModelProperty(value = "实施团队id")
    private Long deptId;
    @ApiModelProperty(value = "实施团队名称")
    private String deptName;
    @ApiModelProperty(value = "实施负责人id")
    private Long userId;
    @ApiModelProperty(value = "实施负责人")
    private String userName;
}
