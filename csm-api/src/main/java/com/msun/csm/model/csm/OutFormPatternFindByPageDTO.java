package com.msun.csm.model.csm;

import com.msun.csm.common.model.dto.BasePageDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/12/20 16:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "分页查询实体", description = "/")
public class OutFormPatternFindByPageDTO extends BasePageDTO {

    @ApiModelProperty(value = "检索文本")
    private String searchText;

    @ApiModelProperty(value = "表单类型")
    private String formCategoryCode;

}
