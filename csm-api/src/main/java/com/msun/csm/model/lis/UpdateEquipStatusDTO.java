package com.msun.csm.model.lis;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/10/31/13:57
 */
@Data
public class UpdateEquipStatusDTO {

        @ApiModelProperty ("产品code【Lis（lis产品）、Hd（血透产品）】")
        private String productCode;

        @ApiModelProperty ("设备id")
        private Long equipId;

        @ApiModelProperty ("设备状态【0：未申请；1：已申请:；2：已驳回:；3：研发中；4：研发完成:；5：测试通过:；6：测试失败】")
        private Integer equipStatus;

        @ApiModelProperty ("驳回原因")
        private String rejectReason;

        @ApiModelProperty ("操作人登录账号")
        private String account;

        @ApiModelProperty ("操作时间")
        private String operateTime;
}
