package com.msun.csm.model.imsp;

import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/7/1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateProjectUserRelationReq {

    // 项目id
    @NotNull(message = "项目id不能为空")
    private Long projectId;

    // 用户id
    @NotEmpty(message = "用户id不能为空")
    private List<Long> userYYId;

    // 操作类型，1：新增，2：删除
    @NotNull(message = "操作类型不能为空")
    private int type;
}
