package com.msun.csm.model.tduck.req;

import java.util.List;

import javax.validation.constraints.NotNull;

import com.alibaba.fastjson.JSONObject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/7/30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TDuckTask {

    /**
     * 调研标题
     */
    @NotNull(message = "调研标题不能为空")
    private String surveyTitle;

    /**
     * 调研结果
     */
    @NotNull(message = "调研结果不能为空")
    private String surveyValue;

    /**
     * 待办标题
     */
    @NotNull(message = "待办标题不能为空")
    private String taskTitle;

    /**
     * 待办内容
     */
    @NotNull(message = "待办内容不能为空")
    private String taskDetail;

    /**
     * 备注
     */
    private String remark;

    /**
     * 运营平台产品ID
     */
    @NotNull(message = "运营平台产品ID不能为空")
    private Long yyProductId;

    /**
     * 排序号
     */
    @NotNull(message = "排序号不能为空")
    private Integer sortNo;

    /**
     * 待办跳转的页面地址
     */
    private String taskPageUrl;

    /**
     * （云健康产品编码）
     */
    private String cloudProductCode;

    /**
     * 检测脚本内容
     */
    private String verifySqlText;

    /**
     * 待办检测方式：interface-接口检测；sql-SQL脚本检测
     */
    private String taskValidateType;

    /**
     * 待办编码
     */
    @NotNull(message = "待办编码不能为空")
    private String taskCode;

    /**
     * 待办说明链接
     */
    private String taskExplainLink;

    /**
     * 检测标准
     */
    private String validateStandards;

    /**
     * 检测明细脚本
     */
    private String validateDetailSql;

    /**
     * 项目文件，结构为：{@link com.msun.csm.dao.entity.proj.ProjProjectFile}
     */
    private List<JSONObject> projectFileList;

    /**
     * 文件列表
     */
    private List<TDuckTaskFileInfo> fileList;
}
