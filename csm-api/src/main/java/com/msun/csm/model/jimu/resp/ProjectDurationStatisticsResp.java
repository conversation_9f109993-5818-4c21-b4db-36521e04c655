package com.msun.csm.model.jimu.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectDurationStatisticsResp {

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 老换新类型 1:新客户 2:老换新 3:老体系
     */
    private String upgradationTypeName;

    /**
     * 老换新类型 1:新客户 2:老换新 3:老体系
     */
    private Integer upgradationType;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 客户类型 1单体 2区域
     */
    private String customTypeName;

    /**
     * 客户类型 1单体 2区域
     */
    private String customType;

    /**
     * 客服分公司名称
     */
    private String serviceOrgName;

    /**
     * 客户名称
     */
    private String customName;

    /**
     * 派工时间
     */
    private String workTime;

    /**
     * 入驻时间
     */
    private String settleInTime;

    /**
     * 上线时间
     */
    private String onlineTime;

    /**
     * 外部验收时间
     */
    private String externalAcceptTime;

    /**
     * 申请验收时间
     */
    private String applyAcceptTime;

    /**
     * 验收时间
     */
    private String acceptTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 入驻到上线
     */
    private String settleToOnline;

    /**
     * 上线到外部验收
     */
    private String onlineToExternalAccept;

    /*
     * 入驻到外部验收
     */
    private String settleToExternalAccept;

    /**
     * 上线到验收
     */
    private String settleToAccept;

    /**
     * 客户合作状态
     */
    private Integer customDeliverStatus;

    /**
     * 项目状态
     */
    private String projectStatusName;
}
