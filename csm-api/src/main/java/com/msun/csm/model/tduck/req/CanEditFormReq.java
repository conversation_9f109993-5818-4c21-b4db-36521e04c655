package com.msun.csm.model.tduck.req;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor

public class CanEditFormReq {

    /**
     * 项目ID
     */
    @NotNull(message = "参数【projectInfoId】不可为null")
    private Long projectInfoId;

    /**
     * 医院ID
     */
    @NotNull(message = "参数【hospitalInfoId】不可为null")
    private Long hospitalInfoId;

    /**
     * 产品ID
     */
    @NotNull(message = "参数【yyProductId】不可为null")
    private Long yyProductId;

    /**
     * 来源：survey-提交调研结果；config-提交最终调研结果
     */
    @NotNull(message = "参数【source】不可为null")
    private String source;

    /**
     * 调研负责人的用户ID
     */
    @NotNull(message = "参数【surveyUserId】不可为null")
    private Long surveyUserId;

    /**
     * 填鸭传过来的当前登录人
     */
    @NotNull(message = "参数【currentSysUserId】不可为null")
    private Long currentSysUserId;

    /**
     * 科室名称
     */
    @NotNull(message = "参数【deptName】不可为null")
    private String deptName;

}
