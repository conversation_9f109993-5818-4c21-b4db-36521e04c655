package com.msun.csm.model.yunying;


import java.util.List;

import javax.validation.constraints.NotEmpty;

import lombok.Data;

/**
 * 派工单 ---工单信息
 */
@Data
public class OrderDTO {

    public static final String ERROR = "NO";


    /**
     * 交付平台工单id
     */
    private Long id;


    /**
     * （运营平台）项目唯一标识
     */
    private Long projectNum;

    /**
     * (运营平台) 项目名称
     */
    private String projectName;

    /**
     * 实施团队id(运营平台)
     */
    private Integer teamId;

    /**
     * 实施团队id (交付平台)
     */
    private Integer jfTeamId;


    /**
     * 实施团队名称
     */
    private String teamName;

    /**
     * 工单id（运营平台）
     */
    private Long workOrderId;

    /**
     * 工单编号
     */
    private String workNum;

    /**
     * 工单名称
     */
    private String workOrderName;

    /**
     * 工单类型
     * 1、自研软件；2、硬件；3、耗材；4、接口；5、软件服务费；6、硬件服务费；7、容灾；8、外采软件；9、云资源
     */
    private Integer orderType;

    /**
     * 项目经理id(运营平台)
     */
    private Integer projectManagerId;

    /**
     * 项目经理id (交付平台)
     */
    private Integer jfProManId;

    /**
     * 项目经理名称
     */
    private String projectManagerName;


    /**
     * 产品
     */
    @NotEmpty (message = "产品不能为空")
    private List<ProductDTO> product;


    /**
     * 运营平台产品id串儿
     */
    private String yyProductIdStr;

    /**
     * 运营平台产品名称串
     */
    private String yyProductNames;


    /**
     * 该工单是否需要处理默认项目经理和团队信息
     */
    private boolean autoFlag;

    /**
     * 解决方案 1单体医院,2区域,3医共体,4全县域医共体,5疾控,6医保,7卫健,8基层版单体医院,9市平台,10云健康升级,11嵌入式软件,12运营服务
     */
    private String solution;


    /**
     * 工单的云资源类型 （1共享云、2非众阳云、3 众阳云-需要有云资源派工单）
     */
    private String orderCloud;

    /**
     * 项目销售派工时间
     */
    private String workTime;
    /**
     * 项目类型-1单体2区域3医共体
     */
    private Integer projectType;
    /**
     * 结算状态-明鹏
     */
    private String settleStatus;
    /**
     * 结算比例-明鹏
     */
    private String settleProp;
    /**
     * 结算金额-明鹏
     */
    private String settleAmount;
    /**
     * 监理标识-明鹏
     */
    private Integer supervisorFlag;
    /**
     * 云健康点数-明鹏
     */
    private String msunHealthPoint;
}
