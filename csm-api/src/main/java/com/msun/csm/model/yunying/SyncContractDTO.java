package com.msun.csm.model.yunying;

import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 派工单信息同步
 */
@Data
public class SyncContractDTO {
    /**
     * 合同编号 （id）
     */
    @NotNull(message = "合同编号不能为空")
    private Long contractNum;


    /**
     * 合同号
     */
    @NotNull(message = "合同号不能为空")
    private String contractStr;

    /**
     * 合同名称 / 项目名称
     */
    private String contractName;

    /**
     * 客户名称（实施地名称）
     */
    @NotNull(message = "客户名称不能为空")
    private String customerName;

    /**
     * 客户id (运营平台)
     */
    @NotNull(message = "客户id不能为空")
    private Integer customerId;

    /**
     * 客户id（交付平台）
     */
    private Integer jfCustomerId;

    /**
     * 合同类型
     * 1标准合同 2借货合同 3新产品试用协议 4客户协议 5其他
     */
    private Integer contractPreType;


    /**
     * 等于'electronicSales'时为电销
     */
    private String saleOrgType;

    /**
     * 销售人员id （运营平台）
     */
    @NotNull(message = "销售人员id不能为空")
    private Integer saleUserId;

    /**
     * 销售人员id (交付平台)
     */
    private Integer jfSaleUserId;

    /**
     * 签订部门ID
     */
    @NotNull(message = "签订部门ID不能为空")
    private Integer saleOrgId;

    /**
     * 销售人员名称
     */
    @NotNull(message = "销售人员名称不能为空")
    private String saleUserName;

    /**
     * 实施地客户信息
     */
    @NotNull(message = "实施地客户信息不能为空")
    private CustomerDTO customerInfo;

    /**
     * 主体客户信息
     */
    @NotNull(message = "主体客户信息不能为空")
    private CustomerDTO principalCustomer;

    /**
     * 床位数
     */
    private Integer bedNumber;

    /**
     * 工单信息
     */
    @NotEmpty(message = "工单信息不能为空")
    private List<OrderDTO> datas;

    /**
     * 医院总数
     */
    private Integer hospitalNums;


    /**
     * 实施类型： lhx:老换新   xkh: 新客户
     */
    private String projectCustomerType;


    /**
     * 区域、Y
     * 单体、F
     */
    private String isArea;

    /**
     * 合同签订人id
     */
    private Long contractSignPersonId;

    /**
     * 签订人所属团队
     */
    private Long contractSignTeamId;


    /**
     * 老系统项目ID
     */
    @NotNull(message = "老系统项目ID不能为空")
    private Long oldProjectId;

    /**
     * 老系统客户ID
     */
    @NotNull(message = "老系统客户ID不能为空")
    private Long oldCustomerId;

    /**
     * 老系统客户infoID
     */
    @NotNull(message = "老系统客户infoID不能为空")
    private Long oldCustomerInfoId;

    /**
     * 项目类型
     */
    private Integer projectType;

    /**
     * 项目实施类型  1 老换新升级 2新客户上线
     */
    private Integer upgradationType;

    /**
     * 对应的方案分公司
     */
    private Integer preSaleOrgId;
    /**
     * 是否续期合同 true 是 false 否
     */
    private Boolean whetherRenewal;
    /**
     * 是否是云容灾续期合同 true 是 false 否
     */
    private Boolean disaststerRenewal;
    /**
     * 云资源台账id
     */
    private Long customCloudServiceId;
}
