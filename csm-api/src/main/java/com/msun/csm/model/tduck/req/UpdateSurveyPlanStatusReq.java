package com.msun.csm.model.tduck.req;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/7/29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateSurveyPlanStatusReq {
    /**
     * 项目id
     */
    @NotNull (message = "项目id不能为空")
    private Long projectInfoId;
    /**
     * 医院id
     */
    private Long hospitalInfoId;

    /**
     * 产品Id
     */
    @NotNull (message = "产品id不能为空")
    private Long yyProductId;

    /**
     * 调研人id
     */
    @NotNull(message = "调研人id不能为空")
    private Long surveyUserId;

    /**
     * 完成状态，0未完成，1已完成
     * 新增状态：2 进行中   3 待确认
     */
    @ApiModelProperty(value = "完成状态，0未完成，1已完成，2 进行中 ，3 待确认")
    @NotNull(message = "完成状态不能为空")
    private Integer completeStatus;

    /**
     * 调研科室
     */
    @NotNull(message = "调研科室不能为空")
    private String deptName;
}
