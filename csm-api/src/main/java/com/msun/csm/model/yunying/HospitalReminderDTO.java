package com.msun.csm.model.yunying;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 设置医院是否提醒
 */
@Data
public class HospitalReminderDTO {

    /**
     * 产品解决方案类型ID：1单体医院,2区域,3医共体,4全县域医共体,8基层版单体医院
     */
    @NotNull(message = "解决方案不能为空")
    private Integer solutionType;

    /**
     * 白名单标识. 0:不列入白名单, 1:白名单
     */
    @NotNull(message = "白名单标识不能为空")
    private Integer whiteListFlag;

    /**
     * 运营平台实施地客户id
     */
    @NotNull(message = "运营平台实施地客户id不能为空")
    private Long yyCustomerId;

}
