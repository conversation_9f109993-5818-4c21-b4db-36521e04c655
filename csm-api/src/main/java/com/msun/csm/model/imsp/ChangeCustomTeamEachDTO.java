package com.msun.csm.model.imsp;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 变更实施团队
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ChangeCustomTeamEachDTO {

    /**
     * 运营平台工单id
     */
    @ApiModelProperty(value = "运营平台工单id")
    @NotNull(message = "运营平台工单id不能为空")
    private Long yyOrderId;

    /**
     * 新实施团队ID
     */
    @ApiModelProperty(value = "新实施团队ID")
    @NotNull(message = "新实施团队ID不能为空")
    private Long teamId;

    /**
     * 新实施团队负责人ID
     */
    @ApiModelProperty(value = "新实施团队负责人ID")
    @NotNull(message = "新实施团队负责人ID不能为空")
    private Long teamLeaderId;

    /**
     * 交付工单类型：1、自研软件；2、硬件；3、耗材；4、接口；5、软件服务费；6、硬件服务费；7、容灾；8、外采软件；9、云资源
     */
    @ApiModelProperty(value = "交付工单类型：1、自研软件；2、硬件；3、耗材；4、接口；5、软件服务费；6、硬件服务费；7、容灾；8、外采软件；9、云资源")
    @NotNull(message = "交付工单类型不能为空")
    private Integer deliveryOrderType;


}
