package com.msun.csm.model.yunying;

import lombok.Data;

/**
 * 派工单  主客户信息
 */
@Data
public class CustomerDTO {

    /**
     * 年收入  单位（亿元）
     */
    private float annualIncome;

    /**
     * 医院类型
     */
    private String hospitalType;

    /**
     * 医院职工人数
     */
    private Integer employeesNumber;

    /**
     * 床位数量
     */
    private Integer bedNumber;

    /**
     * 运营平台客户id (运营平台)
     */
    private Integer customerId;

    /**
     * 客户id (交付平台)
     */
    private Integer jfCustomerId;

    /**
     * 客户等级 ABC 对交付已经无用
     */
    private String customerLevel;

    /**
     * 省id
     */
    private Integer provinceId;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 市划信息Id
     */
    private Integer cityId;

    /**
     * 市划信息名称
     */
    private String cityName;

    /**
     * 区划信息Id
     */
    private Integer areaId;

    /**
     * 区划信息名称
     */
    private String areaName;

    /**
     * 客户所属销售中心
     */
    private Long saleCenterId;
    /**
     * 销售所属省区id
     */
    private Long saleProvince;
    /**
     * 客户状态-签约客户/潜在客户等
     */
    private String customerInfoStatus;
    /**
     * 客户归属客服团队Id
     */
    private Long csTeamId;
    /**
     * 客户归属客服负责人Id
     */
    private Long csTeamLeaderId;
    /**
     * 客户归属客服部门Id
     */
    private Long csDeptId;
    /**
     * 日门诊量
     */
    private Integer outPatientNum;
    /**
     * 单体医院分院数量
     */
    private Integer hospitalBranchNum;
    /**
     * 区域卫生院数量
     */
    private Integer townshipHospitalNum;
    /**
     * 实施地客户名称
     */
    private String customerName;

    /**
     * 客户等级  三甲 三乙等
     */
    private String hospitalLevel;
}
