package com.msun.csm.model.tduck.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/7/30
 */
@Data
public class TDuckReport {

    /**
     * 采集名称
     */
    private String reportName;

    /**
     * 对应DICT中的数据采集类型ID(报表004，文书005)
     */
    private String collectionType;


    /**
     * 适用医院，未填为适用全部医院
     */
    private Long customerId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 上传图片
     */
    private String imglist;

    /**
     * 规则说明
     */
    private String ruleDesc;

    /**
     * 上线必备0： 否 1: 是
     */
    private String onlineEssential;

    /**
     * 产品id  platform.product 主键
     */
    private Long productId;

    /**
     * print 打印类，statistics统计类
     */
    private String printType;

    /**
     * 报表文件路径
     */
    private String reportFile;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 医院编号。
     * customer_info_id -- int(8)
     */
    private Long customerInfoId;


    @ApiModelProperty(value = "报表节点编码")
    private String printDataCode;

    @ApiModelProperty(value = "报表标识")
    private String reportFileTag;

    /**
     * 是否是默认选项 1默认 0 非默认
     */
    private Integer defaultFlag;

    @ApiModelProperty(value = "打印纸张大小")
    private String reportPaperSize;
}
