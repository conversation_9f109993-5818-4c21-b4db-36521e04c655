package com.msun.csm.model.csm;

import com.msun.core.commons.api.BaseVO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 自定义表单信息表(CustomForm)实体类
 *
 * <AUTHOR>
 * @since 2021-04-22 14:14:43
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OutFormPatternVO extends BaseVO {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long formPatternId;

    /**
     * 表单名称
     */
    @ApiModelProperty(value = "表单名称")
    private String formPatternName;

    /**
     * 描述说明
     */
    @ApiModelProperty(value = "描述说明")
    private String description;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Long sort;

    /**
     * 电脑表单样式内容
     */
    @ApiModelProperty(value = "电脑端表单样式内容")
    private String formContentPc;

    /**
     * 平板表单样式内容
     */
    @ApiModelProperty(value = "平板端表单样式内容")
    private String formContentPad;

    /**
     * 平板端表单样式格式
     */
    @ApiModelProperty(value = "平板端表单样式格式")
    private String formConfigurationPad;

    /**
     * 表单页面配置
     */
    @ApiModelProperty(value = "表单页面配置")
    private String formConfigurationPc;

    /**
     * 流程节点code
     */
    private String formCategoryCode;
    /**
     * 作废标识
     */
    @ApiModelProperty(value = "作废标识")
    private String invalidFlag;

    /**
     * 运营平台产品Id 手麻4050
     */
    @ApiModelProperty(value = "运营平台产品Id 手麻4050")
    private Long yyProductId;

    /**
     * 平板表单样式内容
     */
    @ApiModelProperty(value = "平板端表单样式内容")
    private String formContentWeb;

    /**
     * 平板端表单样式格式
     */
    @ApiModelProperty(value = "平板端表单样式格式")
    private String formConfigurationWeb;
    /**
     * 省
     */
    @ApiModelProperty(value = "省")
    private String dictProvinceName;
    /**
     * 市
     */
    @ApiModelProperty(value = "市")
    private String dictCityName;
}
