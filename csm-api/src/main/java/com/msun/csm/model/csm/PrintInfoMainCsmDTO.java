package com.msun.csm.model.csm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @classDesc: 功能描述:dto
 * @author: wp
 * @date: 2020/06/04 22:12
 * @copyright 众阳健康
 */
@ApiModel(description = "查询数据")
@Data
public class PrintInfoMainCsmDTO {

    /**
     * 医院ID
     */
    @ApiModelProperty("医院ID")
    private Long hospitalId;

    /**
     * 机构ID
     */
    @ApiModelProperty("机构ID")
    private Long hisOrgId;


    /**
     * 描述
     */
    @ApiModelProperty("收集内容")
    private List<PrinterCsmDTO> list;

}
