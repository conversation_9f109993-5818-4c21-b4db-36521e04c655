package com.msun.csm.model.yunying;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 风控反馈结果, 免中间件回调用
 */
@Data
public class SyncMidOrderAuditDTO implements Serializable {

    /**
     * 运营部工单id
     */
    @NotNull(message = "运营部工单id不能为空")
    private Long yyOrderId;

    /**
     * 状态 1通过  0未通过
     */
    @NotNull(message = "状态不能为空")
    private Integer code;

    /**
     * 操作人姓名
     */
    @NotNull(message = "操作人姓名不能为空")
    private String userName;
    /**
     * 操作人账号
     */
    @NotNull(message = "操作人账号不能为空")
    private String account;

    /**
     * 消息
     */
    private String msg;


}
