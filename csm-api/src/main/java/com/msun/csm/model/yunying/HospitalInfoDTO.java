package com.msun.csm.model.yunying;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 运营请求参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class HospitalInfoDTO {

    /**
     * 云健康实施客户id
     */
    @ApiModelProperty(value = "云健康实施客户id")
    @NotNull(message = "云健康实施客户id不能为空")
    private Long yyCustomerId;

    /**
     * 解决方案（1单体医院,2区域,3医共体,4全县域医共体,8基层版单体医院）
     */
    @ApiModelProperty(value = "解决方案（1单体医院,2区域,3医共体,4全县域医共体,8基层版单体医院）")
    @NotNull(message = "解决方案不能为空")
    private Integer solutionType;

}
