package com.msun.csm.model.yunying;

import java.util.List;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/12/6
 */
@Data
public class UpdateCustomInfoDTO {

    /**
     * 旧客户id
     */
    @NotNull(message = "旧客户id不能为空")
    private Long oldYYCustomId;

    /**
     * 新客户id
     */
    @NotNull(message = "新客户id不能为空")
    private Long newYYCustomId;

    /**
     * 变更类型 1 客户合并  2 项目级别客户数据变更
     */
    @NotNull(message = "变更类型不能为空")
    private Integer changeType;

    /**
     * 项目数据变更，必须有该参数
     */
    private List<Long> orderIdList;
}
