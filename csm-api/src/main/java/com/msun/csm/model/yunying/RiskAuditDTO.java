package com.msun.csm.model.yunying;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 风控反馈结果
 */
@Data
public class RiskAuditDTO implements Serializable {

    /**
     * 运营部工单id
     */
    @NotNull
    private Long yyOrderId;

    /**
     * 多个合同号
     */
    private List<RiskAuditContractDTO> contractList;

    /**
     * 操作人姓名
     */
    @NotNull
    private String userNam;

    /**
     * 消息
     */
    private String msg;

    /**
     * 状态 1通过  2未通过
     */
    @NotNull
    private Integer code;

    /**
     * 跳过审核. true:跳过
     */
    private Boolean skipAudit;

    /**
     * 项目id
     */
    private Long projectInfoId;

    /**
     * 当前项目无须审核
     */
    private Boolean notRequireAuditCurProj;


}
