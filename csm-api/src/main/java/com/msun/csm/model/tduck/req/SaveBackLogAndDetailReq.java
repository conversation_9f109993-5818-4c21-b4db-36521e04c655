package com.msun.csm.model.tduck.req;

import java.util.List;
import java.util.Set;

import javax.validation.constraints.NotNull;

import com.msun.csm.common.model.BaseIdNameResp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/7/30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SaveBackLogAndDetailReq {
    /**
     * 项目id
     */
    @NotNull(message = "项目id不能为空")
    private Long projectInfoId;

    /**
     * 医院Id
     */
    @NotNull(message = "医院id不能为空")
    private Long hospitalInfoId;

    /**
     * 产品idList
     */
    private List<BaseIdNameResp> yyProductList;

    /**
     * 待处理任务
     */
    private List<TDuckTask> taskList;

    /**
     * 配置
     */
    private List<TDuckConfig> configList;

    /**
     * 表单
     */
    private List<TDuckForm> formList;

    /**
     * 表单
     */
    private List<TDuckReport> reportList;

    /**
     * 医院Id
     */
    @NotNull(message = "完成状态不能为空 0是项目经理确认，1是最终版本提交，最终版本提交不需要重新生成任务")
    private Integer completeFlag;

    /**
     * 产品ID的Set，填鸭系统使用
     */
    private Set<Long> yyProductIdSet;
}
