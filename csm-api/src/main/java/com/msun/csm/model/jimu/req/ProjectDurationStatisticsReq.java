package com.msun.csm.model.jimu.req;

import java.util.Date;

import com.msun.csm.model.jimu.dto.ProjectPeriodDTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectDurationStatisticsReq extends ProjectPeriodDTO {

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 客户类型 1单体 2区域
     */
    @ApiModelProperty(value = "客户类型")
    private Integer customType;

    /**
     * 客服分公司name
     */
    @ApiModelProperty(value = "客服分公司name")
    private String serviceOrgName;

    /**
     * 入驻时间-开始
     */
    @ApiModelProperty(value = "入驻时间-开始")
    private Date settleInTimeBegin;
    /**
     * 入驻时间-结束
     */
    @ApiModelProperty(value = "入驻时间-结束")
    private Date settleInTimeEnd;

    /**
     * 申请验收时间-开始
     */
    @ApiModelProperty(value = "申请验收时间-开始")
    private Date applyAcceptTimeBegin;
    /**
     * 申请验收时间-结束
     */
    @ApiModelProperty(value = "申请验收时间-结束")
    private Date applyAcceptTimeEnd;

    /**
     * 内部验收时间-开始
     */
    @ApiModelProperty(value = "内部验收时间-开始")
    private Date acceptTimeBegin;
    /**
     * 内部验收时间-结束
     */
    @ApiModelProperty(value = "内部验收时间-结束")
    private Date acceptTimeEnd;

    @ApiModelProperty(value = "项目交付状态")
    private Integer projectDeliverStatus;

}
