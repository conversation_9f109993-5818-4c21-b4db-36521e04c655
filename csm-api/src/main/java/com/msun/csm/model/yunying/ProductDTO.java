package com.msun.csm.model.yunying;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 产品dto
 */
@Data
public class ProductDTO {


    /**
     * (运营平台)产品id
     */
    @NotNull (message = "产品id不能为空")
    private Integer productId;

    /**
     * 产品名称
     */
    @NotNull (message = "产品名称不能为空")
    private String productName;

    /**
     * 产品数量
     */
    private Integer num;


    /**
     * 是否需要集成 1 是 0 否
     */
    private Integer integrationFlag;

    /**
     * 医院总数
     */
    private Integer hospitalNums;

    /**
     * 解决方案 1单体医院,2区域,3医共体,4全县域医共体,5疾控,6医保,7卫健,8基层版单体医院,9市平台,10云健康升级,11嵌入式软件,12运营服务
     */
    @NotNull (message = "解决方案不能为空")
    private Integer pemCusSolType;

    /**
     * 工单产品表ID (运营平台)
     */
    private String projProductId;

    /**
     * 硬件工单:
     * <p>
     * 硬件产品类别 0.未区分数据 1.大硬件  2.小硬件
     */
    private Integer prodHardwareType;


    /**
     * 订阅期限（当工单类型为云资源时需要填写）
     */
    private int productSubscribeYear;


    /**
     * 1、'买产品' = 永久
     * 2、'买服务' = 有时间限制
     */
    private Integer buyMode;

    /**
     * 购买类型: 1新购 3续费
     */
    private Integer buyType;

    /**
     * 买服务开始时间（容灾）YYYY-MM-DD
     */
    private String startDate;


    /**
     * 结束时间(续期使用)
     */
    private String endDate;


    /**
     * 产品结算状态-明鹏
     */
    private String productSettleStatus;

    /**
     * 产品结算比例-明鹏
     */
    private String productSettleProp;
}
