package com.msun.csm.model.csm;


import java.util.List;

import com.alibaba.fastjson.JSONObject;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @DESCRIPTION:
 * @AUTHOR: mengchuan
 * @DATE: 2022/12/15
 */
@Data
@ApiModel (value = "登录人员信息VO")
public class SysUserToMobileVO {

    @ApiModelProperty ("主键id")
    private Long sysUserId;

    @ApiModelProperty ("运营平台对应人员id")
    private String userYunyingId;

    @ApiModelProperty ("用户名称")
    private String userName;

    @ApiModelProperty ("账号")
    private String account;

    @ApiModelProperty ("邮箱")
    private String email;

    @ApiModelProperty ("手机号")
    private String phone;

    @ApiModelProperty ("性别")
    private String sex;

    @ApiModelProperty ("部门id")
    private Long deptId;

    @ApiModelProperty ("部门名称")
    private String deptName;

    @ApiModelProperty ("是否为院内用户【0：否；1：是】")
    private Integer hisUserFlag;

    @ApiModelProperty ("token")
    private String token;

    @ApiModelProperty ("人员所属角色列表")
    private List<UserVsRoleToMobileVO> userVsRoleVOList;

    @ApiModelProperty ("老系统人员id")
    private Long imspUserId;

    @ApiModelProperty ("是否校验【0：否；1：是】")
    private Integer isBound;

    @ApiModelProperty ("密钥")
    private String secretKey;

    @ApiModelProperty ("图片")
    private String imgSrc;

    @ApiModelProperty ("客户运维平台用户角色信息")
    private JSONObject knowUserInfo;

}
