package com.msun.csm.model.imsp;

import java.util.Date;

import javax.validation.constraints.NotNull;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @fileName:
 * @author:z<PERSON><PERSON><PERSON><PERSON>
 * @updateBy:
 * @Date:Created in 8:59 2024/5/7
 * @remark:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateMilestoneReq {

    /**
     * 主键
     */
    @NotNull (message = "里程碑节点主键不能为空")
    @ApiModelProperty (value = "主键")
    private Long milestoneInfoId;

    /**
     * 实际完成时间
     */
    @ApiModelProperty (value = "实际完成时间")
    @JsonFormat (pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat (pattern = "yyyy-MM-dd HH:mm")
    private Date actualCompTime;


    /**
     * 里程碑状态：0.未完成；1.已完成
     */
    @NotNull (message = "里程碑状态不能为空")
    @ApiModelProperty (value = "里程碑状态")
    private Integer milestoneStatus;


    /**
     * 里程碑节点负责人id
     */
    @ApiModelProperty (value = "里程碑节点负责人id")
    private Long nodeHeadId;
}
