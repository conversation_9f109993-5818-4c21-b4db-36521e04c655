package com.msun.csm.model.jimu.dto;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version : V1.52.0
 * @ClassName: ProjectPeriodDTO
 * @Description:
 * @Author: Yhongmin
 * @Date: 15:29 2024/9/29
 */
@Data
public class ProjectPeriodDTO {
    /**
     * 实施地客户名称
     */
    @ApiModelProperty(value = "实施地客户名称")
    private String customName;
    /**
     * 上线时间-开始
     */
    @ApiModelProperty(value = "上线时间-开始")
    @JsonProperty("onlinetime_begin")
    private Date onlinetimeBegin;
    /**
     * 上线时间-结束
     */
    @ApiModelProperty(value = "上线时间-结束")
    @JsonProperty("onlineTime_end")
    private Date onlinetimeEnd;



    /**
     * 申请时间-开始
     */
    @ApiModelProperty(value = "申请时间-开始")
    @JsonProperty("lastApplyAcceptTime_begin")
    private Date lastApplyAcceptTimeBegin;
    /**
     * 上线时间-结束
     */
    @ApiModelProperty(value = "申请时间-结束")
    @JsonProperty("lastApplyAcceptTime_end")
    private Date lastApplyAcceptTimeEnd;
    /**
     * 派单时间-开始
     */
    @ApiModelProperty(value = "派单时间-开始")
    @JsonProperty("worktime_begin")
    private Date worktimeBegin;
    /**
     * 派单时间-结束
     */
    @ApiModelProperty(value = "派单时间-结束")
    @JsonProperty("worktime_end")
    private Date worktimeEnd;

    /**
     * 客户类型 1新客户 2老换新 3老体系
     */
    @ApiModelProperty(value = "客户类型")
    private String upgradationType;

    /**
     * 客户类型
     */
    @ApiModelProperty(value = "客户类型")
    private List<Integer> upgradationTypeList;

    @ApiModelProperty(value = "客户合作状态")
    private Integer customDeliverStatus;

    /**
     *  上线状态， 已上线 ， 未上线
     */
    @ApiModelProperty(value = "上线状态")
    private Integer onlineType;

    /**
     *  客户
     */
    @ApiModelProperty(value = "部门")
    private String deptName;

    /**
     *  客服分公司
     */
    @ApiModelProperty(value = "客服分公司")
    private String customDeptName;
    @ApiModelProperty(value = "实施进度")
    private Integer projectDeliverStatus;
    @ApiModelProperty(value = "客户类型")
    private Integer customType;



}
