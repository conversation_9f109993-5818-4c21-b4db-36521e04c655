package com.msun.csm.model.tduck.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/7/30
 */
@Data
public class TDuckForm {
    /**
     * 采集名称
     */
    private String formName;

    /**
     * 适用医院，未填为适用全部医院
     */
    private Long customerId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 上传图片
     */
    private String imglist;

    /**
     * 上线必备
     * 0： 否
     * 1: 是
     */
    private String onlineEssential;

    /**
     * 产品id  platform.product 主键
     */
    private Integer productId;

    /**
     * print 打印类，statistics统计类
     */
    private String printType;

    /**
     * 节点(表单分类)
     */
    private String printDataCode;

    /**
     * 路径（维护表单工具路径或云健康内维护表单路径）
     */
    private String formUrl;

    /**
     * 产品CODE
     */
    private String appCode;


    @ApiModelProperty("区域系统项目id*******")
    private Long projectInfoId;

    @ApiModelProperty("调研来源id")
    private Long customerInfoId;

    /**
     * 是否是默认选项 1默认 0 非默认
     */
    private Integer defaultFlag;
}
