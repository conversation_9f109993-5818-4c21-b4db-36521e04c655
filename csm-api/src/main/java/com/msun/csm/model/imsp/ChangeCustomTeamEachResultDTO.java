package com.msun.csm.model.imsp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 变更实施团队
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ChangeCustomTeamEachResultDTO {

    /**
     * 运营平台工单id
     */
    @ApiModelProperty(value = "运营平台工单id")
    private Long yyOrderId;

    /**
     * 新实施团队ID
     */
    @ApiModelProperty(value = "新实施团队ID")
    private Long teamId;

    /**
     * 新实施团队负责人ID
     */
    @ApiModelProperty(value = "新实施团队负责人ID")
    private Long teamLeaderId;

    /**
     * 交付工单类型：1、自研软件；2、硬件；3、耗材；4、接口；5、软件服务费；6、硬件服务费；7、容灾；8、外采软件；9、云资源
     */
    @ApiModelProperty(value = "交付工单类型：1、自研软件；2、硬件；3、耗材；4、接口；5、软件服务费；6、硬件服务费；7、容灾；8、外采软件；9、云资源")
    private Integer deliveryOrderType;

    /**
     * 是否更新成功(false:失败, true:成功)
     */
    @ApiModelProperty(value = "是否更新成功(false:失败, true:成功)")
    private Boolean success;

    /**
     * 失败原因
     */
    @ApiModelProperty(value = "失败原因")
    private String message;


}
