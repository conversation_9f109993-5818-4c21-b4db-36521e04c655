package com.msun.csm.model.jimu.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version : V1.52.0
 * @ClassName: ReportCustomInfoVO
 * @Description:
 * @Author: Yhongmin
 * @Date: 14:07 2024/9/30
 */
@Data
public class JmReportCustomInfoVO {
    /**
     * 老换新标识
     */
    @ApiModelProperty(value = "老换新标识")
    private String upgradationType;
    /**
     * 客户类型
     */
    @ApiModelProperty(value = "客户类型")
    private String customType;
    /**
     * 实施地客户名称
     */
    @ApiModelProperty(value = "部门")
    private String deptName;
    /**
     * 分公司
     */
    @ApiModelProperty(value = "分公司")
    private String deptCompanyName;
    /**
     * 客户名称（统计表）
     */
    @ApiModelProperty(value = "客户名称（统计表）")
    private String customName;
    /**
     * 项目状态（临时）
     */
    @ApiModelProperty(value = "项目状态（临时）")
    private String customDeliverStatus;
    /**
     * 项目状态（交付平台）
     */
    @ApiModelProperty(value = "项目状态（交付平台）")
    private String projectDeliverStatus;
    /**
     * 派工时间
     */
    @ApiModelProperty(value = "派工时间")
    private String workTime;
    /**
     * 入驻时间
     */
    @ApiModelProperty(value = "入驻时间")
    private String settleInTime;
    /**
     * 上线时间
     */
    @ApiModelProperty(value = "上线时间")
    private String onlineTime;
    /**
     * 外部验收报告时间
     */
    @ApiModelProperty(value = "外部验收报告时间")
    private String externalAcceptTime;
    /**
     * 最后提交验收申请时间
     */
    @ApiModelProperty(value = "最后提交验收申请时间")
    private String applyAcceptTime;
    /**
     * 验收通过时间
     */
    @ApiModelProperty(value = "验收通过时间")
    private String acceptTime;
    /**
     * 入驻时间（统计表）
     */
    @ApiModelProperty(value = "入驻时间（统计表）")
    private String repSettleInTime;
    /**
     * 验收时间（统计表）
     */
    @ApiModelProperty(value = "验收时间（统计表）")
    private String repAcceptTime;
    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private String customInfoId;
    /**
     * 销售中心
     */
    @ApiModelProperty(value = "销售中心")
    private String salesCenter;
    /**
     * 省
     */
    @ApiModelProperty(value = "省")
    private String province;
    /**
     * 市
     */
    @ApiModelProperty(value = "市")
    private String city;
    /**
     * 县
     */
    @ApiModelProperty(value = "县")
    private String town;
    /**
     * 县
     */
    @ApiModelProperty(value = "统计表项目中台-临时")
    private String repDeliverStatus;
}
