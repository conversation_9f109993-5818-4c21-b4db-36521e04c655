package com.msun.csm.model.jimu.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/11/7
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryProductAuthDataReq {

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customInfoName;

    /**
     * 医院名称
     */
    @ApiModelProperty(value = "医院名称")
    private String hospitalInfoName;
}
