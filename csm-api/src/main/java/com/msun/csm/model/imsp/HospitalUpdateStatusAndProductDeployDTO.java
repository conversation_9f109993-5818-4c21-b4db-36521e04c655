package com.msun.csm.model.imsp;

import java.util.Map;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/28/10:28
 */
@Data
public class HospitalUpdateStatusAndProductDeployDTO {

    @ApiModelProperty(value = "申请单id")
    private Long deliverPlatformApplyId;

    @ApiModelProperty(value = "是否众阳付费 0-否,1-是")
    private Integer isMsunPay;

    @ApiModelProperty(value = "厂商")
    private String deployCloudVendor;

    @ApiModelProperty(value = "域名")
    private Map<String, Object> domainUrl;

    @ApiModelProperty(value = "交付文档地址")
    private String deliverTxtPath;

    @ApiModelProperty(value = "环境id")
    private Long envId;

    @ApiModelProperty(value = "环境名称")
    private String envName;

    @ApiModelProperty(value = "操作状态")
    private Integer operateType;

    @ApiModelProperty(value = "操作人")
    private String operateName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "部署资源类型. 7.容灾; 其他数字标识其他资源(云资源、医院、产品)")
    private Integer deployResourceType;

    @ApiModelProperty(value = "原环境名称")
    private String originalEnvName;

    @ApiModelProperty(value = "原环境id")
    private Long originalEnvId;

    @ApiModelProperty(value = "目标环境名称")
    private String destEnvName;

    @ApiModelProperty(value = "目标环境id")
    private Long destEnvId;

    @ApiModelProperty(value = "首次部署节点: 1是 0否")
    private Integer firstDeployNode;

    @ApiModelProperty(value = "是否是本地机房 1是 0否")
    private Integer isLocalRoom;

    @ApiModelProperty(value = "审核成功后：分配责任人姓名")
    private String assignerName;

    @ApiModelProperty(value = "审核成功后：分配责任人电话")
    private String assignerPhone;
}
