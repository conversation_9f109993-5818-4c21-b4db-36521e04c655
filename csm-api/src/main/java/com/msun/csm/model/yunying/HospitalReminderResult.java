package com.msun.csm.model.yunying;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 白名单返回值记录
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HospitalReminderResult {

    /**
     * 是否修改成功
     */
    private Boolean success;

    /**
     * 失败原因
     */
    private String message;

    /**
     * 产品解决方案类型ID：1单体医院,2区域,3医共体,4全县域医共体,8基层版单体医院
     */
    private Integer solutionType;

    /**
     * 白名单标识. 0:不列入白名单, 1:白名单
     */
    private Integer whiteListFlag;

    /**
     * 运营平台实施地客户id
     */
    private Long yyCustomerId;
}
