package com.msun.csm.model.imsp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version : V1.52.0
 * @ClassName: SyncYunRenewalApplicationRenewDTO
 * @Description:
 * @Author: Yhongmin
 * @Date: 16:31 2024/7/17
 */
@Data
public class SyncYunRenewalApplicationRenewDTO {
    /**
     * 运营平台工单id
     */
    @ApiModelProperty(value = "运营平台工单id")
    private Long yyWoId;
    /**
     * 运营平台实施地id
     */
    @ApiModelProperty(value = "运营平台实施地id")
    private Long customerId;

    /**
     * 解决方案. 1单体医院,2区域,3医共体,4全县域医共体,5疾控,6医保,7卫健,8基层版单体医院,9市平台,10云健康升级,11嵌入式软件,12运营服务
     */
    @ApiModelProperty(value = "解决方案. 1单体医院,2区域,3医共体,4全县域医共体,5疾控,6医保,7卫健,8基层版单体医院,9市平台,10云健康升级,11嵌入式软件,12运营服务")
    private Integer pemcusssolType;

    /**
     * 工单类型1、自研软件；2、硬件；3、耗材；4、接口；5、软件服务费；6、硬件服务费；7、容灾；8、外采软件；9、云资源
     */
    @ApiModelProperty(value = "工单类型1、自研软件；2、硬件；3、耗材；4、接口；5、软件服务费；6、硬件服务费；7、容灾；8、外采软件；9、云资源")
    private Integer type;
    /**
     * 时长（单位 月）
     */
    @ApiModelProperty(value = "时长（单位 月）")
    private Integer len;
    /**
     * 合同类型 1标准合同2借货合同
     */
    @ApiModelProperty(value = "合同类型 1标准合同2借货合同")
    private Integer contractType;
    /**
     * 软件工单编号
     */
    @ApiModelProperty(value = "软件工单编号")
    private Long softProjId;
    /**
     * 销售人员id不能为空
     */
    @ApiModelProperty(value = "销售人员id不能为空")
    private String saleUserId;
    /**
     * 上次云资源台账id
     */
    @ApiModelProperty(value = "上次云资源台账id")
    private Long customCloudServiceId;

    public SyncYunRenewalApplicationRenewDTO(Long yyWoId, Long customerId, Integer pemcusssolType, Integer type, Integer len, Integer contractType, String saleUserId, Long customCloudServiceId) {
        this.yyWoId = yyWoId;
        this.customerId = customerId;
        this.pemcusssolType = pemcusssolType;
        this.type = type;
        this.len = len;
        this.contractType = contractType;
        this.saleUserId = saleUserId;
        this.customCloudServiceId = customCloudServiceId;
    }
}
