package com.msun.csm.model.imsp;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* @description:
* @fileName: ThirdInterfaceVO.java
* @author: lius3
* @createAt: 2024/10/25 9:57
* @updateBy: lius3
* @remark: Copyright
*/
@Data
public class ThirdInterfaceVO {

    /**
     * 三方接口主键id
     */
    @ApiModelProperty(value = "三方接口主键id")
    private Long thirdInterfaceId;

    /**
     * 接口字典id
     */
    @ApiModelProperty (value = "接口字典id")
    private Long dictInterfaceId;

    /**
     * 接口字典名称
     */
    @ApiModelProperty (value = "接口字典名称")
    private String dictInterfaceName;

    /**
     * 上线必备标识
     */
    @ApiModelProperty (value = "上线必备标识")
    private String onlineFlag;

    /**
     * 期望完成时间
     */
    @ApiModelProperty (value = "期望完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date expectTime;

    /**
     * 接口状态【
     * 初始阶段： 0：未申请；1：提交裁定；
     * 裁定阶段： 11：裁定驳回；12、待评审；13：裁定通过；14：接口分公司驳回
     * 测试阶段： 21、测试环境申请授权；22：测试环境授权通过；23：研发中；24、测试环境测试完成；
     * 正式阶段： 31：申请正式环境授权；32：正式环境授权通过；33：研发完成；【下载SDK操作】
     * 50:接口完成 【提交完成按钮触发：三方对接的检测正式环境测试结果；项目组对接 检测正式环境测试结果与是否下载SDK】
     * 】
     */
    @ApiModelProperty (
            value = "接口状态【\n"
                    + "     初始阶段： 0：未申请；1：提交裁定；\n"
                    + "     裁定阶段： 11：裁定驳回；12、待评审；13：裁定通过；14：接口分公司驳回\n"
                    + "     测试阶段： 21、测试环境申请授权；22：测试环境授权通过；23：研发中；24、测试环境测试完成；\n"
                    + "     正式阶段： 31：申请正式环境授权；32：正式环境授权通过；33：研发完成；【下载SDK操作】\n"
                    + "     50:接口完成 【提交完成按钮触发：三方对接的检测正式环境测试结果；项目组对接 检测正式环境测试结果与是否下载SDK】\n"
                    + "     】")
    private Integer status;

    /**
     * 接口来源：1、项目交付，2、反馈单
     */
    @ApiModelProperty("接口来源：1、项目交付，2、反馈单")
    private Integer interfaceSource;
}
