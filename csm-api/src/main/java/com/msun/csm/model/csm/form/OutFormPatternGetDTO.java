package com.msun.csm.model.csm.form;

import com.msun.core.commons.api.BaseDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/12/20 16:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "查询实体", description = "/")
public class OutFormPatternGetDTO extends BaseDTO {

    @ApiModelProperty(value = "模板ID")
    private Long formPatternId;

}
