package com.msun.csm.model.jimu.dto;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version : V1.52.0
 * @ClassName: ReportCustomInfoDTO
 * @Description:
 * @Author: Yhongmin
 * @Date: 13:58 2024/9/30
 */
@Data
public class JmReportCustomInfoDTO {
    /**
     * 老换新标识
     */
    @ApiModelProperty(value = "老换新标识")
    private Integer upgradationType;
    /**
     * 客户类型
     */
    @ApiModelProperty(value = "客户类型")
    private Integer customType;
    /**
     * 实施地客户名称
     */
    @ApiModelProperty(value = "部门")
    private String deptName;
    /**
     * 分公司
     */
    @ApiModelProperty(value = "分公司")
    private String deptCompanyName;
    /**
     * 客户名称（统计表）
     */
    @ApiModelProperty(value = "客户名称（统计表）")
    private String customName;
    /**
     * 项目状态（临时）
     */
    @ApiModelProperty(value = "项目状态（临时）")
    private String customDeliverStatus;
    /**
     * 项目状态（交付平台）
     */
    @ApiModelProperty(value = "项目状态（交付平台）")
    private Integer projectDeliverStatus;
    /**
     * 派工时间
     */
    @JsonProperty("worktime_begin")
    @ApiModelProperty(value = "派工时间")
    private Date worktimeBegin;
    /**
     * 派工时间
     */
    @JsonProperty("worktime_end")
    @ApiModelProperty(value = "派工时间")
    private Date worktimeEnd;
    /**
     * 入驻时间
     */
    @JsonProperty("settleInTime_begin")
    @ApiModelProperty(value = "入驻时间")
    private Date settleInTimeBegin;
    /**
     * 入驻时间
     */
    @JsonProperty("settleInTime_end")
    @ApiModelProperty(value = "入驻时间")
    private Date settleInTimeEnd;
    /**
     * 上线时间
     */
    /*@JsonAlias({"onlinetime_begin"})*/
    @JsonProperty("onlinetime_begin")
    @ApiModelProperty(value = "上线时间")
    private Date onlineTimeBegin;
    /**
     * 上线时间
     */
    @JsonProperty("onlineTime_end")
    @ApiModelProperty(value = "上线时间")
    private Date onlineTimeEnd;
    /**
     * 外部验收报告时间
     */
    @JsonProperty("externalAcceptTime_begin")
    @ApiModelProperty(value = "外部验收报告时间")
    private Date externalAcceptTimeBegin;
    /**
     * 外部验收报告时间
     */
    @JsonProperty("externalAcceptTime_end")
    @ApiModelProperty(value = "外部验收报告时间")
    private Date externalAcceptTimeEnd;
    /**
     * 最后提交验收申请时间
     */
    @JsonProperty("applyAcceptTime_begin")
    @ApiModelProperty(value = "最后提交验收申请时间")
    private Date applyAcceptTimeBegin;
    /**
     * 最后提交验收申请时间
     */
    @JsonProperty("applyAcceptTime_end")
    @ApiModelProperty(value = "最后提交验收申请时间")
    private Date applyAcceptTimeEnd;
    /**
     * 验收通过时间
     */
    @JsonProperty("acceptTime_begin")
    @ApiModelProperty(value = "验收通过时间")
    private Date acceptTimeBegin;
    /**
     * 验收通过时间
     */
    @JsonProperty("acceptTime_begin")
    @ApiModelProperty(value = "验收通过时间")
    private Date acceptTimeEnd;
    /**
     * 入驻时间（统计表）
     */
    @JsonProperty("repSettleInTime_begin")
    @ApiModelProperty(value = "入驻时间（统计表）")
    private Date repSettleInTimeBegin;
    /**
     * 入驻时间（统计表）
     */
    @JsonProperty("repSettleInTime_end")
    @ApiModelProperty(value = "入驻时间（统计表）")
    private Date repSettleInTimeEnd;
    /**
     * 验收时间（统计表）
     */
    @JsonProperty("repAcceptTime_begin")
    @ApiModelProperty(value = "验收时间（统计表）")
    private Date repAcceptTimeBegin;
    /**
     * 验收时间（统计表）
     */
    @JsonProperty("repAcceptTime_end")
    @ApiModelProperty(value = "验收时间（统计表）")
    private Date repAcceptTimeEnd;
    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private String customInfoId;
    /**
     * 销售中心
     */
    @ApiModelProperty(value = "销售中心")
    private String salesCenter;
    /**
     * 省
     */
    @ApiModelProperty(value = "省")
    private Long province;
    /**
     * 市
     */
    @ApiModelProperty(value = "市")
    private String ctiy;
    /**
     * 县
     */
    @ApiModelProperty(value = "县")
    private String tow;
    @ApiModelProperty(value = "产品")
    private String productName;
}
