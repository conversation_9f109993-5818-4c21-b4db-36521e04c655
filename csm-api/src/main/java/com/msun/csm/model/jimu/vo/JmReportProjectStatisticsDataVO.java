package com.msun.csm.model.jimu.vo;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version : V1.52.0
 * @ClassName: ReportCustomInfoVO
 * @Description:
 * @Author: Yhongmin
 * @Date: 14:07 2024/9/30
 */
@Data
public class JmReportProjectStatisticsDataVO {
    /**
     * 老换新标识
     */
    @ApiModelProperty(value = "老换新标识")
    private String upgradationType;
    /**
     * 客户类型
     */
    @ApiModelProperty(value = "客户类型")
    private String customType;

    /**
     * 客户名称（统计表）
     */
    @ApiModelProperty(value = "客户名称（统计表）")
    private String customName;

    /**
     * 项目状态（交付平台）
     */
    @ApiModelProperty(value = "项目状态（交付平台）")
    private String projectDeliverStatus;
    /**
     * 派工时间
     */
    @ApiModelProperty(value = "派工时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date workTime;

    /**
     * 接收时间
     */
    @ApiModelProperty(value = "接收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date receiveTime;

    /**
     * 调研完成时间
     */
    @ApiModelProperty(value = "调研完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date surveyCompleteTime;

    /**
     * 入驻时间
     */
    @ApiModelProperty(value = "入驻时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date settleInTime;

    /**
     * 准备完成时间
     */
    @ApiModelProperty(value = "准备完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date preCompleteTime;
    /**
     * 上线时间
     */
    @ApiModelProperty(value = "上线时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date onlineTime;
    /**
     * 外部验收报告时间
     */
    @ApiModelProperty(value = "外部验收报告时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date externalAcceptTime;

    /**
     * 验收通过时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "验收通过时间")
    private Date acceptTime;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private Long customInfoId;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String deliveryOrderNo;

    @ApiModelProperty(value = "客户类型")
    private String telesalesFlag;

    // 调研后  调研后新增数量设备(个) 接口(个)  小硬件(个) 特殊需求(个)
    @ApiModelProperty(value = "调研后新增数量设备(个)")
    private Integer surveyAddEquip;
    @ApiModelProperty(value = "调研后新增接口(个)")
    private Integer surveyAddThird;
    @ApiModelProperty(value = "调研后小硬件(个)")
    private Integer surveyAddHardWare;
    @ApiModelProperty(value = "调研后特殊需求(个)")
    private Integer surveyAddSpecialRequire;

    //    入驻后新增产品数量
    //    新增产品(个)
    //    新增授权(个)
    @ApiModelProperty(value = "新增授权(个)")
    private Integer authorizationCount;
    @ApiModelProperty(value = "新增产品(个)")
    private Integer addProductCount;

    //    上线前未完成数量
    //    产品准备(个)
    //    必备打印报表(个)
    //    必备表单(个)
    //    必备接口(个)
    //    必备设备(个)
    @ApiModelProperty(value = "必备打印报表(个)")
    private Integer onlineFinsinReport;

    @ApiModelProperty(value = "必备表单(个)")
    private Integer onlineFinsinForm;

    @ApiModelProperty(value = "必备接口(个)")
    private Integer onlineFinsinThird;

    @ApiModelProperty(value = "必备设备(个)")
    private Integer onlineFinsinEquip;

    @ApiModelProperty(value = "产品准备(个)")
    private Integer onlineFinsinTask;

    // 汇总
    @ApiModelProperty(value = "汇总")
    private Integer total;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;
    @ApiModelProperty(value = "产品id")
    private Long yyProductId;
}
