package com.msun.csm.model.imsp;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 延期云资源参数
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SyncYunInfoDTO {

    /**
     * 运营平台工单id
     */
    @ApiModelProperty(value = "运营平台工单id")
    @NotNull(message = "运营平台工单id不能为空")
    private Long yyWoId;
    /**
     * 运营平台实施地id
     */
    @ApiModelProperty(value = "运营平台实施地客户id")
    @NotNull(message = "运营平台实施地客户id不能为空")
    private Long customerId;

    /**
     * 解决方案. 1单体医院,2区域,3医共体,4全县域医共体,5疾控,6医保,7卫健,8基层版单体医院,9市平台,10云健康升级,11嵌入式软件,12运营服务
     */
    @ApiModelProperty(value = "解决方案")
    @NotNull(message = "解决方案不能为空")
    private Integer pemcusssolType;

    /**
     * 工单类型1、自研软件；2、硬件；3、耗材；4、接口；5、软件服务费；6、硬件服务费；7、容灾；8、外采软件；9、云资源
     */
    @ApiModelProperty(value = "工单类型(1、自研软件；2、硬件；3、耗材；4、接口；5、软件服务费；6、硬件服务费；7、容灾；8、外采软件；9、云资源)")
    @NotNull(message = "工单类型不能为空")
    private Integer type;

    /**
     * 时长（单位 月）, 可以为空, 空则不更新
     */
    private Integer subscribeTerm;

    /**
     * 到期时间
     */
    @ApiModelProperty(value = "到期时间")
    @NotNull(message = "到期时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String subscribeEndTime;

    /**
     * 时长（单位 天）
     */
    @ApiModelProperty(value = "确认签字时间")
    @NotNull(message = "确认签字时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String planStartTime;

    /**
     * 最新的到期时间（同步系统管理到期时间使用）
     */
    @ApiModelProperty(value = "最新的到期时间（同步系统管理到期时间使用）")
    @NotNull(message = "最新的到期时间不能为空")
    private String latestExpDate;

    /**
     * 云资源到期提醒必填（可能存在没有的情况，但有必须要传，不然系统管理会清空发函时间）
     */
    @ApiModelProperty(value = "云资源到期提醒必填（可能存在没有的情况，但有必须要传，不然系统管理会清空发函时间）")
    private String sendLetterTime;

}
