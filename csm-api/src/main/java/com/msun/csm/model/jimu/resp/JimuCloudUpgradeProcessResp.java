package com.msun.csm.model.jimu.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class JimuCloudUpgradeProcessResp {

    /**
     * 部门id
     */
    private Long deptYYId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 总数
     */
    private int total;

    private int dttotal;

    private int qytotal;

    /**
     * 老体系
     */
    private int ltx;

    /**
     * 云健康-新客户
     */
    private int xkh;

    /**
     * 云健康-老换新
     */
    private int lhx;

    /**
     * 云健康-已上线
     */
    private int online;

    /**
     * 云健康-未上线
     */
    private int unOnline;

    /**
     * 云健康总数占比
     */
    private String totalRate;

    /**
     * 云健康已上线占比
     */
    private String onlineRate;

    private int xxhcount = 1;
    private int lhxFlag = 2;
    private int lxxFlag = 3;
    private int ysxFlag = 1;
    private int wsxFlag = 0;
    private int dtFlag = 1;
    private int qyFlag = 2;
    /**
     * 客户类型 1 单体 2 区域
     */
    private Integer customType;



}
