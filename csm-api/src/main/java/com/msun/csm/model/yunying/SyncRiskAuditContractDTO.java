package com.msun.csm.model.yunying;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 运营部反馈结果-合同号
 */
@Data
public class SyncRiskAuditContractDTO implements Serializable {

    /**
     * 合同号
     */
    private String contractStr;

    /**
     * 运营平台合同id
     */
    @NotNull
    private Long contractNum;
    /**
     * 状态 1通过  2未通过 3 特批跳过
     */
    @NotNull
    private Integer code;

}
