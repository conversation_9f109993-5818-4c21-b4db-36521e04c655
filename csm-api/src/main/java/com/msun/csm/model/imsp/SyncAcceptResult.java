package com.msun.csm.model.imsp;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 风控反馈结果
 *
 */
@Data
public class SyncAcceptResult implements Serializable {

    /**
     * 合同编号
     */
    @NotNull(message = "合同编号不能为空")
    private Long contractNum;

    /**
     * 工单号
     */
    @NotNull(message = "工单号不能为空")
    private Long workOrderId;

    /**
     * 验收时间
     */
    @NotNull(message = "验收时间不能为空")
    private String acceptTime;

    /**
     * 操作人Id
     */
    @NotNull(message = "操作人Id不能为空")
    private  Long accepterId;

    /**
     * 验收得分
     */
    @NotNull(message = "验收得分不能为空")
    private Integer score;

    /**
     * 验收结果 0 草稿 1 提交 2 通过 3 驳回 4 接收
     */
    @NotNull(message = "验收结果不能为空")
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 运营平台审核未通过的产品ids   ‘,’ 分割
     */
    private String unpassProductIds;

    /**
     * 验收次数 1 or 2
     */
    @NotNull(message = "验收次数不能为空")
    private Integer requiredAcceptanceTimes;

    /**
     * 当前是第几次 1 or 2
     */
    @NotNull(message = "当前是第几次不能为空")
    private Integer currentTimes;

}
