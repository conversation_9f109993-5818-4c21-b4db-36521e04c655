package com.msun.csm.model.imsp;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;

import cn.hutool.core.date.DateUtil;
import lombok.Data;

/**
 * 项目验收表
 *
 * @TableName proj_project_acceptance
 */
@Data
public class ProjProjectAcceptanceDTO implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long projectAcceptanceId;

    /**
     * 运营平台交付工单id
     */
    private Long yyOrderId;

    /**
     * 项目id
     */
    private Long projectInfoId;

    /**
     * 期望验收时间
     */
    private Date expectedAcceptanceTime;

    /**
     * 外部验收类型：0.未签署；1.外部验收报告；2.上线确认单
     */
    private Integer externalAcceptanceType;

    /**
     * 外部验收时间
     */
    private Long externalAcceptanceTime;

    /**
     * 申请验收时间
     */
    private Date applyAcceptanceTime;

    /**
     * 运营平台审核结果 2 通过 3 驳回 4 已接收
     */
    private Integer acceptanceStatus;

    /**
     * 验收人userid（运营平台id）
     */
    private Long accepterUserId;

    /**
     * 验收时间
     */
    private Date acceptanceTime;

    /**
     * 验收得分
     */
    private String acceptanceScore;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人id
     */
    private Long createrId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人id
     */
    private Long updaterId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 逻辑删除【0：否；1：是】
     */
    private Short isDeleted;

    /**
     * 运营平台审核未通过的产品ids   ‘,’ 分割
     */
    private String unpassProductIds;

    /**
     * 验收次数 1 or 2
     */
    private Integer requiredAcceptanceTimes;

    /**
     * 当前是第几次 1 or 2
     */
    private Integer currentTimes;

    public ProjProjectAcceptanceDTO(SyncAcceptResult result) {
        this.acceptanceScore = result.getScore().toString();
        this.yyOrderId = result.getWorkOrderId();
        this.acceptanceTime = DateUtil.parseDateTime(result.getAcceptTime());
        this.accepterUserId = result.getAccepterId();
        this.acceptanceStatus = result.getStatus();
        this.remark = result.getRemark();
        this.unpassProductIds = result.getUnpassProductIds();
        this.requiredAcceptanceTimes = result.getRequiredAcceptanceTimes();
        this.currentTimes            = result.getCurrentTimes();
    }
}
