package com.msun.csm.util.obs;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.obs.services.ObsClient;

/**
 * @DESCRIPTION:
 * @AUTHOR: mengchuan
 * @DATE: 2022/10/14
 */
@Configuration
public class OBSConfig {
    @Value ("${project.obs.accessKey}")
    private String accessKey;

    @Value ("${project.obs.secretKey}")
    private String secretKey;

    @Value ("${project.obs.endpoint}")
    private String endPoint;

    @Bean
    public ObsClient getObsClient() {
        ObsClient obsClient = new ObsClient(accessKey, secretKey, endPoint);
        return obsClient;
    }
}
