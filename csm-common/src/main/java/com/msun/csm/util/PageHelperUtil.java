package com.msun.csm.util;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import java.util.function.Function;

/**
 * <AUTHOR> 7630
 * @description: TODO
 * @date 2025/6/26 14:08
 */
public class PageHelperUtil {
    /**
     *
     * @param pageNo 页码
     * @param pageSize 每页大小
     * @param func 将查询逻辑写在这里，获取到的结果就会原膜原样返回
     * @return
     * @param <T>
     */
    public static <T> T queryPage(int pageNo, int pageSize, Function<Page<?>, T> func) {
        if (pageNo == 0) {
            pageNo = 1; // 默认第一页
        }
        if (pageSize == 0) {
            pageSize = 10; // 默认每页10条
        }
        try (Page<?> page = PageHelper.startPage(pageNo, pageSize)) {
            return func.apply(page);
        }
    }
}
