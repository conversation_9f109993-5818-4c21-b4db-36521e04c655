package com.msun.csm.util;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.EnumSet;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import org.apache.sshd.client.SshClient;
import org.apache.sshd.client.channel.ChannelExec;
import org.apache.sshd.client.channel.ClientChannelEvent;
import org.apache.sshd.client.future.AuthFuture;
import org.apache.sshd.client.future.ConnectFuture;
import org.apache.sshd.client.session.ClientSession;
import org.apache.sshd.sftp.client.SftpClientFactory;
import org.apache.sshd.sftp.client.fs.SftpFileSystem;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
public class SshdUtil {

    private SshClient client = SshClient.setUpDefaultClient();

    private ClientSession session = null;

    /**
     * @param host
     * @param port
     * @param username
     * @param password
     * @return
     * @description 连接
     */
    public SshdUtil(String host, int port, String username, String password) {
//        ClientSession session = null;
        try {
            // 开始连接
            client.start();
            ConnectFuture connectFuture = client.connect(username, host, port).verify(10, TimeUnit.SECONDS);
            if (connectFuture.isConnected()) {
                session = connectFuture.getSession();
                session.addPasswordIdentity(password);
                AuthFuture auth = session.auth().verify(10, TimeUnit.SECONDS);
                if (auth.isSuccess()) {
                    this.session = session;
                }
            }
        } catch (IOException e) {
            log.error("连接异常", e);
            try {
                if (session != null) {
                    session.close();
                }
                client.close();
            } catch (Exception err) {
                log.error("关闭异常", err);
            }
        }
    }

    /**
     * @return
     * @description 关闭连接
     */
    public void closeConnect() {
        try {
            if (session != null) {
                session.close();
            }
            client.close();
        } catch (IOException e) {
            log.error("关闭异常", e);
        }
    }

    /**
     * @param cmd
     * @return
     * @description 执行命令2
     */
    public ConcurrentHashMap<String, ByteArrayOutputStream> executeCmd(String cmd) {
        try {
            if (session != null) {
                log.info("netCheckCmd: {}", cmd);
                ConcurrentHashMap<String, ByteArrayOutputStream> map = new ConcurrentHashMap<>();
                ChannelExec channel = session.createExecChannel(cmd);
                ByteArrayOutputStream output = new ByteArrayOutputStream();
                ByteArrayOutputStream outputErr = new ByteArrayOutputStream();
                channel.setOut(output);
                channel.setErr(outputErr);
                channel.open();
                channel.waitFor(EnumSet.of(ClientChannelEvent.CLOSED), 0);
                channel.close();
                log.info("Output：{}", output.toString().replace("\n", "\\n"));
                log.info("Error：{}", outputErr.toString().replace("\n", "\\n"));
                map.put("Output", output);
                map.put("Error", outputErr);
                return map;
            }
        } catch (IOException e) {
            log.error("执行异常", e);
        }
        return null;
    }

    /**
     * @param sourcePath
     * @param fileName
     * @return
     * @description 上传文件
     */
    public void uploadFile(String sourcePath, String fileName) {
        if (session != null) {
            try (SftpFileSystem fs = SftpClientFactory.instance().createSftpFileSystem(session)) {
                Path remote = fs.getDefaultDir().resolve("/root");
                // 创建多层目录
                Files.createDirectories(remote);
                // 将目标文件拷贝至目标目录
                Files.copy(Paths.get(sourcePath), remote.resolve(fileName));
            } catch (Exception e) {
                log.error("发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
                throw new RuntimeException("获取文件异常！！！");
            }
        }
    }
}

