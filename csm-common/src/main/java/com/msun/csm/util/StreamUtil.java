package com.msun.csm.util;

import cn.hutool.core.collection.CollUtil;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2023-11-14
 */
public class StreamUtil {


    /**
     * Stream去重
     *
     * @param keyExtractor
     * @param <T>
     * @return
     */
    public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> map = new ConcurrentHashMap<>();
        return t -> map.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }


    /**
     * 将列表分片去处理
     *
     * @param source
     * @param stageSize
     * @param cb
     * @param <T>
     */
    public static <T> void chunked(List<T> source, int stageSize, Consumer<List<T>> cb) {
        if (CollUtil.isEmpty(source)) {
            return;
        }
        if (stageSize < 1) {
            throw new RuntimeException("批次大小必须大于0");
        }
        double total = source.size();
        int batchs = (int) Math.ceil(total / stageSize);
        for (int i = 0; i < batchs; i++) {
            if (i == batchs - 1) {
                cb.accept(source.subList(i * stageSize, (int) total));
            } else {
                cb.accept(source.subList(i * stageSize, (i + 1) * stageSize));
            }
        }
    }
}
