package com.msun.csm.util;

import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;

import java.lang.reflect.Type;


public class FastJsonCustomDeserializer implements ObjectDeserializer {

    @Override
    public <T> T deserialze(DefaultJSONParser jsonParser, Type type, Object o) {

        try {
            return jsonParser.parseObject(type);
        } catch (Throwable e) {
            return null;
        }
    }

    @Override
    public int getFastMatchToken() {
        return 0;
    }
}
