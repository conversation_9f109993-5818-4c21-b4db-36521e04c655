package com.msun.csm.util;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Validation;
import javax.validation.Validator;

import org.hibernate.validator.HibernateValidator;
import org.hibernate.validator.messageinterpolation.ResourceBundleMessageInterpolator;
import org.hibernate.validator.resourceloading.AggregateResourceBundleLocator;
import org.springframework.util.CollectionUtils;

public final class ValidateUtil {
    protected static final List<String> VALIDATION_MESSAGES = Collections.singletonList("validationMessage");
    public static final String SEPARATOR = "<br>";
    private static final Validator VALIDATOR;

    private ValidateUtil() {

    }

    public static <T> List<String> validateListResult(T bean, Class<?>... groups) {
        List<String> errorMessages = new ArrayList<>();
        Set<ConstraintViolation<T>> constraintViolations = VALIDATOR.validate(bean, groups);
        if (!CollectionUtils.isEmpty(constraintViolations)) {
            for (ConstraintViolation<T> violation : constraintViolations) {
                errorMessages.add(violation.getMessage());
            }
        }
        return errorMessages;
    }

    public static <T> Map<String, String> validateMapResult(T bean, Class<?>... groups) {
        Map<String, String> errorMessages = new HashMap<>();
        Set<ConstraintViolation<T>> constraintViolations = VALIDATOR.validate(bean, groups);
        if (!CollectionUtils.isEmpty(constraintViolations)) {
            for (ConstraintViolation<T> violation : constraintViolations) {
                errorMessages.put(violation.getPropertyPath().toString(), violation.getMessage());
            }
        }
        return errorMessages;
    }

    public static void validateWithException(Object bean, Class<?>... groups) {
        Set<? extends ConstraintViolation<?>> constraintViolations = VALIDATOR.validate(bean, groups);
        StringBuilder msg = new StringBuilder("参数校验未通过，校验结果：");
        if (!CollectionUtils.isEmpty(constraintViolations)) {
            constraintViolations.forEach(constraintViolation ->
                    msg.append(constraintViolation.getMessageTemplate())
            );
            throw new ConstraintViolationException(msg.toString(), constraintViolations);
        }
    }

    public static String validateListToString(List<String> errorList) {
        StringBuilder stringBuilder = new StringBuilder();
        for (String error : errorList) {
            stringBuilder.append(error);
            stringBuilder.append(SEPARATOR);
        }
        return stringBuilder.toString();
    }

    static {
        VALIDATOR = Validation.byProvider(HibernateValidator.class).configure().messageInterpolator(new ResourceBundleMessageInterpolator(new AggregateResourceBundleLocator(VALIDATION_MESSAGES))).buildValidatorFactory().getValidator();
    }
}
