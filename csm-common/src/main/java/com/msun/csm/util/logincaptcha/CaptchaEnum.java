package com.msun.csm.util.logincaptcha;

/**
 * 拼图验证码错误枚举
 *
 * <AUTHOR>
 * @since 2019-9-5
 */
public enum CaptchaEnum {

    /**
     * 拼图验证码注册TOKEN非法
     */
    JIGSAW_CAPTCHA_TOKEN_INVALID(9999999, "验证失败，请刷新验证码！"),
    /**
     * 拼图验证码滑动距离错误
     */
    JIGSAW_CAPTCHA_SLIDE_FAILED(8888888, "验证失败，请重试！"),
    /**
     * 生成验证码出错
     */
    JIGSAW_CAPTCHA_ERROR(7777777, "获取验证码错误，请刷新验证码！");

    /**
     * 错误码
     */
    private final int code;

    /**
     * 错误信息
     */
    private final String msg;

    public String msg() {
        return msg;
    }

    public int code() {
        return code;
    }

    CaptchaEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
