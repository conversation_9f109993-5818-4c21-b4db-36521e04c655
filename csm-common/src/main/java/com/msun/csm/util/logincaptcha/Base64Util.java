package com.msun.csm.util.logincaptcha;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;

import javax.imageio.ImageIO;

import lombok.extern.slf4j.Slf4j;

/**
 * base64
 *
 * <AUTHOR>
 * @since 2019-9-5
 */
@Slf4j
class Base64Util {

    /**
     * 把图片转换为base64
     *
     * @param image      图片
     * @param doCompress 是否压缩图片
     * @return
     */
    static String getImageBase64(BufferedImage image, boolean doCompress) {
        byte[] bytes;
        if (doCompress) {
            bytes = ImageUtil.compressImg(image);
            if (bytes == null) {
                return "";
            }
        } else {
            ByteArrayOutputStream bao = new ByteArrayOutputStream();
            try {
                ImageIO.write(image, "png", bao);
            } catch (IOException e) {
                log.error("压缩图片出错", e);
            }
            bytes = bao.toByteArray();
        }
        return Base64.getEncoder().encodeToString(bytes);
    }


}
