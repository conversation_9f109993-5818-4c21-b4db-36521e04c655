package com.msun.csm.util;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Name;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFDataValidation;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.ReadSheet;


/**
 * @classDesc: EasyExcel工具类
 * @author: lishen
 * @date: 2022/12/26 9:26
 * @copyright 众阳健康
 */
public class EasyExcelUtil {
    /**
     * 使用 模型 来读取Excel
     *
     * @param fileInputStream Excel的输入流
     * @param clazz           模型的类
     * @return 返回 模型 的列表
     */
    public static EasyExcelData readExcelWithModel(InputStream fileInputStream, Class<? extends Object> clazz)
            throws IOException {
        EasyExcelListener<Object> listener = new EasyExcelListener<Object>();
        ExcelReader excelReader = EasyExcel.read(fileInputStream, clazz, listener).build();
        ReadSheet readSheet = EasyExcel.readSheet(0).build();
        excelReader.read(readSheet);
        excelReader.finish();
        EasyExcelData easyExcelData = new EasyExcelData();
        easyExcelData.setDatas(listener.getDatas());
        easyExcelData.setHeads(listener.getHeads());
        return easyExcelData;
    }

    /**
     * 构建输出流
     *
     * @param fileName：文件名称
     * @param response：
     * @return OutputStream
     */
    public static OutputStream getOutputStream(String fileName, HttpServletResponse response) throws Exception {
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");
        // 设置前端跨域情况进行暴露的头名称
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        return response.getOutputStream();
    }

    /**
     * 导出时 增加下拉列表数据
     *
     * @param workbook 工作簿
     * @param firstRow 从哪一行开始添加下拉
     * @param col      哪一列添加
     * @param strings  下拉的内容
     */
    public static void addLongDropdown(Workbook workbook, int firstRow, int col, String[] strings) {
        XSSFSheet sheet = (XSSFSheet) workbook.getSheetAt(0);
        //默认为最大65535
        int endRow = 65535;
        //随便起个sheet名
        String hiddenName = "hidden" + col;
        //1.创建隐藏的sheet页。
        XSSFSheet hidden = (XSSFSheet) workbook.createSheet(hiddenName);
        //2.循环赋值（为了防止下拉框的行数与隐藏域的行数相对应，将隐藏域加到结束行之后）
        for (int i = 0, length = strings.length; i < length; i++) {
            hidden.createRow(endRow + i).createCell(col).setCellValue(strings[i]);
        }
        //创建可被其他单元格引用的名称
        Name workbookName = workbook.createName();
        workbookName.setNameName(hiddenName);
        //3 A1:A代表隐藏域创建第N列createCell(N)时。以A1列开始A行数据获取下拉数组
        workbookName.setRefersToFormula(hiddenName + "!A1:A" + (strings.length + endRow));
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createFormulaListConstraint(hiddenName);
        CellRangeAddressList addressList = new CellRangeAddressList(firstRow, endRow, col, col);
        DataValidation dataValidation = helper.createValidation(constraint, addressList);
        if (dataValidation instanceof XSSFDataValidation) {
            // 数据校验
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.setShowErrorBox(true);
        } else {
            dataValidation.setSuppressDropDownArrow(false);
        }
        // 作用在目标sheet上
        sheet.addValidationData(dataValidation);
        // 为了用户友好，设置hiddenSheet隐藏
        workbook.setSheetHidden(workbook.getSheetIndex(hiddenName), true);
    }


    /**
     * 监听器
     *
     * @param <T>
     */
    private static final class EasyExcelListener<T> extends AnalysisEventListener<T> {

        /**
         * 自定义用于暂时存储data
         * 可以通过实例获取该值
         */
        private List<T> datas = new ArrayList<>();

        /**
         * 表头
         */
        private Map<Integer, String> heads = new HashMap<>(16);

        /**
         * 每解析一行都会回调invoke()方法
         *
         * @param object  读取后的数据对象
         * @param context 内容
         */
        @Override
        public void invoke(Object object, AnalysisContext context) {
            T map = (T) object;
            //数据存储到list，供批量处理，或后续自己业务逻辑处理。
            datas.add(map);
        }

        /**
         * 表头信息
         *
         * @param headMap
         * @param context
         * @return void
         */
        @Override
        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
            heads = headMap;
        }

        /**
         * 所有数据解析完成了 都会来调用
         *
         * @param context
         * @return void
         */
        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            //解析结束销毁不用的资源
            //注意不要调用datas.clear(),否则getDatas为null
        }

        /**
         * 返回数据
         *
         * @return 返回读取的数据集合
         **/
        public List<T> getDatas() {
            return datas;
        }

        /**
         * 设置读取的数据集合
         *
         * @param datas 设置读取的数据集合
         **/
        public void setDatas(List<T> datas) {
            this.datas = datas;
        }

        /**
         * 表头信息
         *
         * @return 表头信息
         */
        public Map<Integer, String> getHeads() {
            return heads;
        }

        /**
         * 设置表头信息
         *
         * @param heads
         * @return void
         */
        public void setHeads(Map<Integer, String> heads) {
            this.heads = heads;
        }


    }
}
