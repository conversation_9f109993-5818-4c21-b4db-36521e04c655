package com.msun.csm.util;

import static com.msun.csm.common.enums.ResultEnum.VALIDATE_NULL_OR_EMPTY;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Objects;

import org.springframework.web.multipart.MultipartFile;

import com.alibaba.nacos.client.naming.utils.RandomUtils;
import com.msun.csm.common.exception.CustomException;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;


/**
 * @DESCRIPTION:
 * @AUTHOR: mengchuan
 * @DATE: 2022/12/28
 */
@Slf4j
public class FileUtil extends org.apache.tomcat.util.http.fileupload.FileUtils {
    public static String uploadFiles(MultipartFile file, String path) throws IOException {
        String fileName = file.getOriginalFilename();
        if (StrUtil.isBlank(fileName)) {
            throw new CustomException(VALIDATE_NULL_OR_EMPTY);
        }
        String fullFilePath = path + Md5Util.md5(fileName) + fileName.substring(fileName.lastIndexOf(StrUtil.DOT));
        cn.hutool.core.io.FileUtil.writeFromStream(file.getInputStream(), fullFilePath);
        return fullFilePath;
    }

    /**
     * 递归遍历文件夹下所有文件
     *
     * @param file
     * @param path
     */
    public static void recursionFiles(File file, String path) {
        for (File f : Objects.requireNonNull(file.listFiles())) {
            if (f.isDirectory()) {
                recursionFiles(f, path);
            } else {
                try {
                    File folder = new File(path);
                    if (!folder.exists()) {
                        folder.mkdirs();
                    }
                    FileInputStream fis = new FileInputStream(f.getAbsolutePath());
                    FileOutputStream fos = new FileOutputStream(path + f.getName());
                    byte[] b = new byte[1024];
                    int len;
                    while ((len = fis.read(b)) != -1) {
                        fos.write(b, 0, len);
                    }
                    fos.close();
                    fos.flush();
                    fis.close();
                } catch (IOException e) {
                    log.error("recursionFiles，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 随机获取背景图片
     *
     * @param captchaPath 验证码背景图保存路径
     * @return
     */
    public static File getSourceImage(String captchaPath) {
        String[] list = new File(captchaPath).list();
        String filename = "";
        if (list != null && list.length > 0) {
            filename = list[RandomUtils.nextInt(list.length)];
        }
        return new File(captchaPath + File.separator + filename);
    }

}
