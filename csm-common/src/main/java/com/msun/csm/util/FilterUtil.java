package com.msun.csm.util;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;


public class FilterUtil {


    /**
     * 函数式接口 T -> boolean
     *
     * @param function 函数表达式
     * @param <T>          泛型
     * @return boolean值
     */
    public static <T> Predicate<T> distinctByKey(Function<? super T, ?> function) {
        ConcurrentHashMap<Object, Boolean> map = new ConcurrentHashMap<>(16);
        return t -> map.putIfAbsent(function.apply(t), Boolean.TRUE) == null;
    }

    public static <T> Predicate<T> distinctByKeys(Function<? super T, ? extends List<?>> keyExtractors) {
        Map<List<?>, Boolean> seen = new ConcurrentHashMap<>();
        return t -> {
            List<?> keys = keyExtractors.apply(t);
            return seen.putIfAbsent(keys, Boolean.TRUE) == null;
        };
    }
}
