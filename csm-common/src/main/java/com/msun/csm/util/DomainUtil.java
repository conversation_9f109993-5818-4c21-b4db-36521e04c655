package com.msun.csm.util;

import cn.hutool.core.util.StrUtil;
import lombok.Builder;
import lombok.Data;


/**
 * 域名处理工具类
 */
public class DomainUtil {

    private static final String HTTPS_PROTOCOL = "https";
    private static final String HTTP_PROTOCOL = "http";
    private static final String HTTPS = "https://";
    private static final String HTTP = "http://";
    private static final String HTTP_PORT = "80";
    private static final String HTTPS_PORT = "443";

    /**
     * 获取检测端口号
     *
     * @param detectDomain
     * @return
     */
    public static DomainInfo getDomainInfo(String detectDomain) {
        String port = StrUtil.EMPTY;
        String host = StrUtil.EMPTY;
        String protocol = StrUtil.EMPTY;
        if (StrUtil.isNotEmpty(detectDomain)) {
            // 获取检测端口
            String tmp = detectDomain.replace(DomainUtil.HTTPS, StrUtil.EMPTY).replace(DomainUtil.HTTP, StrUtil.EMPTY);
            int start = tmp.indexOf(StrUtil.COLON);
            // 获取host和端口号
            if (start != -1) {
                port = tmp.substring(start + 1);
                host = tmp.replace(StrUtil.COLON + port, StrUtil.EMPTY);
            } else {
                port = detectDomain.contains(DomainUtil.HTTP) ? DomainUtil.HTTP_PORT : DomainUtil.HTTPS_PORT;
                host = tmp;
            }
            // 获取协议
            if (detectDomain.contains(DomainUtil.HTTPS)) {
                protocol = DomainUtil.HTTPS_PROTOCOL;
            } else {
                protocol = DomainUtil.HTTP_PROTOCOL;
            }
        }
        return DomainInfo.builder()
                .port(port)
                .host(host)
                .protocol(protocol)
                .build();
    }

    @Data
    @Builder
    public static class DomainInfo {
        /**
         * 协议
         */
        private String protocol;

        /**
         * 端口
         */
        private String port;

        /**
         * 域名主地址
         */
        private String host;
    }
}
