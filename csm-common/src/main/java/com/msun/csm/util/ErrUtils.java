package com.msun.csm.util;

import cn.hutool.core.util.StrUtil;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description //TODO
 * @Date 2024/8/16 10:12
 * <AUTHOR>
 **/
public class ErrUtils {
    public static final String ROOT_PKG = "com.msun.";


    public static String toString(Throwable e) {
        if (e == null) {
            return "";
        }
        StringWriter stringWriter = new StringWriter();
        e.printStackTrace(new PrintWriter(stringWriter));
        return stringWriter.toString();
    }

    public static String toSimpleString(Throwable e) {
        if (e == null) {
            return "";
        }
        List<String> errStacks = StrUtil.split(ErrUtils.toString(e), "\n");
        String errMsg = errStacks.stream().filter(val -> val.startsWith("\tat " + ROOT_PKG) || val.contains("cause")).collect(Collectors.joining("\n"));
        return StrUtil.format("{}\n{}", e.getMessage(), errMsg);
    }
}
