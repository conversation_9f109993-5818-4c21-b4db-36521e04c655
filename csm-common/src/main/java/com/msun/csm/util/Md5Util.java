package com.msun.csm.util;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import org.apache.commons.codec.binary.Hex;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> hyw, Date on 2022/6/8.
 */
@Slf4j
public class Md5Util {

    private static final char[] DIGITS_LOWER = new char[] {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b',
            'c', 'd', 'e', 'f'};
    private static final ThreadLocal<MessageDigest> MESSAGE_DIGEST_LOCAL = ThreadLocal.withInitial(() -> {
        try {
            return MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException var2) {
            return null;
        }
    });

    public static final String md5(String s) {
        try {
            byte[] strTemp = s.getBytes(StandardCharsets.UTF_8);
            MessageDigest mdTemp = MessageDigest.getInstance("MD5");
            mdTemp.update(strTemp);
            byte[] md = mdTemp.digest();
            return Hex.encodeHexString(md).toUpperCase();
        } catch (Exception e) {
            return StrUtil.EMPTY;
        }
    }

    public static final String md5str(String s) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            String md5Str = Hex.encodeHexString(md.digest(s.getBytes(StandardCharsets.UTF_8)));
            return md5Str;
        } catch (Exception e) {
            return StrUtil.EMPTY;
        }
    }

    public static String md5Hex(byte[] bytes) throws NoSuchAlgorithmException {
        String var2;
        try {
            MessageDigest messageDigest = MESSAGE_DIGEST_LOCAL.get();
            if (messageDigest == null) {
                throw new NoSuchAlgorithmException("MessageDigest get MD5 instance error");
            }
            var2 = encodeHexString(messageDigest.digest(bytes));
        } finally {
            MESSAGE_DIGEST_LOCAL.remove();
        }
        return var2;
    }

    public static String encodeHexString(byte[] bytes) {
        int l = bytes.length;
        char[] out = new char[l << 1];
        int i = 0;
        for (int j = 0; i < l; ++i) {
            out[j++] = DIGITS_LOWER[(240 & bytes[i]) >>> 4];
            out[j++] = DIGITS_LOWER[15 & bytes[i]];
        }
        return new String(out);
    }

    public static final String md5lower(String s) {
        try {
            byte[] strTemp = s.getBytes(StandardCharsets.UTF_8);
            MessageDigest mdTemp = MessageDigest.getInstance("MD5");
            mdTemp.update(strTemp);
            byte[] md = mdTemp.digest();
            return Hex.encodeHexString(md);
        } catch (Exception e) {
            return StrUtil.EMPTY;
        }
    }
}
