package com.msun.csm.util.logincaptcha;

import com.msun.csm.util.Md5Util;

/**
 * token
 *
 * <AUTHOR>
 * @since 2019-9-5
 */
class TokenUtil {

    /**
     * 创建token
     *
     * @return
     */
    static String createToken() {
        long rnd1 = Math.round(Math.random() * 100);
        long rnd2 = Math.round(Math.random() * 100);
        String md5Str1 = Md5Util.md5(rnd1 + "");
        String md5Str2 = Md5Util.md5(rnd2 + "");
        return md5Str1 + md5Str2.substring(0, 2);
    }

}
