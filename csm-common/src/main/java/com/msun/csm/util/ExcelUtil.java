package com.msun.csm.util;


import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.web.multipart.MultipartFile;

import com.msun.csm.common.model.dto.ExcelHeaderRowDTO;
import com.msun.csm.common.model.dto.ExcelSheetInfoDTO;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @description: excel工具类
 * @fileName: ExcelUtil.java
 * @author: caoyang
 * @createAt: 2021-03-26 9:04
 * @updateBy: caoyang
 * @remark: Copyright
 */
@Slf4j
public class ExcelUtil {
    private static Workbook wb = null;
    private static Sheet sheet = null;

    private static Cell cell = null;

    private static Row row = null;

    private static String k = null;

    /*public static HSSFSheet createExcelSheet(HSSFSheet sheet,HSSFWorkbook wb, List<String> header,
            List<List<String>> rows) {
        // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制short
        HSSFRow tableHeader = sheet.createRow((int) 0);
        // 第四步，创建单元格，并设置值表头 设置表头居中
        HSSFCellStyle style = wb.createCellStyle();
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER); // 创建一个居中格式
        HSSFCell cell = null;
        for (int i = 0; i < header.size(); i++) {
            cell = tableHeader.createCell(i, 0);
            cell.setCellValue(header.get(i));
            cell.setCellStyle(style);
        }
        // 自动换行
        style.setWrapText(true);
        if(null!=rows&&rows.size()>0){
        for (int i = 0; i < rows.size(); i++) {
            HSSFRow tableBody = sheet.createRow(i+1);
            for (int j = 0; j < rows.get(i).size(); j++) {
                cell = tableBody.createCell(j, 0);
                cell.setCellValue(rows.get(i).get(j));
                cell.setCellStyle(style);
            }
        }

    }
        return sheet;
    }*/

    /**
     * @param path
     * @param file
     * @param
     * @Title: downLoadFile
     * @Description: 文件下载
     */
    public static boolean downLoadFile(MultipartFile file, String path) {
        boolean excel = false;
        // 文件类型
        String type = null;
        // 文件原名称
        String fileName = file.getOriginalFilename();
        // 添加了自动创建目录的功能
        File file1 = new File(path);
        if (!file1.exists()) {
            file1.mkdir();
        }
        try {
            String filePath = path + fileName;
            file.transferTo(new File(filePath));
            // 转存文件到指定的路径
            excel = true;
        } catch (IllegalStateException e) {
            log.error("downLoadFile，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        } catch (IOException e) {
            log.error("发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        return excel;
    }

    /**
     * 读取Excel文件返回list数组 方法名: readExcel
     *
     * @param stream
     * @return
     * @throws IOException
     */
    public static String[][] readExcel(InputStream stream) throws IOException {
        DecimalFormat df = new DecimalFormat("0");
        wb = WorkbookFactory.create(stream);
        sheet = wb.getSheetAt(0);
        // 行数(从0开始,相当于最后一行的索引),列数·
        int countRow = sheet.getLastRowNum();
        int countCell = sheet.getRow(0).getPhysicalNumberOfCells();
        String[][] str = new String[countRow + 1][countCell];
        for (int i = 0; i <= countRow; i++) {
            for (int j = 0; j < countCell; j++) {
                /**
                 * 2020年6月30日17:08:36
                 * 读取导入的Excel文件时，一并读取列名
                 */
                // 获取这行数----包含列名
                row = sheet.getRow(i);
                // 判断行为空
                if (null != row) {
                    // 获取列
                    cell = row.getCell(j);
                    CellType type = CellType.BLANK;
                    if (cell != null) {
                        // 得到单元格数据类型
                        type = cell.getCellType();
                    }
//                    if (j == (countCell - 1)) {
//                    }
                    // 判断数据类型
                    switch (type) {
                        // 空值
                        case BLANK:
                            k = "";
                            break;
                        // 布尔至
                        case BOOLEAN:
                            k = cell.getBooleanCellValue() + "";
                            break;
                        // 错误
                        case ERROR:
                            k = cell.getErrorCellValue() + "";
                            break;
                        // 公式
                        case FORMULA:
                            k = cell.getCellFormula();
                            break;
                        // 数字
                        case NUMERIC:
                            if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                                // 格式化日期
                                k = new DataFormatter().formatRawCellContents(cell.getNumericCellValue(), 0,
                                        "yyyy-mm-dd");
                            } else {
                                k = df.format(cell.getNumericCellValue());
                            }
                            break;
                        // 字符串
                        case STRING:
                            k = cell.getStringCellValue();
                            break;
                        default:
                            break;
                    }
                    str[i][j] = k;
                } else {
                    break;
                }
            }
        }
        return str;
    }

    /**
     * @param
     * @Description: 删除原文件
     * <AUTHOR> 2017年7月10日16:05:28
     */
    public static void deleteExcel(String path) {
        File file = new File(path);
        // 删除D盘头像处理
        file.delete();
    }

    /**
     * 写入Excel表格数据
     *
     * @param
     * @param
     * @param
     * @param
     * @param ous
     */
    public static void exportExcel(HashMap<String, List<Object[]>> map, OutputStream ous) {
        // 创建新的Excel 工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();
        // 迭代器
        Iterator<String> it = map.keySet().iterator();
        while (it.hasNext()) {
            // 表名
            String tableName = it.next();
            // 表数据
            List<Object[]> list = map.get(tableName);
            // 在Excel工作簿中建一工作表，其名为缺省值, 也可以指定Sheet名称
            HSSFSheet newSheet = workbook.createSheet(tableName);
            for (int j = 0; j < list.size(); j++) {
                // 创建新行(row),并将单元格(cell)放入其中. 行号从0开始计算.
                HSSFRow newRow = newSheet.createRow(j);
                // 设置单元格类型
                HSSFCellStyle style = workbook.createCellStyle();
                // 水平布局：居中
                style.setAlignment(HorizontalAlignment.CENTER);
                // 一条记录的所有值
                Object[] cellValue = list.get(j);
                // 向每一行写入数据
                for (int i = 0; i < cellValue.length; i++) {
                    // 创建单元格
                    HSSFCell newCell = newRow.createCell(i);
                    // 设置单元格内容
                    newCell.setCellValue(new HSSFRichTextString(null == cellValue[i] ? "" : cellValue[i].toString()));
                    // 设置单元格样式
                    newCell.setCellStyle(style);
                }
            }
            // 只在导出人员名单时调整列宽
            if (1 == map.size()) {
                // 列数
                Integer ls = list.get(0).length;
                // 调整列宽
                for (int i = 0; i < ls; i++) {
                    newSheet.autoSizeColumn((short) i);
                }
            }
        }
        try {
            workbook.write(ous);
        } catch (IOException e) {
            log.error("exportExcel，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
    }

    public static synchronized Map<String, List<String[]>> uploadExcelFileBak(InputStream in, int beginRow,
                                                                              int beginCol) throws Exception {
        // 存储所有表数据的MAP
        Map<String, List<String[]>> map = new HashMap<String, List<String[]>>(16);
        // 声名一个工作薄
        Workbook rwb = new HSSFWorkbook(in);
        // 获得工作薄的个数
        Integer totalSheets = rwb.getNumberOfSheets();
        for (int s = 0; s < totalSheets; s++) {
            // 存储一个表数据
            List<String[]> lst = new ArrayList<String[]>();
            // 在Excel文档中，第一张工作表的缺省索引是0
            Sheet newSheet = rwb.getSheetAt(s);
            // 总行数
            int rows = newSheet.getLastRowNum();
            //当表格无数据时
            if (rows == 0 || newSheet.getRow(0) == null) {
                continue;
            }
            // 总列数
            int cols = newSheet.getRow(0).getPhysicalNumberOfCells();
            //获取最大列数
            for (int i = beginRow; i < rows; i++) {
                if (newSheet.getRow(i).getPhysicalNumberOfCells() > cols) {
                    cols = newSheet.getRow(i).getPhysicalNumberOfCells();
                }
            }
            // 循环行数
            for (int i = beginRow; i < rows; i++) {
                String[] array = new String[cols];
                // 获取这行数
                row = newSheet.getRow(i + 1);
                // 循环列数
                for (int j = beginCol; j < cols; j++) {
                    if (row.getCell(j) != null) {
                        String temp = StrUtil.trim(row.getCell(j).getStringCellValue());
                        temp = "null".equals(temp) ? "" : temp;
                        array[j] = temp;
                    } else {
                        array[j] = "";
                    }
                }
                lst.add(array);
            }
            map.put(newSheet.getSheetName(), lst);
        }
        rwb.close();
        return map;
    }

    /**
     * 读取Excel文件返回list数组
     *
     * @param stream
     * @param excelFilePath
     * @return
     * @throws IOException
     */
    public static String[][] readUploadExcel(InputStream stream, String excelFilePath) throws IOException {
        DecimalFormat df = new DecimalFormat("0");
        wb = WorkbookFactory.create(stream);
        sheet = wb.getSheetAt(0);
        // 行数(从0开始,相当于最后一行的索引),列数
        int countRow = sheet.getLastRowNum() + 1;
        int countCell = sheet.getRow(0).getPhysicalNumberOfCells();
        String[][] str = new String[countRow][countCell];
        for (int i = 0; i < countRow; i++) {
            for (int j = 0; j < countCell; j++) {
                // 获取这行数
                row = sheet.getRow(i);
                // 判断行为空
                if (null != row) {
                    // 获取列
                    cell = row.getCell(j);
                    CellType type = CellType.BLANK;
                    if (cell != null) {
                        // 得到单元格数据类型
                        type = cell.getCellType();
                    }
//                    if (j == (countCell - 1)) {
//                        // 不做处理
//                    }
                    // 判断数据类型
                    switch (type) {
                        // 空值
                        case BLANK:
                            k = "";
                            break;
                        // 布尔至
                        case BOOLEAN:
                            k = cell.getBooleanCellValue() + "";
                            break;
                        // 错误
                        case ERROR:
                            k = cell.getErrorCellValue() + "";
                            break;
                        // 公式
                        case FORMULA:
                            k = cell.getCellFormula();
                            break;
                        // 数字
                        case NUMERIC:
                            if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                                // 格式化日期
                                k = new DataFormatter().formatRawCellContents(cell.getNumericCellValue(), 0,
                                        "yyyy-mm-dd");
                            } else {
                                k = df.format(cell.getNumericCellValue());
                            }
                            break;
                        // 字符串
                        case STRING:
                            k = cell.getStringCellValue();
                            break;
                        default:
                            break;
                    }
                    str[i][j] = k;
                } else {
                    break;
                }
            }
        }
        return str;
    }

    public static void exportExcelBySheets(List<ExcelSheetInfoDTO> excelSheetInfoList, OutputStream outputStream) {
        // 新建Excel文件
        try (HSSFWorkbook hssfWorkbook = new HSSFWorkbook()) {
            // 设置单元格样式
            CellStyle cellStyle = hssfWorkbook.createCellStyle();
            // 水平样式为左对齐
            cellStyle.setAlignment(HorizontalAlignment.LEFT);
            // 垂直样式为居中
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            // 自动换行
            cellStyle.setWrapText(true);
            for (ExcelSheetInfoDTO createExcelParam : excelSheetInfoList) {
                // 创建sheet页
                hssfWorkbook.createSheet(createExcelParam.getSheetName());
                // sheet页对象
                HSSFSheet hssfSheet = hssfWorkbook.getSheet(createExcelParam.getSheetName());
                // 当前sheet页表头行
                ExcelHeaderRowDTO[] headerRowArray = createExcelParam.getHeaderRowArray();
                // 当前sheet页数据
                List<Map<String, String>> sheetData = createExcelParam.getSheetData();
                // 创建表头行（第一行）
                HSSFRow hssfRow = hssfSheet.createRow(0);
                // 行高：设置为-1表示自适应高度
                hssfRow.setHeightInPoints(-1);
                // 写入表头行
                for (int headerRowNumber = 0; headerRowNumber < headerRowArray.length; headerRowNumber++) {
                    HSSFCell hssfCell = hssfRow.createCell(headerRowNumber, CellType.BLANK);
                    hssfCell.setCellStyle(cellStyle);
                    hssfCell.setCellValue(headerRowArray[headerRowNumber].getName());
                }
                // 除表头之外的其他数据
                for (Map<String, String> dataItem : sheetData) {
                    // 创建行
                    HSSFRow newRow = hssfSheet.createRow(hssfSheet.getLastRowNum() + 1);
                    // 行高：设置为-1表示自适应高度
                    newRow.setHeightInPoints(-1);
                    for (int headerRowNumber = 0; headerRowNumber < headerRowArray.length; headerRowNumber++) {
                        HSSFCell hssfCell = newRow.createCell(headerRowNumber, CellType.BLANK);
                        hssfCell.setCellStyle(cellStyle);
                        hssfCell.setCellValue(dataItem.getOrDefault(headerRowArray[headerRowNumber].getField(), ""));
                    }
                }
            }
            hssfWorkbook.write(outputStream);
        } catch (Exception e) {
            log.error("导出Excel，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
    }
}
