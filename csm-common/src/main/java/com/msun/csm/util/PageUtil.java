package com.msun.csm.util;

import java.util.ArrayList;
import java.util.List;

import org.springframework.util.CollectionUtils;

import com.msun.csm.common.model.dto.PageList;
import com.msun.csm.common.model.dto.Pagination;

public class PageUtil {

    /**
     * 对List进行分页
     *
     * @param listParam 需要分页的数据
     * @param pageIndex 当前页码
     * @param pageSize  每页的数据条数
     * @param <T>       List的实体泛型
     * @return pageNum页对应的List数据
     */
    public static <T> PageList<T> getPageList(List<T> listParam, int pageIndex, int pageSize) {
        Pagination pagination = new Pagination();
        pagination.setCurrent(pageIndex);
        pagination.setPageSize(pageSize);
        pagination.setTotal(1);
        pagination.setDataCount(listParam == null ? 0 : listParam.size());

        PageList<T> objectPageList = new PageList<>();
        objectPageList.setPagination(pagination);
        objectPageList.setPageResultList(new ArrayList<>());

        // 需要分页的数据为空，直接返回
        if (CollectionUtils.isEmpty(listParam)) {
            return objectPageList;
        }

        // 总的数据条数
        int listCountNumber = listParam.size();

        // 总的页数
        int pageCount = listCountNumber % pageSize == 0 ? listCountNumber / pageSize : (listCountNumber / pageSize) + 1;

        // 分页结果
        pagination.setTotal(pageCount);

        // 截取的开始索引
        int startIndex;
        // 截取的结束索引
        int endIndex;
        if (pageIndex > pageCount) {
            pageIndex = 1;
            return getPageList(listParam, pageIndex, pageSize);
//            throw new IllegalArgumentException(CharSequenceUtil.format("当前页码【pageIndex】不可大于总页码【pageCount】，pageIndex={}，pageCount={}", pageIndex, pageCount));
        } else if (pageIndex == pageCount) {
            startIndex = (pageIndex - 1) * pageSize;
            endIndex = listCountNumber;
        } else {
            startIndex = (pageIndex - 1) * pageSize;
            endIndex = startIndex + pageSize;
        }
        List<T> ts = listParam.subList(startIndex, endIndex);
        objectPageList.setPageResultList(ts);
        objectPageList.setPagination(pagination);
        return objectPageList;
    }

}

