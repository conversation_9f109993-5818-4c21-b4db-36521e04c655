package com.msun.csm.util;

import cn.hutool.core.date.DateTime;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;


/**
 * <AUTHOR>
 * @since 2023-06-007
 */
@Slf4j
public class DateUtil {

    public static final DateTimeFormatter DFY_MD_HMS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter DFY_MD = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final String DATE_FORMAT_YMD = "yyyy-MM-dd";

    /**
     * LocalDateTime 转时间戳
     *
     * @param localDateTime /
     * @return /
     */
    public static Long getTimeStamp(LocalDateTime localDateTime) {
        return localDateTime.atZone(ZoneId.systemDefault()).toEpochSecond();
    }

    /**
     * 时间戳转LocalDateTime
     *
     * @param timeStamp /
     * @return /
     */
    public static LocalDateTime fromTimeStamp(Long timeStamp) {
        return LocalDateTime.ofEpochSecond(timeStamp, 0, OffsetDateTime.now().getOffset());
    }

    /**
     * LocalDateTime 转 Date
     * Jdk8 后 不推荐使用 {@link Date} Date
     *
     * @param localDateTime /
     * @return /
     */
    public static Date toDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * LocalDate 转 Date
     * Jdk8 后 不推荐使用 {@link Date} Date
     *
     * @param localDate /
     * @return /
     */
    public static Date toDate(LocalDate localDate) {
        return toDate(localDate.atTime(LocalTime.now(ZoneId.systemDefault())));
    }


    /**
     * Date转 LocalDateTime
     * Jdk8 后 不推荐使用 {@link Date} Date
     *
     * @param date /
     * @return /
     */
    public static LocalDateTime toLocalDateTime(Date date) {
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    /**
     * 日期 格式化
     *
     * @param localDateTime /
     * @param patten        /
     * @return /
     */
    public static String localDateTimeFormat(LocalDateTime localDateTime, String patten) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(patten);
        return df.format(localDateTime);
    }

    public static String localDateFormat(LocalDate localDate, String patten) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(patten);
        return df.format(localDate);
    }

    /**
     * 日期 格式化
     *
     * @param localDateTime /
     * @param df            /
     * @return /
     */
    public static String localDateTimeFormat(LocalDateTime localDateTime, DateTimeFormatter df) {
        return df.format(localDateTime);
    }

    /**
     * 日期格式化 yyyy-MM-dd HH:mm:ss
     *
     * @param localDateTime /
     * @return /
     */
    public static String localDateTimeFormatyMdHms(LocalDateTime localDateTime) {
        return DFY_MD_HMS.format(localDateTime);
    }

    /**
     * 字符串转 LocalDateTime ，字符串格式 yyyy-MM-dd
     *
     * @param localDateTime /
     * @return /
     */
    public static LocalDateTime parseLocalDateTimeFormat(String localDateTime, String pattern) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        return LocalDateTime.from(dateTimeFormatter.parse(localDateTime));
    }

    /**
     * 字符串转 LocalDateTime ，字符串格式 yyyy-MM-dd
     *
     * @param localDateTime /
     * @return /
     */
    public static LocalDateTime parseLocalDateTimeFormat(String localDateTime, DateTimeFormatter dateTimeFormatter) {
        return LocalDateTime.from(dateTimeFormatter.parse(localDateTime));
    }

    /**
     * 字符串转 LocalDateTime ，字符串格式 yyyy-MM-dd HH:mm:ss
     *
     * @param localDateTime /
     * @return /
     */
    public static LocalDateTime parseLocalDateTimeFormatyMdHms(String localDateTime) {
        return LocalDateTime.from(DFY_MD_HMS.parse(localDateTime));
    }

    /**
     * 日期格式化 yyyy-MM-dd
     *
     * @param localDateTime /
     * @return /
     */
    public String localDateTimeFormatyMd(LocalDateTime localDateTime) {
        return DFY_MD.format(localDateTime);
    }

    /**
     * 获取今天-HH:mm:ss
     *
     * @return String
     */
    public static String getToHourMinuteSecondString() {
        SimpleDateFormat formatDate = new SimpleDateFormat("HH:mm:ss");
        return formatDate.format(new Date());
    }

    /**
     * 获取今天-HHmmss
     *
     * @return String
     */
    public static String getHourMinuteSecondString() {
        SimpleDateFormat formatDate = new SimpleDateFormat("HHmmss");
        return formatDate.format(new Date());
    }

    /**
     * 获取今天
     *
     * @return String
     */
    public static String getTodayString() {
        SimpleDateFormat formatDate = new SimpleDateFormat("yyyy-MM-dd");
        return formatDate.format(new Date());
    }

    /**
     * 说明: 返回两个时间之间天数，天数
     *
     * @param fromDate
     * @param endDate
     * @return:java.lang.Integer
     * @author: Yhongmin
     * @createAt: 2024/5/10 20:25
     * @remark: Copyright
     */
    public static Integer fromDayToDayNum(Date fromDate, Date endDate) {
        Integer dayNum = 0;
        if (fromDate == null || endDate == null) {
            return dayNum;
        }
        Date nextDay = fromDate;
        // 当明天不在结束时间之前是终止循环
        while (nextDay.before(endDate)) {
            Calendar cld = Calendar.getInstance();
            cld.setTime(nextDay);
            cld.add(Calendar.DATE, 1);
            fromDate = cld.getTime();
            // 获得下一天日期字符串
            nextDay = fromDate;
            dayNum++;
        }
        return dayNum;
    }

    /**
     * 说明: 字符转转Date时间
     *
     * @param dateStr
     * @return:java.util.Date
     * @author: Yhongmin
     * @createAt: 2024/7/5 16:19
     * @remark: Copyright
     */
    public static Date getDateFormatYmd(String dateStr) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_YMD);
            return sdf.parse(dateStr);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 说明: 比较两个时间
     *
     * @param date1
     * @param date2
     * @return:java.lang.Integer
     * @author: Yhongmin
     * @createAt: 2024/8/20 17:55
     * @remark: Copyright
     */
    public static Integer getCompareTo(Date date1, Date date2) {
        int result = date1.compareTo(date2);
        return result;
    }

    /**
     * 计算两个时间之间的天数差（不足或正好24小时按一天，超过24小时的部分另算一天）
     *
     * @param start 开始时间
     * @param end   结束时间
     * @return 天数差（若开始时间晚于结束时间，返回0）
     */
    public static int getDifferentDays(Date start, Date end) {
        double s = Long.valueOf(start.getTime()).doubleValue();
        double e = Long.valueOf(end.getTime()).doubleValue();
        double b = e - s;
        if (b < 0) {
            return 0;
        }
        return Double.valueOf(Math.ceil(b / 86400000.0)).intValue();
    }

    public static void main(String[] args) {
        DateTime start = cn.hutool.core.date.DateUtil.parse("2024-10-10 00:00:00", "yyyy-MM-dd HH:mm:ss");
        DateTime end = cn.hutool.core.date.DateUtil.parse("2024-10-12 00:00:01", "yyyy-MM-dd HH:mm:ss");

        int differentDays = getDifferentDays(start, end);
        log.info("相差 -> {}", differentDays);
    }
}
