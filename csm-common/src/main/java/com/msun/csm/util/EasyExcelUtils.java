package com.msun.csm.util;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.row.AbstractRowHeightStyleStrategy;
import org.apache.poi.ss.usermodel.*;

import java.util.*;

public class EasyExcelUtils {
    public static class RowHeightConfig extends AbstractRowHeightStyleStrategy {
        /**
         * 默认高度
         */
        private static final Integer DEFAULT_HEIGHT = 300;

        @Override
        protected void setHeadColumnHeight(Row row, int relativeRowIndex) {

        }

        @Override
        protected void setContentColumnHeight(Row row, int relativeRowIndex) {
            Iterator<Cell> cellIterator = row.cellIterator();
            if (!cellIterator.hasNext()) {
                return;
            }
            // 默认为 1行高度
            int maxHeight = 1;
            while (cellIterator.hasNext()) {
                Cell cell = cellIterator.next();
                if (cell.getCellTypeEnum() == CellType.STRING) {
                    String value = cell.getStringCellValue();
                    int len = value.length();
                    int num = 0;
                    if (len > 50) {
                        num = len % 50 > 0 ? len / 50 : len / 2 - 1;
                    }
                    if (num > 0) {
                        for (int i = 0; i < num; i++) {
                            value = value.substring(0, (i + 1) * 50 + i) + "\n" + value.substring((i + 1) * 50 + i, len + i);
                        }
                    }
                    if (value.contains("\n")) {
                        int length = value.split("\n").length;
                        maxHeight = Math.max(maxHeight, length) + 1;
                    }
                }
            }
            row.setHeight((short) ((maxHeight) * DEFAULT_HEIGHT));
        }
    }

    public static class ColWidthConfig extends AbstractColumnWidthStyleStrategy {
        private final Map<Integer, Map<Integer, Integer>> cache = new HashMap<>();

        protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer integer, Boolean isHead) {
            boolean needSetWidth = isHead || !CollUtil.isEmpty(cellDataList);
            if (needSetWidth) {
                Map<Integer, Integer> maxColumnWidthMap = cache.computeIfAbsent(writeSheetHolder.getSheetNo(), k -> new HashMap<>());

                Integer columnWidth = this.dataLength(cellDataList, cell, isHead);
                // 单元格文本长度大于60换行
                if (columnWidth >= 0) {
                    if (columnWidth > 60) {
                        columnWidth = 60;
                    }
                    Integer maxColumnWidth = maxColumnWidthMap.get(cell.getColumnIndex());
                    if (maxColumnWidth == null || columnWidth > maxColumnWidth) {
                        maxColumnWidthMap.put(cell.getColumnIndex(), columnWidth);
                        Sheet sheet = writeSheetHolder.getSheet();
                        sheet.setColumnWidth(cell.getColumnIndex(), columnWidth * 256);
                    }
                }
            }
        }

        /**
         * 计算长度
         *
         * @param cellDataList
         * @param cell
         * @param isHead
         * @return
         */
        private Integer dataLength(List<WriteCellData<?>> cellDataList, Cell cell, Boolean isHead) {
            if (isHead) {
                return cell.getStringCellValue().getBytes().length;
            } else {
                CellData<?> cellData = cellDataList.get(0);
                CellDataTypeEnum type = cellData.getType();
                if (type == null) {
                    return -1;
                } else {
                    switch (type) {
                        case STRING:
                            // 换行符（数据需要提前解析好）
                            int index = cellData.getStringValue().indexOf("\n");
                            return index != -1
                                    ?
                                    cellData.getStringValue().substring(0, index).getBytes().length + 1
                                    :
                                    cellData.getStringValue().getBytes().length + 1;
                        case BOOLEAN:
                            return cellData.getBooleanValue().toString().getBytes().length;
                        case NUMBER:
                            return cellData.getNumberValue().toString().getBytes().length;
                        default:
                            return -1;
                    }
                }
            }
        }
    }

    public static HorizontalCellStyleStrategy getStyle() {
//自定义表头样式  浅橙色 居中
        WriteCellStyle headCellStyle = new WriteCellStyle();
        headCellStyle.setFillForegroundColor(IndexedColors.TAN.getIndex());  //表头颜色
        headCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);    //文本居中
        //字体
        WriteFont writeFont = new WriteFont();
        writeFont.setFontName("微软雅黑");                                   //字体
        writeFont.setFontHeightInPoints((short) 10);                         //字体大小
        headCellStyle.setWriteFont(writeFont);
        // 自动换行
        headCellStyle.setWrapped(true);

        //内容样式
        WriteCellStyle contentCellStyle = new WriteCellStyle();
        contentCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER); //文本居中
        contentCellStyle.setWriteFont(writeFont);
        //设置边框
        contentCellStyle.setBorderLeft(BorderStyle.THIN);                    //左边框线
        contentCellStyle.setBorderTop(BorderStyle.THIN);                     //顶部框线
        contentCellStyle.setBorderRight(BorderStyle.THIN);                   //右边框线
        contentCellStyle.setBorderBottom(BorderStyle.THIN);                  //底部框线
        ArrayList<WriteCellStyle> contentCells = new ArrayList<>();
        contentCells.add(contentCellStyle);
        //样式策略
        HorizontalCellStyleStrategy handler = new HorizontalCellStyleStrategy();
        handler.setHeadWriteCellStyle(headCellStyle);                        //表头样式
        handler.setContentWriteCellStyleList(contentCells);                  //内容样式
        return new HorizontalCellStyleStrategy(headCellStyle, contentCells);
    }

}
