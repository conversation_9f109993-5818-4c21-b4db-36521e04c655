package com.msun.csm.util;

import static com.msun.csm.common.staticvariable.StaticPara.MACOS_ZIP_PACKAGE;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.RandomAccessFile;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.sevenzipjbinding.IInArchive;
import net.sf.sevenzipjbinding.SevenZip;
import net.sf.sevenzipjbinding.impl.RandomAccessFileInStream;


/**
 * @DESCRIPTION:
 * @AUTHOR: mengchuan
 * @DATE: 2022/8/15
 */
@Slf4j
public class CompressUtil {

    /**
     * zip文件压缩
     *
     * @param inputFile  待压缩文件夹/文件名
     * @param outputFile 生成的压缩包名字
     */

    public static void zipCompress(String inputFile, String outputFile) throws IOException {
        //创建zip输出流
        ZipOutputStream out = new ZipOutputStream(Files.newOutputStream(Paths.get(outputFile)));
        File input = new File(inputFile);
        compress(out, input, null);
        out.close();
    }

    /**
     * @param name 压缩文件名，可以写为null保持默认
     *             递归压缩
     */

    public static void compress(ZipOutputStream out, File input, String name) throws IOException {
        if (name == null) {
            name = input.getName();
        }
        //如果路径为目录（文件夹）
        if (input.isDirectory()) {
            //取出文件夹中的文件（或子文件夹）
            File[] flist = input.listFiles();
            //如果文件夹为空，则只需在目的地zip文件中写入一个目录进入
            assert flist != null;
            if (flist.length == 0) {
                out.putNextEntry(new ZipEntry(name + StrUtil.SLASH));
            } else {
                //如果文件夹不为空，则递归调用compress，文件夹中的每一个文件（或文件夹）进行压缩
                for (File file : flist) {
                    compress(out, file, name + StrUtil.SLASH + file.getName());
                }
            }
        } else {
            //如果不是目录（文件夹），即为文件，则先写入目录进入点，之后将文件写入zip文件中
            out.putNextEntry(new ZipEntry(name));
            FileInputStream fos = new FileInputStream(input);
            int len;
            //将源文件写入到zip文件中
            byte[] buf = new byte[1024];
            while ((len = fos.read(buf)) != -1) {
                out.write(buf, 0, len);
            }
            fos.close();
        }
    }

    /**
     * zip解压
     *
     * @param inputFile   待解压文件名
     * @param destDirPath 解压路径
     */

    public static void zipUncompress(String inputFile, String destDirPath) throws Exception {
        //获取当前压缩文件
        File srcFile = new File(inputFile);
        // 判断源文件是否存在
        if (!srcFile.exists()) {
            throw new Exception(srcFile.getPath() + "所指文件不存在");
        }
        //开始解压
        //构建解压输入流
        ZipInputStream zIn = new ZipInputStream(Files.newInputStream(srcFile.toPath()), Charset.forName("GBK"));
        ZipEntry entry;
        File file;
        while ((entry = zIn.getNextEntry()) != null) {
            if (!entry.getName().startsWith(MACOS_ZIP_PACKAGE)) {
                if (!entry.isDirectory()) {
                    file = new File(destDirPath, entry.getName());
                    if (!file.exists()) {
                        //创建此文件的上级目录
                        new File(file.getParent()).mkdirs();
                    }
                    OutputStream out = Files.newOutputStream(file.toPath());
                    int len;
                    byte[] buf = new byte[1024];
                    while ((len = zIn.read(buf)) != -1) {
                        out.write(buf, 0, len);
                    }
                    // 关流顺序，先打开的后关闭
                    out.close();
                }
            }
        }
    }

    /**
     * 解压rar
     *
     * @param rarPath     需要解压的rar文件全路径
     * @param destDirPath 需要解压到的文件目录
     * @throws Exception
     */
    public static void rarUncompress(String rarPath, String destDirPath) throws IOException {
        IInArchive archive;
        RandomAccessFile randomAccessFile = new RandomAccessFile(rarPath, "r");
        // 第一个参数是需要解压的压缩包路径，第二个参数参考JdkAPI文档的RandomAccessFile
        //r代表以只读的方式打开文本，也就意味着不能用write来操作文件
        archive = SevenZip.openInArchive(null, new RandomAccessFileInStream(randomAccessFile));
        int[] in = new int[archive.getNumberOfItems()];
        for (int i = 0; i < in.length; i++) {
            in[i] = i;
        }
        archive.extract(in, false, new ExtractCallback(archive, destDirPath));
        archive.close();
        randomAccessFile.close();
    }


    private static void getFiles(File rootFile, List<File> files) {
        if (!rootFile.isDirectory()) {
            files.add(rootFile);
            return;
        }
        for (File file : rootFile.listFiles()) {
            getFiles(file, files);
        }
    }
}
