package com.msun.csm.util;

import java.security.MessageDigest;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * <AUTHOR> 2020-06-11 实现跨平台的AES加密解密算法
 */
public class CrossPlatformAesMd5Utils {
    /**
     * 获取加密内容和key值的字节序列的编码方式
     */
    private static final String BYTES_CHARTSET = "utf-8";

    /**
     * 获取SecretKeySpec的算法
     */
    private static final String SECRET_KEY_SPEC_ALGORITHM = "AES";

    /**
     * 初始向量
     */
    public static final String VIPARA = "zEXaPrpd67fPrkMr";

    /**
     * 加密方式/加密模式/填充方式
     */
    public static final String CIPHER_TYPE = "AES/CBC/PKCS5Padding";

    /**
     * 摘要算法
     */
    public static final String DIGEST_ALGORITHM = "MD5";

    /**
     * AES加密
     *
     * @param source 字符串内容
     * @param key    密钥
     */
    public static String aesEncrypt(String source, String key) {
        return aes(source, key, Cipher.ENCRYPT_MODE);
    }


    /**
     * AES解密
     *
     * @param source 字符串内容
     * @param key    密钥
     */
    public static String aesDecrypt(String source, String key) {
        return aes(source, key, Cipher.DECRYPT_MODE);
    }

    /**
     * MD5摘要
     *
     * @param source
     * @return
     */
    public static String md5Encrypt(String source) {
        try {
            MessageDigest mdInstance = MessageDigest.getInstance(DIGEST_ALGORITHM);
            byte[] sourceBytes = source.getBytes(BYTES_CHARTSET);
            byte[] digestBytes = mdInstance.digest(sourceBytes);
            return parseByte2HexStr(digestBytes);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * AES加密算法
     * 1、获取字节序列的字符集编码：UTF-8
     * 2、加密模式：CBC
     * 3、密钥长度：128
     * 4、iv向量：zEXaPrpd67fPrkMr
     * 5、填充方式padding：PKCS5Padding
     *
     * @param source 需要加密的字符穿
     * @param key    加密密钥
     * @param type   加密：{@link Cipher#ENCRYPT_MODE}，解密：{@link Cipher#DECRYPT_MODE}
     * @return
     */
    private static String aes(String source, String key, int type) {
        try {
            // 设置AES密钥
            byte[] keyBytes = key.getBytes(BYTES_CHARTSET);
            SecretKeySpec skeySpec = new SecretKeySpec(keyBytes, SECRET_KEY_SPEC_ALGORITHM);
            // 设置初始向量
            byte[] ivBytes = VIPARA.getBytes(BYTES_CHARTSET);
            IvParameterSpec iv = new IvParameterSpec(ivBytes);
            Cipher cipher = Cipher.getInstance(CIPHER_TYPE);
            cipher.init(type, skeySpec, iv);
            if (type == Cipher.ENCRYPT_MODE) {
                byte[] sourceBytes = source.getBytes(BYTES_CHARTSET);
                byte[] encrypted = cipher.doFinal(sourceBytes);
                // 二进制数组转十六进制字符串
                return parseByte2HexStr(encrypted);
            } else {
                // 十六进制字符串转二进制数组
                byte[] sourceBytes = parseHexStr2Byte(source);
                byte[] decrypted = cipher.doFinal(sourceBytes);
                return new String(decrypted, BYTES_CHARTSET);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 二进位组转十六进制字符串
     *
     * @param buf 二进位组
     * @return 十六进制字符串
     */
    public static String parseByte2HexStr(byte[] buf) {
        StringBuilder sb = new StringBuilder();
        for (byte b : buf) {
            String hex = Integer.toHexString(b & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            sb.append(hex.toUpperCase());
        }
        return sb.toString();
    }

    /**
     * 十六进制字符串转二进位组
     *
     * @param hexStr 十六进制字符串
     * @return 二进位组
     */
    public static byte[] parseHexStr2Byte(String hexStr) {
        if (hexStr.length() < 1) {
            return null;
        }
        byte[] result = new byte[hexStr.length() / 2];
        for (int i = 0; i < hexStr.length() / 2; i++) {
            int high = Integer.parseInt(hexStr.substring(i * 2, i * 2 + 1), 16);
            int low = Integer.parseInt(hexStr.substring(i * 2 + 1, i * 2 + 2), 16);
            result[i] = (byte) (high * 16 + low);
        }
        return result;
    }
}
