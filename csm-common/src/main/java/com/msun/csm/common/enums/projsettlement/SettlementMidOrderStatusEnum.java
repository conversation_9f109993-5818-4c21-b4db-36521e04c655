package com.msun.csm.common.enums.projsettlement;

import lombok.Getter;

/**
 * 免中间件审核状态。0 准申请(已具备申请条件), 1 已申请, 2 审核驳回, 3 审核通过
 */
@Getter
public enum SettlementMidOrderStatusEnum {

    PRE_APPLY(0, "准申请(已具备申请条件)"),
    APPLYED(1, "已申请"),
    REJECTED(2, "审核驳回"),
    APPROVED(3, "审核通过");

    private final int code;

    private final String desc;


    SettlementMidOrderStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据节点code获取节点枚举
     */
    public static SettlementMidOrderStatusEnum getSettlementMidOrderStatus(int midOrderStatus) {
        for (SettlementMidOrderStatusEnum value : SettlementMidOrderStatusEnum.values()) {
            if (midOrderStatus == value.getCode()) {
                return value;
            }
        }
        return null;
    }
}
