package com.msun.csm.common.enums.projprojectinfo;

import lombok.Getter;

/**
 * @Description: 项目实施状态枚举
 * @Author: MengChuAn
 * @Date: 2024/4/25
 */
@Getter
public enum ProjectDeliverStatusEnums {
    //项目状态 1已派工、2调研中、3已入驻、4准备中、5已上线、6已验收  11 一次验收通过
    //已派工、已调研、已入驻、已上线、已验收
    //1已派工
    DELIVERED(1, "已派工"),
    //2调研中
    RESEARCHING(2, "已调研"),
    //3已入驻
    SETTLED(3, "已入驻"),
    //4准备完成
    PREPARING(4, "已准备完成"),
    //5已上线
    ONLINE(5, "已上线"),
    //6已验收
    ACCEPTED(6, "已验收"),
    //7已启动
    //STARTED(7, "已启动");
    FIRST_ACCEPTED(11, "一次验收通过");


    private Integer code;
    private String name;

    ProjectDeliverStatusEnums(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ProjectDeliverStatusEnums getEnum(int code) {
        for (ProjectDeliverStatusEnums s : values()) {    //values()方法返回enum实例的数组
            if (code == s.getCode()) {
                return s;
            }
        }
        return null;
    }

    public static ProjectDeliverStatusEnums getName(String name) {
        for (ProjectDeliverStatusEnums s : values()) {    //values()方法返回enum实例的数组
            if (name == s.getName()) {
                return s;
            }
        }
        return null;
    }
}
