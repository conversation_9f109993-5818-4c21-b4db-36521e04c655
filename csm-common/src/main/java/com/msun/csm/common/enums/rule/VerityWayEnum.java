package com.msun.csm.common.enums.rule;

import lombok.Getter;

/**
 * 验证方式：no 不验证；upload必须上传文件；prompt仅提示必须满足
 */
@Getter
public enum VerityWayEnum {
    NO("no", "不验证"),
    UPLOAD("upload", "必须上传文件"),
    PROMPT("prompt", "仅提示必须满足");

    private final String type;
    private final String desc;

    VerityWayEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
