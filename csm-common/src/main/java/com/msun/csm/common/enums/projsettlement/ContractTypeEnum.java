package com.msun.csm.common.enums.projsettlement;

import lombok.Getter;

/**
 * 合同类型。 1标准合同 2借货合同 3新产品试用协议 4客户协议 5其他
 */
@Getter
public enum ContractTypeEnum {
    STANDARD_CONTRACT(1, "标准合同"),
    BORROW_CONTRACT(2, "借货合同"),
    NEW_RRODUCT_TRIAL_CONTRACT(3, "新产品试用协议"),
    CUSTOM_CONTRACT(4, "客户协议"),
    OTHER_CONTRACT(5, "其他");

    private final Integer code;

    private final String desc;

    ContractTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
