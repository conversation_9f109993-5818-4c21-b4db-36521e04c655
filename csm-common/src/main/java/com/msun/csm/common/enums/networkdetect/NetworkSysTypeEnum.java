package com.msun.csm.common.enums.networkdetect;

import cn.hutool.core.util.StrUtil;

/**
 * 网络检测系统分类
 */
public enum NetworkSysTypeEnum {

    WINDOWS(0, "windows系统", ".exe"),

    LINUX(1, "linux系统", StrUtil.EMPTY),

    OTHER(2, "其他系统", StrUtil.EMPTY);


    private int code;

    /**
     * 工具下载时使用的后缀
     */
    private String suffix;

    private String desc;

    NetworkSysTypeEnum(int code, String desc, String suffix) {
        this.code = code;
        this.desc = desc;
        this.suffix = suffix;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getSuffix() {
        return suffix;
    }

    /**
     * 根据系统类型编码查询后缀
     *
     * @param code
     * @return
     */
    public static NetworkSysTypeEnum getEnumBySysTypeCode(int code) {
        for (NetworkSysTypeEnum value : NetworkSysTypeEnum.values()) {
            if (code == value.getCode()) {
                return value;
            }
        }
        return NetworkSysTypeEnum.OTHER;
    }
}
