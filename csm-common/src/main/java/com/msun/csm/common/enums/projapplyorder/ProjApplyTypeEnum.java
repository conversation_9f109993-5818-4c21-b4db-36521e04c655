package com.msun.csm.common.enums.projapplyorder;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/28/8:44
 */
public enum ProjApplyTypeEnum {

    FIRST_EVN_APPLY(1, "首次环境申请", "env"),
    HOSPITAL_APPLY(2, "增加医院", "hospital"),
    PRODUCT_APPLY(3, "增加产品", "product");


    private final Integer code;

    private final String desc;
    private final String devCode;


    ProjApplyTypeEnum(Integer code, String desc, String devCode) {
        this.code = code;
        this.desc = desc;
        this.devCode = devCode;
    }

    /**
     * 通过code获取描述
     *
     * @param code
     * @return
     */
    public static String getDesc(Integer code) {
        for (ProjApplyTypeEnum s : values()) {
            if (code.equals(s.code)) {
                return s.desc;
            }
        }
        return null;
    }

    public static ProjApplyTypeEnum getEnumByCode(int code) {
        for (ProjApplyTypeEnum s : values()) {    //values()方法返回enum实例的数组
            if (code == s.getCode()) {
                return s;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getDevCode() {
        return devCode;
    }
}

