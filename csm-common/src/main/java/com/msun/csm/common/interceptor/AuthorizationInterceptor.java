package com.msun.csm.common.interceptor;


import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;
import javax.servlet.http.<PERSON>ie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSON;
import com.msun.csm.common.annotation.ACROSS;
import com.msun.csm.common.annotation.CsmSign;
import com.msun.csm.common.dao.entity.config.ConfigAuthorization;
import com.msun.csm.common.dao.mapper.config.ConfigAuthorizationMapper;
import com.msun.csm.common.enums.ResultEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.staticvariable.StaticPara;
import com.msun.csm.util.RedisUtil;
import com.msun.csm.util.StringUtils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description
 * @date 2018/8/3
 */

@Slf4j
public class AuthorizationInterceptor implements HandlerInterceptor {

    /**
     * 存放鉴权信息的Header名称，token
     */
    private final String httpHeaderName = StaticPara.HEADER_TOKEN_NAME;
    private static final String SIGN = "Authorization";


    @Resource
    private RedisUtil redisUtil;
    @Resource
    private ConfigAuthorizationMapper configAuthorizationMapper;


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        //获取方法全路径
        String methodFullPath = method.getDeclaringClass().getName() + "." + method.getName();
        //验证三方系统调用sign
        if (method.getAnnotation(CsmSign.class) != null) {
            return this.validateSign(request);
        }
        // 如果打上了across注解则不需要验证token
        if (method.getAnnotation(ACROSS.class) == null
                //swagger相关配置跳过
                && !methodFullPath.contains("springfox")
                && !methodFullPath.contains("jmreport")
                && !method.getName().contains("error")
                && !method.getName().contains("invoke")
                && handlerMethod.getBeanType().getAnnotation(ACROSS.class) == null) {
            log.debug("AuthorizationInterceptor method is:{},token is:{} ;", method.getName(),
                    request.getHeader(httpHeaderName));
            String tokenStr = request.getHeader(httpHeaderName);
            //鉴权
            if (StringUtils.isBlank(tokenStr)) {
                Cookie[] cookies = request.getCookies();
                log.info("cookies is:{}", JSON.toJSONString(cookies));
                if (ObjectUtils.isEmpty(cookies)) {
                    log.error("{} # {}", method.getName(), ResultEnum.NO_LOGIN.getDesc());
                    throw new CustomException(ResultEnum.NO_LOGIN);
                }
                Cookie ck = Arrays.stream(cookies).filter(cookie -> cookie.getName().equals(httpHeaderName)).findFirst()
                        .orElse(null);
                if (ObjectUtils.isEmpty(ck)) {
                    log.error("{} # {}", method.getName(), ResultEnum.NO_LOGIN.getDesc());
                    throw new CustomException(ResultEnum.NO_LOGIN);
                }
                tokenStr = ck.getValue();
            }
            //更新redis中的数据缓存时间
            Object value = redisUtil.get(tokenStr);
            if (ObjectUtils.isEmpty(value)) {
                log.error("{} # {}", methodFullPath, ResultEnum.AUTH_FAIL.getDesc());
                throw new CustomException(ResultEnum.AUTH_FAIL);
            }
            redisUtil.set(tokenStr, value, StaticPara.REDIS_CACHE_TIME, TimeUnit.HOURS);
            response.addHeader("Access-Control-Allow-Headers", "token,content-type");
        }
        return true;
    }

    //验证三方系统调用sign
    private boolean validateSign(HttpServletRequest request) {
        //获取方法uri
        String methodPath = request.getRequestURI().replace(request.getContextPath() + StrUtil.SLASH, "");
        log.info("methodPath is:{}", methodPath);
        //判断header中是否有sign-CSM_SIGN
        if (request.getHeader(SIGN) == null) {
            log.error("{} # {}", methodPath, ResultEnum.ILLEGAL_REQUEST.getDesc());
            throw new CustomException(ResultEnum.ILLEGAL_REQUEST);
        }
        //1.获取sign，解密数据
        String csmSign = request.getHeader(SIGN);
        log.info("校验外部接口sign值 method is:{},sign原始串 is:{} ;", methodPath, csmSign);
        String[] csmSignArr = csmSign.split(";");
        String appId = csmSignArr[0].split("=")[1];
        String sign = csmSignArr[1].split("=")[1];
        //2.验证appId和appSecret是否正确
        ConfigAuthorization authorization = configAuthorizationMapper.selectByAppId(appId);
        RSA rsa = new RSA(authorization.getPrivateKey(), null);
        String signStr = rsa.decryptStr(sign, KeyType.PrivateKey);
        log.info("校验外部接口sign值 method is:{},sign解密串 is:{} ;", methodPath, signStr);
        String[] signArr = signStr.split("&");
        String appIdSign = signArr[0].split("=")[1];
        if (!appIdSign.equals(authorization.getAppId())) {
            log.error("{} # {}", methodPath, ResultEnum.ILLEGAL_REQUEST.getDesc());
            throw new CustomException(ResultEnum.ILLEGAL_REQUEST);
        }
        //3.校验时间戳-15秒有效
        String timestamp = signArr[2].split("=")[1];
        long now = System.currentTimeMillis();
        if (now - Long.parseLong(timestamp) > 300000 * 6) {
            log.error("{} # {}", methodPath, ResultEnum.OVER_TIME.getDesc());
            throw new CustomException(ResultEnum.OVER_TIME);
        }
        //4.判断接口是否在白名单中
        String[] methods = authorization.getMethods().split(",");
        Arrays.asList(methods).stream().filter(methodPath::contains).findAny()
                .orElseThrow(() -> new CustomException(ResultEnum.NO_AUTH));
        log.info("校验外部接口sign值通过 method is:{} ;", methodPath);
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                           ModelAndView modelAndView) {
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
                                Exception ex) {
    }
}
