package com.msun.csm.common.enums;

public enum HospitalLevelEnum {
    THIRD_LEVEL(1, "三级医院"),
    SCEND_LEVEL(2, "二级医院"),
    FIRST_LEVEL(3, "一级医院"),
    FU_YOU_LEVEL(4, "妇幼保健院"),
    OTHER_LEVEL(5, "其他专科医院（眼科医院、口腔医院）");


    HospitalLevelEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    private final Integer id;

    private final String name;

    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public Long getIdForLong() {
        return Long.valueOf(String.valueOf(id));
    }
}
