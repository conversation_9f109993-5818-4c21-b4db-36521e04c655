package com.msun.csm.common.enums;

import lombok.Getter;

/**
 * @description:产品实施状态枚举类
 * @fileName:
 * @author:<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @updateBy:
 * @Date:Created in 9:41 2024/5/23
 * @remark:
 */
@Getter
public enum ExcutionStatusEnum {
    NOT_ONLINE(0, "未上线"),
    ONLINE(1, "已上线"),
    ACCEPT_NOT_PASS(2, "验收未通过"),
    ACCEPT_PASS(3, "验收通过");

    private Integer code;
    private String desc;

    ExcutionStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
