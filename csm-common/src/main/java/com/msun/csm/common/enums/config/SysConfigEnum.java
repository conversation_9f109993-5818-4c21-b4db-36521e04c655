package com.msun.csm.common.enums.config;


/**
 * 妇幼系统配置枚举类 - 规范系统配置工具类
 *
 * <AUTHOR>
 * @date 2021/12/28 16:35
 */
public enum SysConfigEnum {

    ceshId("ceshId", "0", "是否开启字段过滤"),
    OLD_SYSTEM_VERSION_DETECTION_TABLE("old_system_version_detection_table", "", "老换新版本检测-表格数据初始化配置"),
    CLOUD_CONFIG_RESEARCH("CLOUD_CONFIG_RESEARCH", "", "云健康产品配置可读性调研"),
    NETWORK_TOOL("NETWORK_TOOL", "", "获取网络检测工具");

    /**
     * 配置Code
     */
    private String configCode;
    /**
     * 配置默认值
     */
    private String configValue;
    /**
     * 配置描述
     */
    private String configDesc;


    SysConfigEnum(String configCode, String configValue, String configDesc) {
        this.configCode = configCode;
        this.configValue = configValue;
        this.configDesc = configDesc;
    }

    public String getConfigCode() {
        return configCode;
    }

    public String getConfigValue() {
        return configValue;
    }

    public String getConfigDesc() {
        return configDesc;
    }

}
