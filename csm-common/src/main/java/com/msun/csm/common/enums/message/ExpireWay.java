package com.msun.csm.common.enums.message;

/**
 * 消息过期方式，0未过期，1读取过期，2处理过期，3超过有效期限后自动过期，4需要后期处理的消息读取时过期
 */
public enum ExpireWay {
    UNEXPIRE(0, "未过期"),
    EXPIRE_READ(1, "读取过期"),
    EXPIRE_HANDLE(2, "处理过期"),

    EXPIRE_AUTO_EXCEED_EXPIRETIME(3, "超过有效期限后自动过期"),
    EXPIRE_NEED_BACK_HANDLE(4, "需要后期处理的消息读取时过期");

    private int code;

    private String desc;

    ExpireWay(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
