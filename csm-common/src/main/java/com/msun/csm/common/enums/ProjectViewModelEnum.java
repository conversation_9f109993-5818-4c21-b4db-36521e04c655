package com.msun.csm.common.enums;


public enum ProjectViewModelEnum {

    MILESTONE("milestone", "里程碑模式"),
    PROJECT_PLAN("projectPlan", "项目计划模式");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String description;

    ProjectViewModelEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
