package com.msun.csm.common.enums.sysfile;

import lombok.Getter;

/**
 * 文件系统枚举
 */
@Getter
public enum SysFileEnum {

    RECIEPT_MODEL("reciept_model", "disaster_recovery", "云容灾交付回执单模板"),
    CONFIRMATION_MODEL("confirmation_model", "disaster_recovery", "云容灾交付确认涵模板");

    private final String fileCode;

    private final String businessCode;

    private final String desc;

    SysFileEnum(String fileCode, String businessCode, String desc) {
        this.fileCode = fileCode;
        this.businessCode = businessCode;
        this.desc = desc;
    }
}
