package com.msun.csm.common.enums.could;

import lombok.Getter;

/**
 * 页面阶段状态
 */
@Getter
public enum DisasterCloudMainStatusEnum {
    BASIC(0, "基础信息"),
    OPEN(1, "开通"),
    CHECK(2, "验收"),
    CHECK_RESULT(3, "验收结果"),
    AUTO_RENEWAL(4, "自动续期");

    private final Integer code;
    private final String desc;

    DisasterCloudMainStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DisasterCloudMainStatusEnum getEnum(int code) {
        for (DisasterCloudMainStatusEnum s : values()) {    //values()方法返回enum实例的数组
            if (code == s.getCode()) {
                return s;
            }
        }
        return null;
    }
}
