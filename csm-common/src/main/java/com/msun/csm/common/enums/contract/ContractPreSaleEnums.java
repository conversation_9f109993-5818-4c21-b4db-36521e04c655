package com.msun.csm.common.enums.contract;

import lombok.Getter;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/25
 */
@Getter
public enum ContractPreSaleEnums {
    //华北方案分公司
    HUA_BEI(1011L, 10630006L),
    //山东方案分公司
    SHAN_DONG(1013L, 10630001L),
    //华中方案分公司
    HUA_ZHONG(1015L, 10630004L),
    //西北方案分公司
    XI_BEI(1053L, 10630005L),
    //华南方案分公司
    HUA_NAN(1060L, 10630007L),
    //西南方案分公司
    XI_NAN(1061L, 10630008L),
    //东北方案分公司
    DONG_BEI(1062L, 10630009L);


    private final Long pid;
    private final Long preSalerId;


    ContractPreSaleEnums(Long pid, Long preSalerId) {
        this.pid        = pid;
        this.preSalerId = preSalerId;
    }

    public static ContractPreSaleEnums getEnum(Long pid) {
        for (ContractPreSaleEnums s : values()) {    //values()方法返回enum实例的数组
            if (pid.equals(s.getPid())) {
                return s;
            }
        }
        return null;
    }
}
