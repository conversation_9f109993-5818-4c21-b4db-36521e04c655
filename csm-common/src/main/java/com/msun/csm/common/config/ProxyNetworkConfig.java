package com.msun.csm.common.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import lombok.Getter;

/**
 * 出口网关, 访问云健康使用
 */
@Component
public class ProxyNetworkConfig {

    @Getter
    private static String proxyNetworkUrl;

    @Value("${proxy.networkOutUrl}")
    public void setProxyNetworkUrl(String url) {
        proxyNetworkUrl = url;
    }

}
