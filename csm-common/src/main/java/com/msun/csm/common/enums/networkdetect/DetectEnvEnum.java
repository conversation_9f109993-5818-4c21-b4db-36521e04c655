package com.msun.csm.common.enums.networkdetect;

/**
 * 检测环境
 */
public enum DetectEnvEnum {

    DEV(1, "演示环境"),
    CLOUD(2, "云健康环境"),
    RECORD(3, "记录");

    private int code;

    private String desc;

    DetectEnvEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
