package com.msun.csm.common.enums.projreview;


import cn.hutool.core.util.StrUtil;
import lombok.Getter;

/**
 * 自检结果：0.不满足；1.已满足；2.不需要
 */
@Getter
public enum SelfReviewResultEnum {

    NOT_SATISFIED(0, "不满足"),

    SATISFIED(1, "满足"),

    NOT_REQUIRED(2, "PMO提交审核");

    private final int code;

    private final String desc;

    SelfReviewResultEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据编码获取编码名称
     *
     * @param code 编码
     * @return String
     */
    public static String getDescByCode(int code) {
        for (SelfReviewResultEnum value : SelfReviewResultEnum.values()) {
            if (value.getCode() == code) {
                return value.getDesc();
            }
        }
        return StrUtil.EMPTY;
    }
}
