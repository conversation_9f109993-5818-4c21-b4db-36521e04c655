package com.msun.csm.common.enums.projprojectinfo;

import lombok.Getter;

/**
 * @Description: 新系统项目数据来源标识枚举  生成数据
 * @Author: MengChuAn
 * @Date: 2024/6/27
 */
@Getter
public enum ProjectSourceEnums {

    ORDER_GENERATE(1, "派工单生成"),
    DATA_TRANS(2, "老项目数据迁移");

    private final int code;

    private final String name;

    ProjectSourceEnums(int code, String name) {
        this.code = code;
        this.name = name;
    }
}
