package com.msun.csm.common.model.yunying;

import java.io.Serializable;

import com.msun.csm.common.enums.ResultEnum;
import com.msun.csm.common.exception.CustomException;

import lombok.Data;


/**
 * @param <T>
 * <AUTHOR> hyw, Date on 2021/5/17.
 */
@Data
public class YunyingResult<T> implements Serializable {
    private static final long serialVersionUID = 605544666575940834L;

    /**
     * 通用返回码
     */
    private int code;

    /**
     * 成功标识
     */
    private boolean success;

    /**
     * 通用返回信息
     */
    private String msg;

    /**
     * 通用返回数据
     */
    private T obj;

    public YunyingResult() {
        this.code = ResultEnum.SUCCESS.getIndex();
        this.msg = ResultEnum.SUCCESS.getDesc();
        this.obj = null;
        this.success = true;
    }

    public YunyingResult(int code, String msg) {
        this.code = code;
        this.msg = msg;
        this.obj = null;
        this.success = (this.code == ResultEnum.SUCCESS.getIndex());
    }

    public YunyingResult(int code, String msg, T obj) {
        this.code = code;
        this.msg = msg;
        this.obj = obj;
        this.success = (this.code == ResultEnum.SUCCESS.getIndex());
    }

    public YunyingResult(ResultEnum resultEnum, T obj) {
        this.code = resultEnum.getIndex();
        this.msg = resultEnum.getDesc();
        this.obj = obj;
        this.success = (this.code == ResultEnum.SUCCESS.getIndex());
    }

    public YunyingResult(ResultEnum resultEnum) {
        this.code = resultEnum.getIndex();
        this.msg = resultEnum.getDesc();
        this.obj = null;
        this.success = (this.code == ResultEnum.SUCCESS.getIndex());
    }

    public YunyingResult(T obj) {
        this.code = ResultEnum.SUCCESS.getIndex();
        this.msg = ResultEnum.SUCCESS.getDesc();
        this.obj = obj;
        this.success = true;
    }

    /**
     * 通用 操作成功
     */
    public static <T> YunyingResult<T> success() {
        return new YunyingResult<>(ResultEnum.SUCCESS.getIndex(), ResultEnum.SUCCESS.getDesc());
    }

    public static <T> YunyingResult<T> success(T obj) {
        return new YunyingResult<>(ResultEnum.SUCCESS.getIndex(), ResultEnum.SUCCESS.getDesc(), obj);
    }

    public static <T> YunyingResult<T> success(T obj, String msg) {
        return new YunyingResult<>(ResultEnum.SUCCESS.getIndex(), msg, obj);
    }


    /**
     * 通用 操作失败
     */
    public static <T> YunyingResult<T> fail() {
        return new YunyingResult<>(ResultEnum.FAIL.getIndex(), ResultEnum.FAIL.getDesc());
    }

    public static <T> YunyingResult<T> fail(Integer code, String msg) {
        return new YunyingResult<>(code, msg);
    }

    public static <T> YunyingResult<T> fail(T obj) {
        return new YunyingResult<>(ResultEnum.FAIL.getIndex(), ResultEnum.FAIL.getDesc(), obj);
    }

    public static <T> YunyingResult<T> fail(String msg) {
        return new YunyingResult<>(ResultEnum.FAIL.getIndex(), msg);
    }

    public static <T> YunyingResult<T> fail(ResultEnum resultEnum) {
        return new YunyingResult<>(resultEnum.getIndex(), resultEnum.getDesc());
    }

    public static <T> YunyingResult<T> fail(CustomException customException) {
        return new YunyingResult<>(customException.getCode(), customException.getMessage());
    }

    public YunyingResult(boolean success, int code, String msg, T obj) {
        this.success = success;
        this.code = code;
        this.msg = msg;
        this.obj = obj;
    }
}
