package com.msun.csm.common.enums;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Getter;

/**
 * 场景枚举
 */
@Getter
public enum AutoTestSceneTypeEnum {

    BASE_DATA(0, "基础数据"),
    BUSINESS(1, "业务流程");

    private final Integer code;
    private final String desc;


    AutoTestSceneTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过code获取描述
     *
     * @param code
     * @return
     */
    public static String getDescByCode(Integer code) {
        if (ObjectUtil.isNotEmpty(code)) {
            for (AutoTestSceneTypeEnum s : values()) {
                if (code.equals(s.code)) {
                    return s.desc;
                }
            }
        }
        return StrUtil.EMPTY;
    }
}
