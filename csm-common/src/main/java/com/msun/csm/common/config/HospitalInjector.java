package com.msun.csm.common.config;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.msun.core.component.implementation.filter.HospitalDomainInjectListener;

/**
 * 医院信息注入
 * <pre>自动刷新注入</pre>
 */
@Component
public class HospitalInjector implements HospitalDomainInjectListener {

    /**
     * 注入样例
     *
     * @return {@link Map<String, String>}
     */
    @Override
    public Map<String, String> inject() {
        Map<String, String> map = new HashMap<>();
        map.put("10001-1234567890", "http://nginx.chis.msunsoft.com");
        return map;
    }
}
