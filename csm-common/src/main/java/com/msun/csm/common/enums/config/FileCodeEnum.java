package com.msun.csm.common.enums.config;

import lombok.Getter;

/**
 * 系统文件枚举
 */
@Getter
public enum FileCodeEnum {

    KETTLE_TOOL("kettleTool", "老换新数据工作-数据导入-kettle工具"),
    GUIDE_DATA_SCRIPT("guideDataScript", "老换新数据工作-数据导入-导数据脚本"),
    BROWSER_SET_DESCRIPTION("browserSetDescription", "浏览器设置说明");


    /**
     * 文件编码
     */
    private final String fileCode;

    /**
     * 文件描述
     */
    private final String codeDesc;

    FileCodeEnum(String fileCode, String codeDesc) {
        this.fileCode = fileCode;
        this.codeDesc = codeDesc;
    }

}
