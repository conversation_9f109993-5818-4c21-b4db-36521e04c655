package com.msun.csm.common.enums;

/**
 * @description:
 * @fileName:
 * @author:<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @updateBy:
 * @Date:Created in 10:39 2024/5/7
 * @remark:
 */
public enum ExcelExportEnum {
    FOLLOW_ILMITATE_COUNT("统计流程模拟数据-导出结果", "医院名称,上线状态,就诊数量,门诊发药数量,门诊退费数量,住院开药数量,住院退费数量,办理出院数量,LIS报告数量,PACS报告数量");

    private String fileName;

    private String title;

    ExcelExportEnum(String fileName, String title) {
        this.fileName = fileName;
        this.title = title;
    }

    public String getFileName() {
        return fileName;
    }

    public String getTitle() {
        return title;
    }

}
