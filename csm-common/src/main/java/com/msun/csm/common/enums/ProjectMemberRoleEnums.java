package com.msun.csm.common.enums;

import lombok.Getter;

/**
 * @ClassName: BusinessType
 * @Description: 业务操作类型
 */
@Getter
public enum ProjectMemberRoleEnums {
    /**
     * 项目成员
     */
    MEMBER(1, "member"),

    /**
     * 项目经理
     */
    LEADER(2, "leader"),

    /**
     * 业务服务经理
     */
    BACK_LEADER(3, "back-leader"),

    /**
     * 业务服务工程师
     */
    BACK_MEMBER(4, "back-member"),

    /**
     * 数据服务经理
     */
    DATA_LEADER(5, "data-leader"),

    /**
     * 数据服务工程师
     */
    DATA_MEMBER(6, "data-member"),

    /**
     * 接口服务经理
     */
    INTERFACE_LEADER(7, "interface-leader"),

    /**
     * 接口服务工程师
     */
    INTERFACE_MEMBER(8, "interface-member");

    private String desc;
    private int code;


    ProjectMemberRoleEnums(int code, String desc) {
        this.desc = desc;
        this.code = code;
    }

    public static ProjectMemberRoleEnums getEnumDes(int code) {
        for (ProjectMemberRoleEnums s : values()) {    //values()方法返回enum实例的数组
            if (code == s.getCode()) {
                return s;
            }
        }
        return null;
    }
}
