package com.msun.csm.common.model.convert;

import java.util.List;

import org.mapstruct.InheritConfiguration;
import org.mapstruct.InheritInverseConfiguration;

/**
 * Created with IntelliJ IDEA
 *
 * @param <Po>
 * @param <Vo>
 * @Author: duxu
 * @Date: 2024/04/17/17:33
 */
public interface Vo2PoBaseConvert<Vo, Po> {
    @InheritConfiguration
    Po vo2Po(Vo vo);

    @InheritConfiguration
    List<Po> vo2Po(List<Vo> sourceList);

    @InheritInverseConfiguration
    Vo po2Vo(Po po);

    @InheritInverseConfiguration
    List<Vo> po2Vo(List<Po> poList);
}
