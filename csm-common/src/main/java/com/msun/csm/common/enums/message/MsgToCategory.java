package com.msun.csm.common.enums.message;

/**
 * 消息发送对象分类
 * -1.系统指定人员；1.到个人；2.到角色；3.到部门；4.到项目经理
 */
public enum MsgToCategory {

    SYS(-1, "系统指定人员"),
    SINGLE_PERSON(1, "到个人"),
    TO_ROLE(2, "到角色"),
    TO_DEPT(3, "到部门"),
    TO_PRO_MGR(4, "到项目经理");

    private int code;

    private String desc;

    MsgToCategory(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
