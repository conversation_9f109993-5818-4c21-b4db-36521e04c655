package com.msun.csm.common.exception.handler;

import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.util.validation.ValidationException;

import java.util.stream.Collectors;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import com.msun.csm.common.enums.ResultEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.util.ErrUtils;
import com.taobao.api.ApiException;

/**
 * <AUTHOR> Jie
 * @Date 2018-11-23
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @Value("${project.dingTalk_token}")
    private String accessToken;

    @Value("${spring.profiles.active}")
    private String currentEnv;

    /**
     * 处理所有不可知的异常
     */
    @ExceptionHandler(Exception.class)
    public Result handleException(Exception e) {
        log.error("统一异常捕捉到未知错误：{},statckInfo={}", ErrUtils.toSimpleString(e), e);
        return Result.fail(ResultEnum.FAIL);
    }


    /**
     * 定义要捕获的异常 可以多个 @ExceptionHandler({})
     * 捕获 CustomException
     *
     * @param customException exception
     * @return 响应结果
     */
    @ExceptionHandler(CustomException.class)
    public Result customExceptionHandler(final CustomException customException) {
        log.error("统一异常捕捉到业务异常：{}，statckInfo={}", customException.toString(), customException);
        return Result.fail(customException);
    }

    /**
     * 处理参数校验异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(value = {BindException.class, ValidationException.class, MethodArgumentNotValidException.class})
    public Result handleValidatedException(Exception e) {
        String message = "";
        if (e instanceof MethodArgumentNotValidException) {
            // BeanValidation exception
            MethodArgumentNotValidException ex = (MethodArgumentNotValidException) e;
            message = ex.getBindingResult().getAllErrors().stream()
                    .map(ObjectError::getDefaultMessage)
                    .collect(Collectors.joining("; "));
        } else if (e instanceof ConstraintViolationException) {
            // BeanValidation GET simple param
            ConstraintViolationException ex = (ConstraintViolationException) e;
            message = ex.getConstraintViolations().stream()
                    .map(ConstraintViolation::getMessage)
                    .collect(Collectors.joining("; "));
        } else if (e instanceof BindException) {
            // BeanValidation GET object param
            BindException ex = (BindException) e;
            message = ex.getAllErrors().stream()
                    .map(ObjectError::getDefaultMessage)
                    .collect(Collectors.joining("; "));
        }
        return Result.fail(ResultEnum.PARAM_INVALID.getIndex(), message);
    }

    private void sendDingTalkMessage(Exception param) {
        try {
            //sign字段和timestamp字段必须拼接到请求URL上，否则会出现 310000 的错误信息
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/robot/send?");
            OapiRobotSendRequest req = new OapiRobotSendRequest();
            /**
             * 发送文本消息
             */
            //定义文本内容
            OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
            StringBuilder sb = new StringBuilder();
            sb.append(param).append("\n");
            StackTraceElement[] stackTraceElements = param.getStackTrace();
            for (StackTraceElement element : stackTraceElements) {
                if (element.getClassName().contains("com.msun")) {
                    sb.append("\n").append(element).append("\n");
                }
            }
            text.setContent("监控报警:" + sb);
            //设置消息类型
            req.setMsgtype("text");
            req.setText(text);
            OapiRobotSendResponse rsp = client.execute(req, accessToken);
            log.info(rsp.getBody());
        } catch (ApiException e) {
            e.printStackTrace();
        }
    }
}
