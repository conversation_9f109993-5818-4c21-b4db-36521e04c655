package com.msun.csm.common.enums.projsettlement;

import lombok.Getter;

/**
 * 是否是众阳云
 */
@Getter
public enum CloudServiceTypeEnum {

    MSUN_CLOUDE(1, "众阳云"),
    SHARE_CLOUDE(2, "共享云"),
    PRIVATE_CLOUDE(3, "私有云（专有云）");

    private final int code;

    private final String desc;

    CloudServiceTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据节点code获取节点枚举
     *
     * @param code 节点枚举code
     * @return CheckNodeEnum
     */
    public static CloudServiceTypeEnum getCheckResultEnumByCode(int code) {
        for (CloudServiceTypeEnum value : CloudServiceTypeEnum.values()) {
            if (code == value.getCode()) {
                return value;
            }
        }
        return null;
    }
}
