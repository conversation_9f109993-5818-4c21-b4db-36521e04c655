package com.msun.csm.common.enums.projectsplitprocess;

import lombok.Getter;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2023-10-26
 */
@Getter
public enum SplitProcessStatusEnum {
    //1、提交申请
    APPLY(1, "提交申请", "提交了申请", "PMO未审核"),
    //2、驳回后重新提交申请
    RE_APPLY(2, "重新提交申请", "重新提交了申请", "PMO未审核"),
    //3、PMO审核通过
    PMO_PASS(3, "PMO审核通过", "通过了申请", "PMO审核通过，质管未审核"),
    //4、PMO审核驳回
    PMO_REJECT(4, "PMO审核驳回", "驳回了申请", "PMO驳回申请，待项目经理重新提交"),
    //5、质管审核通过
    QA_PASS(5, "质管审核通过", "通过了申请", "质管审核通过"),
    //6、质管审核驳回
    QA_REJECT(6, "质管审核驳回", "驳回了申请", "质管驳回申请，待项目经理重新提交"),
    //7、撤回申请
    CANCEL_APPLY(7, "撤回申请", "撤销了申请", "流程结束");


    private Integer code;
    private String name;
    private String logDesc;
    private String progressDesc;

    SplitProcessStatusEnum(int code, String name, String logDesc, String progressDesc) {
        this.code = code;
        this.name = name;
        this.logDesc = logDesc;
        this.progressDesc = progressDesc;
    }

    public static SplitProcessStatusEnum getEnum(int code) {
        for (SplitProcessStatusEnum s : values()) {    //values()方法返回enum实例的数组
            if (code == s.getCode()) {
                return s;
            }
        }
        return null;
    }
}
