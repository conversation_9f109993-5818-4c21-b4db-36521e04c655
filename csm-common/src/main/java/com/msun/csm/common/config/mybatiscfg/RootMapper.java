package com.msun.csm.common.config.mybatiscfg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

public interface RootMapper<T> extends BaseMapper<T> {

    /**
     * 自定义批量插入
     * 如果要自动填充，@Param(xx) xx参数名必须是 list/collection/array 3个的其中之一
     *
     * @param list
     * @return
     */
    int insertBatch(@Param("list") Iterable<T> list);

    /**
     * 自定义批量更新，条件为主键
     * 如果要自动填充，@Param(xx) xx参数名必须是 list/collection/array 3个的其中之一
     *
     * @param list
     * @return
     */
    int updateBatch(@Param("list") Iterable<T> list);

    /**
     * 新增更新
     *
     * @param list
     * @return
     */
    void insertUpsert(@Param("list") Iterable<T> list);

}