package com.msun.csm.common.enums;

import lombok.Getter;

/**
 * @Description: 云容灾申请节点状态
 * <p>
 * 状态. 操作类型（0. 派工、2. 自动续期、1. 已申请、11. 已驳回、12. 已审核、13. 部署中、14. 部署完成、15. 已交付，16. 已开通、17. 已续期、3. 已申请验收、31. 已验收、32.验收已驳回。）
 * </p>
 */
@Getter
public enum DisasterRecoveryApplyStatusEnum {

    DISPATCH(0, "派工", StatusEnum.SUCCESS),
    SYS_AUTO_RENEWAL(2, "自动续期", StatusEnum.SUCCESS),
    APPLYED(1, "已申请", StatusEnum.SUCCESS),
    REJECTED(11, "已驳回", StatusEnum.FAIL),
    AUDITED(12, "已审核", StatusEnum.SUCCESS),
    DEPLOYING(13, "部署中", StatusEnum.SUCCESS),
    DEPLOYED(14, "部署完成", StatusEnum.SUCCESS),
    DELIVERED(15, "已交付", StatusEnum.SUCCESS),
    OPENED(16, "已开通", StatusEnum.SUCCESS),
    RENEWED(17, "已续期", StatusEnum.SUCCESS),
    CHECK_APPLYED(3, "已申请验收", StatusEnum.SUCCESS),
    CHECKED(31, "已验收", StatusEnum.SUCCESS),
    CHECK_REJECTED(32, "验收已驳回", StatusEnum.FAIL);


    @Getter
    public enum StatusEnum {
        FAIL(0, "【失败】"),
        SUCCESS(1, "【成功】");

        /**
         * 编码
         */
        private final int code;
        /**
         * 描述
         */
        private final String desc;

        StatusEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    /**
     * 编码
     */
    private final int code;
    /**
     * 描述
     */
    private final String desc;
    /**
     * 描述
     */
    private final StatusEnum statusEnum;

    DisasterRecoveryApplyStatusEnum(int code, String desc, StatusEnum statusEnum) {
        this.code = code;
        this.desc = desc;
        this.statusEnum = statusEnum;
    }

    /**
     * 获取对应状态的枚举
     *
     * @param code 编码
     * @return DisasterRecoveryApplyStatusEnum
     */
    public static DisasterRecoveryApplyStatusEnum getEnumByCode(int code) {
        for (DisasterRecoveryApplyStatusEnum value : DisasterRecoveryApplyStatusEnum.values()) {
            if (code == value.getCode()) {
                return value;
            }
        }
        return null;
    }
}
