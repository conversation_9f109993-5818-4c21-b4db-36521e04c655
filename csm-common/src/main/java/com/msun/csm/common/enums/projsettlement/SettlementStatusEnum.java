package com.msun.csm.common.enums.projsettlement;

import lombok.Getter;

/**
 * 入驻状态
 * 入驻状态：
 * 0.提交入驻条件；1.申请入驻；11.方案分公司经理复核通过；12.方案分公司经理驳回；
 * 21.风控审核通过；22.风控驳回；31.PMO审核通过；32.PMO驳回；41.确认入驻
 */
public enum SettlementStatusEnum {

    INIT(-1, "初始化状态", CheckResultEnum.AUDIT_PASS),
    COMMIT_SETTLEMENT(0, "项目经理提交入驻条件", CheckResultEnum.AUDIT_PASS),
    APPLY_SETTLEMENT(1, "销售申请入驻", CheckResultEnum.AUDIT_PASS),
    BRANCH_MANAGER_AUDIT(11, "方案分公司经理复核通过", CheckResultEnum.AUDIT_PASS),
    BRANCH_MANAGER_REJECT(12, "方案分公司经理驳回", CheckResultEnum.AUDIT_FAIL),
    RISK_AUDIT(21, "运营部审核通过", CheckResultEnum.AUDIT_PASS),
    RISK_REJECT(22, "运营部驳回", CheckResultEnum.AUDIT_FAIL),
    PMO_AUDIT(31, "PMO审核通过", CheckResultEnum.AUDIT_PASS),
    PMO_REJECT(32, "PMO驳回", CheckResultEnum.AUDIT_FAIL),
    CONFIRM_ENTRY(41, "项目经理确认入驻", CheckResultEnum.AUDIT_PASS),
    CONFIRM_ENTRY_REJECT(42, "项目经理确认入驻驳回", CheckResultEnum.AUDIT_FAIL);

    private int code;

    private String desc;

    @Getter
    private CheckResultEnum checkResultEnum;

    SettlementStatusEnum(int code, String desc, CheckResultEnum checkResultEnum) {
        this.code = code;
        this.desc = desc;
        this.checkResultEnum = checkResultEnum;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据节点code获取节点枚举
     *
     * @param settlementStatusCode 节点枚举code
     * @return SettlementStatusEnum
     */
    public static SettlementStatusEnum getSettlementStatusByCode(int settlementStatusCode) {
        for (SettlementStatusEnum value : SettlementStatusEnum.values()) {
            if (settlementStatusCode == value.getCode()) {
                return value;
            }
        }
        return null;
    }
}
