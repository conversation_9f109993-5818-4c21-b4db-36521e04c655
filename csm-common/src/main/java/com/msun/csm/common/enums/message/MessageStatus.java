package com.msun.csm.common.enums.message;

/**
 * 消息状态：0.未读；1.已读未处理；2.已读已处理
 */
public enum MessageStatus {
    UNREAD(1, "未读"),
    ONLY_READ(2, "已读未处理"),
    READ_AND_PROCESSED(3, "已读已处理");

    private int code;

    private String desc;

    MessageStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }

}
