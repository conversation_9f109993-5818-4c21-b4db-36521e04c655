package com.msun.csm.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @description 加上该注解的类http请求时需要进行sign鉴权
 * 三方系统调用的接口必须添加
 * @date 2024-11-26 11:22
 */

@Target ({ElementType.METHOD, ElementType.TYPE})
@Retention (RetentionPolicy.RUNTIME)
public @interface CsmSign {

}
