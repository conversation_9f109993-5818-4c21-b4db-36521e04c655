package com.msun.csm.common.enums;

import lombok.Getter;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2023-10-26
 */
@Getter
public enum AcceptanceTypeEnum {

    //申请验收
    APPLY_CHECK("1", "申请验收"),
    //验收结束
    CHECK_OVER("2", "完成了验收审核"),
    //验收结束
    REJECT_CHECK("3", "驳回了验收申请"),
    //接收申请
    APPLY_RECEIVE("4", "接收了验收申请");


    private String code;
    private String desc;

    AcceptanceTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
