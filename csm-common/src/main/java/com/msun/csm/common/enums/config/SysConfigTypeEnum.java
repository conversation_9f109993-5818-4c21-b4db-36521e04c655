package com.msun.csm.common.enums.config;


/**
 * 妇幼系统配置枚举类 - 规范系统配置工具类
 *
 * <AUTHOR>
 * @date 2021/12/28 16:35
 */
public enum SysConfigTypeEnum {

    TYPE_TEXT("TYPE_TEXT", "文本类型"),
    TYPE_SELECT("TYPE_SELECT", "下拉类型"),
    TYPE_DATE("TYPE_DATE", "日期类型"),
    TYPE_HOUR("TYPE_HOUR", "小时类型"),
    TYPE_BOOL("TYPE_BOOL", "开关类型"),
    TYPE_JSON("TYPE_JSON", "json类型"),
    TYPE_NUMBER("TYPE_NUMBER", "数字类型");

    /**
     * 配置Code
     */
    private String configCode;
    /**
     * 配置默认值
     */
    private String configValue;


    SysConfigTypeEnum(String configCode, String configValue) {
        this.configCode = configCode;
        this.configValue = configValue;
    }

    public String getConfigCode() {
        return configCode;
    }

    public String getConfigValue() {
        return configValue;
    }

}
