package com.msun.csm.common.enums.api.yunwei;

import lombok.Getter;

/**
 * 调运营平台接口参数枚举
 */
@Getter
public enum CloudFeedbackEnum {
    COMMUNICATE_AGREE(1, "已与客户沟通且达成一致，承诺按时签回"),
    COMMUNICATE_DISAGREE(2, "已与客户沟通且未达成一致"),
    NOT_COMMUNICATE(3, "未和医院沟通"),
    OTHER(4, "其他");

    private final Integer code;
    private final String desc;

    CloudFeedbackEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
