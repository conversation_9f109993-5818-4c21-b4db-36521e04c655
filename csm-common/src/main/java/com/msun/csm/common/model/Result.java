package com.msun.csm.common.model;

import lombok.Data;

import java.io.Serializable;

import com.msun.csm.common.enums.ResultEnum;
import com.msun.csm.common.exception.CustomException;
import lombok.extern.slf4j.Slf4j;


/**
 * @param <T>
 * <AUTHOR> hyw, Date on 2021/5/17.
 */
@Data
@Slf4j
public class Result<T> implements Serializable {
    private static final long serialVersionUID = 605544666575940834L;

    /**
     * 通用返回码
     */
    private int code;

    /**
     * 成功标识
     */
    private boolean success;

    /**
     * 通用返回信息
     */
    private String msg;

    /**
     * 通用返回数据
     */
    private T data;

    public Result() {
        this.code = ResultEnum.SUCCESS.getIndex();
        this.msg = ResultEnum.SUCCESS.getDesc();
        this.data = null;
        this.success = true;
    }

    public Result(int code, String msg) {
        this.code = code;
        this.msg = msg;
        this.data = null;
        this.success = (this.code == ResultEnum.SUCCESS.getIndex());
    }

    public Result(boolean success, int code, String msg) {
        this.success = success;
        this.code = code;
        this.msg = msg;
    }

    public Result(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
        this.success = (this.code == ResultEnum.SUCCESS.getIndex());
    }

    public Result(ResultEnum resultEnum, T data) {
        this.code = resultEnum.getIndex();
        this.msg = resultEnum.getDesc();
        this.data = data;
        this.success = (this.code == ResultEnum.SUCCESS.getIndex());
    }

    public Result(ResultEnum resultEnum) {
        this.code = resultEnum.getIndex();
        this.msg = resultEnum.getDesc();
        this.data = null;
        this.success = (this.code == ResultEnum.SUCCESS.getIndex());
    }

    public Result(T data) {
        this.code = ResultEnum.SUCCESS.getIndex();
        this.msg = ResultEnum.SUCCESS.getDesc();
        this.data = data;
        this.success = true;
    }

    /**
     * 通用 操作成功
     */
    public static <T> Result<T> success() {
        return new Result<>(ResultEnum.SUCCESS.getIndex(), ResultEnum.SUCCESS.getDesc());
    }

    public static <T> Result<T> success(T data) {
        return new Result<>(ResultEnum.SUCCESS.getIndex(), ResultEnum.SUCCESS.getDesc(), data);
    }

    public static <T> Result<T> success(T data, String msg) {
        return new Result<>(ResultEnum.SUCCESS.getIndex(), msg, data);
    }


    /**
     * 通用 操作失败
     */
    public static <T> Result<T> fail() {
        return new Result<>(ResultEnum.FAIL.getIndex(), ResultEnum.FAIL.getDesc());
    }

    public static <T> Result<T> fail(Integer code, String msg) {
        log.error("Result fail, code={}, msg={}", code, msg);
        return new Result<>(code, msg);
    }

    public static <T> Result<T> fail(T data) {
        return new Result<>(ResultEnum.FAIL.getIndex(), ResultEnum.FAIL.getDesc(), data);
    }

    public static <T> Result<T> fail(String msg) {
        log.error("Result fail, msg={}", msg);
        return new Result<>(ResultEnum.FAIL.getIndex(), msg);
    }

    public static <T> Result<T> fail(ResultEnum resultEnum) {
        return new Result<>(resultEnum.getIndex(), resultEnum.getDesc());
    }

    public static <T> Result<T> fail(CustomException customException) {
        return new Result<>(customException.getCode(), customException.getMessage());
    }

    public Result(boolean success, int code, String msg, T data) {
        this.success = success;
        this.code = code;
        this.msg = msg;
        this.data = data;
    }
}
