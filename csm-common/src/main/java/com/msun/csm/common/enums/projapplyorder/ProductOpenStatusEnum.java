package com.msun.csm.common.enums.projapplyorder;

/**
 * 产品开通状态
 */
public enum ProductOpenStatusEnum {

    NOT_OPEN(0, 0, "未开通"),
    OPENING(11, 2, "处理中"),
    OPENED(21, 1, "已开通");


    private Integer code;
    /**
     * 老项目产品状态编码
     */
    private Integer oldCode;

    private String desc;




    ProductOpenStatusEnum(Integer code, Integer oldCode, String desc) {
        this.code = code;
        this.desc = desc;
        this.oldCode = oldCode;
    }

    /**
     * 通过code获取描述
     *
     * @param code
     * @return
     */
    public static String getDesc(Integer code) {
        for (ProductOpenStatusEnum s : values()) {
            if (code.equals(s.code)) {
                return s.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getOldCode() {
        return oldCode;
    }
}
