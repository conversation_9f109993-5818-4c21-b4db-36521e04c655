package com.msun.csm.common.enums.projapplyorder;

import com.msun.csm.common.enums.DisasterRecoveryApplyStatusEnum;

import lombok.Getter;

/**
 * 操作类型（0. 派工、2. 自动续期、1. 已申请、11. 已驳回、12. 已审核、13. 部署中、14. 部署完成、15. 已交付，16. 已开通、17. 已续期、3. 已申请验收、31. 已验收、32.
 */
@Getter
public enum YunweiDisasterCloudResultTypeDictEnum {
    //0:已申请、1:已审核、2:已驳回、3:环境部署中、4:环境部署完成、5:已交付、6:已撤销，7:已验收、8:已拒收。
    AUDITED(ProjApplyOrderResultTypeEnum.AUDITED, DisasterRecoveryApplyStatusEnum.AUDITED, "已审核"),
    REJECTED(ProjApplyOrderResultTypeEnum.REJECTED, DisasterRecoveryApplyStatusEnum.REJECTED, "已驳回"),
    ENV_DEPLOYING(ProjApplyOrderResultTypeEnum.ENV_DEPLOYING, DisasterRecoveryApplyStatusEnum.DEPLOYING, "环境部署中"),
    ENV_DEPLOYED(ProjApplyOrderResultTypeEnum.ENV_DEPLOYED, DisasterRecoveryApplyStatusEnum.DEPLOYED, "环境部署完成"),
    DELIVERED(ProjApplyOrderResultTypeEnum.DELIVERED, DisasterRecoveryApplyStatusEnum.DELIVERED, "已交付");

    private final ProjApplyOrderResultTypeEnum applyOrderResultTypeEnum;
    private final DisasterRecoveryApplyStatusEnum disasterRecoveryApplyStatusEnum;
    private final String desc;

    YunweiDisasterCloudResultTypeDictEnum(ProjApplyOrderResultTypeEnum applyOrderResultTypeEnum,
                                          DisasterRecoveryApplyStatusEnum disasterRecoveryApplyStatusEnum,
                                          String desc) {
        this.applyOrderResultTypeEnum = applyOrderResultTypeEnum;
        this.disasterRecoveryApplyStatusEnum = disasterRecoveryApplyStatusEnum;
        this.desc = desc;
    }

    /**
     * 获取转换后的状态
     *
     * @param applyEnum 申请参数枚举状态
     * @return DisasterRecoveryApplyStatusEnum
     */
    public static DisasterRecoveryApplyStatusEnum getEnum(ProjApplyOrderResultTypeEnum applyEnum) {
        for (YunweiDisasterCloudResultTypeDictEnum s : values()) {    //values()方法返回enum实例的数组
            if (applyEnum == s.getApplyOrderResultTypeEnum()) {
                return s.getDisasterRecoveryApplyStatusEnum();
            }
        }
        return null;
    }
}
