package com.msun.csm.common.enums.projapplyorder;

import lombok.Getter;

/**
 * 交付工单节点状态枚举
 */
@Getter
public enum ProjApplyOrderResultTypeEnum {
    //0:已申请、1:已审核、2:已驳回、3:环境部署中、4:环境部署完成、5:已交付、6:已撤销，7:已验收、8:已拒收。
    APPLYED(0, "已申请"),
    AUDITED(1, "已审核"),
    REJECTED(2, "已驳回"),
    ENV_DEPLOYING(3, "环境部署中"),
    ENV_DEPLOYED(4, "环境部署完成"),
    DELIVERED(5, "已交付"),
    REVOKED(6, "已撤销"),
    ACCEPTED(7, "已验收"),
    REFUSED(8, "已拒收");

    private int code;
    private String desc;

    ProjApplyOrderResultTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ProjApplyOrderResultTypeEnum getEnumDes(int code) {
        for (ProjApplyOrderResultTypeEnum s : values()) {    //values()方法返回enum实例的数组
            if (code == s.getCode()) {
                return s;
            }
        }
        return null;
    }

    public String getCodeStr() {
        return String.valueOf(this.code);
    }
}
