package com.msun.csm.common.enums.projsettlement;

/**
 * 是否需要运营部审核
 */
public enum CheckPrepayFlagEnum {

    NEED_CHECK_PREPAY(1, "需要运营部审核"),
    NEED_NO_CHECK_PREPAY(0, "不需要运营部审核");

    private int code;

    private String desc;

    CheckPrepayFlagEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
