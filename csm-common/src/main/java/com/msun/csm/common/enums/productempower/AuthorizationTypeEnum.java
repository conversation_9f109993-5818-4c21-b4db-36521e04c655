package com.msun.csm.common.enums.productempower;

import lombok.Getter;

/**
 * 授权类型枚举
 */
@Getter
public enum AuthorizationTypeEnum {

    ADD_AUTHORIZATION(1, "新增授权"),
    CANCAL_AUTHORIZATION(2, "取消授权");

    private final int code;

    private final String desc;

    AuthorizationTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AuthorizationTypeEnum getEnumByCode(int code) {
        for (AuthorizationTypeEnum s : values()) {    //values()方法返回enum实例的数组
            if (code == s.getCode()) {
                return s;
            }
        }
        return null;
    }
}
