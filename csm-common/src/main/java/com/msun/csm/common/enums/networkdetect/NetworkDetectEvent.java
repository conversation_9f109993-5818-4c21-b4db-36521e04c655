package com.msun.csm.common.enums.networkdetect;

/**
 * 网络检测系统分类
 */
public enum NetworkDetectEvent {

    START(0, "开始"),

    END(1, "结束"),

    OTHER(2, "其他");


    private int code;

    private String desc;

    NetworkDetectEvent(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据系统类型编码查询后缀
     *
     * @param code
     * @return
     */
    public static NetworkDetectEvent getEnumByCode(int code) {
        for (NetworkDetectEvent value : NetworkDetectEvent.values()) {
            if (code == value.getCode()) {
                return value;
            }
        }
        return NetworkDetectEvent.OTHER;
    }
}
