package com.msun.csm.common.enums.projprojectinfo;

/**
 * 特殊产品编码
 */
public enum EspecialProductEnums {
    QUALITY(5721L, "病案首页质控"),
    AUTO_CODE(5680L, "智能诊断编码"),
    ICD(5676L, "智能手术编码");

    private Long code;
    private String name;

    EspecialProductEnums(Long code, String name) {
        this.code = code;
        this.name = name;
    }

    public Long getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}