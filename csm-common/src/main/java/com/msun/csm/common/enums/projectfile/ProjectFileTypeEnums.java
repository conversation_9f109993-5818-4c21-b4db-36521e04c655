package com.msun.csm.common.enums.projectfile;

import lombok.Getter;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/5/24
 */
@Getter
public enum ProjectFileTypeEnums {

    //submit_survey_report 字符串 在 tduck中使用
    SUBMIT_SURVEY_REPORT("stage_survey", "submit_survey_report", 1),

    //调研阶段-提交调研报告及入场条件
    SUBMIT_SURVEY_REPORT_ENTRY("stage_survey", "submit_survey_report_entry", 1),

    //调研阶段-网络改造方案
    SUBMIT_SCHEME_NETWORK("stage_survey", "scheme_newwork", 1),

    //调研报表
    SURVEY_REPORT("stage_survey", "survey_report", 1),
    //调研统计报表
    SURVEY_STATISTICS_REPORT("stage_survey", "survey_statistics_report", 1),

    //调研阶段-设备调研
    SURVEY_DEVICE("stage_survey", "survey_device", 1),

    // 表单调研
    SURVEY_FORM("stage_survey", "survey_form", 1),

    // 三方接口调研
    SURVEYTHIRDPART("survey_third_part", "survey_third_part", 1),

    // 三方接口对接
    SCHEDULETHIRDPART("schedule_third_part", "schedule_third_part", 1),

    // 小硬件调研（采购清单）
    SURVEY_HARDWARE("stage_survey", "survey_hardware", 1),

    //入驻阶段-确认达到入驻条件
    PROJECT_ENTRY("stage_entry", "project_entry", 1),

    //准备阶段-云资源划分及部署
    CLOUD_RESOURCE("stage_preparat", "cloud_resource", 1),

    //准备阶段-产品准备
    PRODUCT_PREPARAT("stage_preparat", "preparat_product", 1),

    //准备阶段-产品准备
    REPORT_PREPARAT("stage_preparat", "preparat_report", 1),

    //准备阶段-设备准备
    PREPARAT_DEVICE("stage_preparat", "preparat_device", 1),

    //准备阶段-表单准备
    FORM_PREPARAT("stage_preparat", "preparat_form", 1),

    //准备阶段-表单准备
    PREPARAT_REPORT_STATISTICS("stage_preparat", "preparat_report_statistics", 1),

    //验收阶段-项目验收
    PROJECT_ACCEPT("stage_accept", "project_accept", 1),

    //产品准备-制定切换方案
    SCHEME_PROJECT("stage_preparat", "scheme_project", 1),

    //工单产品-特批产品补录
    product_supplement("product_supplement", "product_supplement", 3),

    //项目拆分
    SPLIT_PROJECT("splitProject", "splitProject", 2);


    //里程碑大节点code
    private final String stage;

    //里程碑小节点code
    private final String milestone;

    //类型 1 里程碑阶段中产生   2  项目拆分上传
    private final Integer type;

    ProjectFileTypeEnums(String stage, String milestone, Integer type) {
        this.stage     = stage;
        this.milestone = milestone;
        this.type      = type;
    }

    public static ProjectFileTypeEnums getEnum(String milestone) {
        for (ProjectFileTypeEnums s : values()) {    //values()方法返回enum实例的数组
            if (milestone != null && milestone.equals(s.getMilestone())) {
                return s;
            }
        }
        return null;
    }
}
