package com.msun.csm.common.enums;

import lombok.Getter;

/**
 * 双工标识. 是否双工, 哪种双工
 */
@Getter
public enum DuplexFlagEnum {

    NO_DUPLEX(0, "无需双工"),
    DUPLEX(1, "需双工"),
    DUPLEX_SAMPLE(2, "样本双工"),
    DUPLEX_BAR(3, "条码双工");

    private final Integer code;
    private final String desc;


    DuplexFlagEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过code获取描述
     */
    public static String getDescByCode(Integer code) {
        if (null != code) {
            for (DuplexFlagEnum s : values()) {
                if (code.equals(s.code)) {
                    return s.desc;
                }
            }

        }
        return null;
    }
}
