package com.msun.csm.common.enums.networkdetect;

/**
 * 网络检测工具时间类型参数
 */
public enum NetworkToolParamTimeTypeStatus {

    MINUTE(0, "分钟", 60),

    HOUR(1, "小时", 60 * 60),

    DAY(2, "天", 60 * 60 * 24),
    OTHER(3, "默认", 40);

    private int code;

    private String desc;

    private int preCalcCount;

    NetworkToolParamTimeTypeStatus(int code, String desc, int preCalcCount) {
        this.code = code;
        this.desc = desc;
        this.preCalcCount = preCalcCount;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public int getPreCalcCount() {
        return preCalcCount;
    }

    /**
     * 根据编码获取枚举
     * @param code
     * @return
     */
    public static NetworkToolParamTimeTypeStatus getEnumByCode(int code) {
        for (NetworkToolParamTimeTypeStatus value : NetworkToolParamTimeTypeStatus.values()) {
            if (code == value.getCode()) {
                return value;
            }
        }
        return NetworkToolParamTimeTypeStatus.OTHER;
    }
}
