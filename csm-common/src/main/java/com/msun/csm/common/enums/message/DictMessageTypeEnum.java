package com.msun.csm.common.enums.message;

import lombok.Getter;

/**
 * 消息类型
 */
@Getter
public enum DictMessageTypeEnum {

    ALLOCATING_TASK_MESSAGE(100002L, "待办任务分配提醒", "分配待办任务时的消息提醒"),
    CANCEL_TASK_MESSAGE(100003L, "待办任务取消提醒", "取消待办任务时的消息提醒"),
    ALLOCATING_ISSUE_MESSAGE(100004L, "问题分配提醒", "分配问题时的消息提醒"),
    CANCEL_ISSUE_MESSAGE(100005L, "问题取消提醒", "取消问题时的消息提醒"),
    SURVEY_REJECT_MESSAGE(100006L, "调研结果驳回提醒", "调研结果驳回提醒"),
    SURVEY_ACCEPT_MESSAGE(100007L, "调研结果审核通过提醒", "调研结果审核通过提醒"),
    ALLOCATING_SURVEY_MESSAGE(100015L, "调研结果审核分配提醒", "调研结果审核分配提醒"),
    CANCEL_SURVEY_MESSAGE(100016L, "调研结果审核取消提醒", "调研结果审核取消提醒"),
    SETTLEMENT_ENSURE(6001L, "入驻确认单", "调研阶段PMO审核通过后，给销售或生态经理发送通知"),
    SETTLEMENT_RISK_AUDIT(6002L, "入驻审核-风控", "首期项目或后续新合同，标准合同类型，入驻阶段销售提交入驻申请，给运营部发送消息。"),
    SETTLEMENT_PMO_AUDIT(6003L, "入驻审核-PMO", "入驻阶段销售提交入驻申请，给PMO发送消息。"),
    SETTLEMENT_BRANCH_MGR_AUDIT(6004L, "入驻审核-方案分公司", "入驻阶段销售提交入驻申请，给方案分公司经理发送消息。"),
    SETTLEMENT_PROJ_MGR_AUDIT(6005L, "入驻审批消息", "入驻阶段各节点审批通过发送消息给销售和项目经理。"),
    EXCEPTION_SYS_MGR_AUDIT(7008L, "异常消息提醒", "异常消息提醒。"),
    SETTLEMENT_ENTRY_REJECT_PMO_KOWN(7009L, "入驻驳回通知", "入驻驳回通知, 仅发送给pmo。"),
    SETTLEMENT_ENTRY_DETECT_DATA_ERR_KOWN(7010L, "入驻数据检测异常通知", "调研阶段项目经理pmo审核时数据监测, 若有数据异常发送通知, 发送给管理员和pmo。"),
    SETTLEMENT_ENTRY_ERR_NOTIFY(7011L, "入驻阶段异常通知", "入驻阶段销售申请、审核流程异常通知, 发送给管理员、pmo"),
    // 部署节点通知
    DEPLOY_REJECT_KOWN(9001L, "部署节点驳回通知", "部署节点驳回通知, 仅发送给pmo或指定人员。"),
    DEPLOY_DEPLOYED_KOWN(9002L, "部署交付通知", "云资源部署交付通知。"),

    REPORT_COMPLETE_KOWN(100010L, "报表提交审核消息提醒", "报表提交审核消息提醒。"),
    REPORT_EXE_KOWN(100011L, "报表审核结果消息提醒", "报表审核结果消息提醒。"),
    FORM_COMPLETE_KOWN(100012L, "表单提交审核消息提醒", "表单提交审核消息提醒。"),
    FORM_EXE_KOWN(100013L, "表单审核结果消息提醒", "表单审核结果消息提醒。"),

    // 部署审核预警提醒
    DEPLOYMENT_AUDIT_REMINDER(100023L, "云资源审核提醒", "云资源审核提醒。"),
    DEPLOYMENT_EARLY_WARNING_REMINDER(100024L, "云资源部署预警提醒", "云资源部署预警提醒。"),
    DEPLOYMENT_TIMEOUT_REMINDER(100025L, "云资源部署超期提醒", "云资源部署超期提醒。"),
    DEPLOYMENT_AUDIT_FINISH_REMINDER(100026L, "云资源部署审核完成消息提醒", "云资源部署审核完成消息提醒。"),
    SPECIAL_PRODUCT_MESSAGING(100027L, "特殊产品添加后发送消息给项目经理", "特殊产品添加后发送消息给项目经理。"),

    DISASTER_RECOVERY_APPLY(10001L, "云容灾部署申请", "首期项目或后续新合同，标准合同类型，云容灾部署申请，给运营部发送消息。");

    /**
     * 消息类别id
     */
    private final Long id;
    /**
     * 备注
     */
    private final String memo;
    /**
     * 消息类别名称
     */
    private final String messageTypeName;

    DictMessageTypeEnum(Long id, String messageTypeName, String memo) {
        this.id = id;
        this.memo = memo;
        this.messageTypeName = messageTypeName;
    }
}
