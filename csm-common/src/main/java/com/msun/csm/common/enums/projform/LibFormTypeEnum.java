package com.msun.csm.common.enums.projform;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @description: 护士站表单类型
 * @fileName: NursFormTypeEnum.java
 * @author: renqichao
 * @createAt: 2023/9/19 15:40
 * @updateBy: renqichao
 * @remark: 众阳健康
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum LibFormTypeEnum {

    EVALUATION_SHEET("1", "评估单"),
    RECORD_SHEET("2", "记录单"),
    ADULTS_TEMPERATURE_SHEET("30001", "成人体温单"),
    CHILDREN_TEMPERATURE_SHEET("30002", "儿童体温单"),
    AGREEMENT_SHEET("4", "知情文件");

    String code;
    String message;

    public static String getMessageValueOf(String status) {
        if (status == null) {
            return "";
        } else {
            for (LibFormTypeEnum s : LibFormTypeEnum.values()) {
                if (s.getCode().equals(status)) {
                    return s.getMessage();
                }
            }
            return "";
        }
    }
}
