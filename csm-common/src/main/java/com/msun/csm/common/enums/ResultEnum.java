package com.msun.csm.common.enums;

/**
 * @DESCRIPTION:
 * @AUTHOR: mengchuan
 * @DATE: 2022/12/13
 */
public enum ResultEnum {
    SUCCESS(0, "操作成功！"),
    FAIL(1, "操作失败!"),
    VALIDATE_NULL_OR_EMPTY(400, "请求参数不能为空!"),
    PARAM_INVALID(401, "请求参数错误!"),
    NO_DATA(404, "没有此记录!"),
    DATA_EXISTS(406, "数据已存在"),
    SYSTEM_ERROR(500, "系统异常,请稍后再试!"),
    AUTH_FAIL(900, "认证已失效，请重新登录！"),
    USER_NOT_EXIST(901, "用户不存在"),
    NO_AUTH(902, "没有该权限！"),
    NO_REQUEST(903, "您长时间未操作，请重新登录！"),
    RE_QUEST_COUNT(904, "您今天访问的页面已超出限制，请明天再访问！"),
    NO_LOGIN(905, "您暂未登陆，请先登录！"),
    ILLEGAL_REQUEST(906, "非法请求!"),
    OVER_TIME(907, "请求超时限"),
    ACCOUNT_PWD_ERR(908, "用户名或密码错误!"),
    ACCOUNT_EXIST(909, "数据库已存在同名账号，请修改账号!"),
    FILE_NAME_MISMATCH(1114, "文件名称不符,请修改后再上传"),
    FILE_AUTO_DEAL_ERR(1115, "图像文件自动处理失败"),
    BUILD_STARTED(1116, "任务已经开始，不能删除！");


    private String desc;
    private int index;


    ResultEnum(int index, String desc) {
        this.desc = desc;
        this.index = index;
    }

    public static ResultEnum getEnumDes(int index) {
        for (ResultEnum s : values()) {    //values()方法返回enum实例的数组
            if (index == s.getIndex()) {
                return s;
            }
        }
        return null;
    }

    public String getDesc() {
        return desc;
    }

    public int getIndex() {
        return index;
    }

}
