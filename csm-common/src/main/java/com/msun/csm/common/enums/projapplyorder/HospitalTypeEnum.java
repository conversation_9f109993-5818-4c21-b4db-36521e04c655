package com.msun.csm.common.enums.projapplyorder;

import lombok.Getter;

/**
 * 医院类型
 */
@Getter
public enum HospitalTypeEnum {

    INDIVIDUAL(1, "hospital", "单体"),
    HOSPITAL(2, "clinic", "卫生院"),
    ELECTRIC_SALES(3, "electricSales", "电销");

    @Getter
    public enum HospitalTypeBranchEnum {

        SINGLE_BRANCH(1, "branch");

        /**
         * 项目编码
         */
        private final int code;
        private final String devCode;

        HospitalTypeBranchEnum(int code, String devCode) {
            this.code = code;
            this.devCode = devCode;
        }

        public static HospitalTypeBranchEnum getEnumByCode(int code) {
            for (HospitalTypeBranchEnum s : values()) {    //values()方法返回enum实例的数组
                if (code == s.getCode()) {
                    return s;
                }
            }
            return null;
        }
    }

    /**
     * 项目类型编码
     */
    private final int code;
    /**
     * 运维平台标识
     */
    private final String devCode;
    private final String desc;

    /**
     *
     */
//    private final String desc;
    HospitalTypeEnum(int code, String devCode, String desc) {
        this.code = code;
        this.devCode = devCode;
        this.desc = desc;
    }

    public static HospitalTypeEnum getEnumByCode(int code) {
        for (HospitalTypeEnum s : values()) {    //values()方法返回enum实例的数组
            if (code == s.getCode()) {
                return s;
            }
        }
        return null;
    }

}
