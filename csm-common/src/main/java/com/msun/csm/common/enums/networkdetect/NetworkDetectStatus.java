package com.msun.csm.common.enums.networkdetect;

/**
 * 网络状态
 */
public enum NetworkDetectStatus {

    SUCCESS(1, "正常"),

    FAILED(0, "异常"),

    NOT_DETECT(2, "未检测");

    public enum DomainDetectStatus {
        SUCCESS(1, "测试成功"),
        FAILED(0, "失败");

        private int code;

        private String desc;

        DomainDetectStatus(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum DnsDetectStatus {
        STABLE(1, "正常"),
        FAILED(0, "异常"),
        UN_STABLE(3, "波动");

        private int code;

        private String desc;

        DnsDetectStatus(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    private int code;

    private String desc;

    NetworkDetectStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
