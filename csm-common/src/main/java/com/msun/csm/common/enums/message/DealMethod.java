package com.msun.csm.common.enums.message;

import lombok.Getter;

/**
 * 消息类型表中。消息处理方式
 */
public enum DealMethod {

    READ(Short.parseShort("1"), "读取"),
    BACK_HANDLE(Short.parseShort("2"), "后续处理");

    /**
     * 编码
     */
    @Getter
    private final Short code;

    /**
     * 描述
     */
    private final String desc;

    DealMethod(Short code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
