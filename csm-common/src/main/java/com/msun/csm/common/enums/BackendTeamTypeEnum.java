package com.msun.csm.common.enums;

import lombok.Getter;

@Getter
public enum BackendTeamTypeEnum {

    /**
     * 业务服务团队
     */
    BUSINESS_TEAM("bustype", "业务服务团队"),

    /**
     * 数据服务团队
     */
    DATA_TEAM("datatype", "数据服务团队"),

    /**
     * 接口服务团队
     */
    INTERFACE_TEAM("interfacetype", "接口服务团队");

    BackendTeamTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 服务团队编码，对应knowledge.dict_know_common表的dict_code
     */
    private final String code;

    /**
     * 团队类型描述
     */
    private final String name;

    public static BackendTeamTypeEnum getBackendTeamTypeEnumByCode(String code) {
        for (BackendTeamTypeEnum backendTeamTypeEnum : BackendTeamTypeEnum.values()) {
            if (backendTeamTypeEnum.code.equals(code)) {
                return backendTeamTypeEnum;
            }
        }
        throw new IllegalArgumentException("非法的后端服务团队类型，当前类型编码=" + code);
    }
}
