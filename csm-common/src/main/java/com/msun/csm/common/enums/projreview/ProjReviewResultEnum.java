package com.msun.csm.common.enums.projreview;


import lombok.Getter;

/**
 * 审核状态
 */
@Getter
public enum ProjReviewResultEnum {
    // 0 项目经理已申请, 1 项目经理重新申请, 2 PMO审核驳回, 3 PMO审核通过 4. 通知销售入场申请
    PROJ_MGR_APPLYIED(0, ProjReviewTypeEnum.NO_AUDIT, ProjReviewNodeEnum.PROJ_MANAGER, "项目经理已申请"),
    PROJ_MGR_RE_APPLYIED(1, ProjReviewTypeEnum.NO_AUDIT, ProjReviewNodeEnum.PROJ_MANAGER, "项目经理重新申请"),
    PMO_REJECT(2, ProjReviewTypeEnum.REJECTED, ProjReviewNodeEnum.PMO, "PMO审核驳回"),
    PMO_APPROVED(3, ProjReviewTypeEnum.AUDITED, ProjReviewNodeEnum.PMO, "PMO审核通过"),
    NOTIFY_SALE_SETTLEMENT_APPLY(4, ProjReviewTypeEnum.AUDITED, ProjReviewNodeEnum.PMO, "通知销售入场申请"),
    SETTLEMENT_ENSURE(5, ProjReviewTypeEnum.AUDITED, ProjReviewNodeEnum.PMO, "项目经理确认入驻");

    private final int nodeCode;

    private final ProjReviewNodeEnum projReviewTypeEnum;

    private final String desc;

    private final ProjReviewTypeEnum reviewTypeEnum;

    ProjReviewResultEnum(int nodeCode, ProjReviewTypeEnum reviewTypeEnum, ProjReviewNodeEnum projReviewTypeEnum,
                         String desc) {
        this.nodeCode = nodeCode;
        this.reviewTypeEnum = reviewTypeEnum;
        this.projReviewTypeEnum = projReviewTypeEnum;
        this.desc = desc;
    }

    public static ProjReviewResultEnum getEnumByNodeCode(Integer nodeCode) {
        for (ProjReviewResultEnum value : ProjReviewResultEnum.values()) {
            if (value.getNodeCode() == nodeCode) {
                return value;
            }
        }
        return null;
    }

}
