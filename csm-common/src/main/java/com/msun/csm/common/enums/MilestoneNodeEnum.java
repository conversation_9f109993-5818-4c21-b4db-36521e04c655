package com.msun.csm.common.enums;

import lombok.Getter;

/**
 * @description:
 * @fileName:
 * @author:<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @updateBy:
 * @Date:Created in 17:00 2024/4/28
 * @remark:
 */
@Getter
public enum MilestoneNodeEnum {

    MAINTENANCE_HOSPITAL("MaintenanceHospital", "维护医院信息"),
    CONFIRM_PRODUCT("confirm_product", "确认产品范围"),
    CONFIRM_MEMBER("confirm_member", "确认实施模式与成员"),

    MEDICAL_INSURANCE("medical_insurance", "医保接口开发"),
    SURVEY_PLAN("survey_plan", "调研计划制定及任务分配"),
    SURVEY_PRODUCT("survey_product", "产品业务调研"),
    SURVEY_DEVICE("survey_device", "设备调研"),
    SURVEY_THIRD_PART("survey_third_part", "三方接口调研"),
    SURVEY_REPORT("survey_report", "报表调研"),
    //调研统计报表
    SURVEY_STATISTICS_REPORT("survey_statistics_report", "调研统计报表"),
    PREPARAT_REPORT_STATISTICS("preparat_report_statistics", "准备统计报表"),
    SURVEY_FORM("survey_form", "护理表单调研"),
    SURVEY_HARDWARE("survey_hardware", "小硬件调研"),
    SCHEME_NEWWORK("scheme_newwork", "制定网络改造方案"),
    BASEDATA_COLLECT("basedata_collect", "基础数据收集"),
    SURVEY_SUMMARY("survey_summary", "调研总结与审核"),
    SUBMIT_SURVEY_REPORT("submit_survey_report", "提交调研报告及入场条件"),
    SURVEY_CHECK("survey_check", "项目管理团队核实(调研)"),
    TEST_NET("test_net", "网络测试"),
    PREPARAT_OLDFORNEW("preparat_oldfornew", "老换新准备工作"),
    IMPLEMENT_PLAN("implement_plan", "制定项目实施计划"),
    PROJECT_ENTRY("project_entry", "确认达到入驻条件"),
    CLOUD_RESOURCE("cloud_resource", "云资源划分及部署"),
    DATA_IMPORT("data_import", "数据准备"),
    PRODUCT_IMPOWER("product_impower", "申请产品授权部署"),
    CLOUD_BROWSER("cloud_browser", "众阳浏览器安装"),
    IMPORT_DATA_OLDFORNEW("import_data_oldfornew", "老换新导数据"),
    PREPARAT_PRODUCT("preparat_product", "各产品准备工作"),
    PREPARAT_DEVICE("preparat_device", "设备对接进度"),
    SCHEDULE_THIRD_PART("schedule_third_part", "三方接口进度"),
    PREPARAT_REPORT("preparat_report", "报表制作"),
    PREPARAT_FORM("preparat_form", "表单制作"),
    SCHEME_PROJECT("scheme_project", "制定切换方案"),
    PREPARAT_CHECK("preparat_check", "项目管理办公室审核"),
    TEST_LAUNCH("test_launch", "全流程模拟上线"),
    PROJECT_LAUNCH("project_launch", "确认上线"),
    UNFINISH_WORK("unfinish_work", "上现阶段未完成工作"),
    PRODUCT_APPLICATION("product_application", "系统应用功能点"),
    PROJECT_ACCEPT("project_accept", "项目验收"),
    DATA_PREPARE("data_prepare", "部署前数据准备"),
    SETTLED_PMO_AUDIT("settled_pmo_audit", "提交项目管理办公室核实"),
    SCHEME_IMPLEMENT("scheme_implement", "准备及上线任务分配"),
    NULl("", null);


    String code;

    String name;


    MilestoneNodeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static MilestoneNodeEnum getByCode(String key) {
        for (MilestoneNodeEnum eyeCheckTransfer : MilestoneNodeEnum.values()) {
            if (eyeCheckTransfer.code.equals(key)) {
                return eyeCheckTransfer;
            }
        }
        return MilestoneNodeEnum.NULl;
    }
}
