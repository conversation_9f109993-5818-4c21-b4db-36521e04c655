package com.msun.csm.common.interceptor;

import static com.msun.csm.common.staticvariable.StaticPara.TRACE_ID;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.msun.csm.common.annotation.BizDesc;
import com.msun.csm.common.annotation.Log;
import io.swagger.annotations.ApiOperation;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import cn.hutool.core.lang.UUID;


/**
 * @DESCRIPTION:
 * @AUTHOR: mengchuan
 * @DATE: 2023/5/6
 */
@Order(1)
@Component
public class LogInterceptor extends HandlerInterceptorAdapter {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String traceId = UUID.randomUUID().toString(true).toUpperCase();
        MDC.put(TRACE_ID, traceId);
        buildMDCInfo(handler);
        return true;
    }

    private static void buildMDCInfo(Object handler) {
        BizDesc bizDesc = null;
        ApiOperation apiOperation = null;
        Log logAnnotation = null;
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            if (handlerMethod.getMethod().isAnnotationPresent(BizDesc.class)) {
                bizDesc = handlerMethod.getMethod().getAnnotation(BizDesc.class);
            } else if (handlerMethod.getMethod().getDeclaringClass().isAnnotationPresent(BizDesc.class)) {
                bizDesc = handlerMethod.getMethod().getDeclaringClass().getAnnotation(BizDesc.class);
            } else if (handlerMethod.getMethod().isAnnotationPresent(Log.class)) {
                logAnnotation = handlerMethod.getMethod().getAnnotation(Log.class);
            } else if (handlerMethod.getMethod().getDeclaringClass().isAnnotationPresent(Log.class)) {
                logAnnotation = handlerMethod.getMethod().getDeclaringClass().getAnnotation(Log.class);
            } else if (handlerMethod.getMethod().isAnnotationPresent(ApiOperation.class)) {
                apiOperation = handlerMethod.getMethod().getAnnotation(ApiOperation.class);
            } else if (handlerMethod.getMethod().getDeclaringClass().isAnnotationPresent(ApiOperation.class)) {
                apiOperation = handlerMethod.getMethod().getDeclaringClass().getAnnotation(ApiOperation.class);
            }
        }
        if (bizDesc != null) {
            MDC.put("bizName", bizDesc.value());
            MDC.put("bizDetail", bizDesc.detail());
            MDC.put("person", bizDesc.person());
        } else if (logAnnotation != null) {
            MDC.put("bizName", logAnnotation.operName());
            MDC.put("bizDetail", logAnnotation.operDetail());
            MDC.put("person", "");
        } else if (apiOperation != null) {
            MDC.put("bizName", apiOperation.value());
            MDC.put("bizDetail", apiOperation.notes());
            MDC.put("person", "");
        } else {
            MDC.put("bizName", "");
            MDC.put("bizDetail", "");
            MDC.put("person", "");
        }
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                           ModelAndView modelAndView) {
        //防止内存泄露
        MDC.remove(TRACE_ID);
        MDC.remove("bizName");
        MDC.remove("bizDetail");
        MDC.remove("person");
    }
}
