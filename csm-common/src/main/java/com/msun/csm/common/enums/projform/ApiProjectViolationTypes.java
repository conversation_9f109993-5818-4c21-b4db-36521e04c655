package com.msun.csm.common.enums.projform;

/**
 * @ClassName: ApiProjectViolationTypes
 * @Description: 项目交付违规类型
 * @Author: zhangdi
 * @Date: 2024-10-18
 */
public enum ApiProjectViolationTypes {


    /**
     *调研后新增数量
     *
     * 设备(个)
     * 接口(个)
     * 小硬件(个)
     * 特殊需求(个)
     *
     *
     * 入驻后新增产品数量
     *
     * 新增产品(个)
     * 新增授权(个)
     *
     *
     * 上线前未完成数量
     *
     * 产品准备(个)
     * 必备打印报表(个)
     * 必备表单(个)
     * 必备接口(个)
     * 必备设备(个)
     */



    SURVEYEQUIPMENT("survey_equipment", "设备"),
    SURVEYINTERFACE("survey_interface", "接口"),
    SURVEYHARDWARE("survey_hardware", "小硬件"),
    SURVEYSPECIAL("survey_special", "特殊需求"),

    ENTERPRODUCT("enter_product", "入驻后新增产品数量"),
    ENTERAUTHORIZATION("enter_authorization", "新增授权"),

    UNFINISHEDPRODUCT("unfinished_product", "待处理任务"),
    UNFINISHEDREPORT("unfinished_report", "必备打印报表"),
    UNFINISHEDFORM("unfinished_form", "必备表单"),
    UNFINISHEDINTERFACE("unfinished_interface", "必备接口"),
    UNFINISHEDEQUIPMENT("unfinished_equipment", "必备设备");


    String code;

    String name;


    ApiProjectViolationTypes(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getDescByCode(String key) {
        for (ApiProjectViolationTypes item : ApiProjectViolationTypes.values()) {
            if (item.getCode().equals(key)) {
                return item.name;
            }
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
