package com.msun.csm.common.enums;

import lombok.Getter;

/**
 * @description:用于表示前端显示与否的标识
 * @fileName:
 * @author:<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @updateBy:
 * @Date:Created in 10:44 2024/5/27
 * @remark:
 */
@Getter
public enum FrontVisibleEnum {

    DISPLAY(1, "展示"),
    NOT_DISPLAY(0, "不展示");

    private Integer code;

    private String desc;

    FrontVisibleEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
