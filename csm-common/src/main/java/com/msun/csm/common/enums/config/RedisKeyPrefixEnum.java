package com.msun.csm.common.enums.config;


/**
 * 说明: 缓存key前缀 - 统一管理key前缀
 * @author: <PERSON><PERSON><PERSON>
 * @createAt: 2024/6/17 14:08
 * @remark: Copyright
  */
public enum RedisKeyPrefixEnum {
    SYS_CONFIG("csm:config:", "系统配置缓存");

    private String keyPrefix;
    private String keyPrefixDesc;

    RedisKeyPrefixEnum(String keyPrefix, String keyPrefixDesc) {
        this.keyPrefix = keyPrefix;
        this.keyPrefixDesc = keyPrefixDesc;
    }

    public String getKeyPrefix() {
        return keyPrefix;
    }

    public String getKeyPrefixDesc() {
        return keyPrefixDesc;
    }

}
