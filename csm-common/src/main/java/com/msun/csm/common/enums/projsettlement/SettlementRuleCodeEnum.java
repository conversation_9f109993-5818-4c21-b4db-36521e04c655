package com.msun.csm.common.enums.projsettlement;

/**
 * 入场条件规则
 */
public enum SettlementRuleCodeEnum {

    SETTLEMENT_HARDWARE_LIST_FILE("SETTLEMENT_HARDWARE_LIST_FILE", "采购到货清单 (根据小硬件清单购买商品后到货清单)", 0),
    SETTLEMENT_CLOUD_CONFIRM_FORM("SETTLEMENT_CLOUD_CONFIRM_FORM", "云资源确认单", 1),
    SETTLEMENT_PLAN_CLOUD_FILE("SETTLEMENT_PLAN_CLOUD_FILE", "预规划资源附件", 2),
    SETTLEMENT_PAY_ADVANCE_CHARGE("SETTLEMENT_PAY_ADVANCE_CHARGE", "预付款提示", 3),

    SETTLEMENT_RESEARCH("SETTLEMENT_RESEARCH", "调研内容文件", 4),
    SETTLEMENT_NO_SEND_MID_SERVICE("SETTLEMENT_NO_SEND_MID_SERVICE",
            "请先确定云健康部署环境，若部署在私有云（且非众阳合作云），请联系销售派工中间件部署服务，并在销售申请入驻时上传《预资源规划》", 5),
    SETTLEMENT_NO_CLOUD_FORM("SETTLEMENT_NO_CLOUD_FORM",
            "请先确定云健康部署环境，若部署在众阳付费云，请联系销售派工云资源工单，并在销售申请入驻时上传《云服务开通确认单》", 6);

    private final String code;

    private final String desc;

    private final int voCode;

    SettlementRuleCodeEnum(String code, String desc, int voCode) {
        this.code = code;
        this.desc = desc;
        this.voCode = voCode;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public int getVoCode() {
        return voCode;
    }

    /**
     * 通过code获取描述
     *
     * @param voCode 前端使用编码
     * @return String
     */
    public static String getCodeByVoCode(int voCode) {
        for (SettlementRuleCodeEnum s : values()) {
            if (voCode == s.getVoCode()) {
                return s.getCode();
            }
        }
        return null;
    }

}
