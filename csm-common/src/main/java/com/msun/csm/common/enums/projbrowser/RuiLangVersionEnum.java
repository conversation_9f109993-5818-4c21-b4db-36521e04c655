package com.msun.csm.common.enums.projbrowser;


/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/28/8:44
 */
public enum RuiLangVersionEnum {

    V5_5("5.5", "V5.5"),
    V6_6("6.6", "V6.6"),
    V6_7("6.7", "V6.7"),
    V6_8("6.8", "V6.8");

    //5.5 6.6 6.7 6.8   miv不清楚
    private String version;
    /**
     * 老项目产品状态编码
     */
    private String versionCode;


    RuiLangVersionEnum(String version, String versionCode) {
        this.version = version;
        this.versionCode = versionCode;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(String versionCode) {
        this.versionCode = versionCode;
    }
    public static RuiLangVersionEnum[] toArray() {
        return RuiLangVersionEnum.values();
    }
}
