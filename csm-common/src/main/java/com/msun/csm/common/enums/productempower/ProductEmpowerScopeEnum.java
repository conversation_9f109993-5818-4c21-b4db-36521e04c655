package com.msun.csm.common.enums.productempower;

import lombok.Getter;

/**
 * 产品授权范围
 */
@Getter
public enum ProductEmpowerScopeEnum {

    PRODUCT(1, "授权产品"),
    MODULE(2, "授权菜单");

    private final int code;

    private final String desc;

    ProductEmpowerScopeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ProductEmpowerScopeEnum getEnumByCode(int code) {
        for (ProductEmpowerScopeEnum s : values()) {    //values()方法返回enum实例的数组
            if (code == s.getCode()) {
                return s;
            }
        }
        return null;
    }
}
