package com.msun.csm.common.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CsmPageResult<T> {

    /**
     * 当前页
     */
    private Integer pageNum;

    /**
     * 每页数据条数
     */
    private Integer pageSize;

    /**
     * 总数据条数
     */
    Integer total;

    /**
     * 分页结果
     */
    private List<T> pageResultList;
}
