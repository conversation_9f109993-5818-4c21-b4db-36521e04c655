package com.msun.csm.common.enums.projapplyorder;

/**
 * 云环境类型
 */
public enum CloudEnvTypeEnum {

    PUBLIC(0, 1, "众阳云, 共享云"),
    PRIVATE(1, 0, "私有云");

    private int code;
    private int devCode;
    private String desc;


    CloudEnvTypeEnum(int code, int devCode, String desc) {
        this.code = code;
        this.desc = desc;
        this.devCode = devCode;
    }

    public int getCode() {
        return code;
    }

    public int getDevCode() {
        return devCode;
    }

    public String getDesc() {
        return desc;
    }

    public static CloudEnvTypeEnum getEnumByCode(int code) {
        for (CloudEnvTypeEnum s : values()) {    //values()方法返回enum实例的数组
            if (code == s.getCode()) {
                return s;
            }
        }
        return null;
    }
}
