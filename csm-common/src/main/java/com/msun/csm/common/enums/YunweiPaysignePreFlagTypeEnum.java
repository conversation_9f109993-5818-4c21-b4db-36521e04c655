package com.msun.csm.common.enums;

import com.msun.csm.common.enums.projsettlement.PaySignageEnum;

import lombok.Getter;

/**
 * @ClassName: BusinessType
 * @Description: 业务操作类型
 */
@Getter
public enum YunweiPaysignePreFlagTypeEnum {
    /**
     * 通过
     */
    PASSED(0, "通过", PaySignageEnum.PAY),
    /**
     * 不通过
     */
    NOT_PASS(1, "不通过", PaySignageEnum.NOT_PAY),
    /**
     * 进行中
     */
    ING(2, "进行中", PaySignageEnum.NOT_PAY),
    /**
     * 特批
     */
    JUMP_PAY_SIGN_AUDIT(3, "特批", PaySignageEnum.NOT_PAY);


    private final int code;
    private final String desc;
    private final PaySignageEnum paySignageEnum;

    YunweiPaysignePreFlagTypeEnum(int code, String desc, PaySignageEnum paySignageEnum) {
        this.code = code;
        this.desc = desc;
        this.paySignageEnum = paySignageEnum;
    }

    public static YunweiPaysignePreFlagTypeEnum getEnum(int code) {
        for (YunweiPaysignePreFlagTypeEnum s : values()) {
            if (code == s.getCode()) {
                return s;
            }
        }
        return null;
    }
}
