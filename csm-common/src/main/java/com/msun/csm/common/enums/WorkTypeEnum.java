package com.msun.csm.common.enums;

/**
 * @description:
 * @fileName:
 * @author:<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @updateBy:
 * @Date:Created in 10:02 2024/4/25
 * @remark:
 */

public enum WorkTypeEnum {
    /*
     * 工单派工
     * */
    ORDER_WORK(0),
    /*
     * 特批派工
     * */
    SPECIAL_WORK(1);


    private Integer code;


    WorkTypeEnum(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return this.code;
    }
}
