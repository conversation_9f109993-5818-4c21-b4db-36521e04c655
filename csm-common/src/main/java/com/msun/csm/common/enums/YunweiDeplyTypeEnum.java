package com.msun.csm.common.enums;

import lombok.Getter;

/**
 * @Description: 运维平台回调接口资源类型
 */
@Getter
public enum YunweiDeplyTypeEnum {
    //7、容灾
    DISASTER_RECOVERY(7, "容灾工单");

    private final int code;

    private final String name;

    YunweiDeplyTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static YunweiDeplyTypeEnum getEnum(int code) {
        for (YunweiDeplyTypeEnum s : values()) {
            if (code == s.getCode()) {
                return s;
            }
        }
        return null;
    }
}
