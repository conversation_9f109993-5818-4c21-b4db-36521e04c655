package com.msun.csm.common.enums.rule;

/**
 * @description:
 * @fileName:
 * @author:<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @updateBy:
 * @Date:Created in 17:00 2024/4/28
 * @remark:
 */
public enum CustomerLimitProductEnum {

    AIMS("aims", "手麻表单", 4050L),
    ICUISNEW("icuisnew", "重症表单", 4058L),
    EMIS("emis", "急诊表单", 4059L),
    REPORTWEB("reportweb", "数据查询", 3668L),
    BASEPRINT("baseprint", "打印报表", 5622L),
    REPORTLIMIT("reportlimit", "统计报表", 3668L),
    INTERFACELIMIT("interfacelimit", "三方接口", 3668L),

    HULIDANYUAN("hulidanyuan", "护理表单", 3368L);


    String code;

    String desc;

    Long yyProductId;


    CustomerLimitProductEnum(String code, String desc, Long yyProductId) {
        this.code = code;
        this.desc = desc;
        this.yyProductId = yyProductId;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public Long getYyProductId() {
        return this.yyProductId;
    }

    public static CustomerLimitProductEnum getEnumByCode(String code) {
        for (CustomerLimitProductEnum value : CustomerLimitProductEnum.values()) {
            if (code == value.getCode()) {
                return value;
            }
        }
        return null;
    }
}
