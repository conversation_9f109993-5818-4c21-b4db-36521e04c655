package com.msun.csm.common.enums;


public enum ProjectPlanStatusEnum {

    UNFINISHED(0, "未开始/未完成"),
    FINISHED(1, "已完成"),
    UNDERWAY(2, "进行中");

    /**
     * 状态编码
     */
    private final Integer statusCode;

    /**
     * 状态名称
     */
    private final String statusName;

    ProjectPlanStatusEnum(Integer statusCode, String statusName) {
        this.statusCode = statusCode;
        this.statusName = statusName;
    }

    public Integer getStatusCode() {
        return statusCode;
    }

    public static ProjectPlanStatusEnum getPlanStatusEnumByCode(Integer statusCode) {
        for (ProjectPlanStatusEnum statusEnum : ProjectPlanStatusEnum.values()) {
            if (statusEnum.statusCode.equals(statusCode)) {
                return statusEnum;
            }
        }
        return null;
    }
}
