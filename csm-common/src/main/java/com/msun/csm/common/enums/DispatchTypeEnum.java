package com.msun.csm.common.enums;

import lombok.Getter;

/**
 * @Description: 派工类型
 */
@Getter
public enum DispatchTypeEnum {

    FIRST(1, "首期"),
    RENEWAL(2, "续期");

    /**
     * 编码
     */
    private final int code;
    /**
     * 描述
     */
    private final String desc;

    DispatchTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 获取对应状态的枚举
     *
     * @param code 编码
     * @return DisasterRecoveryApplyStatusEnum
     */
    public static DispatchTypeEnum getEnumByCode(int code) {
        for (DispatchTypeEnum value : DispatchTypeEnum.values()) {
            if (code == value.getCode()) {
                return value;
            }
        }
        return null;
    }
}
