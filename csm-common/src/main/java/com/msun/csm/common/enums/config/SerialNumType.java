package com.msun.csm.common.enums.config;

import lombok.Getter;

/**
 * 编号类型
 * 区分不通编码类型
 * 如：部署申请单编号 与 医院编号 与 产品编号会使用不通类型编码, 区分后会根据对应编码船舰对应编号。
 * 使用的表结构为 config_serial_number
 */
@Getter
public enum SerialNumType {

    APPLY_ORDER("applyOrder", "云资源, 医院, 产品申请开通申请单"),
    DISASTER_CLOUD_ORDER("disasterCloudOrder", "云容灾开通申请");

    private final String keyCode;
    private final String keyDesc;

    SerialNumType(String keyCode, String keyDesc) {
        this.keyCode = keyCode;
        this.keyDesc = keyDesc;
    }
}
