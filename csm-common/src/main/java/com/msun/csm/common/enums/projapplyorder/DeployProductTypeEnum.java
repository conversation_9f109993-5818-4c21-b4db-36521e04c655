package com.msun.csm.common.enums.projapplyorder;

/**
 * 产品部署申请, 产品类型
 * <p>作用与外采类产品的部署申请.</p>
 */
public enum DeployProductTypeEnum {

    NOT_PURCHASE(0, "非外采"),
    PURCHASE(1, "外采");

    private final int code;
    private final String desc;


    DeployProductTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static DeployProductTypeEnum getEnumByCode(int code) {
        for (DeployProductTypeEnum s : values()) {
            if (code == s.getCode()) {
                return s;
            }
        }
        return null;
    }
}
