package com.msun.csm.common.staticvariable;

import lombok.Data;

/**
 * @DESCRIPTION:
 * @AUTHOR: mengchuan
 * @DATE: 2022/8/10
 */
@Data
public class StaticPara {

    public static final String HEADER_TOKEN_NAME = "token";

    /**
     * redis缓存时间（小时）
     */
    public static final Integer REDIS_CACHE_TIME = 6;

    /**
     * 通配符
     */
    public static final String WILDCARD = "*";

    /**
     * 开始时间
     */
    public static final String STANDARD_START_TIME = " 00:00:00";

    /**
     * 结束时间
     */
    public static final String STANDARD_END_TIME = " 23:59:59";

    /**
     * traceId
     */
    public static final String TRACE_ID = "traceId";

    /**
     * mac系统下压缩包中的隐藏文件夹
     */
    public static final String MACOS_ZIP_PACKAGE = "__MACOSX";

    /**
     * 接口超时时间-毫秒
     */
    public static final Long INTERFACE_TIMEOUT = 10000L;

    /**
     * 默认Long类型值-例如老项目中无数据的字段
     */
    public static final Long DEFAULT_LONG = -1L;

    /**
     * obs文件临时访问有效时间（秒）
     */
    public static final Integer OBS_TEMPTIME = 300;

    /**
     * tduck接口成功701返回码
     */
    public static final Integer TDUCK_SUCCESS_CODE = 701;
}
