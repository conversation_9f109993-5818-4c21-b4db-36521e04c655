package com.msun.csm.common.enums.api.yunwei;

import lombok.Getter;

/**
 * 云资源类型( 1 云资源  2 云订阅  3 云容灾 ）
 */
@Getter
public enum CloudTypeEnum {
    CLOUD_RESOURCE(1, "云资源", 9),
    CLOUD_SUBSCRIBE(2, "云订阅", 1),
    CLOUD_DISASTER_RECOVERY(3, "云容灾", 7);

    private final Integer code;
    private final Integer orderType;
    private final String desc;

    CloudTypeEnum(Integer code, String desc, Integer orderType) {
        this.code = code;
        this.orderType = orderType;
        this.desc = desc;
    }

    public static CloudTypeEnum getEnumByOrderType(int orderType) {
        for (CloudTypeEnum s : values()) {    //values()方法返回enum实例的数组
            if (orderType == s.getOrderType()) {
                return s;
            }
        }
        return null;
    }
}
