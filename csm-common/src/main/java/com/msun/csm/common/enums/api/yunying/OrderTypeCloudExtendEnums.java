package com.msun.csm.common.enums.api.yunying;

import lombok.Getter;

/**
 * 未派工云资源
 */
@Getter
public enum OrderTypeCloudExtendEnums {
    // 未派工云资源。 电销用户, 共享云
    NO_CLOUD_RESOURCE_ORDER(-1, "未派工云资源(未派工众阳云, 如电销客户; 共享云)");


    private final Integer code;
    private final String name;

    OrderTypeCloudExtendEnums(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static OrderTypeCloudExtendEnums getEnum(int code) {
        for (OrderTypeCloudExtendEnums s : values()) {
            if (code == s.getCode()) {
                return s;
            }
        }
        return null;
    }
}
