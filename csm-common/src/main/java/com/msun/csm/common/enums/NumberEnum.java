package com.msun.csm.common.enums;

/**
 * 说明: 数字魔法值枚举
 *
 * @return:
 * @author: <PERSON><PERSON><PERSON>
 * @createAt: 2024/5/10 11:10
 * @remark: Copyright
 */
public enum NumberEnum {

    /**
     * 0
     */
    NO_NEGATIVE_ONE(-1, "-1"),
    /**
     * 0
     */
    NO_0(0, "0"),
    /**
     * 1
     */
    NO_1(1, "1"),
    /**
     * 2
     */
    NO_2(2, "2"),
    /**
     * 3
     */
    NO_3(3, "3"),
    /**
     * 4
     */
    NO_4(4, "4"),
    /**
     * 5
     */
    NO_5(5, "5"),
    /**
     * 6
     */
    NO_6(6, "6"),
    /**
     * 7
     */
    NO_7(7, "7"),
    /**
     * 9
     */
    NO_9(9, "9"),
    /**
     * 18
     */
    NO_18(18, "18"),
    /**
     * 11
     */
    NO_11(11, "11"),
    /**
     * 12
     */
    NO_12(12, "12"),
    /**
     * 13
     */
    NO_13(13, "13"),
    /**
     * 14
     */
    NO_14(14, "14"),
    /**
     * 15
     */
    NO_15(15, "15"),
    /**
     * 18
     */
    NO_19(19, "19"),
    /**
     * 60
     */
    NO_60(60, "60"),
    /**
     * 65
     */
    NO_65(65, "65"),
    /**
     * 8
     */
    NO_8(8, "8"),
    /**
     * 99
     */
    NO_99(99, "99"),
    /**
     * 128
     */
    NO_128(128, "128"),
    /**
     * 152
     */
    NO_152(152, "152"),
    /**
     * 10
     */
    NO_10(10, "10"),

    NO_20(20, "20"),

    NO_21(21, "21"),
    NO_22(22, "22"),
    NO_23(23, "23"),
    NO_24(24, "24"),
    NO_25(25, "25"),
    NO_26(26, "26"),
    NO_27(27, "27"),
    NO_28(28, "28"),
    NO_29(29, "29"),
    NO_30(30, "30"),
    NO_31(31, "31"),
    NO_32(32, "32"),
    NO_33(33, "33"),
    NO_34(34, "34"),
    NO_35(35, "35"),
    NO_36(36, "36"),
    NO_37(37, "37"),
    NO_38(38, "38"),
    NO_39(39, "39"),
    NO_40(40, "40"),
    NO_41(41, "41"),
    NO_42(42, "42"),
    NO_43(43, "43"),
    NO_44(44, "44"),
    NO_45(45, "45"),
    NO_46(46, "46"),
    NO_47(47, "47"),
    NO_48(48, "48"),
    NO_49(49, "49"),
    NO_50(50, "50"),
    NO_51(51, "51"),
    NO_52(52, "52"),
    NO_53(53, "53"),
    NO_54(54, "54"),
    NO_55(55, "55"),
    NO_56(56, "56"),
    NO_57(57, "57"),
    NO_58(58, "58"),
    NO_59(59, "59"),
    NO_61(61, "61"),
    NO_62(62, "62"),
    NO_63(63, "63"),
    NO_64(64, "64"),
    NO_66(66, "66"),
    NO_67(67, "67"),
    NO_68(68, "68"),
    NO_69(69, "69"),
    NO_70(70, "70"),
    NO_71(71, "71"),
    NO_72(72, "72"),
    NO_73(73, "73"),
    NO_74(74, "74"),
    NO_75(75, "75"),
    NO_76(76, "76"),
    NO_77(77, "77"),
    NO_78(78, "78"),
    NO_79(79, "79"),
    NO_80(80, "80"),
    NO_81(81, "81"),
    NO_82(82, "82"),
    NO_83(83, "83"),
    NO_84(84, "84"),
    NO_85(85, "85"),
    NO_86(86, "86"),
    NO_87(87, "87"),
    NO_88(88, "88"),
    NO_89(89, "89"),
    NO_90(90, "90"),
    NO_91(91, "91"),
    NO_92(92, "92"),
    NO_93(93, "93"),
    NO_94(94, "94"),
    NO_95(95, "95"),
    NO_96(96, "96"),
    NO_97(97, "97"),
    NO_98(98, "98"),
    NO_100(100, "100"),
    NO_101(101, "101"),
    NO_102(102, "102"),
    NO_103(103, "103"),
    NO_104(104, "104"),
    NO_105(105, "105"),
    NO_106(106, "106"),
    NO_107(107, "107"),
    NO_108(108, "108"),
    NO_109(109, "109"),
    NO_110(110, "110"),
    NO_111(111, "111"),
    NO_112(112, "112"),
    NO_113(113, "113"),
    NO_114(114, "114"),
    NO_115(115, "115"),
    NO_116(116, "116"),
    NO_117(117, "117"),
    NO_118(118, "118"),
    NO_119(119, "119"),
    NO_120(120, "120"),
    NO_121(121, "121"),
    NO_122(122, "122"),
    NO_123(123, "123"),
    NO_124(124, "124"),
    NO_125(125, "125"),
    NO_126(126, "126"),
    NO_127(127, "127"),
    NO_129(129, "129"),
    NO_130(130, "130"),
    NO_131(131, "131"),
    NO_132(132, "132"),
    NO_133(133, "133"),
    NO_134(134, "134"),
    NO_135(135, "135"),
    NO_136(136, "136"),
    NO_137(137, "137"),
    NO_138(138, "138"),
    NO_139(139, "139"),
    NO_140(140, "140"),
    NO_141(141, "141"),
    NO_142(142, "142"),
    NO_143(143, "143"),
    NO_144(144, "144"),
    NO_145(145, "145"),
    NO_146(146, "146"),
    NO_147(147, "147"),
    NO_148(148, "148"),
    NO_149(149, "149"),
    NO_150(150, "150"),
    NO_151(151, "151"),
    NO_153(153, "153"),
    NO_154(154, "154"),
    NO_155(155, "155"),
    NO_156(156, "156"),
    NO_157(157, "157"),
    NO_158(158, "158"),
    NO_159(159, "159"),
    NO_160(160, "160"),
    NO_161(161, "161"),
    NO_162(162, "162"),
    NO_163(163, "163"),
    NO_164(164, "164"),
    NO_165(165, "165"),
    NO_166(166, "166"),
    NO_167(167, "167"),
    NO_168(168, "168"),
    NO_169(169, "169"),
    NO_170(170, "170"),
    NO_171(171, "171"),
    NO_172(172, "172"),
    NO_173(173, "173"),
    NO_174(174, "174"),
    NO_175(175, "175"),
    NO_176(176, "176"),
    NO_177(177, "177"),
    NO_178(178, "178"),
    NO_179(179, "179"),
    NO_180(180, "180"),
    NO_181(181, "181"),
    NO_182(182, "182"),
    NO_183(183, "183"),
    NO_184(184, "184"),
    NO_185(185, "185"),
    NO_186(186, "186"),
    NO_187(187, "187"),
    NO_188(188, "188"),
    NO_189(189, "189"),
    NO_190(190, "190"),
    NO_191(191, "191"),
    NO_192(192, "192"),
    NO_193(193, "193"),
    NO_194(194, "194"),
    NO_195(195, "195"),
    NO_196(196, "196"),
    NO_197(197, "197"),
    NO_198(198, "198"),
    NO_199(199, "199"),
    NO_200(200, "200"),
    /**
     * 999
     */
    NO_999(999, "999"),

    NO_1000(1000, "1000"),

    NO_40004(40004, "40004");


    private final Integer num;
    private final String description;

    NumberEnum(Integer num, String description) {
        this.num = num;
        this.description = description;
    }

    public Integer num() {
        return this.num;
    }

    public String description() {
        return this.description;
    }
}



