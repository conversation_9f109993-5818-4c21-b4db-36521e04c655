package com.msun.csm.common.enums;

import lombok.Getter;

/**
 * @description:
 * @fileName:
 * @author:<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @updateBy:
 * @Date:Created in 17:00 2024/4/28
 * @remark:
 */
@Getter
public enum DataPrepareMenuEnum {

    OldSysDataQualityDetect("OldSysDataQualityDetect", "老系统数据质量检测"),
    OldSysVersionDetect("OldSysVersionDetect", "老系统版本检测"),
    DictCompareToolsConfig("DictCompareToolsConfig", "字典对照及工具配置"),
    NewOnlineBasicDataImport("NewOnlineBasicDataImport", "新上线数据准备工作"),
    PublicHealthDataImport("PublicHealthDataImport", "老换新公卫数据导入");


    String menuCode;

    String menuName;


    DataPrepareMenuEnum(String menuCode, String menuName) {
        this.menuCode = menuCode;
        this.menuName = menuName;
    }

    public static DataPrepareMenuEnum getByCode(String key) {
        for (DataPrepareMenuEnum eyeCheckTransfer : DataPrepareMenuEnum.values()) {
            if (eyeCheckTransfer.menuCode.equals(key)) {
                return eyeCheckTransfer;
            }
        }
        return null;
    }

    public static DataPrepareMenuEnum getByYyProductId(String menuName) {
        for (DataPrepareMenuEnum eyeCheckTransfer : DataPrepareMenuEnum.values()) {
            if (eyeCheckTransfer.menuName.equals(menuName)) {
                return eyeCheckTransfer;
            }
        }
        return null;
    }

    public static DataPrepareMenuEnum getCodes(String key) {
        for (DataPrepareMenuEnum eyeCheckTransfer : DataPrepareMenuEnum.values()) {
            if (eyeCheckTransfer.menuCode.equals(key)) {
                return eyeCheckTransfer;
            }
        }
        return null;
    }
}
