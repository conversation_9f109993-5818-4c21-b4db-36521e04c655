package com.msun.csm.common.enums.device;

import lombok.Getter;

/**
 * 设备状态枚举
 * 设备状态【0：未申请；1：已申请:；2：已驳回:；3：研发中；4：研发完成:；5：测试通过:；6：测试失败】
 */
@Getter
public enum EquipStatusEnum {

    NOT_APPLY(0, "未申请"),

    APPLYED(1, "已申请"),

    REJECTED(2, "已驳回"),

    DEVELOPING(3, "研发中"),

    DEVELOPED(4, "研发完成"),

    TEST_PASS(5, "测试通过"),

    TEST_FAIL(6, "测试失败"),

    BE_SURE(8, "待确定"),
    
    WAIT_UPLOAD(9, "待上传");

    private final Integer code;

    private final String desc;


    EquipStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
