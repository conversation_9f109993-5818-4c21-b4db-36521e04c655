package com.msun.csm.common.enums.could;

import lombok.Getter;

/**
 * 云服务授权类型
 */
@Getter
public enum CloudPhaseTypeEnum {
    FIRST(1, "首次开通"),
    //2、硬件
    RENEWAL(2, "续期"),

    FIX_TIME(3, "修改时间");


    private final Integer code;
    private final String desc;

    CloudPhaseTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CloudPhaseTypeEnum getEnum(int code) {
        for (CloudPhaseTypeEnum s : values()) {    //values()方法返回enum实例的数组
            if (code == s.getCode()) {
                return s;
            }
        }
        return null;
    }
}
