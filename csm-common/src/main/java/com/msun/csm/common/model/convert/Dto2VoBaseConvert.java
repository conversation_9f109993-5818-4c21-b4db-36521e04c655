package com.msun.csm.common.model.convert;

import java.util.List;

import org.mapstruct.InheritConfiguration;
import org.mapstruct.InheritInverseConfiguration;

/**
 * Created with IntelliJ IDEA
 *
 * @param <Dto>
 * @param <Vo>
 * @Author: duxu
 * @Date: 2024/04/17/17:32
 */
public interface Dto2VoBaseConvert<Dto, Vo> {
    @InheritConfiguration
    Vo dto2Vo(Dto dto);

    @InheritConfiguration
    List<Vo> dto2Vo(List<Dto> sourceList);

    @InheritInverseConfiguration
    Dto vo2Dto(Vo vo);

    @InheritInverseConfiguration
    List<Dto> vo2Dto(List<Vo> voList);
}
