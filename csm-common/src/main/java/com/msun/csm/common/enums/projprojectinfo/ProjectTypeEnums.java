package com.msun.csm.common.enums.projprojectinfo;

import java.util.ArrayList;
import java.util.List;

import com.msun.csm.common.model.BaseIdNameResp;

import lombok.Getter;

/**
 * @Description: 项目类型枚举
 * @Author: MengChuAn
 * @Date: 2024/4/25
 */
@Getter
public enum ProjectTypeEnums {
    //项目实施类型  1单体 2区域 3医共体
    //1单体
    SINGLE(1, "单体", "hospital", 0), //2区域
    REGION(2, "区域", "countryPlat", 1), //3医共体
    MEDICAL_COMMUNITY(3, "医共体", "", -1), //4 电销
    TELE_SALES(4, "电销", "", -1);


    private final Integer code;
    private final String name;
    private final String devCode;
    private final Integer ruleCode;

    ProjectTypeEnums(int code, String name, String devCode, Integer ruleCode) {
        this.code = code;
        this.name = name;
        this.devCode = devCode;
        this.ruleCode = ruleCode;
    }

    public static ProjectTypeEnums getEnum(int code) {
        for (ProjectTypeEnums s : values()) {    //values()方法返回enum实例的数组
            if (code == s.getCode()) {
                return s;
            }
        }
        return null;
    }

    public static List<BaseIdNameResp> getBaseDataList() {
        List<BaseIdNameResp> list = new ArrayList<>();
        for (ProjectTypeEnums s : values()) {    //values()方法返回enum实例的数组
            BaseIdNameResp baseIdNameResp = new BaseIdNameResp();
            baseIdNameResp.setId(s.getCode().longValue());
            baseIdNameResp.setName(s.getName());
            list.add(baseIdNameResp);
        }
        return list;
    }
}
