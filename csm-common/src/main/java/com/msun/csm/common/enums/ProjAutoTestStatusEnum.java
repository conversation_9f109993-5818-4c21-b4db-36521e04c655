package com.msun.csm.common.enums;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Getter;

/**
 * 自动化测试状态枚举
 */
@Getter
public enum ProjAutoTestStatusEnum {

    WAIT_DETECT(0, "未测试"),
    DETECTING(1, "检测中"),
    DETECT_EXEC_FAILED(2, "检测执行失败"),
    DETECT_FAILED(3, "检测不通过"),
    DETECT_PASS_BY_ARTIFICIAL(4, "人工判定通过"),
    DETECT_PASS(5, "检测通过");

    private final Integer code;
    private final String desc;


    ProjAutoTestStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 通过code获取描述
     *
     * @param code
     * @return
     */
    public static String getDescByCode(Integer code) {
        if (ObjectUtil.isNotEmpty(code)) {
            for (ProjAutoTestStatusEnum s : values()) {
                if (code.equals(s.code)) {
                    return s.desc;
                }
            }
        }
        return StrUtil.EMPTY;
    }
}
