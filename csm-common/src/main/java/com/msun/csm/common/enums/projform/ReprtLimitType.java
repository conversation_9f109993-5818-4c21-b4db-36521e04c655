package com.msun.csm.common.enums.projform;

/**
 * @ClassName: FormSourseType
 * @Description: 表单来源类型
 * @Author: zhangdi
 * @Date: 2024-09-18
 */
public enum ReprtLimitType {


    /**
     * ● 锐浪模板：bnMicVtBVuIB0I95O/i2/Q==
     * ● 众阳模板：0FuRorvtsDrRcjH44FJzFg==
     * ● 手动选择：dB9qAtmz7imE1AOPyfKZ2Q==
     */
    RUILANG("bnMicVtBVuIB0I95O/i2/Q==", "锐浪模板", 1),
    ZHONGYANG("0FuRorvtsDrRcjH44FJzFg==", "众阳模板", 2),

    HANDSELECT("dB9qAtmz7imE1AOPyfKZ2Q==", "手动选择", 3);


    String code;

    String name;

    Integer number;


    ReprtLimitType(String code, String name, Integer number) {
        this.code = code;
        this.name = name;
        this.number = number;
    }

    public static String getDescByCode(String key) {
        for (ReprtLimitType item : ReprtLimitType.values()) {
            if (item.getCode().equals(key)) {
                return item.name;
            }
        }
        return null;
    }

    public static String getCodeByNumber(Integer key) {
        for (ReprtLimitType item : ReprtLimitType.values()) {
            if (item.getNumber().equals(key)) {
                return item.code;
            }
        }
        return null;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public Integer getNumber() {
        return this.number;
    }
}
