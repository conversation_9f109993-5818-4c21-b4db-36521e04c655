package com.msun.csm.common.model.convert;

import java.util.List;

import org.mapstruct.InheritConfiguration;
import org.mapstruct.InheritInverseConfiguration;

/**
 * Created with IntelliJ IDEA
 *
 * @param <Dto>
 * @param <Po>
 * @Author: duxu
 * @Date: 2024/04/17/17:31
 */
public interface Dto2PoBaseConvert<Dto, Po> {

    @InheritConfiguration
    Po dto2Po(Dto dto);

    @InheritConfiguration
    List<Po> dto2Po(List<Dto> sourceList);

    @InheritInverseConfiguration
    Dto po2Dto(Po po);

    @InheritInverseConfiguration
    List<Dto> po2Dto(List<Po> poList);
}
