package com.msun.csm.common.enums.projsettlement;

/**
 * 审核结果
 */
public enum CheckResultEnum {

    AUDIT_PASS(1, "审核通过", 1),
    AUDIT_FAIL(0, "审核未通过", 2),
    /**
     * 此处的yYcode新定义, 不属于运营平台状态
     */
    NOT_AUDIT(2, "未审批", -1),
    /**
     * 跳过首付款审核
     */
    JUMP_PAY_SIGN_AUDIT(1, "跳过首付款审核", 3);

    private final int code;

    /**
     * 运营平台是否通过状态
     */
    private final int yYcode;

    private final String desc;

    CheckResultEnum(int code, String desc, int yYcode) {
        this.code = code;
        this.desc = desc;
        this.yYcode = yYcode;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public int getyYcode() {
        return yYcode;
    }

    /**
     * 根据节点code获取节点枚举
     *
     * @param checkResultCode 节点枚举code
     * @return CheckNodeEnum
     */
    public static CheckResultEnum getCheckResultEnumByCode(int checkResultCode) {
        for (CheckResultEnum value : CheckResultEnum.values()) {
            if (checkResultCode == value.getCode()) {
                return value;
            }
        }
        return null;
    }

    public static CheckResultEnum getCheckResultEnumByYyCode(int checkResultCode) {
        for (CheckResultEnum value : CheckResultEnum.values()) {
            if (checkResultCode == value.getyYcode()) {
                return value;
            }
        }
        return null;
    }
}
