package com.msun.csm.common.enums.projreview;


import cn.hutool.core.util.StrUtil;
import lombok.Getter;

/**
 * 调研状态
 */
@Getter
public enum ProjReviewTypeEnum {

    NO_AUDIT(0, "未审核"),

    AUDITED(1, "已审核"),

    REJECTED(2, "已驳回");

    private final int code;

    private final String desc;

    ProjReviewTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据编码获取编码名称
     *
     * @param code 请求编码
     * @return String
     */
    public static String getDescByCode(int code) {
        for (ProjReviewTypeEnum value : ProjReviewTypeEnum.values()) {
            if (value.getCode() == code) {
                return value.getDesc();
            }
        }
        return StrUtil.EMPTY;
    }

    /**
     * 根据编码获取枚举值
     *
     * @param code 请求编码
     * @return String
     */
    public static ProjReviewTypeEnum getEnumByCode(int code) {
        for (ProjReviewTypeEnum value : ProjReviewTypeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
