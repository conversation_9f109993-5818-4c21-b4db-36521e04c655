package com.msun.csm.common.dao.mapper.config;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.msun.csm.common.dao.entity.config.ConfigAuthorization;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/11/26
 */
@Mapper
public interface ConfigAuthorizationMapper {
    /**
     * delete by primary key
     *
     * @param configAuthorizationId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long configAuthorizationId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(ConfigAuthorization record);

    int insertOrUpdate(ConfigAuthorization record);

    int insertOrUpdateSelective(ConfigAuthorization record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(ConfigAuthorization record);

    /**
     * select by primary key
     *
     * @param configAuthorizationId primary key
     * @return object by primary key
     */
    ConfigAuthorization selectByPrimaryKey(Long configAuthorizationId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(ConfigAuthorization record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(ConfigAuthorization record);

    int updateBatch(@Param("list") List<ConfigAuthorization> list);

    int updateBatchSelective(@Param("list") List<ConfigAuthorization> list);

    int batchInsert(@Param("list") List<ConfigAuthorization> list);

    /**
     * 根据appId查询配置信息
     *
     * @param appId
     * @return
     */
    ConfigAuthorization selectByAppId(String appId);
}
