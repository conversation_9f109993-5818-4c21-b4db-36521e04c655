package com.msun.csm.common.enums;

/**
 * @description:
 * @fileName:
 * @author:<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @updateBy:
 * @Date:Created in 17:00 2024/4/28
 * @remark:
 */
public enum DataResourceEnum {

    PRODUCT("product", "来源于产品列表"),
    HOSPITAL("hospital", "来源于医院列表");


    String code;

    String desc;


    DataResourceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }
}
