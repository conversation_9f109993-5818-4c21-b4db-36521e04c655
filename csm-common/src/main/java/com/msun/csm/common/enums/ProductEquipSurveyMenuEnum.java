package com.msun.csm.common.enums;

import lombok.Getter;

/**
 * @description:
 * @fileName:
 * @author:<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @updateBy:
 * @Date:Created in 17:00 2024/4/28
 * @remark:
 */
@Getter
public enum ProductEquipSurveyMenuEnum {

    SURVEYEQUIPALL("EquipmentSurveySummary", -1L, "汇总"),
    LISEQUIP("LisEquipment", 4073L, "Lis"),
    PACSEQUIP("PacsEquipment", 4063L, "Pacs"),
    HDEQUIP("HemodialysisEquipment", 4051L, "血透"),
    AIMSEQUIP("SurgicalAnesthesiaEquipment", 4050L, "手麻"),
    ECGEQUIP("ECGEquipment", 4055L, "心电");


    String code;

    String name;

    Long yyProductId;


    ProductEquipSurveyMenuEnum(String code, Long yyProductId, String name) {
        this.code = code;
        this.yyProductId = yyProductId;
        this.name = name;
    }

    public static ProductEquipSurveyMenuEnum getByCode(String key) {
        for (ProductEquipSurveyMenuEnum eyeCheckTransfer : ProductEquipSurveyMenuEnum.values()) {
            if (eyeCheckTransfer.code.equals(key)) {
                return eyeCheckTransfer;
            }
        }
        return null;
    }

    public static ProductEquipSurveyMenuEnum getByYyProductId(Long yyProductId) {
        for (ProductEquipSurveyMenuEnum eyeCheckTransfer : ProductEquipSurveyMenuEnum.values()) {
            if (eyeCheckTransfer.yyProductId.equals(yyProductId)) {
                return eyeCheckTransfer;
            }
        }
        return null;
    }

    public static ProductEquipSurveyMenuEnum getCodes(String key) {
        for (ProductEquipSurveyMenuEnum eyeCheckTransfer : ProductEquipSurveyMenuEnum.values()) {
            if (eyeCheckTransfer.code.equals(key)) {
                return eyeCheckTransfer;
            }
        }
        return null;
    }
}
