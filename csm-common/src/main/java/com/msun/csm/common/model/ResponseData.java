package com.msun.csm.common.model;

import com.msun.core.component.implementation.api.deviceanaysis.dto.ErrorResponseData;
import com.msun.core.component.implementation.api.deviceanaysis.dto.SuccessResponseData;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@Builder
@ToString
@ApiModel
public class ResponseData<T> {

    public static final String DEFAULT_SUCCESS_MESSAGE = "请求成功";

    public static final String DEFAULT_ERROR_MESSAGE = "网络异常";

    public static final int DEFAULT_SUCCESS_CODE = 200;

    public static final int DEFAULT_ERROR_CODE = 500;

    /**
     * 请求是否成功
     */
    @ApiModelProperty(value = "请求是否成功", example = "true", required = true, position = 1, dataType = "Boolean",
            notes = "若整个请求流程完成时,则为true;若请求过程中出现异常信息,则为false")
    private Boolean success;

    /**
     * 响应状态码
     */
    @ApiModelProperty(value = "响应状态码", example = "200", required = true, position = 2, dataType = "Integer",
            notes = "响应状态码")
    private Integer code;

    /**
     * 响应信息
     */
    @ApiModelProperty(value = "响应信息", example = "请求成功", required = true, position = 3, dataType = "String",
            notes = "响应消息")
    private String message;

    /**
     * 信息
     */
    @ApiModelProperty(value = "响应信息", example = "请求成功", required = true, position = 3, dataType = "String",
            notes = "响应消息")
    private String msg;

    /**
     * 响应对象数据类型,使用注解@ResponseBody负责将对象解析为JSON字符(由Jackson/FastJson支持)
     */
    @ApiModelProperty(value = "响应对象", example = "{'name':'张三','age':'18','sex':'M'}", required = true,
            position = 4, dataType = "Object", notes = "响应消息")
    private T data;

    public ResponseData() {
    }

    public ResponseData(Boolean success, Integer code, String message, T data) {
        this.success = success;
        this.code = code;
        this.message = message;
        this.data = data;
        this.msg  = message;
    }

    public static SuccessResponseData success() {
        return new SuccessResponseData();
    }

    public static SuccessResponseData success(Object object) {
        return new SuccessResponseData(object);
    }

    public static <T> ResponseData success(String message, T data) {
        return new ResponseData(true, 200, message, data);
    }

    public static <T> ResponseData success(Boolean success, Integer code, String message, T data) {
        return new ResponseData(success, code, message, data);
    }

    public static SuccessResponseData success(String message) {
        return new SuccessResponseData(message);
    }

    public static SuccessResponseData success(Integer code, String message, Object object) {
        return new SuccessResponseData(code, message, object);
    }

    public static ErrorResponseData error(String message) {
        return new ErrorResponseData(message);
    }

    public static ErrorResponseData error(Integer code, String message) {
        return new ErrorResponseData(code, message);
    }

    public ResponseData(Boolean success, Integer code, String message, String msg, T data) {
        this.success = success;
        this.code = code;
        this.message = message;
        this.msg  = msg;
        this.data = data;
    }
}
