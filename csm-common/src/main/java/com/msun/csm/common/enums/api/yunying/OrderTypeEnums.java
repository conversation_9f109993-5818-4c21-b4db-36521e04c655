package com.msun.csm.common.enums.api.yunying;

import lombok.Getter;

/**
 * @Description: 运营平台工单类型枚举
 * @Author: MengChuAn
 * @Date: 2024/4/25
 */
@Getter
public enum OrderTypeEnums {
    //1、自研软件
    SOFTWARE(1, "自研软件工单"),
    //2、硬件
    HARDWARE(2, "硬件工单"),
    //3、耗材
    CONSUMABLE(3, "耗材工单"),
    //4、接口
    INTERFACE(4, "接口工单"),
    //5、软件服务费
    SOFTWARE_SERVICE(5, "软件服务费工单"),
    //6、硬件服务费
    HARDWARE_SERVICE(6, "硬件服务费工单"),
    //7、容灾
    DISASTER_RECOVERY(7, "容灾工单"),
    //8、外采软件
    PURCHASE_SOFTWARE(8, "外采软件工单"),
    //9、云资源
    CLOUD_RESOURCE(9, "云资源工单");


    private final Integer code;
    private final String name;

    OrderTypeEnums(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static OrderTypeEnums getEnum(int code) {
        for (OrderTypeEnums s : values()) {    //values()方法返回enum实例的数组
            if (code == s.getCode()) {
                return s;
            }
        }
        return null;
    }
}
