package com.msun.csm.common.enums.projproduct;

import lombok.Getter;

@Getter
public enum ProjProductEnum {

    LIS("LIS", "lis_device", "(云)LIS系统"),
    PACS("PACS", "pacs_device", "(云)PACS系统"),
    HD("HD", "hd_device", "(云)血液透析管理系统"),
    AIMS("AIMS", "aims_device", "(云)手术麻醉临床信息系统"),
    ECG("ECG", "ecg_device", "(云)心电网络系统");

    private final String productCode;
    private final String fileSceneCode;
    private final String productName;

    ProjProductEnum(String productCode, String fileSceneCode, String productName) {
        this.productCode = productCode;
        this.fileSceneCode = fileSceneCode;
        this.productName = productName;
    }
}
