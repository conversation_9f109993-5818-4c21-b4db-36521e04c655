package com.msun.csm.common.annotation;


import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.HashMap;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @ClassName: Log
 * @Description: 操作日志记录注解
 */
@Target ({ElementType.PARAMETER, ElementType.METHOD})
@Retention (RetentionPolicy.RUNTIME)
@Documented
public @interface Log {

    /**
     * 缓存
     */
    Map<Integer, String> OPER_TYPE_MAP = new HashMap<>();

    /**
     * 操作名称
     */
    String operName() default "";

    /**
     * 操作日志详情
     * 默认详情是根据 operType.desc + operNode.desc 拼接的内容。例如：
     *
     * @return
     * @Log (operLogType = Log.LogOperType.SEARCH, operNode = Log.LogOperNode.LOGIN, operDetail = " 客户列表查询 （ 左侧列表 ） ")
     * 上述配置将记录为：
     * 查询.用户管理.客户列表查询（ 左侧列表 ）
     */
    String operDetail() default "";

    /**
     * 操作类型, 1: 页面按钮电机, 2: 第三方接口调用
     *
     * @return
     */
    LogOperType operLogType() default LogOperType.OTHER;

    /**
     * 接口类型 1.
     *
     * @return
     */
    IntLogType intLogType() default IntLogType.OTHER;

    /**
     * 中文名称
     *
     * @return
     */
    String cnName() default "";

    /**
     * 是否保存入参
     * true, 是
     * false, 否
     *
     * @return
     */
    boolean saveParam() default false;

    /**
     * 操作类型枚举
     */
    enum LogOperType {
        NONE_MATCH(0, "其他"),
        LOGIN(1, "登录"),
        ADD(2, "新增"),

        SEARCH(3, "查询"),
        DEL(4, "删除"),
        FIX(5, "修改"),
        EXP(6, "导出"),
        DOWNLOAD(7, "下载"),
        OTHER(8, "其他"),
        UPLOAD(9, "上传"),
        APPLY(10, "申请");

        private int code;

        private String desc;

        LogOperType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        /**
         * 根据code获取描述
         *
         * @param code
         * @return
         */
        public static String getDescByCode(int code) {
            for (LogOperType operType : LogOperType.values()) {
                if (operType.getCode() == code) {
                    return operType.getDesc();
                }
            }
            return null;
        }
    }

    /**
     * 模块对应二级节点
     */
    enum OperNode {
        CUS_MG_NODE(1, "用户管理", LogOperModel.SYS_MG),
        LOGIN_NODE(2, "登录", LogOperModel.SYS_MG),
        HOME_PAGE_NODE(3, "首页", LogOperModel.SYS_MG),
        CUS_LIST_NODE(4, "客户列表", LogOperModel.CUS_MG),
        ROLE_LIST_NODE(5, "角色列表", LogOperModel.SYS_MG),
        SYNC_WORK_ORDER_LIST_NODE(6, "派工单同步", LogOperModel.OTHER),
        OTHER_NODE(0, "其他", LogOperModel.OTHER);

        private int code;
        private String desc;
        private LogOperModel logOperModel;

        OperNode(int code, String desc, LogOperModel logOperModel) {
            this.code = code;
            this.desc = desc;
            this.logOperModel = logOperModel;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public LogOperModel getLogOperModel() {
            return logOperModel;
        }

    }

    /**
     * 操作模块
     * 系统对应一级模块
     */
    enum LogOperModel {
        SYS_MG(1, "系统管理"),
        CUS_MG(2, "客户管理"),
        OTHER(0, "其他");
        private int code;

        private String desc;

        LogOperModel(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

    }

    /**
     * 操作节点
     */
    enum LogOperNode {
        CUS_MG(1, "用户管理"),
        LOGIN(2, "客户管理");
        private int code;

        private String desc;

        LogOperNode(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

    }

    enum IntLogType {
        /**
         * 企业微信消息类
         */
        COMP_WCHAT_MSG(1, "success", "企业微信消息类"),
        /**
         * 运营平台系统
         */
        OPER_SYSTEM(2, "warning", "运营平台系统"),
        /**
         * 自身系统调用
         */
        SELF_SYS(3, "primary", "自身系统调用"),
        /**
         * 其他
         */
        OTHER(4, "info", "其他");


        private int code;

        private String desc;

        private String color;

        IntLogType(int code, String color, String desc) {
            this.code = code;
            this.color = color;
            this.desc = desc;
        }

        public static IntLogType getIntLogType(int code) {
            for (int i = 0; i < IntLogType.values().length; i++) {
                IntLogType intLogType = IntLogType.values()[i];
                if (intLogType.getCode() == code) {
                    return intLogType;
                }
            }
            return null;
        }

        public int getCode() {
            return code;
        }

        public String getColor() {
            return color;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 封装的操作类型对象
     */
    @Data
    @AllArgsConstructor
    class OperType {
        int code;

        String desc;
    }
}
