package com.msun.csm.common.enums;

/**
 * @description:
 * @fileName:
 * @author:<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @updateBy:
 * @Date:Created in 17:46 2024/5/6
 * @remark:
 */
public enum MilestoneStatusEnum {
    UN_COMPLETE((short) 0, "未完成"),
    COMPLETED((short) 1, "已完成");


    Short code;
    String desc;


    MilestoneStatusEnum(Short code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Short getCode() {
        return code;
    }
}
