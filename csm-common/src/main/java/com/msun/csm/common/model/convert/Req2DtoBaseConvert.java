package com.msun.csm.common.model.convert;

import java.util.List;

import org.mapstruct.InheritConfiguration;
import org.mapstruct.InheritInverseConfiguration;

/**
 * Created with IntelliJ IDEA
 *
 * @param <Req>
 * @param <Dto>
 */
public interface Req2DtoBaseConvert<Req, Dto> {
    @InheritConfiguration
    Dto req2Dto(Req req);

    @InheritConfiguration
    List<Dto> req2Dto(List<Req> sourceList);

    @InheritInverseConfiguration
    Req dto2Req(Dto dto);

    @InheritInverseConfiguration
    List<Req> dto2Req(List<Dto> dtoList);
}
