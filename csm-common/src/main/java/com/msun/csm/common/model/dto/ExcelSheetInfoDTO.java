package com.msun.csm.common.model.dto;

import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExcelSheetInfoDTO<T> {

    /**
     * sheet页名称
     */
    private String sheetName;

    /**
     * 表头行，不需要表头时传null
     */
    private ExcelHeaderRowDTO[] headerRowArray;

    /**
     * sheet页的数据
     */
    private List<T> sheetData;


    public List<Map<String, String>> getSheetData() {
        String jsonString = JSON.toJSONString(sheetData, SerializerFeature.WriteMapNullValue);
        // 避免有泛型的时候数据类型转化失败
        TypeReference<List<Map<String, String>>> typeReference = new TypeReference<List<Map<String, String>>>() {
        };
        return JSON.parseObject(jsonString, typeReference);
    }
}
