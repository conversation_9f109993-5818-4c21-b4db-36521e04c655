package com.msun.csm.common.enums.api.yunying;

import lombok.Getter;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2023-10-26
 */
@Getter
public enum ProductSolutionEnum {

    //解决方案: 1单体医院,2区域,3医共体,4全县域医共体,5疾控,6医保,7卫健,8基层版单体医院,9市平台,10云健康升级,11嵌入式软件,12运营服务
    //1、自研软件
    DAN_TI(1, "单体医院"),
    //2、硬件
    QU_YU(2, "区域"),
    //3、耗材
    YI_GONG_TI(3, "医共体"),
    //4、接口
    QUAN_XIAN_YU(4, "全县域医共体"),
    //5、软件服务费
    JI_KONG(5, "疾控"),
    //6、硬件服务费
    YI_BAO(6, "医保"),
    //7、容灾
    WEI_JAIN(7, "卫健"),
    //8、外采软件
    JI_CENG_DAN_TI(8, "基层版单体医院"),
    //9、云资源
    SHI_PING_TAI(9, "市平台"),
    //7、容灾
    YUN_JIAN_KANG_SHENG_JI(10, "云健康升级"),
    //8、外采软件
    QIAN_RU_SHI_RUAN_JIAN(11, "嵌入式软件"),
    //9、云资源
    YUN_YING_FU_WU(12, "运营服务");


    private Integer code;
    private String name;

    ProductSolutionEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ProductSolutionEnum getEnum(Integer code) {
        for (ProductSolutionEnum s : values()) {    //values()方法返回enum实例的数组
            if (code == s.code) {
                return s;
            }
        }
        return null;
    }
}
