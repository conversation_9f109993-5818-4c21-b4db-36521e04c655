package com.msun.csm.common.enums.tduck;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IEnum;

/**
 * <AUTHOR> wangqing
 * @description : 字典枚举基础接口
 * 继承该接口会在jackson默认增强显示字段
 * @create :  2021/12/21 10:19
 * @param <T>:  T
 **/
public interface IDictEnum<T extends Serializable> extends IEnum<T> {

    static <T extends IDictEnum> T getInstance(Class<T> clazz, String code) {
        T[] constants = clazz.getEnumConstants();

        for (T t : constants) {
            if (String.valueOf(t.getValue()).equals(code)) {
                return t;
            }
        }
        return null;
    }

    /**
     * 数据库中存储的值
     *
     * @return 数据库中存储的值
     */
    @Override
    T getValue();


    /**
     * 获取枚举描述
     *
     * @return 描述
     */
    String getDesc();


}
