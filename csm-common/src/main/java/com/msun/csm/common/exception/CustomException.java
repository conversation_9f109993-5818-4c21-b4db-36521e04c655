package com.msun.csm.common.exception;


import cn.hutool.core.util.StrUtil;
import com.msun.csm.common.enums.ResultEnum;
import com.msun.csm.util.ErrUtils;

/**
 * @Author: MengChuan
 * @Date: 2018/7/12 14:11
 * @Description:
 * @Project: popjiaoyan
 */
public class CustomException extends RuntimeException {

    private static final long serialVersionUID = 1772059667557568041L;

    private Integer code;

    private String desc;

    private String errStack;

    public CustomException() {
        super();
    }

    public CustomException(String desc, Throwable ex) {
        super(desc + ":" + ex.getMessage(), ex);
        this.code = ResultEnum.FAIL.getIndex();
        this.desc = desc;
        this.errStack = ErrUtils.toSimpleString(ex);
    }

    public CustomException(String desc) {
        super(desc);
        this.code = ResultEnum.FAIL.getIndex();
        this.desc = desc;
        this.errStack = ErrUtils.toSimpleString(new Exception(desc));
    }

    public CustomException(ResultEnum resultEnum) {
        super(resultEnum.getDesc());
        this.code = resultEnum.getIndex();
        this.desc = resultEnum.getDesc();
        this.errStack = ErrUtils.toSimpleString(new Exception(resultEnum.getDesc()));
    }


    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public String toString() {
        String errMsg = "";
        if (code != null) {
            errMsg += "错误码：" + code + "\n";
        }
        if (StrUtil.isNotBlank(desc)) {
            errMsg += "错误描述：" + desc + "\n";
        }
        if (StrUtil.isNotBlank(this.getMessage())) {
            errMsg += "错误信息：" + this.getMessage() + "\n";
        }
        errMsg += "堆栈信息：" + this.errStack + "\n";
        return errMsg;
    }
}
