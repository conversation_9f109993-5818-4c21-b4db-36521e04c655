package com.msun.csm.common.enums;

/**
 * @description:
 * @fileName:
 * @author:zzy
 * @updateBy:
 * @Date:Created in 15:21 2024/4/24
 * @remark:
 */
public enum WorkStatusEnum {
    NOT_ONLINE(0, "未上线"),
    ONLINE(1, "已上线"),
    ACCEPT(2, "已验收");


    private Integer code;
    private String desc;

    WorkStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 通过code获取描述
     *
     * @param code
     * @return
     */
    public static String getDesc(Integer code) {
        for (WorkStatusEnum s : values()) {
            if (code.equals(s.code)) {
                return s.desc;
            }
        }
        return null;
    }
}
