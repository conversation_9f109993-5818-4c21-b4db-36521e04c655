package com.msun.csm.common.model.dto;

import java.util.Date;

import lombok.Builder;
import lombok.Data;

/**
 * 云资源服务时间对象
 * 应用与云资源、云容灾等有计划开始时间、时间开通时间、到期时间的计算功能
 */
@Data
@Builder
public class CloudServiceRenewalTimeDTO {

    /**
     * 本次派工的订阅期限（单位：月）
     */
    private Integer serviceSubscribeTerm;
    /**
     * 上次云资源订阅数据
     */
    private LastCloudServiceData lastCloudServiceData;

    /**
     * 上次云资源订阅数据
     */
    @Data
    @Builder
    public static class LastCloudServiceData {
        /**
         * 上一次签字时间（合同上开通时间）
         */
        private Date planStartTime;
        /**
         * 上一次实际开通时间
         */
        private Date subscribeStartTime;
        /**
         * 上次派工的订阅期限（单位：月）
         */
        private Integer serviceSubscribeTerm;
        /**
         * 上次次派工的合同类型. 合同类型 1标准合同2借货合同3新产品试用协议4客户协议5其他
         */
        private Integer contractType;
    }
}
