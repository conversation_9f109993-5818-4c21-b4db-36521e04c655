package com.msun.csm;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.msun.csm.model.req.projreport.ProjHospitalTerminalConfigReq;
import com.msun.csm.model.resp.qywechat.UrlTokenResp;
import com.msun.csm.service.message.SendMessageService;
import com.msun.csm.service.wechat.IWechatUserService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.quartz.CronExpression;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.text.ParseException;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
//@SpringBootTest(classes = CsmApplication.class)
public class BaseTest {

    @Autowired
    private IWechatUserService iWechatUserService;
    @Autowired
    private SendMessageService sendMessageService;

    @Test
    void cronTest() throws ParseException {
        String cronExpr = "0 0 * * * ?";
        Date now = new Date();
        Date timeAfter = new CronExpression(cronExpr).getTimeAfter(now);
        String nowStr = DateUtil.format(now, "yyyy-MM-dd HH:mm:ss");
        String format = DateUtil.format(timeAfter, "yyyy-MM-dd HH:mm:ss");

        log.info("now: {}, after: {}", nowStr, format);
    }

    @Test
    void testVx() {
        UrlTokenResp urlTokenResp = iWechatUserService.redirectUrl(
                StrUtil.format("{}confirmPlanOnlineTime?projectInfoId={}", "https://imsp-test.msuncloud.com/csm-front-mobile/", "488062084736569345"));
        sendMessageService.sendEnterpriseWeChatMessageForOnePeople("填报计划上线时间", StrUtil.format("请确认您的项目【{}】计划上线时间是否有变化", "黑龙江康沅专科门诊有限公司项目"), Collections.singletonList("guijie"), urlTokenResp.getUrl(), null);
    }

    @Test
    void testJson() {

        Map<String, Object> map = new HashMap<>();
        map.put("memory", "4343");
        String jsonString = JSON.toJSONString(map);

        ProjHospitalTerminalConfigReq req = JSON.parseObject(jsonString, ProjHospitalTerminalConfigReq.class);
        System.out.println(req);
    }

}
