package com.msun.csm.controller;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.msun.csm.model.req.ConfigAgentScenarioDictReq;

/**
 * AI智能体场景配置控制器测试类
 * <AUTHOR> @date 2025/07/07
 */
@SpringBootTest
@AutoConfigureTestMvc
public class ConfigAgentScenarioDictControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 测试查询所有配置
     */
    @Test
    public void testGetAllConfigs() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.post("/agentScenarioConfigDict/getAllConfigs")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200));
    }

    /**
     * 测试根据条件查询配置
     */
    @Test
    public void testGetConfigByCondition() throws Exception {
        ConfigAgentScenarioDictReq req = new ConfigAgentScenarioDictReq();
        req.setScenarioCode("IMAGE_DETECTION");
        req.setScenarioDesc("图片检测");

        mockMvc.perform(MockMvcRequestBuilders.post("/agentScenarioConfigDict/getDict")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(req)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200));
    }

    /**
     * 测试空条件查询
     */
    @Test
    public void testGetConfigWithEmptyCondition() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.post("/agentScenarioConfigDict/getDict")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200));
    }
}
