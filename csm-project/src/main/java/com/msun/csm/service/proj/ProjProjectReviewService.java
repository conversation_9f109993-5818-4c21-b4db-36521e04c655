package com.msun.csm.service.proj;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import static com.msun.csm.feign.entity.yunying.enums.OrderStepEnum.PURCHASE_SOFTWARE_TEST;
import static com.msun.csm.feign.entity.yunying.enums.OrderStepEnum.SOFTWARE_TEST;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.github.pagehelper.PageInfo;
import com.msun.core.commons.api.ResponseResult;
import com.msun.csm.common.constants.ObsExpireTimeConsts;
import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.enums.MilestoneNodeEnum;
import com.msun.csm.common.enums.MilestoneStatusEnum;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.ProjectPlanStatusEnum;
import com.msun.csm.common.enums.ResultEnum;
import com.msun.csm.common.enums.TelesalesFlagEnum;
import com.msun.csm.common.enums.message.MsgToCategory;
import com.msun.csm.common.enums.projprojectinfo.ProjectTypeEnums;
import com.msun.csm.common.enums.projreview.ProjReviewNodeEnum;
import com.msun.csm.common.enums.projreview.ProjReviewResultEnum;
import com.msun.csm.common.enums.projreview.ProjReviewTypeEnum;
import com.msun.csm.common.enums.projreview.ProjectStageCodeEnum;
import com.msun.csm.common.enums.rule.VerityWayEnum;
import com.msun.csm.common.enums.sysfile.FileUsedModeEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysFile;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.dict.DictProjectStage;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjProductDeliverRecord;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectReviewInfo;
import com.msun.csm.dao.entity.proj.ProjProjectReviewInfoRelative;
import com.msun.csm.dao.entity.proj.ProjProjectReviewLog;
import com.msun.csm.dao.entity.proj.ProjProjectReviewRecord;
import com.msun.csm.dao.entity.proj.ProjProjectReviewRecordDetail;
import com.msun.csm.dao.entity.proj.ProjSurveyFineConfig;
import com.msun.csm.dao.entity.proj.extend.ProjProjectFileExtend;
import com.msun.csm.dao.entity.rule.RuleProjectRuleConfig;
import com.msun.csm.dao.entity.rule.RuleProjectRuleDetailConfig;
import com.msun.csm.dao.mapper.dict.DictProjectStageMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOnlineStepMapper;
import com.msun.csm.dao.mapper.proj.ProjProductDeliverRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectReviewInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectReviewLogMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectReviewRecordDetailMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectReviewRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyFineConfigMapper;
import com.msun.csm.dao.mapper.rule.RuleProjectRuleConfigMapper;
import com.msun.csm.dao.mapper.rule.RuleProjectRuleDetailConfigMapper;
import com.msun.csm.dao.mapper.sysfile.SysFileMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.model.convert.ProjCustomInfoConvert;
import com.msun.csm.model.convert.projprojectreview.ProjProjectReviewInfoRelativeConvert;
import com.msun.csm.model.convert.projprojectreview.ProjProjectReviewRecordConvert;
import com.msun.csm.model.csm.PrintInfoSendMessageDTO;
import com.msun.csm.model.dto.ProjProjectInfoDTO;
import com.msun.csm.model.dto.UpdateMilestoneDTO;
import com.msun.csm.model.dto.projreview.ProjPmoReviewQueryDTO;
import com.msun.csm.model.dto.projreview.ProjReviewDTO;
import com.msun.csm.model.dto.projreview.ProjReviewExcludePmoDTO;
import com.msun.csm.model.dto.projreview.ProjReviewLogDTO;
import com.msun.csm.model.dto.projreview.ProjReviewQueryDTO;
import com.msun.csm.model.dto.projreview.ProjReviewRecordDTO;
import com.msun.csm.model.dto.projreview.ProjReviewUploadDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckSaveDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementRuleSaveDTO;
import com.msun.csm.model.param.MessageParam;
import com.msun.csm.model.req.projectfile.UploadFileReq;
import com.msun.csm.model.req.projectreview.QueryInfoReq;
import com.msun.csm.model.resp.projectreview.UserModelAllResp;
import com.msun.csm.model.resp.projectreview.UserModelResp;
import com.msun.csm.model.vo.ProjCustomInfoVO;
import com.msun.csm.model.vo.ProjMilestoneInfoVO;
import com.msun.csm.model.vo.ProjProjectInfoVO;
import com.msun.csm.model.vo.projprojectreview.ProjProjectReviewInfoRelativeVO;
import com.msun.csm.model.vo.projprojectreview.ProjProjectReviewMainVO;
import com.msun.csm.model.vo.projprojectreview.ProjProjectReviewRecordVO;
import com.msun.csm.model.vo.projprojectreview.ProjResearchReviewUploadResultVO;
import com.msun.csm.model.vo.projsettlement.ProjReviewLogVO;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.config.ProjMessageInfoService;
import com.msun.csm.service.config.projectreview.ConfigProjectReviewTypeUserService;
import com.msun.csm.service.message.SendMessageService;
import com.msun.csm.service.proj.entry.EntryExceptionDetectService;
import com.msun.csm.service.proj.entry.exception.SettlementEntryException;
import com.msun.csm.util.PageHelperUtil;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.obs.OBSClientUtils;

/**
 * 调研内容审核业务层
 *
 * <AUTHOR>
 * @since 2024-06-18 08:29:00
 */

@Slf4j
@Service
public class ProjProjectReviewService {

    @Resource
    private RuleProjectRuleConfigMapper projectRuleConfigMapper;


    @Resource
    private ProjMilestoneInfoMapper milestoneInfoMapper;

    @Resource
    private ProjProjectReviewRecordMapper reviewRecordMapper;

    @Resource
    private ProjProjectReviewRecordConvert reviewRecordConvert;

    @Resource
    private RuleProjectRuleDetailConfigMapper ruleDetailConfigMapper;

    @Resource
    private ProjProjectReviewRecordDetailMapper reviewRecordDetailMapper;

    @Resource
    private ProjProjectReviewInfoMapper reviewInfoMapper;

    @Resource
    private ProjProjectInfoMapper projectInfoMapper;

    @Resource
    private DictProjectStageMapper dictProjectStageMapper;

    @Resource
    private ProjProjectReviewLogMapper projectReviewLogMapper;

    @Resource
    private UserHelper userHelper;

    @Resource
    private SysFileMapper sysFileMapper;

    @Resource
    private ProjCustomInfoMapper customInfoMapper;

    @Resource
    private ProjOrderProductService orderProductService;

    @Resource
    private ProjProjectFileService projProjectFileService;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private ProjCustomInfoConvert customInfoConvert;

    @Resource
    private ProjProjectReviewInfoRelativeConvert reviewInfoRelativeConvert;

    @Resource
    private ProjProjectSettlementRuleService settlementRuleService;

    @Resource
    private ProjProjectSettlementCheckSaleService settlementCheckSaleService;

    @Resource
    private ProjMilestoneInfoService milestoneInfoService;

    @Resource
    private ProjProjectSettlementCheckService settlementCheckService;

    @Resource
    private ProjProjectSettlementCheckMainService mainService;

    @Resource
    private ProjProductDeliverRecordMapper productDeliverRecordMapper;

    @Resource
    private EntryExceptionDetectService entryExceptionDetectService;


    @Resource
    private ProjProjectSettlementCheckService checkService;

    @Resource
    private ProjProjectPlanService projProjectPlanService;

    @Resource
    private ProjSurveyFineConfigMapper projSurveyFineConfigMapper;

    @Resource
    private ProjOnlineStepMapper projOnlineStepMapper;
    @Resource
    private ProjSurveyReportService projSurveyReportService;
    @Autowired
    private ProjProjectInfoMapper projProjectInfoMapper;

    @Resource
    private ConfigProjectReviewTypeUserService configProjectReviewTypeUserService;
    @Resource
    private SendMessageService sendMessageService;

    @Lazy
    @Resource
    private ProjMessageInfoService messageInfoService;

    @Value("${project.current.qyWeChat-Auth-url}")
    private String weChatAuthUrl;

    @Value("${project.current.url}")
    private String platformUrl;

    /**
     * 查询调研内容, 不存在则初始化
     *
     * @param researchReviewDTO 请求参数
     * @return Result<List < ProjProjectReviewRecordVO>>
     */
    @Transactional(rollbackFor = Throwable.class)
    public Result<ProjProjectReviewMainVO> findProjReviewContent(ProjReviewQueryDTO researchReviewDTO) {
        ProjProjectReviewMainVO reviewMainVO = new ProjProjectReviewMainVO();
        String stageCode = researchReviewDTO.getProjectStageCode();
        // 查询是否有审核人数据
        QueryInfoReq dtose = new QueryInfoReq();
        dtose.setProjectInfoId(researchReviewDTO.getProjectInfoId());
        if (ProjectStageCodeEnum.STAGE_SURVEY.getCode().equals(researchReviewDTO.getProjectStageCode())) {
            dtose.setReviewTypeCode("dywcsh");
        } else if (ProjectStageCodeEnum.STAGE_ENTRY.getCode().equals(researchReviewDTO.getProjectStageCode())) {
            dtose.setReviewTypeCode("qrrzsh");
        } else if (ProjectStageCodeEnum.STAGE_PREPARAT.getCode().equals(researchReviewDTO.getProjectStageCode())) {
            dtose.setReviewTypeCode("zbgzsh");
        }
        UserModelAllResp userModelAllResp = configProjectReviewTypeUserService.findUserModel(dtose);
        List<UserModelResp> listUser = userModelAllResp.getUserModelRespList();
        // 不需要审核
        if (!userModelAllResp.getIsNeedReview()) {
            reviewMainVO.setReviewerName("(无需审核)");
        } else {
            if (listUser == null || listUser.isEmpty()) {
                reviewMainVO.setReviewerName("(暂无审核人, 请先联系交付组进行配置后进行审核)");
            } else {
                String userName = listUser.stream().map(UserModelResp::getUserName).collect(Collectors.joining(","));
                reviewMainVO.setReviewerName("(请联系" + userName + "进行审核)");
            }
        }
        List<ProjProjectReviewRecord> reviewRecords = reviewRecordMapper.selectListByRelation(researchReviewDTO);
        log.info("确认入驻排查问题日志，查询调研内容, 不存在则初始化, 项目id:{},reviewRecords={}", researchReviewDTO.getProjectInfoId(), reviewRecords);
        if (CollUtil.isNotEmpty(reviewRecords)) {
            swapRecordList(stageCode, reviewMainVO, reviewRecords, researchReviewDTO.getProjectInfoId(), researchReviewDTO.getDisplayType());
            reviewMainVO.setShowAppOpenTip(settlementCheckService.setShowAppOpenTip(researchReviewDTO.getProjectInfoId()));
            if (!userModelAllResp.getIsNeedReview()) {
                reviewMainVO.setShowUrgeFlag(false);
            }
            log.info("确认入驻排查问题日志，查询调研内容, 不存在则初始化, 项目id:{},reviewMainVO={}", researchReviewDTO.getProjectInfoId(), JSON.toJSON(reviewMainVO));
            return Result.success(reviewMainVO);
        }
        // 不存在则生成规则
        insertReviewRecord(researchReviewDTO.getProjectInfoId(), stageCode, researchReviewDTO.getSceneCode());
        reviewRecords = reviewRecordMapper.selectListByRelation(researchReviewDTO);
        swapRecordList(stageCode, reviewMainVO, reviewRecords, researchReviewDTO.getProjectInfoId(), researchReviewDTO.getDisplayType());
        reviewMainVO.setShowAppOpenTip(settlementCheckService.setShowAppOpenTip(researchReviewDTO.getProjectInfoId()));
        if (!userModelAllResp.getIsNeedReview()) {
            reviewMainVO.setShowUrgeFlag(false);
        }
        log.info("确认入驻排查问题日志，查询调研内容, 不存在则生成规则, 项目id:{},reviewMainVO={}", researchReviewDTO.getProjectInfoId(), JSON.toJSON(reviewMainVO));
        return Result.success(reviewMainVO);
    }

    /**
     * 包装返回值
     *
     * @param projectStageCode 项目阶段编码
     * @param reviewMainVO     返回值
     * @param reviewRecords    记录详情
     */
    private void swapRecordList(String projectStageCode, ProjProjectReviewMainVO reviewMainVO, List<ProjProjectReviewRecord> reviewRecords, Long projectInfoId, Integer displayType) {
        log.info("确认入驻排查问题日志，包装返回值,projectStageCode={},reviewMainVO={},reviewRecords={},projectInfoId={},displayType={}",
                projectStageCode, JSON.toJSON(reviewMainVO), JSON.toJSON(reviewRecords), projectInfoId, displayType);
        List<Integer> displayTypeList = new ArrayList<>();
        displayTypeList.add(displayType);
        displayTypeList.add(0);
        // 设置里程碑阶段
        reviewMainVO.setSceneCode(projectStageCode);
        // 查找并设置调研记录
        List<ProjProjectReviewRecordVO> reviewRecordVOS = findReviewRecordVOS(projectStageCode, reviewRecords, projectInfoId);
        // 过滤掉非非公共部分的数据
        List<ProjProjectReviewRecordVO> resutlt = reviewRecordVOS.stream().filter(e -> !e.getIsPublic().equals(NumberEnum.NO_2.num()) && displayTypeList.contains(e.getDisplayType())).collect(Collectors.toList());
        reviewMainVO.setProjectReviewRecordVOS(resutlt);
        // 设置记录
        swapRecordListImpl(projectStageCode, reviewMainVO, reviewRecordVOS, projectInfoId);
    }

    /**
     * 查找审核记录
     *
     * @param projectStageCode 项目阶段
     * @param reviewRecords    审核记录
     * @param projectInfoId    项目id
     * @return 反前端审核内容
     */
    private List<ProjProjectReviewRecordVO> findReviewRecordVOS(String projectStageCode, List<ProjProjectReviewRecord> reviewRecords, Long projectInfoId) {
        List<ProjProjectReviewRecordVO> reviewRecordVOS = reviewRecordConvert.po2Vo(reviewRecords);
        reviewRecordVOS.forEach(e -> {
            e.setNeedUploadFile(VerityWayEnum.UPLOAD.getType().equals(e.getVerityWay()) ? NumberEnum.NO_1.num() : NumberEnum.NO_0.num());
            if (e.getTemplateFlag() == NumberEnum.NO_1.num().intValue()) {
                // 上传的凭证文件链接
                if (ObjectUtil.isNotEmpty(e.getProjectFileId())) {
                    ProjProjectFile projectFile = projProjectFileService.selectByPrimaryKey(e.getProjectFileId());
                    if (ObjectUtil.isNotEmpty(projectFile)) {
                        e.setProjectFileTmpUrl(OBSClientUtils.getTemporaryUrl(projectFile.getFilePath(), ObsExpireTimeConsts.SEVEN_DAY));
                        e.setProjectFileName(projectFile.getFileName());
                    }
                }
                // 模板文件下载链接
                if (StrUtil.isNotBlank(e.getTemplateFileCode())) {
                    List<SysFile> sysFiles = sysFileMapper.selectList(new QueryWrapper<SysFile>().eq("business_code", e.getTemplateFileCode()).eq("file_code", e.getTemplateFileCode()).orderByDesc("create_time"));
                    if (CollUtil.isNotEmpty(sysFiles)) {
                        SysFile sysFile = sysFiles.get(0);
                        if (FileUsedModeEnum.FILE.getType() == sysFile.getFileUsedMode()) {
                            e.setTemplateFileTmpUrl(OBSClientUtils.getTemporaryUrl(sysFile.getFilePath(), ObsExpireTimeConsts.SEVEN_DAY));
                        } else {
                            e.setTemplateFileTmpUrl(sysFile.getFilePath());
                        }
                    }
                }
            }
            // 添加里程碑节点信息
            String milestoneNodeCode = e.getMilestoneNodeCode();
            List<ProjMilestoneInfo> milestoneInfos = milestoneInfoMapper.selectList(new QueryWrapper<ProjMilestoneInfo>().eq("project_info_id", projectInfoId).eq("project_stage_code", projectStageCode).eq("invalid_flag", NumberEnum.NO_0.num()).eq("milestone_node_code", milestoneNodeCode));
            if (CollUtil.isNotEmpty(milestoneInfos)) {
                ProjMilestoneInfo milestoneInfo = milestoneInfos.get(0);
                ProjMilestoneInfoVO milestoneInfoVO = BeanUtil.copyProperties(milestoneInfo, ProjMilestoneInfoVO.class);
                e.setProjMilestoneInfoVO(milestoneInfoVO);
            }
            if (e.getContainChildrenFlag() == NumberEnum.NO_1.num().intValue()) {
                List<ProjProjectReviewRecordDetail> details = reviewRecordDetailMapper.selectList(new QueryWrapper<ProjProjectReviewRecordDetail>().eq("project_review_record_id", e.getProjectReviewRecordId()));
                if (CollUtil.isNotEmpty(details)) {
                    StringBuilder stringBuilder = new StringBuilder();
                    int size = details.size();
                    int i = 0;
                    // 处理与产品之间的关系
                    for (ProjProjectReviewRecordDetail detail : details) {
                        i++;
                        // 根据查询若需要这个产品, 这需要
                        stringBuilder.append(detail.getChildRuleContent());
                        if (i < size) {
                            stringBuilder.append("\n");
                        }
                    }
                    e.setProjectRuleContent(stringBuilder.toString());
                }
            }
        });
        return reviewRecordVOS;
    }

    /**
     * 包装返回值
     *
     * @param projectStageCode 项目阶段编码
     * @param reviewMainVO     返回值
     * @param reviewRecordVOS  记录详情
     */
    private void swapRecordListImpl(String projectStageCode, ProjProjectReviewMainVO reviewMainVO, List<ProjProjectReviewRecordVO> reviewRecordVOS, Long projectInfoId) {
        log.info("确认入驻排查问题日志，包装返回值,projectStageCode={},reviewMainVO={},reviewRecordVOS={},projectInfoId={}",
                projectStageCode, JSON.toJSON(reviewMainVO), JSON.toJSON(reviewRecordVOS), projectInfoId);
        List<ProjProjectReviewInfo> reviewInfos = reviewInfoMapper.selectList(new QueryWrapper<ProjProjectReviewInfo>().eq("project_info_id", projectInfoId));
        log.info("确认入驻排查问题日志，包装返回值,reviewInfos={}", JSON.toJSON(reviewInfos));
        List<ProjProjectReviewRecordVO> reviewRecordAppData = reviewRecordVOS.stream().filter(e -> e.getItemCode().equals("entry_payment_instrument")).collect(Collectors.toList());
        log.info("确认入驻排查问题日志，包装返回值,reviewRecordAppData={}", JSON.toJSON(reviewRecordAppData));
        if (CollUtil.isNotEmpty(reviewRecordAppData)) {
            ProjProjectReviewRecordVO vo = reviewRecordAppData.get(0);
            // 若是首期项目未审核不能进行入驻, 非首期项目可以申请
            boolean firstProject = mainService.isFirstProject(projectInfoId);
            log.info("确认入驻排查问题日志，包装返回值,firstProject={}", JSON.toJSON(firstProject));
            if (firstProject) {
                reviewMainVO.setCanSettlement(CollUtil.isNotEmpty(reviewInfos) && ObjectUtil.isNotEmpty(reviewInfos.get(0)) && ObjectUtil.isNotEmpty(reviewInfos.get(0).getEntryReviewStatus()) && reviewInfos.get(0).getEntryReviewStatus() == NumberEnum.NO_1.num().intValue());
                log.info("确认入驻排查问题日志，包装返回值,firstProject成立，reviewMainVO={}", JSON.toJSON(reviewMainVO));
            } else {
                log.info("确认入驻排查问题日志，包装返回值,firstProject不成立");
                reviewMainVO.setCanSettlement(true);
            }
            // 是否提示开通患者智能app支付
            reviewMainVO.setShowPatientSmartNotPass(!ObjectUtil.isNotEmpty(vo.getReviewResult()) || vo.getReviewResult() != 1);
        } else {
            reviewMainVO.setCanSettlement(true);
            reviewMainVO.setShowPatientSmartNotPass(false);
            log.info("确认入驻排查问题日志，包装返回值1,reviewMainVO={}", JSON.toJSON(reviewMainVO));
        }
        // 设置页面编辑权限、审核意见
        if (CollUtil.isNotEmpty(reviewInfos) && !reviewInfos.isEmpty()) {
            ProjProjectReviewInfo reviewInfo = reviewInfos.get(0);
            Integer reviewStatus = null;
            boolean bs = false;
            if (projectStageCode.equals(ProjectStageCodeEnum.STAGE_SURVEY.getCode())) {
                reviewStatus = reviewInfo.getSurveyReviewStatus();
                reviewMainVO.setReviewOpinion(reviewInfo.getSurveyReviewOpinion());
                reviewMainVO.setSpecialInstructions(reviewInfo.getSurveyReviewSpecialInstructions());
                bs = reviewInfo != null && reviewInfo.getSurveyReviewStatus() != null && reviewInfo.getSurveyReviewStatus() == 0;
            } else if (projectStageCode.equals(ProjectStageCodeEnum.STAGE_ENTRY.getCode())) {
                reviewStatus = reviewInfo.getEntryReviewStatus();
                reviewMainVO.setReviewOpinion(reviewInfo.getEntryReviewOpinion());
                reviewMainVO.setSpecialInstructions(reviewInfo.getEntryReviewSpecialInstructions());
                bs = reviewInfo != null && reviewInfo.getEntryReviewStatus() != null && reviewInfo.getEntryReviewStatus() == 0;
            } else if (projectStageCode.equals(ProjectStageCodeEnum.STAGE_PREPARAT.getCode())) {
                reviewStatus = reviewInfo.getPreparatReviewStatus();
                reviewMainVO.setReviewOpinion(reviewInfo.getPreparatReviewOpinion());
                reviewMainVO.setSpecialInstructions(reviewInfo.getPreparatReviewSpecialInstructions());
                // 显示催办
                bs = reviewInfo != null && reviewInfo.getPreparatReviewStatus() != null && reviewInfo.getPreparatReviewStatus() == 0;
            }
            reviewMainVO.setShowUrgeFlag(bs);
            setCanEdit(projectInfoId, reviewMainVO, reviewStatus, projectStageCode);
            log.info("确认入驻排查问题日志，包装返回值2,reviewMainVO={}", JSON.toJSON(reviewMainVO));
        } else {
            // 未查询到审核信息
            reviewMainVO.setProMgrCanEdit(true);
            reviewMainVO.setPmoCanEdit(false);
            // 显示催办
            reviewMainVO.setShowUrgeFlag(false);
        }
        // 计划上线时间
        ProjProjectInfo projectInfo = projProjectInfoMapper.selectById(projectInfoId);
        reviewMainVO.setPlanOnlineTime(projectInfo.getPlanOnlineTime());
        log.info("确认入驻排查问题日志，包装返回值,reviewMainVO={}", JSON.toJSON(reviewMainVO));
    }

    /**
     * 根据状态设置编辑状态
     *
     * @param projectInfoId 项目id
     * @param reviewMainVO  主返回值
     * @param reviewStatus  审核状态
     */
    private void setCanEdit(Long projectInfoId, ProjProjectReviewMainVO reviewMainVO, Integer reviewStatus, String projectStageCode) {
        // 非首期
        if (!mainService.isFirstProject(projectInfoId)) {
            // 兼容已经提交pmo申请和未提交pmo申请的内容
            reviewMainVO.setPmoCanEdit(false);
            reviewMainVO.setProMgrCanEdit(!projectStageCode.equals(ProjectStageCodeEnum.STAGE_ENTRY.getCode()));
            limitMilestoneFinishDone(reviewMainVO, projectInfoId);
            return;
        }
        // 首期
        if (ObjectUtil.isEmpty(reviewStatus)) {
            reviewMainVO.setProMgrCanEdit(true);
            reviewMainVO.setPmoCanEdit(false);
        } else {
            ProjReviewTypeEnum resultEnum = ProjReviewTypeEnum.getEnumByCode(reviewStatus);
            assert resultEnum != null;
            if (ProjReviewTypeEnum.NO_AUDIT == resultEnum) {
                reviewMainVO.setProMgrCanEdit(false);
                reviewMainVO.setPmoCanEdit(true);
            }
            if (ProjReviewTypeEnum.REJECTED == resultEnum) {
                reviewMainVO.setProMgrCanEdit(true);
                reviewMainVO.setPmoCanEdit(false);
            }
            if (ProjReviewTypeEnum.AUDITED == resultEnum) {
                reviewMainVO.setProMgrCanEdit(false);
                reviewMainVO.setPmoCanEdit(false);
                log.info("确认入驻排查问题日志，包装返回值3,reviewMainVO={}", JSON.toJSON(reviewMainVO));
                limitMilestoneFinishDone(reviewMainVO, projectInfoId);
                log.info("确认入驻排查问题日志，包装返回值4,reviewMainVO={}", JSON.toJSON(reviewMainVO));
            }
        }
    }

    /**
     * 限制如果完成了节点, 则不能显示提交按钮
     *
     * @param reviewMainVO  主参数
     * @param projectInfoId 项目id
     */
    private void limitMilestoneFinishDone(ProjProjectReviewMainVO reviewMainVO, Long projectInfoId) {
        // 只有入驻节点判断
        String milestoneNodeCode;
        if (mainService.isOldSettlement(projectInfoId)) {
            milestoneNodeCode = MilestoneNodeEnum.PROJECT_ENTRY.getCode();
        } else {
            milestoneNodeCode = MilestoneNodeEnum.SETTLED_PMO_AUDIT.getCode();
        }
        log.info("确认入驻排查问题日志，包装返回值6,milestoneNodeCode={},reviewMainVO={}", milestoneNodeCode, JSON.toJSON(reviewMainVO));
        ProjMilestoneInfo milestoneInfo = milestoneInfoService.getMilestoneInfo(projectInfoId, milestoneNodeCode);
        log.info("确认入驻排查问题日志，包装返回值7,milestoneNodeCode={},milestoneInfo={}", milestoneNodeCode, JSON.toJSON(milestoneInfo));
        if (ObjectUtil.isNotEmpty(milestoneInfo)) {
            log.info("确认入驻排查问题日志，包装返回值9,milestoneNodeCode={},reviewMainVO={},判断={}", milestoneNodeCode, JSON.toJSON(reviewMainVO), NumberEnum.NO_1.num().intValue() != milestoneInfo.getMilestoneStatus());
            reviewMainVO.setCanSettlement(NumberEnum.NO_1.num().intValue() != milestoneInfo.getMilestoneStatus());
            log.info("确认入驻排查问题日志，包装返回值5,milestoneNodeCode={},reviewMainVO={}", milestoneNodeCode, JSON.toJSON(reviewMainVO));
        }
    }

    /**
     * 生成规则
     *
     * @param projectInfoId    项目id
     * @param projectStageCode 项目阶段编码
     */
    private void insertReviewRecord(Long projectInfoId, String projectStageCode, String sceneCode) {
        List<RuleProjectRuleConfig> ruleConfigs = projectRuleConfigMapper.selectList(new QueryWrapper<RuleProjectRuleConfig>().eq("project_stage_code", projectStageCode).eq("scene_code", sceneCode).eq("is_deleted", 0));
        if (CollUtil.isEmpty(ruleConfigs)) {
            throw new RuntimeException("未查询到生成规则. projectInfoId: " + projectInfoId + ", project_stage_code: " + projectStageCode);
        }
        // - 插入审核信息, 若有则不插入
        ProjProjectReviewInfo projectReviewInfo = addReviewInfo(projectInfoId);
        List<ProjProjectReviewRecord> reviewRecordList = new ArrayList<>();
        List<ProjProjectReviewRecordDetail> reviewRecordDetailList = new ArrayList<>();
        // - 查询projectStageId
        List<DictProjectStage> projectStages = dictProjectStageMapper.selectList(new QueryWrapper<DictProjectStage>().eq("project_stage_code", projectStageCode));
        if (CollUtil.isEmpty(projectStages)) {
            throw new RuntimeException("未查询到项目阶段id. projectStageCode: " + projectStageCode);
        }
        DictProjectStage dictProjectStage = projectStages.get(0);
        ProjProjectInfo projectInfo = mainService.getProjectInfo(projectInfoId);
        boolean isFirstProject = mainService.isFirstProject(projectInfoId);
        int upgradationType = mainService.upgradationType(projectInfoId);
        boolean isTaleCustom = mainService.isTeleSales(projectInfo.getCustomInfoId());
        int projectType = projectInfo.getProjectType();
        // 获取是否有患者智能APP产品
        Integer hzznCount = projOnlineStepMapper.hasPatient(projectInfo.getProjectInfoId(), projectInfo.getOrderInfoId(), 1);
        for (RuleProjectRuleConfig ruleConfig : ruleConfigs) {
            // 项目维度判断单体、区域、电销项目
            if (isTaleCustom) {
                if (!(ruleConfig.getTelesalesFlag() == TelesalesFlagEnum.YES.getCode())) {
                    continue;
                }
            } else if (projectType == ProjectTypeEnums.REGION.getCode()) {
                if (!(ruleConfig.getRegionFlag() == NumberEnum.NO_1.num().intValue())) {
                    continue;
                }
            } else if (projectType == ProjectTypeEnums.SINGLE.getCode()) {
                if (!(ruleConfig.getMonomerFlag() == NumberEnum.NO_1.num().intValue())) {
                    continue;
                }
            } else {
                continue;
            }
            // 判断首期
            if (!(ruleConfig.getHisFlag() == -1)) {
                int tmpFlag = isFirstProject ? NumberEnum.NO_1.num() : NumberEnum.NO_0.num();
                if (tmpFlag != ruleConfig.getHisFlag()) {
                    continue;
                }
            }
            // 判断老换新或新上线
            if (!(ruleConfig.getUpgradationType() == -1)) {
                if (upgradationType != ruleConfig.getUpgradationType()) {
                    continue;
                }
            }
            // 对于没有患者智能APP的产品，并且这个规则是患者智能的 continue 跳过
            if (ruleConfig.getItemCode().equals("entry_payment_instrument") && hzznCount == 0) {
                continue;
            }
            ProjProjectReviewRecord projectReviewRecord = new ProjProjectReviewRecord();
            BeanUtil.copyProperties(ruleConfig, projectReviewRecord);
            projectReviewRecord.setProjectReviewRecordId(SnowFlakeUtil.getId());
            projectReviewRecord.setProjectReviewInfoId(projectReviewInfo.getProjectReviewInfoId());
            projectReviewRecord.setProjectInfoId(projectInfoId);
            projectReviewRecord.setProjectStageId(dictProjectStage.getId());
            // 查询是否有明细数据, 有则生成
            if (ruleConfig.getContainChildrenFlag() == NumberEnum.NO_1.num().intValue()) {
                // 有则查询子表项
                List<RuleProjectRuleDetailConfig> ruleDetailConfigs = ruleDetailConfigMapper.selectList(new QueryWrapper<RuleProjectRuleDetailConfig>().eq("parent_item_code", ruleConfig.getItemCode()));
                if (CollUtil.isEmpty(ruleDetailConfigs)) {
                    continue;
                }
                for (RuleProjectRuleDetailConfig ruleDetailConfig : ruleDetailConfigs) {
                    ProjProjectReviewRecordDetail projectRuleDetailConfig = BeanUtil.copyProperties(ruleDetailConfig, ProjProjectReviewRecordDetail.class);
                    projectRuleDetailConfig.setProjectReviewRecordDetailId(SnowFlakeUtil.getId());
                    projectRuleDetailConfig.setProjectReviewRecordId(projectReviewRecord.getProjectReviewRecordId());
                    projectRuleDetailConfig.preInsert();
                    // 判断是否关联产品信息
                    if (ruleDetailConfig.getProductRelationFlag() == NumberEnum.NO_1.num().intValue()) {
                        if (ObjectUtil.isNotEmpty(ruleDetailConfig.getProductRelationId())) {
                            // 查询项目中是否有此产品
                            List<ProjProductDeliverRecord> deliverRecords = productDeliverRecordMapper.selectList(new QueryWrapper<ProjProductDeliverRecord>().eq("product_deliver_id", ruleDetailConfig.getProductRelationId()).eq("project_info_id", projectInfoId));
                            if (CollUtil.isNotEmpty(deliverRecords)) {
                                reviewRecordDetailList.add(projectRuleDetailConfig);
                            }
                            // todo 查询硬件产品记录, 暂且不做
                        }
                    } else {
                        reviewRecordDetailList.add(projectRuleDetailConfig);
                    }
                }
            }
            projectReviewRecord.preInsert();
            reviewRecordList.add(projectReviewRecord);
        }
        int count;
        if (CollUtil.isNotEmpty(reviewRecordList)) {
            count = reviewRecordMapper.insertBatch(reviewRecordList);
            log.info("新增项目审核内容. count: {}", count);
            if (count <= 0) {
                throw new RuntimeException("新增项目审核内容异常.");
            }
        }
        if (CollUtil.isNotEmpty(reviewRecordDetailList)) {
            count = reviewRecordDetailMapper.insertBatch(reviewRecordDetailList);
            log.info("新增项目审核内容明细. count: {}", count);
        }
    }

    /**
     * 增加审核信息
     *
     * @param projectInfoId 项目id
     * @return ProjProjectReviewInfo
     */
    private ProjProjectReviewInfo addReviewInfo(Long projectInfoId) {
        List<ProjProjectReviewInfo> reviewInfos = reviewInfoMapper.selectList(new QueryWrapper<ProjProjectReviewInfo>().eq("project_info_id", projectInfoId));
        if (CollUtil.isNotEmpty(reviewInfos)) {
            return reviewInfos.get(0);
        }
        ProjProjectReviewInfo projectReviewInfo = new ProjProjectReviewInfo();
        projectReviewInfo.setProjectReviewInfoId(SnowFlakeUtil.getId());
        projectReviewInfo.setProjectInfoId(projectInfoId);
        int count = reviewInfoMapper.insert(projectReviewInfo);
        if (count <= 0) {
            throw new RuntimeException("新增审核信息异常. projectInfoId: " + projectInfoId);
        }
        log.info("新增审核信息. count: {}", count);
        return projectReviewInfo;
    }

    /**
     * 上传文件
     *
     * @param researchViewUploadDTO 请求参数
     * @return Result<ProjResearchReviewUploadResultVO>
     */
    public Result<ProjResearchReviewUploadResultVO> uploadFile(ProjReviewUploadDTO researchViewUploadDTO) {
        // 查询里程杯节点编码
        ProjMilestoneInfo milestoneInfo = milestoneInfoMapper.selectById(researchViewUploadDTO.getMilestoneInfoId());
        UploadFileReq req = new UploadFileReq();
        req.setMilestoneCode(milestoneInfo.getMilestoneNodeCode());
        req.setProjectInfoId(researchViewUploadDTO.getProjectInfoId());
        try {
            researchViewUploadDTO.getMultipartFile().getInputStream();
            String fileName = researchViewUploadDTO.getMultipartFile().getOriginalFilename();
            String obsRelativePath = projProjectFileService.uploadFileOnly(req, researchViewUploadDTO.getMultipartFile().getInputStream(), fileName);
            ProjResearchReviewUploadResultVO uploadResultVO = new ProjResearchReviewUploadResultVO();
            uploadResultVO.setObsRelativeUrl(obsRelativePath);
            uploadResultVO.setObsTempUrl(OBSClientUtils.getTemporaryUrl(obsRelativePath, ObsExpireTimeConsts.SEVEN_DAY));
            uploadResultVO.setOriginalFileName(fileName);
            return Result.success(uploadResultVO);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 项目经理和PMO审核提交保存
     * 会根据reviewNodeType判断是项目经理或者PMO审核
     *
     * @param projReviewDTO 请求参数
     * @return Result<ProjResearchReviewUploadResultVO>
     */
    @Transactional(rollbackFor = Throwable.class)
    public Result<ProjResearchReviewUploadResultVO> saveReview(ProjReviewDTO projReviewDTO) {
        // 更新计划上线时间guijie
        if (projReviewDTO.getPlanOnlineTime() != null || StrUtil.isNotBlank(projReviewDTO.getWhyUnknownOnlineTime())) {
            new LambdaUpdateChainWrapper<>(projectInfoMapper).eq(ProjProjectInfo::getProjectInfoId, projReviewDTO.getProjectInfoId()).set(projReviewDTO.getPlanOnlineTime() != null, ProjProjectInfo::getPlanOnlineTime, projReviewDTO.getPlanOnlineTime()).set(StrUtil.isNotBlank(projReviewDTO.getWhyUnknownOnlineTime()), ProjProjectInfo::getWhyUnknownOnlineTime, projReviewDTO.getWhyUnknownOnlineTime()).update();
        }
        // 更新审核信息
        if (projReviewDTO.getReviewNodeType() == ProjReviewNodeEnum.PROJ_MANAGER.getCode()) {
            Long milestoneInfoId = projReviewDTO.getMilestoneInfoId();
            List<ProjMilestoneInfo> milestoneInfos = milestoneInfoMapper.selectList(new QueryWrapper<ProjMilestoneInfo>().eq("milestone_info_id", projReviewDTO.getMilestoneInfoId()).eq("invalid_flag", NumberEnum.NO_0.num()));
            if (CollUtil.isEmpty(milestoneInfos)) {
                throw new RuntimeException("未查询到里程碑信息. milestone_info_id: " + milestoneInfoId);
            }
            // 获取里程碑阶段编码
            ProjMilestoneInfo milestoneInfo = milestoneInfos.get(0);
            // 项目经理提交, 更新自填内容
            updateProjMgrSelfReview(projReviewDTO, milestoneInfo.getMilestoneNodeCode());
            updateReviewInfoPmoSubmit(projReviewDTO);
            // 更新Pmo关联信息
            updateReviewRelativeForProjManager(milestoneInfo, projReviewDTO);
            // 更新准备,入驻,准备状态节点等数据 （只是发消息，未发现实际有业务逻辑） TODO: 观察是否有必要存在，如无则删除。
            updateNodeInfoForProjMgr(milestoneInfo.getProjectStageCode(), projReviewDTO.getProjectInfoId());
            // 数据检测
            if (ProjectStageCodeEnum.STAGE_SURVEY.getCode().equals(milestoneInfo.getProjectStageCode())) {
                // 数据检测
                entryExceptionDetectService.entryDetectData(mainService.getProjectInfo(projReviewDTO.getProjectInfoId()), EntryExceptionDetectService.DetectNode.SURVEY_SUBMIT);
            }

        } else if (projReviewDTO.getReviewNodeType() == ProjReviewNodeEnum.PMO.getCode()) {
            updateReviewInfo(projReviewDTO);
            ProjectPlanStatusEnum status = ProjectPlanStatusEnum.FINISHED;
            if (projReviewDTO.getReviewResult() != NumberEnum.NO_1.num().intValue()) {
                status = ProjectPlanStatusEnum.UNFINISHED;
            }
            projProjectPlanService.updatePlanAndTodoTaskStatus(projReviewDTO.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_SUMMARY, status);
        }
        return Result.success();
    }

    public void editPlanOnlineTime(ProjReviewDTO projReviewDTO) {
        // 更新计划上线时间guijie
        new LambdaUpdateChainWrapper<>(projectInfoMapper)
                .eq(ProjProjectInfo::getProjectInfoId, projReviewDTO.getProjectInfoId())
                .set(projReviewDTO.getPlanOnlineTime() != null, ProjProjectInfo::getPlanOnlineTime, projReviewDTO.getPlanOnlineTime())
                .set(StrUtil.isNotBlank(projReviewDTO.getWhyUnknownOnlineTime()), ProjProjectInfo::getWhyUnknownOnlineTime, projReviewDTO.getWhyUnknownOnlineTime())
                .set(ProjProjectInfo::getLastEditPlanOnlineTimeTs, System.currentTimeMillis())
                .set(ProjProjectInfo::getUpdateTime, new Date())
                .update();
    }

    public Result<ProjProjectInfo> getProjectInfoById(Long projectInfoId) {
        ProjProjectInfo projectInfo = new LambdaQueryChainWrapper<>(projectInfoMapper).eq(ProjProjectInfo::getProjectInfoId, projectInfoId).eq(ProjProjectInfo::getIsDeleted, 0).one();
        if (projectInfo == null) {
            throw new CustomException(StrUtil.format("通过项目ID【{}】查询项目未找到", projectInfoId));
        }
        return Result.success(projectInfo);
    }

    /**
     * 更新自填内容
     *
     * @param projReviewDTO     调研内容
     * @param milestoneNodeCode 里程碑节点
     */
    private void updateProjMgrSelfReview(ProjReviewDTO projReviewDTO, String milestoneNodeCode) {
        List<ProjReviewRecordDTO> reviewRecordDTOList = projReviewDTO.getReviewRecordDTOList();
        if (reviewRecordDTOList == null || CollUtil.isEmpty(reviewRecordDTOList)) {
            return;
        }
        for (ProjReviewRecordDTO projReviewRecordDTO : reviewRecordDTOList) {
            Long projectFileId = null;
            if (StrUtil.isNotEmpty(projReviewRecordDTO.getFileUrl())) {
                UploadFileReq uploadFileReq = new UploadFileReq();
                uploadFileReq.setProjectInfoId(projReviewDTO.getProjectInfoId());
                uploadFileReq.setMilestoneCode(milestoneNodeCode);
                projectFileId = SnowFlakeUtil.getId();
                ProjProjectFileExtend projectFile = new ProjProjectFileExtend(uploadFileReq, projReviewDTO.getProjectStageCode(), projReviewRecordDTO.getFileName(), projectFileId, projReviewRecordDTO.getFileUrl(), "");
                int count = projProjectFileService.insert(projectFile);
                log.info("新增项目文件. count: {}", count);
            }
            ProjProjectReviewRecord projectReviewRecord = new ProjProjectReviewRecord();
            if (ObjectUtil.isNotEmpty(projectFileId)) {
                projectReviewRecord.setProjectFileId(projectFileId);
            }
            projectReviewRecord.setSelfReviewMemo(projReviewRecordDTO.getSelfReviewMemo());
            projectReviewRecord.setSelfReviewResult(projReviewRecordDTO.getSelfReviewResult());
            projectReviewRecord.setProjectReviewRecordId(projReviewRecordDTO.getProjectReviewRecordId());
            // 更新审核相关信息
            updateReviewRecord(projectReviewRecord);
        }
    }

    /**
     * 更新审核内容
     * <p>若是首期会更新关联表</p>
     * <p>后续模块只会更新调研内容</p>
     *
     * @param projReviewDTO 请求内容
     *                      //     * @param updateRelative 是否更新关联表数据（true, 会在首期使用, false, 会在后续模块使用）
     */
    public void updateReviewInfo(ProjReviewDTO projReviewDTO) {
        List<DictProjectStage> projectStages = dictProjectStageMapper.selectList(new QueryWrapper<DictProjectStage>().eq("project_stage_code", projReviewDTO.getProjectStageCode()));
        if (ObjectUtil.isEmpty(projectStages)) {
            throw new RuntimeException("未获取到项目阶段信息.");
        }
        List<ProjReviewRecordDTO> reviewRecordDTOList = projReviewDTO.getReviewRecordDTOList();
        if (reviewRecordDTOList == null || CollUtil.isEmpty(reviewRecordDTOList)) {
            ProjReviewQueryDTO researchReviewDTO = new ProjReviewQueryDTO();
            researchReviewDTO.setSceneCode(projReviewDTO.getSceneCode());
            researchReviewDTO.setProjectStageCode(projReviewDTO.getProjectStageCode());
            researchReviewDTO.setProjectInfoId(projReviewDTO.getProjectInfoId());
            List<ProjProjectReviewRecord> list = reviewRecordMapper.selectListByRelation(researchReviewDTO);
            if (CollUtil.isNotEmpty(list)) {
                reviewRecordDTOList = list.stream().map(e -> {
                    ProjReviewRecordDTO dto = new ProjReviewRecordDTO();
                    dto.setProjectReviewRecordId(e.getProjectReviewRecordId());
                    dto.setReviewResult(1);
                    dto.setProjectReviewRecordId(e.getProjectReviewRecordId());
                    return dto;
                }).collect(Collectors.toList());
            }
        }
        if (reviewRecordDTOList != null) {
            for (ProjReviewRecordDTO projReviewRecordDTO : reviewRecordDTOList) {
                ProjProjectReviewRecord projectReviewRecord = new ProjProjectReviewRecord();
                if (projReviewRecordDTO.getReviewResult() == null) {
                    projectReviewRecord.setReviewResult(1);
                } else {
                    projectReviewRecord.setReviewResult(projReviewRecordDTO.getReviewResult());
                }
                projectReviewRecord.setReviewMemo(projReviewRecordDTO.getOpinion());
                projectReviewRecord.setProjectReviewRecordId(projReviewRecordDTO.getProjectReviewRecordId());
                updateReviewRecord(projectReviewRecord);
            }
        }
        // pmo审核需要执行
        DictProjectStage dictProjectStage = projectStages.get(0);
        updateReviewRelativeForPMO(dictProjectStage, projReviewDTO);
        updateNodeInfoForProjPMO(projReviewDTO);
    }

    /**
     * pmo审核处理
     *
     * @param projReviewDTO 请求内容
     */
    private void updateNodeInfoForProjPMO(ProjReviewDTO projReviewDTO) {
        Long projectInfoId = projReviewDTO.getProjectInfoId();
        String projectStageCode = projReviewDTO.getProjectStageCode();
        ProjProjectInfo projectInfo = mainService.getProjectInfo(projectInfoId);
        ProjectStageCodeEnum projectStageCodeEnum = ProjectStageCodeEnum.getEnumByCode(projReviewDTO.getProjectStageCode());
        String content;
        if (projReviewDTO.getReviewResult() == NumberEnum.NO_1.num().intValue()) {
            ProjProjectReviewInfo reviewInfo = reviewInfoMapper.selectOne(new QueryWrapper<ProjProjectReviewInfo>().eq("project_info_id", projReviewDTO.getProjectInfoId()));
            // 通过
            if (projectStageCode.equals(ProjectStageCodeEnum.STAGE_SURVEY.getCode())) {
                ProjProjectSettlementRuleSaveDTO ruleSaveDTO = new ProjProjectSettlementRuleSaveDTO();
                ruleSaveDTO.setProjectInfoId(StrUtil.toString(projectInfoId));
                // 获取
                SysUserVO sysUserVO = getReviewApplyUser(reviewInfo.getSurveyApplyUserId(), reviewInfo.getLastApplyUserId());
                settlementRuleService.saveSettlement(ruleSaveDTO, sysUserVO);
                // 数据检测
                entryExceptionDetectService.entryDetectData(mainService.getProjectInfo(projReviewDTO.getProjectInfoId()), EntryExceptionDetectService.DetectNode.SURVEY_AUDITED);
                // 首期项目PMO调研阶段审核通过后给报表平台发送初始化指令
                if (projectInfo.getHisFlag() == 1) {
                    projSurveyReportService.sendStartPrintReportData(projectInfo.getCustomInfoId());
                }

            } else if (projectStageCode.equals(ProjectStageCodeEnum.STAGE_PREPARAT.getCode())) {
                SysUserVO sysUserVO = getReviewApplyUser(reviewInfo.getPreparatApplyUserId(), reviewInfo.getLastApplyUserId());
                // 更新准备阶段节点相关状态
                updatePrepareStageCompleteState(projectInfoId, sysUserVO);
                // 首期项目发送消息
                if (projectInfo.getHisFlag() == 1) {
                    settlementCheckService.sendToPmoMessage(projectInfoId, projectInfo.getProjectName() + ", " + ProjectStageCodeEnum.STAGE_PREPARAT.getName() + ", " + "完成审核, 请知晓.");
                }
                content = "已审核通过. 请知晓!";
                assert projectStageCodeEnum != null;
                settlementCheckSaleService.sendMessageToProjMgr(projReviewDTO.getProjectInfoId(), projectInfo.getProjectName() + ", " + projectStageCodeEnum.getName() + content);
            }
        } else {
            // 驳回
            content = "已被驳回. 请知晓!";
            assert projectStageCodeEnum != null;
            settlementCheckSaleService.sendMessageToProjMgr(projReviewDTO.getProjectInfoId(), projectInfo.getProjectName() + ", " + projectStageCodeEnum.getName() + content);
        }
    }

    /**
     * 获取提交审核人
     *
     * @param applySysUserID 提交审核人用户id
     * @return 用户信息
     */
    private SysUserVO getReviewApplyUser(Long applySysUserID, Long lastApplySysUserId) {
        SysUserVO sysUserVO = userHelper.getCurrentUser();
        // 若存在提交人, 则使用提交人作为完成人更新里程碑节点, 否则使用当前登录人更新
        Long sysUserId = null;
        if (ObjectUtil.isNotEmpty(applySysUserID)) {
            sysUserId = applySysUserID;
        } else if (ObjectUtil.isNotEmpty(lastApplySysUserId)) {
            sysUserId = lastApplySysUserId;
        }
        // 若传入的用户id可用,查询
        if (ObjectUtil.isNotEmpty(sysUserId)) {
            SysUser sysUser = sysUserMapper.selectOne(new QueryWrapper<SysUser>().eq("sys_user_id", sysUserId));
            if (ObjectUtil.isNotEmpty(sysUser)) {
                sysUserVO = BeanUtil.copyProperties(sysUser, SysUserVO.class);
            }
        }
        return sysUserVO;
    }

    /**
     * 更新准备阶段完成状态
     *
     * @param projectInfoId 项目id
     */
    private void updatePrepareStageCompleteState(Long projectInfoId, SysUserVO sysUserVO) {
        ProjMilestoneInfo projMilestoneInfo = milestoneInfoService.getMilestoneInfo(projectInfoId, MilestoneNodeEnum.PREPARAT_CHECK.getCode());
        updateMilestoneComplete(projMilestoneInfo, sysUserVO);
        settlementCheckService.updateYunyingNodeStatus(projectInfoId, SOFTWARE_TEST, PURCHASE_SOFTWARE_TEST, sysUserVO);
    }

    /**
     * 更新里程碑节点
     *
     * @param projMilestoneInfo 里程碑信息
     */
    private void updateMilestoneComplete(ProjMilestoneInfo projMilestoneInfo, SysUserVO sysUserVO) {
        UpdateMilestoneDTO updateMilestoneDTO = new UpdateMilestoneDTO();
        updateMilestoneDTO.setMilestoneStatus(MilestoneStatusEnum.COMPLETED.getCode().intValue());
        updateMilestoneDTO.setMilestoneInfoId(projMilestoneInfo.getMilestoneInfoId());
        milestoneInfoService.updateMilestoneImpl(updateMilestoneDTO, sysUserVO);
    }

    /**
     * 更新相关节点状态
     *
     * @param projectStageCode 项目阶段
     * @param projectInfoId    项目id
     */
    private void updateNodeInfoForProjMgr(String projectStageCode, Long projectInfoId) {
        ProjProjectInfo projectInfo = mainService.getProjectInfo(projectInfoId);
        // 首期项目发送消息
        ProjSurveyFineConfig projSurveyFineConfig = projSurveyFineConfigMapper.selectOne(new QueryWrapper<ProjSurveyFineConfig>());
        //开启了项目调研监控，发给PMO和其他人的消息内容不一样，分开发送
        if (ObjectUtil.isNotEmpty(projSurveyFineConfig) && projSurveyFineConfig.getOpenFlag() == 1 && projectInfo.getHisFlag() == 1 && projectStageCode.equals(ProjectStageCodeEnum.STAGE_SURVEY.getCode())) {
            settlementCheckService.sendToRoleMessage(projectInfoId, 4003L, projectInfo.getProjectName() + ", " + Objects.requireNonNull(ProjectStageCodeEnum.getEnumByCode(projectStageCode)).getName() + ", 项目经理提交审核申请, 请知晓.");
            //向PMO单独发送消息
            String typeName;
            Integer type = projectInfo.getUpgradationType();
            if (Integer.valueOf("1").equals(type)) {
                typeName = "老换新";
            } else {
                typeName = "新客户";
            }
            Date now = new Date();
            settlementCheckService.sendToRoleMessage(projectInfoId, 5001L, projectInfo.getProjectName() + "(" + typeName + ")项目经理提交调研阶段审核申请，请于24小时内（" + DateFormatUtils.format(now, "yyyy-MM-dd HH:MM:ss") + " 至 " + DateFormatUtils.format(DateUtil.offsetDay(new Date(), 1), "yyyy-MM-dd HH:MM:ss") + "）尽快完成审核。项目组调研截至时间为：" + DateFormatUtils.format(DateUtil.offsetDay(projectInfo.getControlTime(), projSurveyFineConfig.getCompleteDay()), "yyyy-MM-dd HH:MM:ss"));
        } else {
            settlementCheckService.sendToPmoMessage(projectInfoId, projectInfo.getProjectName() + ", " + Objects.requireNonNull(ProjectStageCodeEnum.getEnumByCode(projectStageCode)).getName() + ", 项目经理提交审核申请, 请知晓.");
        }
    }

    /**
     * 更新pmo审核装啊嚏
     *
     * @param dictProjectStage 项目阶段编码
     * @param projReviewDTO    申请信息
     */
    private void updateReviewRelativeForPMO(DictProjectStage dictProjectStage, ProjReviewDTO projReviewDTO) {
        ProjReviewResultEnum reviewResultEnum;
        String operateTitle = "";
        String operateContent = "";
        String reviewResultStr = "";
        if (projReviewDTO.getReviewResult() == NumberEnum.NO_1.num().intValue()) {
            // 通过
            reviewResultEnum = ProjReviewResultEnum.PMO_APPROVED;
            reviewResultStr = "通过";
        } else {
            // 驳回
            reviewResultEnum = ProjReviewResultEnum.PMO_REJECT;
            reviewResultStr = "驳回";
        }
        if (ProjectStageCodeEnum.STAGE_SURVEY.getCode().equals(projReviewDTO.getProjectStageCode())) {
            operateTitle += "调研审核" + reviewResultStr;
            operateContent = operateTitle;
        } else if (ProjectStageCodeEnum.STAGE_ENTRY.getCode().equals(projReviewDTO.getProjectStageCode())) {
            operateTitle += "入驻审核" + reviewResultStr;
            operateContent = operateTitle;
        } else if (ProjectStageCodeEnum.STAGE_PREPARAT.getCode().equals(projReviewDTO.getProjectStageCode())) {
            operateTitle += "准备审核" + reviewResultStr;
            operateContent = operateTitle;
        }
        if (projReviewDTO.getReviewOpinion() != null && !projReviewDTO.getReviewOpinion().isEmpty()) {
            operateContent += "，审核意见：" + projReviewDTO.getReviewOpinion();
        }
        updateReviewRelativeImplNew(dictProjectStage.getId(), dictProjectStage.getProjectStageCode(), projReviewDTO, reviewResultEnum, true, operateTitle, operateContent);
    }

    /**
     * @param projectReviewRecord 保存值
     */
    private void updateReviewRecord(ProjProjectReviewRecord projectReviewRecord) {
        int count = reviewRecordMapper.updateById(projectReviewRecord);
        log.info("更新项目审核内容. count: {}", count);
        if (count <= 0) {
            throw new RuntimeException("更新项目审核内容异常.");
        }
    }

    /**
     * 更新插入审核相关信息
     * 审核信息与审核日志
     *
     * @param milestoneInfo 里程碑信息
     * @param projReviewDTO 请求参数
     */
    private void updateReviewRelativeForProjManager(ProjMilestoneInfo milestoneInfo, ProjReviewDTO projReviewDTO) {
        // 查询审核日志是否发起过申请, 若存在则状态为重新申请
        List<ProjProjectReviewLog> reviewLogs = projectReviewLogMapper.selectList(new QueryWrapper<ProjProjectReviewLog>().eq("project_info_id", projReviewDTO.getProjectInfoId()).eq("scene_code", projReviewDTO.getSceneCode()).eq("project_stage_id", milestoneInfo.getProjectStageId()));
        ProjReviewResultEnum reviewResultEnum;
        String operateContent = "";
        String operateContentOne = "";
        if (CollUtil.isEmpty(reviewLogs)) {
            reviewResultEnum = ProjReviewResultEnum.PROJ_MGR_APPLYIED;
            operateContentOne = "已提交";
        } else {
            reviewResultEnum = ProjReviewResultEnum.PROJ_MGR_RE_APPLYIED;
            operateContentOne = "已重新提交";
        }
        // 查询是否有审核人数据
        QueryInfoReq dtose = new QueryInfoReq();
        dtose.setProjectInfoId(milestoneInfo.getProjectInfoId());
        if (ProjectStageCodeEnum.STAGE_SURVEY.getCode().equals(milestoneInfo.getProjectStageCode())) {
            dtose.setReviewTypeCode("dywcsh");
        } else if (ProjectStageCodeEnum.STAGE_ENTRY.getCode().equals(milestoneInfo.getProjectStageCode())) {
            dtose.setReviewTypeCode("qrrzsh");
        } else if (ProjectStageCodeEnum.STAGE_PREPARAT.getCode().equals(milestoneInfo.getProjectStageCode())) {
            dtose.setReviewTypeCode("zbgzsh");
        }

        /*
            1. 查询是否需要审核
            2. 审核人员
         */
        UserModelAllResp userModelAllResp = configProjectReviewTypeUserService.findUserModel(dtose);
        List<UserModelResp> listUser = userModelAllResp.getUserModelRespList();
        String userName = "";
        List<Long> userIds = new ArrayList<>();
        if (listUser == null || listUser.isEmpty()) {
            if (userModelAllResp.getIsNeedReview()) {
                userName = "暂无审核人, 请先联系交付组进行配置";
            } else {
                userName = "无需审核，自动审核完成";
            }
        } else {
            userName = listUser.stream().map(UserModelResp::getUserName).collect(Collectors.joining(","));
            userIds = listUser.stream().map(UserModelResp::getSysUserId).collect(Collectors.toList());
        }

        // 发消息的地址===https://imsp.msunhis.com/csm-front-mobile/projectReview?参数集合
        String businessUrl = platformUrl + "projectReview?projectInfoId=" + projReviewDTO.getProjectInfoId() + "&projectStageCode=" + milestoneInfo.getProjectStageCode()
                + "&milestoneInfoId=" + projReviewDTO.getMilestoneInfoId() + "&sceneCode=" + projReviewDTO.getSceneCode()
                + "&reviewNodeType=" + projReviewDTO.getReviewNodeType();
        Long msgInfoId = messageInfoService.insert(businessUrl, userHelper.getCurrentUser().getSysUserId());
        String url = weChatAuthUrl + "?state=" + msgInfoId;
        ProjProjectInfo info = projectInfoMapper.selectById(projReviewDTO.getProjectInfoId());
        String hisFlagStr = info.getHisFlag() == 1 ? "首期" : "非首期";
        operateContent = "【" + info.getProjectName() + "】" + "【" + hisFlagStr + "】";
        String operateTitle = operateContentOne;
        if (ProjectStageCodeEnum.STAGE_SURVEY.getCode().equals(projReviewDTO.getProjectStageCode())) {
            operateContent = operateContentOne + "调研审核，下一审核人";
            operateContent += userName;
            operateTitle = operateTitle + "调研审核";
        } else if (ProjectStageCodeEnum.STAGE_ENTRY.getCode().equals(projReviewDTO.getProjectStageCode())) {
            operateContent = operateContentOne + "入驻审核，下一审核人";
            operateContent += userName;
            operateContent += "，;计划上线时间:" + projReviewDTO.getPlanOnlineTime() + "请及时处理";
            operateTitle = operateTitle + "入驻审核";
        } else if (ProjectStageCodeEnum.STAGE_PREPARAT.getCode().equals(projReviewDTO.getProjectStageCode())) {
            operateContent = operateContentOne + "准备审核，下一审核人";
            operateContent += userName;
            operateTitle = operateTitle + "准备审核";
        }
        // 发送消息
        if (CollUtil.isNotEmpty(userIds)) {
            sendMessageInfo(projReviewDTO.getProjectInfoId(), userIds, operateTitle, url);
        }
        updateReviewRelativeImplNew(milestoneInfo.getProjectStageId(), milestoneInfo.getProjectStageCode(), projReviewDTO, reviewResultEnum, false, operateTitle, operateContent);
        // 无需审核时直接模拟审核通过
        if (!userModelAllResp.getIsNeedReview()) {
            log.error("无需审核");
            // 模拟审核通过
            projReviewDTO.setReviewResult(NumberEnum.NO_1.num().intValue());
            updateReviewInfo(projReviewDTO);
            ProjectPlanStatusEnum status = ProjectPlanStatusEnum.FINISHED;
            if (projReviewDTO.getReviewResult() != NumberEnum.NO_1.num().intValue()) {
                status = ProjectPlanStatusEnum.UNFINISHED;
            }
            projProjectPlanService.updatePlanAndTodoTaskStatus(projReviewDTO.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_SUMMARY, status);
        }
    }

    public void sendMessageInfo(Long projectInfoId, List<Long> sysUserIds, String title, String url) {
        ProjProjectInfo projProjectInfo = projectInfoMapper.selectById(projectInfoId);
        SysUser sysUser = sysUserMapper.selectById(projProjectInfo.getProjectLeaderId());
        String hisFlagStr = projProjectInfo.getHisFlag() == 1 ? "首期" : "非首期";
        String sb = "【" + projProjectInfo.getProjectName() + "】【" + hisFlagStr + "】" + "工单号【" + projProjectInfo.getProjectNumber() + "】" + title + ",项目经理:" + sysUser.getUserName();
        MessageParam messageParam = new MessageParam();
        messageParam.setProjectInfoId(projProjectInfo.getProjectInfoId());
        messageParam.setContent(sb);
        messageParam.setTitle(title);
        messageParam.setUrl(url);
        messageParam.setMessageTypeId(7003L);
        messageParam.setMessageToCategory(MsgToCategory.SYS.getCode());
        messageParam.setSysUserIds(sysUserIds);
        sendMessageService.sendMessage(messageParam);
    }

    /**
     * 更新插入审核相关信息
     * 审核信息与审核日志
     *
     * @param projectStageId
     * @param projectStageCode
     * @param projReviewDTO
     * @param reviewResultEnum
     * @param isPmo
     * @param operateTitle     标题
     * @param operateContent   内容
     */
    private void updateReviewRelativeImplNew(Long projectStageId, String projectStageCode, ProjReviewDTO projReviewDTO, ProjReviewResultEnum reviewResultEnum, boolean isPmo, String operateTitle, String operateContent) {
        // 插入审核日志记录
        insertReviewLogNew(reviewResultEnum, projectStageId, projReviewDTO, projReviewDTO.getProjectInfoId(), projReviewDTO.getSceneCode(), operateTitle, operateContent);
        // 更新审核信息
        ProjProjectReviewInfo reviewInfo = setReviewInfo(projectStageCode, reviewResultEnum, projReviewDTO.getReviewOpinion(), isPmo);
        int count = reviewInfoMapper.update(reviewInfo, new QueryWrapper<ProjProjectReviewInfo>().eq("project_info_id", projReviewDTO.getProjectInfoId()));
        log.info("更新项目审核信息. count: {}", count);
    }

    /**
     * 项目经理提交pmo审核的时候保存特殊说明
     *
     * @param projReviewDTO 请求参数
     */
    private void updateReviewInfoPmoSubmit(ProjReviewDTO projReviewDTO) {
        //根据项目id 获取pmo审核信息
        ProjProjectReviewInfo reviewInfo = reviewInfoMapper.selectOne(new QueryWrapper<ProjProjectReviewInfo>().eq("project_info_id", projReviewDTO.getProjectInfoId()));
        if (projReviewDTO.getProjectStageCode().equals(ProjectStageCodeEnum.STAGE_SURVEY.getCode())) {
            reviewInfo.setSurveyReviewSpecialInstructions(projReviewDTO.getSpecialInstructions());
        } else if (projReviewDTO.getProjectStageCode().equals(ProjectStageCodeEnum.STAGE_ENTRY.getCode())) {
            reviewInfo.setEntryReviewSpecialInstructions(projReviewDTO.getSpecialInstructions());
        } else if (projReviewDTO.getProjectStageCode().equals(ProjectStageCodeEnum.STAGE_PREPARAT.getCode())) {
            reviewInfo.setPreparatReviewSpecialInstructions(projReviewDTO.getSpecialInstructions());
        }
        int count = reviewInfoMapper.update(reviewInfo, new QueryWrapper<ProjProjectReviewInfo>().eq("project_info_id", projReviewDTO.getProjectInfoId()));
        log.info("pmo提交审核更新项目审核信息. count: {}", count);
    }

    /**
     * 设置审核信息
     *
     * @param projectStageCode 项目阶段
     * @param reviewResultEnum 审核状态
     * @return ProjProjectReviewInfo
     */
    private ProjProjectReviewInfo setReviewInfo(String projectStageCode, ProjReviewResultEnum reviewResultEnum, String reviewOpinion, boolean isPmo) {
        ProjProjectReviewInfo reviewInfo = new ProjProjectReviewInfo();
        Date reviewTime = new Date();
        Long reviewUserId = userHelper.getCurrentUser().getSysUserId();
        if (projectStageCode.equals(ProjectStageCodeEnum.STAGE_SURVEY.getCode())) {
            reviewInfo.setSurveyReviewStatus(reviewResultEnum.getReviewTypeEnum().getCode());
            if (isPmo) {
                reviewInfo.setSurveyReviewTime(reviewTime);
                reviewInfo.setSurveyReviewUserId(reviewUserId);
            } else {
                reviewInfo.setSurveyApplyUserId(userHelper.getCurrentUser().getSysUserId());
                reviewInfo.setSurveyApplyTime(reviewTime);
            }
            reviewInfo.setSurveyReviewOpinion(reviewOpinion);
        } else if (projectStageCode.equals(ProjectStageCodeEnum.STAGE_ENTRY.getCode())) {
            reviewInfo.setEntryReviewStatus(reviewResultEnum.getReviewTypeEnum().getCode());
            if (isPmo) {
                reviewInfo.setEntryReviewTime(reviewTime);
                reviewInfo.setEntryReviewUserId(reviewUserId);
            } else {
                reviewInfo.setEntryApplyUserId(userHelper.getCurrentUser().getSysUserId());
                reviewInfo.setEntryApplyTime(reviewTime);
            }
            reviewInfo.setEntryReviewOpinion(reviewOpinion);
        } else if (projectStageCode.equals(ProjectStageCodeEnum.STAGE_PREPARAT.getCode())) {
            reviewInfo.setPreparatReviewStatus(reviewResultEnum.getReviewTypeEnum().getCode());
            if (isPmo) {
                reviewInfo.setPreparatReviewTime(reviewTime);
                reviewInfo.setPreparatReviewUserId(reviewUserId);
            } else {
                reviewInfo.setPreparatApplyUserId(userHelper.getCurrentUser().getSysUserId());
                reviewInfo.setPreparatApplyTime(reviewTime);
            }
            reviewInfo.setPreparatReviewOpinion(reviewOpinion);
        }
        if (!isPmo) {
            reviewInfo.setLastApplyTime(reviewTime);
            reviewInfo.setLastApplyUserId(reviewUserId);
        }
        return reviewInfo;
    }

    /**
     * 插入审核日志 先保留，后期会删除
     *
     * @param reviewResultEnum 审核结果枚举
     * @param projectStageId   项目阶段id
     * @param projectInfoId    项目id
     * @param sceneCode        场景编码
     */
    public void insertReviewLogOld(ProjReviewResultEnum reviewResultEnum, Long projectStageId, ProjReviewDTO projReviewDTO, Long projectInfoId, String sceneCode) {
        ProjProjectReviewLog reviewLog = new ProjProjectReviewLog();
        reviewLog.setProjectInfoId(projectInfoId);
        reviewLog.setSceneCode(sceneCode);
        reviewLog.setProjectStageId(projectStageId);
        reviewLog.setOperateNode(StrUtil.toString(reviewResultEnum.getNodeCode()));
        reviewLog.setOperateTitle(reviewResultEnum.getDesc());
        reviewLog.setOperateContent(StrUtil.isNotBlank(projReviewDTO.getReviewOpinion()) ? projReviewDTO.getReviewOpinion() : reviewResultEnum.getDesc());
        reviewLog.setOperateTime(new Date());
        reviewLog.setOperateUserId(userHelper.getCurrentUser().getSysUserId());
        int count = projectReviewLogMapper.insert(reviewLog);
        log.info("新增审核日志. count: {}", count);
    }


    /**
     * 插入审核日志
     *
     * @param reviewResultEnum 审核结果枚举
     * @param projectStageId   项目阶段id
     * @param projectInfoId    项目id
     * @param sceneCode        场景编码
     */
    public void insertReviewLogNew(ProjReviewResultEnum reviewResultEnum, Long projectStageId, ProjReviewDTO projReviewDTO, Long projectInfoId, String sceneCode, String operateTitle, String operateContent) {
        ProjProjectReviewLog reviewLog = new ProjProjectReviewLog();
        reviewLog.setProjectInfoId(projectInfoId);
        reviewLog.setSceneCode(sceneCode);
        reviewLog.setProjectStageId(projectStageId);
        reviewLog.setOperateNode(StrUtil.toString(reviewResultEnum.getNodeCode()));
        reviewLog.setOperateTitle(operateTitle);
        reviewLog.setOperateContent(operateContent);
        reviewLog.setOperateTime(new Date());
        reviewLog.setOperateUserId(userHelper.getCurrentUser().getSysUserId());
        int count = projectReviewLogMapper.insert(reviewLog);
        log.info("新增审核日志. count: {}", count);
    }


    public Result<PageInfo<ProjProjectReviewInfoRelativeVO>> findPmoReview(ProjPmoReviewQueryDTO dto) {
        // 查询 医院信息
        return PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> {
            List<ProjProjectReviewInfoRelative> relatives = reviewInfoMapper.selectByCondition(dto);
            PageInfo<ProjProjectReviewInfoRelative> pageInfoPo = PageInfo.of(relatives);
            List<ProjProjectReviewInfoRelativeVO> relativeVOS = reviewInfoRelativeConvert.po2Vo(relatives);
            relativeVOS.forEach(e -> {
                List<String> productNames = orderProductService.getProductName(e.getProjectInfoId());
                if (CollUtil.isNotEmpty(productNames)) {
                    StringBuilder names = new StringBuilder();
                    for (String productName : productNames) {
                        names.append(productName).append(StrUtil.COMMA);
                    }
                    e.setProductName(names.toString());
                }
                // 查询客户信息
                ProjCustomInfo customInfo = customInfoMapper.selectById(Long.parseLong(e.getCustomInfoId()));
                if (ObjectUtil.isNotEmpty(customInfo)) {
                    ProjCustomInfoVO customInfoVO = customInfoConvert.po2Vo(customInfo);
                    e.setCustomInfoVO(customInfoVO);
                }
                ProjProjectInfoDTO projectInfoDTO = new ProjProjectInfoDTO();
                projectInfoDTO.setProjectInfoId(e.getProjectInfoId());
                List<ProjProjectInfoVO> list = projectInfoMapper.findProjectInfo(projectInfoDTO);
//            if (CollUtil.isNotEmpty(projectInfoDTO.getDataRange()) && (projectInfoDTO.getProjectMemberId() != null && projectInfoDTO.getProjectMemberId() > 0)) {
//                list.addAll(projectInfoMapper.findMemberProjectInfo(projectInfoDTO));
//                list.addAll(projectInfoMapper.findDataRangeProjectInfo(projectInfoDTO));
//            } else if (CollUtil.isNotEmpty(projectInfoDTO.getDataRange())) {
//                list.addAll(projectInfoMapper.findDataRangeProjectInfo(projectInfoDTO));
//            } else if (projectInfoDTO.getProjectMemberId() != null && projectInfoDTO.getProjectMemberId() > 0) {
//                list.addAll(projectInfoMapper.findMemberProjectInfo(projectInfoDTO));
//            } else {
//                list.addAll(projectInfoMapper.findMemberProjectInfo(projectInfoDTO));
//            }
//            //去重并从大到小排序
//            list = list.stream()
//                    .collect(Collectors.toMap(
//                            ProjProjectInfoVO::getProjectInfoId, // 使用 projectInfoId 作为键
//                            vo -> vo,                           // 保留当前对象作为值
//                            (existing, replacement) -> existing // 如果有重复，保留第一个
//                    )).values().stream()
//                    .sorted(Comparator.comparing(ProjProjectInfoVO::getProjectNumber).reversed())
//                    .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(list)) {
                    // fixme 查询项目优化
                    ProjProjectInfoVO projectInfoVO = list.get(0);
                    e.setProjectInfoVO(projectInfoVO);
                }
            });
            PageInfo<ProjProjectReviewInfoRelativeVO> pageInfoVO = PageInfo.of(relativeVOS);
            pageInfoVO.setList(relativeVOS);
            pageInfoVO.setTotal(pageInfoPo.getTotal());
            return Result.success(pageInfoVO);
        });
    }

    public Result<List<ProjReviewLogVO>> findReviewLogs(ProjReviewLogDTO reviewLogDTO) {
        // 查询projectStageId
        List<DictProjectStage> dictProjectStages = dictProjectStageMapper.selectList(new QueryWrapper<DictProjectStage>().eq("project_stage_code", reviewLogDTO.getProjectStageCode()));
        if (CollUtil.isEmpty(dictProjectStages)) {
            throw new RuntimeException("未查询到项目阶段信息. projectStageCode: " + reviewLogDTO.getProjectStageCode());
        }
        DictProjectStage dictProjectStage = dictProjectStages.get(0);
        List<ProjProjectReviewLog> reviewLogs = projectReviewLogMapper.selectList(new QueryWrapper<ProjProjectReviewLog>().eq("project_info_id", reviewLogDTO.getProjectInfoId()).eq("scene_code", reviewLogDTO.getSceneCode()).eq("project_stage_id", dictProjectStage.getId()).orderByAsc("create_time"));
        if (CollUtil.isEmpty(reviewLogs)) {
            return Result.success(CollUtil.newArrayList());
        }
        List<ProjReviewLogVO> logVOS = reviewLogs.stream().map(e -> {
            ProjReviewLogVO vo = new ProjReviewLogVO();
            vo.setOperateTime(e.getOperateTime());
            vo.setOperateContent(e.getOperateContent());
            vo.setOperateUserId(e.getOperateUserId());
            if (ObjectUtil.isNotEmpty(e.getOperateUserId())) {
                SysUser sysUser = sysUserMapper.getUserByIdByIsDeleted(e.getOperateUserId(), CollUtil.newArrayList(NumberEnum.NO_0.num(), NumberEnum.NO_1.num()));
                vo.setOperateUserName(sysUser.getUserName());
                vo.setOperateUserPhone(sysUser.getPhone());
            }
            vo.setProjectInfoId(e.getProjectInfoId());
            vo.setLogTitle(e.getOperateTitle());
           /* if (StrUtil.isNotBlank(e.getOperateNode())) {
                vo.setLogTitle(Objects.requireNonNull(ProjReviewResultEnum.getEnumByNodeCode(Integer.parseInt(e.getOperateNode()))).getDesc());
            }*/
            return vo;
        }).collect(Collectors.toList());
        return Result.success(logVOS);

    }

    /**
     * 保存项目经理调研内容自填结果
     *
     * @param reviewExcludePmoDTO 请求保存的内容
     * @return 结果
     */
    public Result<String> saveReviewExcludePmo(ProjReviewExcludePmoDTO reviewExcludePmoDTO) {
        ProjReviewDTO reviewDTO = BeanUtil.copyProperties(reviewExcludePmoDTO, ProjReviewDTO.class);
        ProjMilestoneInfo milestoneInfo = milestoneInfoMapper.selectOne(new QueryWrapper<ProjMilestoneInfo>().eq("milestone_info_id", reviewDTO.getMilestoneInfoId()).eq("invalid_flag", NumberEnum.NO_0.num()));
        if (ObjectUtil.isEmpty(milestoneInfo)) {
            String content = "此里程碑节点不存在, 可根据里程杯节点id查看, 参数中milestone_info_id字段. reviewExcludePmoDTO: " + reviewExcludePmoDTO;
            log.error(content);
            throw new CustomException("此里程碑节点不存在.");
        }
        // 更新调研内容
        updateProjMgrSelfReview(reviewDTO, milestoneInfo.getMilestoneNodeCode());
        // 获取里程碑节点编码和项目id
        String projectStageCode = reviewDTO.getProjectStageCode();
        Long projectInfoId = reviewDTO.getProjectInfoId();
        if (projectStageCode.equals(ProjectStageCodeEnum.STAGE_SURVEY.getCode())) {
            ProjProjectSettlementRuleSaveDTO ruleSaveDTO = new ProjProjectSettlementRuleSaveDTO();
            ruleSaveDTO.setProjectInfoId(StrUtil.toString(projectInfoId));
            settlementRuleService.saveSettlement(ruleSaveDTO);
            // 数据检测
            entryExceptionDetectService.entryDetectData(mainService.getProjectInfo(projectInfoId), EntryExceptionDetectService.DetectNode.SURVEY_PROJ_MGR_SURE);
        } else if (projectStageCode.equals(ProjectStageCodeEnum.STAGE_PREPARAT.getCode())) {
            // 更新准备阶段节点相关状态
            updatePrepareStageCompleteState(projectInfoId, userHelper.getCurrentUser());
        } else if (projectStageCode.equals(ProjectStageCodeEnum.STAGE_ENTRY.getCode())) {
            ProjProjectSettlementCheckSaveDTO saveDTO = reviewExcludePmoDTO.getSettlementSaveDTO();
            try {
                checkService.saveSettlement(saveDTO);
            } catch (Throwable e) {
                throw new SettlementEntryException(e, ResultEnum.FAIL, "入驻审核异常", Long.parseLong(saveDTO.getProjectInfoId()));
            }
        }
        projProjectPlanService.updatePlanAndTodoTaskStatus(reviewExcludePmoDTO.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_SUMMARY, ProjectPlanStatusEnum.FINISHED);
        return Result.success();
    }

    /**
     * 项目主要阶段催办
     *
     * @param projReviewDTO
     * @return
     */
    public Result<String> urgeReview(ProjReviewDTO projReviewDTO) {
        // 更新审核信息
        Long milestoneInfoId = projReviewDTO.getMilestoneInfoId();
        List<ProjMilestoneInfo> milestoneInfos = milestoneInfoMapper.selectList(new QueryWrapper<ProjMilestoneInfo>().eq("milestone_info_id", projReviewDTO.getMilestoneInfoId()).eq("invalid_flag", NumberEnum.NO_0.num()));
        if (CollUtil.isEmpty(milestoneInfos)) {
            throw new RuntimeException("未查询到里程碑信息. milestone_info_id: " + milestoneInfoId);
        }
        // 获取里程碑阶段编码
        ProjMilestoneInfo milestoneInfo = milestoneInfos.get(0);
        // 更新Pmo关联信息
        // 查询审核日志是否发起过申请, 若存在则状态为重新申请
        List<ProjProjectReviewLog> reviewLogs = projectReviewLogMapper.selectList(new QueryWrapper<ProjProjectReviewLog>().eq("project_info_id", projReviewDTO.getProjectInfoId()).eq("scene_code", projReviewDTO.getSceneCode()).eq("project_stage_id", milestoneInfo.getProjectStageId()));

        ProjReviewResultEnum reviewResultEnum;
        String operateContent = "";
        String operateContentOne = "";
        if (CollUtil.isEmpty(reviewLogs)) {
            reviewResultEnum = ProjReviewResultEnum.PROJ_MGR_APPLYIED;
            operateContentOne = "已提交";
        } else {
            reviewResultEnum = ProjReviewResultEnum.PROJ_MGR_RE_APPLYIED;
            operateContentOne = "已重新提交";
        }
        // 查询是否有审核人数据
        QueryInfoReq dtose = new QueryInfoReq();
        dtose.setProjectInfoId(milestoneInfo.getProjectInfoId());
        if (ProjectStageCodeEnum.STAGE_SURVEY.getCode().equals(milestoneInfo.getProjectStageCode())) {
            dtose.setReviewTypeCode("dywcsh");
        } else if (ProjectStageCodeEnum.STAGE_ENTRY.getCode().equals(milestoneInfo.getProjectStageCode())) {
            dtose.setReviewTypeCode("qrrzsh");
        } else if (ProjectStageCodeEnum.STAGE_PREPARAT.getCode().equals(milestoneInfo.getProjectStageCode())) {
            dtose.setReviewTypeCode("zbgzsh");
        }

        /*
            1. 查询是否需要审核
            2. 审核人员
         */
        UserModelAllResp userModelAllResp = configProjectReviewTypeUserService.findUserModel(dtose);
        List<UserModelResp> listUser = userModelAllResp.getUserModelRespList();
        List<Long> userIds = new ArrayList<>();
        String userName = "";
        if (listUser == null || listUser.isEmpty()) {
            return Result.fail("暂无审核人, 请先联系交付组进行配置");
        } else {
            userIds = listUser.stream().map(UserModelResp::getSysUserId).collect(Collectors.toList());
            userName = listUser.stream().map(UserModelResp::getUserName).collect(Collectors.joining(","));
        }
        // 发消息的地址===https://imsp.msunhis.com/csm-front-mobile/projectReview?参数集合
        String businessUrl = platformUrl + "projectReview?projectInfoId=" + projReviewDTO.getProjectInfoId() + "&projectStageCode=" + milestoneInfo.getProjectStageCode() + "&milestoneInfoId=" + projReviewDTO.getMilestoneInfoId() + "&sceneCode=" + projReviewDTO.getSceneCode() + "&reviewNodeType=" + projReviewDTO.getReviewNodeType();
        Long msgInfoId = messageInfoService.insert(businessUrl, userHelper.getCurrentUser().getSysUserId());
        ProjProjectInfo projProjectInfo = projectInfoMapper.selectById(projReviewDTO.getProjectInfoId());
        String url = weChatAuthUrl + "?state=" + msgInfoId;
        String operateTitle = operateContentOne;
        if (ProjectStageCodeEnum.STAGE_SURVEY.getCode().equals(projReviewDTO.getProjectStageCode())) {
            operateContent = operateContentOne + "调研审核，下一审核人";
            operateContent += userName;
            operateTitle = operateTitle + "调研审核";
        } else if (ProjectStageCodeEnum.STAGE_ENTRY.getCode().equals(projReviewDTO.getProjectStageCode())) {
            operateContent = operateContentOne + "入驻审核，下一审核人";
            operateContent += userName;
            if (projProjectInfo != null && projProjectInfo.getPlanOnlineTime() != null) {
                operateContent += "，;计划上线时间:" + projReviewDTO.getPlanOnlineTime() + "请及时处理";
            }
            operateTitle = operateTitle + "入驻审核";
        } else if (ProjectStageCodeEnum.STAGE_PREPARAT.getCode().equals(projReviewDTO.getProjectStageCode())) {
            operateContent = operateContentOne + "准备审核，下一审核人";
            operateContent += userName;
            operateTitle = operateTitle + "准备审核";
        }
        // 发送消息
        if (CollUtil.isNotEmpty(userIds)) {
            sendMessageInfo(projReviewDTO.getProjectInfoId(), userIds, operateTitle, url);
        }
        insertReviewLogNew(reviewResultEnum, milestoneInfo.getProjectStageId(), projReviewDTO, projReviewDTO.getProjectInfoId(), projReviewDTO.getSceneCode(), "催办", operateContent);
        return Result.success();
    }

    /**
     * 打印平台下沉接口完成/失败后进行发送消息
     *
     * @param dto
     * @return
     */
    public ResponseResult sendMessageByPrint(PrintInfoSendMessageDTO dto) {
        ResponseResult result = new ResponseResult();
        if (dto == null || dto.getHospitalId() == null) {
            result.setSuccess(false);
            result.setMessage("医院id不能为空");
            return result;
        }
        // 根据医院id查询首期项目信息
        ProjProjectInfo projProjectInfo = projectInfoMapper.selectByHospitalIdOne(dto.getHospitalId());
        if (projProjectInfo == null) {
            result.setSuccess(true);
            result.setMessage("未查询到首期项目信息,无需发送");
            return result;
        }
        String title = "";
        if (dto.getSuccess()) {
            title += "打印平台打印报表已下沉成功，请知晓!";
        } else {
            if (dto.getErrorMsg() != null && !"".equals(dto.getErrorMsg())) {
                title += "打印平台打印报表已下沉失败，失败原因:" + dto.getErrorMsg() + ",请及时排查!";
            } else {
                title += "打印平台打印报表下沉失败，原因不明，请及时排查!";
            }
        }

        // 发送人员信息  系统管理员
        List<SysUser> sysUserList = sysUserMapper.selectByRoleCode("1");
        List<Long> sysUserIds = new ArrayList<>();
        if (sysUserList != null && !sysUserList.isEmpty()) {
            for (SysUser sysUser : sysUserList) {
                if (!sysUserIds.contains(sysUser.getSysUserId())) {
                    sysUserIds.add(sysUser.getSysUserId());
                }
            }
        }

        String hisFlagStr = projProjectInfo.getHisFlag() == 1 ? "首期" : "非首期";
        String sb = "【" + projProjectInfo.getProjectName() + "】【" + hisFlagStr + "】" + "工单号【" + projProjectInfo.getProjectNumber() + "】" + title;
        MessageParam messageParam = new MessageParam();
        messageParam.setProjectInfoId(projProjectInfo.getProjectInfoId());
        messageParam.setContent(sb);
        messageParam.setTitle(title);
        messageParam.setMessageTypeId(7003L);
        messageParam.setMessageToCategory(MsgToCategory.SYS.getCode());
        messageParam.setSysUserIds(sysUserIds);
        sendMessageService.sendMessage(messageParam);

        return ResponseResult.success();
    }
}
