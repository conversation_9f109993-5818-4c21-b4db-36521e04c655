package com.msun.csm.service.proj;

import static com.msun.csm.common.enums.projproduct.ProjProductEnum.AIMS;
import static com.msun.csm.common.enums.projproduct.ProjProductEnum.LIS;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.imsp.SystemSettingApi;
import com.msun.core.component.implementation.api.imsp.dto.EquipAutoCheckDTO;
import com.msun.core.component.implementation.api.imsp.dto.EquipmentCompareWrapperResult;
import com.msun.core.component.implementation.api.imsp.dto.EquipmentStatusDto;
import com.msun.core.component.implementation.api.imsp.dto.EquipmentStatusWrapperResult;
import com.msun.core.component.implementation.api.imsp.dto.OldNewEquipmentCompareDto;
import com.msun.core.component.implementation.api.imsp.dto.ProductEquipmentDto;
import com.msun.core.component.implementation.api.imsp.dto.ProductEquipmentWrapperDto;
import com.msun.core.component.implementation.api.imsp.dto.SystemConfigDto;
import com.msun.core.component.implementation.filter.ImplHospitalDomainHolder;
import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.device.EquipStatusEnum;
import com.msun.csm.common.enums.projprojectinfo.ProjectTypeEnums;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.dao.entity.proj.ProjEquipRecord;
import com.msun.csm.dao.entity.proj.ProjEquipRecordLog;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.mapper.proj.ProjEquipRecordMapper;
import com.msun.csm.model.dto.EquipStatusUpdateDto;
import com.msun.csm.model.req.todotask.SaveOrUpdateTodoTaskParam;
import com.msun.csm.model.vo.CloudEquipVO;
import com.msun.csm.model.vo.ProjEquipRecordVO;
import com.msun.csm.service.common.CommonService;
import com.msun.csm.util.DomainMapUtil;
import com.msun.csm.util.equip.ICloudEquipUtil;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 设备调研对接工具类
 */
@Slf4j
@Service
public class ProjEquipRecordCommonService {
    @Resource
    private ProjHospitalInfoService hospitalInfoService;

    @Resource
    private CommonService commonService;


    @Resource
    private SystemSettingApi systemSettingApi;

    @Resource
    private ProjEquipRecordMapper equipRecordMapper;

    @Resource
    private ProjTodoTaskService projTodoTaskService;

    @Resource
    private ProjEquipRecordLogService projEquipRecordLogService;

    @Resource
    private ImplHospitalDomainHolder domainHolder;


    /**
     * 判断是否可以更新当前设备
     * <p>
     * 若交付当前设备状态不是以下几种状态时, 可以处理更新
     * 1. 不是开发中
     * 且
     * 2. 不是已申请
     * 且
     * 3. 不是测试通过
     * 且
     * 4. 不是已驳回
     * </p>
     *
     * @param projEquipRecord 交付当前设备信息
     * @return true: 可以更新
     */
    public boolean canUpdateEquip(ProjEquipRecord projEquipRecord, String productCode) {
        boolean back;
        // LIS、pacs 研发完成、测试失败允许更新状态
        if (StrUtil.equalsAny(productCode, LIS.getProductCode(),
                AIMS.getProductCode())) {
            ProjProjectInfo projectInfo = commonService.getProjectInfo(projEquipRecord.getProjectInfoId());
            // 未查询到项目不更新
            if (ObjectUtil.isEmpty(projectInfo)) {
                log.warn("更新设备状态时未查询到项目. productCode: {}, detail: {}", productCode,
                        JSONUtil.toJsonStr(projEquipRecord));
                return false;
            }
            if (projEquipRecord.getEquipStatus() == EquipStatusEnum.TEST_PASS.getCode().intValue()) {
                return false;
            }
            // 若项目是区域时默认可以更新, 成功或失败
            if (projectInfo.getProjectType() == ProjectTypeEnums.REGION.getCode().intValue()) {
                log.warn("更新设备状态时查询到项目为区域, 可以更新状态, 不受限制. projectInfo: {} productCode: {}, detail: {}",
                        JSONUtil.toJsonStr(projectInfo),
                        productCode,
                        JSONUtil.toJsonStr(projEquipRecord));
                return true;
            }
            back = projEquipRecord.getEquipStatus() == EquipStatusEnum.DEVELOPED.getCode().intValue()
                    || projEquipRecord.getEquipStatus() == EquipStatusEnum.TEST_FAIL.getCode().intValue();
        } else {
            // 其他设备除测试成功以外都可以更新
            back = projEquipRecord.getEquipStatus() != EquipStatusEnum.TEST_PASS.getCode().intValue();
        }
        if (!back) {
            log.warn("当前设备不符合更新条件. detail: {}", projEquipRecord);
        }
        return back;
    }

    /**
     * 转换百分比
     * <p>
     * 会在新增设备和更新设备检测状态时都会用
     * </p>
     *
     * @param dto 请求的参数, 含lis反馈的检测结果完成率
     */
    public String transferProgress(EquipAutoCheckDTO dto) {
        return transferProgress(dto.getProgress());
    }

    /**
     * 转换百分比
     * <p>
     * 会在新增设备和更新设备检测状态时都会用
     * </p>
     *
     * @param progress 请求的参数, 含lis反馈的检测结果完成率
     */
    public String transferProgress(String progress) {
        String testProgress;
        if (ObjectUtil.isNotEmpty(progress)) {
            BigDecimal bigDecimal = new BigDecimal(progress);
            DecimalFormat df = new DecimalFormat("#.##%");
            testProgress = df.format(bigDecimal);
        } else {
            testProgress = "0%";
        }
        return testProgress;
    }


    /**
     * 获取医院信息. 根据客户id和项目类型或获取已开通医院, 取第一条作为结果返回
     * <p>若未查询到或云健康医院id是空, 则抛出异常</p>
     *
     * @param customInfoId  实施地客户id
     * @param projectInfoId 项目id
     * @return 符合要求的医院信息
     */
    public List<ProjHospitalInfo> findHospitalInfos(Long customInfoId, Long projectInfoId) {
        ProjProjectInfo projectInfo = commonService.getProjectInfo(projectInfoId);
        return findHospitalInfos(customInfoId, projectInfo.getProjectType());
    }

    /**
     * 获取医院信息. 根据客户id和项目类型或获取已开通医院, 取第一条作为结果返回
     * <p>若未查询到或云健康医院id是空, 则抛出异常</p>
     *
     * @param customInfoId 实施地客户id
     * @param projectType  项目类型
     * @return 符合要求的医院信息
     */
    public List<ProjHospitalInfo> findHospitalInfos(Long customInfoId, Integer projectType) {
        if (!ObjectUtil.isAllNotEmpty(customInfoId, projectType)) {
            log.error("获取医院信息异常. customInfoId: {}, projectType: {}", customInfoId, projectType);
            throw new CustomException("获取医院信息异常.");
        }
        List<ProjHospitalInfo> hospitalInfoList =
                hospitalInfoService.findOpenHospitalInfoSimple(customInfoId, projectType);
        // 查询当前客户下的主院数据
        if (CollectionUtil.isEmpty(hospitalInfoList)) {
            log.error("请检查客户下的医院信息. customInfoId: {}, projectType: {}", customInfoId, projectType);
            throw new CustomException("请检查客户下的医院信息是否可用.");
        }
        if (ObjectUtil.isEmpty(hospitalInfoList.get(0).getCloudHospitalId())) {
            log.error("请检查客户下的医院信息. customInfoId: {}, projectType: {}", customInfoId, projectType);
            throw new CustomException("该项目未部署,禁止对照");
        }
        return hospitalInfoList;
    }

    /**
     * 获取医院信息. 根据客户id和项目类型或获取已开通医院, 取第一条作为结果返回
     * <p>若未查询到或云健康医院id是空, 则抛出异常</p>
     *
     * @param customInfoId 实施地客户id
     * @param projectType  项目类型
     * @return 符合要求的医院信息
     */
    public ProjHospitalInfo getHospitalInfo(Long customInfoId, Integer projectType) {
        List<ProjHospitalInfo> hospitalInfoList = findHospitalInfos(customInfoId, projectType);
        return hospitalInfoList.get(0);
    }

    /**
     * 获取医院信息
     * <p>
     * 在当前客户的项目类型下查询对应的医院信息
     * </p>
     *
     * @param hospitalInfoList 医院集合
     * @param hospitalId       云健康id
     * @return 匹配的医院信息
     */
    public ProjHospitalInfo getHospitalInfo(List<ProjHospitalInfo> hospitalInfoList, Long hospitalId) {
        try {
            List<ProjHospitalInfo> hospitalInfos =
                    hospitalInfoList.stream().filter(e -> e.getCloudHospitalId().longValue() == hospitalId).collect(Collectors.toList());
            return hospitalInfos.get(0);
        } catch (Throwable e) {
            throw new CustomException("医院信息异常. 云健康医院id: " + hospitalId);
        }
    }

    /**
     * 发送到云健康
     * <p>
     * 将设备信息发送到云健康
     * </p>
     *
     * @param productCode  产品code
     * @param hospitalInfo 医院
     * @param equipments   设备
     * @return 返回成功或失败
     */
    public ResponseResult<String> syncEquipment(String productCode, ProjHospitalInfo hospitalInfo,
                                                List<ProductEquipmentDto> equipments) {
        SystemConfigDto<ProductEquipmentDto> sysDto = new SystemConfigDto<>();
        sysDto.setProductCode(productCode);
        //设置hospitalId
        sysDto.setHospitalId(hospitalInfo.getCloudHospitalId());
        sysDto.setHisOrgId(hospitalInfo.getOrgId());
        sysDto.setOrgId(hospitalInfo.getOrgId());
        sysDto.setData(equipments);
        log.info("发送到云健康请求参数. {}", JSONUtil.toJsonStr(sysDto));
        //数据组装   API数据发送
        return systemSettingApi.syncEquipment(sysDto);
    }

    /**
     * 批量查询设备信息
     * <p>
     * 按照不同医院查询
     * </p>
     *
     * @param customInfoId 客户id
     * @param projectType  项目类型
     * @param productCode  产品编码
     * @return Map集合, Key为string类型,,云健康医院id
     */
    private ResponseResult<Map<Long, EquipmentCompareWrapperResult>> findEquipmentDataBatch(Long customInfoId,
                                                                                            Integer projectType,
                                                                                            String productCode) {
        // 查询已开通的医院
        List<ProjHospitalInfo> hospitalInfos = equipRecordCommonService.findHospitalInfos(customInfoId, projectType);
        ProjHospitalInfo domainHospital = hospitalInfos.get(0);
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(domainHospital);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        // 拼接参数同域名下多个医院
        List<ProductEquipmentDto> list = new ArrayList<>();
        SystemConfigDto<ProductEquipmentDto> configDto = new SystemConfigDto<>();
        configDto.setHospitalId(domainHospital.getCloudHospitalId());
        configDto.setHisOrgId(domainHospital.getOrgId());
        configDto.setOrgId(domainHospital.getOrgId());
        for (ProjHospitalInfo hospitalInfo : hospitalInfos) {
            ProductEquipmentDto productEquipmentDto = new ProductEquipmentDto();
            productEquipmentDto.setHospitalId(hospitalInfo.getCloudHospitalId());
            productEquipmentDto.setHisOrgId(hospitalInfo.getOrgId());
            productEquipmentDto.setProductCode(productCode);
            list.add(productEquipmentDto);
        }
        configDto.setData(list);
        log.info("设备自动对照调用云健康参数信息:{}", JSON.toJSONString(configDto));
        return systemSettingApi.getEquipmentDataBatch(configDto);
    }

    /**
     * 获取设备结果
     *
     * @param customInfoId    客户id
     * @param projectType     项目类型
     * @param productCode     产品编码
     * @param iCloudEquipUtil 解析用工具类, 不同设备传入自己的工具类, 但需要实现ICloudEquipUtil接口
     * @return 解析结果, 医院区分, 遇到异常会抛出
     */
    public Map<Long, List<CloudEquipVO>> findEquipmentDataBatch(Long customInfoId,
                                                                Integer projectType,
                                                                String productCode,
                                                                ICloudEquipUtil iCloudEquipUtil) {
        // 查询云健康设备
        ResponseResult<Map<Long, EquipmentCompareWrapperResult>> equipmentResult =
                equipRecordCommonService.findEquipmentDataBatch(customInfoId, projectType,
                        productCode);
        if (equipmentResult.getSuccess()) {
            Map<Long, EquipmentCompareWrapperResult> resultMap = Convert.toMap(Long.class,
                    EquipmentCompareWrapperResult.class,
                    equipmentResult.getData());

            if (MapUtil.isEmpty(resultMap)) {
                log.warn("未获取到云健康设备信息, 返回结果. {}", equipmentResult);
                return MapUtil.newHashMap();
            }
            Map<Long, List<CloudEquipVO>> listMap = MapUtil.newHashMap();
            for (Long hospitalId : resultMap.keySet()) {
                if (!listMap.containsKey(hospitalId)) {
                    listMap.put(hospitalId, CollUtil.newArrayList());
                }
                List<CloudEquipVO> cloudEquipVOS = listMap.get(hospitalId);
                List<OldNewEquipmentCompareDto> equipmentCompareDtos =
                        resultMap.get(hospitalId).getEquipmentCompareDtos();
                if (CollUtil.isNotEmpty(equipmentCompareDtos)) {
                    for (OldNewEquipmentCompareDto dto : equipmentCompareDtos) {
                        cloudEquipVOS.add(iCloudEquipUtil.resolve(dto));
                    }
                }
                listMap.put(hospitalId,
                        cloudEquipVOS.stream().distinct().collect(Collectors.toList()));
            }
            return listMap;
        } else {
            throw new CustomException("查询云健康设备信息失败 , " + equipmentResult.getMessage());
        }
    }

    /**
     * 获取对照关系, 对照云健康设备时用于回显对照关系
     *
     * @param resultMap      获取的云健康设备信息
     * @param equipRecordVOS 查询的设备信息
     * @param productName    产品名称, 如手麻, lis, pacs等
     */
    public void compareEquip(Map<Long, List<CloudEquipVO>> resultMap,
                             List<? extends ProjEquipRecordVO> equipRecordVOS, String productName) {
        if (MapUtil.isNotEmpty(resultMap)) {
            for (Long hospitalId : resultMap.keySet()) {
                // 过滤未对照的设备, 若医院信息可用, 云健康设备id为空, 且医院对等
                List<ProjEquipRecordVO> recordVsLisVOS =
                        equipRecordVOS.stream().filter(e ->
                                ObjectUtil.isNotEmpty(e.getCloudHospitalId())
                                        && e.getCloudHospitalId() == hospitalId.longValue()
                                        && ObjectUtil.isEmpty(e.getCloudEquipId())).collect(Collectors.toList());
                log.info("可匹配的设备信息. record: {}", recordVsLisVOS);
                // 判断名称是否相同
                if (CollUtil.isEmpty(recordVsLisVOS)) {
                    continue;
                }
                // 判断有哪些名称相同, 更新对照关系
                List<CloudEquipVO> equipVOS = resultMap.get(hospitalId);
                for (CloudEquipVO equipVO : equipVOS) {
                    if (StrUtil.isBlank(equipVO.getCloudEquipName())) {
                        continue;
                    }
                    for (ProjEquipRecordVO equipRecordVO : recordVsLisVOS) {
                        if (!StrUtil.equals(equipVO.getCloudEquipName(), equipRecordVO.getEquipModelName())) {
                            continue;
                        }
                        ProjEquipRecord update = new ProjEquipRecord();
                        update.setCloudEquipId(equipVO.getCloudEquipId());
                        update.setCloudEquipName(equipVO.getCloudEquipName());
                        update.setEquipRecordId(equipRecordVO.getEquipRecordId());
                        // 更新云健康设备数据信息
                        int count = equipRecordMapper.updateById(update);
                        log.info("自动更新{}设备云健康对照. count: {}. update: {}", productName, count, update);
                        // 设置为已对照
                        equipRecordVO.setCloudEquipVO(Convert.convert(CloudEquipVO.class, equipVO));
                    }
                }
            }
            // 匹配云健康设备
            for (ProjEquipRecordVO equipRecordVO : equipRecordVOS) {
                for (Long hospitalId : resultMap.keySet()) {
                    List<CloudEquipVO> cloudEquipVOS = resultMap.get(hospitalId);
                    if (CollUtil.isEmpty(cloudEquipVOS)) {
                        continue;
                    }
                    for (CloudEquipVO cloudEquipVO : cloudEquipVOS) {
                        if (ObjectUtil.isEmpty(cloudEquipVO.getCloudEquipId())) {
                            continue;
                        }
                        if (ObjectUtil.isEmpty(equipRecordVO.getCloudEquipId())) {
                            continue;
                        }
                        if (cloudEquipVO.getCloudEquipId().longValue() != equipRecordVO.getCloudEquipId()) {
                            continue;
                        }
                        equipRecordVO.setCloudEquipVO(Convert.convert(CloudEquipVO.class, cloudEquipVO));
                    }
                }
            }
        } else {
            log.warn("获取{}对照数据时, 获取云健康设备异常. result: {}", productName, resultMap);
        }
    }

    /**
     * 一键检测设备检测状态
     * <p>
     * 支持单个和批量设备检测
     * </p>
     *
     * @param productCode   产品code, 如：手麻：AIMS
     * @param hospitalInfo  医院信息
     * @param equipmentData 需要检测的设备详情
     * @return 设备检测结果
     */
    public Map<Long, EquipmentStatusWrapperResult> getEquipmentStatusBatch(String productCode,
                                                                           ProjHospitalInfo hospitalInfo,
                                                                           List<ProductEquipmentDto> equipmentData) {
        List<ProductEquipmentWrapperDto> equipmentWrapperDtos = CollUtil.newArrayList();
        Map<Long, List<ProductEquipmentDto>> equipmentDtoMap = MapUtil.newHashMap();
        // 获取设备医院对照
        for (ProductEquipmentDto equipmentDto : equipmentData) {
            if (ObjectUtil.isEmpty(equipmentDto.getHospitalId()) || ObjectUtil.isEmpty(equipmentDto.getHisOrgId())) {
                log.warn(productCode + "设备检测, 医院id或机构id为空. equipmentDto: {}", equipmentDto);
                continue;
            }
            if (!equipmentDtoMap.containsKey(equipmentDto.getHospitalId())) {
                equipmentDtoMap.put(equipmentDto.getHospitalId(), CollUtil.newArrayList());
            }
            // 复制医院
            equipmentDtoMap.get(equipmentDto.getHospitalId()).add(Convert.convert(ProductEquipmentDto.class,
                    equipmentDto));
        }
        for (Long hospitalId : equipmentDtoMap.keySet()) {
            ProductEquipmentWrapperDto equipmentWrapperDto = new ProductEquipmentWrapperDto();
            equipmentWrapperDto.setHospitalId(hospitalId);
            equipmentWrapperDto.setHisOrgId(equipmentDtoMap.get(hospitalId).get(0).getHisOrgId());
            equipmentWrapperDto.setProductEquipmentDtoList(Convert.toList(ProductEquipmentDto.class,
                    equipmentDtoMap.get(hospitalId)));
            // 加入集合
            equipmentWrapperDtos.add(equipmentWrapperDto);
        }
        SystemConfigDto<ProductEquipmentWrapperDto> configDto = new SystemConfigDto<>();
        configDto.setHospitalId(hospitalInfo.getCloudHospitalId());
        configDto.setOrgId(hospitalInfo.getOrgId());
        configDto.setHisOrgId(hospitalInfo.getOrgId());
        configDto.setProductCode(productCode);
        configDto.setData(equipmentWrapperDtos);
        try {
            //调用接口进行验证
            ResponseResult<Map<Long, EquipmentStatusWrapperResult>> equipmentStatusResult =
                    systemSettingApi.getEquipmentStatusBatch(configDto);
            log.info("设备检测返回结果:{}", equipmentStatusResult);
            if (ObjectUtil.isEmpty(equipmentStatusResult)) {
                log.error("未查询到设备检测状态. configDto: {}", configDto);
                throw new CustomException("未查询到设备检测状态.");
            }
            if (!equipmentStatusResult.isSuccess()) {
                log.error("获取设备检测状态异常. configDto: {}", configDto);
                throw new CustomException("获取设备检测状态异常. result: " + equipmentStatusResult.getMessage());
            }
            if (MapUtil.isEmpty(equipmentStatusResult.getData())) {
                log.warn("未查询到设备检测状态. result: {}", JSONUtil.toJsonStr(equipmentStatusResult));
                return MapUtil.newHashMap();
            }
            //更新数据状态-
            return Convert.toMap(Long.class,
                    EquipmentStatusWrapperResult.class,
                    equipmentStatusResult.getData());
        } catch (Throwable e) {
            log.error("设备检测接口异常. message: {}, e=", e.getMessage(), e);
            throw new CustomException(e.getMessage());
        }
    }

    /**
     * 更新设备检测状态
     * <p>
     * 处理设备一键检测返回结果
     * </p>
     *
     * @param mapData          云健康一键检测返回值
     * @param hospitalInfoList 当前检测医院集合
     * @param projectInfoId    项目id
     * @param productCode      产品编码. 如: 心电: ECG
     */
    public void updateEquipStatus(Map<Long, EquipmentStatusWrapperResult> mapData,
                                  List<ProjHospitalInfo> hospitalInfoList,
                                  Long projectInfoId,
                                  String productCode,
                                  List<ProductEquipmentDto> equipmentData) {
        EquipStatusUpdateDto updateDto = EquipStatusUpdateDto.builder()
                .mapData(mapData)
                .hospitalInfoList(hospitalInfoList)
                .projectInfoId(projectInfoId)
                .build();
        updateEquipStatus(updateDto, productCode, equipmentData);
    }

    /**
     * 更新设备检测状态
     * <p>
     * 处理设备一键检测返回结果
     * </p>
     *
     * @param updateDto   更多参数
     * @param productCode 产品编码. 如: 心电: ECG
     */
    public void updateEquipStatus(EquipStatusUpdateDto updateDto, String productCode,
                                  List<ProductEquipmentDto> equipmentData) {
        if (ObjectUtil.isEmpty(updateDto.getUpdateStatusFlag())) {
            // 默认更新
            updateDto.setUpdateStatusFlag(NumberEnum.NO_1.num());
        }
        Map<Long, EquipmentStatusWrapperResult> mapData = updateDto.getMapData();
        List<ProjHospitalInfo> hospitalInfoList = updateDto.getHospitalInfoList();
        Long projectInfoId = updateDto.getProjectInfoId();
        for (Long hospitalId : mapData.keySet()) {
            EquipmentStatusWrapperResult wrapperResult = mapData.get(hospitalId);
            // 获取匹配的医院信息, 兼容测试项目中使用相同云健康医院id的情况
            ProjHospitalInfo hospital = getHospitalInfo(hospitalInfoList, hospitalId);
            List<EquipmentStatusDto> equipmentStatusDtos = wrapperResult.getEquipmentStatusDtos();
            if (CollUtil.isEmpty(equipmentStatusDtos)) {
                equipmentStatusDtos = CollUtil.newArrayList();
            } else {
                equipmentStatusDtos =
                        equipmentStatusDtos.stream().filter(e -> ObjectUtil.isNotEmpty(e.getId())).collect(Collectors.toList());
            }
            // 更新没有反馈的设备信息
            List<ProductEquipmentDto> productFaildEquipmentDtos = getEquipmentData(equipmentData, equipmentStatusDtos,
                    hospitalId);
            List<EquipmentStatusDto> productEnableEquipmentDtos = CollUtil.newArrayList();
            if (CollUtil.isNotEmpty(equipmentStatusDtos)) {
                productEnableEquipmentDtos =
                        equipmentStatusDtos.stream().filter(e -> productFaildEquipmentDtos.stream().noneMatch(f -> e.getId().longValue() == f.getOldEquipId())).collect(Collectors.toList());
            }
            if (CollUtil.isNotEmpty(productFaildEquipmentDtos)) {
                log.warn("未查询到心电设备检测状态信息. hospitalId: {}", hospitalId);
                for (ProductEquipmentDto equipmentDto : productFaildEquipmentDtos) {
                    List<ProjEquipRecord> currentRecords = getEquipRecord(equipmentDto.getOldEquipId(), projectInfoId,
                            hospital);
                    if (CollUtil.isEmpty(currentRecords)) {
                        log.warn("未查询到当前设备. projectInfoId: {}, equipmentDto: {}, hospital: {}", projectInfoId,
                                JSONUtil.toJsonStr(equipmentDto), JSONUtil.toJsonStr(hospital));
                        continue;
                    }
                    for (ProjEquipRecord currentRecord : currentRecords) {
                        EquipStatusFailedUpdateParam failedUpdateParam = EquipStatusFailedUpdateParam.builder()
                                .currentEquipRecord(currentRecord)
                                .hospital(hospital)
                                .updateStatusFlag(updateDto.getUpdateStatusFlag())
                                .equipmentDto(equipmentDto)
                                .build();
                        int count = updateFailedWrapper(failedUpdateParam, projectInfoId, productCode);
                        log.info("更新{}设备检测状态为检测失败. count: {}, 当前设备: {}", productCode, count,
                                JSON.toJSONString(currentRecord));
                        //LIS设备完成云健康对照时会自动检测，此时updateStatusFlag=0，只保存检测明细，不更新检测状态，也不保存操作日志
                        if (updateDto.getUpdateStatusFlag() == NumberEnum.NO_1.num().intValue()) {
                            //保存设备操作日志
                            ProjEquipRecordLog projEquipRecordLog = new ProjEquipRecordLog();
                            projEquipRecordLog.setYyProductId(currentRecord.getYyProductId());
                            projEquipRecordLog.setEquipRecordBusinessId(currentRecord.getEquipRecordId());
                            projEquipRecordLog.setOperateName("设备检测");
                            projEquipRecordLog.setOperateContent("检测无反馈");
                            projEquipRecordLogService.saveLog(projEquipRecordLog);
                        }
                    }
                }
            }
            if (CollUtil.isEmpty(productEnableEquipmentDtos)) {
                continue;
            }
            // 正常更新设备检测状态
            for (EquipmentStatusDto equipmentStatusDto : productEnableEquipmentDtos) {
                // 设置更新内容
                List<ProjEquipRecord> currentRecords = getEquipRecord(equipmentStatusDto.getId(), projectInfoId,
                        hospital);
                if (CollUtil.isEmpty(currentRecords)) {
                    log.warn("未查询到当前设备. projectInfoId: {}, equipmentStatusDto: {}, hospital: {}", projectInfoId,
                            JSONUtil.toJsonStr(equipmentStatusDto), JSONUtil.toJsonStr(hospital));
                    continue;
                }
                for (ProjEquipRecord currentRecord : currentRecords) {
                    EquipStatusUpdateParam updateParam = EquipStatusUpdateParam.builder()
                            .currentEquipRecord(currentRecord)
                            .equipmentStatusDto(equipmentStatusDto)
                            .updateStatusFlag(updateDto.getUpdateStatusFlag())
                            .hospital(hospital)
                            .build();
                    int count = updateEquipStatusWrapper(updateParam, projectInfoId,
                            productCode);
                    log.info("更新设备信息. count: {}, 当前设备: {}, 云健康反馈信息: {}", count,
                            JSONUtil.toJsonStr(currentRecord),
                            JSONUtil.toJsonStr(equipmentStatusDto));
                    //保存设备操作日志
                    String operateContent = "";
                    if (equipmentStatusDto.getCode() == 1) {
                        operateContent = "检测成功";
                    } else {
                        operateContent = "检测失败，失败原因：" + (ObjectUtil.isEmpty(equipmentStatusDto.getMsg()) ? "无"
                                : equipmentStatusDto.getMsg());
                    }
                    //LIS设备完成云健康对照时会自动检测，此时updateStatusFlag=0，只保存检测明细，不更新检测状态，也不保存操作日志
                    if (updateDto.getUpdateStatusFlag() == NumberEnum.NO_1.num().intValue()) {
                        //保存操作日志
                        ProjEquipRecordLog projEquipRecordLog = new ProjEquipRecordLog();
                        projEquipRecordLog.setYyProductId(currentRecord.getYyProductId());
                        projEquipRecordLog.setEquipRecordBusinessId(currentRecord.getEquipRecordId());
                        projEquipRecordLog.setOperateName("设备检测");
                        projEquipRecordLog.setOperateContent(operateContent);
                        projEquipRecordLogService.saveLog(projEquipRecordLog);
                    }
                }
            }
            log.warn(productCode + "设备检测, 存在未反馈的设备, 更新为检测失败", JSONUtil.toJsonStr(productFaildEquipmentDtos));
        }
    }

    /**
     * 获取当前设备的检测记录
     *
     * @param cloudEquipId  云健康设备id
     * @param projectInfoId 项目id
     * @param hospital      设备所在医院
     * @return 当前设备信息
     */
    private List<ProjEquipRecord> getEquipRecord(Long cloudEquipId, Long projectInfoId,
                                                 ProjHospitalInfo hospital) {
        log.info("获取设备, cloud_equip_id: {}, hospital_info_id: {}, project_info_id: {}", cloudEquipId,
                hospital.getHospitalInfoId(), projectInfoId);
        ProjProjectInfo projectInfo = commonService.getProjectInfo(projectInfoId);
        List<ProjEquipRecord> equipRecords = equipRecordMapper.selectList(new QueryWrapper<ProjEquipRecord>().eq(
                        "cloud_equip_id",
                        cloudEquipId)
                .eq("hospital_info_id", hospital.getHospitalInfoId()).eq("project_info_id", projectInfoId)
                .ne("equip_status",
                        EquipStatusEnum.TEST_PASS.getCode())
                .eq("required_flag", NumberEnum.NO_1.num())
        );
        if (CollUtil.isEmpty(equipRecords)) {
            log.warn("主动检测更新设备时未查询到设备信息. cloudEquipId: {}, projectInfoId: {}, hospital: {}",
                    cloudEquipId,
                    projectInfoId, JSONUtil.toJsonStr(hospital));
        }
        return equipRecords;
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class EquipStatusUpdateParam {
        /**
         * currentEquipRecord 当前设备
         */
        private ProjEquipRecord currentEquipRecord;
        /**
         * equipmentStatusDto 获取的检测内容
         */
        private EquipmentStatusDto equipmentStatusDto;
        /**
         * hospital           设备所在医院
         */
        private ProjHospitalInfo hospital;

        /**
         * 是否更新状态 0：否，1：时
         */
        private Integer updateStatusFlag;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class EquipStatusFailedUpdateParam {
        /**
         * currentEquipRecord 当前设备
         */
        private ProjEquipRecord currentEquipRecord;
        /**
         * equipmentDto 获取的检测内容
         */
        private ProductEquipmentDto equipmentDto;
        /**
         * hospital           设备所在医院
         */
        private ProjHospitalInfo hospital;

        /**
         * 是否更新状态 0：否，1：时
         */
        private Integer updateStatusFlag;
    }


    /**
     * 更新检测状态
     * <p>
     * 更新前会判断设备的当前检测状态, 若不符合更新条件会返回0
     * </p>
     *
     * @param
     * @param
     * @param
     * @param projectInfoId 项目id
     * @param productCode   产品编码, 用于判断是否是lis产品, 做特殊处理
     * @return 更新条数
     */
    private int updateEquipStatusWrapper(EquipStatusUpdateParam updateParam, Long projectInfoId, String productCode) {
        if (!canUpdateEquip(updateParam.getCurrentEquipRecord(), productCode)) {
            return NumberEnum.NO_0.num();
        }
        EquipmentStatusDto equipmentStatusDto = updateParam.getEquipmentStatusDto();
        ProjHospitalInfo hospital = updateParam.getHospital();
        int count = 0;
        if (updateParam.getUpdateStatusFlag() == NumberEnum.NO_1.num().intValue()) {
            ProjEquipRecord projEquipRecord = new ProjEquipRecord();
            if (ObjectUtil.isEmpty(updateParam.getUpdateStatusFlag())) {
                updateParam.setUpdateStatusFlag(NumberEnum.NO_1.num());
            }
            if (updateParam.getUpdateStatusFlag() == NumberEnum.NO_1.num().intValue()) {
                projEquipRecord.setEquipStatus(updateParam.getEquipmentStatusDto().getCode() == 1
                        ? EquipStatusEnum.TEST_PASS.getCode() : EquipStatusEnum.TEST_FAIL.getCode());
            }
            projEquipRecord.setCheckResult(equipmentStatusDto.getMsg());
            if (LIS.getProductCode().equals(productCode)) {
                projEquipRecord.setTestProgress(equipRecordCommonService.transferProgress(equipmentStatusDto.getProgress()));
            }
            count = updateStatus(projEquipRecord, equipmentStatusDto.getId(), hospital.getHospitalInfoId(),
                    projectInfoId);
            log.info("更新{}设备检测状态. count: {}, detail: {}, updateParam: {}", productCode, count,
                    JSON.toJSONString(projEquipRecord), JSONUtil.toJsonStr(updateParam));
        } else {
            log.info("不更新状态. count: {}", count);
        }
        if (LIS.getProductCode().equals(productCode)) {
            equipRecordVsLisService.batchInsertLisCheck(equipmentStatusDto, hospital);
        }
        return count;
    }

    /**
     * 更新检测状态
     * <p>
     * 更新前会判断设备的当前检测状态, 若不符合更新条件会返回0
     * </p>
     *
     * @param failedUpdateParam 获取的检测内容等
     * @param productCode       产品编码
     * @param projectInfoId     项目id
     * @return 更新条数
     */
    private int updateFailedWrapper(EquipStatusFailedUpdateParam failedUpdateParam, Long projectInfoId,
                                    String productCode) {
        if (!canUpdateEquip(failedUpdateParam.getCurrentEquipRecord(), productCode)) {
            return NumberEnum.NO_0.num();
        }
        if (failedUpdateParam.getUpdateStatusFlag() == NumberEnum.NO_1.num().intValue()) {
            // 设置更新内容
            ProjEquipRecord projEquipRecord = new ProjEquipRecord();
            projEquipRecord.setEquipStatus(EquipStatusEnum.TEST_FAIL.getCode());
            projEquipRecord.setCheckResult("设备未反馈");
            return updateStatus(projEquipRecord, failedUpdateParam.getEquipmentDto().getOldEquipId(),
                    failedUpdateParam.getHospital().getHospitalInfoId(),
                    projectInfoId);
        } else {
            log.info("不更新测试失败状态. count: {}", 0);
            return NumberEnum.NO_0.num();
        }
    }

    @Resource
    private ProjEquipRecordCommonService equipRecordCommonService;

    @Lazy
    @Resource
    private ProjEquipRecordVsLisService equipRecordVsLisService;

    /**
     * 过滤需要更新的设备信息
     * <p>
     * 返回值为检测时未反馈设备信息, 更新为失败时会用
     * </p>
     *
     * @param equipmentData 待更新的设备
     * @return 待更新的设备
     */
    public List<ProductEquipmentDto> getEquipmentData(List<ProductEquipmentDto> equipmentData,
                                                      List<EquipmentStatusDto> equipmentStatusDtos, Long hospitalId) {
        List<ProductEquipmentDto> productEquipmentDtos =
                equipmentData.stream().filter(e -> e.getHospitalId().longValue() == hospitalId).collect(Collectors.toList());
        if (CollUtil.isEmpty(productEquipmentDtos)) {
            return CollUtil.newArrayList();
        }
        productEquipmentDtos =
                productEquipmentDtos.stream().filter(e -> equipmentStatusDtos.stream().noneMatch(f -> ObjectUtil.isNotEmpty(f.getId()) && f.getId().longValue() == e.getOldEquipId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(productEquipmentDtos)) {
            return CollUtil.newArrayList();
        }
        return productEquipmentDtos;
    }

    /**
     * 更新设备检测状态
     *
     * @param projEquipRecord 待更新设备
     * @param cloudEquipId    云健康设备id
     * @param hospitalInfoId  医院id
     * @param projectInfoId   项目id
     * @return 更新生效数据个数
     */
    public int updateStatus(ProjEquipRecord projEquipRecord, Long cloudEquipId, Long hospitalInfoId,
                            Long projectInfoId) {
        UpdateWrapper<ProjEquipRecord> wrapper = new UpdateWrapper<>();
        wrapper.eq("cloud_equip_id", cloudEquipId);
        wrapper.eq("hospital_info_id", hospitalInfoId);
        wrapper.eq("project_info_id", projectInfoId);
        return equipRecordMapper.update(projEquipRecord, wrapper);
    }

    /**
     * 更新设备对接待办状态及进度
     *
     * @param projectInfoId  项目id
     * @param hospitalInfoId 医院id
     * @param yyProductId    产品id
     * @return 是否更新成功
     */
    public void updateEquipProjectTodoTaskStatus(Long projectInfoId, Long hospitalInfoId, Long yyProductId) {
        SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
        param.setProjectInfoId(projectInfoId);
        param.setHospitalInfoId(hospitalInfoId);
        param.setYyProductId(yyProductId);
        param.setCode(DictProjectPlanItemEnum.PREPARAT_DEVICE.getPlanItemCode());
        projTodoTaskService.updateProjectTodoTaskStatus(param);
        projTodoTaskService.todoTaskTotalCountSync(projectInfoId, DictProjectPlanItemEnum.PREPARAT_DEVICE.getPlanItemCode());
        projTodoTaskService.todoTaskTotalCountSync(projectInfoId, DictProjectPlanItemEnum.SURVEY_DEVICE.getPlanItemCode());
    }

}
