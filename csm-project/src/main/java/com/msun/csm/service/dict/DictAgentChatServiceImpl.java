package com.msun.csm.service.dict;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.dict.DictAgentChat;
import com.msun.csm.dao.mapper.dict.DictAgentChatMapper;
import com.msun.csm.dao.mapper.dict.DictAgentScenarioConfigMapper;
import com.msun.csm.model.convert.DictAgentChatConvert;
import com.msun.csm.model.req.DictAgentChatReq;
import com.msun.csm.model.resp.DictAgentChatScenarioConfigResp;
import com.msun.csm.model.vo.DictAgentChatVO;
import com.msun.csm.service.common.BaseQueryService;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * AI智能体配置服务实现类
 * <AUTHOR>
 * @date 2024/10/10
 */
@Slf4j
@Service
public class DictAgentChatServiceImpl implements DictAgentChatService {

    @Resource
    private DictAgentChatMapper dictAgentChatMapper;

    @Resource
    private DictAgentScenarioConfigMapper dictAgentScenarioConfigMapper;

    @Resource
    private BaseQueryService baseQueryService;

    @Resource
    private DictAgentChatConvert dictAgentChatConvert;

    @Value("${spring.profiles.active:dev}")
    private String activeProfiles;

    /**
     * 查询所有AI检测图片配置
     * @return 配置列表
     */
    @Override
    public Result<List<DictAgentChatVO>> getAllAiImageDetectionConfigs() {
        try {
            log.info("开始查询所有AI检测图片配置");

            // 查询所有未删除的智能体配置
            LambdaQueryWrapper<DictAgentChat> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DictAgentChat::getIsDeleted, 0);
            queryWrapper.orderByAsc(DictAgentChat::getAgentCode);

            List<DictAgentChat> configList = dictAgentChatMapper.selectList(queryWrapper);

            if (CollectionUtil.isEmpty(configList)) {
                log.warn("未查询到AI检测图片配置数据");
                return Result.success(null, "暂无配置数据");
            }

            // 转换为VO对象
            List<DictAgentChatVO> voList = dictAgentChatConvert.entityListToVOList(configList);

            log.info("成功查询到{}条AI检测图片配置", configList.size());
            return Result.success(voList, "查询成功");

        } catch (Exception e) {
            log.error("查询AI检测图片配置失败", e);
            return Result.fail("查询配置失败：" + e.getMessage());
        }
    }

    /**
     * 根据场景编码查询配置
     * @param dictAgentChatReq 请求参数
     * @return 配置信息
     */
    @Override
    public Result<DictAgentChatScenarioConfigResp> getConfigByScenario(DictAgentChatReq dictAgentChatReq) {
        try {
            log.info("根据场景编码查询配置，scenarioCode: {}", dictAgentChatReq.getScenarioCode());
            
            if (dictAgentChatReq.getScenarioCode() == null || dictAgentChatReq.getScenarioCode().trim().isEmpty()) {
                return Result.fail("场景编码不能为空");
            }
            
            DictAgentChatScenarioConfigResp config = dictAgentScenarioConfigMapper.selectByParam(dictAgentChatReq);
            
            if (config == null) {
                log.warn("未找到场景编码为{}的配置", dictAgentChatReq.getScenarioCode());
                return Result.fail("未找到对应的配置信息");
            }
            
            log.info("成功查询到场景配置，agentName: {}", config.getAgentName());
            return Result.success(config, "查询成功");
            
        } catch (Exception e) {
            log.error("根据场景编码查询配置失败", e);
            return Result.fail("查询配置失败：" + e.getMessage());
        }
    }

    /**
     * 发送图片检测消息
     * @param dictAgentChatReq 请求参数
     * @return 检测结果
     */
    @Override
    public Result sendChartMessage(DictAgentChatReq dictAgentChatReq) {
        try {
            log.info("发送图片检测消息，scenarioCode: {}", dictAgentChatReq.getScenarioCode());
            
            // 参数校验
            if (dictAgentChatReq.getScenarioCode() == null || dictAgentChatReq.getScenarioCode().trim().isEmpty()) {
                return Result.fail("场景编码不能为空");
            }
            
            if (CollectionUtil.isEmpty(dictAgentChatReq.getFileUrls())) {
                return Result.fail("请上传图片");
            }
            
            // 调用基础服务的方法
            return baseQueryService.sendChartMessage(dictAgentChatReq);
            
        } catch (Exception e) {
            log.error("发送图片检测消息失败", e);
            return Result.fail("发送消息失败：" + e.getMessage());
        }
    }
}
