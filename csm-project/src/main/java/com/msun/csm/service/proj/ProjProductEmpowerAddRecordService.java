package com.msun.csm.service.proj;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProductInfo;
import com.msun.csm.model.dto.empower.EmpowerRecord;
import com.msun.csm.model.dto.empower.ProductEmpowerAddRecordProductDto;
import com.msun.csm.model.dto.empower.ProjProductEmpowerAddRecordDto;
import com.msun.csm.model.dto.empower.ProjProductEmpowerAddRecordSaveDto;
import com.msun.csm.model.vo.dict.DictValue;
import com.msun.csm.model.vo.productempower.ProjProductEmpowerAddRecordVO;

/**
 * <AUTHOR>
 * @since 2024-09-27 10:09:59
 */

public interface ProjProductEmpowerAddRecordService {

    /**
     * 查询客户信息（所有）
     *
     * @return List<DictValue>
     */
    List<DictValue> findCustomInfo();

    /**
     * 查询产品授权记录
     *
     * @param dto 请求查询参数
     * @return Result<PageInfo < ProjProductEmpowerAddRecordVO>>
     */
    Result<PageInfo<ProjProductEmpowerAddRecordVO>> findProductEmpowerAddRecord(ProjProductEmpowerAddRecordDto dto);

    /**
     * 新增产品授权
     *
     * @param dto 请求参数
     * @return Result<String>
     */
    Result<List<EmpowerRecord>> save(ProjProductEmpowerAddRecordSaveDto dto);

    /**
     * 查询产品根据实施类型和客户id
     *
     * @param implementationType 实施类型 1. 单体, 2. 区域
     * @param customInfoId       客户id
     * @return List<ProductInfo>
     */
    List<ProductInfo> findProduct(Integer implementationType, Long customInfoId);

    /**
     * 获取需要授权的产品
     *
     * @param dto 请求参数
     * @return Result<List < DictValue>>
     */
    Result<List<DictValue>> findProductNeedEmpower(ProductEmpowerAddRecordProductDto dto);

    /**
     * 查询授权字典表
     *
     * @return Result<List < DictProductVsEmpowerVO>>
     */
    Result<List<DictValue>> findProductModuleNeedEmpower();

    /**
     * 取消授权, 定时任务使用
     */
    void cancelAuth();
}
