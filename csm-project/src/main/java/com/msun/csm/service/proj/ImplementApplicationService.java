package com.msun.csm.service.proj;

import java.util.List;

import com.msun.csm.dao.entity.proj.*;
import com.msun.csm.model.param.DeleteCommonDeductionParam;
import com.msun.csm.model.param.DeleteProcessDeductionParam;
import com.msun.csm.model.param.InitImplementApplicationPageParamVO;
import com.msun.csm.model.param.InitSatisfactionSurveyParamVO;
import com.msun.csm.model.param.QueryDeductionTypeParam;
import com.msun.csm.model.param.QueryDocumentScoreRecordParam;
import com.msun.csm.model.param.QueryProcessScoreRecordParam;
import com.msun.csm.model.param.QuerySatisfactionSurveyScoreRecordParam;
import com.msun.csm.model.param.SaveCommonDeductionParam;
import com.msun.csm.model.param.SaveDocumentDeductionParam;
import com.msun.csm.model.param.SaveProcessDeductionParam;
import com.msun.csm.model.param.SaveSatisfactionDeductionParam;

public interface ImplementApplicationService {

    /**
     * 初始化快速实施应用得分明细
     */
    InitImplementApplicationResultVO initImplementApplicationPage(InitImplementApplicationPageParamVO param);

    /**
     * 查询快速实施应用的项目文档扣分明细
     */
    List<DocumentScoreRecordVO> queryDocumentScoreRecord(QueryDocumentScoreRecordParam param);

    /**
     * 修改快速实施应用项目文档的扣分和扣分说明
     */
    ProjDeductionDetailDocument saveDocumentDeduction(SaveDocumentDeductionParam param);

    /**
     * 查询快速实施应用的项目过程扣分明细
     */
    List<ProcessScoreRecordVO> queryProcessScoreRecord(QueryProcessScoreRecordParam param);

    /**
     * 查询新增扣分项的扣分类型
     */
    AddItemInfoVO queryDeductionClassification(QueryDeductionTypeParam param);

    /**
     * 快速实施应用的项目过程新增扣分项/修改扣分项内容/修改扣分及扣分说明
     */
    ProjDeductionDetailProcess saveProcessDeduction(SaveProcessDeductionParam param);

    /**
     * 删除快速实施应用的项目过程的扣分项
     */
    boolean deleteProcessDeduction(DeleteProcessDeductionParam param);

    /**
     * 查询快速实施应用的线下培训/设备接口对接/数据统计的扣分明细
     */
    List<CommonScoreRecordVO> queryCommonScoreRecord(QueryProcessScoreRecordParam param);

    /**
     * 快速实施应用的线下培训/设备接口对接/数据统计的新增扣分项/修改扣分项内容/修改扣分及扣分说明
     */
    ProjDeductionDetailCommon saveCommonDeduction(SaveCommonDeductionParam param);

    /**
     * 删除快速实施应用的线下培训/设备接口对接/数据统计的扣分项
     */
    boolean deleteCommonDeduction(DeleteCommonDeductionParam param);

    /**
     * 满意度调查评分初始化
     */
    InitSatisfactionSurveyResultVO initSatisfactionSurvey(InitSatisfactionSurveyParamVO param);

    /**
     * 查询满意度调查的扣分明细
     */
    List<SatisfactionSurveyScoreRecordVO> querySatisfactionSurveyScoreRecord(QuerySatisfactionSurveyScoreRecordParam param);

    /**
     * 修改满意度调查的得分记录
     */
    boolean saveSatisfactionDeduction(SaveSatisfactionDeductionParam param);

}
