package com.msun.csm.service.common;

import static com.msun.csm.common.enums.message.DictMessageTypeEnum.EXCEPTION_SYS_MGR_AUDIT;
import static com.msun.csm.common.enums.message.DictMessageTypeEnum.SETTLEMENT_ENTRY_DETECT_DATA_ERR_KOWN;
import static com.msun.csm.common.enums.message.DictMessageTypeEnum.SETTLEMENT_ENTRY_ERR_NOTIFY;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.msun.csm.common.enums.message.MsgToCategory;
import com.msun.csm.model.param.MessageParam;
import com.msun.csm.service.message.SendMessageService;

import lombok.extern.slf4j.Slf4j;

/**
 * 异常消息提醒服务
 */
@Slf4j
@Service
public class ExceptionMessageService {

    @Resource
    private SendMessageService sendMessageService;

    /**
     * 发送给管理员异常消息
     *
     * @param projectInfoId 项目id
     * @param content       节点名称
     */
    public void sendToSystemManager(Long projectInfoId, String content) {
        try {
            MessageParam messageParam = new MessageParam();
            messageParam.setProjectInfoId(projectInfoId);
            messageParam.setContent(content);
            messageParam.setMessageTypeId(EXCEPTION_SYS_MGR_AUDIT.getId()); // 发送给管理员异常通知
            messageParam.setMessageToCategory(MsgToCategory.TO_ROLE.getCode());
            sendMessageService.sendMessage(messageParam);
        } catch (Throwable ignore) {
            log.error("发送给管理员异常消息: e.message: {}, e=", ignore.getMessage(), ignore);
        }
    }

    /**
     * 入驻数据监测异常通知，发送给pmo及系统管理员
     *
     * @param projectInfoId 项目id
     * @param content       节点名称
     */
    public void sendErrMessageForEntryDetect(Long projectInfoId, String content) {
        try {
            MessageParam messageParam = new MessageParam();
            messageParam.setProjectInfoId(projectInfoId);
            messageParam.setContent(content);
            messageParam.setMessageTypeId(SETTLEMENT_ENTRY_DETECT_DATA_ERR_KOWN.getId()); // 发送给管理员异常通知
            messageParam.setMessageToCategory(MsgToCategory.TO_ROLE.getCode());
            sendMessageService.sendMessage(messageParam);
        } catch (Throwable ignore) {
            log.error("发送给管理员异常消息: e.message: {}, e=", ignore.getMessage(), ignore);
        }
    }

    /**
     * 发送给pmo及系统管理员
     *
     * @param projectInfoId 项目id
     * @param content       节点名称
     */
    public void sendErrMessageForEntry(Long projectInfoId, String content) {
        try {
            MessageParam messageParam = new MessageParam();
            messageParam.setProjectInfoId(projectInfoId);
            messageParam.setContent(content);
            messageParam.setMessageTypeId(SETTLEMENT_ENTRY_ERR_NOTIFY.getId()); // 发送给管理员异常通知
            messageParam.setMessageToCategory(MsgToCategory.TO_ROLE.getCode());
            sendMessageService.sendMessage(messageParam);
        } catch (Throwable ignore) {
            log.error("发送给管理员异常消息: e.message: {}, e=", ignore.getMessage(), ignore);
        }
    }
}
