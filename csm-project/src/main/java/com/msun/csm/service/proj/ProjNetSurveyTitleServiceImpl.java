package com.msun.csm.service.proj;

import static com.msun.csm.common.staticvariable.StaticPara.OBS_TEMPTIME;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFHyperlinkRun;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.SimpleId;
import com.msun.csm.dao.entity.config.ConfigNetSurveyDetail;
import com.msun.csm.dao.entity.config.ConfigNetSurveyTitle;
import com.msun.csm.dao.entity.config.ConfigNetTemplateContent;
import com.msun.csm.dao.entity.config.ConfigNetTemplateTitle;
import com.msun.csm.dao.entity.dict.extend.DictNetPortMappingExtend;
import com.msun.csm.dao.entity.proj.PacsPrefrontDisk;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjNetPortMapping;
import com.msun.csm.dao.entity.proj.ProjNetSurveyDetail;
import com.msun.csm.dao.entity.proj.ProjNetSurveyResult;
import com.msun.csm.dao.entity.proj.ProjNetSurveyTitle;
import com.msun.csm.dao.entity.proj.ProjNetTemplateContent;
import com.msun.csm.dao.entity.proj.ProjNetTemplateTitle;
import com.msun.csm.dao.entity.proj.ProjProductDeliverRecord;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.mapper.config.ConfigNetSurveyDetailMapper;
import com.msun.csm.dao.mapper.config.ConfigNetSurveyTitleMapper;
import com.msun.csm.dao.mapper.config.ConfigNetTemplateContentMapper;
import com.msun.csm.dao.mapper.config.ConfigNetTemplateTitleMapper;
import com.msun.csm.dao.mapper.dict.DictNetPortMappingMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjNetPortMappingMapper;
import com.msun.csm.dao.mapper.proj.ProjNetSurveyDetailMapper;
import com.msun.csm.dao.mapper.proj.ProjNetSurveyResultMapper;
import com.msun.csm.dao.mapper.proj.ProjNetSurveyTitleMapper;
import com.msun.csm.dao.mapper.proj.ProjNetTemplateContentMapper;
import com.msun.csm.dao.mapper.proj.ProjNetTemplateTitleMapper;
import com.msun.csm.dao.mapper.proj.ProjProductDeliverRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectFileMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.model.req.netimprove.NetSurveyResult;
import com.msun.csm.model.req.netimprove.NetSurveySaveReq;
import com.msun.csm.model.req.switchplan.UpdateCheckedFileReq;
import com.msun.csm.model.resp.netimprove.NetDetailResp;
import com.msun.csm.model.resp.netimprove.NetPortMappingResp;
import com.msun.csm.model.resp.netimprove.NetSurveyResp;
import com.msun.csm.model.resp.netimprove.NetTemplateContentResp;
import com.msun.csm.model.resp.netimprove.NetTemplateResp;
import com.msun.csm.model.resp.netimprove.NetTemplateTitleResp;
import com.msun.csm.model.resp.project.FileResp;
import com.msun.csm.model.resp.switchplan.PhaseAndLogResp;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.obs.OBSClientUtils;

import cn.hutool.core.util.ObjectUtil;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/21
 */

@Service
public class ProjNetSurveyTitleServiceImpl implements ProjNetSurveyTitleService {

    @Resource
    private ProjNetSurveyTitleMapper projNetSurveyTitleMapper;

    @Resource
    private ProjNetSurveyDetailMapper projNetSurveyDetailMapper;

    @Resource
    private ProjNetSurveyResultMapper projNetSurveyResultMapper;

    @Resource
    private ProjProjectInfoMapper projectInfoMapper;

    @Resource
    private ConfigNetSurveyTitleMapper configNetSurveyTitleMapper;

    @Resource
    private ConfigNetSurveyDetailMapper configNetSurveyDetailMapper;

    @Resource
    private ConfigNetTemplateTitleMapper configNetTemplateTitleMapper;

    @Resource
    private ConfigNetTemplateContentMapper configNetTemplateContentMapper;

    @Resource
    private ProjNetTemplateTitleMapper projNetTemplateTitleMapper;

    @Resource
    private ProjNetTemplateContentMapper projNetTemplateContentMapper;

    @Resource
    private ProjCustomInfoMapper customInfoMapper;

    @Resource
    private DictNetPortMappingMapper dictNetPortMappingMapper;

    @Resource
    private ProjNetPortMappingMapper projNetPortMappingMapper;

    @Resource
    private ProjProjectFileMapper projectFileMapper;

    @Resource
    private ProjProductDeliverRecordMapper productDeliverRecordMapper;

    @Resource
    private UserHelper userHelper;

    @Resource
    private ProjProjectPlanService projProjectPlanService;

    @Override
    public int deleteByPrimaryKey(Long netSurveyTitleId) {
        return projNetSurveyTitleMapper.deleteByPrimaryKey(netSurveyTitleId);
    }

    @Override
    public int insert(ProjNetSurveyTitle record) {
        return projNetSurveyTitleMapper.insert(record);
    }

    @Override
    public int insertOrUpdate(ProjNetSurveyTitle record) {
        return projNetSurveyTitleMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(ProjNetSurveyTitle record) {
        return projNetSurveyTitleMapper.insertOrUpdateSelective(record);
    }

    @Override
    public int insertSelective(ProjNetSurveyTitle record) {
        return projNetSurveyTitleMapper.insertSelective(record);
    }

    @Override
    public ProjNetSurveyTitle selectByPrimaryKey(Long netSurveyTitleId) {
        return projNetSurveyTitleMapper.selectByPrimaryKey(netSurveyTitleId);
    }

    @Override
    public int updateByPrimaryKeySelective(ProjNetSurveyTitle record) {
        return projNetSurveyTitleMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ProjNetSurveyTitle record) {
        return projNetSurveyTitleMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<ProjNetSurveyTitle> list) {
        return projNetSurveyTitleMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<ProjNetSurveyTitle> list) {
        return projNetSurveyTitleMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<ProjNetSurveyTitle> list) {
        return projNetSurveyTitleMapper.batchInsert(list);
    }

    private static final Long[] PACS_PRODUCT = {4063L, 5731L, 5728L};

    /**
     * 初始化数据
     *
     * @param simpleId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<List<NetSurveyResp>> initAndReturn(SimpleId simpleId) {
        //先查询该项目是否已有问卷标题数据
        List<ProjNetSurveyTitle> projNetSurveyTitleList = projNetSurveyTitleMapper.selectByProjectId(simpleId.getId());
        if (!CollectionUtils.isEmpty(projNetSurveyTitleList)) {
            //查询数据拼装返回值直接返户
            return Result.success(this.getNetSurveyRespList(simpleId.getId()));
        }
        SysUserVO currentUser = userHelper.getCurrentUser();
        // 查询项目数据
        ProjProjectInfo projectInfo = projectInfoMapper.selectByPrimaryKey(simpleId.getId());
        if (projectInfo == null) {
            throw new CustomException("项目不存在");
        }
        // 根据项目属性查询问卷标题数据
        ProjCustomInfo customInfo = customInfoMapper.selectByPrimaryKey(projectInfo.getCustomInfoId());
        List<ConfigNetSurveyTitle> configNetSurveyTitleList = configNetSurveyTitleMapper.selectByProject(projectInfo,
                customInfo.getTelesalesFlag());
        if (CollectionUtils.isEmpty(configNetSurveyTitleList)) {
            throw new CustomException("网络改造方案调研标题数据不存在");
        }
        //查询调研项
        List<String> titleCodes = configNetSurveyTitleList.stream().map(ConfigNetSurveyTitle::getNetSurveyTitleCode)
                .collect(Collectors.toList());
        List<ConfigNetSurveyDetail> configNetSurveyDetails = configNetSurveyDetailMapper.selectByTitleCode(titleCodes);
        if (CollectionUtils.isEmpty(configNetSurveyDetails)) {
            throw new CustomException("网络改造方案调研详情数据不存在");
        }
        //复制调研标题和详情数据到项目表中
        List<ProjNetSurveyTitle> saveTitleList = new ArrayList<>();
        List<ProjNetSurveyDetail> saveDetailList = new ArrayList<>();
        for (ConfigNetSurveyTitle configNetSurveyTitle : configNetSurveyTitleList) {
            ProjNetSurveyTitle projNetSurveyTitle = new ProjNetSurveyTitle();
            BeanUtils.copyProperties(configNetSurveyTitle, projNetSurveyTitle);
            projNetSurveyTitle.setNetSurveyTitleId(SnowFlakeUtil.getId());
            projNetSurveyTitle.setProjectInfoId(simpleId.getId());
            projNetSurveyTitle.setCreaterId(currentUser.getSysUserId());
            projNetSurveyTitle.setCreateTime(new Date());
            projNetSurveyTitle.setUpdaterId(currentUser.getSysUserId());
            projNetSurveyTitle.setUpdateTime(new Date());
            projNetSurveyTitle.setIsDeleted(0);
            saveTitleList.add(projNetSurveyTitle);
        }
        for (ConfigNetSurveyDetail configNetSurveyDetail : configNetSurveyDetails) {
            ProjNetSurveyDetail projNetSurveyDetail = new ProjNetSurveyDetail();
            BeanUtils.copyProperties(configNetSurveyDetail, projNetSurveyDetail);
            projNetSurveyDetail.setNetSurveyDetailId(SnowFlakeUtil.getId());
            projNetSurveyDetail.setProjectInfoId(simpleId.getId());
            projNetSurveyDetail.setCreaterId(currentUser.getSysUserId());
            projNetSurveyDetail.setCreateTime(new Date());
            projNetSurveyDetail.setUpdaterId(currentUser.getSysUserId());
            projNetSurveyDetail.setUpdateTime(new Date());
            projNetSurveyDetail.setIsDeleted(0);
            saveDetailList.add(projNetSurveyDetail);
        }
        projNetSurveyTitleMapper.batchInsert(saveTitleList);
        projNetSurveyDetailMapper.batchInsert(saveDetailList);
        // 处理返回值
        List<NetSurveyResp> netSurveyRespList = this.getNetSurveyRespList(simpleId.getId());
        return Result.success(netSurveyRespList);
    }

    /**
     * 拼装返回值
     *
     * @param projectInfoId
     * @return
     */
    private List<NetSurveyResp> getNetSurveyRespList(Long projectInfoId) {
        List<ProjNetSurveyTitle> projNetSurveyTitleList = projNetSurveyTitleMapper.selectByProjectId(projectInfoId);
        List<String> titleCodeList = projNetSurveyTitleList.stream().map(ProjNetSurveyTitle::getNetSurveyTitleCode)
                .distinct().collect(Collectors.toList());
        List<ProjNetSurveyDetail> projNetSurveyDetailList = projNetSurveyDetailMapper.selectByTitleCodeAndProjectId(
                titleCodeList, projectInfoId);
        Map<String, List<ProjNetSurveyDetail>> projNetSurveyDetailMap = projNetSurveyDetailList.stream().collect(
                Collectors.groupingBy(ProjNetSurveyDetail::getNetSurveyTitleCode));
        List<NetSurveyResp> netSurveyRespList = new ArrayList<>();
        //查询调研结果
        List<ProjNetSurveyResult> projNetSurveyResultList = projNetSurveyResultMapper.selectByProjectId(projectInfoId);
        for (ProjNetSurveyTitle projNetSurveyTitle : projNetSurveyTitleList) {
            NetSurveyResp netSurveyResp = new NetSurveyResp();
            BeanUtils.copyProperties(projNetSurveyTitle, netSurveyResp);
            ProjNetSurveyResult projNetSurveyResult = projNetSurveyResultList.stream().filter(
                            item -> item.getNetSurveyTitleCode().equals(projNetSurveyTitle.getNetSurveyTitleCode())).findFirst()
                    .orElse(null);
            List<NetDetailResp> netDetailRespList = new ArrayList<>();
            projNetSurveyDetailMap.get(projNetSurveyTitle.getNetSurveyTitleCode()).forEach(projNetSurveyDetail -> {
                NetDetailResp netDetailResp = new NetDetailResp();
                BeanUtils.copyProperties(projNetSurveyDetail, netDetailResp);
                netDetailResp.setChecked(0);
                if (projNetSurveyResult != null) {
                    String[] resultArr = projNetSurveyResult.getSurveyResult().split(",");
                    if (Arrays.stream(resultArr)
                            .anyMatch(item -> item.equals(projNetSurveyDetail.getNetSurveyDetailCode()))) {
                        netDetailResp.setChecked(1);
                    }
                }
                netDetailRespList.add(netDetailResp);
            });
            netSurveyResp.setNetDetailRespList(netDetailRespList);
            netSurveyRespList.add(netSurveyResp);
        }
        return netSurveyRespList;
    }

    /**
     * 保存网络改造方案调研数据
     *
     * @param req
     * @return
     */
    @Override
    public Result saveNetResult(NetSurveySaveReq req) {
        // 查询项目数据
        ProjProjectInfo projectInfo = projectInfoMapper.selectByPrimaryKey(req.getProjectInfoId());
        if (projectInfo == null) {
            throw new CustomException("项目不存在");
        }
        SysUserVO currentUser = userHelper.getCurrentUser();
        Date now = new Date();
        // 保存调研结果
        List<ProjNetSurveyTitle> surveyTitleList = projNetSurveyTitleMapper.selectByProjectId(req.getProjectInfoId());
        List<ProjNetSurveyResult> surveyResultList = new ArrayList<>();
        for (NetSurveyResult surveyResult : req.getSurveyResultList()) {
            ProjNetSurveyResult projNetSurveyResult = new ProjNetSurveyResult();
            projNetSurveyResult.setNetSurveyResultId(SnowFlakeUtil.getId());
            projNetSurveyResult.setProjectInfoId(req.getProjectInfoId());
            surveyTitleList.stream().filter(projNetSurveyTitle ->
                            projNetSurveyTitle.getNetSurveyTitleCode().equals(surveyResult.getTitleCode()))
                    .findFirst().ifPresent(projNetSurveyTitle -> {
                        projNetSurveyResult.setNetSurveyTitleName(projNetSurveyTitle.getNetSurveyTitleName());
                        projNetSurveyResult.setOrderNo(projNetSurveyTitle.getOrderNo());
                    });
            projNetSurveyResult.setNetSurveyTitleCode(surveyResult.getTitleCode());
            projNetSurveyResult.setSurveyResult(String.join(",", surveyResult.getDetailCodes()));
            projNetSurveyResult.setCreaterId(currentUser.getSysUserId());
            projNetSurveyResult.setUpdaterId(currentUser.getSysUserId());
            projNetSurveyResult.setCreateTime(now);
            projNetSurveyResult.setUpdateTime(now);
            projNetSurveyResult.setIsDeleted(0);
            surveyResultList.add(projNetSurveyResult);
        }
        projNetSurveyResultMapper.deleteByProjectId(req.getProjectInfoId());
        projNetSurveyResultMapper.batchInsert(surveyResultList);
        // 根据项目属性查询问卷标题数据
        ProjCustomInfo customInfo = customInfoMapper.selectByPrimaryKey(projectInfo.getCustomInfoId());
        List<ConfigNetTemplateTitle> configNetTemplateTitles = configNetTemplateTitleMapper.selectByProject(projectInfo,
                customInfo.getTelesalesFlag());
        List<String> detailCodeList = req.getSurveyResultList().stream().map(NetSurveyResult::getDetailCodes).flatMap(
                list -> list.stream()).distinct().collect(Collectors.toList());
        //查询改造方案详情数据
        List<ConfigNetTemplateContent> configNetTemplateContentList = new ArrayList<>();
        configNetTemplateTitles.stream().forEach(templateTitle -> {
            String titleCode = templateTitle.getNetTemplateTitleCode();
            List<ConfigNetTemplateContent> configNetTemplateContents =
                    configNetTemplateContentMapper.selectByTitleCodeAndSurveyResult(titleCode, detailCodeList);
            configNetTemplateContentList.addAll(configNetTemplateContents);
        });
        //复制调研标题和详情数据到项目表中
        List<ProjNetTemplateTitle> saveTitleList = new ArrayList<>();
        List<ProjNetTemplateContent> saveContentList = new ArrayList<>();
        List<ProjNetPortMapping> portMappingList = new ArrayList<>();
        List<ProjProductDeliverRecord> deliverRecordList = productDeliverRecordMapper.findProductDeliverRecordByProjectInfoId(req.getProjectInfoId());
        List<Long> yyProductIdList = deliverRecordList.stream()
                .map(ProjProductDeliverRecord::getProductDeliverId).collect(Collectors.toList());
        List<DictNetPortMappingExtend> portMappingExtendList = dictNetPortMappingMapper.selectByYYProductIdList(
                yyProductIdList);
        portMappingExtendList.stream().forEach(portMappingExtend -> {
            ProjNetPortMapping portMapping = new ProjNetPortMapping();
            BeanUtils.copyProperties(portMappingExtend, portMapping);
            portMapping.setPortMappingId(SnowFlakeUtil.getId());
            portMapping.setProjectInfoId(req.getProjectInfoId());
            portMapping.setNetSurveyDetailCode("-1");
            portMapping.setYyProductId(portMappingExtend.getYyProductId());
            portMapping.setCreaterId(currentUser.getSysUserId());
            portMapping.setCreateTime(now);
            portMapping.setUpdaterId(currentUser.getSysUserId());
            portMapping.setUpdateTime(now);
            portMapping.setIsDeleted(0);
            portMappingList.add(portMapping);
        });

        for (ConfigNetTemplateTitle configNetTemplateTitle : configNetTemplateTitles) {
            ProjNetTemplateTitle projNetTemplateTitle = new ProjNetTemplateTitle();
            BeanUtils.copyProperties(configNetTemplateTitle, projNetTemplateTitle);
            projNetTemplateTitle.setProjectInfoId(req.getProjectInfoId());
            projNetTemplateTitle.setNetTemplateTitleId(SnowFlakeUtil.getId());
            projNetTemplateTitle.setCreaterId(currentUser.getSysUserId());
            projNetTemplateTitle.setCreateTime(now);
            projNetTemplateTitle.setUpdaterId(currentUser.getSysUserId());
            projNetTemplateTitle.setUpdateTime(now);
            projNetTemplateTitle.setIsDeleted(0);
            projNetTemplateTitle.setDownloadStatus(0);
            saveTitleList.add(projNetTemplateTitle);
        }
        for (ConfigNetTemplateContent configNetTemplateContent : configNetTemplateContentList) {
            ProjNetTemplateContent content = new ProjNetTemplateContent();
            BeanUtils.copyProperties(configNetTemplateContent, content);
            content.setProjectInfoId(req.getProjectInfoId());
            content.setNetTemplateContentId(SnowFlakeUtil.getId());
            content.setCreaterId(currentUser.getSysUserId());
            content.setCreateTime(now);
            content.setUpdaterId(currentUser.getSysUserId());
            content.setUpdateTime(now);
            content.setIsDeleted(0);
            saveContentList.add(content);
        }
        //逻辑删除项目表中已有的数据，新增数据
        if (portMappingList.size() > 0) {
            projNetPortMappingMapper.deleteByProjectId(req.getProjectInfoId());
            projNetPortMappingMapper.batchInsert(portMappingList);
        } else {
            // 产品端口为空时，删除产品映射后的端口表标题
            saveTitleList = saveTitleList.stream().filter(item -> !"portMapping".equals(item.getNetTemplateTitleCode())).collect(Collectors.toList());
        }

        // 没有产品映射后的端口表时，将标题中的五替换为四
        List<String> netTemplateTitleCodeList = saveTitleList.stream().map(ProjNetTemplateTitle::getNetTemplateTitleCode).collect(Collectors.toList());
        if (!netTemplateTitleCodeList.contains("portMapping")) {
            saveTitleList.forEach(item -> {
                if (item.getNetTemplateTitleCode().startsWith("pacsPrefront")) {
                    item.setNetTemplateTitleName(item.getNetTemplateTitleName().replace("五", "四"));
                }
            });
        }

        // PACS产品
        List<Long> pacsProduct = Arrays.asList(PACS_PRODUCT);
        // 如果实施产品中没有PACS产品，将PACS前置机准备去掉
        if (pacsProduct.stream().noneMatch(yyProductIdList::contains)) {
            saveTitleList = saveTitleList.stream().filter(item -> !item.getNetTemplateTitleCode().startsWith("pacsPrefront")).collect(Collectors.toList());
        }
        projNetTemplateTitleMapper.deleteByProjectId(req.getProjectInfoId());
        projNetTemplateContentMapper.deleteByProjectId(req.getProjectInfoId());
        projNetTemplateTitleMapper.batchInsert(saveTitleList);
        projNetTemplateContentMapper.batchInsert(saveContentList);
        // 保存网络改造方案，项目计划状态改为进行中
        projProjectPlanService.updatePlanAndTodoTaskStatusFromUnfinishedToUnderway(req.getProjectInfoId(), DictProjectPlanItemEnum.SCHEME_NEWWORK);
        return Result.success();
    }

    /**
     * 查询网络改造方案数据
     *
     * @param req
     * @return
     */
    @Override
    public Result<NetTemplateResp> findNetContent(SimpleId req) {
        // 查询项目数据
        ProjProjectInfo projectInfo = projectInfoMapper.selectByPrimaryKey(req.getId());
        if (projectInfo == null) {
            throw new CustomException("项目不存在");
        }
        // 根据项目id查询问卷标题数据
        List<ProjNetTemplateTitle> titleList = projNetTemplateTitleMapper.selectByProjectId(req.getId());
        List<String> titleCodeList = titleList.stream().map(ProjNetTemplateTitle::getNetTemplateTitleCode)
                .distinct().collect(Collectors.toList());
        // 根据问卷标题编码查询问卷详情数据
        List<ProjNetTemplateContent> contentList = projNetTemplateContentMapper.selectByTitleCodeAndProjectId(
                titleCodeList, req.getId());
        List<NetTemplateTitleResp> titleRespList = new ArrayList<>();
        for (ProjNetTemplateTitle title : titleList) {
            NetTemplateTitleResp titleResp = new NetTemplateTitleResp();
            titleResp.setNetTemplateTitleId(title.getNetTemplateTitleId());
            titleResp.setNetTemplateTitleCode(title.getNetTemplateTitleCode());
            titleResp.setNetTemplateTitleName(title.getNetTemplateTitleName());
            titleResp.setProjectInfoId(req.getId());
            titleResp.setOrderNo(title.getOrderNo());
            List<ProjNetTemplateContent> contents = contentList.stream()
                    .filter(content -> title.getNetTemplateTitleCode().equals(content.getNetTemplateTitleCode()))
                    .collect(Collectors.toList());
            List<NetTemplateContentResp> contentResps = new ArrayList<>();
            List<NetPortMappingResp> netPortMappingRespList = new ArrayList<>();
            contents.stream().forEach(content -> {
                NetTemplateContentResp contentResp = new NetTemplateContentResp();
                contentResp.setNetTemplateContentId(content.getNetTemplateContentId());
                contentResp.setNetTemplateContentCode(content.getNetTemplateContentCode());
                contentResp.setNetTemplateContent(content.getNetTemplateContent());
                contentResp.setProjectInfoId(req.getId());
                contentResp.setNetSurveyDetailCode(content.getNetSurveyDetailCode());
                contentResp.setYyProductId(content.getYyProductId());
                contentResp.setExcutorType(content.getExcutorType());
                contentResp.setOrderNo(content.getOrderNo());
                // 内容类型 1 文本 2 表格
                contentResp.setContentType(1);
                contentResps.add(contentResp);
            });
            contentResps.stream().sorted(Comparator.comparing(NetTemplateContentResp::getOrderNo));
            titleResp.setNetTemplateContentRespList(contentResps);
            if (titleResp.getNetTemplateTitleCode().equals("portMapping")) {
                List<ProjNetPortMapping> netPortMappingList = projNetPortMappingMapper.selectByProjectInfoId(
                        req.getId());
                if (!CollectionUtils.isEmpty(netPortMappingList)) {
                    for (ProjNetPortMapping netPortMapping : netPortMappingList) {
                        NetPortMappingResp netPortMappingResp = new NetPortMappingResp();
                        BeanUtils.copyProperties(netPortMapping, netPortMappingResp);
                        netPortMappingRespList.add(netPortMappingResp);
                    }
                }
            }

            // PACS前置机磁盘
            if (titleResp.getNetTemplateTitleCode().startsWith("pacsPrefront")) {
                List<PacsPrefrontDisk> dictPacsPrefrontDisks = this.createPacsPrefrontDisk(projectInfo.getProjectType());
                List<NetTemplateContentResp> netTemplateContentRespList = titleResp.getNetTemplateContentRespList();
                for (NetTemplateContentResp netTemplateContentResp : netTemplateContentRespList) {
                    if (netTemplateContentResp.getNetTemplateContentCode().startsWith("pacsPrefrontDisk")) {
                        netTemplateContentResp.setPacsPrefrontDisk(dictPacsPrefrontDisks);
                    }
                }
            }

            titleResp.setNetPortMappingRespList(netPortMappingRespList);
            titleRespList.add(titleResp);
        }
        //封装返回数据
        NetTemplateResp netTemplateResp = new NetTemplateResp();
        titleRespList.stream().sorted(Comparator.comparing(NetTemplateTitleResp::getOrderNo));
        netTemplateResp.setNetTemplateTitleResp(titleRespList);
        netTemplateResp.setTemplateTitle(projectInfo.getProjectName() + "网络改造方案");
        return Result.success(netTemplateResp);
    }

    /**
     * 查询当前阶段
     *
     * @param req
     * @return
     */
    @Override
    public Result<PhaseAndLogResp> getCurrentPhase(SimpleId req) {
        PhaseAndLogResp phaseAndLogResp = new PhaseAndLogResp();
        List<ProjNetTemplateTitle> templateTitles = projNetTemplateTitleMapper.selectByProjectId(req.getId());
        // 判断是否有网络改造方案数据，有则是第二阶段，没有则是第一阶段
        // 1: 填写医院基本情况  2 生成并下载网络切换方案  3 切换方案定稿并提交
        if (CollectionUtils.isEmpty(templateTitles)) {
            phaseAndLogResp.setCurrentPhaseCode(1);
        } else if (templateTitles.get(0).getDownloadStatus() == 1) {
            phaseAndLogResp.setCurrentPhaseCode(3);
        } else {
            phaseAndLogResp.setCurrentPhaseCode(2);
        }
        return Result.success(phaseAndLogResp);
    }

    /**
     * 下载文件
     *
     * @param projectInfoId
     * @param response
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void downContentFile(Long projectInfoId, HttpServletResponse response) {
        //查询项目信息
        ProjProjectInfo projectInfo = projectInfoMapper.selectByPrimaryKey(projectInfoId);
        //修改下载状态
        int count = projNetTemplateTitleMapper.updateDownloadStatus(projectInfoId);
        if (count == 0) {
            throw new CustomException("网络改造方案数据不存在");
        }
        NetTemplateResp netTemplateResp = this.findNetContent(new SimpleId(projectInfoId)).getData();
        XWPFDocument document = createXWPFDocument(projectInfo, netTemplateResp);
        // 设置响应头信息
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode(projectInfo.getProjectName() + "网络改造方案.docx"));
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        // 将Excel写入输出流
        try {
            document.write(response.getOutputStream());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private XWPFDocument createXWPFDocument(ProjProjectInfo projectInfo, NetTemplateResp netTemplateResp) {
        XWPFDocument document = new XWPFDocument();
        XWPFParagraph paragraphTitle = document.createParagraph();
        paragraphTitle.setAlignment(ParagraphAlignment.CENTER);
        setParagraphStyle(paragraphTitle);
        XWPFRun runTitle = paragraphTitle.createRun();
        runTitle.setText(projectInfo.getProjectName() + "网络改造方案");
        runTitle.setBold(true);
        runTitle.setFontFamily("宋体");
        runTitle.setFontSize(20);
        for (NetTemplateTitleResp title : netTemplateResp.getNetTemplateTitleResp()) {
            XWPFParagraph paragraph = document.createParagraph();
            paragraph.setAlignment(ParagraphAlignment.LEFT);
            setParagraphStyle(paragraph);
            XWPFRun run = paragraph.createRun();
            run.setText(title.getNetTemplateTitleName());
            run.setBold(true);
            run.setFontFamily("宋体");
            run.setFontSize(15);
            List<NetTemplateContentResp> contents = title.getNetTemplateContentRespList();
            List<NetPortMappingResp> portMappingList = title.getNetPortMappingRespList();
            if (portMappingList.size() > 0) {
                int portMappingSize = portMappingList.size();
                XWPFTable table = document.createTable(portMappingSize + 1, 3);
                table.getRow(0).getCell(0).setText("中台");
                table.getRow(0).getCell(1).setText("出口网关唯一标识");
                table.getRow(0).getCell(2).setText("需要院内开放的固定端口号");
                for (int i = 0; i < portMappingSize; i++) {
                    NetPortMappingResp portMapping = portMappingList.get(i);
                    table.getRow(i + 1).getCell(0).setText(portMapping.getPortMappingName());
                    table.getRow(i + 1).getCell(1).setText(portMapping.getExportGatewayCode());
                    table.getRow(i + 1).getCell(2).setText(portMapping.getMappingPort());
                }
            } else {
                int size = title.getNetTemplateContentRespList().size();
                for (int i = 0; i < size; i++) {
                    XWPFParagraph paragraphContent = document.createParagraph();
                    paragraphContent.setAlignment(ParagraphAlignment.LEFT);
                    setParagraphStyle(paragraphContent);
                    XWPFRun runContent = paragraphContent.createRun();
                    runContent.setText(contents.get(i).getNetTemplateContent());
                    runContent.setFontSize(12);
                    runContent.setFontFamily("宋体");
                    if (title.getNetTemplateTitleCode().startsWith("pacsPrefront") && contents.get(i).getNetTemplateContentCode().startsWith("pacsPrefrontDisk")) {
                        if (!CollectionUtils.isEmpty(contents.get(i).getPacsPrefrontDisk())) {
                            List<PacsPrefrontDisk> pacsPrefrontDisk = contents.get(i).getPacsPrefrontDisk();
                            int portMappingSize = pacsPrefrontDisk.size();
                            XWPFTable table = document.createTable(portMappingSize + 1, 5);
                            table.getRow(0).getCell(0).setText("用途");
                            table.getRow(0).getCell(1).setText("磁盘大小");
                            table.getRow(0).getCell(2).setText("卷组名称");
                            table.getRow(0).getCell(3).setText("逻辑卷");
                            table.getRow(0).getCell(4).setText("挂载点");
                            for (int n = 0; n < portMappingSize; n++) {
                                PacsPrefrontDisk portMapping = pacsPrefrontDisk.get(n);
                                table.getRow(n + 1).getCell(0).setText(portMapping.getUseScene());
                                table.getRow(n + 1).getCell(1).setText(portMapping.getDiskSize());
                                table.getRow(n + 1).getCell(2).setText(portMapping.getVolumeGroupName());
                                table.getRow(n + 1).getCell(3).setText(portMapping.getLogicalVolume());
                                table.getRow(n + 1).getCell(4).setText(portMapping.getMountPoint());
                            }
                        }
                    }
                }
            }
        }
        //增加注释
        XWPFParagraph appendix = document.createParagraph();
        setParagraphStyle(appendix);
        appendix.setAlignment(ParagraphAlignment.LEFT);
        XWPFRun runAppendix = appendix.createRun();
        runAppendix.addBreak();
        runAppendix.setText("附：网络改造操作指引手册");
        runAppendix.addBreak();
        XWPFHyperlinkRun runLink = appendix.createHyperlinkRun("https://kdocs.cn/l/ckfYtNLm9yDm");
        runLink.setText("https://kdocs.cn/l/ckfYtNLm9yDm");
        runLink.setColor("0000FF");
        return document;
    }

    private static void setParagraphStyle(XWPFParagraph paragraphTitle) {
        //poi转换过来的行间距过大，需要手动调整
        if (paragraphTitle.getSpacingBefore() >= 1000 || paragraphTitle.getSpacingAfter() > 1000) {
            paragraphTitle.setSpacingBefore(0);
            paragraphTitle.setSpacingAfter(0);
        }
        //设置word中左右间距
        paragraphTitle.setIndentationLeft(0);
        paragraphTitle.setIndentationRight(0);
    }

    /**
     * 更新确认文件
     *
     * @param req
     * @return
     */
    @Override
    public Result updateConfirmationFile(UpdateCheckedFileReq req) {
        // 根据项目id查询问卷标题数据
        List<ProjNetTemplateTitle> titleList = projNetTemplateTitleMapper.selectByProjectId(req.getProjectInfoId());
        if (CollectionUtils.isEmpty(titleList)) {
            throw new CustomException("请先确认网络改造方案调研内容!");
        }
        ProjProjectFile pf = projectFileMapper.selectByPrimaryKey(req.getProjectFileId());
        if (pf == null) {
            throw new CustomException("请选择正确的文件!");
        }
        // 更新项目网络改造方案确认文件
        int count = projNetTemplateTitleMapper.updateConfirmationFile(req.getProjectFileId(), req.getProjectInfoId());
        return Result.success(count);
    }

    /**
     * 获取确认文件路径
     *
     * @param req
     * @return
     */
    @Override
    public Result getConfirmationFilePath(SimpleId req) {
        // 根据项目id查询问卷标题数据
        List<ProjNetTemplateTitle> titleList = projNetTemplateTitleMapper.selectByProjectId(req.getId());
        if (CollectionUtils.isEmpty(titleList)) {
            throw new CustomException("请先确认网络改造方案调研内容!");
        }
        FileResp fileResp = new FileResp();
        if (ObjectUtil.isNotEmpty(titleList.get(0).getProjectFileId())) {
            ProjProjectFile projectFile = projectFileMapper.selectByPrimaryKey(titleList.get(0).getProjectFileId());
            if (ObjectUtil.isNotEmpty(projectFile)) {
                fileResp.setFilePath(OBSClientUtils.getTemporaryUrl(projectFile.getFilePath(), OBS_TEMPTIME));
                fileResp.setFileName(projectFile.getFileName());
                fileResp.setFileId(projectFile.getProjectFileId());
            }
        }
        return Result.success(fileResp);
    }

    /**
     * 创建网络改造方案PACS前置机磁盘信息
     *
     * @param type 1-单体；2-区域
     * @return 网络改造方案PACS前置机磁盘信息
     */
    private List<PacsPrefrontDisk> createPacsPrefrontDisk(Integer type) {
        List<PacsPrefrontDisk> list = new ArrayList<>();

        if (Integer.valueOf(1).equals(type)) {
            PacsPrefrontDisk system = PacsPrefrontDisk
                    .builder()
                    .sortNo(1)
                    .useScene("系统")
                    .diskSize("200G")
                    .volumeGroupName("")
                    .logicalVolume("")
                    .mountPoint("")
                    .build();
            list.add(system);

            PacsPrefrontDisk application = PacsPrefrontDisk
                    .builder()
                    .sortNo(2)
                    .useScene("应用")
                    .diskSize("200G")
                    .volumeGroupName("msun_app")
                    .logicalVolume("app")
                    .mountPoint("/msun/app")
                    .build();
            list.add(application);

            PacsPrefrontDisk dicom = PacsPrefrontDisk
                    .builder()
                    .sortNo(3)
                    .useScene("影像存储")
                    .diskSize("建议20T以上")
                    .volumeGroupName("msun_dicomfiles")
                    .logicalVolume("dicom_files")
                    .mountPoint("/msun/dicom_files")
                    .build();
            list.add(dicom);
            return list;
        }

        PacsPrefrontDisk system = PacsPrefrontDisk
                .builder()
                .sortNo(1)
                .useScene("系统")
                .diskSize("100G")
                .volumeGroupName("")
                .logicalVolume("")
                .mountPoint("")
                .build();
        list.add(system);

        PacsPrefrontDisk application = PacsPrefrontDisk
                .builder()
                .sortNo(2)
                .useScene("应用")
                .diskSize("200G")
                .volumeGroupName("msun_app")
                .logicalVolume("app")
                .mountPoint("/msun/app")
                .build();
        list.add(application);

        PacsPrefrontDisk dicom = PacsPrefrontDisk
                .builder()
                .sortNo(3)
                .useScene("影像存储")
                .diskSize("建议5T以上")
                .volumeGroupName("msun_dicomfiles")
                .logicalVolume("dicom_files")
                .mountPoint("/msun/dicom_files")
                .build();
        list.add(dicom);
        return list;
    }
}
