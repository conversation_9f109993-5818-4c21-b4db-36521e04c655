package com.msun.csm.service.proj.applyorder;

import java.util.List;

import com.msun.csm.dao.entity.proj.ProjApplyOrderHospitalRecord;
import com.msun.csm.dao.entity.proj.ProjApplyOrderHospitalRecordRelative;

/**
 * <AUTHOR>
 * @since 2024-05-22 05:04:23
 */

public interface ProjApplyOrderHospitalRecordService {

    List<ProjApplyOrderHospitalRecordRelative> findByApplyOrderId(String applyOrderId);

    int inserBatch(List<ProjApplyOrderHospitalRecord> hospitalRecords);

    int deleteByApplyOrderId(Long applyOrderId);
}
