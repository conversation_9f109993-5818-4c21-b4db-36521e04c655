package com.msun.csm.service.proj;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.msun.csm.dao.entity.proj.ProductInfo;
import com.msun.csm.dao.entity.proj.ProjApplyOrderProduct;
import com.msun.csm.dao.entity.proj.ProjProductArrangeRecord;
import com.msun.csm.dao.mapper.proj.ProjProductArrangeRecordMapper;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/24
 */

@Service
public class ProjProductArrangeRecordServiceImpl implements ProjProductArrangeRecordService {

    @Resource
    private ProjProductArrangeRecordMapper projProductArrangeRecordMapper;

    @Override
    public int deleteByPrimaryKey(Long productArrangeReocrdId) {
        return projProductArrangeRecordMapper.deleteByPrimaryKey(productArrangeReocrdId);
    }

    @Override
    public int insert(ProjProductArrangeRecord record) {
        return projProductArrangeRecordMapper.insert(record);
    }

    @Override
    public int insertOrUpdate(ProjProductArrangeRecord record) {
        return projProductArrangeRecordMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(ProjProductArrangeRecord record) {
        return projProductArrangeRecordMapper.insertOrUpdateSelective(record);
    }

    @Override
    public int insertSelective(ProjProductArrangeRecord record) {
        return projProductArrangeRecordMapper.insertSelective(record);
    }

    @Override
    public ProjProductArrangeRecord selectByPrimaryKey(Long productArrangeReocrdId) {
        return projProductArrangeRecordMapper.selectByPrimaryKey(productArrangeReocrdId);
    }

    @Override
    public int updateByPrimaryKeySelective(ProjProductArrangeRecord record) {
        return projProductArrangeRecordMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ProjProductArrangeRecord record) {
        return projProductArrangeRecordMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<ProjProductArrangeRecord> list) {
        return projProductArrangeRecordMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<ProjProductArrangeRecord> list) {
        return projProductArrangeRecordMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<ProjProductArrangeRecord> list) {
        return projProductArrangeRecordMapper.batchInsert(list);
    }

    @Override
    public List<ProjProductArrangeRecord> getArrangeProducts(Long projectId, Long productId) {
        return projProductArrangeRecordMapper.getArrangeProducts(projectId, productId);
    }

    @Override
    public List<ProjApplyOrderProduct> findByYyOrderProductIdList(List<ProductInfo> products, Long projectInfoId) {
        return projProductArrangeRecordMapper.findByYyOrderProductIdList(products, projectInfoId);
    }
}
