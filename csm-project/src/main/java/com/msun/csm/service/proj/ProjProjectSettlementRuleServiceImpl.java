package com.msun.csm.service.proj;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import static com.msun.csm.common.enums.projectfile.ProjectFileTypeEnums.SUBMIT_SURVEY_REPORT_ENTRY;
import static com.msun.csm.common.enums.projsettlement.SettlementRuleCodeEnum.SETTLEMENT_RESEARCH;
import static com.msun.csm.feign.entity.yunying.enums.OrderStepEnum.PURCHASE_SOFTWARE_SURVEY;
import static com.msun.csm.feign.entity.yunying.enums.OrderStepEnum.SOFTWARE_SURVEY;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.constants.ObsExpireTimeConsts;
import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.OpenStatusEnum;
import com.msun.csm.common.enums.api.yunying.OrderTypeEnums;
import com.msun.csm.common.enums.projprojectinfo.ProjectTypeEnums;
import com.msun.csm.common.enums.projsettlement.CheckNodeEnum;
import com.msun.csm.common.enums.projsettlement.CheckPrepayFlagEnum;
import com.msun.csm.common.enums.projsettlement.CheckPresaleFlagEnum;
import com.msun.csm.common.enums.projsettlement.CheckResultEnum;
import com.msun.csm.common.enums.projsettlement.SettlementMidOrderStatusEnum;
import com.msun.csm.common.enums.projsettlement.SettlementRuleCodeEnum;
import com.msun.csm.common.enums.projsettlement.SettlementStatusEnum;
import com.msun.csm.common.enums.rule.SceneCodeEnum;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysFile;
import com.msun.csm.dao.entity.proj.ProjCustomCloudService;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjOrderInfo;
import com.msun.csm.dao.entity.proj.ProjOrderProduct;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectOrderRelation;
import com.msun.csm.dao.entity.proj.ProjProjectSettlement;
import com.msun.csm.dao.entity.proj.ProjProjectSettlementCheck;
import com.msun.csm.dao.entity.proj.ProjProjectSettlementRule;
import com.msun.csm.dao.entity.proj.ProjProjectSettlementUnCheck;
import com.msun.csm.dao.entity.proj.extend.ProjProjectFileExtend;
import com.msun.csm.dao.entity.rule.RuleProjectRuleConfig;
import com.msun.csm.dao.mapper.proj.ProjCustomCloudServiceMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderProductMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectFileMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectSettlementCheckMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectSettlementMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectSettlementMidOrderMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectSettlementRuleMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectSettlementUnCheckMapper;
import com.msun.csm.dao.mapper.rule.RuleProjectRuleConfigMapper;
import com.msun.csm.dao.mapper.sysfile.SysFileMapper;
import com.msun.csm.entity.ProjProjectSettlementMidOrder;
import com.msun.csm.model.convert.ProjSettlementRuleAndRuleConfigConvert;
import com.msun.csm.model.convert.ProjSettlementRuleConvert;
import com.msun.csm.model.dto.UpdateMilestoneDTO;
import com.msun.csm.model.dto.applyorder.ProjApplyOrderMainDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckApplyEnterDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckSaleSaveDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckUploadDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementRuleDelFileDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementRuleQueryDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementRuleSaveDTO;
import com.msun.csm.model.dto.projsetttlement.SettlementRuleCheckParam;
import com.msun.csm.model.dto.projsetttlement.SettlementRuleParam;
import com.msun.csm.model.dto.projsetttlement.SettlementRuleTransport;
import com.msun.csm.model.req.projectfile.UploadFileReq;
import com.msun.csm.model.vo.ProjHospitalInfoVO;
import com.msun.csm.model.vo.SysFileVO;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementRuleHistoryVO;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementRuleVO;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementSubmitEntryVO;
import com.msun.csm.model.vo.projsettlement.ShowFile;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.common.CommonService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderService;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.obs.OBSClientUtils;

/**
 * <AUTHOR>
 * @since 2024-06-17 10:49:37
 */
@Slf4j
@Service
public class ProjProjectSettlementRuleServiceImpl implements ProjProjectSettlementRuleService {

    @Resource
    private ProjProjectSettlementRuleMapper projectSettlementRuleMapper;

    @Resource
    private RuleProjectRuleConfigMapper ruleProjectRuleConfigMapper;

    @Resource
    private ProjApplyOrderService applyOrderService;

    @Resource
    private ProjProjectInfoService projectInfoService;

    @Resource
    private ProjHospitalInfoService hospitalInfoService;

    @Resource
    private ProjHospitalInfoMapper hospitalInfoMapper;

    @Resource
    private ProjSettlementRuleAndRuleConfigConvert settlementRuleAndRuleConfigConvert;

    @Resource
    private ProjSettlementRuleConvert settlementRuleConvert;

    @Resource
    private ProjProjectFileService projProjectFileService;

    @Resource
    private ProjProjectFileMapper projProjectFileMapper;

    @Resource
    private ProjProjectSettlementMapper projectSettlementMapper;

    @Resource
    private ProjProjectSettlementCheckService settlementCheckService;


    @Resource
    private ProjProjectSettlementCheckMainService mainService;

    @Resource
    private ProjProjectSettlementMapper projProjectSettlementMapper;

    @Resource
    private ProjProjectSettlementLogService settlementLogService;

    @Resource
    private ProjMilestoneInfoMapper milestoneInfoMapper;

    @Resource
    private UserHelper userHelper;

    @Resource
    private ProjProjectSettlementCheckSaleService settlementCheckSaleService;


    @Resource
    private ProjContractInfoService contractInfoService;

    @Resource
    private ProjOrderInfoMapper orderInfoMapper;

    @Resource
    private ProjOrderProductMapper orderProductMapper;

    @Resource
    private ProjProjectSettlementCheckMapper settlementCheckMapper;

    @Resource
    private SysFileMapper sysFileMapper;

    @Resource
    private ProjCustomCloudServiceMapper customCloudServiceMapper;

    @Resource
    private ProjProjectSettlementUnCheckMapper settlementUnCheckMapper;

    @Resource
    private ProjProjectSettlementRuleService settlementRuleService;

    @Resource
    private ProjProjectSettlementMidOrderMapper settlementMidOrderMapper;


    @Resource
    private ProjProjectPlanService projProjectPlanService;

    @Resource
    private ProjTodoTaskService todoTaskService;

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Result<ProjProjectSettlementSubmitEntryVO> findProjProjectSettlementRuleList(ProjProjectSettlementRuleQueryDTO projectSettlementRuleQueryDTO) {
        List<ProjProjectSettlementRuleHistoryVO> historyVOS;
        // 判断项目是单体还是区域
        long projectInfoId = Long.parseLong(projectSettlementRuleQueryDTO.getProjectInfoId());
        // 根据项目id查询规则表是否有数据csm.proj_project_settlement_rule
        List<ProjProjectSettlementRule> rules = projectSettlementRuleMapper.selectList(
                new QueryWrapper<ProjProjectSettlementRule>()
                        .eq("project_info_id", Long.valueOf(projectSettlementRuleQueryDTO.getProjectInfoId()))
                        .orderByAsc("order_no"));
        if (CollUtil.isEmpty(rules)) {
            // 创建申请单
            ProjProjectSettlement settlement = insertOrUpdate(projectInfoId, userHelper.getCurrentUser().getSysUserId());
            // 创建规则. 还包括创建终审数据
            SettlementRuleParam settlementRuleParam = SettlementRuleParam.builder()
                    .sysUserId(userHelper.getCurrentUser().getSysUserId())
                    .projectInfoId(projectInfoId)
                    .projectSettlementId(settlement.getProjectSettlementId())
                    .createRule(true)
                    .createCheck(false)
                    .build();
            setRulesAndCheckBySence(settlementRuleParam);
            historyVOS = getDefaultRuleList(projectSettlementRuleQueryDTO, settlement.getSettlementStatus());
            return Result.success(new ProjProjectSettlementSubmitEntryVO(historyVOS,
                    mainService.isPaysignageForTip(projectInfoId)));
        }
        List<ProjProjectSettlement> settlements = projectSettlementMapper.selectList(
                new QueryWrapper<ProjProjectSettlement>().eq("project_info_id", projectInfoId));
        historyVOS = transformHistoryVO(settlementRuleConvert.po2Vo(rules), projectSettlementRuleQueryDTO.getProjectInfoId(), settlements.get(0).getSettlementStatus());
        return Result.success(new ProjProjectSettlementSubmitEntryVO(historyVOS, mainService.isPaysignageForTip(projectInfoId)));
    }


    private List<ProjProjectSettlementRuleHistoryVO> getDefaultRuleList(ProjProjectSettlementRuleQueryDTO projectSettlementRuleQueryDTO,
                                                                        int settlementStatus) {
        List<ProjProjectSettlementRule> settlementRules =
                projectSettlementRuleMapper.selectList(new QueryWrapper<ProjProjectSettlementRule>().eq(
                                "project_info_id", Long.parseLong(projectSettlementRuleQueryDTO.getProjectInfoId()))
                        .orderByAsc("order_no"));
        List<ProjProjectSettlementRuleVO> voList = settlementRuleConvert.po2Vo(settlementRules);
        return transformHistoryVO(voList,
                projectSettlementRuleQueryDTO.getProjectInfoId(), settlementStatus);
    }

    /**
     * 是否是首期项目h`
     *
     * @param projectInfoId 项目id
     * @return boolean
     */
    private boolean isFirstProject(Long projectInfoId) {
        ProjProjectInfo projectInfo = projectInfoService.selectByPrimaryKey(projectInfoId);
        return applyOrderService.isFirstProject(projectInfo);
    }

    /**
     * 更新或新增
     *
     * @param settlementRule 项目规则
     */
    private void insertOrUpdateRule(ProjProjectSettlementRule settlementRule) {
        int count;
        if (ObjectUtil.isEmpty(settlementRule.getProjectSettlementRuleId())) {
            settlementRule.setProjectSettlementRuleId(SnowFlakeUtil.getId());
            settlementRule.setCreateTime(new Date());
            settlementRule.setUpdateTime(new Date());
            count = projectSettlementRuleMapper.insert(settlementRule);
            log.info("saveSettlement 新增 count: {}", count);
        } else {
            settlementRule.setUpdateTime(new Date());
            count = projectSettlementRuleMapper.updateById(settlementRule);
            log.info("saveSettlement 更新 count: {}", count);
        }
    }

    public Result<Boolean> saveSettlement(ProjProjectSettlementRuleSaveDTO settlementRuleSaveDTO) {
        return settlementRuleService.saveSettlement(settlementRuleSaveDTO, userHelper.getCurrentUser());
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Result<Boolean> saveSettlement(ProjProjectSettlementRuleSaveDTO settlementRuleSaveDTO,
                                          SysUserVO sysUserVO) {
        long projectInfoId = Long.parseLong(settlementRuleSaveDTO.getProjectInfoId());
        if (mainService.isOldSettlement(projectInfoId)) {
            updateSettlementState(Long.parseLong(settlementRuleSaveDTO.getProjectInfoId()));
            // 查询settlement
            List<ProjProjectSettlement> settlements = projectSettlementMapper.selectList(
                    new QueryWrapper<ProjProjectSettlement>().eq("project_info_id",
                            Long.parseLong(settlementRuleSaveDTO.getProjectInfoId())));
            if (CollUtil.isEmpty(settlements)) {
                throw new RuntimeException("提交入驻条件. 未查询到入驻申请单.");
            }
            ProjProjectSettlement settlement = settlements.get(0);
            SysUserVO reviewSysUser = userHelper.getCurrentUser();
            // 插入日志
            settlementLogService.insert(settlement, CheckNodeEnum.SUBMIT_ENTRY.getDesc(), CheckNodeEnum.SUBMIT_ENTRY,
                    reviewSysUser, CheckNodeEnum.SUBMIT_ENTRY.getSettlementStatusEnums().get(0).getCode());
            // 更新终审表
            settlementCheckService.updateResult(settlement.getProjectSettlementId(), reviewSysUser.getSysUserId(),
                    CheckNodeEnum.SUBMIT_ENTRY.getDesc(),
                    CheckNodeEnum.SUBMIT_ENTRY, CheckResultEnum.AUDIT_PASS);
        }
        // 发送消息
        settlementCheckSaleService.sendMessageToSalePerson(projectInfoId,
                ", " + CheckNodeEnum.SUBMIT_ENTRY.getMsgDesc());
        //更新里程碑状态
        ProjMilestoneInfo milestoneInfo = milestoneInfoMapper.selectOne(
                new QueryWrapper<ProjMilestoneInfo>().eq("project_info_id", projectInfoId)
                        .eq("milestone_node_code", "survey_summary").eq("invalid_flag", NumberEnum.NO_0.num()));
        UpdateMilestoneDTO updateMilestoneDTO = new UpdateMilestoneDTO();
        updateMilestoneDTO.setMilestoneStatus(1);
        updateMilestoneDTO.setMilestoneInfoId(milestoneInfo.getMilestoneInfoId());
        updateMilestoneDTO.setNodeHeadId(sysUserVO.getSysUserId());
        updateMilestoneDTO.setActualCompTime(new Date());
        milestoneInfoMapper.updateMilestone(updateMilestoneDTO);
        settlementCheckService.updateYunyingNodeStatus(projectInfoId, SOFTWARE_SURVEY, PURCHASE_SOFTWARE_SURVEY,
                sysUserVO);
        return Result.success();
    }

    @Override
    public Result<Boolean> delSettlementFile(ProjProjectSettlementRuleDelFileDTO settlementRuleDelFileDTO) {
        int count = projProjectFileMapper.delete(new QueryWrapper<ProjProjectFile>()
                .eq("project_file_id", settlementRuleDelFileDTO.getProjectFileId())
                .eq("project_info_id", Long.valueOf(settlementRuleDelFileDTO.getProjectInfoId()))
                .eq("milestone_node_code",
                        SUBMIT_SURVEY_REPORT_ENTRY.getMilestone()));
        return Result.success(count > 0);
    }

    private ProjProjectSettlement insertOrUpdate(Long projectInfoId, Long sysUserId) {
        return insertOrUpdate(projectInfoId, sysUserId, null);
    }

    private ProjProjectSettlement insertOrUpdate(Long projectInfoId, Long sysUserId,
                                                 SettlementRuleTransport transport) {
        ProjProjectSettlement findSettlement = ObjectUtil.isNotEmpty(transport) ? transport.getUpdateSettlement() : null;
        // 判断是否部署过云资源
        ProjProjectInfo projectInfo = projectInfoService.selectByPrimaryKey(projectInfoId);
        boolean hasDeployedCloud = hasDeployedCloud(projectInfo.getCustomInfoId());
        boolean firstDeployFlag = isFirstProject(projectInfoId);
        int count;
        ProjProjectSettlement settlement;
        if (ObjectUtil.isEmpty(findSettlement)) {
            // 新增表单
            settlement = new ProjProjectSettlement();
            // 根据交付工单id查询运营平台工单id
            List<ProjOrderProduct> orderProducts = orderProductMapper.selectList(new QueryWrapper<ProjOrderProduct>()
                            .eq("order_info_id", projectInfo.getOrderInfoId()));
            ProjOrderProduct orderProduct = orderProducts.get(0);
            settlement.setYyOrderId(orderProduct.getYyOrderProductId());
            settlement.setProjectInfoId(projectInfoId);
            settlement.setProjectSettlementId(SnowFlakeUtil.getId());
            settlement.setProjectUserTime(new Date());
            settlement.render(hasDeployedCloud, firstDeployFlag);
            settlement.setProjectUserId(sysUserId);
            settlement.setSettlementStatus(SettlementStatusEnum.INIT.getCode());
            // 默认设置为否。后面程序会根据规则更新
            settlement.setCheckPrepayFlag(mainService.hasPaysign(projectInfoId)
                    ? CheckPrepayFlagEnum.NEED_NO_CHECK_PREPAY.getCode()
                    : CheckPrepayFlagEnum.NEED_CHECK_PREPAY.getCode());
            settlement.setCheckPresaleFlag(CheckPresaleFlagEnum.NEED_NO_CHECK_PRE_SALE.getCode());
            count = projectSettlementMapper.insert(settlement);
            log.info("新增入驻申请. count: {}", count);
        } else {
            settlement = findSettlement;
        }
        return settlement;
    }

    /**
     * 更新状态
     *
     * @param projectInfoId 项目id
     */
    private void updateSettlementState(long projectInfoId) {
        List<ProjProjectSettlement> settlements = projProjectSettlementMapper.selectList(
                new QueryWrapper<ProjProjectSettlement>().eq("project_info_id", projectInfoId));
        ProjProjectSettlement settlement = settlements.get(0);
        ProjProjectSettlement copy = new ProjProjectSettlement();
        copy.setProjectSettlementId(settlement.getProjectSettlementId());
        copy.setSettlementStatus(SettlementStatusEnum.COMMIT_SETTLEMENT.getCode());
        int count = projectSettlementMapper.updateById(copy);
        if (count <= 0) {
            throw new RuntimeException("更新入驻申请状态失败.");
        }
    }

    /**
     * 是否部署过云资源
     *
     * @param customInfoId 客户id
     * @return boolean
     */
    private boolean hasDeployedCloud(Long customInfoId) {
        List<ProjHospitalInfoVO> hospitalInfoList = hospitalInfoService.getHospitalInfoByCustomeInfoId(customInfoId);
        return CollUtil.isNotEmpty(hospitalInfoList) && hospitalInfoList.stream().anyMatch(
                e -> ObjectUtil.isNotEmpty(e.getEnvId()) && e.getEnvId() != -1
                        && e.getHospitalOpenStatus().intValue() == OpenStatusEnum.OPENED.getCode());
    }

    /**
     * 增加规则
     *
     * @param transport 转换参数
     */
    private void insertOrUpdateRule(SettlementRuleTransport transport) {
        if (CollUtil.isNotEmpty(transport.getInsertSettlementRules())) {
            for (ProjProjectSettlementRule insertSettlementRule : transport.getInsertSettlementRules()) {
                insertOrUpdateRule(insertSettlementRule);
            }
        }
    }

    /**
     * 作废参数
     *
     * @param transport 转换参数
     */
    private void deleteRule(SettlementRuleTransport transport) {
        if (CollUtil.isNotEmpty(transport.getDeleteSettlementRules())) {
            int count =
                    projectSettlementRuleMapper.deleteBatchIds(transport.getDeleteSettlementRules().stream().map(ProjProjectSettlementRule::getProjectSettlementRuleId).collect(Collectors.toList()));
            log.info("批量删除项目规则. count: {}", count);
        }
    }

    /**
     * 生成规则或审核节点
     *
     * @param transport 传输对象
     */
    public void setRulesAndCheckBySence(SettlementRuleTransport transport) {
        // 生成规则
        if (transport.getCreateRule()) {
            // 设置入场规则
            setSettlementRules(transport);
            // 动态转换规则, 新增或作废或不变化
            dynamicChangeRules(transport);
            // 新增入场条件规则
            insertOrUpdateRule(transport);
            // 删除需要作废的规则
            deleteRule(transport);
        }
        // 获取审核节点数据, 新增审核节点
        if (transport.getCreateCheck()) {
            findSettlementCheck(transport);
            if (CollUtil.isEmpty(transport.getCheckParamsSet())) {
                return;
            }
            // 作废并新增审核节点及当前审核节点
            // 若已经创建过审核节点, 则获取原数据
            if (transport.getHasCreateCheck()) {
                ProjProjectSettlement update = new ProjProjectSettlement();
                update.setSettlementStatus(-1);
                int updateCount = projectSettlementMapper.update(update, new QueryWrapper<ProjProjectSettlement>().eq(
                        "project_info_id", transport.getProjectInfoId()));
                log.info("恢复初始值当前入驻审核. count: {}", updateCount);
            }
            ProjProjectSettlement settlement = insertOrUpdate(transport.getProjectInfoId(), transport.getSysUserId(),
                    transport);
            transport.setUpdateSettlement(settlement);
            // 更新当前审核节点状态
            int upcount = projectSettlementMapper.updateById(transport.getUpdateSettlement());
            log.info("更新入驻当前节点状态记录. count: {}, projectInfoId: {}", upcount, transport.getProjectInfoId());
            transport.getSettlementCheck().setProjectSettlementId(settlement.getProjectSettlementId());
            // 删除这一轮审核节点中不存在的审核节点
            dynamicNeedDeleteChecks(transport);
            if (CollUtil.isNotEmpty(transport.getDeleteChecks())) {
                int delcount =
                        settlementCheckMapper.deleteBatchIds(transport.getDeleteChecks().stream().map(ProjProjectSettlementCheck::getProjectSettlementCheckId).collect(Collectors.toList()));
                log.info("删除审核节点. count: {}", delcount);
            }
            for (SettlementRuleCheckParam checkParam : transport.getCheckParamsSet()) {
                if (ObjectUtil.isEmpty(checkParam.getCheckResultEnum()) || checkParam.getCheckResultEnum().getCode()
                        == CheckResultEnum.AUDIT_FAIL.getCode()) {
                    int checkcount = settlementCheckMapper.delete(new QueryWrapper<ProjProjectSettlementCheck>().eq(
                            "project_info_id",
                            transport.getProjectInfoId()).eq("check_node", checkParam.getCheckNodeEnum().getCode()));
                    log.info("逻辑删除审核节点. count: {}", checkcount);
                    settlementCheckService.insert(transport.getSettlementCheck(), checkParam);
                    if (checkParam.getCheckNodeEnum().getCode() == CheckNodeEnum.BRANCH_MANAGER_AUDIT.getCode()) {
                        int uncheckcount =
                                settlementUnCheckMapper.delete(new QueryWrapper<ProjProjectSettlementUnCheck>().eq(
                                        "project_info_id", transport.getProjectInfoId()));
                        log.info("逻辑删除方案分公司审核记录. count: {}", uncheckcount);
                    }
                }
            }
        }
    }

    /**
     * 设置入场条件
     *
     * @param transport 转换参数
     */
    public void setSettlementRules(SettlementRuleTransport transport) {
        QueryWrapper<RuleProjectRuleConfig> queryWrapper = getCommonQueryWrapperForSettlementRule(transport);
        // 设置首期项目需要的查询条件
        if (transport.getIsFirstProject()) {
            if (ObjectUtil.isNotEmpty(transport.getChooseResourceFormType())) {
                if (transport.getChooseResourceFormType() == OrderTypeEnums.CLOUD_RESOURCE.getCode().intValue()) {
                    queryWrapper.in("cloud_service_flag", CollUtil.newArrayList(-1, NumberEnum.NO_1.num()));
                } else if (transport.getChooseResourceFormType() == OrderTypeEnums.HARDWARE.getCode().intValue()) {
                    queryWrapper.in("cloud_service_flag", CollUtil.newArrayList(-1, NumberEnum.NO_2.num()));
                }
            } else if (transport.getIsTelesalesCustomer()) {
                queryWrapper.in("cloud_service_flag", CollUtil.newArrayList(-1, NumberEnum.NO_1.num()));
            } else if (transport.getTransportMidOrder().isFreeMidOrderFlag()) {
                queryWrapper.in("cloud_service_flag", CollUtil.newArrayList(-1, NumberEnum.NO_2.num()));
            } else {
                queryWrapper.in("cloud_service_flag", CollUtil.newArrayList(-1, NumberEnum.NO_0.num()));
            }
        }
        List<ProjProjectSettlementRule> rules = findSettlementRule(transport, queryWrapper);
        transport.getInsertSettlementRules().addAll(rules);
        // 筛查是否需要首付款规则
        sencePaySignage(transport);
    }

    /**
     * 动态转换规则, 新增或作废或不变化
     *
     * @param transport 转换参数
     */
    private void dynamicChangeRules(SettlementRuleTransport transport) {
        // 没有规则记录则直接返回, 不处理
        if (!transport.getHasCreateRule()) {
            return;
        }
        // 获取需要作废的规则
        List<ProjProjectSettlementRule> needInsertRules =
                transport.getInsertSettlementRules().stream().filter(e -> transport.getCreatedSettlementRules().stream().noneMatch(f -> f.getProjectRuleCode().equals(e.getProjectRuleCode()))).collect(Collectors.toList());
        // 获取需要新增的规则
        List<ProjProjectSettlementRule> needDeleteRules =
                transport.getCreatedSettlementRules().stream().filter(e -> transport.getInsertSettlementRules().stream().noneMatch(f -> f.getProjectRuleCode().equals(e.getProjectRuleCode()))).collect(Collectors.toList());
        transport.setInsertSettlementRules(needInsertRules);
        transport.setDeleteSettlementRules(needDeleteRules);
    }

    /**
     * 筛查需要删除的节点
     *
     * @param transport 转换对象
     */
    private void dynamicNeedDeleteChecks(SettlementRuleTransport transport) {
        // 没有规则记录则直接返回, 不处理
        if (!transport.getHasCreateRule()) {
            return;
        }
        Set<ProjProjectSettlementCheck> needDeleteChecks =
                transport.getCreatedSettlementChecks().stream().filter(e -> transport.getCheckParamsSet().stream().noneMatch(f -> f.getCheckNodeEnum().getCode() == e.getCheckNode())).collect(Collectors.toSet());
        transport.setDeleteChecks(needDeleteChecks);
    }

    /**
     * 查询审核节点有哪些
     *
     * @param projectInfoId 项目id
     * @return List<ProjProjectSettlementCheck>
     */
    public List<ProjProjectSettlementCheck> findSettlementCheck(Long projectInfoId) {
        // 查询是否生成了审核记录
        List<ProjProjectSettlementCheck> createdSettlementChecks =
                settlementCheckMapper.selectList(new QueryWrapper<ProjProjectSettlementCheck>().eq(
                        "project_info_id", projectInfoId).orderByAsc("check_node"));
        boolean created = CollUtil.isNotEmpty(createdSettlementChecks);
        if (created) {
            return createdSettlementChecks;
        }
        // 生成传输对象
        SettlementRuleParam settlementRuleParam = SettlementRuleParam.builder().build();
        settlementRuleParam.setProjectInfoId(projectInfoId);
        settlementRuleParam.setCreateRule(false);
        settlementRuleParam.setCreateCheck(true);
        SettlementRuleTransport transport = setTransport(settlementRuleParam);
        // 获取云资源审核节点
        findSettlementCheck(transport);
        List<ProjProjectSettlementCheck> checks = CollUtil.newArrayList();
        for (SettlementRuleCheckParam checkParam : transport.getCheckParamsSet()) {
            ProjProjectSettlementCheck check = new ProjProjectSettlementCheck();
            check.setCheckNode(checkParam.getCheckNodeEnum().getCode());
            checks.add(check);
        }
        // 排序
        checks =
                checks.stream().sorted(Comparator.comparing(ProjProjectSettlementCheck::getCheckNode)).collect(Collectors.toList());
        transport.setCreatedSettlementChecks(checks);
        return transport.getCreatedSettlementChecks();
    }

    /**
     * 获取审核节点
     *
     * @param transport 转换参数
     */
    public void findSettlementCheck(SettlementRuleTransport transport) {
        // 生成新、老流程审核节点
        if (transport.getIsOldSetltlement()) {
            // 添加默认规则
            senceDefaultRuleOldStart(transport);
            // 场景一：获取项目是否支付首付款
            sencePaySignage(transport);
            // 场景二：是否首期项目
            senceFirstProject(transport);
            // 场景三：PMO审核、项目经理确认入驻
            senceDefaultRuleOldEnd(transport);
        } else {
            // 添加默认规则
            senceDefaultRule(transport);
            // 场景一：获取项目是否支付首付款
            sencePaySignage(transport);
            // 场景二：是否首期项目
            senceFirstProject(transport);
        }
    }

    /**
     * 创建规则和审核节点
     * 若 createRule = true, 则只创建规则. create = false, 只创建审核节点
     *
     * @param settlementRuleParam 请求参数
     */
    public void setRulesAndCheckBySence(SettlementRuleParam settlementRuleParam) {
        // 设置规则或设置审核节点并生成响应数据
        setRulesAndCheckBySence(setTransport(settlementRuleParam));
    }

    /**
     * 设置传输对象
     *
     * @param settlementRuleParam 请求参数
     * @return SettlementRuleTransport
     */
    private SettlementRuleTransport setTransport(SettlementRuleParam settlementRuleParam) {
        // 生成传输参数
        SettlementRuleTransport transport = getTransport(settlementRuleParam);
        // 若选择云资源或者中间件服务后进行配置, 生成审核节点使用选择的工单进行生成。
        if (ObjectUtil.isNotEmpty(settlementRuleParam.getSettlementCheckSaleSaveDTO())) {
            ProjOrderInfo orderInfo = null;
            ProjProjectSettlementCheckSaleSaveDTO settlementCheckSaleSaveDTO =
                    settlementRuleParam.getSettlementCheckSaleSaveDTO();
            if (ObjectUtil.isNotEmpty(settlementCheckSaleSaveDTO.getCustomCloudServiceId())) {
                // 查询对应资源工单判断是云资源或者中间件服务工单
                orderInfo = orderInfoMapper.selectOne(new QueryWrapper<ProjOrderInfo>().eq("order_info_id",
                        Long.parseLong(settlementCheckSaleSaveDTO.getCustomCloudServiceId())));
                // 不为空且不等于-1时, 此种情况为派工了中间件服务工单, 若=-1则派工了免中间件服务工单
            } else if (ObjectUtil.isNotEmpty(settlementCheckSaleSaveDTO.getMiddlewareOrderInfoId())) {
                orderInfo = orderInfoMapper.selectOne(new QueryWrapper<ProjOrderInfo>().eq("order_info_id",
                        Long.parseLong(settlementCheckSaleSaveDTO.getMiddlewareOrderInfoId())));
            }
            // 验证是否需要生成方案分公司审核节点
            ProjProjectOrderRelation projectOrderRelation =
                    mainService.getCloudResourceRelationBothType(transport.getProjectInfoId());
            if (ObjectUtil.isNotEmpty(projectOrderRelation)) {
                ProjCustomCloudService customCloudService =
                        customCloudServiceMapper.selectOne(new QueryWrapper<ProjCustomCloudService>().eq(
                                "custom_cloud_service_id", projectOrderRelation.getBussinessInfoId()));
                // 设置是否变更云类型
                transport.setChangeCloudServiceType(ObjectUtil.isNotEmpty(settlementRuleParam.getCloudServiceType()) && settlementRuleParam.getCloudServiceType().intValue() != customCloudService.getCloudServiceType());
                // 验证节点是否有变化. 若未变更云类型, 再进行判断
                if (!transport.getChangeCloudServiceType()) {
                    transport.setProjectOrderRelation(projectOrderRelation);
                    transport.setCustomCloudService(customCloudService);
                }
            }
            // 若未查询到服务工单则跳过
            if (ObjectUtil.isNotEmpty(orderInfo)) {
                assert orderInfo != null;
                transport.setChooseResourceFormType(orderInfo.getDeliveryOrderType());
                transport.setResoureYyOrderId(orderInfo.getYyOrderId());
            }
            // 若getMiddlewareOrderInfoId为-1, 则销售选择的是免中间件部署申请
            if (StrUtil.equals(settlementCheckSaleSaveDTO.getMiddlewareOrderInfoId(), "-1")) {
                transport.getTransportMidOrder().setFreeMidOrderFlag(true);
            }
        } else {
            // 初始化规则时进行判断
            if (transport.getDispatchCloudForm()) {
                transport.setChooseResourceFormType(OrderTypeEnums.CLOUD_RESOURCE.getCode());
            } else if (transport.getHasMiddlewareService()) {
                transport.setChooseResourceFormType(OrderTypeEnums.HARDWARE.getCode());
            }
        }
        return transport;
    }

    /**
     * 获取首付款用的传输对象
     *
     * @param projectInfoId 项目id
     * @return SettlementRuleTransport 传输对象
     */
    public SettlementRuleTransport getTransportForPayedSignage(Long projectInfoId) {
        return SettlementRuleTransport.builder()
                .projectInfoId(projectInfoId)
                .hasPurchaseSoftWareOrder(mainService.hasPurchaseSoftOrderInfo(projectInfoId))
                .updateSettlement(projectSettlementMapper.selectOne(new QueryWrapper<ProjProjectSettlement>().eq(
                        "project_info_id", projectInfoId)))
                .payedSignage(mainService.hasPaysign(projectInfoId))
                .build().init();
    }

    /**
     * 获取传输对象
     *
     * @param settlementRuleParam 请求参数
     * @return SettlementRuleTransport 生成的传输对象
     */
    public SettlementRuleTransport getTransport(SettlementRuleParam settlementRuleParam) {
        ProjProjectSettlementCheck settlementCheck = new ProjProjectSettlementCheck();
        settlementCheck.setProjectInfoId(settlementRuleParam.getProjectInfoId());
        settlementCheck.setProjectSettlementId(settlementRuleParam.getProjectSettlementId());
        // 查询是否生成了审核记录
        List<ProjProjectSettlementCheck> createdSettlementChecks =
                settlementCheckMapper.selectList(new QueryWrapper<ProjProjectSettlementCheck>().eq(
                        "project_info_id", settlementRuleParam.getProjectInfoId()));
        Map<Integer, ProjProjectSettlementCheck> createdSettlementCheckMap = createdSettlementChecks.stream().collect(
                Collectors.toMap(ProjProjectSettlementCheck::getCheckNode, p -> p));
        // 查询已经生成的规则
        List<ProjProjectSettlementRule> createdSettlementRules =
                projectSettlementRuleMapper.selectList(new QueryWrapper<ProjProjectSettlementRule>().eq(
                        "project_info_id", settlementRuleParam.getProjectInfoId()));
        Long projectInfoId = settlementRuleParam.getProjectInfoId();
        // 设置updateSettlement,若能查到则设置,查不到置空,会对预付款审核起作用,需要使用到checkPrePayFlag标识
        ProjProjectSettlement findSettlement =
                projProjectSettlementMapper.selectOne(new QueryWrapper<ProjProjectSettlement>().eq("project_info_id",
                        settlementRuleParam.getProjectInfoId()));
        // 规则入参
        return SettlementRuleTransport.builder()
                .sysUserId(settlementRuleParam.getSysUserId())
                .settlementCheck(settlementCheck)
                .projectSettlementId(settlementRuleParam.getProjectSettlementId())
                .projectType(mainService.getProjectTypeEnum(projectInfoId).getCode())
                .projectInfoId(projectInfoId)
                .createRule(settlementRuleParam.getCreateRule())
                .createCheck(settlementRuleParam.getCreateCheck())
                .hasCreateCheck(CollUtil.isNotEmpty(createdSettlementChecks))
                .createdSettlementChecks(createdSettlementChecks)
                .createdSettlementCheckMap(createdSettlementCheckMap)
                .hasCreateRule(CollUtil.isNotEmpty(createdSettlementRules))
                .createdSettlementRules(createdSettlementRules)
                .hasPurchaseSoftWareOrder(mainService.hasPurchaseSoftOrderInfo(projectInfoId))
                .hasMiddlewareService(mainService.hasMiddlewareOrderInfo(projectInfoId))
                .dispatchCloudForm(mainService.dispatchCloudForm(projectInfoId))
                .isTelesalesCustomer(mainService.isTeleSalesByProjectInfoId(projectInfoId))
                .payedSignage(mainService.hasPaysign(projectInfoId))
                .projectInfo(mainService.getProjectInfo(projectInfoId))
                .isFirstProject(mainService.isFirstProject(projectInfoId))
                .contractInfo(mainService.getContractByProjectInfoId(projectInfoId))
                .isOldSetltlement(mainService.isOldSettlement(projectInfoId))
                .settlementCheckSaleSaveDTO(settlementRuleParam.getSettlementCheckSaleSaveDTO())
                .updateSettlement(findSettlement)
                .settlementRuleParam(settlementRuleParam)
                .build().init();
    }

    /**
     * 设置默认规则
     *
     * @param transport 规则生成参数
     */
    private void senceDefaultRule(SettlementRuleTransport transport) {
        transport.getCheckParamsSet().add(SettlementRuleCheckParam.builder().checkNodeEnum(CheckNodeEnum.SALE_APPLY_ENTRY).build());
    }

    private void senceDefaultRuleOldStart(SettlementRuleTransport transport) {
        // 创建终审数据（入场条件、销售申请）
        transport.getCheckParamsSet().add(SettlementRuleCheckParam.builder().checkNodeEnum(CheckNodeEnum.SALE_APPLY_ENTRY).build());
    }

    private void senceDefaultRuleOldEnd(SettlementRuleTransport transport) {
        transport.getCheckParamsSet().add(SettlementRuleCheckParam.builder().checkNodeEnum(CheckNodeEnum.PMO_AUDIT).build());
        transport.getCheckParamsSet().add(SettlementRuleCheckParam.builder().checkNodeEnum(CheckNodeEnum.ENSURE_SETTLE_IN).build());
    }

    /**
     * 场景一：首付款是否支付
     *
     * @param transport 规则入参
     */
    private void sencePaySignage(SettlementRuleTransport transport) {
        // 判断是否缴纳首付款
        boolean needpay = needPaySignage(transport);
        if (transport.getCreateCheck()) {
            if (needpay) {
                // 添加审核节点标识
                transport.getUpdateSettlement().setCheckPrepayFlag(CheckPrepayFlagEnum.NEED_CHECK_PREPAY.getCode());
                createRistRule(transport);
            }
        }
        // 筛出首付款标识
        if (transport.getCreateRule() && !needpay) {
            List<ProjProjectSettlementRule> rules =
                    transport.getInsertSettlementRules().stream().filter(e -> !StrUtil.equals(e.getProjectRuleCode(),
                            SettlementRuleCodeEnum.SETTLEMENT_PAY_ADVANCE_CHARGE.getCode())).collect(Collectors.toList());
            transport.setInsertSettlementRules(rules);
        }
    }

    /**
     * 需要缴纳首付款
     *
     * @param transport 传输对象
     * @return boolean true: 需要缴纳, false: 不需要缴纳
     */
    public boolean needPaySignage(SettlementRuleTransport transport) {
        // 若首付款跳过, 但云资源确认时间审核驳回, 用于再此发起申请跳过首付款审核使用
        if (ObjectUtil.isNotEmpty(transport)
                && ObjectUtil.isNotEmpty(transport.getUpdateSettlement())
                && ObjectUtil.isNotEmpty(transport.getUpdateSettlement().getCheckPrepayFlag())
                && transport.getUpdateSettlement().getCheckPrepayFlag()
                == CheckPrepayFlagEnum.NEED_NO_CHECK_PREPAY.getCode()) {
            return false;
        }
        if (!transport.getPayedSignage()) {
            // 创建规则
            if (!transport.getHasPurchaseSoftWareOrder()) {
                return true;
            }
        }
        return false;
    }

    public boolean needPaySignage(Long projectInfoId) {
        return needPaySignage(getTransportForPayedSignage(projectInfoId));
    }

    /**
     * 过滤是否需要创建分公司经理审核节点
     * 电销客户不会创建
     *
     * @param transport 参数传输
     */
    public void createBranchMgrRuleSwap(SettlementRuleTransport transport) {
        createBranchMgrRuleSwap(transport, null);
    }

    /**
     * 过滤是否需要创建分公司经理审核节点
     * 电销客户不会创建
     *
     * @param transport 参数传输
     */
    public void createBranchMgrRuleSwap(SettlementRuleTransport transport, CheckResultEnum checkResultEnum) {
        if (!transport.getIsTelesalesCustomer()) {
            createBranchMgrRule(transport, checkResultEnum);
        }
    }

    /**
     * 首期项目情况规则生成
     *
     * @param transport 规则入参
     */
    private void senceFirstProject(SettlementRuleTransport transport) {
        if (!transport.getIsFirstProject()) {
            return;
        }
        if (ObjectUtil.isEmpty(transport.getChooseResourceFormType())) {
            if (!transport.getHasCreateCheck()) {
                createBranchMgrRuleSwap(transport);
                // 若申请免中间件, 创建运营审核节点
                if (transport.getTransportMidOrder().isFreeMidOrderFlag()) {
                    createRistRule(transport);
                }
            } else {
                // 处理免中间件
                if (transport.getTransportMidOrder().isFreeMidOrderFlag()) {
                    createBranchMgrRule(transport);
                    // 获取运营审核结果
                    AtomicInteger processed = new AtomicInteger();
                    resourceFormValid(transport, processed, (transport1, processed1) -> {
                        if (transport1.getTransportMidOrder().isFreeMidOrderFlag()) {
                            // 若选择的私有云节点, 手填的部署节点
                            if (ObjectUtil.isNotEmpty(transport1.getCustomCloudService()) && StrUtil.equals(transport1.getSettlementCheckSaleSaveDTO().getEnvirName(),
                                    transport1.getCustomCloudService().getDeployNodeName())) {
                                processed1.getAndIncrement();
                            }
                            // 判断免中间件是否审核通过
                            ProjProjectSettlementMidOrder midOrder =
                                    settlementMidOrderMapper.selectOne(new QueryWrapper<ProjProjectSettlementMidOrder>()
                                            .eq("project_info_id", transport1.getProjectInfoId()));
                            if (ObjectUtil.isNotEmpty(midOrder)) {
                                transport1.getTransportMidOrder().getProcessLimitCount().incrementAndGet();
                                // 判断状态是否是通过
                                if (midOrder.getStatus() == SettlementMidOrderStatusEnum.APPROVED.getCode()) {
                                    processed1.getAndIncrement();
                                }
                            }
                        }
                    });
                    // 判断是否符合通过条件
                    if (processed.get() != transport.getTransportMidOrder().getProcessLimitCount().get()) {
                        createRistRule(transport);
                    }
                } else {
                    // 创建规则. 首期无云资源和中间件服务情况
                    AtomicInteger processed = new AtomicInteger();
                    resourceFormValid(transport, processed, (transport12, processed12) -> {
                        // 共享云时判断
                        if (ObjectUtil.isNotEmpty(transport12.getCustomCloudService()) && transport12.getCustomCloudService().getDeployNodeId() == Long.parseLong(transport12.getSettlementCheckSaleSaveDTO().getDeployNodeId())) {
                            processed12.getAndIncrement();
                        }
                    });
                    CheckResultEnum checkResultEnum = null;
                    Integer result = getCheckResult(transport, CheckNodeEnum.BRANCH_MANAGER_AUDIT);
                    if (ObjectUtil.isNotEmpty(result)) {
                        processed.getAndIncrement();
                    }
                    if (processed.get() == NumberEnum.NO_1.num()) {
                        checkResultEnum = CheckResultEnum.AUDIT_PASS;
                    }
                    createBranchMgrRuleSwap(transport, checkResultEnum);
                }
            }
        } else {
            // 派工了云资源工单
            if (transport.getChooseResourceFormType() == OrderTypeEnums.CLOUD_RESOURCE.getCode().intValue()) {
                // 若未创建过审核节点
                if (!transport.getHasCreateCheck()) {
                    createBranchMgrRuleSwap(transport);
                    createRistRule(transport);
                } else {
                    // -------- 方案审核 +++++++++++++
                    // 验证资源是否有修改
                    CheckResultEnum checkResultEnum = getBranchCheckResultForCloud(transport);
                    createBranchMgrRuleSwap(transport, checkResultEnum);
                    // ------ 验证是否需要运营部审核 +++++
                    checkResultEnum = getRiskCheckResultForCloud(transport);
                    createRistRule(transport, checkResultEnum);
                }
            } else if (transport.getChooseResourceFormType() == OrderTypeEnums.HARDWARE.getCode().intValue()) {
                // 若未创建过审核节点
                if (!transport.getHasCreateCheck()) {
                    createBranchMgrRuleSwap(transport);
                } else {
                    createBranchMgrRule(transport);
                }
            }
        }
    }

    /**
     * 创建方案审核规则
     *
     * @param transport 传输载荷
     */
    private void createBranchMgrRule(SettlementRuleTransport transport) {
        // 验证是否需要生成方案分公司审核节点
        CheckResultEnum checkResultEnum = getBranchCheckResultForHardware(transport);
        createBranchMgrRuleSwap(transport, checkResultEnum);
    }

    public CheckResultEnum getBranchCheckResultForHardware(SettlementRuleTransport transport) {
        AtomicInteger processed = new AtomicInteger();
        resourceFormValid(transport, processed, (transport1, processed1) -> {
            // 若选择的私有云节点或手填
            if (StrUtil.equals(transport1.getSettlementCheckSaleSaveDTO().getEnvirName(),
                    transport1.getCustomCloudService().getDeployNodeName())) {
                processed1.getAndIncrement();
            }
            // 若云类型相同
            if (!transport.getChangeCloudServiceType()) {
                processed1.getAndIncrement();
            }
        });
        // 验证资源是否备用有两个步骤
        int result = getCheckResult(transport, CheckNodeEnum.BRANCH_MANAGER_AUDIT);
        if (ObjectUtil.isNotEmpty(result) && result == CheckResultEnum.AUDIT_PASS.getCode()) {
            processed.getAndIncrement();
        }
        CheckResultEnum checkResultEnum = null;
        if (processed.get() == NumberEnum.NO_3.num()) {
            checkResultEnum = CheckResultEnum.AUDIT_PASS;
        }
        return checkResultEnum;
    }

    /**
     * 获取审核结果
     *
     * @param transport 转换参数
     * @return CheckResultEnum
     */
    public CheckResultEnum getBranchCheckResultForCloud(SettlementRuleTransport transport) {
        AtomicInteger processed = new AtomicInteger();
        resourceFormValid(transport, processed, (transport1, processed1) -> {
            // 若派工的中间件服务
            if (transport1.getDispatchCloudForm()) {
                // 若派工的众阳云
                if (transport1.getResoureYyOrderId().longValue() == transport1.getProjectOrderRelation().getYyOrderId()) {
                    processed1.getAndIncrement();
                }
                if (transport1.getCustomCloudService().getDeployNodeId() == Long.parseLong(transport1.getSettlementCheckSaleSaveDTO().getDeployNodeId())) {
                    processed1.getAndIncrement();
                }
            }
        });
        String oldPlanStartTime = DateUtil.formatDateTime(transport.getCustomCloudService().getPlanStartTime());
        String newPlanStartTime =
                DateUtil.formatDateTime(transport.getSettlementCheckSaleSaveDTO().getSubscribeStartTime());
        if (StrUtil.equals(oldPlanStartTime, newPlanStartTime)) {
            processed.getAndIncrement();
        }
        int result = getCheckResult(transport, CheckNodeEnum.BRANCH_MANAGER_AUDIT);
        // 若审核通过
        if (result != -1 && (result == CheckResultEnum.AUDIT_PASS.getCode())) {
            processed.getAndIncrement();
        }
        CheckResultEnum checkResultEnum = null;
        // 资源没有修改则全部验证通过4个步骤
        if (processed.get() == NumberEnum.NO_4.num()) {
            checkResultEnum = CheckResultEnum.AUDIT_PASS;
        }
        return checkResultEnum;
    }

    public CheckResultEnum getRiskCheckResultForCloud(SettlementRuleTransport transport) {
        String oldPlanStartTime = DateUtil.formatDateTime(transport.getCustomCloudService().getPlanStartTime());
        String newPlanStartTime =
                DateUtil.formatDateTime(transport.getSettlementCheckSaleSaveDTO().getSubscribeStartTime());
        AtomicInteger processed = new AtomicInteger();
        if (StrUtil.equals(oldPlanStartTime, newPlanStartTime)) {
            processed.getAndIncrement();
        }
        int result = getCheckResult(transport, CheckNodeEnum.RISK_AUDIT);
        if (result != -1 && (result == CheckResultEnum.AUDIT_PASS.getCode())) {
            processed.getAndIncrement();
        }
        // 判断免中间件审核状态, 若未通过则不会跳过审核
        CheckResultEnum checkResultEnum = null;
        // 若云资源开通确认时间有变换, 则需要风控重新审核
        if (processed.get() == NumberEnum.NO_3.num()) {
            checkResultEnum = CheckResultEnum.AUDIT_PASS;
        }
        return checkResultEnum;
    }

    /**
     * 获取上次流程审核结果. 返回空, 则上次审核无此审核节点. 若返回 0.驳回; 1. 审核通过
     *
     * @param transport     转换
     * @param checkNodeEnum 审核节点
     * @return 审核结果
     */
    public static int getCheckResult(SettlementRuleTransport transport, CheckNodeEnum checkNodeEnum) {
        ProjProjectSettlementCheck check =
                transport.getCreatedSettlementCheckMap().get(checkNodeEnum.getCode());
        if (ObjectUtil.isEmpty(check)) {
            return -1;
        }
        return ObjectUtil.isEmpty(check.getCheckResult()) ? -1 : check.getCheckResult();
    }

    /**
     * 资源表单验证
     * 只有在首期含云资源或者中间件部署服务时会用到
     *
     * @param transport 转换参数
     * @param processed 验证数
     */
    public void resourceFormValid(SettlementRuleTransport transport, AtomicInteger processed, IFormValid iFormValid) {
        iFormValid.valid(transport, processed);
    }

    /**
     * 验证接口.
     * 可匿名实现验证具体逻辑
     */
    public interface IFormValid {
        /**
         * 验证接口, 实现不通场景的验证
         *
         * @param processed 验证记录器
         */
        void valid(SettlementRuleTransport transport, AtomicInteger processed);
    }

    public void createRistRule(SettlementRuleTransport transport) {
        createRistRule(transport, null);
    }

    public void createRistRule(SettlementRuleTransport transport, CheckResultEnum checkResultEnum) {
        // 添加审核节点
        SettlementRuleCheckParam checkParam = SettlementRuleCheckParam.builder()
                .checkNodeEnum(CheckNodeEnum.RISK_AUDIT)
                .checkResultEnum(BeanUtil.copyProperties(checkResultEnum, CheckResultEnum.class))
                .build();
        // 判断若已存在的记录已审核则删除再新增, 若结果未审核, 则不操作, 保持原记录
        if (transport.getCheckParamsSet().contains(checkParam)) {
            for (SettlementRuleCheckParam settlementRuleCheckParam : transport.getCheckParamsSet()) {
                if (settlementRuleCheckParam.getCheckNodeEnum().getCode() == CheckNodeEnum.RISK_AUDIT.getCode()
                        && ObjectUtil.isNotEmpty(settlementRuleCheckParam.getCheckResultEnum())) {
                    transport.getCheckParamsSet().remove(checkParam);
                }
            }
        } else {
            transport.getCheckParamsSet().add(checkParam);
        }
    }

    /**
     * 若被驳回或未经过此节点直接返回true
     *
     * @param transport 传输参数
     * @return boolean true: 可以创建, false: 不能创建
     */
    public static boolean canCreateCheck(SettlementRuleTransport transport, CheckNodeEnum judgeCheckNode) {
        // 若已经存在, 并且审核通过则不会生成此节点
        List<ProjProjectSettlementCheck> checks =
                transport.getCreatedSettlementChecks().stream().filter(e -> e.getCheckNode() == judgeCheckNode.getCode()).collect(Collectors.toList());
        if (CollUtil.isEmpty(checks)) {
            return true;
        }
        ProjProjectSettlementCheck check = checks.get(0);
        // 若被驳回或未经过此节点直接返回true
        return ObjectUtil.isEmpty(check.getCheckResult()) || check.getCheckResult() == CheckResultEnum.AUDIT_FAIL.getCode();
    }

    /**
     * 获取项目类型（此处提供给通用规则查询时使用, 规则表中 region_flag 标识 1 区域 0 非区域）
     *
     * @param projectType 项目类型
     * @return int
     */
    private static int getProjectTypeFlag(int projectType) {
        return projectType == ProjectTypeEnums.REGION.getCode() ? NumberEnum.NO_1.num() : NumberEnum.NO_0.num();
    }

    /**
     * 添加规则
     *
     * @param transport 转换参数
     */
    private List<ProjProjectSettlementRule> findSettlementRule(SettlementRuleTransport transport,
                                                               QueryWrapper<RuleProjectRuleConfig> queryWrapper) {
        List<RuleProjectRuleConfig> ruleConfigs = ruleProjectRuleConfigMapper.selectList(queryWrapper);
        List<ProjProjectSettlementRule> settlementRules = CollUtil.newArrayList();
        if (CollUtil.isEmpty(ruleConfigs)) {
            return settlementRules;
        }
        ruleConfigs.forEach(e -> {
            ProjProjectSettlementRule settlementRule = BeanUtil.copyProperties(e, ProjProjectSettlementRule.class);
            settlementRule.setProjectInfoId(transport.getProjectInfoId());
            settlementRule.setProjectSettlementId(transport.getProjectSettlementId());
            settlementRules.add(settlementRule);
        });
        return settlementRules;
    }

    /**
     * 查询通用规则
     *
     * @param transport 传输参数
     * @return QueryWrapper<RuleProjectRuleConfig> 查询器
     */
    private QueryWrapper<RuleProjectRuleConfig> getCommonQueryWrapperForSettlementRule(SettlementRuleTransport transport) {
        QueryWrapper<RuleProjectRuleConfig> queryWrapper = new QueryWrapper<RuleProjectRuleConfig>().eq("scene_code",
                SceneCodeEnum.SETTLEMENT.getCode());
        // 项目类型
        queryWrapper.eq(getFlagFieldName(transport.getProjectType()), NumberEnum.NO_1.num());
        // 老换新和新上线
        queryWrapper.in("upgradation_type", CollUtil.newArrayList(-1, transport.getProjectInfo().getUpgradationType()));
        // 是否首期
        queryWrapper.in("his_flag", CollUtil.newArrayList(-1, transport.getIsFirstProject() ? NumberEnum.NO_1.num()
                : NumberEnum.NO_0.num()));
        // 合同类型
        queryWrapper.in("contract_type", CollUtil.newArrayList(0, transport.getContractInfo().getContractType()));
        // 是否是电销用户
        queryWrapper.in("telesales_flag", CollUtil.newArrayList(-1,
                transport.getIsTelesalesCustomer() ? NumberEnum.NO_1.num() : NumberEnum.NO_0.num()));
        return queryWrapper;
    }

    /**
     * 获取类型标识. 根据单体或区域类型查询不同字段
     *
     * @param projectTypeFlag 项目类型
     * @return String
     */
    public static String getFlagFieldName(int projectTypeFlag) {
        String flagFieldName = null;
        if (ProjectTypeEnums.SINGLE.getCode() == projectTypeFlag) {
            flagFieldName = "monomer_flag";
        } else if (ProjectTypeEnums.REGION.getCode() == projectTypeFlag) {
            flagFieldName = "region_flag";
        }
        if (StrUtil.isBlank(flagFieldName)) {
            throw new RuntimeException("项目类型不正确.");
        }
        return flagFieldName;
    }

    public void createBranchMgrRule(SettlementRuleTransport transport, CheckResultEnum checkResultEnum) {
        // - 更新为1, 需要运营部审核
        transport.getUpdateSettlement().setCheckPresaleFlag(NumberEnum.NO_1.num());
        // 判断并创建审核节点
        SettlementRuleCheckParam checkParam = SettlementRuleCheckParam.builder()
                .checkNodeEnum(CheckNodeEnum.BRANCH_MANAGER_AUDIT)
                .checkResultEnum(BeanUtil.copyProperties(checkResultEnum, CheckResultEnum.class))
                .build();
        transport.getCheckParamsSet().add(checkParam);
    }

    @Resource
    private CommonService commonService;

    /**
     * 转换前端需要vo
     *
     * @param voList        查询出的配置
     * @param projectInfoId 项目id
     * @return List<ProjProjectSettlementRuleHistoryVO>
     */
    public List<ProjProjectSettlementRuleHistoryVO> transformHistoryVO(List<ProjProjectSettlementRuleVO> voList,
                                                                       String projectInfoId, int settlementStatus) {
        List<ProjProjectSettlementRuleHistoryVO> historyVOS = new ArrayList<>();
        // 查询客户信息
        Long customInfoId = commonService.getProjectInfo(Long.parseLong(projectInfoId)).getCustomInfoId();
        voList.forEach(e -> {
            ProjProjectSettlementRuleHistoryVO ruleHistoryVO = new ProjProjectSettlementRuleHistoryVO();
            // 判断如果需要查询附件
            if (e.getProjectRuleCode().equals(SettlementRuleCodeEnum.SETTLEMENT_HARDWARE_LIST_FILE.getCode())) {
                ruleHistoryVO.setDocumentList(getShowFileFileByProjectInfoId(
                        settlementCheckSaleService.getSaleFileHardware(Long.parseLong(projectInfoId))));
            } else {
                // 获取项目文件id
                List<ProjProjectSettlementRule> settlementRules = projectSettlementRuleMapper.selectList(
                        new QueryWrapper<ProjProjectSettlementRule>().eq("project_info_id",
                                Long.parseLong(projectInfoId)).eq("project_rule_code", e.getProjectRuleCode()));
                ruleHistoryVO.setDocumentList(
                        getShowFileFileByProjectInfoId(settlementRules.get(0).getProjectFileId()));
            }
            // 添加模板信息
            if (NumberEnum.NO_1.num().intValue() == e.getTemplateFlag() && StrUtil.isNotBlank(e.getTemplateFileCode())) {
                if (StrUtil.equals(e.getProjectRuleCode(),
                        SettlementRuleCodeEnum.SETTLEMENT_PLAN_CLOUD_FILE.getCode()) || StrUtil.equals(e.getProjectRuleCode(),
                        SettlementRuleCodeEnum.SETTLEMENT_NO_SEND_MID_SERVICE.getCode())
                ) {
                    ProjApplyOrderMainDTO applyOrderMainDTO = new ProjApplyOrderMainDTO();
                    applyOrderMainDTO.setCustomInfoId(StrUtil.toString(customInfoId));
                    applyOrderMainDTO.setProjectInfoId(projectInfoId);
                    SysFileVO sysFileVO = applyOrderService.downloadPreResourceFromOutSystem(applyOrderMainDTO);
                    ShowFile showFile = new ShowFile();
                    showFile.setFileUrl(sysFileVO.getFilePath());
                    showFile.setFileName(sysFileVO.getFileName());
                    ruleHistoryVO.setModelFile(showFile);
                } else {
                    List<SysFile> sysFiles = sysFileMapper.selectList(new QueryWrapper<SysFile>().eq("business_code",
                            SceneCodeEnum.SETTLEMENT.getCode()).eq("file_code", e.getTemplateFileCode()));
                    if (CollUtil.isNotEmpty(sysFiles) && ObjectUtil.isNotEmpty(sysFiles.get(0).getFilePath())) {
                        SysFile sysFile = sysFiles.get(0);
                        ShowFile showFile = new ShowFile();
                        showFile.setFileUrl(OBSClientUtils.getTemporaryUrl(sysFile.getFilePath(),
                                ObsExpireTimeConsts.SEVEN_DAY));
                        showFile.setFileName(sysFile.getFileName());
                        ruleHistoryVO.setModelFile(showFile);
                    }
                }
            }
            ruleHistoryVO.setEdit(false);
            ruleHistoryVO.setOrderNo(e.getOrderNo());
            ruleHistoryVO.setProjectInfoId(projectInfoId);
            ruleHistoryVO.setProjectRuleContent(e.getProjectRuleContent());
            ruleHistoryVO.setProjectRuleCode(e.getProjectRuleCode());
            ruleHistoryVO.setSettlementStatus(settlementStatus);
            ruleHistoryVO.setProjectSettlementRuleId(ObjectUtil.isNotEmpty(e.getProjectSettlementRuleId())
                    ? StrUtil.toString(e.getProjectSettlementRuleId()) : null);
            historyVOS.add(ruleHistoryVO);
        });
        // 追加调研报告
        ProjProjectSettlementRuleHistoryVO ruleHistoryVO = new ProjProjectSettlementRuleHistoryVO();
        ruleHistoryVO.setProjectInfoId(projectInfoId);
        ruleHistoryVO.setProjectRuleCode(SETTLEMENT_RESEARCH.getCode());
        ruleHistoryVO.setEdit(true);
        ruleHistoryVO.setProjectRuleContent("项目经理上传调研报告：");
        ruleHistoryVO.setDocumentList(getShowFileList(Long.parseLong(projectInfoId),
                SUBMIT_SURVEY_REPORT_ENTRY.getMilestone()));
        historyVOS.add(ruleHistoryVO);
        return historyVOS;
    }

    /**
     * 根据项目id和里程碑节点获取文件
     *
     * @param projectInfoId     项目id
     * @param milestoneNodeCode 里程碑节点
     * @return List<ShowFile>
     */
    private List<ShowFile> getShowFileList(Long projectInfoId, String milestoneNodeCode) {
        List<ProjProjectFile> projProjectFiles = projProjectFileMapper.selectList(
                new QueryWrapper<ProjProjectFile>().eq("project_info_id", projectInfoId)
                        .eq("milestone_node_code", milestoneNodeCode));
        return getShowFileFileByProjectInfoId(projProjectFiles);
    }

    /**
     * 根据项目id获取项目存储的文件信息
     *
     * @param projectFileId 项目文件id
     * @return List<ShowFile>
     */
    public List<ShowFile> getShowFileFileByProjectInfoId(Long projectFileId) {
        List<ProjProjectFile> projProjectFiles = projProjectFileMapper.selectList(
                new QueryWrapper<ProjProjectFile>().eq("project_file_id", projectFileId));
        return getShowFileFileByProjectInfoId(projProjectFiles);
    }

    /**
     * 实际获取项目文件集合
     *
     * @param projProjectFiles 文件集合
     * @return List<ShowFile>
     */
    public List<ShowFile> getShowFileFileByProjectInfoId(List<ProjProjectFile> projProjectFiles) {
        if (CollUtil.isNotEmpty(projProjectFiles)) {
            return projProjectFiles.stream().map(f -> {
                ShowFile showFile = new ShowFile();
                showFile.setProjectFileId(f.getProjectFileId());
                showFile.setFileName(f.getFileName());
                showFile.setFileUrl(OBSClientUtils.getTemporaryUrl(f.getFilePath(), 604800));
                return showFile;
            }).collect(Collectors.toList());
        }
        return null;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Result<Boolean> uploadFile(ProjProjectSettlementCheckUploadDTO settlementCheckUploadDTO) {
        long projectInfoId = Long.parseLong(settlementCheckUploadDTO.getProjectInfoId());
        UploadFileReq req = new UploadFileReq(projectInfoId,
                SUBMIT_SURVEY_REPORT_ENTRY.getMilestone(),
                settlementCheckUploadDTO.getMultipartFile(), null, false, null);
        Result<ProjProjectFileExtend> result = projProjectFileService.uploadFile(req, null);
        if (ObjectUtil.isEmpty(result.getData())) {
            throw new RuntimeException("上传文件异常.");
        }
        // 如果上传文件时项目计划状态是未开始，变更为进行中。如果是进行中或者已完成，不做处理
        projProjectPlanService.updatePlanAndTodoTaskStatusFromUnfinishedToUnderway(projectInfoId, DictProjectPlanItemEnum.SURVEY_SUMMARY);
        return Result.success();
    }

    /**
     * 拆分项目使用, 会复制入驻规则及状态数据, 会向下一个审核节点发送消息通知
     * <p>
     * 新的项目需要重新走入驻流程, 会给销售发申请. 此接口只能在拆分项目中使用
     * </p>
     *
     * @param splitProject 拆分出的项目
     */
    public void splitProject(ProjProjectInfo splitProject) {
        String projectInfoId = StrUtil.toString(splitProject.getProjectInfoId());
        // 生成规则
        ProjProjectSettlementRuleQueryDTO queryDTO = new ProjProjectSettlementRuleQueryDTO();
        queryDTO.setProjectInfoId(projectInfoId);
        Result<ProjProjectSettlementSubmitEntryVO> entryVOResult =
                settlementRuleService.findProjProjectSettlementRuleList(queryDTO);
        log.info("拆分项目. 生成入驻规则. param: {}, result: {}", queryDTO, entryVOResult);

    }

    /**
     * 推送消息给销售, 新项目未入驻, 销售需要重新走入驻申请
     *
     * @param splitProject 拆分的项目
     */
    public void splitProjectSendMessage(ProjProjectInfo splitProject) {
        String projectInfoId = StrUtil.toString(splitProject.getProjectInfoId());
        // 推消息给销售
        ProjProjectSettlementCheckApplyEnterDTO enterDTO = new ProjProjectSettlementCheckApplyEnterDTO();
        enterDTO.setProjectInfoId(projectInfoId);
        Result<String> urgingResult = settlementCheckService.settlementUrging(enterDTO);
        log.info("拆分项目. 推送消息. param: {}, result: {}", enterDTO, urgingResult);
    }

}
