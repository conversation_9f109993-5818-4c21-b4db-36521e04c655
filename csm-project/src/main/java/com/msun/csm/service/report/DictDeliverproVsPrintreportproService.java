package com.msun.csm.service.report;


import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.report.DictDeliverproVsPrintreportpro;
import com.msun.csm.model.req.projreport.DictDeliverVsReportProductReq;
import com.msun.csm.model.resp.projreport.DictDeliverproVsPrintreportproResp;

/**
 * <AUTHOR>
 * @description 针对表【dict_deliverpro_vs_printreportpro(实施产品与打印平台产品对照)】的数据库操作Service
 * @createDate 2025-05-22 08:31:55
 */
public interface DictDeliverproVsPrintreportproService extends IService<DictDeliverproVsPrintreportpro> {

    /**
     * 查询打印产品
     * @return
     */
    List<BaseCodeNameResp> queryPrintProduct();

    /**
     * 查询实施产品与打印平台产品对照
     * @param dto
     * @return
     */
    Result<PageInfo<DictDeliverproVsPrintreportproResp>> findDeliverVsPrintProduct(DictDeliverVsReportProductReq dto);

    /**
     * 查询所有实施产品
     * @return
     */
    List<BaseIdNameResp> findProductAllDeliver();

    /**
     * 新增对照字典表
     * @param dto
     * @return
     */
    Result saveVsProduct(DictDeliverproVsPrintreportpro dto);

    /**
     * 删除对照字典表
     * @param dto
     * @return
     */
    Result deleteVsProduct(DictDeliverproVsPrintreportpro dto);
}
