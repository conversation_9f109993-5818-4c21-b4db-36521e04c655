package com.msun.csm.service.proj;

import static com.msun.csm.common.enums.projectsplitprocess.SplitProcessStatusEnum.CANCEL_APPLY;
import static com.msun.csm.common.enums.projectsplitprocess.SplitProcessStatusEnum.PMO_REJECT;
import static com.msun.csm.common.enums.projectsplitprocess.SplitProcessStatusEnum.QA_PASS;
import static com.msun.csm.common.enums.projectsplitprocess.SplitProcessStatusEnum.QA_REJECT;
import static com.msun.csm.common.staticvariable.StaticPara.TDUCK_SUCCESS_CODE;
import static com.msun.csm.service.proj.ProjCustomInfoServiceImpl.ROLE_CODE_ADMIN;
import static java.util.stream.Collectors.groupingBy;

import java.time.DateTimeException;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;

import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.enums.MemberInitFlagEnum;
import com.msun.csm.common.enums.MilestoneNodeEnum;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.ProjectPlanStatusEnum;
import com.msun.csm.common.enums.ProjectViewModelEnum;
import com.msun.csm.common.enums.ResultEnum;
import com.msun.csm.common.enums.api.yunying.OrderTypeEnums;
import com.msun.csm.common.enums.api.yunying.ProductSolutionEnum;
import com.msun.csm.common.enums.projprojectinfo.ProjectDeliverStatusEnums;
import com.msun.csm.common.enums.projprojectinfo.ProjectTypeEnums;
import com.msun.csm.common.enums.projprojectinfo.ProjectUpgradationTypeEnums;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.SimpleId;
import com.msun.csm.dao.entity.SysDept;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.config.ConfigOnlineStep;
import com.msun.csm.dao.entity.config.ConfigProjectStage;
import com.msun.csm.dao.entity.dict.DictProjectRole;
import com.msun.csm.dao.entity.oldimsp.OldCustomerInfo;
import com.msun.csm.dao.entity.proj.AddProjProjectConfigParamDTO;
import com.msun.csm.dao.entity.proj.ProductInfo;
import com.msun.csm.dao.entity.proj.ProjContractCustomInfo;
import com.msun.csm.dao.entity.proj.ProjCustomDetailInfo;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjManualCreateLog;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjMilestoneTask;
import com.msun.csm.dao.entity.proj.ProjOrderInfo;
import com.msun.csm.dao.entity.proj.ProjOrderProduct;
import com.msun.csm.dao.entity.proj.ProjProductDeliverRecord;
import com.msun.csm.dao.entity.proj.ProjProjectConfirmPlanOnlineTime;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectMember;
import com.msun.csm.dao.entity.proj.ProjProjectSplitProcess;
import com.msun.csm.dao.entity.proj.ProjProjectStageInfo;
import com.msun.csm.dao.entity.proj.ProjSurveyFineConfig;
import com.msun.csm.dao.entity.proj.ProjSyncApiLogs;
import com.msun.csm.dao.entity.proj.ProjTipRecord;
import com.msun.csm.dao.entity.proj.extend.ProjOrderInfoExtend;
import com.msun.csm.dao.entity.proj.extend.ProjProjectInfoExtend;
import com.msun.csm.dao.entity.proj.extend.ProjProjectMemberExtend;
import com.msun.csm.dao.entity.proj.projreport.ConfigCustomBackendDetailLimit;
import com.msun.csm.dao.entity.proj.projreport.ConfigCustomBackendLimit;
import com.msun.csm.dao.entity.rule.RuleProductRuleConfig;
import com.msun.csm.dao.entity.tmp.TmpProjectNewVsOld;
import com.msun.csm.dao.mapper.config.ConfigOnlineStepMapper;
import com.msun.csm.dao.mapper.config.ConfigProjectStageMapper;
import com.msun.csm.dao.mapper.dict.DictProjectRoleMapper;
import com.msun.csm.dao.mapper.dict.DictProjectStageMapper;
import com.msun.csm.dao.mapper.proj.ProjContractCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomDetailInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipFilesMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjManualCreateLogMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneTaskDetailMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneTaskMapper;
import com.msun.csm.dao.mapper.proj.ProjOnlineStepMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderProductMapper;
import com.msun.csm.dao.mapper.proj.ProjProductArrangeRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjProductBacklogMapper;
import com.msun.csm.dao.mapper.proj.ProjProductConfigLogMapper;
import com.msun.csm.dao.mapper.proj.ProjProductConfigMapper;
import com.msun.csm.dao.mapper.proj.ProjProductDeliverRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjProductEmpowerRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjProductTaskMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectConfirmPlanOnlineTimeMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectMemberMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectSplitProcessMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectStageInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyFineConfigMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyPlanMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyReportMapper;
import com.msun.csm.dao.mapper.proj.ProjSyncApiLogsMapper;
import com.msun.csm.dao.mapper.projform.ProjSurveyFormMapper;
import com.msun.csm.dao.mapper.report.ConfigCustomBackendDetailLimitMapper;
import com.msun.csm.dao.mapper.report.ConfigCustomBackendLimitMapper;
import com.msun.csm.dao.mapper.rule.RuleProductRuleConfigMapper;
import com.msun.csm.dao.mapper.sysdept.SysDeptMapper;
import com.msun.csm.dao.mapper.sysrole.SysRoleMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.dao.mapper.tmp.TmpProjectNewVsOldMapper;
import com.msun.csm.feign.client.oldimsp.OldImspFeignClient;
import com.msun.csm.feign.client.tduck.TDuckFeignClient;
import com.msun.csm.feign.client.yunying.YunyingFeignClient;
import com.msun.csm.feign.entity.oldimsp.req.ImspProjectMergeReq;
import com.msun.csm.feign.entity.oldimsp.req.ImspProjectSplitReq;
import com.msun.csm.feign.entity.oldimsp.resp.ImspProjectSplitResp;
import com.msun.csm.feign.entity.tduck.req.SplitAndMergeReq;
import com.msun.csm.feign.entity.yunying.req.OrderMergeReq;
import com.msun.csm.feign.entity.yunying.req.OrderSplitReq;
import com.msun.csm.feign.entity.yunying.req.ProductBaseReq;
import com.msun.csm.feign.entity.yunying.req.TokenReq;
import com.msun.csm.model.dto.ProductInfoDTO;
import com.msun.csm.model.dto.ProjHospitalInfoPageDTO;
import com.msun.csm.model.dto.ProjManualCreateLogQueryDto;
import com.msun.csm.model.dto.ProjMilestoneInfoDTO;
import com.msun.csm.model.dto.ProjProjectInfoDTO;
import com.msun.csm.model.dto.role.SysRoleDTO;
import com.msun.csm.model.dto.tip.ProjTipRecordDto;
import com.msun.csm.model.req.project.CustomProjectReq;
import com.msun.csm.model.req.project.FindProjectParamReq;
import com.msun.csm.model.req.project.ProjectMergeReq;
import com.msun.csm.model.req.project.ProjectSelectNotOnlineReq;
import com.msun.csm.model.req.project.ProjectSplitReq;
import com.msun.csm.model.resp.custombackendlimit.QueryProjectDataResp;
import com.msun.csm.model.resp.project.FindProjectParamResp;
import com.msun.csm.model.resp.project.FindProjectResp;
import com.msun.csm.model.resp.project.ProductBase;
import com.msun.csm.model.resp.project.ProjectFiscalYearResp;
import com.msun.csm.model.resp.project.ProjectNotOnlineResp;
import com.msun.csm.model.resp.project.ProjectProductCountResp;
import com.msun.csm.model.resp.project.ProjectSplitResp;
import com.msun.csm.model.resp.project.ProjectThisWeekResp;
import com.msun.csm.model.resp.project.WorkOrderBase;
import com.msun.csm.model.vo.DictProjectStageVO;
import com.msun.csm.model.vo.ProjCustomInfoVO;
import com.msun.csm.model.vo.ProjHospitalInfoVO;
import com.msun.csm.model.vo.ProjProjectInfoVO;
import com.msun.csm.model.vo.ProjectToolsOptionsForProjectInfoVO;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.model.yunying.CustomerDTO;
import com.msun.csm.model.yunying.OrderDTO;
import com.msun.csm.model.yunying.ProductDTO;
import com.msun.csm.model.yunying.SyncContractDTO;
import com.msun.csm.service.common.BaseQueryService;
import com.msun.csm.service.oldimsp.OldCustomerInfoService;
import com.msun.csm.util.CsmSignUtil;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.StringUtils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/24
 */

@Service
@Slf4j
public class ProjProjectInfoServiceImpl implements ProjProjectInfoService {

    @Resource
    private ProjProjectInfoMapper projectInfoMapper;

    @Resource
    private ProjOrderProductMapper orderProductMapper;

    @Resource
    private ProjProjectMemberMapper projectMemberMapper;

    @Resource
    private ProjCustomInfoMapper customInfoMapper;

    @Resource
    private ProjCustomDetailInfoMapper customDetailInfoMapper;

    @Resource
    private ProjContractCustomInfoMapper contractCustomInfoMapper;

    @Resource
    private ProjHospitalInfoMapper hospitalInfoMapper;

    @Resource
    private UserHelper userHelper;

    @Resource
    private TmpProjectNewVsOldMapper tmpProjectNewVsOldMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private SysDeptMapper sysDeptMapper;

    @Resource
    private ProjOrderInfoMapper orderInfoMapper;

    @Resource
    private YunyingFeignClient yunyingFeignClient;

    @Resource
    private ProjProductArrangeRecordMapper arrangeRecordMapper;

    @Resource
    private ProjProductDeliverRecordMapper deliverRecordMapper;

    @Resource
    private ProjProductDeliverRecordService deliverRecordService;

    @Resource
    private ProjSurveyPlanService surveyPlanService;

    @Resource
    private ProjProjectPlanService projProjectPlanService;

    @Resource
    ProjMilestoneInfoService projMilestoneInfoService;

    @Resource
    private ProjProductEmpowerRecordMapper empowerRecordMapper;

    @Resource
    private ProjSyncApiLogsMapper syncApiLogsMapper;

    @Resource
    private OldImspFeignClient oldImspFeignClient;

    @Resource
    private ProjProjectSplitProcessMapper splitProcessMapper;

    @Resource
    private SysRoleMapper sysRoleMapper;

    @Resource
    private RuleProductRuleConfigMapper ruleProductRuleConfigMapper;

    @Resource
    private OldCustomerInfoService oldCustomerInfoService;

    @Resource
    private ProjMilestoneInfoService milestoneInfoService;
    @Resource
    private ProjMilestoneInfoMapper milestoneInfoMapper;
    @Resource
    private DictProjectStageMapper dictProjectStageMapper;
    @Resource
    private ConfigProjectStageMapper configProjectStageMapper;
    @Resource
    private ProjProjectStageInfoMapper projProjectStageInfoMapper;
    //调研结果相关表
    @Resource
    private ProjProductBacklogMapper productBacklogMapper;
    @Resource
    private ProjProductTaskMapper productTaskMapper;
    @Resource
    private ProjProductConfigMapper productConfigMapper;
    @Resource
    private ProjProductConfigLogMapper productConfigLogMapper;
    @Resource
    private ProjSurveyReportMapper surveyReportMapper;
    @Resource
    private ProjSurveyFormMapper surveyFormMapper;
    @Resource
    private ProjSurveyPlanMapper surveyPlanMapper;
    @Resource
    private TDuckFeignClient tDuckFeignClient;
    @Resource
    private ProjMilestoneTaskMapper milestoneTaskMapper;
    @Resource
    private ProjMilestoneTaskDetailMapper milestoneTaskDetailMapper;

    @Resource
    private ProjTipRecordService tipRecordService;

    @Resource
    private ProjManualCreateLogMapper projManualCreateLogMapper;
    @Resource
    private ProjOnlineStepMapper onlineStepMapper;

    @Resource
    private ConfigOnlineStepMapper configOnlineStepMapper;

    @Lazy
    @Resource
    private BaseQueryService baseQueryService;

    @Lazy
    @Resource
    private ProjProjectSettlementRuleService settlementRuleService;

    @Resource
    private ProjProjectSettlementCheckService settlementCheckService;
    @Resource
    private ConfigCustomBackendLimitMapper customBackendLimitMapper;

    @Resource
    private ConfigCustomBackendDetailLimitMapper configCustomBackendDetailLimitMapper;

    @Resource
    private ProjEquipFilesMapper equipFilesMapper;

    @Resource
    private ProjEquipRecordMapper equipRecordMapper;

    @Resource
    private ProjProjectConfigService projectConfigService;
    @Resource
    private ProjProjectConfirmPlanOnlineTimeMapper confirmPlanOnlineTimeMapper;

    /**
     * 计算两个日期之间的天数差
     *
     * @param date1
     * @param date2
     * @return
     */
    public static long calculateDaysDifference(Date date1, Date date2) {
        long diffInMillies = Math.abs(date1.getTime() - date2.getTime());
        return TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);
    }

    /**
     * 集合1 是否包含集合2中的某个值
     *
     * @param list1
     * @param list2
     * @return
     */
    public static boolean containsAny(List<Long> list1, List<Long> list2) {
        return list2.stream().anyMatch(list1::contains);
    }


    @Override
    @Deprecated
    public int insert(ProjProjectInfo record) {
        return projectInfoMapper.insert(record);
    }


    @Override
    public ProjProjectInfo selectByPrimaryKey(Long projectInfoId) {
        return projectInfoMapper.selectByPrimaryKey(projectInfoId);
    }

    @Override
    public int updateByPrimaryKeySelective(ProjProjectInfo record) {
        return projectInfoMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ProjProjectInfo record) {
        return projectInfoMapper.updateByPrimaryKey(record);
    }

    /**
     * 获取项目查询参数
     *
     * @return
     * <AUTHOR>
     * @date 2024/4/25
     */
    @Override
    public Result<FindProjectParamResp> getParam(FindProjectParamReq req) {
        FindProjectParamResp resp = new FindProjectParamResp();
        //获取项目类型枚举
        List<BaseIdNameResp> deliveryStatusList = Stream.of(ProjectDeliverStatusEnums.values())
                .map(e -> new BaseIdNameResp(e.getCode().longValue(), e.getName())).collect(Collectors.toList());
        resp.setProjectDeliverStatus(deliveryStatusList);
        //获取工单数据
        //获取产品
        ProductInfoDTO productParam = new ProductInfoDTO();
        productParam.setCustomInfoId(req.getCustomInfoId());
        if (StringUtils.isNotEmpty(req.getOrderNumber())) {
            resp.setProjectNumbers(Collections.singletonList(req.getOrderNumber()));
            productParam.setDeliveryOrderNo(req.getOrderNumber());
            ProjProjectInfo projectInfo = projectInfoMapper.findByOrderNumber(req.getOrderNumber());
            productParam.setProjectInfoId(projectInfo.getProjectInfoId());
        } else {
            ProjProjectInfoDTO dto = new ProjProjectInfoDTO();
            ProjProjectInfoDTO infoDTO = this.handleProjProjectInfoDTO(dto);
            infoDTO.setCustomInfoId(req.getCustomInfoId());
            List<ProjProjectInfoVO> list = projectInfoMapper.findProjectInfo(infoDTO);
//            if (CollUtil.isNotEmpty(infoDTO.getDataRange()) && (infoDTO.getProjectMemberId() != null && infoDTO.getProjectMemberId() > 0)) {
//                list.addAll(projectInfoMapper.findMemberProjectInfo(infoDTO));
//                list.addAll(projectInfoMapper.findDataRangeProjectInfo(infoDTO));
//            } else if (CollUtil.isNotEmpty(infoDTO.getDataRange())) {
//                list.addAll(projectInfoMapper.findDataRangeProjectInfo(infoDTO));
//            } else if (infoDTO.getProjectMemberId() != null && infoDTO.getProjectMemberId() > 0) {
//                list.addAll(projectInfoMapper.findMemberProjectInfo(infoDTO));
//            } else {
//                list.addAll(projectInfoMapper.findMemberProjectInfo(infoDTO));
//            }
//            //去重并从大到小排序
//            list = list.stream()
//                    .collect(Collectors.toMap(
//                            ProjProjectInfoVO::getProjectInfoId, // 使用 projectInfoId 作为键
//                            vo -> vo,                           // 保留当前对象作为值
//                            (existing, replacement) -> existing // 如果有重复，保留第一个
//                    )).values().stream()
//                    .sorted(Comparator.comparing(ProjProjectInfoVO::getProjectNumber).reversed())
//                    .collect(Collectors.toList());
            // fixme 查询项目优化
            resp.setProjectNumbers(list.stream().map(ProjProjectInfoVO::getProjectNumber).collect(Collectors.toList()));
        }
        List<ProductInfo> orderProducts = orderProductMapper.findAllList(productParam);
        List<BaseIdNameResp> productList = orderProducts.stream()
                .map(p -> new BaseIdNameResp(p.getYyOrderProductId(), p.getProductName())).collect(Collectors.toList());
        resp.setOrderProducts(productList);
        return Result.success(resp);
    }

    /**
     * 查询项目信息
     *
     * @param dto
     * @return
     */
    @Override
    public Result<FindProjectResp> findProjectInfo(ProjProjectInfoDTO dto) {
        FindProjectResp resp = new FindProjectResp();
        ProjProjectInfoDTO infoDTO = this.handleProjProjectInfoDTO(dto);
        List<ProjProjectInfoVO> infos = projectInfoMapper.findProjectInfo(infoDTO);
//        if (CollUtil.isNotEmpty(infoDTO.getDataRange()) && (infoDTO.getProjectMemberId() != null && infoDTO.getProjectMemberId() > 0)) {
//            infos.addAll(projectInfoMapper.findMemberProjectInfo(infoDTO));
//            infos.addAll(projectInfoMapper.findDataRangeProjectInfo(infoDTO));
//        } else if (CollUtil.isNotEmpty(infoDTO.getDataRange())) {
//            infos.addAll(projectInfoMapper.findDataRangeProjectInfo(infoDTO));
//        } else if (infoDTO.getProjectMemberId() != null && infoDTO.getProjectMemberId() > 0) {
//            infos.addAll(projectInfoMapper.findMemberProjectInfo(infoDTO));
//        } else {
//            infos.addAll(projectInfoMapper.findMemberProjectInfo(infoDTO));
//        }
//        //去重并从大到小排序
//        infos = infos.stream()
//                .collect(Collectors.toMap(
//                        ProjProjectInfoVO::getProjectInfoId, // 使用 projectInfoId 作为键
//                        vo -> vo,                           // 保留当前对象作为值
//                        (existing, replacement) -> existing // 如果有重复，保留第一个
//                )).values().stream()
//                .sorted(Comparator.comparing(ProjProjectInfoVO::getProjectNumber).reversed())
//                .collect(Collectors.toList());        //客户详情信息
        List<ProjCustomInfoVO> customInfoVOS = customInfoMapper.selectVOById(dto.getCustomInfoId());
        resp.setCustomInfo(customInfoVOS.get(0));
        if (CollectionUtils.isEmpty(infos)) {
            resp.setSingleProjectList(Collections.emptyList());
            resp.setRegionProjectList(Collections.emptyList());
            return Result.success(resp);
        }
        boolean isSale = customInfoVOS.get(0).getTelesalesFlag() == 1;
        //累计工期计算
        infos.stream().filter(p -> null == p.getSettleInTime()).forEach(p -> p.setStandardDuration(null));
        infos.stream().filter(p -> null != p.getSettleInTime())
                .forEach(p -> {
                    Date beginDate = p.getSettleInTime();
                    Date endDate = ObjectUtils.isEmpty(p.getApplyAcceptTime()) ? new Date() : p.getApplyAcceptTime();
//                    Long duration = DateUtil.betweenDay(beginDate, endDate, true) + 1;
                    p.setStandardDuration(String.valueOf(com.msun.csm.util.DateUtil.getDifferentDays(beginDate, endDate)));
                });
        List<Long> projectIds = infos.stream().map(ProjProjectInfoVO::getProjectInfoId).collect(Collectors.toList());
        //新增处理 是否开启小前端大后端配置
        List<QueryProjectDataResp> customBackendLimits = customBackendLimitMapper.selectByProjectIds(projectIds);
        List<ProjProjectInfoVO> finalInfos = infos;
        customBackendLimits.stream().filter(limit -> limit.getOpenFlag() == 1).forEach(limit -> {
            ProjProjectInfoVO vo = finalInfos.stream().filter(c -> c.getProjectInfoId().equals(limit.getProjectInfoId()))
                    .findFirst().get();
            if (ObjectUtil.isNotNull(vo)) {
                vo.setBackendFlag(true);
                vo.setBeProjectLeaderId(limit.getBackendProjectManagerId());
                vo.setBeProjectLeaderName(limit.getBackendProjectManagerName());
            }
        });
        //处理产品数量
        List<ProjectProductCountResp> countResps = orderProductMapper.countByProjectId(projectIds);
        infos.stream().forEach(
                p -> countResps.stream().filter(c -> c.getProjectInfoId().equals(p.getProjectInfoId())).findFirst()
                        .ifPresent(c -> {
                            p.setProductCount(c.getProductCount());
                            p.setProductNames(c.getProductNames());
                        }));
        //处理人员数据
        List<ProjProjectMember> members = projectMemberMapper.selectByProjectIds(projectIds);
        if (!CollectionUtils.isEmpty(members)) {
            Map<Long, List<ProjProjectMember>> memberMap = members.stream()
                    .collect(groupingBy(ProjProjectMember::getProjectInfoId));
            infos.stream().forEach(p -> p.setTeamMembers(
                    Optional.ofNullable(memberMap.get(p.getProjectInfoId())).orElse(new ArrayList<>()).stream()
                            .map(m -> m.getProjectMemberName()).collect(Collectors.joining(","))));
        }
        //处理原项目信息
        List<TmpProjectNewVsOld> newVsOlds = tmpProjectNewVsOldMapper.selectByNewProjectIds(projectIds);
        infos.stream().forEach(
                p -> newVsOlds.stream().filter(n -> n.getNewProjectInfoId().equals(p.getProjectInfoId())).findFirst()
                        .ifPresent(n -> {
                            p.setOldProjectId(n.getOldProjectInfoId());
                            // 老系统的医院id处理存在问题，需要修改。原逻辑不动改为 根据老系统项目id查询老系统的医院信息，存入主键id
                            List<OldCustomerInfo> oldCustomerInfos =
                                    oldCustomerInfoService.list(new QueryWrapper<OldCustomerInfo>()
                                            .eq("project_id", n.getOldProjectInfoId())
                                            .orderByAsc("create_time")
                                    );
                            if (CollectionUtil.isEmpty(oldCustomerInfos)) {
                                log.error("未查询到老系统医院信息，老系统项目ID={}", n.getOldProjectInfoId());
                                throw new CustomException(String.format("未查询到老系统医院信息，老系统项目ID=%s",
                                        n.getOldProjectInfoId()));
                            }
                            p.setOldCustomInfoId(oldCustomerInfos.get(0).getId());
                            p.setOldCustomerId(n.getOldCustomId());
                            p.setProjectSource(n.getNewProjectSource());
                        }));
        //处理医院数量

        ProjHospitalInfoPageDTO hospitalDTO = new ProjHospitalInfoPageDTO();
        hospitalDTO.setCustomInfoId(dto.getCustomInfoId());
        List<ProjHospitalInfoVO> hospitalInfoVOs = hospitalInfoMapper.selectHospitalInfoList(hospitalDTO);
        int singleHospitalCount = hospitalInfoVOs.stream()
                .filter(h -> h.getProjectType().equals(ProjectTypeEnums.SINGLE.getCode())).collect(Collectors.toList())
                .size();
        int regionHospitalCount = hospitalInfoVOs.stream()
                .filter(h -> h.getProjectType().equals(ProjectTypeEnums.REGION.getCode()) || h.getProjectType()
                        .equals(ProjectTypeEnums.MEDICAL_COMMUNITY.getCode())).collect(Collectors.toList()).size();
        //处理项目经理数据
        infos.stream().forEach(p -> {
            p.setProjectManager(userHelper.getCurrentUser().getSysUserId().equals(p.getProjectLeaderId()));
            String projectTypeName = ProjectTypeEnums.getEnum(p.getProjectType()).getName();
            p.setProjectTypeName(isSale ? projectTypeName + "(电销)" : projectTypeName);
            p.setUpgradationTypeName(ProjectUpgradationTypeEnums.getEnum(p.getUpgradationType()).getName());
            p.setProjectDeliverStatusName(ProjectDeliverStatusEnums.getEnum(p.getProjectDeliverStatus()).getName());
            ProjTipRecordDto recordDto = ProjTipRecordDto.builder()
                    .configTipId(NumberEnum.NO_1.num().longValue())
                    .projectInfoId(p.getProjectInfoId())
                    .build();
            ProjTipRecord tipRecord = tipRecordService.getTipReacord(recordDto);
            if (ObjectUtil.isEmpty(tipRecord)) {
                p.setReadTipFlag(false);
            } else {
                p.setReadTipFlag(tipRecord.getReadFlag() == NumberEnum.NO_1.num().intValue());
            }
            // 项目是否上线标识
            p.setIsOnlineFlag(ObjectUtil.isNotEmpty(p.getProjectDeliverStatus()) && p.getProjectDeliverStatus() >= 5);

            // 项目是否确认计划上线时间标识
            Long count = new LambdaQueryChainWrapper<>(confirmPlanOnlineTimeMapper)
                    .eq(ProjProjectConfirmPlanOnlineTime::getIsDeleted, 0)
                    .eq(ProjProjectConfirmPlanOnlineTime::getProjectInfoId, p.getProjectInfoId())
                    .count();
            p.setIsConfirmOnlineTime(count == null || count <= 0);
        });
        //处理项目成员数据
        infos.forEach(p -> {
            boolean isPlanModel = projectConfigService.isPlanModel(p.getProjectInfoId());
            p.setHospitalCount(p.getProjectType().equals(ProjectTypeEnums.SINGLE.getCode()) ? singleHospitalCount : regionHospitalCount);
            if (isPlanModel) {
                p.setTargetPage(1);
            } else {
                p.setTargetPage(0);
            }
        });
        resp.setSingleProjectList(
                infos.stream().filter(p -> p.getProjectType().equals(ProjectTypeEnums.SINGLE.getCode()))
                        .collect(Collectors.toList()));
        resp.setRegionProjectList(infos.stream()
                .filter(p -> p.getProjectType().equals(ProjectTypeEnums.REGION.getCode()) || p.getProjectType()
                        .equals(ProjectTypeEnums.MEDICAL_COMMUNITY.getCode())).collect(Collectors.toList()));
        return Result.success(resp);
    }

    @Override
    public Integer updateInitFlag(Long projectInfoId) {
        LambdaUpdateWrapper<ProjProjectInfo> luw = new LambdaUpdateWrapper<>();
        luw.eq(ProjProjectInfo::getProjectInfoId, projectInfoId);
        ProjProjectInfo projectInfo = new ProjProjectInfo();
        projectInfo.setMemberInitFlag(MemberInitFlagEnum.INIT_FLAG_COMPLETE.getCode());
        int update = projectInfoMapper.update(projectInfo, luw);
        return update;
    }

    @Override
    public Result isProjectLeader(String projectInfoId) {
        ProjProjectInfo info = projectInfoMapper.selectById(Long.valueOf(projectInfoId));
        if (info == null) {
            return Result.fail();
        }
        return Result.success(info.getProjectLeaderId().equals(userHelper.getCurrentUser().getSysUserId()));
    }

    /**
     * 查询项目工单产品信息
     *
     * @param req
     * @return
     */
    @Override
    public Result<ProjectSplitResp> getProjectOrderProductInfo(SimpleId req) {
        ProjProjectInfo project = projectInfoMapper.selectById(req.getId());
        if (ObjectUtil.isEmpty(project)) {
            return Result.fail("项目不存在");
        }
        //项目基本信息
        SysUser manager = sysUserMapper.selectById(project.getProjectLeaderId());
        QueryWrapper deptQ = new QueryWrapper();
        deptQ.eq("dept_yunying_id", manager.getDeptId());
        SysDept dept = sysDeptMapper.selectOne(deptQ);
        ProjectSplitResp projectSplitResp = new ProjectSplitResp(project, manager, dept.getDeptName());
        //工单数据
        ProjOrderInfo order = orderInfoMapper.selectById(project.getOrderInfoId());
        if (order.getDeliveryOrderType() != OrderTypeEnums.SOFTWARE.getCode()) {
            return Result.fail("项目不存在软件工单");
        }
        WorkOrderBase orderBase = new WorkOrderBase(order);
        //产品数据
        ProjOrderProduct param = new ProjOrderProduct();
        param.setProjectInfoId(project.getProjectInfoId());
        param.setOrderInfoId(order.getOrderInfoId());
        List<ProductBase> productBaseList = orderProductMapper.selectProductBaseByProjectId(req.getId());
        if (productBaseList.size() == 0) {
            return Result.fail("项目不存在产品");
        }
        //兼容没有解决方案的数据
        productBaseList.stream().forEach(product -> {
            ProductSolutionEnum solutionEnum = ProductSolutionEnum.getEnum(product.getPemcusssolType());
            product.setPemcusssolTypeName(ObjectUtil.isNotNull(solutionEnum) ? solutionEnum.getName() : "");
        });
        //申请单状态
        //当前项目不是上线后阶段:按钮状态 1，直接拆分 2，申请拆分 3，查看进度 4，重新提交
        if (project.getProjectDeliverStatus() != ProjectDeliverStatusEnums.ONLINE.getCode()
                && project.getProjectDeliverStatus() != ProjectDeliverStatusEnums.ACCEPTED.getCode()) {
            projectSplitResp.setStatus(1);
        } else {
            ProjProjectSplitProcess process = splitProcessMapper.selectLastByProjectId(req.getId());
            if (ObjectUtil.isEmpty(process) || process.getStatus() == QA_PASS.getCode()) {
                projectSplitResp.setStatus(2);
            } else if (process.getStatus() == PMO_REJECT.getCode() || process.getStatus() == QA_REJECT.getCode()
                    || process.getStatus() == CANCEL_APPLY.getCode()) {
                //驳回或者撤销的可以重新提交
                projectSplitResp.setStatus(4);
            } else {
                //提交后或者PMO通过的查看
                projectSplitResp.setStatus(3);
            }
        }
        orderBase.setProductList(productBaseList);
        projectSplitResp.setWorkOrderList(Collections.singletonList(orderBase));
        return Result.success(projectSplitResp);
    }

    /**
     * 查询可以合并项目
     *
     * @param req
     * @return
     */
    @Override
    public Result<List<ProjectSplitResp>> findMergeProject(SimpleId req) {
        ProjProjectInfo project = projectInfoMapper.selectById(req.getId());
        if (ObjectUtil.isEmpty(project)) {
            return Result.fail("项目不存在");
        }
        project.setProjectInfoId(null);
        project.setProjectName(null);
        List<ProjProjectInfo> projectInfos = projectInfoMapper.selectByParam(project);
        //过滤掉当前项目
        projectInfos.removeIf(p -> p.getProjectInfoId().equals(req.getId()));
        if (CollectionUtils.isEmpty(projectInfos)) {
            return Result.fail("不存在满足合并条件的项目");
        }
        if (ProjectDeliverStatusEnums.ONLINE.getCode().equals(project.getProjectDeliverStatus())) {
            return Result.fail("该项目已上线，不满足合并条件");
        }
        List<ProjectSplitResp> projectSplitResps = projectInfos.stream()
                .map(projectInfo -> new ProjectSplitResp(projectInfo, null, null)).collect(Collectors.toList());
        return Result.success(projectSplitResps);
    }

    /**
     * 项目拆分
     *
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result splitProject(ProjectSplitReq req) {
        log.info("拆分项目，入参:{}", JSON.toJSONString(req));
        //1.生成新工单
        Long mainProjectId = req.getMainProject().getId();
        Long managerId = req.getMainProject().getManagerId();
        ProjProjectInfo mainProject = projectInfoMapper.selectById(mainProjectId);
        SysUser currentUser = sysUserMapper.selectById(mainProject.getProjectLeaderId());
        if (ObjectUtil.isEmpty(mainProject)) {
            return Result.fail("项目不存在");
        }
        log.info("拆分项目，调用运营初始化工单");
        TokenReq baseReq = new TokenReq();
        String initResp = yunyingFeignClient.initWorkOrder(currentUser.getAccount(), baseReq);
        JSONObject initRespJson = JSONObject.parseObject(initResp);
        log.info("拆分项目，调用运营初始化工单返回，{}", initRespJson);
        if (initRespJson.getInteger("code") != 200) {
            log.error("拆分项目，调用运营初始化工单失败,{}", initRespJson.getString("msg"));
            throw new CustomException(ResultEnum.SYSTEM_ERROR);
        }
        JSONObject workOrder = initRespJson.getJSONObject("obj");
        Long newYYOrderId = workOrder.getLong("projId");
        String newYYOrderNum = workOrder.getString("projNo");
        log.info("拆分项目,调用运营初始化工单成功，新工单id:{}, 新工单Num:{}", newYYOrderId, newYYOrderNum);
        //2.创建新项目
        Date now = new Date();
        mainProject.setUpdateTime(now);
        mainProject.setUpdaterId(currentUser.getSysUserId());
        projectInfoMapper.updateById(mainProject);
        Long splitProjectId = SnowFlakeUtil.getId();
        ProjProjectInfo splitProject = new ProjProjectInfoExtend(mainProject, splitProjectId, now, managerId,
                req.getMinorProject().getName());
        splitProject.setProjectNumber(newYYOrderNum);
        //处理项目成员
        List<ProjProjectMember> members = projectMemberMapper.selectByProjectIds(
                Collections.singletonList(mainProjectId));
        List<ProjProjectMember> newMembers = members.stream()
                .map(member -> new ProjProjectMemberExtend(member, splitProjectId, managerId, now))
                .collect(Collectors.toList());
        projectMemberMapper.batchInsert(newMembers);
        log.info("拆分项目，创建新项目完成，id={}", splitProjectId);
        //3.修改工单-产品项目数据
        List<ProductBase> splitYYPList = req.getMinorProject().getProductList();
        if (CollectionUtils.isEmpty(splitYYPList)) {
            return Result.fail("拆分项目，产品数据不能为空");
        }
        List<Long> splitYYPIdList = splitYYPList.stream().map(ProductBase::getYyId).collect(Collectors.toList());
        Long splitOrderId = SnowFlakeUtil.getId();
        ProjOrderInfo mainOrder = orderInfoMapper.selectById(mainProject.getOrderInfoId());
        ProjOrderInfo splitOrder = new ProjOrderInfoExtend(mainOrder, splitOrderId, now, managerId, newYYOrderId,
                newYYOrderNum);
        orderInfoMapper.insert(splitOrder);
        splitProject.setOrderInfoId(splitOrder.getOrderInfoId());
        splitProject.setMemberInitFlag(1);
        projectInfoMapper.insert(splitProject);
        //产需对应的实施产品id
        List<ProjProductDeliverRecord> deliverRecords = deliverRecordMapper.findProductDeliverRecordByProjectInfoId(mainProjectId);
        deliverRecords = deliverRecords.stream().filter(record -> splitYYPIdList.contains(record.getYyOrderProductId()))
                .collect(Collectors.toList());
        List<Long> splitDeliverIds = deliverRecords.stream().map(ProjProductDeliverRecord::getProductDeliverId)
                .collect(Collectors.toList());
        //工单产品
        orderProductMapper.updateByProjectAndOrder(mainProjectId, mainProject.getOrderInfoId(), splitProjectId,
                splitOrderId, splitYYPIdList);
        //部署产品-arrange
        arrangeRecordMapper.updateByProjectId(mainProjectId, splitProjectId, splitYYPIdList);
        //实施产品-deliver
        deliverRecordMapper.updateByProjectId(mainProjectId, splitProjectId, splitYYPIdList);
        //授权产品-empower
        empowerRecordMapper.updateByProjectId(mainProjectId, splitProjectId, splitYYPIdList);
        log.info("拆分项目，修改工单-产品项目数据完成");
        //创建里程碑-保持项目里程碑状态
        //projectStage
        ConfigProjectStage stageParam = new ConfigProjectStage();
        stageParam.setUpgradationType(splitProject.getUpgradationType());
        stageParam.setHisFlag(0);
        if (NumberEnum.NO_1.num().equals(splitProject.getProjectType())) {
            stageParam.setMonomerFlag(NumberEnum.NO_1.num());
        }
        if (NumberEnum.NO_2.num().equals(mainProject.getProjectType())) {
            stageParam.setRegionFlag(NumberEnum.NO_1.num());
        }
        List<ConfigProjectStage> projectStageList = configProjectStageMapper.getByUpgradationByParam(stageParam);
        List<Long> ids = projectStageList.stream().map(ConfigProjectStage::getProjectStageId)
                .collect(Collectors.toList());
        List<DictProjectStageVO> stageList = dictProjectStageMapper.findProjectStageById(ids);
        SysUser finalCurrentUser = currentUser;
        stageList.stream().forEach(po -> {
            ProjProjectStageInfo proto = new ProjProjectStageInfo();
            proto.setId(SnowFlakeUtil.getId());
            proto.setCreaterId(finalCurrentUser.getSysUserId());
            proto.setCreateTime(new Date());
            proto.setUpdaterId(finalCurrentUser.getSysUserId());
            proto.setUpdateTime(new Date());
            proto.setInvalidFlag(0);
            proto.setProjectInfoId(splitProject.getProjectInfoId());
            proto.setProjectStageId(po.getId());
            proto.setProjectStageCode(po.getProjectStageCode());
            proto.setProjectStageName(po.getProjectStageName());
            proto.setOrderNo(po.getOrderNo());
            projProjectStageInfoMapper.insert(proto);
        });
        //milestone
        List<ProjMilestoneInfo> mainMilestoneInfos = milestoneInfoMapper.findByProjectInfoId(mainProjectId);
        ProjMilestoneInfoDTO msParam = new ProjMilestoneInfoDTO();
        if (mainProject.getProjectType() == 1) {
            msParam.setMonomerFlag(1);
        }
        if (mainProject.getProjectType() == 2) {
            msParam.setRegionFlag(1);
        }
        msParam.setUpgradationType(mainProject.getUpgradationType());
        //拆分的项目一定不是首期
        msParam.setHisFlag(0);
        //项目里程碑节点开始初始化 、 0903 新增需求。初始化时 增加根据工单类型过滤里程碑节点信息【自研软件与外采软件】
        // 拆分的项目一定是自研软件
        msParam.setSelfSoftwareFlag(NumberEnum.NO_1.num());
        List<ProjMilestoneInfo> splitMilestoneInfos = milestoneInfoService.getMilestoneInfoByProjectInfoId(msParam);
        splitMilestoneInfos.forEach(splitMilestoneInfo -> {
            ProjMilestoneInfo mainInfo = mainMilestoneInfos.stream()
                    .filter(main -> main.getMilestoneNodeId().equals(splitMilestoneInfo.getMilestoneNodeId()))
                    .findFirst().orElse(null);
            if (mainInfo != null) {
                BeanUtil.copyProperties(mainInfo, splitMilestoneInfo, "preNodeId", "preNodeFlag");
            }
            if (ObjectUtil.isEmpty(splitMilestoneInfo.getMilestoneStatus())) {
                splitMilestoneInfo.setMilestoneStatus(0);
            }
            splitMilestoneInfo.setMilestoneInfoId(SnowFlakeUtil.getId());
            splitMilestoneInfo.setCreaterId(finalCurrentUser.getSysUserId());
            splitMilestoneInfo.setCreateTime(new Date());
            splitMilestoneInfo.setInvalidFlag(0);
            splitMilestoneInfo.setProjectInfoId(splitProjectId);
        });
        //项目里程碑节点表插入数据
        milestoneInfoMapper.batchInsert(splitMilestoneInfos);
        log.info("拆分项目，里程碑节点数据处理完成");
        //处理调研数据-backLog、product_config、product_config_log、product_task--修改
        //增加报表、表单数据处理 proj_survey_report、proj_survey_form
        //survey_plan
        if (!CollectionUtils.isEmpty(splitDeliverIds)) {
            productBacklogMapper.updateByProjectId(mainProjectId, splitProjectId, splitDeliverIds);
            productConfigMapper.updateByProjectId(mainProjectId, splitProjectId, splitDeliverIds);
            productConfigLogMapper.updateByProjectId(mainProjectId, splitProjectId, splitDeliverIds);
            productTaskMapper.updateByProjectId(mainProjectId, splitProjectId, splitDeliverIds);
            surveyReportMapper.updateByProjectId(mainProjectId, splitProjectId, splitDeliverIds);
            surveyFormMapper.updateByProjectId(mainProjectId, splitProjectId, splitDeliverIds);
            surveyPlanMapper.updateByProjectId(mainProjectId, splitProjectId, splitDeliverIds);
            equipRecordMapper.updateByProjectId(mainProjectId, splitProjectId, splitDeliverIds);
            //处理milestone_task/milestone_task_detail数据
            //查询是否有准备工作节点对应的milestone_task数据，有的话，对应修改milestone_task_detail中产品数据
            List<ProjMilestoneTask> milestoneTasks = milestoneTaskMapper.findByProjectAndCode(mainProjectId,
                    MilestoneNodeEnum.PREPARAT_PRODUCT.getCode());
            if (!CollectionUtils.isEmpty(milestoneTasks)) {
                //插入一条新的milestone_task数据
                ProjMilestoneTask saveMileStoneTaskData = new ProjMilestoneTask();
                BeanUtil.copyProperties(milestoneTasks.get(0), saveMileStoneTaskData);
                saveMileStoneTaskData.setMilestoneTaskId(SnowFlakeUtil.getId());
                saveMileStoneTaskData.setProjectInfoId(splitProjectId);
                saveMileStoneTaskData.setCreateTime(new Date());
                saveMileStoneTaskData.setUpdateTime(new Date());
                saveMileStoneTaskData.setCreaterId(finalCurrentUser.getSysUserId());
                saveMileStoneTaskData.setUpdaterId(finalCurrentUser.getSysUserId());
                milestoneTaskMapper.insert(saveMileStoneTaskData);
                //修改milestone_task_detail中产品数据
                milestoneTaskDetailMapper.updateByPid(milestoneTasks.get(0).getMilestoneTaskId(),
                        saveMileStoneTaskData.getMilestoneTaskId(), splitDeliverIds);
            }

            // 设备调研
            List<ProjMilestoneTask> surveyDevice = milestoneTaskMapper.findByProjectAndCode(mainProjectId, MilestoneNodeEnum.SURVEY_DEVICE.getCode());
            if (!CollectionUtils.isEmpty(surveyDevice)) {
                //插入一条新的milestone_task数据
                ProjMilestoneTask saveMileStoneTaskData = new ProjMilestoneTask();
                BeanUtil.copyProperties(surveyDevice.get(0), saveMileStoneTaskData);
                saveMileStoneTaskData.setMilestoneTaskId(SnowFlakeUtil.getId());
                saveMileStoneTaskData.setProjectInfoId(splitProjectId);
                saveMileStoneTaskData.setCreateTime(new Date());
                saveMileStoneTaskData.setUpdateTime(new Date());
                saveMileStoneTaskData.setCreaterId(finalCurrentUser.getSysUserId());
                saveMileStoneTaskData.setUpdaterId(finalCurrentUser.getSysUserId());
                milestoneTaskMapper.insert(saveMileStoneTaskData);
                //修改milestone_task_detail中产品数据
                int i = milestoneTaskDetailMapper.updateByPid(surveyDevice.get(0).getMilestoneTaskId(), saveMileStoneTaskData.getMilestoneTaskId(), splitDeliverIds);
                log.info("项目拆分更新设备调研明细，oldPid={}，newPid={}，splitDeliverIds={}", surveyDevice.get(0).getMilestoneTaskId(), saveMileStoneTaskData.getMilestoneTaskId(), splitDeliverIds);
            }

            // 设备对接
            List<ProjMilestoneTask> preparatDevice = milestoneTaskMapper.findByProjectAndCode(mainProjectId, MilestoneNodeEnum.PREPARAT_DEVICE.getCode());
            if (!CollectionUtils.isEmpty(preparatDevice)) {
                //插入一条新的milestone_task数据
                ProjMilestoneTask saveMileStoneTaskData = new ProjMilestoneTask();
                BeanUtil.copyProperties(preparatDevice.get(0), saveMileStoneTaskData);
                saveMileStoneTaskData.setMilestoneTaskId(SnowFlakeUtil.getId());
                saveMileStoneTaskData.setProjectInfoId(splitProjectId);
                saveMileStoneTaskData.setCreateTime(new Date());
                saveMileStoneTaskData.setUpdateTime(new Date());
                saveMileStoneTaskData.setCreaterId(finalCurrentUser.getSysUserId());
                saveMileStoneTaskData.setUpdaterId(finalCurrentUser.getSysUserId());
                milestoneTaskMapper.insert(saveMileStoneTaskData);
                //修改milestone_task_detail中产品数据
                int i = milestoneTaskDetailMapper.updateByPid(preparatDevice.get(0).getMilestoneTaskId(), saveMileStoneTaskData.getMilestoneTaskId(), splitDeliverIds);
                log.info("项目拆分更新设备对接明细，oldPid={}，newPid={}，splitDeliverIds={}", surveyDevice.get(0).getMilestoneTaskId(), saveMileStoneTaskData.getMilestoneTaskId(), splitDeliverIds);
            }
        }
        //处理上线步骤数据
        //先判断是否已经初始化上下步骤，若没有初始化，不需要处理；若已经初始化则需要根据拆分的产品处理特殊产品数据
        log.info("拆分项目，上线步骤数据处理开始");
        int onlineStepCount = 0;
        if (mainProject.getOnlineStepFlag() == 1) {
            //患者智能-1
            List<RuleProductRuleConfig> hzzns = ruleProductRuleConfigMapper.findSpecialProduct(1);
            //合理用药-2
            List<RuleProductRuleConfig> hlyys = ruleProductRuleConfigMapper.findSpecialProduct(2);
            //健康档案浏览器-3
            List<RuleProductRuleConfig> llqs = ruleProductRuleConfigMapper.findSpecialProduct(3);
            List<Long> configStepIds = new ArrayList<>();
            //确定拆分的产品是否包含患者智能
            splitYYPIdList.stream().forEach(id -> {
                //包含
                if (hzzns.stream().anyMatch(rule -> rule.getYyProductId().equals(id))) {
                    List<ConfigOnlineStep> hzznConfigs = configOnlineStepMapper.selectList(
                            new QueryWrapper<ConfigOnlineStep>()
                                    .eq("online_step_code", "old_hzzn_stop")
                                    .or()
                                    .eq("online_step_code", "new_hzzn_start")
                    );
                    hzznConfigs.stream().forEach(config -> configStepIds.add(config.getConfigOnlineStepId()));
                }
            });
            //确定拆分的产品是否包含合理用药
            splitYYPIdList.stream().forEach(id -> {
                //包含
                if (hlyys.stream().anyMatch(rule -> rule.getYyProductId().equals(id))) {
                    List<ConfigOnlineStep> hlyyConfigs = configOnlineStepMapper.selectList(
                            new QueryWrapper<ConfigOnlineStep>()
                                    .eq("online_step_code", "reasonable_medication_records")
                    );
                    hlyyConfigs.stream().forEach(config -> configStepIds.add(config.getConfigOnlineStepId()));
                }
            });
            //确定拆分的产品是否包含健康档案浏览器
            splitYYPIdList.stream().forEach(id -> {
                //包含
                if (llqs.stream().anyMatch(rule -> rule.getYyProductId().equals(id))) {
                    List<ConfigOnlineStep> llqConfigs = configOnlineStepMapper.selectList(
                            new QueryWrapper<ConfigOnlineStep>()
                                    .eq("online_step_code", "browse_health_profile")
                    );
                    llqConfigs.stream().forEach(config -> configStepIds.add(config.getConfigOnlineStepId()));
                }
            });
            if (!CollectionUtils.isEmpty(configStepIds)) {
                onlineStepCount = onlineStepMapper.deleteByProjectAndStepIds(mainProjectId, configStepIds);
            }
        }
        log.info("拆分项目，上线步骤数据处理完成, 数据量-{}", onlineStepCount);

        // 先处理交付平台自己的数据，避免交付处理失败之后，数据回滚，但是填鸭平台、运营平台处理成功的情况
        boolean planModel = projectConfigService.isPlanModel(mainProjectId);
        // fixme 开启了小前端大后端的项目，进行项目拆分时报错
        if (planModel) {
            AddProjProjectConfigParamDTO addProjProjectConfigParamDTO = new AddProjProjectConfigParamDTO();
            addProjProjectConfigParamDTO.setProjectInfoId(splitProjectId);
            addProjProjectConfigParamDTO.setViewModel(ProjectViewModelEnum.PROJECT_PLAN);
            projectConfigService.addProjectConfig(addProjProjectConfigParamDTO);

            projProjectPlanService.updatePlanTotalAndCompleteCountByProjectAndItemCode(mainProjectId, DictProjectPlanItemEnum.SURVEY_PRODUCT);

            ProjMilestoneInfoDTO projMilestoneInfoDTO = new ProjMilestoneInfoDTO();
            projMilestoneInfoDTO.setProjectInfoId(splitProjectId);
            projMilestoneInfoService.generateMilestoneInfo(projMilestoneInfoDTO);
            projProjectPlanService.updatePlanTotalAndCompleteCountByProjectAndItemCode(splitProjectId, DictProjectPlanItemEnum.SURVEY_PRODUCT);

            List<ProjProductDeliverRecord> surveyProductDeliverRecord = deliverRecordService.getSurveyProductDeliverRecord(mainProjectId, true);
            int finishedProductCount = surveyPlanService.getFinishedProductCount(mainProjectId);
            // 需要调研的产品总数不等于已完成的调研总数
            if (surveyProductDeliverRecord.size() != finishedProductCount && finishedProductCount != 0) {
                projProjectPlanService.updatePlanAndTodoTaskStatusByProjectAndItemCode(mainProjectId, DictProjectPlanItemEnum.SURVEY_PRODUCT, ProjectPlanStatusEnum.UNDERWAY);
            }

            List<ProjProductDeliverRecord> splitProjectIdsurveyProductDeliverRecord = deliverRecordService.getSurveyProductDeliverRecord(splitProjectId, true);
            int splitProjectIdfinishedProductCount = surveyPlanService.getFinishedProductCount(splitProjectId);
            // 需要调研的产品总数不等于已完成的调研总数
            if (splitProjectIdsurveyProductDeliverRecord.size() != splitProjectIdfinishedProductCount && splitProjectIdfinishedProductCount != 0) {
                projProjectPlanService.updatePlanAndTodoTaskStatusByProjectAndItemCode(splitProjectId, DictProjectPlanItemEnum.SURVEY_PRODUCT, ProjectPlanStatusEnum.UNDERWAY);
            }
        }
        //调用tduck接口处理调研数据
        SplitAndMergeReq tduckReq = new SplitAndMergeReq();
        tduckReq.setNewProjectInfoId(splitProjectId);
        tduckReq.setOldProjectInfoId(mainProjectId);
        tduckReq.setYyProductIdList(splitDeliverIds);
        String splitStr = tDuckFeignClient.splitProject(tduckReq);
        JSONObject splitJson = JSONObject.parseObject(splitStr);
        log.info("拆分项目，TDUCK调研返回结果,{}", JSON.toJSONString(splitJson));
        int code = splitJson.getInteger("code");
        if (TDUCK_SUCCESS_CODE != code) {
            log.error("拆分项目，TDUCK调研结果相关数据处理失败,{}", splitJson.getString("msg"));
            throw new CustomException(ResultEnum.FAIL);
        }
        log.info("拆分项目，调研结果相关数据处理完成");
        //8.生成新工单-调用运营平台
        OrderSplitReq splitReq = new OrderSplitReq();
        List<ProductBase> splitPrductList = req.getMinorProject().getProductList();
        splitReq.setNewProjId(newYYOrderId.toString());
        splitReq.setNewProjNo(newYYOrderNum);
        splitReq.setProjId(splitPrductList.get(0).getWoId().toString());
        splitReq.setProducts(
                splitPrductList.stream().map(product -> new ProductBaseReq(product)).collect(Collectors.toList()));
        log.info("拆分项目，调用运营平台拆分工单参数，{}", JSON.toJSONString(splitReq));
        String yyResp = yunyingFeignClient.splitWorkOrder(currentUser.getAccount(), splitReq);
        JSONObject yyRespJson = JSONObject.parseObject(yyResp);
        log.info("拆分项目，调用运营平台拆分工单返回，{}", yyRespJson);
        if (yyRespJson.getInteger("code") != 200) {
            log.error("拆分项目，运营平台拆分工单失败,{}", yyRespJson.getString("msg"));
            throw new CustomException(ResultEnum.SYSTEM_ERROR);
        }
        log.info("拆分项目成功!");
        //9调用老项目-项目拆分接口
        ImspProjectSplitReq imspProjectSplitReq = new ImspProjectSplitReq();
        BeanUtil.copyProperties(req, imspProjectSplitReq);
        imspProjectSplitReq.setNewYYOrderId(newYYOrderId);
        imspProjectSplitReq.setNewYYOrderNum(newYYOrderNum);
        //处理参数-区域单体/新客户/项目id对照
        imspProjectSplitReq.getMainProject()
                .setIsArea("1".equals(imspProjectSplitReq.getMainProject().getIsArea()) ? "Y" : "F");
        imspProjectSplitReq.getMinorProject()
                .setIsArea("1".equals(imspProjectSplitReq.getMinorProject().getIsArea()) ? "Y" : "F");
        imspProjectSplitReq.getMinorProject().setCustomerType("xkh");
        List<TmpProjectNewVsOld> tmpProjectNewVsOlds = tmpProjectNewVsOldMapper.selectByNewProjectIds(
                Collections.singletonList(mainProjectId));
        imspProjectSplitReq.getMainProject().setId(tmpProjectNewVsOlds.get(0).getOldProjectInfoId());
        SysUser manager = sysUserMapper.selectById(managerId);
        imspProjectSplitReq.getMainProject().setManagerId(Long.valueOf(manager.getUserYunyingId()));
        imspProjectSplitReq.getMinorProject().setManagerId(Long.valueOf(manager.getUserYunyingId()));
        String auth = CsmSignUtil.getHeader();
        String resp = oldImspFeignClient.splitProject(imspProjectSplitReq, auth);
        JSONObject respJson = JSONObject.parseObject(resp);
        log.info("拆分项目，调用老项目拆分项目返回，{}", respJson);
        if (!respJson.getBoolean("success")) {
            log.error("调用老项目拆分项目报错,{}", respJson.getString("msg"));
            throw new CustomException("调用老项目拆分项目报错");
        }
        //拆分项目后处理新老项目关联关系
        ImspProjectSplitResp imspProjectSplitResp = JSONObject.parseObject(respJson.getString("data"),
                ImspProjectSplitResp.class);
        final TmpProjectNewVsOld tmpProjectNewVsOld = getTmpData(imspProjectSplitResp, splitProjectId, mainProject);
        //判断原项目派工项目还是迁移项目
        List<TmpProjectNewVsOld> tmpProjectNewVsOldList =
                tmpProjectNewVsOldMapper.selectByNewProjectIds(Collections.singletonList(mainProjectId));
        if (tmpProjectNewVsOldList.size() > 0) {
            //迁移项目
            tmpProjectNewVsOld.setNewProjectSource(tmpProjectNewVsOldList.get(0).getNewProjectSource());
        }
        tmpProjectNewVsOldMapper.insert(tmpProjectNewVsOld);
        //记录日志
        saveLogs(JSONObject.toJSONString(req), "拆分项目");
        saveLogs(JSONObject.toJSONString(splitReq), "拆分项目-调运营");
        // 判断项目是否是已调研阶段, 处理入驻规则数据, 会发消息推送给销售
        if (splitProject.getProjectDeliverStatus() == ProjectDeliverStatusEnums.RESEARCHING.getCode().intValue()) {
            // 创建新项目规则
            settlementRuleService.splitProject(splitProject);
            // 发送消息通知
            settlementRuleService.splitProjectSendMessage(splitProject);
            // 更新通知销售入驻申请节点状态为未完成
            settlementCheckService.updateMilestoneProjEntryNotComplete(splitProject.getProjectInfoId());
        }
        return Result.success();
    }

    //处理新老项目对照
    private @NotNull
    TmpProjectNewVsOld getTmpData(ImspProjectSplitResp imspProjectSplitResp, Long splitProjectId,
                                  ProjProjectInfo mainProject) {
        TmpProjectNewVsOld tmpProjectNewVsOld = new TmpProjectNewVsOld();
        tmpProjectNewVsOld.setOldCustomId(imspProjectSplitResp.getCustomerId());
        tmpProjectNewVsOld.setOldProjectInfoId(imspProjectSplitResp.getProjectId());
        tmpProjectNewVsOld.setOldCustomInfoId(imspProjectSplitResp.getCustomerInfoId());
        tmpProjectNewVsOld.setNewProjectInfoId(splitProjectId);
        tmpProjectNewVsOld.setNewCustomInfoId(mainProject.getCustomInfoId());
        tmpProjectNewVsOld.setProjectNewVsOldId(SnowFlakeUtil.getId());
        tmpProjectNewVsOld.setNewProjectSource(1);
        return tmpProjectNewVsOld;
    }

    /**
     * 项目合并
     *
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result mergeProject(ProjectMergeReq req) {
        log.info("合并项目，入参 :{}", JSON.toJSONString(req));
        Long mainProjectId = req.getMainProjectId();
        Long minorProjectId = req.getMinorProjectId();
        if (mainProjectId.equals(minorProjectId)) {
            log.error("合并项目，主项目和子项目id相同");
            throw new CustomException(ResultEnum.PARAM_INVALID);
        }
        SysUserVO currentUser = userHelper.getCurrentUser();
        //2.删除子项目
        projectInfoMapper.deleteById(minorProjectId);
        //3.修改工单-产品项目数据
        //删除工单
        ProjProjectInfo mainProject = projectInfoMapper.selectByPrimaryKey(mainProjectId);
        ProjProjectInfo minorProject = projectInfoMapper.selectByPrimaryKey(minorProjectId);
        Long mainOrderInfoId = mainProject.getOrderInfoId();
        Long minorOrderInfoId = minorProject.getOrderInfoId();
        orderInfoMapper.deleteById(minorProject.getOrderInfoId());
        //修改工单产品
        orderProductMapper.updateByProjectAndOrder(minorProjectId, minorOrderInfoId, mainProjectId, mainOrderInfoId,
                null);
        //部署产品-arrange
        arrangeRecordMapper.updateByProjectId(minorProjectId, mainProjectId, null);
        //实施产品-deliver
        deliverRecordMapper.updateByProjectId(minorProjectId, mainProjectId, null);
        //授权产品-empower
        empowerRecordMapper.updateByProjectId(minorProjectId, mainProjectId, null);
        //处理调研数据-backLog、product_config、product_config_log、product_task--修改
        //增加报表、表单数据处理 proj_survey_report、proj_survey_form
        //surveyplan
        productBacklogMapper.updateByProjectId(minorProjectId, mainProjectId, null);
        productConfigMapper.updateByProjectId(minorProjectId, mainProjectId, null);
        productConfigLogMapper.updateByProjectId(minorProjectId, mainProjectId, null);
        productTaskMapper.updateByProjectId(minorProjectId, mainProjectId, null);
        surveyReportMapper.updateByProjectId(minorProjectId, mainProjectId, null);
        surveyFormMapper.updateByProjectId(minorProjectId, mainProjectId, null);
        surveyPlanMapper.updateByProjectId(minorProjectId, mainProjectId, null);
        //更新设备记录
        equipRecordMapper.updateByProjectId(minorProjectId, mainProjectId, null);
        equipFilesMapper.updateByProjectId(minorProjectId, mainProjectId);
        //todo 处理milestone_task/milestone_task_detail数据
        //查询是否有准备工作节点对应的milestone_task数据，有的话，对应修改milestone_task_detail中产品数据
        List<ProjMilestoneTask> mainDatas = milestoneTaskMapper.findByProjectAndCode(mainProjectId,
                MilestoneNodeEnum.PREPARAT_PRODUCT.getCode());
        List<ProjMilestoneTask> minorDatas = milestoneTaskMapper.findByProjectAndCode(minorProjectId,
                MilestoneNodeEnum.PREPARAT_PRODUCT.getCode());
        if (!CollectionUtils.isEmpty(minorDatas)) {
            Long newId;
            if (CollectionUtils.isEmpty(mainDatas)) {
                //主项目没有产品准备待办，需要新增
                ProjMilestoneTask mainData = new ProjMilestoneTask();
                BeanUtil.copyProperties(minorDatas.get(0), mainData);
                newId = SnowFlakeUtil.getId();
                mainData.setMilestoneTaskId(newId);
                mainData.setProjectInfoId(mainProjectId);
                mainData.setCreateTime(new Date());
                mainData.setUpdateTime(new Date());
                mainData.setCreaterId(userHelper.getCurrentUser().getSysUserId());
                mainData.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
                milestoneTaskMapper.insert(mainData);
            } else {
                newId = mainDatas.get(0).getMilestoneTaskId();
            }
            //修改milestone_task_detail中产品数据
            milestoneTaskDetailMapper.updateByPid(minorDatas.get(0).getMilestoneTaskId(), newId, null);
        }
        //处理上线步骤，合并后默认将主项目初始化状态设置为为初始化，重新初始化
        log.info("合并项目，修改上线步骤状态为未初始化");
        ProjProjectInfo onlineStepStatusParam = new ProjProjectInfo();
        onlineStepStatusParam.setOnlineStepFlag(0);
        onlineStepStatusParam.setProjectInfoId(mainProjectId);
        int count = projectInfoMapper.updateByPrimaryKeySelective(onlineStepStatusParam);
        log.info("合并项目，修改上线步骤状态为未初始化完成,影响行数-{}", count);

        // 先处理交付平台自己的数据，避免交付处理失败之后，数据回滚，但是运营平台处理成功的情况
        boolean planModel = projectConfigService.isPlanModel(mainProjectId);
        if (planModel) {
            // 产品业务调研项目计划
            projProjectPlanService.updatePlanTotalAndCompleteCountByProjectAndItemCode(mainProjectId, DictProjectPlanItemEnum.SURVEY_PRODUCT);

            List<ProjProductDeliverRecord> surveyProductDeliverRecord = deliverRecordService.getSurveyProductDeliverRecord(mainProjectId, true);
            int finishedProductCount = surveyPlanService.getFinishedProductCount(mainProjectId);
            // 需要调研的产品总数不等于已完成的调研总数
            if (surveyProductDeliverRecord.size() != finishedProductCount && finishedProductCount != 0) {
                projProjectPlanService.updatePlanAndTodoTaskStatusByProjectAndItemCode(mainProjectId, DictProjectPlanItemEnum.SURVEY_PRODUCT, ProjectPlanStatusEnum.UNDERWAY);
            }
            if (surveyProductDeliverRecord.size() == finishedProductCount) {
                projProjectPlanService.updatePlanAndTodoTaskStatusByProjectAndItemCode(mainProjectId, DictProjectPlanItemEnum.SURVEY_PRODUCT, ProjectPlanStatusEnum.FINISHED);
            }

        }
        //调用tduck接口处理调研数据
        SplitAndMergeReq tDuckReq = new SplitAndMergeReq();
        tDuckReq.setNewProjectInfoId(mainProjectId);
        tDuckReq.setOldProjectInfoId(minorProjectId);
        String mergeStr = tDuckFeignClient.mergeProject(tDuckReq);
        JSONObject mergeJson = JSONObject.parseObject(mergeStr);
        log.info("合并项目，TDUCK调研返回结果,{}", JSON.toJSONString(mergeJson));
        int code = mergeJson.getInteger("code");
        if (TDUCK_SUCCESS_CODE != code) {
            log.error("合并项目，TDUCK调研结果相关数据处理失败,{}", mergeJson.getString("msg"));
            throw new CustomException(ResultEnum.FAIL);
        }
        log.info("合并项目，调研结果相关数据处理完成!");
        //9调用老项目-项目合并接口
        ImspProjectMergeReq imspProjectMergeReq = new ImspProjectMergeReq();
        List<Long> projectIds = new ArrayList<>();
        projectIds.add(mainProjectId);
        projectIds.add(minorProjectId);
        List<TmpProjectNewVsOld> tmpProjectNewVsOlds = tmpProjectNewVsOldMapper.selectByNewProjectIds(projectIds);
        if (tmpProjectNewVsOlds.size() != 2) {
            log.error("合并项目失败，新老项目数据对照有误");
            throw new CustomException(ResultEnum.FAIL);
        }
        imspProjectMergeReq.setMainProjectId(
                tmpProjectNewVsOlds.stream().filter(o -> o.getNewProjectInfoId().equals(mainProjectId)).findFirst()
                        .get().getOldProjectInfoId());
        imspProjectMergeReq.setMinorProjectId(
                tmpProjectNewVsOlds.stream().filter(o -> o.getNewProjectInfoId().equals(minorProjectId)).findFirst()
                        .get().getOldProjectInfoId());
        String auth = CsmSignUtil.getHeader();
        String resp = oldImspFeignClient.mergeProject(imspProjectMergeReq, auth);
        JSONObject respJson = JSONObject.parseObject(resp);
        if (!respJson.getBoolean("success")) {
            log.error("调用老项目合并项目报错,{}", respJson.getString("msg"));
            throw new CustomException("调用老项目合并项目报错");
        }
        //7.调用运营平台合并工单
        ProjOrderInfo mainOrder = orderInfoMapper.selectByPrimaryKey(mainOrderInfoId);
        ProjOrderInfo minorOrder = orderInfoMapper.selectByPrimaryKey(minorOrderInfoId);
        OrderMergeReq mergeReq = new OrderMergeReq(mainOrder.getYyOrderId(), minorOrder.getYyOrderId());
        log.info("合并项目，调用运营平台拆分工单参数，{}", JSON.toJSONString(mergeReq));
        String yyResp = yunyingFeignClient.mergeWorkOrder(currentUser.getAccount(), mergeReq);
        JSONObject yyRespJson = JSONObject.parseObject(yyResp);
        if (yyRespJson.getInteger("code") != 200) {
            log.error("合并项目，运营平台合并工单失败,{}", yyRespJson.getString("msg"));
            throw new CustomException(ResultEnum.FAIL);
        }
        log.info("合并项目,运营平台合并工单成功，主工单id:{}", mainProjectId);
        //8记录日志
        saveLogs(JSONObject.toJSONString(req), "合并项目");
        return Result.success();
    }

    /**
     * 保存日志
     *
     * @param datas
     */
    private void saveLogs(String datas, String actionName) {
        ProjSyncApiLogs syncApiLogsEntity = new ProjSyncApiLogs();
        syncApiLogsEntity.setId(SnowFlakeUtil.getId());
        syncApiLogsEntity.setActionname(actionName);
        syncApiLogsEntity.setDatas(datas);
        syncApiLogsMapper.insert(syncApiLogsEntity);
    }

    @Override
    public List<Long> getProjectInfoIdByOrderId(Long yyOrderId) {
        return projectInfoMapper.getProjectInfoIdByOrderId(yyOrderId);
    }

    /**
     * 提取代码  将用户对于项目的筛选权限提取出来
     *
     * @param dto
     * @return
     */
    public ProjProjectInfoDTO handleProjProjectInfoDTO(ProjProjectInfoDTO dto) {
        SysUserVO sysUserVO = userHelper.getCurrentUser();
        List<SysRoleDTO> resRoles = sysRoleMapper.selectRoleByUserId(sysUserVO.getSysUserId());
        // 当前登录用户的角色编码
        Set<String> currentUserRoleCodeSet = resRoles.stream().map(SysRoleDTO::getRoleCode).collect(Collectors.toSet());
        //if (Arrays.stream(ROLE_CODE_ADMIN).anyMatch(currentUserRoleCodeSet::contains) || Arrays.stream
        // (ROLE_CODE_DEPT).anyMatch(currentUserRoleCodeSet::contains)) {
        if (Arrays.stream(ROLE_CODE_ADMIN).anyMatch(currentUserRoleCodeSet::contains)) {
            dto.setProjectMemberId(null);
        } else {
            dto.setProjectMemberId(sysUserVO.getSysUserId());
        }

        // 当前登录用户单独指定的拥有查询权限的部门ID
        List<String> dataRangeList = new ArrayList<>();
        for (SysRoleDTO sysRoleDTO : resRoles) {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(sysRoleDTO.getDataRange())) {
                dataRangeList.addAll(Arrays.asList(sysRoleDTO.getDataRange().split(",")));
            }
        }

        // 如果单独指定的拥有查询权限的部门ID不是空的，遍历获取所有对应部门以及对应部门的的下属部门
        if (!CollectionUtils.isEmpty(dataRangeList)) {
            // 将部门ID转为Long类型
            Set<Long> needQueryDeptIdSet = dataRangeList.stream().map(Long::valueOf).collect(Collectors.toSet());

            // 所有部门
            List<SysDept> allDeptList = sysDeptMapper.selectList(new QueryWrapper<SysDept>().eq("is_deleted", 0));

            List<SysDept> needQueryDept = getDeptList(needQueryDeptIdSet, allDeptList);

            List<Long> collect = needQueryDept.stream().map(SysDept::getDeptYunyingId).distinct().collect(Collectors.toList());
            log.info("获取项目信息时，获取查询部门以及下属部门={}", collect);

            dto.setDataRange(collect);
        }
        return dto;
    }

    // 获取给定部门ID获取所有的对应部门以及下属部门（包括该部门本身）
    public List<SysDept> getDeptList(Set<Long> finalQuery, List<SysDept> allCategories) {
        List<SysDept> result = new ArrayList<>();
        for (Long id : finalQuery) {
            findDeptList(id, result, allCategories);
        }
        return result;
    }

    // 递归查找下级部门
    private void findDeptList(Long id, List<SysDept> result, List<SysDept> allCategories) {
        for (SysDept category : allCategories) {
            if (id.equals(category.getPid())) {
                result.add(category); // 找到子分类，加入结果
                findDeptList(category.getDeptYunyingId(), result, allCategories); // 递归查找该子分类的下级分类
            }
        }
    }

    /**
     * 一键批量同步四大模块所有配置到云健康
     *
     * @param projectInfoId
     * @return
     */
    @Override
    public Result<FindProjectResp> syncCommConfigArea(Long projectInfoId) {
        log.info("一键批量同步四大模块所有配置到云健康：{}", projectInfoId);
        ProjProjectInfo projectInfo = projectInfoMapper.selectById(projectInfoId);
        if (ObjectUtil.isEmpty(projectInfo) || projectInfo.getProjectType() != ProjectTypeEnums.REGION.getCode()) {
            return Result.fail(ResultEnum.PARAM_INVALID);
        }
        List<TmpProjectNewVsOld> projectNewVsOldList = tmpProjectNewVsOldMapper.selectByNewProjectIds(
                Collections.singletonList(projectInfoId));
        if (CollectionUtils.isEmpty(projectNewVsOldList)) {
            return Result.fail(ResultEnum.NO_DATA);
        }
        Long oldProjectId = projectNewVsOldList.get(0).getOldProjectInfoId();
        String auth = CsmSignUtil.getHeader();
        String resp = oldImspFeignClient.syncCommConfigArea(oldProjectId, auth);
        JSONObject respJson = JSONObject.parseObject(resp);
        if (!respJson.getBoolean("success")) {
            log.error("调用老项目一键导配置报错,{}", respJson.getString("msg"));
            throw new CustomException("调用老项目一键导配置报错");
        }
        return Result.success();
    }

    /**
     * 项目工具==查询项目信息
     *
     * @param dto
     * @return
     */
    @Override
    public Result<List<ProjectToolsOptionsForProjectInfoVO>> selectProjectToolsOptionsForProjectInfo(
            ProjProjectInfoDTO dto) {
        Result<FindProjectResp> projectInfo = this.findProjectInfo(dto);
        // 判断查询的数据是否存在值。当存在值的时候 重新赋值给VO
        List<ProjectToolsOptionsForProjectInfoVO> projectToolsOptionsForProjectInfoVOS = new ArrayList<>();
        if (projectInfo.getData() != null) {
            for (ProjProjectInfoVO projectInfoVO1 : projectInfo.getData().getSingleProjectList()) {
                ProjectToolsOptionsForProjectInfoVO projectToolsOptionsForProjectInfoVO
                        = new ProjectToolsOptionsForProjectInfoVO();
                projectToolsOptionsForProjectInfoVO.setProjectId(projectInfoVO1.getProjectInfoId());
                projectToolsOptionsForProjectInfoVO.setProjectName(projectInfoVO1.getProjectName());
                // 判断项目工具中的部分工具是否涉及到验收问题，禁止点击
                projectToolsOptionsForProjectInfoVO = infoOnlineForOnlintime(projectToolsOptionsForProjectInfoVO,
                        projectInfoVO1.getProjectInfoId());
                projectToolsOptionsForProjectInfoVO.setIsArea(0);
                projectToolsOptionsForProjectInfoVO.setProjectAndNumber(
                        projectInfoVO1.getProjectName() + "--" + projectInfoVO1.getProjectNumber());
                projectToolsOptionsForProjectInfoVOS.add(projectToolsOptionsForProjectInfoVO);
            }
            for (ProjProjectInfoVO projectInfoVO1 : projectInfo.getData().getRegionProjectList()) {
                ProjectToolsOptionsForProjectInfoVO projectToolsOptionsForProjectInfoVO
                        = new ProjectToolsOptionsForProjectInfoVO();
                projectToolsOptionsForProjectInfoVO.setProjectId(projectInfoVO1.getProjectInfoId());
                projectToolsOptionsForProjectInfoVO.setProjectName(projectInfoVO1.getProjectName());
                projectToolsOptionsForProjectInfoVO.setIsArea(1);
                // 判断项目工具中的部分工具是否涉及到验收问题，禁止点击
                projectToolsOptionsForProjectInfoVO = infoOnlineForOnlintime(projectToolsOptionsForProjectInfoVO,
                        projectInfoVO1.getProjectInfoId());
                projectToolsOptionsForProjectInfoVO.setProjectAndNumber(
                        projectInfoVO1.getProjectName() + "--" + projectInfoVO1.getProjectNumber());
                projectToolsOptionsForProjectInfoVOS.add(projectToolsOptionsForProjectInfoVO);
            }
            return Result.success(projectToolsOptionsForProjectInfoVOS);
        } else {
            return Result.fail("项目数据为空");
        }
    }

    /**
     * 判断项目是否上线---- 其他逻辑（赵飞）
     *
     * @param projectInfoId
     * @return
     */
    ProjectToolsOptionsForProjectInfoVO infoOnlineForOnlintime(ProjectToolsOptionsForProjectInfoVO vo,
                                                               Long projectInfoId) {
        // 当前项目的实施地客户下，只要首期进行了验收
        // 那与当前项目的实施类型一致的项目 都不允许进行制作（根据 上线超过三十天 并且 limmit中的标识限制判断） **** 必须两个条件都满足
        // 1、查询当前项目的实施地客户
        ProjProjectInfo projProjectInfo = projectInfoMapper.selectById(projectInfoId);
        // 2、根据实施类型 查询当前实施地客户下的项目
        List<ProjProjectInfo> projProjectInfos = projectInfoMapper.selectList(new QueryWrapper<ProjProjectInfo>()
                .eq("upgradation_type", projProjectInfo.getUpgradationType())
                .eq("custom_info_id", projProjectInfo.getCustomInfoId())
        );
        // 3、判断首期项目是否进行了验收以及是否超过30天
        List<ProjProjectInfo> projProjectInfos1 = projectInfoMapper.selectList(new QueryWrapper<ProjProjectInfo>()
                .eq("project_type", projProjectInfo.getProjectType())
                .eq("custom_info_id", projProjectInfo.getCustomInfoId())
                .eq("his_flag", 1)
        );
        ProjProjectInfo projProjectInfo1 = null;
        if (CollectionUtil.isNotEmpty(projProjectInfos1)) {
            projProjectInfo1 = projProjectInfos1.get(0);
        } else {
            vo.setOnline(0);
            vo.setInterfaceLimitFlag(0);
            vo.setReportLimitFlag(0);
            return vo;
        }
        // 4、判断验收上线标识赋值
        if (ObjectUtil.isNotEmpty(projProjectInfo1) && ObjectUtil.isNotEmpty(projProjectInfo1.getAcceptTime())) {
            // 计算两个日期之间的天数差
            long daysDifference = calculateDaysDifference(new Date(), projProjectInfo1.getAcceptTime());
            if (daysDifference >= 30) {
                vo.setOnline(1);
            } else {
                vo.setOnline(0);
            }
        } else {
            vo.setOnline(0);
        }
        // 当没有首期的时候 不进行校验
        if (ObjectUtil.isEmpty(projProjectInfo1)) {
            vo.setInterfaceLimitFlag(0);
            vo.setReportLimitFlag(0);
            return vo;
        }
        // 5、判断是否限制制作 *******  判断首期项目是否可制作 而不是当前项目
        // 5.1、判断当前项目是否在这个实施类型下
        List<Long> projectList =
                projProjectInfos.stream().map(ProjProjectInfo::getProjectInfoId).collect(Collectors.toList());
        if (projectList.contains(projectInfoId)) {
            Integer interfaceFlag = baseQueryService.queryAcceptanceExceedTimeLimit(projectInfoId, null, 0);
            Integer reportFlag = baseQueryService.queryAcceptanceExceedTimeLimit(projectInfoId, null, 2);
            // 1、判断是否限制制作接口
            if (interfaceFlag == 1) {
                vo.setInterfaceLimitFlag(1);
            } else {
                vo.setInterfaceLimitFlag(0);
            }
            // 2、判断是否限制制作报表
            if (reportFlag == 1) {
                vo.setReportLimitFlag(1);
            } else {
                vo.setReportLimitFlag(0);
            }
/*
            // 5.2、查询医院数据根据项目id
            SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
            selectHospitalDTO.setProjectInfoId(projProjectInfo1.getProjectInfoId());
            List<ProjHospitalInfo> hospitalInfoList = hospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);
            // 云健康医院id组合成 新的list集合
            List<Long> cloudHospitalIdList =
                    hospitalInfoList.stream().map(ProjHospitalInfo::getCloudHospitalId).filter(Objects::nonNull)
                            .collect(Collectors.toList());
            List<TmpHospitalLimit> tmpHospitalLimits = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(cloudHospitalIdList)) {
                // 查询表中的数据是否包含这些医院数据，当包含时  上线标识为1
                tmpHospitalLimits = tmpHospitalLimitMapper.selectList(
                        new QueryWrapper<TmpHospitalLimit>()
                                .in("cloud_hospital_id", cloudHospitalIdList)
                );
            }
            // limit中的数据判断报表、表单是否可制作
            if (CollectionUtil.isNotEmpty(tmpHospitalLimits)) {
                // 1、判断是否限制制作接口
                if (tmpHospitalLimits.stream()
                        .anyMatch(tmpHospitalLimit -> tmpHospitalLimit.getInterfaceLimitFlag() == 1)) {
                    vo.setInterfaceLimitFlag(1);
                } else {
                    vo.setInterfaceLimitFlag(0);
                }
                // 2、判断是否限制制作报表
                if (tmpHospitalLimits.stream()
                        .anyMatch(tmpHospitalLimit -> tmpHospitalLimit.getReportLimitFlag() == 1)) {
                    vo.setReportLimitFlag(1);
                } else {
                    vo.setReportLimitFlag(0);
                }
            } else {
                vo.setInterfaceLimitFlag(0);
                vo.setReportLimitFlag(0);
            }*/
        }
        return vo;

    }

    @Override
    public List<ProjProjectInfo> getProjectInfoByCustomerInfoId(Long customerInfoId) {
        LambdaQueryWrapper<ProjProjectInfo> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ProjProjectInfo::getCustomInfoId, customerInfoId)
                .eq(ProjProjectInfo::getIsDeleted, 0);
        return projectInfoMapper.selectList(lqw);
    }

    /**
     * 说明: 新项目历程备初始化时调用基础数据初始化接口
     *
     * @param projectInfoId
     * @return:void
     * @author: Yhongmin
     * @createAt: 2024/7/19 15:14
     * @remark: Copyright
     */
    @Override
    public void addDictData(Long projectInfoId) {
        TmpProjectNewVsOld tmpProjectNewVsOld = tmpProjectNewVsOldMapper.selectOne(
                new QueryWrapper<TmpProjectNewVsOld>().eq("new_project_info_id", projectInfoId));
        if (tmpProjectNewVsOld == null) {
            return;
        }
        String auth = CsmSignUtil.getHeader();
        oldImspFeignClient.addDictData(tmpProjectNewVsOld.getOldProjectInfoId(), auth);
    }

    /**
     * 【分院模式】  该项目是否 是非首期的分院实施模式【true:是；false: 否】
     *
     * @param projectInfoId 项目id
     * @return Boolean
     */
    @Override
    public Boolean isBranchHospital(Long projectInfoId) {
        try {
            ProjProjectInfo projProjectInfo = projectInfoMapper.selectById(projectInfoId);
            if (projProjectInfo.getHisFlag() != 1) {
                // 查询分院模式的产品有哪些
                List<RuleProductRuleConfig> ruleProductRuleConfigs = ruleProductRuleConfigMapper.selectList(
                        new QueryWrapper<RuleProductRuleConfig>()
                                .eq("branch_hospital_product_flag", 1)
                );
                List<Long> branchHospitalProductIds =
                        ruleProductRuleConfigs.stream().map(RuleProductRuleConfig::getYyProductId)
                                .collect(Collectors.toList());
                // 查询该项目的派工产品数据
                List<ProjOrderProduct> projOrderProducts = orderProductMapper.selectList(
                        new QueryWrapper<ProjOrderProduct>()
                                .eq("project_info_id", projectInfoId)
                );
                List<Long> orderProductIds =
                        projOrderProducts.stream().map(ProjOrderProduct::getYyOrderProductId)
                                .collect(Collectors.toList());
                return containsAny(orderProductIds, branchHospitalProductIds);
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error("是非首期的分院实施模式查询失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            throw new RuntimeException("是非首期的分院实施模式查询失败");
        }

    }

    /**
     * 后台管理-创建自定义项目
     *
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result createCustomProject(CustomProjectReq req) {
        //拼装参数
        SyncContractDTO syncContractDTO = dealParam(req);
        log.info("自定义创建项目参数:{}", JSONObject.toJSONString(syncContractDTO));
        //调用老平台创建项目接口
        String auth = CsmSignUtil.getHeader();
        String respStr = oldImspFeignClient.syncWorkOrder(syncContractDTO, auth);
        log.info("自定义创建项目返回结果:{}", respStr);
        // 记录操作日志
        ProjManualCreateLog logEntity = new ProjManualCreateLog();
        if (ObjectUtil.isNotNull(syncContractDTO.getPrincipalCustomer())) {
            logEntity.setPrincipalCustomerName(syncContractDTO.getPrincipalCustomer().getCustomerName());
        }
        if (ObjectUtil.isNotNull(syncContractDTO.getCustomerInfo())) {
            logEntity.setCustomerName(syncContractDTO.getCustomerInfo().getCustomerName());
        }
        if (ObjectUtil.isNotNull(syncContractDTO.getProjectType())) {
            ProjectTypeEnums projectTypeEnums = ProjectTypeEnums.getEnum(syncContractDTO.getProjectType());
            if (ObjectUtil.isNotNull(projectTypeEnums)) {
                logEntity.setProjectTypeName(projectTypeEnums.getName());
            }
        }
        if (ObjectUtil.isNotNull(syncContractDTO.getUpgradationType())) {
            ProjectUpgradationTypeEnums projectUpgradationTypeEnums = ProjectUpgradationTypeEnums.getEnum(
                    syncContractDTO.getUpgradationType());
            if (ObjectUtil.isNotNull(projectUpgradationTypeEnums)) {
                logEntity.setUpgradationTypeName(projectUpgradationTypeEnums.getName());
            }
        }
        if (ObjectUtil.isNotNull(req.getProjectTeamId())) {
            SysDept sysDept = sysDeptMapper.selectById(req.getProjectTeamId());
            if (ObjectUtil.isNotNull(sysDept)) {
                logEntity.setProjectTeamName(sysDept.getDeptName());
            }
        }
        if (CollUtil.isNotEmpty(req.getOrderProductList())) {
            logEntity.setOrderProductIds(
                    req.getOrderProductList().stream().map(d -> d.getId().toString()).collect(Collectors.joining(",")));
            logEntity.setOrderProductNames(
                    req.getOrderProductList().stream().map(d -> d.getName()).collect(Collectors.joining(",")));
        }
        logEntity.setProjManualCreateLogId(SnowFlakeUtil.getId());
        logEntity.setPrincipalCustomerInfoId(req.getPrincipalCustomInfoId());
        logEntity.setCustomerInfoId(req.getCustomInfoId());
        logEntity.setProjectName(req.getProjectName());
        logEntity.setProjectType(req.getProjectType());
        logEntity.setUpgradationType(req.getUpgradationType());
        logEntity.setProjectTeamId(req.getProjectTeamId());
        logEntity.setProjectLeaderAccount(req.getProjectLeaderAccount());
        logEntity.setSalesPersonAccount(req.getSalesPersonAccount());
        logEntity.setOldApiRequest(StrUtil.maxLength(JSON.toJSONString(syncContractDTO, false), 1800));
        logEntity.setOldApiResponse(StrUtil.maxLength(respStr, 800));
        SysUserVO currentUser = userHelper.getCurrentUser();
        logEntity.setOperaterUserId(currentUser.getSysUserId());
        logEntity.setOperaterUserName(currentUser.getUserName());
        logEntity.setOperateTime(new Date());
        logEntity.setCreaterId(currentUser.getSysUserId());
        logEntity.setCreateTime(new Date());
        logEntity.setUpdaterId(currentUser.getSysUserId());
        logEntity.setUpdateTime(new Date());
        projManualCreateLogMapper.insert(logEntity);
        return Result.success();
    }

    private SyncContractDTO dealParam(CustomProjectReq req) {
        Long principalId = req.getPrincipalCustomInfoId();
        Long customInfoId = req.getCustomInfoId();
        ProjContractCustomInfo principalCustomInfo = contractCustomInfoMapper.selectByPrimaryKey(principalId);
        //处理主客户信息
        CustomerDTO principalCusDTO = new CustomerDTO();
        principalCusDTO.setAnnualIncome(0);
        principalCusDTO.setHospitalType("1");
        principalCusDTO.setEmployeesNumber(0);
        principalCusDTO.setBedNumber(0);
        principalCusDTO.setCustomerId(principalCustomInfo.getYyPartaId().intValue());
        principalCusDTO.setCustomerLevel("A");
        principalCusDTO.setProvinceId(0);
        principalCusDTO.setCityId(0);
        principalCusDTO.setAreaId(0);
        principalCusDTO.setSaleCenterId(principalCustomInfo.getContractCustomSalecenterId());
        principalCusDTO.setSaleProvince(principalCustomInfo.getContractCustomSaleprovinceId());
        principalCusDTO.setCsTeamId(-1L);
        principalCusDTO.setCsTeamLeaderId(-1L);
        principalCusDTO.setCsDeptId(-1L);
        principalCusDTO.setOutPatientNum(0);
        principalCusDTO.setHospitalBranchNum(0);
        principalCusDTO.setTownshipHospitalNum(0);
        principalCusDTO.setCustomerName(principalCustomInfo.getContractCustomName());
        ProjCustomInfo customInfo = customInfoMapper.selectByPrimaryKey(customInfoId);
        ProjCustomDetailInfo customerDetail = customDetailInfoMapper.getCustomDetailInfoByCustomInfoId(customInfoId);
        //处理实施地客户信息
        CustomerDTO customerDTO = new CustomerDTO();
        customerDTO.setAnnualIncome(customerDetail.getCustomAnnualIncome().floatValue());
        customerDTO.setHospitalType(customInfo.getCustomType());
        customerDTO.setEmployeesNumber(0);
        customerDTO.setBedNumber(customerDetail.getCustomBedCount());
        customerDTO.setCustomerId(customInfo.getYyCustomerId().intValue());
        customerDTO.setCustomerLevel(customInfo.getCustomLevel());
        customerDTO.setProvinceId(customerDetail.getProvinceId().intValue());
        customerDTO.setCityId(customerDetail.getCityId().intValue());
        customerDTO.setAreaId(customerDetail.getTownId().intValue());
        customerDTO.setSaleCenterId(principalCustomInfo.getContractCustomSalecenterId());
        customerDTO.setSaleProvince(principalCustomInfo.getContractCustomSaleprovinceId());
        customerDTO.setCsTeamId(customInfo.getCustomTeamId());
        customerDTO.setCsTeamLeaderId(customInfo.getCustomHeadId());
        customerDTO.setCsDeptId(customInfo.getCustomDeptId());
        customerDTO.setOutPatientNum(customerDetail.getCustomOutPatientCount());
        customerDTO.setHospitalBranchNum(customerDetail.getHospitalBranchNum());
        customerDTO.setTownshipHospitalNum(customerDetail.getTownshipHospitalNum());
        customerDTO.setCustomerName(customInfo.getCustomName());
        //处理工单数据
        OrderDTO orderDTO = new OrderDTO();
        orderDTO.setProjectNum(SnowFlakeUtil.getId());
        SysDept sysDept = sysDeptMapper.selectById(req.getProjectTeamId());
        orderDTO.setTeamId(sysDept.getDeptYunyingId().intValue());
        orderDTO.setTeamName(sysDept.getDeptName());
        orderDTO.setWorkOrderId(SnowFlakeUtil.getId());
        orderDTO.setWorkNum("TMP_" + SnowFlakeUtil.getId());
        orderDTO.setWorkOrderName(req.getProjectName());
        //固定自研工单类型
        orderDTO.setOrderType(1);
        SysUser projectManager = sysUserMapper.selectByAccount(req.getProjectLeaderAccount());
        if (projectManager == null) {
            throw new RuntimeException("项目负责人不存在");
        }
        orderDTO.setProjectManagerId(Integer.valueOf(projectManager.getUserYunyingId()));
        //工单云类型默认共享云
        orderDTO.setOrderCloud("1");
        orderDTO.setWorkTime(DateUtil.formatDateTime(new Date()));
        orderDTO.setMsunHealthPoint("-");
        //处理产品数据
        List<BaseIdNameResp> productList = req.getOrderProductList();
        List<ProductDTO> productDTOList = new ArrayList<>();
        for (int i = 0; i < productList.size(); i++) {
            BaseIdNameResp idName = productList.get(i);
            ProductDTO productDTO = new ProductDTO();
            productDTO.setProductId(idName.getId().intValue());
            productDTO.setProductName(idName.getName());
            productDTO.setNum(1);
            productDTO.setPemCusSolType(1);
            productDTO.setProjProductId(String.valueOf(idName.getId()));
            productDTO.setBuyMode(1);
            productDTO.setBuyType(1);
            productDTO.setIntegrationFlag(0);
            productDTO.setProdHardwareType(0);
            //添加数据到list
            productDTOList.add(productDTO);
        }
        orderDTO.setProduct(productDTOList);
        //处理整体数据
        SyncContractDTO syncContractDTO = new SyncContractDTO();
        syncContractDTO.setPrincipalCustomer(principalCusDTO);
        syncContractDTO.setCustomerInfo(customerDTO);
        syncContractDTO.setDatas(Collections.singletonList(orderDTO));
        syncContractDTO.setContractNum(SnowFlakeUtil.getId());
        syncContractDTO.setContractStr("TMP_HT_" + syncContractDTO.getContractNum());
        syncContractDTO.setContractName("临时合同-" + req.getProjectName());
        syncContractDTO.setCustomerName(customerDTO.getCustomerName());
        syncContractDTO.setCustomerId(principalCustomInfo.getYyPartaId().intValue());
        //固定合同类型为标准
        syncContractDTO.setContractPreType(1);
        SysUser salePerson = sysUserMapper.selectByAccount(req.getSalesPersonAccount());
        if (salePerson == null) {
            throw new RuntimeException("销售负责人不存在");
        }
        syncContractDTO.setSaleUserId(Integer.valueOf(salePerson.getUserYunyingId()));
        syncContractDTO.setSaleUserName(salePerson.getUserName());
        syncContractDTO.setSaleOrgId(salePerson.getDeptId().intValue());
        syncContractDTO.setSaleOrgType("sales");
        syncContractDTO.setUpgradationType(req.getUpgradationType());
        syncContractDTO.setProjectType(req.getProjectType());
        return syncContractDTO;
    }

    /**
     * 获取手动创建自定义项目操作日志的分页列表
     *
     * @param queryDto
     * @return
     */
    @Override
    public Result<PageInfo<ProjManualCreateLog>> queryProjManualCreateLogPageList(
            @RequestBody ProjManualCreateLogQueryDto queryDto) {
        if (ObjectUtil.isNull(queryDto)) {
            queryDto = new ProjManualCreateLogQueryDto();
        }
        if (ObjectUtil.isNull(queryDto.getPageNum()) || queryDto.getPageNum() < 1) {
            queryDto.setPageNum(1);
        }
        if (ObjectUtil.isNull(queryDto.getPageSize()) || queryDto.getPageSize() < 1) {
            queryDto.setPageSize(10);
        }
        Page<Object> page = PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize());
        LambdaQueryWrapper<ProjManualCreateLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotNull(queryDto.getPrincipalCustomerInfoId()),
                ProjManualCreateLog::getPrincipalCustomerInfoId, queryDto.getPrincipalCustomerInfoId());
        queryWrapper.eq(ObjectUtil.isNotNull(queryDto.getCustomerInfoId()),
                ProjManualCreateLog::getCustomerInfoId, queryDto.getCustomerInfoId());
        queryWrapper.like(StrUtil.isNotBlank(queryDto.getProjectName()),
                ProjManualCreateLog::getProjectName, StrUtil.nullToDefault(queryDto.getProjectName(), "").trim());
        List<ProjManualCreateLog> queryList = projManualCreateLogMapper.selectList(queryWrapper);
        PageInfo<ProjManualCreateLog> listPage = PageInfo.of(queryList);
        listPage.setPageNum(page.getPageNum());
        listPage.setPageSize(page.getPageSize());
        listPage.setTotal(page.getTotal());
        return Result.success(listPage);
    }

    @Override
    public boolean isSmallFrontBigBack(Long projectInfoId) {
        ConfigCustomBackendLimit configCustomBackendLimit = customBackendLimitMapper.selectByProjectId(projectInfoId);
        return configCustomBackendLimit != null && Integer.valueOf(1).equals(configCustomBackendLimit.getOpenFlag());
    }

    @Override
    public boolean isOpenSurveyAudit(Long projectInfoId) {
        boolean isSmallFrontBigBack = this.isSmallFrontBigBack(projectInfoId);
        // 未开启小前端大后端时，直接返回false
        if (!isSmallFrontBigBack) {
            return false;
        }
        ConfigCustomBackendDetailLimit customBackendDetailLimit = configCustomBackendDetailLimitMapper.getCustomBackendDetailLimit(projectInfoId, 10);
        return customBackendDetailLimit != null && Integer.valueOf(1).equals(customBackendDetailLimit.getOpenFlag());
    }

    @Resource
    private DictProjectRoleMapper projectRoleMapper;

    @Override
    public ProjProjectMember getBackLeader(Long projectInfoId) {
        DictProjectRole dictProjectRole = projectRoleMapper.selectOne(
                new QueryWrapper<DictProjectRole>().eq("role_type", 2)
                        .eq("project_role_code", "back-leader")
        );

        if (dictProjectRole == null) {
            return null;
        }
        List<ProjProjectMember> projProjectMembers = projectMemberMapper.selectByProjectIdAndRole(projectInfoId, dictProjectRole.getProjectRoleId());
        if (CollectionUtils.isEmpty(projProjectMembers)) {
            return null;
        }
        return projProjectMembers.get(0);
    }

    @Resource
    private ProjSurveyFineConfigMapper projSurveyFineConfigMapper;

    @Override
    public String getSurveyAssessmentTime(Long projectInfoId) {
        ProjProjectInfo projProjectInfo = projectInfoMapper.selectByPrimaryKey(projectInfoId);
        return this.getSurveyAssessmentTime(projProjectInfo);
    }

    @Override
    public String getSurveyAssessmentTime(ProjProjectInfo projProjectInfo) {
        ProjSurveyFineConfig projSurveyFineConfig = projSurveyFineConfigMapper.selectOne(new QueryWrapper<>());
        return DateUtil.formatDateTime(DateUtil.offsetDay(projProjectInfo.getCreateTime(), projSurveyFineConfig.getCompleteDay()));
    }

    @Override
    public List<ProjProjectInfo> getOpenSurveyProductAudit() {
        return projectInfoMapper.getOpenSurveyProductAudit();
    }

    /**
     * 查询未上线项目列表
     * @param req
     * @return
     */
    @Override
    public Result<List<ProjectNotOnlineResp>> selectNotOnlineProjectList(ProjectSelectNotOnlineReq req) {
        List<ProjectNotOnlineResp> list = projectInfoMapper.selectNotOnlineProjectList(req);
        return Result.success(list);
    }

    @Override
    public Result<ProjectFiscalYearResp> selectYearFiscalProjectList(ProjectSelectNotOnlineReq req) {
        if (req.getStartTime() == null || req.getEndTime() == null) {
            // 根据当前年份是否大于4月1日。 如果大于4月1日，则处理开始时间为当前年份的4月1日， 结束时间为下一年的4月1日。 如果小于4月1日，则处理开始时间为上一年的4月1日， 结束时间为当前年份的4月1日。
            try {
                LocalDate currentDate = LocalDate.now();
                LocalDate aprilFirstCurrentYear = LocalDate.of(currentDate.getYear(), 4, 1);
                LocalDate startDate;
                LocalDateTime endDate;
                if (currentDate.isAfter(aprilFirstCurrentYear)) {
                    startDate = aprilFirstCurrentYear;
                    endDate = aprilFirstCurrentYear.plusYears(1).atTime(23, 59, 59);
                } else if (currentDate.isEqual(aprilFirstCurrentYear)) {
                    startDate = aprilFirstCurrentYear;
                    endDate = aprilFirstCurrentYear.plusYears(1).atTime(23, 59, 59);
                } else {
                    startDate = aprilFirstCurrentYear.minusYears(1);
                    endDate = aprilFirstCurrentYear.atTime(23, 59, 59);
                }
                // 这里可以根据startDate和endDate进行进一步的处理，例如查询数据库等
                req.setStartTime(java.sql.Date.valueOf(startDate));
                req.setEndTime(Date.from(endDate.atZone(ZoneId.systemDefault()).toInstant()));
            } catch (DateTimeException e) {
                // 处理日期时间异常情况，例如日志记录或返回错误信息
                log.error("处理日期时发生异常: {}", e.getMessage(), e);
                e.printStackTrace();
            } catch (Exception e) {
                // 处理其他异常情况，例如日志记录或返回错误信息
                log.error("发生未知异常: {}", e.getMessage(), e);
                e.printStackTrace();
            }
        }
        ProjectFiscalYearResp rresp = projectInfoMapper.selectYearFiscalProjectList(req);
        // 查询数据
        return Result.success(rresp);
    }

    @Override
    public Result<ProjectThisWeekResp> selectThisWeekProjectList(ProjectSelectNotOnlineReq req) {
        if (req.getStartTime() == null || req.getEndTime() == null) {
            // 根据当前周的起止日期
            // 获取当前日期
            LocalDate today = LocalDate.now();
            // 获取本周的第一天（周一）
            LocalDate startOfWeek = today.with(DayOfWeek.MONDAY);
            // 获取本周的最后一天（周日）
            LocalDateTime endOfWeek = today.with(DayOfWeek.SUNDAY).atTime(23, 59, 59);
            // 设置起止日期
            req.setStartTime(java.sql.Date.valueOf(startOfWeek));
            req.setEndTime(Date.from(endOfWeek.atZone(ZoneId.systemDefault()).toInstant()));
        }
        ProjectThisWeekResp rresp = projectInfoMapper.selectThisWeekProjectList(req);
        return Result.success(rresp);
    }

    /**
     * 查询已上线项目列表
     * @param req
     * @return
     */
    @Override
    public Result<List<ProjectNotOnlineResp>> selectOnlineProjectList(ProjectSelectNotOnlineReq req) {
        if (req.getStartTime() == null || req.getEndTime() == null) {
            // 根据当前周的起止日期
            // 获取当前日期
            LocalDate today = LocalDate.now();
            // 获取本周的第一天（周一）
            LocalDate startOfWeek = today.with(DayOfWeek.MONDAY);
            // 获取本周的最后一天（周日）
            LocalDateTime endOfWeek = today.with(DayOfWeek.SUNDAY).atTime(23, 59, 59);
            // 设置起止日期
            req.setStartTime(java.sql.Date.valueOf(startOfWeek));
            req.setEndTime(Date.from(endOfWeek.atZone(ZoneId.systemDefault()).toInstant()));
        }
        List<ProjectNotOnlineResp> rresp = projectInfoMapper.selectOnlineProjectList(req);
        return Result.success(rresp);
    }
}
