package com.msun.csm.service.proj;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.msun.csm.common.enums.ResultEnum;
import com.msun.csm.common.enums.issue.IssueOperEnums;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.BaseColorResp;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.SimpleId;
import com.msun.csm.dao.entity.config.ConfigIssueClassification;
import com.msun.csm.dao.entity.config.ConfigIssuePriority;
import com.msun.csm.dao.entity.config.ConfigIssueStatus;
import com.msun.csm.dao.entity.proj.*;
import com.msun.csm.dao.mapper.config.ConfigIssueClassificationMapper;
import com.msun.csm.dao.mapper.config.ConfigIssuePriorityMapper;
import com.msun.csm.dao.mapper.config.ConfigIssueStatusMapper;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.dao.mapper.proj.*;
import com.msun.csm.model.req.issue.BatchDelIssueReq;
import com.msun.csm.model.req.issue.BatchSaveIssueReq;
import com.msun.csm.model.req.issue.QueryIssueReq;
import com.msun.csm.model.req.issue.SaveIssueReq;
import com.msun.csm.model.resp.issue.IssueBaseQueryResp;
import com.msun.csm.model.resp.issue.IssueDataResp;
import com.msun.csm.model.vo.ProjHospitalInfoVO;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.message.SendBusinessMessageService;
import com.msun.csm.util.SnowFlakeUtil;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/11/29
 */

@Service
@Slf4j
public class ProjIssueInfoServiceImpl implements ProjIssueInfoService {

    //未分配状态
    private static final Long UN_ASSIGNED = 1L;
    //已分配状态
    private static final Long ASSIGNED = 2L;

    @Resource
    private ProjIssueInfoMapper projIssueInfoMapper;

    @Resource
    private ConfigIssueClassificationMapper configIssueClassificationMapper;

    @Resource
    private ConfigIssuePriorityMapper configIssuePriorityMapper;

    @Resource
    private ConfigIssueStatusMapper configIssueStatusMapper;

    @Resource
    private ProjProjectMemberMapper projectMemberMapper;

    @Resource
    private ProjProjectInfoMapper projectInfoMapper;

    @Resource
    private ProjHospitalInfoService hospitalInfoService;

    //实施产品
    @Resource
    private ProjProductDeliverRecordMapper productDeliverRecordMapper;

    @Resource
    private DictProductMapper dictProductMapper;

    @Resource
    private UserHelper userHelper;

    @Resource
    private ProjIssueOperLogMapper issueOperLogMapper;

    @Resource
    private SendBusinessMessageService sendBusinessMessageService;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return projIssueInfoMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(ProjIssueInfo record) {
        return projIssueInfoMapper.insert(record);
    }

    @Override
    public int insertOrUpdate(ProjIssueInfo record) {
        return projIssueInfoMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(ProjIssueInfo record) {
        return projIssueInfoMapper.insertOrUpdateSelective(record);
    }

    @Override
    public int insertSelective(ProjIssueInfo record) {
        return projIssueInfoMapper.insertSelective(record);
    }

    @Override
    public ProjIssueInfo selectByPrimaryKey(Long id) {
        return projIssueInfoMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(ProjIssueInfo record) {
        return projIssueInfoMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ProjIssueInfo record) {
        return projIssueInfoMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<ProjIssueInfo> list) {
        return projIssueInfoMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<ProjIssueInfo> list) {
        return projIssueInfoMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<ProjIssueInfo> list) {
        return projIssueInfoMapper.batchInsert(list);
    }

    /**
     * @param simpleId
     * @return
     * @Description: 获取基础查询数据-下老数据
     */
    @Override
    public Result<IssueBaseQueryResp> getBaseQueryData(SimpleId simpleId) {
        Long projectInfoId = simpleId.getId();
        IssueBaseQueryResp issueBaseQueryResp = new IssueBaseQueryResp();
        // 分类
        List<ConfigIssueClassification> classificationConfigList = configIssueClassificationMapper.selectAll();
        List<BaseIdNameResp> classificationRespList = classificationConfigList.stream().map(item -> {
            BaseIdNameResp baseIdNameResp = new BaseIdNameResp();
            baseIdNameResp.setId(item.getId());
            baseIdNameResp.setName(item.getName());
            return baseIdNameResp;
        }).collect(Collectors.toList());
        //查询项目自定义的分类
        List<String> classificationNameList = projIssueInfoMapper.getClassificationList(projectInfoId);
        if (!CollectionUtils.isEmpty(classificationNameList)) {
            Long lastId = classificationRespList.get(classificationRespList.size() - 1).getId();
            for (int i = 0; i < classificationNameList.size(); i++) {
                BaseIdNameResp baseIdNameResp = new BaseIdNameResp();
                baseIdNameResp.setId(lastId + i + 1);
                baseIdNameResp.setName(classificationNameList.get(i));
                classificationRespList.add(baseIdNameResp);
            }
        }
        issueBaseQueryResp.setClassificationList(classificationRespList);
        // 优先级
        List<ConfigIssuePriority> priorities = configIssuePriorityMapper.selectAll();
        List<BaseColorResp> priorityRespList = priorities.stream().map(item -> {
            BaseColorResp baseColorResp = new BaseColorResp();
            baseColorResp.setId(item.getId());
            baseColorResp.setName(item.getName());
            baseColorResp.setColor(item.getColor());
            return baseColorResp;
        }).collect(Collectors.toList());
        issueBaseQueryResp.setPriorityList(priorityRespList);
        // 状态
        List<ConfigIssueStatus> statuses = configIssueStatusMapper.selectAll();
        List<BaseColorResp> statusList = statuses.stream().map(item -> {
            BaseColorResp baseColorResp = new BaseColorResp();
            baseColorResp.setId(item.getId());
            baseColorResp.setName(item.getName());
            baseColorResp.setColor(item.getColor());
            return baseColorResp;
        }).collect(Collectors.toList());
        issueBaseQueryResp.setStatusList(statusList);
        // 产品
        //根据客户、项目类型查询系统已有的所有项目
        ProjProjectInfo projectInfoExtend = projectInfoMapper.selectByPrimaryKey(projectInfoId);
        List<ProjProjectInfo> projectInfoList = projectInfoMapper.findByCustomAndProjectType(projectInfoExtend);
        List<Long> projectIds = projectInfoList.stream().map(a -> a.getProjectInfoId()).collect(Collectors.toList());
        projectIds.add(projectInfoId);
        List<ProjProductDeliverRecord> deliverRecords = productDeliverRecordMapper.findByProjectInfoIdList(projectIds);
        List<BaseIdNameResp> productRespList = dictProductMapper.findByProductIds(
                deliverRecords.stream().map(a -> a.getProductDeliverId()).collect(Collectors.toSet()));
        productRespList.stream().forEach(item -> {
            if (item.getName().endsWith(StrUtil.DASHED)) {
                item.setName(item.getName().substring(0, item.getName().length() - 1));
            }
        });
        BaseIdNameResp other = new BaseIdNameResp();
        other.setId(-1L);
        other.setName("其他");
        productRespList.add(0, other);
        issueBaseQueryResp.setProductList(productRespList);
        // 用户
        List<ProjProjectMember> members = projectMemberMapper.selectByProjectIds(
                Collections.singletonList(simpleId.getId()));
        List<BaseIdNameResp> userRespList = members.stream().map(item -> {
            BaseIdNameResp baseIdNameResp = new BaseIdNameResp();
            baseIdNameResp.setId(item.getProjectMemberId());
            baseIdNameResp.setName(item.getProjectMemberName());
            return baseIdNameResp;
        }).collect(Collectors.toList());
        issueBaseQueryResp.setUserList(userRespList);

        Result<List<ProjHospitalInfoVO>> listResult = hospitalInfoService.findHospitalInfoByProjIdAndCustomerId(projectInfoExtend.getCustomInfoId().toString(), projectInfoId.toString());
        List<ProjHospitalInfoVO> hospitalInfoList = listResult.getData();
        List<BaseIdNameResp> hospitalRespList = hospitalInfoList.stream().map(item -> {
            BaseIdNameResp baseIdNameResp = new BaseIdNameResp();
            baseIdNameResp.setId(Long.valueOf(item.getHospitalInfoId()));
            baseIdNameResp.setName(item.getHospitalName());
            return baseIdNameResp;
        }).collect(Collectors.toList());
        issueBaseQueryResp.setHospitalList(hospitalRespList);
        return Result.success(issueBaseQueryResp);
    }

    /**
     * @param req
     * @return
     * @Description: 保存待办事项
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result saveIssue(SaveIssueReq req) {
        log.info("保存或者更新待办事项参数-{}", JSON.toJSONString(req));
        SysUserVO user = userHelper.getCurrentUser();
        Date now = new Date();
        ProjIssueInfo issueInfo = new ProjIssueInfo();
        BeanUtil.copyProperties(req, issueInfo);
        issueInfo.setUpdateTime(now);
        issueInfo.setUpdaterId(user.getSysUserId());
        if (req.getClassification() != null) {
            issueInfo.setClassification(req.getClassification().replace(StrUtil.SPACE, StrUtil.EMPTY));
        }
        int operType = -1;
        if (req.getId() != null) {
            log.info("更新待办事项");
            ProjIssueInfo currentIssueInfo = projIssueInfoMapper.selectByPrimaryKey(req.getId());
            if (issueInfo.getChargePerson() != null) {
                if (currentIssueInfo.getChargePerson() == null || !issueInfo.getChargePerson()
                        .equals(currentIssueInfo.getChargePerson())) {
                    operType = IssueOperEnums.ASSIGN_CHARGE_PERSON.getCode();
                }
            } else if (issueInfo.getDescription() != null) {
                if (currentIssueInfo.getDescription() == null || !issueInfo.getDescription()
                        .equals(currentIssueInfo.getDescription())) {
                    operType = IssueOperEnums.MODIFY_ISSUE_DESC.getCode();
                }
            } else if (issueInfo.getStatus() != null) {
                if (currentIssueInfo.getStatus() == null || !issueInfo.getStatus()
                        .equals(currentIssueInfo.getStatus())) {
                    operType = IssueOperEnums.MODIFY_ISSUE_STATUS.getCode();
                }
            }
            //分配责任人，状态自动变化
            if (operType == IssueOperEnums.ASSIGN_CHARGE_PERSON.getCode()) {
                issueInfo.setStatus(ASSIGNED);
            }
            projIssueInfoMapper.updateByPrimaryKeySelective(issueInfo);
            ProjIssueInfo afterUpdateIssue = projIssueInfoMapper.selectByPrimaryKey(req.getId());
            sendBusinessMessageService.sendAllocatingOrCancelIssueMessage(currentIssueInfo.getProjectInfoId(), currentIssueInfo, afterUpdateIssue);
        } else {
            log.info("新增待办事项");
            if (StringUtils.isBlank(req.getDescription())) {
                throw new CustomException(ResultEnum.PARAM_INVALID);
            }
            //状态，如果分配了责任人，状态为已分配，否则为未分配
            if (req.getChargePerson() != null) {
                issueInfo.setStatus(ASSIGNED);
            } else {
                issueInfo.setStatus(UN_ASSIGNED);
            }
            issueInfo.setCreaterId(user.getSysUserId());
            issueInfo.setCreateTime(now);
            issueInfo.setIsDeleted(0);
            issueInfo.setId(SnowFlakeUtil.getId());
            projIssueInfoMapper.insert(issueInfo);
            operType = IssueOperEnums.ADD.getCode();
            sendBusinessMessageService.sendAllocatingOrCancelIssueMessage(issueInfo.getProjectInfoId(), null, issueInfo);
        }
        //保存操作日志-需要判断更新字段，所以在数据修改前记录日志
        if (operType != -1) {
            saveLog(Collections.singletonList(issueInfo), operType);
        }
        return Result.success();
    }

    /**
     * @param req
     * @return
     * @Description: 批量删除待办事项
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result delIssueBatch(BatchDelIssueReq req) {
        log.info("批量删除待办事项参数-{}", JSON.toJSONString(req));
        List<Long> ids = req.getIds();
        //删除操作日志
        saveLog(ids.stream().map(id -> {
            ProjIssueInfo issueInfo = new ProjIssueInfo();
            issueInfo.setId(id);
            return issueInfo;
        }).collect(Collectors.toList()), IssueOperEnums.DELETE.getCode());
        // 不是质管操作
        if (!"supervisionCenter".equals(req.getOperationSource())) {
            List<ProjIssueInfo> projIssueInfos = projIssueInfoMapper.selectByIdList(ids);
            List<String> collect = projIssueInfos.stream().map(ProjIssueInfo::getOperationSource).collect(Collectors.toList());
            if (collect.contains("supervisionCenter")) {
                throw new IllegalArgumentException("质管人员添加的问题仅允许质管人员删除，请去掉质管人员添加的问题后再进行删除操作");
            }
        }
        int delCount = projIssueInfoMapper.deleteBatchIds(ids);
        if (delCount == ids.size()) {
            return Result.success();
        } else {
            throw new CustomException(ResultEnum.FAIL);
        }
    }

    /**
     * @param queryIssueReq
     * @return
     * @Description: 查询待办事项
     */
    @Override
    public Result<List<IssueDataResp>> queryData(QueryIssueReq queryIssueReq) {
        log.info("查询待办事项参数-{}", JSON.toJSONString(queryIssueReq));
        List<IssueDataResp> issueDataResps = projIssueInfoMapper.queryData(queryIssueReq);
        //如果不分组，直接返回
        if (queryIssueReq.getGroupByType() == null) {
            return Result.success(issueDataResps);
        }
        //根据分组类型，进行数据分组-1问题负责人、2问题分类、3产品模块,null 不分组
        List<IssueDataResp> resps = new ArrayList<>();
        log.info("分组查询-分组类型-{}", queryIssueReq.getGroupByType());
        if (queryIssueReq.getGroupByType() == 1) {
            Map<Long, List<IssueDataResp>> productMao = issueDataResps.stream()
                    .collect(Collectors.groupingBy(a -> Optional.ofNullable(a.getChargePerson()).orElse(-1L)));
            productMao.forEach((key, value) -> {
                IssueDataResp issueDataResp = new IssueDataResp();
                //父级展示，假数据
                issueDataResp.setId(key);
                if (key == -1L) {
                    issueDataResp.setDescription("无负责人");
                } else {
                    issueDataResp.setDescription(value.get(0).getChargePersonName());
                }
                issueDataResp.setChildren(value);
                resps.add(issueDataResp);
            });
        }
        if (queryIssueReq.getGroupByType() == 2) {
            Map<String, List<IssueDataResp>> productMao = issueDataResps.stream().collect(
                    Collectors.groupingBy(a -> Optional.ofNullable(a.getClassification()).orElse("无分类")));
            productMao.forEach((key, value) -> {
                IssueDataResp issueDataResp = new IssueDataResp();
                //父级展示，假数据
                issueDataResp.setDescription(key);
                issueDataResp.setChildren(value);
                resps.add(issueDataResp);
            });
        }
        if (queryIssueReq.getGroupByType() == 3) {
            Map<Integer, List<IssueDataResp>> productMao = issueDataResps.stream()
                    .collect(Collectors.groupingBy(a -> Optional.ofNullable(a.getProductId()).orElse(-1)));
            productMao.forEach((key, value) -> {
                IssueDataResp issueDataResp = new IssueDataResp();
                issueDataResp.setId(key.longValue());
                if (key == -1) {
                    issueDataResp.setDescription("无产品");
                } else {
                    issueDataResp.setDescription(value.get(0).getProductName());
                }
                issueDataResp.setChildren(value);
                resps.add(issueDataResp);
            });
        }
        return Result.success(resps);
    }

    /**
     * @param req
     * @return
     * @Description: 批量保存待办事项
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result batchSaveIssue(BatchSaveIssueReq req) {
        log.info("批量保存待办事项参数-{}", JSON.toJSONString(req));
        SysUserVO user = userHelper.getCurrentUser();
        List<ProjIssueInfo> issueInfos = req.getDescription().stream().map(item -> {
            ProjIssueInfo issueInfo = new ProjIssueInfo();
            issueInfo.setDescription(item);
            issueInfo.setProjectInfoId(req.getProjectInfoId());
            issueInfo.setProductId(req.getProductId());
            if (req.getClassification() != null) {
                issueInfo.setClassification(req.getClassification().replaceAll(StrUtil.SPACE, StrUtil.EMPTY));
            }
            issueInfo.setStatus(UN_ASSIGNED);
            issueInfo.setDept(req.getDept());
            issueInfo.setCreaterId(user.getSysUserId());
            issueInfo.setUpdaterId(user.getSysUserId());
            //创建时间毫秒级区分
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
            String dateStr = sf.format(new Date());
            issueInfo.setCreateTime(DateUtil.parse(dateStr, "yyyy-MM-dd HH:mm:ss.SSS"));
            issueInfo.setUpdateTime(DateUtil.parse(dateStr, "yyyy-MM-dd HH:mm:ss.SSS"));
            issueInfo.setIsDeleted(0);
            issueInfo.setId(SnowFlakeUtil.getId());
            issueInfo.setProjectPlanId(req.getProjectPlanId());
            issueInfo.setTodoTaskId(req.getTodoTaskId());
            return issueInfo;
        }).collect(Collectors.toList());
        //保存操作日志
        saveLog(issueInfos, IssueOperEnums.ADD.getCode());
        return projIssueInfoMapper.batchInsert(issueInfos) == req.getDescription().size() ? Result.success()
                : Result.fail();
    }

    /**
     * @param simpleId
     * @return
     * @Description: 获取待办事项数量
     */
    @Override
    public Result queryCount(SimpleId simpleId) {
        int count = projIssueInfoMapper.queryCount(simpleId.getId());
        return Result.success(count);
    }

    /**
     * @param simpleId
     * @return
     * @Description: 导出
     */
    @Override
    public Result export(SimpleId simpleId) {
        return Result.success();
    }

    /**
     * @param issueInfoList
     * @Description: 保存操作日志
     */
    private void saveLog(List<ProjIssueInfo> issueInfoList, int operType) {
        //判断是新增、更新还是删除
        if (issueInfoList != null && issueInfoList.size() > 0) {
            SysUserVO user = userHelper.getCurrentUser();
            Date now = new Date();
            List<ProjIssueOperLog> logList = issueInfoList.stream()
                    .map(item -> getProjIssueOperLog(item, user, now, operType))
                    .collect(Collectors.toList());
            issueOperLogMapper.batchInsert(logList);
        }
    }

    @NotNull
    private static ProjIssueOperLog getProjIssueOperLog(ProjIssueInfo item, SysUserVO user, Date now, int operType) {
        // 1. 创建问题 2. 分配责任人 3. 修改问题描述 4.问题状态变更 5 删除问题数据
        ProjIssueOperLog log = new ProjIssueOperLog();
        log.setId(SnowFlakeUtil.getId());
        log.setIssueInfoId(item.getId());
        log.setOperType(operType);
        log.setModifyData(JSON.toJSONString(item));
        log.setCreaterId(user.getSysUserId());
        log.setUpdaterId(user.getSysUserId());
        log.setCreateTime(now);
        log.setUpdateTime(now);
        log.setIsDeleted(0);
        return log;
    }

}
