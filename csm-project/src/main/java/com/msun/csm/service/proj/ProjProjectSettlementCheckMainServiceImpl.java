package com.msun.csm.service.proj;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import static com.msun.csm.common.enums.projsettlement.SettlementRuleCodeEnum.SETTLEMENT_CLOUD_CONFIRM_FORM;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.enums.MilestoneNodeEnum;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.TelesalesFlagEnum;
import com.msun.csm.common.enums.api.yunying.OrderTypeEnums;
import com.msun.csm.common.enums.projapplyorder.ProductOpenStatusEnum;
import com.msun.csm.common.enums.projprojectinfo.ProjectTypeEnums;
import com.msun.csm.common.enums.projreview.ProjectStageCodeEnum;
import com.msun.csm.common.enums.projsettlement.PaySignageEnum;
import com.msun.csm.common.enums.projsettlement.SettlementRuleCodeEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.dao.entity.SysDept;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.dict.DictProduct;
import com.msun.csm.dao.entity.proj.ProjContractInfo;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjOrderInfo;
import com.msun.csm.dao.entity.proj.ProjOrderProduct;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectOrderRelation;
import com.msun.csm.dao.entity.proj.ProjProjectSettlement;
import com.msun.csm.dao.entity.proj.ProjProjectSettlementCheck;
import com.msun.csm.dao.entity.proj.ProjProjectSettlementLog;
import com.msun.csm.dao.entity.proj.ProjProjectSettlementRule;
import com.msun.csm.dao.entity.proj.projsettlement.ProjSettlementOrderInfo;
import com.msun.csm.dao.entity.tmp.TmpProjectNewVsOld;
import com.msun.csm.dao.mapper.proj.ProjMilestoneInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderProductMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectOrderRelationMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectSettlementCheckMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectSettlementLogMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectSettlementMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectSettlementRuleMapper;
import com.msun.csm.dao.mapper.sysdept.SysDeptMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.dao.mapper.tmp.TmpProjectNewVsOldMapper;
import com.msun.csm.feign.client.yunying.YunyingFeignClient;
import com.msun.csm.feign.entity.yunying.req.GetPreSaleUserParam;
import com.msun.csm.feign.entity.yunying.req.YyPayFeedBackDto;
import com.msun.csm.feign.entity.yunying.resp.GetPreSaleUserResp;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementKeyFlagVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.common.CommonService;
import com.msun.csm.service.common.ExceptionMessageService;
import com.msun.csm.service.operlog.SysOperLogService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderService;
import com.msun.csm.service.yunying.YunYingServiceImpl;

/**
 * 审核服务获取基础信息
 *
 * <AUTHOR>
 * @since 2024-06-18 08:29:00
 */
@Slf4j
@Service
public class ProjProjectSettlementCheckMainServiceImpl implements ProjProjectSettlementCheckMainService {
    /**
     * 患者智能产品id yy_order_product_id
     */
    public static final Long BAILING_CLOUD_PATIENT_YY_ORDER_PRODUCT_ID = 5300L;
    public static final Long BAILING_CLOUD_PATIENT_ALIPAY_YY_ORDER_PRODUCT_ID = 6169L;
    public static final Long BAILING_CLOUD_PATIENT_APP_YY_ORDER_PRODUCT_ID = 7062L;

    /**
     * 中间件部署服务运营产品id
     */
    private static final long MIDDLEWARE_YY_PRODUCT_ID = 5920L;
    public static final List<Long> BAILING_PRODUCT_IDS = CollUtil.newArrayList(
            BAILING_CLOUD_PATIENT_YY_ORDER_PRODUCT_ID,
            BAILING_CLOUD_PATIENT_ALIPAY_YY_ORDER_PRODUCT_ID,
            BAILING_CLOUD_PATIENT_APP_YY_ORDER_PRODUCT_ID
    );

    @Resource
    private ProjProjectOrderRelationMapper projectOrderRelationMapper;


    @Resource
    private ProjOrderProductMapper orderProductMapper;

    @Resource
    private ProjOrderInfoService projOrderInfoService;

    @Resource
    private ProjContractInfoService projContractInfoService;

    @Resource
    private ProjProjectInfoService projectInfoService;

    @Resource
    private ProjProjectSettlementService settlementService;

    @Resource
    private ProjCustomInfoService customInfoService;

    @Resource
    private ProjProjectSettlementCheckMapper settlementCheckMapper;

    @Resource
    private ProjProjectSettlementLogMapper settlementLogMapper;

    @Resource
    private ProjApplyOrderService applyOrderService;

    @Resource
    private ProjOrderInfoMapper orderInfoMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private SysDeptMapper sysDeptMapper;

    @Resource
    private ProjProjectSettlementMapper settlementMapper;

    @Resource
    private TmpProjectNewVsOldMapper tmpProjectNewVsOldMapper;

    @Resource
    private ProjProjectSettlementRuleMapper settlementRuleMapper;
    @Resource
    private CommonService commonService;

    @Resource
    private ProjMilestoneInfoMapper milestoneInfoMapper;

    @Resource
    private SysOperLogService sysOperLogService;

    @Resource
    private YunyingFeignClient yunyingFeignClient;

    @Resource
    private ExceptionMessageService exceptionMessageService;

    @Resource
    private UserHelper userHelper;

    public String getProductNames(Long projectInfoId) {
        List<DictProduct> dictProducts = orderProductMapper.findDictProductByProjectInfoId(projectInfoId);
        StringBuilder stringBuilder = new StringBuilder();
        if (CollUtil.isNotEmpty(dictProducts)) {
            dictProducts.forEach(e -> {
                stringBuilder.append(e.getProductName()).append(StrUtil.COMMA);
            });
            return stringBuilder.substring(0, stringBuilder.length() - 1);
        }
        return null;
    }

    @Override
    public ProjProjectOrderRelation getCloudResourceRelationBothType(Long projectInfoId) {
        return projectOrderRelationMapper.getCloudResourceRelation(projectInfoId);
    }

    @Override
    public boolean isOldSettlement(Long projectInfoId) {
        // 查询项目里程碑是否存在
        List<ProjMilestoneInfo> milestoneInfos =
                milestoneInfoMapper.selectList(new QueryWrapper<ProjMilestoneInfo>()
                        .eq("project_info_id", projectInfoId)
                        .eq("project_stage_code", ProjectStageCodeEnum.STAGE_ENTRY.getCode())
                        .eq("invalid_flag", NumberEnum.NO_0.num())
                        .eq("milestone_node_code", MilestoneNodeEnum.SETTLED_PMO_AUDIT.getCode())
                );
        return CollUtil.isEmpty(milestoneInfos);
    }

    /**
     * 根据项目id查询合同信息
     *
     * @param projectInfoId 项目id
     * @return ProjContractInfo
     */
    public ProjContractInfo getContractByProjectInfoId(Long projectInfoId) {
        ProjProjectInfo projectInfo = getProjectInfo(projectInfoId);
        return getContract(projectInfo.getOrderInfoId());
    }

    @Override
    public boolean isTeleSales(Long customInfoId) {
        ProjCustomInfo customInfo = getCustomInfo(customInfoId);
        return isTeleSales(customInfo);
    }

    @Override
    public boolean isTeleSalesByProjectInfoId(Long projectInfoId) {
        ProjProjectInfo projectInfo = getProjectInfo(projectInfoId);
        ProjCustomInfo customInfo = getCustomInfo(projectInfo.getCustomInfoId());
        return isTeleSales(customInfo);
    }

    @Override
    public boolean isTeleSales(ProjCustomInfo customInfo) {
        return TelesalesFlagEnum.YES.getCode() == customInfo.getTelesalesFlag();
    }

    /**
     * 查询是否存在标准合同
     *
     * @param projectInfoId 项目id
     * @return true: 是标准合同, false: 否
     */
    public boolean hasStandardContract(Long projectInfoId) {
        List<ProjContractInfo> contractInfos = findStandardContractInfo(projectInfoId);
        if (CollUtil.isEmpty(contractInfos)) {
            log.info("未查询到标准合同. projectInfoId: {}", projectInfoId);
            return false;
        }
        return CollUtil.isNotEmpty(contractInfos);
    }

    @Override
    public boolean isFirstProject(Long projectInfoId) {
        try {
            ProjProjectInfo projectInfo = getProjectInfo(projectInfoId);
            return applyOrderService.isFirstProject(projectInfo);
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }

    }

    /**
     * 获取项目换新类型,
     * 项目实施类型  1 老换新升级 2新客户上线
     *
     * @param projectInfoId 项目id
     * @return int
     */
    public int upgradationType(Long projectInfoId) {
        ProjProjectInfo projectInfo = getProjectInfo(projectInfoId);
        return projectInfo.getUpgradationType();
    }

    /**
     * 获取软件工单
     *
     * @param projectInfoId 项目id
     * @return ProjOrderInfo
     */
    public ProjOrderInfo getSoftOrderInfo(Long projectInfoId) {
        ProjProjectInfo projectInfo = getProjectInfo(projectInfoId);
        return orderInfoMapper.selectById(projectInfo.getOrderInfoId());
    }

    /**
     * 获取软件工单
     *
     * @param orderInfoId 工单id
     * @return ProjOrderInfo
     */
    public ProjOrderInfo getSoftOrderInfoById(Long orderInfoId) {
        return orderInfoMapper.selectById(orderInfoId);
    }

    /**
     * 获取合同号
     *
     * @param orderInfoId 工单id
     * @return String
     */
    public ProjContractInfo getContract(Long orderInfoId) {
        ProjOrderInfo projOrderInfo = projOrderInfoService.selectByPrimaryKey(orderInfoId);
        if (ObjectUtil.isEmpty(projOrderInfo)) {
            throw new RuntimeException("未获取到工单信息.");
        }
        ProjContractInfo contractInfo = projContractInfoService.selectByPrimaryKey(projOrderInfo.getContractInfoId());
        if (ObjectUtil.isEmpty(contractInfo)) {
            throw new RuntimeException("未获取到合同信息.");
        }
        return contractInfo;
    }

    public ProjProjectInfo getProjectInfo(Long projectInfoId) {
        return projectInfoService.selectByPrimaryKey(projectInfoId);
    }

    @Override
    public ProjectTypeEnums getProjectTypeEnum(Long projectInfoId) {
        ProjProjectInfo projectInfo = getProjectInfo(projectInfoId);
        if (ObjectUtil.isEmpty(projectInfo)) {
            throw new RuntimeException("未查询到项目.");
        }
        return ProjectTypeEnums.getEnum(projectInfo.getProjectType());
    }


    public boolean hasCloudFormConfirm(Long projectInfoId) {
        List<ProjSettlementOrderInfo> orderInfos = findCloudFormConfirm(projectInfoId);
        return CollUtil.isNotEmpty(orderInfos);
    }

    /**
     * 查找云资源工单
     *
     * @param projectInfoId 项目id
     * @return List<ProjSettlementOrderInfo>
     */
    public List<ProjSettlementOrderInfo> findCloudFormConfirm(Long projectInfoId) {
        if (CollUtil.isEmpty(findSettlementRule(projectInfoId, SETTLEMENT_CLOUD_CONFIRM_FORM))) {
            return CollUtil.newArrayList();
        }
        return findCloudServiceForm(projectInfoId);
    }

    @Override
    public boolean hasPaysign(Long projectInfoId) {
        List<ProjContractInfo> contractInfos = findStandardContractInfo(projectInfoId, true);
        // 未查询到标准合同
        if (ObjectUtil.isEmpty(contractInfos)) {
            return true;
        }
        // 存在未缴纳首付款合同
        log.info("存在未缴纳首付款合同, {}", contractInfos);
        return commonService.paysignage(projectInfoId,
                contractInfos.stream().map(ProjContractInfo::getYyContractId).collect(Collectors.toList()));
    }

    /**
     * 查询标准合同信息
     *
     * @param projectInfoId 项目id
     * @return 标准合同集合
     */
    public List<ProjContractInfo> findStandardContractInfo(Long projectInfoId) {
        return findStandardContractInfo(projectInfoId, false);
    }

    /**
     * 查询标准合同信息
     *
     * @param projectInfoId 项目id
     * @param filterNoPay   true: 过滤出未付款的标准合同
     * @return 标准合同集合
     */
    public List<ProjContractInfo> findStandardContractInfo(Long projectInfoId, Boolean filterNoPay) {
        // 查询软件标准合同
        List<ProjContractInfo> contractInfos = findSoftContractInfo(projectInfoId);
        // 查询云资源标准合同
        ProjContractInfo contractInfo = findCloudContractInfo(projectInfoId);
        if (ObjectUtil.isNotEmpty(contractInfo)) {
            contractInfos.add(contractInfo);
        } else {
            log.warn("未查询到云资源. projectInfoId: {}", projectInfoId);
        }
        contractInfos =
                contractInfos.stream().filter(CommonService::isStandardContract).distinct().collect(Collectors.toList());
        if (filterNoPay) {
            return contractInfos.stream().filter(e -> CommonService.isStandardContract(e)
                    && e.getPaySignage() == PaySignageEnum.NOT_PAY.getCode()).collect(Collectors.toList());
        } else {
            return contractInfos.stream().filter(CommonService::isStandardContract).collect(Collectors.toList());
        }
    }

    public void prePaymentFeeBack(SysUser sysUser, Long yyOrderId, String errorMessageContent, Long projectInfoId) {
        log.info("调用运营部审核首付款接口. yyOrderId: {}, projectInfoId: {}, sysUser: {}", yyOrderId, projectInfoId, sysUser);
        // 查询项目合同
        List<ProjContractInfo> contractInfos = findStandardContractInfo(projectInfoId, true);
        if (CollUtil.isEmpty(contractInfos)) {
            log.error("未查询到标准合同, yyOrderId: {}, projectInfoId: {}, sysUser: {}", yyOrderId, projectInfoId, sysUser);
            throw new CustomException("未查询到标准合同.");
        }
        List<String> yyContractIds =
                contractInfos.stream().map(e -> StrUtil.toString(e.getYyContractId())).collect(Collectors.toList());
        log.info("项目的标准合同. projectInfoId: {}, 合同: {}", projectInfoId, yyContractIds);
        prePaymentFeeBack(sysUser, yyOrderId, errorMessageContent, projectInfoId, yyContractIds);
    }

    public void prePaymentFeeBack(SysUser sysUser, Long yyOrderId, String errorMessageContent, Long projectInfoId,
                                  List<String> yyContractIds) {
        String uid = UUID.randomUUID().toString();
        YyPayFeedBackDto yyPayFeedBackDto = new YyPayFeedBackDto("imsp", yyOrderId, yyContractIds);
        String account = sysUser.getAccount();
        sysOperLogService.apiOperLogInsertObjAry("预付款通知运营部审核", "入参(" + uid + ")",
                Log.LogOperType.FIX.getCode(), yyPayFeedBackDto, account);
        String result = yunyingFeignClient.prePaymentFeedBack(account, yyPayFeedBackDto);
        if (StrUtil.isNotBlank(result)) {
            JSONObject jsonObject = JSONObject.parseObject(result);
            if (ObjectUtil.isNotEmpty(jsonObject) && !((boolean) jsonObject.get("success"))) {
                exceptionMessageService.sendToSystemManager(projectInfoId, errorMessageContent);
            }
        }
        sysOperLogService.apiOperLogInsert(result, "预付款通知运营部审核", "出参(" + uid + ")",
                Log.LogOperType.FIX.getCode());
    }

    /**
     * 查询软件标准合同
     *
     * @param projectInfoId 项目id
     * @return 合同集合
     */
    private List<ProjContractInfo> findSoftContractInfo(Long projectInfoId) {
        List<ProjContractInfo> contractInfos = commonService.findContractInfo(projectInfoId);
        if (CollUtil.isEmpty(contractInfos)) {
            return CollUtil.newArrayList();
        }
        // 过滤出标准合同
        return contractInfos;
    }

    private ProjContractInfo findCloudContractInfo(Long projectInfoId) {
        List<ProjSettlementOrderInfo> orderInfos = findCloudFormConfirm(projectInfoId);
        if (CollUtil.isEmpty(orderInfos)) {
            return null;
        }
        return commonService.getContractInfoByYyOrderId(orderInfos.get(0).getYyOrderId());
    }

    /**
     * 根据项目id合规则枚举查询生成规则
     *
     * @param projectInfoId          项目id
     * @param settlementRuleCodeEnum 规则枚举
     * @return list
     */
    private List<ProjProjectSettlementRule> findSettlementRule(Long projectInfoId,
                                                               SettlementRuleCodeEnum settlementRuleCodeEnum) {
        return settlementRuleMapper.selectList(new QueryWrapper<ProjProjectSettlementRule>().eq("project_info_id",
                projectInfoId).eq("project_rule_code", settlementRuleCodeEnum.getCode()));
    }

    @Override
    public ProjProjectInfo getProjectInfo(String projectInfoId) {
        return getProjectInfo(Long.parseLong(projectInfoId));
    }

    /**
     * 获取当前节点状态对象
     *
     * @param projectInfoId 项目id
     * @return ProjProjectSettlement
     */
    public ProjProjectSettlement getCurrentSettlement(Long projectInfoId) {
        ProjProjectSettlement settlement = settlementService.getSettlementByProjectInfoId(projectInfoId);
        if (ObjectUtil.isEmpty(settlement)) {
            throw new RuntimeException("未查询到入驻申请工单.");
        }
        return settlement;
    }

    /**
     * 获取客户信息
     *
     * @param customInfoId 客户信息id
     * @return ProjCustomInfo
     */
    public ProjCustomInfo getCustomInfo(Long customInfoId) {
        return customInfoService.selectByPrimaryKey(customInfoId);
    }

    @Override
    public List<ProjProjectSettlementCheck> findSettlementCheck(String projectInfoId) {
        return settlementCheckMapper.selectList(new QueryWrapper<ProjProjectSettlementCheck>().eq("project_info_id",
                Long.parseLong(projectInfoId)).orderByAsc("check_node"));
    }

    @Override
    public List<ProjProjectSettlementLog> findSettlementCheckLogCreateTimeAsc(String projectInfoId) {
        return findSettlementCheckLogCreateTimeAsc(Long.parseLong(projectInfoId));
    }

    @Override
    public List<ProjProjectSettlementLog> findSettlementCheckLogCreateTimeDesc(Long projectInfoId) {
        return settlementLogMapper.selectList(new QueryWrapper<ProjProjectSettlementLog>().eq("project_info_id",
                projectInfoId).orderByDesc("create_time"));
    }

    public List<ProjProjectSettlementLog> findSettlementCheckLogCreateTimeAsc(Long projectInfoId) {
        return settlementLogMapper.selectList(new QueryWrapper<ProjProjectSettlementLog>().eq("project_info_id",
                projectInfoId).orderByAsc("create_time"));
    }

    @Override
    public boolean dispatchCloudForm(Long projectInfoId) {
        ProjProjectInfo projectInfo = getProjectInfo(projectInfoId);
        if (ObjectUtil.isEmpty(projectInfo)) {
            throw new RuntimeException("未查询到项目信息.");
        }
        return dispatchCloudForm(projectInfo);
    }

    public boolean dispatchCloudForm(ProjProjectInfo projectInfo) {
        return !CollUtil.isEmpty(findCloudServiceForm(projectInfo.getProjectInfoId()));
    }

    /**
     * 查询云资源工单（去除已绑定后工单信息）
     *
     * @param projectInfoId 项目id
     * @return List<ProjSettlementOrderInfo>
     */
    public List<ProjSettlementOrderInfo> findCloudServiceForm(Long projectInfoId) {
        List<ProjSettlementOrderInfo> orderInfos = findSettlementOrderInfoExcludeRelation(projectInfoId, true);
        if (CollUtil.isEmpty(orderInfos)) {
            return null;
        }
        orderInfos =
                orderInfos.stream().filter(e -> e.getDeliveryOrderType() == OrderTypeEnums.CLOUD_RESOURCE.getCode().intValue()).collect(Collectors.toList());
        return orderInfos;
    }

    private boolean hasMiddlewareProduct(Long projectInfoId) {
        List<ProjSettlementOrderInfo> settlementOrderInfos = findMiddlewareSettlementOrderInfo(projectInfoId);
        if (CollUtil.isEmpty(settlementOrderInfos)) {
            return false;
        }
        List<ProjOrderProduct> orderProducts = orderProductMapper.selectList(new QueryWrapper<ProjOrderProduct>().in(
                "order_info_id",
                settlementOrderInfos.stream().map(ProjSettlementOrderInfo::getOrderInfoId).collect(Collectors.toList())));
        if (CollUtil.isEmpty(orderProducts)) {
            return false;
        }
        return orderProducts.stream().anyMatch(e -> e.getYyOrderProductId() == MIDDLEWARE_YY_PRODUCT_ID);
    }

    public boolean hasMiddlewareOrderInfo(Long projectInfoId) {
        return hasMiddlewareProduct(projectInfoId);
    }

    /**
     * 是否存在外采软件工单
     *
     * @param projectInfoId 项目id
     * @return true, 存在. false, 不存在
     */
    public boolean hasPurchaseSoftOrderInfo(Long projectInfoId) {
        ProjProjectInfo projectInfo = commonService.getProjectInfo(projectInfoId);
        ProjOrderInfo orderInfo = commonService.getOrderInfo(projectInfo.getOrderInfoId());
        return orderInfo.getDeliveryOrderType() == OrderTypeEnums.PURCHASE_SOFTWARE.getCode();
    }

    /**
     * 查询中间件服务工单
     *
     * @return List<ProjSettlementOrderInfo>
     */
    public List<ProjSettlementOrderInfo> findMiddlewareSettlementOrderInfo(Long projectInfoId) {
        return findSettlementOrderInfo(projectInfoId, OrderTypeEnums.HARDWARE);
    }

    private List<ProjSettlementOrderInfo> findSettlementOrderInfo(Long projectInfoId, OrderTypeEnums orderTypeEnums) {
        List<ProjSettlementOrderInfo> settlementOrderInfos = findSettlementOrderInfoExcludeRelation(projectInfoId,
                true);
        if (CollUtil.isEmpty(settlementOrderInfos)) {
            return null;
        }
        settlementOrderInfos =
                settlementOrderInfos.stream().filter(e -> e.getDeliveryOrderType() == orderTypeEnums.getCode().intValue()).collect(Collectors.toList());
        if (CollUtil.isEmpty(settlementOrderInfos)) {
            // 如果没有硬件工单
            return null;
        }
        return settlementOrderInfos;
    }

    /**
     * 查询工单信息。
     * 更具运营实施地客户id查询工单
     *
     * @param projectInfoId 项目id
     * @return List<ProjSettlementOrderInfo>
     */
    public List<ProjSettlementOrderInfo> findSettlementOrderInfo(Long projectInfoId) {
        ProjProjectInfo projectInfo = getProjectInfo(projectInfoId);
        ProjCustomInfo customInfo = getCustomInfo(projectInfo.getCustomInfoId());
        Long yyCustomerId = customInfo.getYyCustomerId();
        List<ProjSettlementOrderInfo> orderInfos = settlementMapper.findSettlementOrderInfo(yyCustomerId);
        if (CollUtil.isEmpty(orderInfos)) {
            return CollUtil.newArrayList();
        }
        return orderInfos;
    }

    public boolean hasBailingProduct(Long projectInfoId) {
        List<ProjOrderProduct> orderProducts = getBailingOrderProduct(projectInfoId);
        return CollUtil.isNotEmpty(orderProducts);
    }

    /**
     * 获取百灵app产品根据项目id获取
     *
     * @param projectInfoId 项目id
     * @return List<ProjOrderProduct>
     */
    public List<ProjOrderProduct> getBailingOrderProduct(Long projectInfoId) {
        return orderProductMapper.selectList(new QueryWrapper<ProjOrderProduct>().eq("project_info_id", projectInfoId)
                .in("yy_order_product_id", BAILING_PRODUCT_IDS));
    }

    public boolean isBailingAppOpen(Long projectInfoId) {
        // 判断是否开通
        List<TmpProjectNewVsOld> tmpProjectNewVsOlds =
                tmpProjectNewVsOldMapper.selectByNewProjectIds(CollUtil.newArrayList(projectInfoId));
        if (CollUtil.isEmpty(tmpProjectNewVsOlds)) {
            log.info("未查询到老系统对应的项目信息.");
            return false;
        }
        List<Integer> appStatus =
                settlementMapper.selectBailingAppOpenStatus(tmpProjectNewVsOlds.get(0).getOldProjectInfoId(),
                        BAILING_PRODUCT_IDS);
        if (ObjectUtil.isEmpty(appStatus)) {
            return false;
        }
        return appStatus.stream().anyMatch(e -> e.intValue() == ProductOpenStatusEnum.OPENED.getOldCode().intValue());
    }

    @Resource
    private ProjProjectSettlementRuleService settlementRuleService;

    public ProjProjectSettlementKeyFlagVO getSettlementKeyFlagBean(Long projectInfoId) {
        ProjProjectSettlementKeyFlagVO keyFlagVO = new ProjProjectSettlementKeyFlagVO();
        ProjProjectInfo projectInfo = getProjectInfo(projectInfoId);
        ProjCustomInfo customInfo = getCustomInfo(projectInfo.getCustomInfoId());
        keyFlagVO.setPaySignage(!settlementRuleService.needPaySignage(settlementRuleService.getTransportForPayedSignage(projectInfoId)));
        keyFlagVO.setIsTelSaleProject(isTeleSales(customInfo));
        keyFlagVO.setIsFirstProject(isFirstProject(projectInfoId));
        keyFlagVO.setIsStandardContract(hasStandardContract(projectInfoId));
        keyFlagVO.setIsDispatchMiddlewareForm(hasMiddlewareOrderInfo(projectInfoId));
        keyFlagVO.setIsDispatchCloudResForm(dispatchCloudForm(projectInfo));
        return keyFlagVO;
    }

    /**
     * 获取在标准合同前提下确认是否结清了首付款
     * 1. 若不是标准合同, 如果交了首付款, 返回false
     * 2. 如果是标准合同, 交了首付款 return true
     * 3. 如果是标准合同, 未交首付款 return false
     *
     * @param projectInfoId 项目id
     * @return Boolean
     */
    public Boolean isPaysignageForTip(Long projectInfoId) {
        ProjProjectSettlementKeyFlagVO keyFlagVO = getSettlementKeyFlagBean(projectInfoId);
        return keyFlagVO.getPaySignage();
    }

    /**
     * 查询交付工单
     *
     * @param orderInfoId 工单id
     * @return ProjOrderInfo
     */
    public ProjOrderInfo getOrderInfo(Long orderInfoId) {
        ProjOrderInfo projOrderInfo = orderInfoMapper.selectById(orderInfoId);
        if (ObjectUtil.isEmpty(projOrderInfo)) {
            throw new RuntimeException("未查询到交付工单信息. orderInfoId: " + orderInfoId);
        }
        return projOrderInfo;
    }

    /**
     * 获取方案分公司经理id
     *
     * @param orderInfoId 工单id
     * @return Long
     */
    @Override
    public String getBranchMgrLeaderYunyingId(Long orderInfoId) {
        ProjContractInfo contractInfo = getContract(orderInfoId);
        long contractPresaleteamId = contractInfo.getContractPresaleteamId();
        String yunyingUserId;
        // 首先从运营平台获取, 若失败, 原流程查询部门负责人id, 2025-02-21号晚修改
        yunyingUserId = getBranchMgrLeaderYunyingIdByYunying(orderInfoId, contractPresaleteamId);
        if (StrUtil.isBlank(yunyingUserId)) {
            // 获取负责人id
            yunyingUserId = getBranchMgrLeaderYunyingIdByTeamId(contractPresaleteamId);
        }
        if (StrUtil.isBlank(yunyingUserId)) {
            log.error("未查询到方案审核人员. orderInfoId: {}, contractPresaleteamId: {}, contractInfo: {}", orderInfoId,
                    contractPresaleteamId, JSONUtil.toJsonStr(contractInfo));
            throw new CustomException("未查询到方案审核人员.");
        }
        return yunyingUserId;
    }

    /**
     * 获取方案负责人
     *
     * @param contractPresaleteamId 方案或销售中心id. 此id派工时生成
     * @return 负责人id(运营用户id)
     */
    private String getBranchMgrLeaderYunyingIdByTeamId(Long contractPresaleteamId) {
        List<SysDept> sysDepts = sysDeptMapper.selectList(new QueryWrapper<SysDept>().eq("dept_yunying_id",
                contractPresaleteamId));
        if (CollUtil.isEmpty(sysDepts)) {
            throw new RuntimeException("未查询到运营平台团队信息.");
        }
        SysDept sysDept = sysDepts.get(0);
        // 获取团队长用户id
        return sysDept.getDeptLeaderYunyingId();
    }

    /**
     * 向运营平台获取方案负责人
     *
     * @param orderInfoId           软件工单id
     * @param contractPresaleteamId 方案或销售中心id
     * @return 负责人id(运营用户id)
     */
    private String getBranchMgrLeaderYunyingIdByYunying(Long orderInfoId, Long contractPresaleteamId) {
        ProjOrderInfo orderInfo = commonService.getOrderInfo(orderInfoId);
        GetPreSaleUserParam baseReq = GetPreSaleUserParam.builder()
                .projId(StrUtil.toString(orderInfo.getYyOrderId()))
                .token(YunYingServiceImpl.TOKEN)
                .preSaleOrgId(StrUtil.toString(contractPresaleteamId))
                .build();
        try {
            // 获取登录人账号
            String loginUserAccount = userHelper.getCurrentUser().getAccount();
            // 存入参
            sysOperLogService.apiOperLogInsertObjAry("向运营平台获取方案经理", "入参", Log.LogOperType.SEARCH.getCode(),
                    loginUserAccount, baseReq);
            log.info("向运营平台获取方案经理, loginUserAccount: {}, 入参: {}", loginUserAccount, JSONUtil.toJsonStr(baseReq));
            // 调用运营接口
            GetPreSaleUserResp preSaleUserResp =
                    yunyingFeignClient.getPreSaleUser(loginUserAccount, baseReq);
            sysOperLogService.apiOperLogInsert(preSaleUserResp, "向运营平台获取方案经理", "出参",
                    Log.LogOperType.SEARCH.getCode());
            if (ObjectUtil.isEmpty(preSaleUserResp) || preSaleUserResp.getCode() != NumberEnum.NO_200.num().intValue()) {
                log.error("向运营平台获取方案经理异常. 接口返回值异常: {}", preSaleUserResp);
                return StrUtil.EMPTY;
            }
            Long userId = preSaleUserResp.getObj().getPreSaleUserId();
            log.info("向运营平台获取方案经理: {}", userId);
            return StrUtil.toString(userId);
        } catch (Throwable e) {
            log.error("向运营平台获取方案经理异常. message: {}, e=", e.getMessage(), e);
            sysOperLogService.apiOperLogInsert(e.getMessage(), "向运营平台获取方案经理", "出参",
                    Log.LogOperType.SEARCH.getCode());
            return StrUtil.EMPTY;
        }
    }

    /**
     * 发送异常消息提醒
     *
     * @param orderInfoId 软件工单id
     * @param baseReq     请求值
     */
    private void sendErrMsg(Long orderInfoId, GetPreSaleUserParam baseReq) {
        log.error("向运营平台未查询到方案经理. 接口查询异常. 入参: {}", JSONUtil.toJsonStr(baseReq));
        Long projectInfoId = -1L;
        ProjProjectInfo projectInfo = null;
        try {
            projectInfo = commonService.getProjectInfoByOrderInfoId(orderInfoId);
            if (ObjectUtil.isNotEmpty(projectInfo)) {
                projectInfoId = projectInfo.getProjectInfoId();
            }
        } catch (Throwable e) {
            log.error("向运营平台未查询到方案经理, 获取项目信息异常.");
        }
        String keyMsg = ObjectUtil.isNotEmpty(projectInfo)
                ? projectInfo.getProjectName() + StrUtil.DASHED + projectInfo.getProjectNumber()
                : "项目工单id(orderInfoId): " + orderInfoId;
        String message = "向运营平台获取方案经理异常, " + keyMsg;
        exceptionMessageService.sendErrMessageForEntry(projectInfoId, message);
    }

    @Override
    public Long getProjectMgrPersonId(Long projectInfoId) {
        return getProjectInfo(projectInfoId).getProjectLeaderId();
    }

    @Override
    public SysUser getSysUserByProjectInfoId(Long projectInfoId) {
        Long sysUserId = getProjectMgrPersonId(projectInfoId);
        return sysUserMapper.getUserById(sysUserId);
    }

    @Override
    public List<ProjSettlementOrderInfo> findSettlementOrderInfoExcludeRelation(Long projectInfoId,
                                                                                boolean includeSelf) {
        List<ProjSettlementOrderInfo> settlementOrderInfos = findSettlementOrderInfo(projectInfoId);
        ProjProjectInfo projectInfo = getProjectInfo(projectInfoId);
        List<ProjProjectOrderRelation> relations =
                projectOrderRelationMapper.selectList(new QueryWrapper<ProjProjectOrderRelation>()
                        .eq("custom_info_id", projectInfo.getCustomInfoId()));
        if (includeSelf) {
            relations =
                    relations.stream().filter(e -> e.getProjectInfoId().longValue() != projectInfoId).collect(Collectors.toList());
        }
        // 获取工单信息（排除已经绑定的工单）
        List<ProjProjectOrderRelation> finalRelations = relations;
        settlementOrderInfos =
                settlementOrderInfos.stream().filter(e -> finalRelations.stream().noneMatch(f -> f.getYyOrderId() == e.getYyOrderId().longValue())).collect(Collectors.toList());
        if (CollUtil.isEmpty(settlementOrderInfos)) {
            return null;
        }
        return settlementOrderInfos;
    }

}
