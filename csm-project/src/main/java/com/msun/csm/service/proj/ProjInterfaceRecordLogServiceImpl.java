package com.msun.csm.service.proj;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjInterfaceRecordLog;
import com.msun.csm.dao.mapper.proj.ProjInterfaceRecordLogMapper;
import com.msun.csm.model.vo.ProjInterfaceRecordLogVo;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.util.SnowFlakeUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/9/12
 */
@Slf4j
@Service
public class ProjInterfaceRecordLogServiceImpl implements ProjInterfaceRecordLogService {

    @Resource
    private ProjInterfaceRecordLogMapper projInterfaceRecordLogMapper;

    @Resource
    private UserHelper userHelper;

    /**
     * 三方接口添加流程日志
     *
     * @param recordName
     * @param thirdInterfaceId
     * @return
     */
    @Override
    public Result saveInterfaceRecordLog(String recordName, Long thirdInterfaceId, String comments) {
        ProjInterfaceRecordLog projInterfaceRecordLog = new ProjInterfaceRecordLog();
        projInterfaceRecordLog.setInterfaceRecordLogId(SnowFlakeUtil.getId());
        projInterfaceRecordLog.setRecordName(recordName);
        projInterfaceRecordLog.setComments(comments);
        projInterfaceRecordLog.setThirdInterfaceId(thirdInterfaceId);
        projInterfaceRecordLog.setCreaterId(userHelper.getCurrentUser().getSysUserId());
        projInterfaceRecordLog.setCreaterName(userHelper.getCurrentUser().getUserName());
        projInterfaceRecordLog.setCreateTime(new Date());
        projInterfaceRecordLog.setOperateUserPhone(userHelper.getCurrentUser().getPhone());
        projInterfaceRecordLogMapper.insert(projInterfaceRecordLog);
        return Result.success();
    }

    /**
     * 三方接口日志信息查询
     *
     * @param thirdInterfaceId
     * @return
     */
    @Override
    public Result<List<ProjInterfaceRecordLogVo>> selectInterfaceRecordLog(Long thirdInterfaceId) {
        List<ProjInterfaceRecordLogVo> list = new ArrayList<>();
        List<ProjInterfaceRecordLog> projInterfaceRecordLogs = projInterfaceRecordLogMapper.selectList(new QueryWrapper<ProjInterfaceRecordLog>()
                .eq("third_interface_id", thirdInterfaceId)
                .orderByDesc("create_time")
        );
        for (ProjInterfaceRecordLog log : projInterfaceRecordLogs) {
            ProjInterfaceRecordLogVo projInterfaceRecordLogVo = new ProjInterfaceRecordLogVo();
            projInterfaceRecordLogVo.setInterfaceRecordLogId(log.getInterfaceRecordLogId());
            projInterfaceRecordLogVo.setLogTitle(log.getRecordName());
            projInterfaceRecordLogVo.setThirdInterfaceId(log.getThirdInterfaceId());
            projInterfaceRecordLogVo.setOperateContent(log.getComments());
            projInterfaceRecordLogVo.setOperateUserName(log.getCreaterName());
            projInterfaceRecordLogVo.setOperateTime(log.getCreateTime());
            projInterfaceRecordLogVo.setOperateUserPhone(log.getOperateUserPhone());
            list.add(projInterfaceRecordLogVo);
        }
        return Result.success(list);
    }

}
