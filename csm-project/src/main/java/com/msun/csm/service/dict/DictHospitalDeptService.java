package com.msun.csm.service.dict;

import java.util.List;

import com.msun.csm.dao.entity.dict.DictHospitalDept;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/9/23
 */

public interface DictHospitalDeptService {

    int deleteByPrimaryKey(Long dictHospitalDeptId);

    int insert(DictHospitalDept record);

    int insertOrUpdate(DictHospitalDept record);

    int insertOrUpdateSelective(DictHospitalDept record);

    int insertSelective(DictHospitalDept record);

    DictHospitalDept selectByPrimaryKey(Long dictHospitalDeptId);

    int updateByPrimaryKeySelective(DictHospitalDept record);

    int updateByPrimaryKey(DictHospitalDept record);

    int updateBatch(List<DictHospitalDept> list);

    int updateBatchSelective(List<DictHospitalDept> list);

    int batchInsert(List<DictHospitalDept> list);
}
