package com.msun.csm.service.proj;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.msun.csm.util.PageHelperUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.msun.csm.dao.entity.dict.DictProductExtend;
import com.msun.csm.dao.entity.proj.GetBoardRecordParam;
import com.msun.csm.dao.entity.proj.GetBoardRecordParamPO;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjProductDeliverRecord;
import com.msun.csm.dao.entity.proj.ProjSurveyPlan;
import com.msun.csm.dao.mapper.dict.DictProductExtendMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyPlanMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyReportMapper;
import com.msun.csm.dao.mapper.projform.ProjSurveyFormMapper;
import com.msun.csm.dao.mapper.report.ConfigCustomBackendLimitMapper;
import com.msun.csm.dao.mapper.report.ProjStatisticalReportMainMapper;
import com.msun.csm.model.dto.BackendOperationBoardInfo;
import com.msun.csm.model.dto.ProjProductBacklogDTO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.req.projform.ProjSurveyFormReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportReq;
import com.msun.csm.model.req.projreport.statis.ProjStatisticalReportMainPageReq;
import com.msun.csm.model.resp.projform.ProjSurveyFormResp;
import com.msun.csm.model.resp.projform.ProjSurveyReprotFormCount;
import com.msun.csm.model.resp.projreport.ProjSurveyReportResp;
import com.msun.csm.model.resp.statis.ProjStatisticalReportMainSelectResp;
import com.msun.csm.model.vo.ProjProductBacklogVO;

import cn.hutool.core.date.DateUtil;

@Service
public class BackendOperationBoardServiceImpl implements BackendOperationBoardService {

    @Resource
    private ConfigCustomBackendLimitMapper customBackendLimitMapper;

    @Resource
    private ProjSurveyReportMapper projSurveyReportMapper;

    @Resource
    private ProjSurveyFormMapper projSurveyFormMapper;

    @Resource
    private ProjStatisticalReportMainMapper projStatisticalReportMainMapper;

    @Resource
    private ProjProductDeliverRecordService projProductDeliverRecordService;

    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;

    @Resource
    private ProjProductBacklogService productBacklogService;

    @Resource
    private ProjSurveyPlanMapper projSurveyPlanMapper;

    @Resource
    private DictProductExtendMapper dictProductExtendMapper;

    @Override
    public List<BackendOperationBoardInfo> getBoardRecord(GetBoardRecordParam param) {
        GetBoardRecordParamPO paramPO = new GetBoardRecordParamPO();
        paramPO.setKeyWord(param.getKey());
        paramPO.setProjectDeliverStatusList(param.getProjectDeliverStatusList());
        if (StringUtils.isNotBlank(param.getStartTime())) {
            paramPO.setStartTime(DateUtil.beginOfDay(DateUtil.parseDate(param.getStartTime())));
        }
        if (StringUtils.isNotBlank(param.getEndTime())) {
            paramPO.setEndTime(DateUtil.endOfDay(DateUtil.parseDate(param.getEndTime())));
        }

        List<BackendOperationBoardInfo> backendOperationBoardInfo = customBackendLimitMapper.getBackendOperationBoardInfo(paramPO);
        if (CollectionUtils.isEmpty(backendOperationBoardInfo)) {
            return new ArrayList<>();
        }
        backendOperationBoardInfo.forEach(item -> {
            // 打印报表
            ProjSurveyReportReq projSurveyReportReq = new ProjSurveyReportReq();
            projSurveyReportReq.setProjectInfoId(item.getProjectInfoId());
            projSurveyReportReq.setPageNum(1);
            projSurveyReportReq.setPageSize(5000);
            List<ProjSurveyReprotFormCount> surveyReportCount = projSurveyReportMapper.selectSurveyReportCount(projSurveyReportReq);
            List<ProjSurveyReportResp> surveyReportList = PageHelperUtil.queryPage(projSurveyReportReq.getPageNum(), projSurveyReportReq.getPageSize(), page -> projSurveyReportMapper.selectSurveyReportByPage(projSurveyReportReq));
            // 调研完成数：审核通过、制作完成两个状态算完成
            long surveyReportCompleted = surveyReportList.stream().filter(surveyReport -> {
                if (Integer.valueOf(5).equals(surveyReport.getFinishStatus())) {
                    return true;
                }
                return Integer.valueOf(1).equals(surveyReport.getFinishStatus());
            }).count();
            // 准备完成数：制作完成状态算完成
            long preparatReportCompleted = surveyReportList.stream().filter(surveyReport -> {
                return Integer.valueOf(1).equals(surveyReport.getFinishStatus());
            }).count();


            // 表单
            ProjSurveyFormReq projSurveyFormReq = new ProjSurveyFormReq();
            projSurveyFormReq.setProjectInfoId(item.getProjectInfoId());
            projSurveyFormReq.setPageNum(1);
            projSurveyFormReq.setPageSize(5000);
            List<ProjSurveyReprotFormCount> surveyFormCount = projSurveyFormMapper.selectSurveyFormCount(projSurveyFormReq);
            // 列表查询
            List<ProjSurveyFormResp> surveyFormList = PageHelperUtil.queryPage(projSurveyReportReq.getPageNum(), projSurveyFormReq.getPageSize(), page -> projSurveyFormMapper.selectSurveyFormByPage(projSurveyFormReq));
            // 调研完成数：审核通过、制作完成两个状态算完成
            long surveyFormCompleted = surveyFormList.stream().filter(surveyReport -> {
                if (Integer.valueOf(5).equals(surveyReport.getFinishStatus())) {
                    return true;
                }
                return Integer.valueOf(1).equals(surveyReport.getFinishStatus());
            }).count();
            // 准备完成数：制作完成状态算完成
            long preparatFormCompleted = surveyFormList.stream().filter(surveyReport -> {
                return Integer.valueOf(1).equals(surveyReport.getFinishStatus());
            }).count();


            // 统计报表
            ProjStatisticalReportMainPageReq projStatisticalReportMainPageReq = new ProjStatisticalReportMainPageReq();
            projStatisticalReportMainPageReq.setProjectInfoId(item.getProjectInfoId());
            projStatisticalReportMainPageReq.setPageNum(1);
            projStatisticalReportMainPageReq.setPageSize(5000);
            List<ProjStatisticalReportMainSelectResp> statisticalReportList = projStatisticalReportMainMapper.findReportPage(projStatisticalReportMainPageReq);

            // 调研完成数：裁定通过、制作中、已下沉状态算完成
            long surveyStatisticsReportCompleted = statisticalReportList.stream().filter(surveyReport -> {
                if (Integer.valueOf(13).equals(surveyReport.getReportStatus())) {
                    return true;
                }
                if (Integer.valueOf(21).equals(surveyReport.getReportStatus())) {
                    return true;
                }
                return Integer.valueOf(31).equals(surveyReport.getReportStatus());
            }).count();
            // 准备完成数：已下沉状态算完成
            long preparatStatisticsReportCompleted = statisticalReportList.stream().filter(surveyReport -> {
                return Integer.valueOf(31).equals(surveyReport.getReportStatus());
            }).count();

            ProjProductBacklogDTO productBacklogDTO = new ProjProductBacklogDTO();
            productBacklogDTO.setProjectInfoId(item.getProjectInfoId());
            List<ProjProductBacklogVO> backlogList = productBacklogService.findProductBacklogVOS(productBacklogDTO);
            long preparatProductCompleted = backlogList.stream().filter(backlog -> Integer.valueOf(1).equals(backlog.getCompleteStatus())).count();

            // 医院数量
            SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
            selectHospitalDTO.setProjectInfoId(item.getProjectInfoId());
            List<ProjHospitalInfo> hospitalList = projHospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);

            // 需要调研的产品
            List<ProjProductDeliverRecord> surveyProductRecord = projProductDeliverRecordService.getSurveyProductDeliverRecord(item.getProjectInfoId(), true);
            // 产品总数量
            List<ProjProductDeliverRecord> allProductRecord = projProductDeliverRecordService.getSurveyProductDeliverRecord(item.getProjectInfoId(), null);
            // 无需调研的产品
            List<ProjProductDeliverRecord> notNeedSurveyProductRecord = projProductDeliverRecordService.getSurveyProductDeliverRecord(item.getProjectInfoId(), false);
            // 产品业务调研
            item.setSurveyProductCompleted(Math.min(this.getSurveyProductCompleted(item.getProjectInfoId()), surveyProductRecord.size()));
            item.setSurveyProductTotal(surveyProductRecord.size());

            // 打印报表调研
            item.setSurveyReportCompleted(Integer.valueOf(String.valueOf(surveyReportCompleted)));
            item.setSurveyReportTotal(surveyReportCount.get(0).getTotalCount());
            // 统计报表调研
            item.setSurveyStatisticsReportCompleted(Integer.valueOf(String.valueOf(surveyStatisticsReportCompleted)));
            item.setSurveyStatisticsReportTotal(statisticalReportList.size());
            // 表单调研
            item.setSurveyFormCompleted(Integer.valueOf(String.valueOf(surveyFormCompleted)));
            item.setSurveyFormTotal(surveyFormCount.get(0).getTotalCount());
            // 产品准备工作
            item.setPreparatProductCompleted(Integer.valueOf(String.valueOf(preparatProductCompleted)));
            item.setPreparatProductTotal(backlogList.size());
            // 打印报表准备
            item.setPreparatReportCompleted(Integer.valueOf(String.valueOf(preparatReportCompleted)));
            item.setPreparatReportTotal(surveyReportCount.get(0).getTotalCount());
            // 统计报表准备
            item.setPreparatStatisticsReportCompleted(Integer.valueOf(String.valueOf(preparatStatisticsReportCompleted)));
            item.setPreparatStatisticsReportTotal(statisticalReportList.size());
            // 表单准备
            item.setPreparatFormCompleted(Integer.valueOf(String.valueOf(preparatFormCompleted)));
            item.setPreparatFormTotal(surveyFormCount.get(0).getTotalCount());

            item.setHospitalCount(hospitalList.size());
            item.setProductTotalCount(allProductRecord.size());
            item.setNotNeedSurveyProductCount(notNeedSurveyProductRecord.size());
        });
        return backendOperationBoardInfo;
    }

    private Integer getSurveyProductCompleted(Long projectInfoId) {
        // 不需要调研的数据
        List<DictProductExtend> dictProductExtendList = dictProductExtendMapper.selectList(
                new QueryWrapper<DictProductExtend>()
                        .eq("is_deleted", 0)
                        .eq("survey_flag", 0)
        );
        List<Long> notNeedSurveyYyProductIdList = dictProductExtendList.stream().map(DictProductExtend::getYyProductId).collect(Collectors.toList());

        List<ProjSurveyPlan> projSurveyPlanList = projSurveyPlanMapper.selectList(new QueryWrapper<ProjSurveyPlan>().eq("project_info_id", projectInfoId).eq("is_deleted", 0));
        if (CollectionUtils.isEmpty(projSurveyPlanList)) {
            return 0;
        }
        projSurveyPlanList = projSurveyPlanList.stream().filter(item -> !notNeedSurveyYyProductIdList.contains(item.getYyProductId())).collect(Collectors.toList());
        int completeCount = 0;
        Map<Long, List<ProjSurveyPlan>> groupByProduct = projSurveyPlanList.stream().collect(Collectors.groupingBy(ProjSurveyPlan::getYyProductId));
        Set<Long> yyProductIdSet = groupByProduct.keySet();
        for (Long yyProductId : yyProductIdSet) {
            List<ProjSurveyPlan> surveyPlanList = groupByProduct.get(yyProductId);
            Set<Integer> statusSet = surveyPlanList.stream().map(ProjSurveyPlan::getCompleteStatus).collect(Collectors.toSet());
            if (statusSet.contains(3) || statusSet.contains(1)) {
                completeCount = completeCount + 1;
            }
        }
        return completeCount;
    }
}
