package com.msun.csm.service.proj;

import java.util.List;

import org.springframework.web.bind.annotation.RequestBody;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.SimpleId;
import com.msun.csm.dao.entity.proj.ProjManualCreateLog;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectMember;
import com.msun.csm.model.dto.ProjManualCreateLogQueryDto;
import com.msun.csm.model.dto.ProjProjectInfoDTO;
import com.msun.csm.model.req.project.CustomProjectReq;
import com.msun.csm.model.req.project.FindProjectParamReq;
import com.msun.csm.model.req.project.ProjectMergeReq;
import com.msun.csm.model.req.project.ProjectSelectNotOnlineReq;
import com.msun.csm.model.req.project.ProjectSplitReq;
import com.msun.csm.model.resp.project.FindProjectParamResp;
import com.msun.csm.model.resp.project.FindProjectResp;
import com.msun.csm.model.resp.project.ProjectNotOnlineResp;
import com.msun.csm.model.resp.project.ProjectSplitResp;
import com.msun.csm.model.vo.ProjectToolsOptionsForProjectInfoVO;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/24
 */

public interface ProjProjectInfoService {


    @Deprecated
    int insert(ProjProjectInfo record);


    ProjProjectInfo selectByPrimaryKey(Long projectInfoId);

    int updateByPrimaryKeySelective(ProjProjectInfo record);

    int updateByPrimaryKey(ProjProjectInfo record);

    /**
     * 获取项目查询参数
     *
     * @return
     * <AUTHOR>
     * @date 2024/4/25
     */
    Result<FindProjectParamResp> getParam(FindProjectParamReq req);

    /**
     * 查询项目信息
     *
     * @param param
     * @return
     */
    Result<FindProjectResp> findProjectInfo(ProjProjectInfoDTO param);

    /**
     * 更新项目的初始化标识
     *
     * @param projectInfoId
     */
    Integer updateInitFlag(Long projectInfoId);

    /**
     * 根据项目id查询当前登录人员是否是此项目的项目经理
     *
     * @param projectInfoId
     * @return
     */
    Result isProjectLeader(String projectInfoId);

    /**
     * 查询项目工单产品信息
     *
     * @param projectId
     * @return
     */
    Result<ProjectSplitResp> getProjectOrderProductInfo(SimpleId projectId);

    /**
     * 查询可以合并项目
     *
     * @param req
     * @return
     */
    Result<List<ProjectSplitResp>> findMergeProject(SimpleId req);


    /**
     * 项目拆分
     *
     * @param req
     * @return
     */
    Result splitProject(ProjectSplitReq req);

    /**
     * 项目合并
     *
     * @param req
     * @return
     */
    Result mergeProject(ProjectMergeReq req);


    /**
     * 通过运营平台工单id查询项目id
     *
     * @param yyOrderId
     * @return
     */
    List<Long> getProjectInfoIdByOrderId(Long yyOrderId);

    /**
     * 一键批量同步四大模块所有配置到云健康
     *
     * @param projectId
     * @return
     */
    Result<FindProjectResp> syncCommConfigArea(Long projectId);

    /**
     * 项目工具==查询项目信息
     *
     * @return
     */
    Result<List<ProjectToolsOptionsForProjectInfoVO>> selectProjectToolsOptionsForProjectInfo(ProjProjectInfoDTO dto);


    /**
     * 查询当前项目下的客户是否存在已上线项目
     */
    List<ProjProjectInfo> getProjectInfoByCustomerInfoId(Long customerInfoId);

    /**
     * 说明: 新项目历程备初始化时调用基础数据初始化接口
     *
     * @param projectInfoId
     * @return:void
     * @author: Yhongmin
     * @createAt: 2024/7/19 15:14
     * @remark: Copyright
     */
    void addDictData(Long projectInfoId);

    /**
     * 【分院模式】  该项目是否 是非首期的分院实施模式【true:是；false: 否】
     *
     * @param projectInfoId
     * @return
     */
    Boolean isBranchHospital(Long projectInfoId);

    /**
     * 后台管理-创建自定义项目
     *
     * @param req
     * @return
     */
    Result createCustomProject(CustomProjectReq req);

    /**
     * 获取手动创建自定义项目操作日志的分页列表
     *
     * @param queryDto
     * @return
     */
    Result<PageInfo<ProjManualCreateLog>> queryProjManualCreateLogPageList(@RequestBody ProjManualCreateLogQueryDto queryDto);

    /**
     * 判断当前项目是否开启了小前端大后端
     *
     * @param projectInfoId 项目ID
     * @return true-开启了小前端大后端；false-未开启小前端大后端
     */
    boolean isSmallFrontBigBack(Long projectInfoId);

    /**
     * 判断当前项目是否开启了产品业务调研的后端审核
     *
     * @param projectInfoId 项目ID
     * @return true-开启后端审核；false-未开启后端审核
     */
    boolean isOpenSurveyAudit(Long projectInfoId);

    ProjProjectMember getBackLeader(Long projectInfoId);

    /**
     * 获取项目调研考核时间
     *
     * @param projectInfoId 项目ID
     * @return 调研考核时间，格式为yyyy-MM-dd HH:mm:ss
     */
    String getSurveyAssessmentTime(Long projectInfoId);

    /**
     * 获取项目调研考核时间
     *
     * @param projectInfoId 项目ID
     * @return 调研考核时间，格式为yyyy-MM-dd HH:mm:ss
     */
    String getSurveyAssessmentTime(ProjProjectInfo projectInfoId);

    /**
     * 获取开启了产品业务调研审核的项目
     *
     * @return 开启了产品业务调研审核的项目
     */
    List<ProjProjectInfo> getOpenSurveyProductAudit();

    /**
     * 查询未上线项目
     * @param req
     * @return
     */
    Result<List<ProjectNotOnlineResp>> selectNotOnlineProjectList(ProjectSelectNotOnlineReq req);
}
