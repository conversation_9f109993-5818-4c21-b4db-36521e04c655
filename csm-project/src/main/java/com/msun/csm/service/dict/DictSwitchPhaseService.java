package com.msun.csm.service.dict;

import java.util.List;

import com.msun.csm.dao.entity.dict.DictSwitchPhase;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/9/23
 */

public interface DictSwitchPhaseService {

    int deleteByPrimaryKey(Long dictSwitchPhaseId);

    int insert(DictSwitchPhase record);

    int insertOrUpdate(DictSwitchPhase record);

    int insertOrUpdateSelective(DictSwitchPhase record);

    int insertSelective(DictSwitchPhase record);

    DictSwitchPhase selectByPrimaryKey(Long dictSwitchPhaseId);

    int updateByPrimaryKeySelective(DictSwitchPhase record);

    int updateByPrimaryKey(DictSwitchPhase record);

    int updateBatch(List<DictSwitchPhase> list);

    int updateBatchSelective(List<DictSwitchPhase> list);

    int batchInsert(List<DictSwitchPhase> list);

}
