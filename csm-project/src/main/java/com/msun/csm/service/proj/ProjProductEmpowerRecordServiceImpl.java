package com.msun.csm.service.proj;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.msun.csm.dao.entity.proj.ProjProductEmpowerRecord;
import com.msun.csm.dao.mapper.proj.ProjProductEmpowerRecordMapper;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/24
 */

@Service
public class ProjProductEmpowerRecordServiceImpl implements ProjProductEmpowerRecordService {

    @Resource
    private ProjProductEmpowerRecordMapper projProductEmpowerRecordMapper;

    @Override
    public int deleteByPrimaryKey(Long productEmpowerRecordId) {
        return projProductEmpowerRecordMapper.deleteByPrimaryKey(productEmpowerRecordId);
    }

    @Override
    public int insert(ProjProductEmpowerRecord record) {
        return projProductEmpowerRecordMapper.insert(record);
    }

    @Override
    public int insertOrUpdate(ProjProductEmpowerRecord record) {
        return projProductEmpowerRecordMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(ProjProductEmpowerRecord record) {
        return projProductEmpowerRecordMapper.insertOrUpdateSelective(record);
    }

    @Override
    public int insertSelective(ProjProductEmpowerRecord record) {
        return projProductEmpowerRecordMapper.insertSelective(record);
    }

    @Override
    public ProjProductEmpowerRecord selectByPrimaryKey(Long productEmpowerRecordId) {
        return projProductEmpowerRecordMapper.selectByPrimaryKey(productEmpowerRecordId);
    }

    @Override
    public int updateByPrimaryKeySelective(ProjProductEmpowerRecord record) {
        return projProductEmpowerRecordMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ProjProductEmpowerRecord record) {
        return projProductEmpowerRecordMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<ProjProductEmpowerRecord> list) {
        return projProductEmpowerRecordMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<ProjProductEmpowerRecord> list) {
        return projProductEmpowerRecordMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<ProjProductEmpowerRecord> list) {
        return projProductEmpowerRecordMapper.batchInsert(list);
    }

    @Override
    public List<ProjProductEmpowerRecord> findEmpowerRecord(Long projectInfoId, List<Long> productId) {
        return projProductEmpowerRecordMapper.findEmpowerRecord(projectInfoId, productId);
    }
}
