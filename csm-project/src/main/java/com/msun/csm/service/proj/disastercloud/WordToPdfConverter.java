package com.msun.csm.service.proj.disastercloud;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;

import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;

import com.itextpdf.text.Chunk;
import com.itextpdf.text.Document;
import com.itextpdf.text.Element;
import com.itextpdf.text.Font;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfWriter;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class WordToPdfConverter {


    public static void convert(FileInputStream fis, FileOutputStream fos) {
        try {
            // 读取Word文档
            XWPFDocument document = new XWPFDocument(fis);
            // 创建PDF文档
            Document pdfDocument = new Document();
            // 将PDF文档写入输出文件
            PdfWriter.getInstance(pdfDocument, fos);
            // 打开PDF文档
            pdfDocument.open();
            // 将Word文档内容写入PDF文档
            WordToPdfWriter writer = new WordToPdfWriter(pdfDocument);
            writer.write(document);
            // 关闭PDF文档
            pdfDocument.close();
            log.info("Word转PDF成功！");
        } catch (Exception e) {
            log.error("Word转PDF失败：{}", e.getMessage());
        }
    }

    public static void convert(String inputFilePath, String outputFilePath) throws FileNotFoundException {
        convert(new FileInputStream(inputFilePath), new FileOutputStream(outputFilePath));
    }
}

class WordToPdfWriter {

    private Document pdfDocument;

    WordToPdfWriter(Document pdfDocument) {
        this.pdfDocument = pdfDocument;
    }

    public void write(XWPFDocument document) throws Exception {
        //设置基础中文字体
        BaseFont bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        //给字体添加样式
        Font fontChinese = new Font(bfChinese, 15, Font.BOLD);
        Font normal = new Font(bfChinese, 15, Font.NORMAL);
        // 逐页将Word文档内容写入PDF文档
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            String text = paragraph.getText();
            Paragraph pdfParagraph;
            if (text.contains("$a")) {
                pdfParagraph = new Paragraph(text.replace("$a", StrUtil.EMPTY), fontChinese);
                pdfParagraph.add("\n");
                pdfParagraph.setAlignment(Element.ALIGN_CENTER);
            } else if (text.contains("$b")) {
                pdfParagraph = new Paragraph(text.replace("$b", StrUtil.EMPTY), normal);
                pdfParagraph.setFirstLineIndent(30);
            } else if (text.contains("$c")) {
                pdfParagraph = new Paragraph(text.replace("$c", StrUtil.EMPTY), normal);
                pdfParagraph.add("\n");
                pdfParagraph.setAlignment(Element.ALIGN_RIGHT);
            } else if (text.contains("$d")) {
                pdfParagraph = new Paragraph(text.replace("$d", StrUtil.EMPTY), normal);
                pdfParagraph.add("\n");
                pdfParagraph.setAlignment(Element.ALIGN_RIGHT);
            } else if (text.contains("$e")) {
                pdfParagraph = new Paragraph(text.replace("$e", StrUtil.EMPTY));
                pdfParagraph.add("\n");
            } else {
                pdfParagraph = new Paragraph(text, normal);
                Chunk chunk = new Chunk();
                chunk.setLineHeight(12);
                pdfParagraph.add(chunk);
            }
            pdfParagraph.setMultipliedLeading(2);
            pdfDocument.add(pdfParagraph);
        }
    }
}
