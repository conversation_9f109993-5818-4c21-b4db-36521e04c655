package com.msun.csm.service.proj;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjResearchPlanForChecked;
import com.msun.csm.dao.entity.tmp.TmpProjectNewVsOld;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneTaskMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.dao.mapper.tmp.TmpProjectNewVsOldMapper;
import com.msun.csm.model.dto.FieldInfoDTO;
import com.msun.csm.model.dto.PreparatoryWorkDTO;
import com.msun.csm.model.dto.PreparatoryWorkListHeaderInfoDTO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.param.ProjectInfoIdParam;
import com.msun.csm.model.param.UpdateProjMilestoneInfoAfterAuditPmoParam;
import com.msun.csm.model.vo.ProjMilestoneInfoVO;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Service
public class PreparatoryWorkServiceImpl implements PreparatoryWorkService {

    @Resource
    private ProjMilestoneInfoMapper projMilestoneInfoMapper;

    @Resource
    private ProjMilestoneTaskMapper projMilestoneTaskMapper;

    @Resource
    private TmpProjectNewVsOldMapper tmpProjectNewVsOldMapper;
    @Resource
    private ProjProjectInfoMapper projectInfoMapper;

    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Override
    public PreparatoryWorkDTO getPreparatoryWorkList(Long projectInfoId, Long hospitalInfoId) {
        // 先获取新老项目对照关系
        QueryWrapper<TmpProjectNewVsOld> queryWrapper = new QueryWrapper<TmpProjectNewVsOld>()
                .eq("new_project_info_id", projectInfoId);
        TmpProjectNewVsOld tmpProjectNewVsOld = tmpProjectNewVsOldMapper.selectOne(queryWrapper);
        // 需要展示的动态表头列
        List<ProjMilestoneInfo> milestoneInfoListNeedCheck
                = projMilestoneInfoMapper.getMilestoneInfoByProjectInfoIdOnlyNeedCheck(projectInfoId);
        // 根据需要展示的动态表头列获取到researchCodeList
        List<String> researchCodeList = milestoneInfoListNeedCheck.stream().map(ProjMilestoneInfo::getMilestoneNodeCode)
                .collect(Collectors.toList());
        // 根据projectInfoId和hospitalInfoId获取所有的项目任务计划
        List<ProjResearchPlanForChecked> projResearchPlanForCheckedByProjectInfoId
                = projMilestoneTaskMapper.getProjResearchPlanForCheckedByProjectInfoId(projectInfoId, hospitalInfoId);
        // 根据researchCode过滤出需要检查的项目任务计划
        List<ProjResearchPlanForChecked> projResearchPlanForChecked = projResearchPlanForCheckedByProjectInfoId.stream()
                .filter(item -> researchCodeList.contains(item.getMilestoneNodeCode())).collect(Collectors.toList());
        ProjectInfoIdParam projectInfoIdParam = new ProjectInfoIdParam();
        projectInfoIdParam.setProjectInfoId(projectInfoId);
        projectInfoIdParam.setHospitalInfoId(hospitalInfoId);
        List<ProjHospitalInfo> projHospitalInfoList = this.getProjHospitalInfoList(projectInfoIdParam);
        // 组装表头
        List<PreparatoryWorkListHeaderInfoDTO> headerInfoList = this.createHeader(milestoneInfoListNeedCheck);
        // 开始拼装行数据
        List<Map<String, Object>> list = new ArrayList<>(projHospitalInfoList.size());
        for (int row = 1; row <= projHospitalInfoList.size(); row++) {
            ProjHospitalInfo projHospitalInfo = projHospitalInfoList.get(row - 1);
            Long hospitalId = projHospitalInfo.getHospitalInfoId();
            Map<String, Object> map = new HashMap<>();
            for (int column = 1; column <= headerInfoList.size(); column++) {
                int finalColumn = column;
                PreparatoryWorkListHeaderInfoDTO infoDTO = headerInfoList.stream()
                        .filter(item -> finalColumn == item.getSortNumber()).findFirst().get();
                if ("hospitalName".equals(infoDTO.getField())) {
                    map.put(infoDTO.getField(), projHospitalInfo.getHospitalName());
                } else if ("hospitalId".equals(infoDTO.getField())) {
                    map.put(infoDTO.getField(), hospitalId);
                } else {
                    map.put(infoDTO.getField(),
                            this.getFieldInfoByField(infoDTO.getField(), hospitalId, row, projResearchPlanForChecked,
                                    milestoneInfoListNeedCheck, tmpProjectNewVsOld, projHospitalInfo));
                }
            }
            list.add(map);
        }
        // 固定header
        List<String> headerNameList = new ArrayList<>();
        headerNameList.add("number");
        headerNameList.add("hospitalName");
        headerNameList.add("hospitalId");
        headerNameList.add("projectLeader");
        headerNameList.add("checked");
        // 最终返回的表头信息只包含活动列
        List<PreparatoryWorkListHeaderInfoDTO> collect = headerInfoList.stream()
                .filter(item -> !headerNameList.contains(item.getField())).collect(Collectors.toList());
        return PreparatoryWorkDTO.builder().headerInfo(collect).data(list).build();
    }

    /**
     * 医院开通状态：0-未开通、11-处理中、21-已开通
     *
     * @param hospitalOpenStatus
     * @return
     */
    private String convertHospitalOpenStatus(Integer hospitalOpenStatus) {
        if (Integer.valueOf(11).equals(hospitalOpenStatus)) {
            return "处理中";
        }
        if (Integer.valueOf(21).equals(hospitalOpenStatus)) {
            return "已完成";
        }
        return "未开通";

    }


    /**
     * 根据字段名获取字段值
     *
     * @param field                      字段名
     * @param hospitalId                 医院ID
     * @param row                        行号
     * @param researchPlanForCheckedList 项目任务计划
     * @return 字段值
     */
    private Object getFieldInfoByField(String field, Long hospitalId, Integer row,
                                       List<ProjResearchPlanForChecked> researchPlanForCheckedList,
                                       List<ProjMilestoneInfo> milestoneInfoListNeedCheck,
                                       TmpProjectNewVsOld tmpProjectNewVsOld, ProjHospitalInfo projHospitalInfo) {
        // 序号列直接返回行号
        if ("number".equals(field)) {
            return row;
        }
        if ("hospitalName".equals(field)) {
            ProjResearchPlanForChecked collect = researchPlanForCheckedList.stream()
                    .filter(item -> hospitalId.equals(item.getHospitalInfoId())).findFirst().orElse(null);
            log.info("转换列值，hospitalName列，collect={}", collect);
            if (collect != null) {
                return collect.getHospitalName();
            }
            return "";

        }
        if ("projectLeader".equals(field)) {
            ProjResearchPlanForChecked collect = researchPlanForCheckedList.stream()
                    .filter(item -> hospitalId.equals(item.getHospitalInfoId())).findFirst().orElse(null);
            log.info("转换列值，projectLeader列，collect={}", collect);
            if (collect != null) {
                return collect.getLeaderName();
            }
            return "";
        }
        if ("checked".equals(field)) {
            List<ProjResearchPlanForChecked> collect = researchPlanForCheckedList.stream()
                    .filter(item -> hospitalId.equals(item.getHospitalInfoId())).collect(Collectors.toList());
            log.info("转换列值，checked列，collect={}", collect);
            List<Integer> completeStatusList = collect.stream().map(ProjResearchPlanForChecked::getCompleteStatus)
                    .collect(Collectors.toList());
            log.info("转换列值，checked列，completeStatusList={}", completeStatusList);
            // 存在未完成
            if (CollectionUtils.isEmpty(completeStatusList) || completeStatusList.contains(0)) {
                return "不满足";
            } else {
                // 不存在未完成
                return "满足";
            }
        }
        ProjResearchPlanForChecked collect = researchPlanForCheckedList.stream()
                .filter(item -> hospitalId.equals(item.getHospitalInfoId()) && field.equals(
                        underlineToHump(item.getMilestoneNodeCode()))).findFirst().orElse(null);
        log.info("转换列值，默认列，collect={}", collect);
        String showValue;
        if (collect == null) {
            showValue = "未开始";
        } else if (collect.getCompleteStatus() == 0) {
            showValue = "未完成";
        } else {
            showValue = "已完成";
        }
        if ("cloudResource".equals(field)) {
            showValue = this.convertHospitalOpenStatus(projHospitalInfo.getHospitalOpenStatus());
        }
        List<ProjMilestoneInfo> collect2 = milestoneInfoListNeedCheck.stream()
                .filter(item -> field.equals(this.underlineToHump(item.getMilestoneNodeCode())))
                .collect(Collectors.toList());
        return FieldInfoDTO
                .builder()
                .showValue(showValue)
                .isComponent(this.getIsComponent(collect2.get(0)))
                // TODO 处理跳转的URL，替换参数占位符
                .jumpUrl(this.getJumpUrl(collect2.get(0), tmpProjectNewVsOld))
                .name(collect2.get(0).getMilestoneNodeName())
                .build();
    }

    private List<ProjHospitalInfo> getProjHospitalInfoList(ProjectInfoIdParam param) {
        if (null != param.getHospitalInfoId()) {
            List<ProjHospitalInfo> list = new ArrayList<>();
            QueryWrapper<ProjHospitalInfo> queryWrapper = new QueryWrapper<ProjHospitalInfo>()
                    .eq("hospital_info_id", param.getHospitalInfoId());
            ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectOne(queryWrapper);
            if (null == hospitalInfo) {
                return new ArrayList<>();
            }
            list.add(hospitalInfo);
            return list;
        }
        // 查询当前项目下的医院信息
        SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
        selectHospitalDTO.setProjectInfoId(param.getProjectInfoId());
        // 查询当前项目下的医院信息(医院名称、终端数)
        return projHospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);
    }

    /**
     * 转换前端组件名
     *
     * @param projMilestoneInfo 项目里程碑信息
     * @return 前端组件名
     */
    private String getIsComponent(ProjMilestoneInfo projMilestoneInfo) {
        // 如果检查跳转路径不是空并且不是以“/”开头，说明维护的是前端组件
        if (StringUtils.isNotBlank(projMilestoneInfo.getCheckUrl()) && !projMilestoneInfo.getCheckUrl()
                .startsWith("/")) {
            return projMilestoneInfo.getCheckUrl();
        }
        // 取里程碑的前端组件名
        return projMilestoneInfo.getIsComponent();
    }

    /**
     * 转换跳转地址
     *
     * @param projMilestoneInfo 项目里程碑信息
     * @return 跳转地址
     */
    private String getJumpUrl(ProjMilestoneInfo projMilestoneInfo, TmpProjectNewVsOld tmpProjectNewVsOld) {
        // 如果检查跳转路径不是空，并且是以“/开头”，说明是后端跳转链接
        if (StringUtils.isNotBlank(projMilestoneInfo.getCheckUrl()) && projMilestoneInfo.getCheckUrl()
                .startsWith("/")) {
            String checkUrl = projMilestoneInfo.getCheckUrl();
            String replace = checkUrl
                    .replace("#{projectId}", String.valueOf(tmpProjectNewVsOld.getOldProjectInfoId()))
                    .replace("#{customerId}", String.valueOf(tmpProjectNewVsOld.getOldCustomId()))
                    .replace("#{detailId}", String.valueOf(projMilestoneInfo.getMilestoneInfoId()));
            return replace;
        }
        // 如果不是以“/”开头，说明维护的是前端组件
        return null;
    }

    /**
     * 创建表头行
     *
     * @param list 项目里程碑信息
     * @return 表头行
     */
    private List<PreparatoryWorkListHeaderInfoDTO> createHeader(List<ProjMilestoneInfo> list) {
        List<PreparatoryWorkListHeaderInfoDTO> headerInfoList = new ArrayList<>();
        PreparatoryWorkListHeaderInfoDTO infoDTO1 = PreparatoryWorkListHeaderInfoDTO.builder().sortNumber(1)
                .name("序号").field("number").build();
        headerInfoList.add(infoDTO1);
        PreparatoryWorkListHeaderInfoDTO infoDTO2 = PreparatoryWorkListHeaderInfoDTO.builder()
                .sortNumber(1 + headerInfoList.size()).name("医院名称").field("hospitalName").build();
        headerInfoList.add(infoDTO2);
        PreparatoryWorkListHeaderInfoDTO infoDTO4 = PreparatoryWorkListHeaderInfoDTO.builder()
                .sortNumber(1 + headerInfoList.size()).name("医院ID").field("hospitalId").build();
        headerInfoList.add(infoDTO4);
        if (!CollectionUtils.isEmpty(list)) {
            List<ProjMilestoneInfo> sortList = list.stream().sorted(Comparator.comparing(ProjMilestoneInfo::getOrderNo))
                    .collect(Collectors.toList());
            for (ProjMilestoneInfo projMilestoneInfoVO : sortList) {
                PreparatoryWorkListHeaderInfoDTO infoDTO = PreparatoryWorkListHeaderInfoDTO.builder()
                        .sortNumber(1 + headerInfoList.size()).name(projMilestoneInfoVO.getSimpleMilestoneNodeName())
                        .field(underlineToHump(projMilestoneInfoVO.getMilestoneNodeCode())).build();
                headerInfoList.add(infoDTO);
            }
        }
        PreparatoryWorkListHeaderInfoDTO infoDTO3 = PreparatoryWorkListHeaderInfoDTO.builder()
                .sortNumber(1 + headerInfoList.size()).name("满足上线条件").field("checked").build();
        headerInfoList.add(infoDTO3);
        PreparatoryWorkListHeaderInfoDTO infoDTO5 = PreparatoryWorkListHeaderInfoDTO.builder()
                .sortNumber(1 + headerInfoList.size()).name("项目负责人").field("projectLeader").build();
        headerInfoList.add(infoDTO5);
        return headerInfoList;
    }


    /**
     * 下划线字符串转驼峰
     *
     * @param str 下划线字符串
     * @return 驼峰字符串
     */
    private String underlineToHump(String str) {
        if (StringUtils.isBlank(str)) {
            return "";
        }
        String[] split = str.split("_");
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < split.length; i++) {
            String item = split[i];
            if (i > 0) {
                if (item.length() > 1) {
                    result.append(item.substring(0, 1).toUpperCase()).append(item.substring(1));
                } else {
                    result.append(item.toUpperCase());
                }
            } else {
                result.append(item);
            }
        }
        return result.toString();
    }

    @Override
    public void exportPreparatoryWorkListExcel(Long projectInfoId, Long hospitalInfoId, HttpServletResponse response) {
        PreparatoryWorkDTO preparatoryWorkDTO = this.getPreparatoryWorkListForExport(projectInfoId, hospitalInfoId);
        List<PreparatoryWorkListHeaderInfoDTO> headerInfo = preparatoryWorkDTO.getHeaderInfo();
        String[] title = new String[headerInfo.size()];
        for (PreparatoryWorkListHeaderInfoDTO preparatoryWorkListHeaderInfoDTO : headerInfo) {
            title[preparatoryWorkListHeaderInfoDTO.getSortNumber() - 1] = preparatoryWorkListHeaderInfoDTO.getName();
        }
        this.createExcelXls("准备工作列表", preparatoryWorkDTO, title, response);
    }


    private PreparatoryWorkDTO getPreparatoryWorkListForExport(Long projectInfoId, Long hospitalInfoId) {
        // 需要展示的动态表头列
        List<ProjMilestoneInfo> milestoneInfoListNeedCheck
                = projMilestoneInfoMapper.getMilestoneInfoByProjectInfoIdOnlyNeedCheck(projectInfoId);
        // 根据需要展示的动态表头列获取到researchCodeList
        List<String> researchCodeList = milestoneInfoListNeedCheck.stream().map(ProjMilestoneInfo::getMilestoneNodeCode)
                .collect(Collectors.toList());
        // 根据projectInfoId和hospitalInfoId获取所有的项目任务计划
        List<ProjResearchPlanForChecked> projResearchPlanForCheckedByProjectInfoId
                = projMilestoneTaskMapper.getProjResearchPlanForCheckedByProjectInfoId(projectInfoId, hospitalInfoId);
        // 根据researchCode过滤出需要检查的项目任务计划
        List<ProjResearchPlanForChecked> projResearchPlanForChecked = projResearchPlanForCheckedByProjectInfoId.stream()
                .filter(item -> researchCodeList.contains(item.getMilestoneNodeCode())).collect(Collectors.toList());
        ProjectInfoIdParam projectInfoIdParam = new ProjectInfoIdParam();
        projectInfoIdParam.setProjectInfoId(projectInfoId);
        projectInfoIdParam.setHospitalInfoId(hospitalInfoId);
        List<ProjHospitalInfo> projHospitalInfoList = this.getProjHospitalInfoList(projectInfoIdParam);
        // 组装表头
        List<PreparatoryWorkListHeaderInfoDTO> headerInfoList = this.createHeader(milestoneInfoListNeedCheck);
        // 开始拼装行数据
        List<Map<String, Object>> list = new ArrayList<>(projHospitalInfoList.size());
        for (int row = 1; row <= projHospitalInfoList.size(); row++) {
            ProjHospitalInfo projHospitalInfo = projHospitalInfoList.get(row - 1);
            Long hospitalId = projHospitalInfo.getHospitalInfoId();
            Map<String, Object> map = new HashMap<>();
            for (int column = 1; column <= headerInfoList.size(); column++) {
                int finalColumn = column;
                PreparatoryWorkListHeaderInfoDTO infoDTO = headerInfoList.stream()
                        .filter(item -> finalColumn == item.getSortNumber()).findFirst().get();
                map.put(infoDTO.getField(), this.getFieldInfoByFieldForExport(infoDTO.getField(), hospitalId, row,
                        projResearchPlanForChecked, projHospitalInfo));
            }
            list.add(map);
        }
        return PreparatoryWorkDTO.builder().headerInfo(headerInfoList).data(list).build();
    }

    /**
     * 根据字段名获取字段值
     *
     * @param field                      字段名
     * @param hospitalId                 医院ID
     * @param row                        行号
     * @param researchPlanForCheckedList 项目任务计划
     * @return 字段值
     */
    private String getFieldInfoByFieldForExport(String field, Long hospitalId, Integer row,
                                                List<ProjResearchPlanForChecked> researchPlanForCheckedList,
                                                ProjHospitalInfo projHospitalInfo) {
        // 序号列直接返回行号
        if ("number".equals(field)) {
            return String.valueOf(row);
        }
        if ("hospitalId".equals(field)) {
            return String.valueOf(hospitalId);
        }
        if ("hospitalName".equals(field)) {
            ProjResearchPlanForChecked collect = researchPlanForCheckedList.stream()
                    .filter(item -> hospitalId.equals(item.getHospitalInfoId())).findFirst().orElse(null);
            log.info("转换列值，hospitalName列，collect={}", collect);
            String hospitalName = "";
            if (collect != null) {
                hospitalName = collect.getHospitalName();
            }
            return hospitalName;
        }
        if ("projectLeader".equals(field)) {
            ProjResearchPlanForChecked collect = researchPlanForCheckedList.stream()
                    .filter(item -> hospitalId.equals(item.getHospitalInfoId())).findFirst().orElse(null);
            log.info("转换列值，projectLeader列，collect={}", collect);
            String projectLeader = "";
            if (collect != null) {
                projectLeader = collect.getLeaderName();
            }
            return projectLeader;
        }
        if ("checked".equals(field)) {
            List<ProjResearchPlanForChecked> collect = researchPlanForCheckedList.stream()
                    .filter(item -> hospitalId.equals(item.getHospitalInfoId())).collect(Collectors.toList());
            log.info("转换列值，checked列，collect={}", collect);
            List<Integer> completeStatusList = collect.stream().map(ProjResearchPlanForChecked::getCompleteStatus)
                    .collect(Collectors.toList());
            log.info("转换列值，checked列，completeStatusList={}", completeStatusList);
            // 存在未完成
            String result;
            if (CollectionUtils.isEmpty(completeStatusList) || completeStatusList.contains(0)) {
                result = "不满足";
            } else {
                // 不存在未完成
                result = "满足";
            }
            return result;
        }
        if ("cloudResource".equals(field)) {
            return this.convertHospitalOpenStatus(projHospitalInfo.getHospitalOpenStatus());
        }
        ProjResearchPlanForChecked collect = researchPlanForCheckedList.stream()
                .filter(item -> hospitalId.equals(item.getHospitalInfoId()) && field.equals(
                        underlineToHump(item.getMilestoneNodeCode()))).findFirst().orElse(null);
        log.info("转换列值，默认列，collect={}", collect);
        String result;
        if (collect == null) {
            result = "未开始";
        } else if (collect.getCompleteStatus() == 0) {
            result = "未完成";
        } else {
            result = "已完成";
        }
        return result;
    }


    public void createExcelXls(String sheetName, PreparatoryWorkDTO preparatoryWorkDTO, String[] title,
                               HttpServletResponse response) {
        // 新建Excel文件
        try (HSSFWorkbook hssfWorkbook = new HSSFWorkbook();
             ServletOutputStream fileOutputStream = response.getOutputStream()) {
            // 设置单元格样式
            CellStyle cellStyle = hssfWorkbook.createCellStyle();
            // 水平样式为左对齐
            cellStyle.setAlignment(HorizontalAlignment.LEFT);
            // 垂直样式为居中对齐
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            // 自动换行
            cellStyle.setWrapText(true);
            // 创建sheet页
            hssfWorkbook.createSheet(sheetName);
            // sheet页对象
            HSSFSheet sheet = hssfWorkbook.getSheet(sheetName);
            // 创建表头行（第一行）
            HSSFRow row = sheet.createRow(0);
            // 行高：设置为-1表示自适应高度
            row.setHeightInPoints(-1);
            // 写入表头行
            for (int titleRowNumber = 0; titleRowNumber < title.length; titleRowNumber++) {
                HSSFCell cell = row.createCell(titleRowNumber, CellType.BLANK);
                cell.setCellValue(title[titleRowNumber]);
                cell.setCellStyle(cellStyle);
            }
            hssfWorkbook.write(fileOutputStream);
            // 获得表头行对象
            HSSFRow titleRow = sheet.getRow(0);
            // 获取表头行列数
            int columnCount = titleRow.getLastCellNum();
            List<Map<String, Object>> data = preparatoryWorkDTO.getData();
            for (int rowId = 0; rowId < data.size(); rowId++) {
                Map<String, Object> dataItem = data.get(rowId);
                // 创建行
                HSSFRow newRow = sheet.createRow(rowId + 1);
                // 行高：设置为-1表示自适应高度
                newRow.setHeightInPoints(-1);
                for (int columnIndex = 0; columnIndex < columnCount; columnIndex++) {
                    String stringCellValue = titleRow.getCell(columnIndex).getStringCellValue();
                    String field = preparatoryWorkDTO.getHeaderInfo().stream()
                            .filter(item -> stringCellValue.equals(item.getName())).findFirst().get().getField();
                    HSSFCell cell = newRow.createCell(columnIndex);
                    cell.setCellStyle(cellStyle);
                    cell.setCellValue(ObjectUtil.isEmpty(dataItem.get(field)) ? "" : dataItem.get(field).toString());
                }
            }
            String fileName = "准备工作列表-" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN)
                    + ".xlsx";
            response.reset();
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName));
            hssfWorkbook.write(fileOutputStream);
        } catch (Exception e) {
            log.error("导出准备工作列表，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateProjMilestoneInfoAfterAuditPmo(
            UpdateProjMilestoneInfoAfterAuditPmoParam updateProjMilestoneInfoAfterAuditPmoParam) {
        // 先获取新老项目对照关系
        QueryWrapper<TmpProjectNewVsOld> queryWrapper = new QueryWrapper<TmpProjectNewVsOld>()
                .eq("old_project_info_id", updateProjMilestoneInfoAfterAuditPmoParam.getOldProjectId());
        TmpProjectNewVsOld tmpProjectNewVsOld = tmpProjectNewVsOldMapper.selectOne(queryWrapper);
        log.info("PMO审核成功之后更新里程碑信息，新老项目对照关系={}", JSON.toJSONString(tmpProjectNewVsOld));
        if (null == tmpProjectNewVsOld) {
            log.info("PMO审核成功之后更新里程碑信息，新老项目对照关系不存在，老项目ID={}",
                    updateProjMilestoneInfoAfterAuditPmoParam.getOldProjectId());
            return false;
        }
        SysUser sysUser = null;
        if (null != updateProjMilestoneInfoAfterAuditPmoParam.getYunYingUserId()) {
            sysUser = sysUserMapper.selectUserIdByYungyingId(
                    Convert.toStr(updateProjMilestoneInfoAfterAuditPmoParam.getYunYingUserId()));
        }
        // 根据新系统项目ID和里程碑节点编码获取里程碑信息
        ProjMilestoneInfo proposalInfo = new ProjMilestoneInfo();
        proposalInfo.setProjectInfoId(tmpProjectNewVsOld.getNewProjectInfoId());
        proposalInfo.setMilestoneNodeCode(updateProjMilestoneInfoAfterAuditPmoParam.getMilestoneNodeCode());
        ProjMilestoneInfoVO milestoneInfoVO = projMilestoneInfoMapper.getMilestoneInfoByParams(proposalInfo);
        log.info("PMO审核成功之后更新里程碑信息，里程碑信息={}", JSON.toJSONString(milestoneInfoVO));
        UpdateWrapper<ProjMilestoneInfo> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("milestone_info_id", milestoneInfoVO.getMilestoneInfoId());
        ProjMilestoneInfo product = new ProjMilestoneInfo();
        product.setMilestoneStatus(Integer.valueOf("1"));
        product.setUpdaterId(sysUser == null ? null : sysUser.getSysUserId());
        product.setNodeHeadId(sysUser == null ? null : sysUser.getSysUserId());
        Date updateDate = new Date();
        product.setUpdateTime(updateDate);
        product.setActualCompTime(updateDate);
        int update = projMilestoneInfoMapper.update(product, updateWrapper);
        //回更主表信息
        switch (updateProjMilestoneInfoAfterAuditPmoParam.getMilestoneNodeCode()) {
            // 调研阶段项目管理办公室审核
            case "survey_check":
                updateProProjectInfo(tmpProjectNewVsOld.getNewProjectInfoId(), 2);
                break;
            //确认达到入驻条件
            case "project_entry":
                updateProProjectInfo(tmpProjectNewVsOld.getNewProjectInfoId(), 3);
                break;
            //确认达到入驻条件
            case "preparat_check":
                updateProProjectInfo(tmpProjectNewVsOld.getNewProjectInfoId(), 4);
                break;
            default:
                break;
        }
        return update > 0;
    }

    /**
     * 说明: 同步回更项目信息-项目状态 1已派工、2已调研、3已入驻、4、准备完成、5已上线、6已验收、7已启动
     *
     * @param projectInfoId
     * @param projectDeliverStatus
     * @return:void
     * @author: Yhongmin
     * @createAt: 2024/5/27 14:29
     * @remark: Copyright
     */
    public void updateProProjectInfo(Long projectInfoId, Integer projectDeliverStatus) {
        projectInfoMapper.update(null, new UpdateWrapper<ProjProjectInfo>().eq("project_info_id", projectInfoId)
                .set("project_deliver_status", projectDeliverStatus));
    }
}
