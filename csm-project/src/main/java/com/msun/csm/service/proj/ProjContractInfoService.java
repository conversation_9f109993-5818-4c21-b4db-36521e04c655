package com.msun.csm.service.proj;

import java.util.List;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjContractInfo;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/24
 */

public interface ProjContractInfoService {

    /**
     * 说明: 导入合同同步合同信息-工单信息，云资源信息
     * @param file
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/7/18 17:59
     * @remark: Copyright
      */
    Result excelImportSyncProjContractInfo(@RequestParam("file") MultipartFile file);

    int deleteByPrimaryKey(Long contractInfoId);

    int insert(ProjContractInfo record);

    int insertOrUpdate(ProjContractInfo record);

    int insertOrUpdateSelective(ProjContractInfo record);

    int insertSelective(ProjContractInfo record);

    ProjContractInfo selectByPrimaryKey(Long contractInfoId);

    int updateByPrimaryKeySelective(ProjContractInfo record);

    int updateByPrimaryKey(ProjContractInfo record);

    int updateBatch(List<ProjContractInfo> list);

    int updateBatchSelective(List<ProjContractInfo> list);

    int batchInsert(List<ProjContractInfo> list);
}
