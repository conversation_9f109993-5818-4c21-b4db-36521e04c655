package com.msun.csm.service.proj;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjNetworkDetDomainRecord;
import com.msun.csm.model.dto.networkdetect.ProjNetworkDetDomainRecordDTO;
import com.msun.csm.model.req.ProjNetworkDetectResultReq;
import com.msun.csm.model.vo.networkdetected.ProjNetworkDetDomainRecordVO;

/**
 * <AUTHOR>
 * @since 2024-05-14 09:10:11
 */

public interface ProjNetworkDetDomainRecordService {

    /**
     * 分页查询
     *
     * @param projNetworkDetDnsRecordDTO
     * @return
     */
    Result<PageInfo<ProjNetworkDetDomainRecordVO>> findNetworkDetDomainInfoList(
            ProjNetworkDetDomainRecordDTO projNetworkDetDnsRecordDTO);

    /**
     * 具体查询集合
     * @param dto
     * @return
     */
    List<ProjNetworkDetDomainRecord> findNetworkDetDomainInfoListImpl(ProjNetworkDetDomainRecordDTO dto);

    /**
     * 插入, 删除历史数据
     * @param resultReq
     */
    void insertBeforeDelete(ProjNetworkDetectResultReq resultReq);

    int deleteNotThieDet(Long logId, String localIpAddress);
}
