package com.msun.csm.service.dict;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.dict.DictProduct;
import com.msun.csm.dao.entity.dict.DictProductExtend;
import com.msun.csm.dao.entity.dict.DictProductLog;
import com.msun.csm.dao.entity.dict.DictProductVsModules;
import com.msun.csm.dao.mapper.dict.DictProductExtendMapper;
import com.msun.csm.dao.mapper.dict.DictProductLogMapper;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.dao.mapper.dict.DictProductVsModulesMapper;
import com.msun.csm.model.dto.DictProductDTO;
import com.msun.csm.model.vo.dict.DictProductVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.util.SnowFlakeUtil;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/25
 */

@Service
public class DictProductServiceImpl implements DictProductService {

    @Resource
    private DictProductMapper dictProductMapper;

    @Resource
    private DictProductExtendMapper dictProductExtendMapper;

    @Resource
    private DictProductLogMapper dictProductLogMapper;

    @Resource
    private UserHelper userHelper;

    @Resource
    private DictProductVsModulesMapper dictProductVsModulesMapper;

    @Override
    public int deleteByPrimaryKey(Long productDictId) {
        return dictProductMapper.deleteByPrimaryKey(productDictId);
    }


    @Override
    public int insertOrUpdate(DictProduct record) {
        return dictProductMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(DictProduct record) {
        return dictProductMapper.insertOrUpdateSelective(record);
    }

    @Override
    public int insertSelective(DictProduct record) {
        return dictProductMapper.insertSelective(record);
    }

    @Override
    public DictProduct selectByPrimaryKey(Long productDictId) {
        return dictProductMapper.selectByPrimaryKey(productDictId);
    }

    @Override
    public int updateByPrimaryKeySelective(DictProduct record) {
        return dictProductMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(DictProduct record) {
        return dictProductMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<DictProduct> list) {
        return dictProductMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<DictProduct> list) {
        return dictProductMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<DictProduct> list) {
        return dictProductMapper.batchInsert(list);
    }

    @Override
    public List<Long> getNotSurveyYyProductId() {
        List<DictProductExtend> productExtendList = dictProductExtendMapper.selectList(
                new QueryWrapper<DictProductExtend>()
                        .eq("is_deleted", 0)
                        .eq("survey_flag", 0)
        );
        if (CollectionUtils.isEmpty(productExtendList)) {
            return new ArrayList<>();
        }
        return productExtendList.stream().map(DictProductExtend::getYyProductId).collect(Collectors.toList());
    }

    /**
     * 查询产品字典
     *
     * @param productName
     * @param productSource
     * @return
     */
    @Override
    public Result<List<DictProductVO>> findDictProductList(String productName, Integer productSource) {
        List<DictProductVO> productList = dictProductMapper.findDictProductList(productName, productSource);
        for (DictProductVO productVO : productList) {
            //处理产品类型回显
            productVO.setProductTypeName(handleProductTypeName(productVO.getProductType()));
        }
        return Result.success(productList);
    }

    /**
     * 处理产品类型回显
     *
     * @param productType
     * @return
     */
    private String handleProductTypeName(String productType) {
        String productTypeName = "";
        switch (productType) {
            case "1":
                productTypeName = "自研软件";
                break;
            case "2":
                productTypeName = "硬件";
                break;
            case "3":
                productTypeName = "耗材";
                break;
            case "4":
                productTypeName = "接口";
                break;
            case "5":
                productTypeName = "软件服务费";
                break;
            case "6":
                productTypeName = "硬件服务费";
                break;
            case "7":
                productTypeName = "容灾";
                break;
            case "8":
                productTypeName = "外采软件";
                break;
            case "9":
                productTypeName = "云资源";
                break;
            default:
                productTypeName = "未知";
                break;
        }
        return productTypeName;
    }

    /**
     * 新增产品字典
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result saveDictProduct(DictProductDTO dto) {
        List<DictProduct> productList = dictProductMapper.selectList(new QueryWrapper<DictProduct>()
                .eq("product_name", dto.getProductName()));
        if (CollectionUtils.isNotEmpty(productList)) {
            return Result.fail("《" + dto.getProductName() + "》产品已存在，请勿重复添加！");
        }
        //生成交付平台产品ID-负数值
        Long productId = dictProductMapper.getMinProductDictId();
        if (productId > 0) {
            productId = -1L;
        }
        DictProduct dictProduct = new DictProduct();
        dictProduct.setProductName(dto.getProductName());
        dictProduct.setProductDictId(productId);
        dictProduct.setYyProductId(productId);
        dictProduct.setProductType("1");
        dictProduct.setYyIsCloud(1);
        dictProduct.setYyIsHaveModule(0);
        dictProductMapper.insert(dictProduct);
        //保存日志
        DictProductLog dictProductLog = new DictProductLog();
        dictProductLog.setDictProductLogId(SnowFlakeUtil.getId());
        dictProductLog.setOperateModule("dict");
        dictProductLog.setOperateModuleName("产品字典");
        dictProductLog.setOperateType(1);
        dictProductLog.setOperateContent("新增产品字典：" + dto.getProductName());
        dictProductLog.setCreateName(userHelper.getCurrentUser().getUserName());
        dictProductLogMapper.insert(dictProductLog);
        return Result.success();
    }

    /**
     * 删除产品字典
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result deleteDictProduct(DictProductDTO dto) {
        if (dto.getProductDictId() > 0) {
            return Result.fail("只能删除来源为交付平台的产品！");
        }
        DictProduct dictProduct = dictProductMapper.selectById(dto.getProductDictId());
        dictProductMapper.deleteById(dto.getProductDictId());
        //删除工单产品与实施产品对照
        dictProductMapper.deleteOrderVsDeliver(dto.getProductDictId());
        //删除工单产品与部署产品对照
        dictProductMapper.deleteOrderVsArrange(dto.getProductDictId());
        //删除工单产品与授权菜单对照
        dictProductMapper.deleteOrderVsEmpower(dto.getProductDictId());
        //保存日志
        DictProductLog dictProductLog = new DictProductLog();
        dictProductLog.setDictProductLogId(SnowFlakeUtil.getId());
        dictProductLog.setOperateModule("dict");
        dictProductLog.setOperateModuleName("产品字典");
        dictProductLog.setOperateType(3);
        dictProductLog.setOperateContent("删除产品字典：" + dictProduct.getProductName());
        dictProductLog.setCreateName(userHelper.getCurrentUser().getUserName());
        dictProductLogMapper.insert(dictProductLog);
        return Result.success();
    }

    /**
     * 查询产品字典
     *
     * @param productName
     * @return
     */
    @Override
    public Result<List<BaseIdNameResp>> findDictProducts(String productName) {
        return Result.success(dictProductMapper.findDictProducts(productName));
    }

    @Override
    public String getProductNameByYyProductId(Long yyProductId) {
        BaseIdNameResp productInfo = dictProductMapper.getProductByYyProductId(yyProductId);
        if (productInfo != null) {
            return productInfo.getName();
        }
        DictProductVsModules moduleInfo = dictProductVsModulesMapper.getModuleByYyModuleId(yyProductId);
        if (moduleInfo != null) {
            return moduleInfo.getYyModuleName();
        }
        return null;
    }
}
