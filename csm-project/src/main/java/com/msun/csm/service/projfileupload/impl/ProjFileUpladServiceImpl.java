package com.msun.csm.service.projfileupload.impl;

import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.msun.csm.util.PageHelperUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.msun.csm.common.enums.projprojectinfo.ProjectDeliverStatusEnums;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysConfig;
import com.msun.csm.dao.entity.dict.DictPlanItemFile;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjProjectAcceptance;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectPlanFile;
import com.msun.csm.dao.mapper.config.SysConfigMapper;
import com.msun.csm.dao.mapper.dict.DictPlanItemFileMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectAcceptanceMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectPlanFileMapper;
import com.msun.csm.model.dto.projfileupload.FindCustomFilePageDTO;
import com.msun.csm.model.dto.projfileupload.FindProjectFileDTO;
import com.msun.csm.model.dto.projfileupload.ProjPlanFileDelDTO;
import com.msun.csm.model.dto.projfileupload.ProjectFileUploadDTO;
import com.msun.csm.model.dto.projfileupload.ProjectPlanFileDTO;
import com.msun.csm.model.vo.projfileupload.FindCustomFileVO;
import com.msun.csm.model.vo.projfileupload.FindProjectFileVO;
import com.msun.csm.model.vo.projfileupload.ProjPlanUploadFileVO;
import com.msun.csm.model.vo.projfileupload.ProjectPlanFileDetailVO;
import com.msun.csm.model.vo.projfileupload.ProjectPlanFileVO;
import com.msun.csm.service.projfileupload.ProjFileUpladService;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.obs.OBSClientUtils;
import com.obs.services.model.PutObjectResult;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
public class ProjFileUpladServiceImpl implements ProjFileUpladService {

    @Resource
    private SysConfigMapper sysConfigMapper;

    @Resource
    private DictPlanItemFileMapper dictPlanItemFileMapper;

    @Resource
    private ProjProjectPlanFileMapper projProjectPlanFileMapper;

    @Resource
    private ProjCustomInfoMapper customInfoMapper;

    @Resource
    private ProjProjectInfoMapper projectInfoMapper;

    @Resource
    private ProjProjectAcceptanceMapper projProjectAcceptanceMapper;

    @Value("${project.obs.prePath}")
    private String prePath;

    /**
     * 查询项目计划上传资料信息
     *
     * @param projectPlanFileDTO
     * @return
     */
    @Override
    public Result<ProjectPlanFileVO> findProjectPlanFiles(ProjectPlanFileDTO projectPlanFileDTO) {
        ProjectPlanFileVO projectPlanFileVO = new ProjectPlanFileVO();
        //查询资料上传配置开启情况
        SysConfig sysConfig = sysConfigMapper.selectConfigByName("project_file_upload");
        if (ObjectUtil.isEmpty(sysConfig) || "0".equals(sysConfig.getConfigValue())) {
            projectPlanFileVO.setFileUploadFlag(false);
            projectPlanFileVO.setProjectPlanFileList(new ArrayList<>());
            return Result.success(projectPlanFileVO);
        }
        ProjProjectInfo projectInfo = projectInfoMapper.selectById(projectPlanFileDTO.getProjectInfoId());
        //是否是二次验收项目
        ProjProjectAcceptance projProjectAcceptance = projProjectAcceptanceMapper.selectByProjectInfoIdOne(projectInfo.getProjectInfoId());
        boolean isSecondAcceptance = false;
        if (ObjectUtil.isNotEmpty(projProjectAcceptance)) {
            isSecondAcceptance = projProjectAcceptance.getRequiredAcceptanceTimes() == 2;
        }
        //开启了配置或者部分项目开启 并且当前项目包含在配置中 并且是首期项目 并且不是二次验收项目
        if (("1".equals(sysConfig.getConfigValue()) || sysConfig.getConfigValue().contains(String.valueOf(projectPlanFileDTO.getProjectInfoId()))) && projectInfo.getHisFlag() == 1 && !isSecondAcceptance) {
            projectPlanFileVO.setFileUploadFlag(true);
            ProjCustomInfo customInfo = customInfoMapper.selectById(projectInfo.getCustomInfoId());
            //查询项目计划需上传资料字典信息
            List<ProjectPlanFileDetailVO> projectPlanFileList = Lists.newArrayList();
            List<DictPlanItemFile> dictPlanItemFileList = dictPlanItemFileMapper.selectList(new QueryWrapper<DictPlanItemFile>().eq("plan_item_code", projectPlanFileDTO.getPlanItemCode()).eq(customInfo.getTelesalesFlag() == 1, "telesales_flag", 1).eq(projectInfo.getProjectType() == 1, "monomer_flag", 1).eq(projectInfo.getProjectType() == 2, "region_flag", 1).eq(projectInfo.getUpgradationType() == 1, "old_for_new_flag", 1).eq(projectInfo.getUpgradationType() == 2, "new_customer_flag", 1).eq(projectInfo.getHisFlag() == 1, "initial_flag", 1).eq(projectInfo.getHisFlag() == 0, "uninitial_flag", 1).eq(projectInfo.getSupervisorFlag() == 1, "supervisor_flag", 1));
            if (CollectionUtil.isNotEmpty(dictPlanItemFileList)) {
                //查询已上传的资料
                List<ProjProjectPlanFile> fileList = projProjectPlanFileMapper.selectList(new QueryWrapper<ProjProjectPlanFile>().eq("project_info_id", projectPlanFileDTO.getProjectInfoId()).eq("plan_stage_code", projectPlanFileDTO.getPlanStageCode()).eq("plan_item_code", projectPlanFileDTO.getPlanItemCode()));
                if (CollectionUtil.isNotEmpty(fileList)) {
                    fileList.forEach(file -> {
                        file.setFilePath(OBSClientUtils.getTemporaryUrl(file.getFilePath(), 3600));
                    });
                }
                for (DictPlanItemFile dictPlanItemFile : dictPlanItemFileList) {
                    ProjectPlanFileDetailVO projectPlanFileDetailVO = new ProjectPlanFileDetailVO();
                    projectPlanFileDetailVO.setDictPlanItemFileId(dictPlanItemFile.getDictPlanItemFileId());
                    projectPlanFileDetailVO.setPlanItemCode(dictPlanItemFile.getPlanItemCode());
                    projectPlanFileDetailVO.setFileDesc(dictPlanItemFile.getFileDesc());
                    projectPlanFileDetailVO.setNeedFlag(dictPlanItemFile.getNeedFlag());
                    projectPlanFileDetailVO.setFileList(fileList.stream().filter(file -> file.getDictPlanItemFileId().equals(dictPlanItemFile.getDictPlanItemFileId())).collect(Collectors.toList()));
                    projectPlanFileList.add(projectPlanFileDetailVO);
                }
            }
            projectPlanFileVO.setProjectPlanFileList(projectPlanFileList);
        } else {
            projectPlanFileVO.setFileUploadFlag(false);
            projectPlanFileVO.setProjectPlanFileList(new ArrayList<>());
            return Result.success(projectPlanFileVO);
        }
        return Result.success(projectPlanFileVO);
    }

    /**
     * 上传附件
     *
     * @param projectFileUploadDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<ProjPlanUploadFileVO> uploadProjectPlanFile(ProjectFileUploadDTO projectFileUploadDTO) {
        // 构建路径
        String path = prePath + projectFileUploadDTO.getProjectInfoId() + StrUtil.SLASH + projectFileUploadDTO.getPlanItemCode() + StrUtil.SLASH + projectFileUploadDTO.getFile().getOriginalFilename();
        // 获取文件名（不包括路径）
        String fileName = path.substring(path.lastIndexOf(StrUtil.SLASH) + 1);
        // 获取文件名（不包括扩展名）
        String fileNameWithoutExtension = fileName.contains(".") ? fileName.substring(0, fileName.lastIndexOf(".")) : fileName;
        // 拼接时间戳和文件扩展名
        String newFileName = fileNameWithoutExtension + "_" + System.currentTimeMillis() + "." + (fileName.contains(".") ? fileName.substring(fileName.lastIndexOf(".") + 1) : "");
        // 构建新的完整路径
        String objectKey = path.substring(0, path.lastIndexOf(StrUtil.SLASH) + 1) + newFileName;
        PutObjectResult putObjectResult;
        try {
            putObjectResult = OBSClientUtils.uploadMultipartFile(projectFileUploadDTO.getFile(), objectKey, projectFileUploadDTO.getIsPublic());
        } catch (Exception e) {
            log.error("上传文件异常.e.message:{}, e=", e.getMessage(), e);
            throw new CustomException("上传文件失败");
        }
        //保存文件记录
        ProjProjectPlanFile projProjectPlanFile = new ProjProjectPlanFile();
        projProjectPlanFile.setProjectPlanFileId(SnowFlakeUtil.getId());
        projProjectPlanFile.setProjectInfoId(projectFileUploadDTO.getProjectInfoId());
        projProjectPlanFile.setPlanStageCode(projectFileUploadDTO.getPlanStageCode());
        projProjectPlanFile.setPlanItemCode(projectFileUploadDTO.getPlanItemCode());
        projProjectPlanFile.setDictPlanItemFileId(projectFileUploadDTO.getDictPlanItemFileId());
        projProjectPlanFile.setFilePath(objectKey);
        projProjectPlanFile.setFileName(projectFileUploadDTO.getFile().getOriginalFilename());
        projProjectPlanFile.setFileDesc("");
        projProjectPlanFileMapper.insert(projProjectPlanFile);
        //封装出参
        ProjPlanUploadFileVO projPlanUploadFileVO = new ProjPlanUploadFileVO();
        BeanUtil.copyProperties(projProjectPlanFile, projPlanUploadFileVO);
        if (projectFileUploadDTO.getIsPublic()) {
            projPlanUploadFileVO.setFileUrl(URLDecoder.decode(putObjectResult.getObjectUrl()));
        } else {
            projPlanUploadFileVO.setFileUrl(OBSClientUtils.getTemporaryUrl(objectKey, 3600));
        }
        return Result.success(projPlanUploadFileVO);
    }

    /**
     * 删除附件
     *
     * @param dto
     * @return
     */
    @Override
    public Result deleteProjectPlanFile(ProjPlanFileDelDTO dto) {
        projProjectPlanFileMapper.deleteById(dto.getProjectPlanFileId());
        return Result.success();
    }

    /**
     * 校验附件完整性
     *
     * @param projectPlanFileDTO
     * @return
     */
    @Override
    public Result<Boolean> checkProjectFileFull(ProjectPlanFileDTO projectPlanFileDTO) {
        SysConfig sysConfig = sysConfigMapper.selectConfigByName("project_file_upload");
        if (ObjectUtil.isEmpty(sysConfig) || "0".equals(sysConfig.getConfigValue())) {
            return Result.success(true);
        }
        ProjProjectInfo projectInfo = projectInfoMapper.selectById(projectPlanFileDTO.getProjectInfoId());
        //是否是二次验收项目
        ProjProjectAcceptance projProjectAcceptance = projProjectAcceptanceMapper.selectByProjectInfoIdOne(projectInfo.getProjectInfoId());
        boolean isSecondAcceptance = false;
        if (ObjectUtil.isNotEmpty(projProjectAcceptance)) {
            isSecondAcceptance = projProjectAcceptance.getRequiredAcceptanceTimes() == 2;
        }
        //开启了配置或者部分项目开启 并且当前项目包含在配置中 并且是首期项目 并且不是二次验收项目
        if (("1".equals(sysConfig.getConfigValue()) || sysConfig.getConfigValue().contains(String.valueOf(projectPlanFileDTO.getProjectInfoId()))) && projectInfo.getHisFlag() == 1 && !isSecondAcceptance) {
            ProjCustomInfo customInfo = customInfoMapper.selectById(projectInfo.getCustomInfoId());
            //查询项目计划需上传资料字典信息
            List<DictPlanItemFile> dictPlanItemFileList = dictPlanItemFileMapper.selectList(new QueryWrapper<DictPlanItemFile>().eq("plan_item_code", projectPlanFileDTO.getPlanItemCode()).eq(customInfo.getTelesalesFlag() == 1, "telesales_flag", 1).eq(projectInfo.getProjectType() == 1, "monomer_flag", 1).eq(projectInfo.getProjectType() == 2, "region_flag", 1).eq(projectInfo.getUpgradationType() == 1, "old_for_new_flag", 1).eq(projectInfo.getUpgradationType() == 2, "new_customer_flag", 1).eq(projectInfo.getHisFlag() == 1, "initial_flag", 1).eq(projectInfo.getHisFlag() == 0, "uninitial_flag", 1).eq(projectInfo.getSupervisorFlag() == 1, "supervisor_flag", 1).eq("need_flag", 1));
            if (CollectionUtil.isNotEmpty(dictPlanItemFileList)) {
                //查询已上传的资料
                List<ProjProjectPlanFile> fileList = projProjectPlanFileMapper.selectList(new QueryWrapper<ProjProjectPlanFile>().eq("project_info_id", projectPlanFileDTO.getProjectInfoId()).eq("plan_stage_code", projectPlanFileDTO.getPlanStageCode()).eq("plan_item_code", projectPlanFileDTO.getPlanItemCode()));
                for (DictPlanItemFile dictPlanItemFile : dictPlanItemFileList) {
                    if (CollectionUtil.isEmpty(fileList.stream().filter(v -> v.getDictPlanItemFileId().equals(dictPlanItemFile.getDictPlanItemFileId())).collect(Collectors.toList()))) {
                        return Result.success(false);
                    }
                }
            }
        } else {
            return Result.success(true);
        }
        return Result.success(true);
    }

    /**
     * 分页查询客户上传资料
     *
     * @param pageDTO
     * @return
     */
    @Override
    public Result<PageInfo<FindCustomFileVO>> findCustomFilesByPage(FindCustomFilePageDTO pageDTO) {
        if (ObjectUtil.isNotEmpty(pageDTO.getStartTime())) {
            pageDTO.setStartTime(pageDTO.getStartTime() + " 00:00:00");
        }
        if (ObjectUtil.isNotEmpty(pageDTO.getEndTime())) {
            pageDTO.setEndTime(pageDTO.getEndTime() + " 23:59:59");
        }
        List<FindCustomFileVO> customFiles = PageHelperUtil.queryPage(pageDTO.getPageNum(), pageDTO.getPageSize(), page -> dictPlanItemFileMapper.findCustomFiles(pageDTO));
        if (CollectionUtil.isNotEmpty(customFiles)) {
            customFiles.forEach(v -> {
                v.setProjectDeliverStatusName(ProjectDeliverStatusEnums.getEnum(v.getProjectDeliverStatus()).getName());
            });
        }
        return Result.success(new PageInfo<>(customFiles));
    }

    /**
     * 查询项目计划阶段上传资料
     *
     * @param dto
     * @return
     */
    @Override
    public Result<List<FindProjectFileVO>> findProjectFiles(FindProjectFileDTO dto) {
        List<FindProjectFileVO> list = dictPlanItemFileMapper.findProjectFiles(dto);
        if (CollectionUtil.isNotEmpty(list)) {
            for (FindProjectFileVO findProjectFileVO : list) {
                //查询已上传的资料
                List<ProjProjectPlanFile> fileList = projProjectPlanFileMapper.selectList(new QueryWrapper<ProjProjectPlanFile>().eq("project_info_id", dto.getProjectInfoId()).eq("dict_plan_item_file_id", findProjectFileVO.getDictPlanItemFileId()));
                if (CollectionUtil.isNotEmpty(fileList)) {
                    fileList.forEach(file -> {
                        file.setFilePath(OBSClientUtils.getTemporaryUrl(file.getFilePath(), 3600));
                    });
                }
                findProjectFileVO.setFileList(fileList);
            }
        }
        return Result.success(list);
    }
}
