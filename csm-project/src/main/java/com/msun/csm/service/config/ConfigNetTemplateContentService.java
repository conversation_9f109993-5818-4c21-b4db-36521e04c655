package com.msun.csm.service.config;

import java.util.List;

import com.msun.csm.dao.entity.config.ConfigNetTemplateContent;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/21
 */

public interface ConfigNetTemplateContentService {

    int deleteByPrimaryKey(Long netTemplateContentId);

    int insert(ConfigNetTemplateContent record);

    int insertOrUpdate(ConfigNetTemplateContent record);

    int insertOrUpdateSelective(ConfigNetTemplateContent record);

    int insertSelective(ConfigNetTemplateContent record);

    ConfigNetTemplateContent selectByPrimaryKey(Long netTemplateContentId);

    int updateByPrimaryKeySelective(ConfigNetTemplateContent record);

    int updateByPrimaryKey(ConfigNetTemplateContent record);

    int updateBatch(List<ConfigNetTemplateContent> list);

    int updateBatchSelective(List<ConfigNetTemplateContent> list);

    int batchInsert(List<ConfigNetTemplateContent> list);

}
