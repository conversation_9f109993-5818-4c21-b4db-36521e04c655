package com.msun.csm.service.dict;

import java.util.List;

import javax.annotation.Resource;

import com.msun.csm.util.PageHelperUtil;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.mapper.dict.DictProductLogMapper;
import com.msun.csm.model.dto.DictProductLogPageDTO;
import com.msun.csm.model.vo.dict.DictProductLogVO;

@Service
public class DictProductLogServiceImpl implements DictProductLogService {

    @Resource
    private DictProductLogMapper dictProductLogMapper;

    /**
     * 分页查询产品字典日志
     *
     * @param pageDTO
     * @return
     */
    @Override
    public Result<PageInfo<DictProductLogVO>> selectDictProductLogByPage(DictProductLogPageDTO pageDTO) {
        return PageHelperUtil.queryPage(pageDTO.getPageNum(), pageDTO.getPageSize(), page -> {
            List<DictProductLogVO> logList = dictProductLogMapper.selectDictProductLogByPage(pageDTO);
            PageInfo<DictProductLogVO> pageInfo = new PageInfo<>(logList);
            return Result.success(pageInfo);
        });

    }
}
