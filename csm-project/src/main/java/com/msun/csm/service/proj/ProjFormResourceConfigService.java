package com.msun.csm.service.proj;

import java.util.List;

import javax.validation.constraints.NotNull;

import org.springframework.validation.annotation.Validated;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.ProjFormResourceConfigDTO;
import com.msun.csm.model.dto.ProjFormResourceConfigListDTO;
import com.msun.csm.model.dto.ProjFormResourceConfigPageDTO;
import com.msun.csm.model.vo.ProjFormResourceConfigVO;

/**
 * 项目表单资源配置表(ProjFormResourceConfig)表服务接口
 *
 * <AUTHOR>
 * @since 2024-05-20 13:44:37
 */
@Validated
public interface ProjFormResourceConfigService {
    /**
     * 说明: 分页查询
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result<com.github.pagehelper.PageInfo<com.msun.csm.model.vo.ProjFormResourceConfigPageVO>>
     * @author: Yhongmin
     * @createAt: 2024/5/20 15:48
     * @remark: Copyright
     */
    Result<PageInfo<ProjFormResourceConfigVO>> findByPage(ProjFormResourceConfigPageDTO dto);

    /**
     * 说明:
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result<java.util.List<com.msun.csm.model.vo.ProjFormResourceConfigVO>>
     * @author: Yhongmin
     * @createAt: 2024/5/20 18:34
     * @remark: Copyright
     */
    Result<List<ProjFormResourceConfigVO>> findAll(ProjFormResourceConfigPageDTO dto);

    /**
     * 说明: 初始化接口
     *
     * @param projectInfoId
     * @return:void
     * @author: Yhongmin
     * @createAt: 2024/5/21 12:39
     * @remark: Copyright
     */
    void initialization(Long projectInfoId);

    /**
     * 通过ID查询单条数据
     *
     * @param projFormId 主键
     * @return 实例对象
     */
    ProjFormResourceConfigVO getProjFormResourceConfigById(@NotNull Long projFormId);

    /**
     * 新增数据
     *
     * @param entity 实例对象
     * @return 成功为1，失败为0
     */
    Result<String> save(@NotNull ProjFormResourceConfigDTO entity);

    /**
     * 修改数据
     *
     * @param entity 实例对象
     * @return 成功为1，失败为0
     */
    Result<String> update(@NotNull ProjFormResourceConfigDTO entity);

    /**
     * 说明: 批量修改报表状态
     *
     * @param configList
     * @return:com.msun.csm.common.model.Result<java.lang.String>
     * @author: Yhongmin
     * @createAt: 2024/5/20 19:31
     * @remark: Copyright
     */
    Result<String> batchUpdateById(@NotNull ProjFormResourceConfigListDTO configList);

    /**
     * 作废
     *
     * @param id 主键
     * @return 是否成功
     */
    Result<String> updateMcInvalid(@NotNull Long id);
}
