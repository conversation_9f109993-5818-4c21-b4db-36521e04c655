package com.msun.csm.service.config;

import java.util.List;

import com.msun.csm.common.model.Result;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

/**
 * <AUTHOR> @since 2025-07-07 02:48:56
 */
@Slf4j
@Service
public class ConfigAgentScenarioDictImpl implements ConfigAgentScenarioDict {

    @Resource
    private ConfigAgentScenarioDictMapper configAgentScenarioDictMapper;

    @Override
    public Result<List<ConfigAgentScenarioDict>> getAgentScenarioConfig() {
        return Result.success(configAgentScenarioDictMapper.getAgentScenarioConfig());
    }

}
