package com.msun.csm.service.proj;

import java.util.List;

import com.msun.csm.dao.entity.proj.ProjNetSurveyResult;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/21
 */

public interface ProjNetSurveyResultService {

    int deleteByPrimaryKey(Long netSurveyResultId);

    int insert(ProjNetSurveyResult record);

    int insertOrUpdate(ProjNetSurveyResult record);

    int insertOrUpdateSelective(ProjNetSurveyResult record);

    int insertSelective(ProjNetSurveyResult record);

    ProjNetSurveyResult selectByPrimaryKey(Long netSurveyResultId);

    int updateByPrimaryKeySelective(ProjNetSurveyResult record);

    int updateByPrimaryKey(ProjNetSurveyResult record);

    int updateBatch(List<ProjNetSurveyResult> list);

    int updateBatchSelective(List<ProjNetSurveyResult> list);

    int batchInsert(List<ProjNetSurveyResult> list);

}
