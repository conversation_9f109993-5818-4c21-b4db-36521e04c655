package com.msun.csm.service.proj;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import static java.util.stream.Collectors.groupingBy;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Name;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.jetbrains.annotations.NotNull;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTJc;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTcPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTVerticalJc;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STJc;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STVerticalJc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.imsp.SystemSettingWorkflowApi;
import com.msun.core.component.implementation.api.imsp.dto.WorkflowInstanceCommonDataSaveParam;
import com.msun.core.component.implementation.api.imsp.dto.WorkflowInstanceProcessParam;
import com.msun.core.component.implementation.api.imsp.dto.WorkflowInstanceSaveParam;
import com.msun.core.component.implementation.api.imsp.vo.ThridWorkflowResourceQueryVO;
import com.msun.core.component.implementation.api.imsp.vo.WorkflowInstanceVO;
import com.msun.core.component.implementation.filter.ImplHospitalDomainHolder;
import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.enums.ProjectPlanStatusEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.ProjectInfoId;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysDept;
import com.msun.csm.dao.entity.SysFile;
import com.msun.csm.dao.entity.SysRole;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.dict.DictInterface;
import com.msun.csm.dao.entity.dict.DictInterfaceFirm;
import com.msun.csm.dao.entity.dict.DictProduct;
import com.msun.csm.dao.entity.dict.DictProjectRole;
import com.msun.csm.dao.entity.oldimsp.Customer;
import com.msun.csm.dao.entity.proj.HospitalOnlineInfo;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjFeedbackInterface;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfoRelative;
import com.msun.csm.dao.entity.proj.ProjHospitalVsProjectType;
import com.msun.csm.dao.entity.proj.ProjInterfaceGroupApplyDetail;
import com.msun.csm.dao.entity.proj.ProjInterfaceVsAuthor;
import com.msun.csm.dao.entity.proj.ProjInterfaceVsDeviceInfo;
import com.msun.csm.dao.entity.proj.ProjInterfaceVsWorkflow;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectMember;
import com.msun.csm.dao.entity.proj.ProjTaskProgress;
import com.msun.csm.dao.entity.proj.ProjThirdInterface;
import com.msun.csm.dao.entity.proj.ThirdInterfaceMemberDept;
import com.msun.csm.dao.entity.proj.projreport.ConfigCustomBackendDetailLimit;
import com.msun.csm.dao.mapper.dict.DictInterfaceFirmMapper;
import com.msun.csm.dao.mapper.dict.DictInterfaceMapper;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.dao.mapper.dict.DictProjectRoleMapper;
import com.msun.csm.dao.mapper.oldimsp.ImspProjectMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjFeedbackInterfaceMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalVsProjectTypeMapper;
import com.msun.csm.dao.mapper.proj.ProjInterfaceGroupApplyDetailMapper;
import com.msun.csm.dao.mapper.proj.ProjInterfaceVsAuthorMapper;
import com.msun.csm.dao.mapper.proj.ProjInterfaceVsDeviceInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjInterfaceVsWorkflowMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectFileMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectMemberMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectPlanMapper;
import com.msun.csm.dao.mapper.proj.ProjTaskProgressMapper;
import com.msun.csm.dao.mapper.proj.ProjThirdInterfaceMapper;
import com.msun.csm.dao.mapper.report.ConfigCustomBackendDetailLimitMapper;
import com.msun.csm.dao.mapper.sysdept.SysDeptMapper;
import com.msun.csm.dao.mapper.sysfile.SysFileMapper;
import com.msun.csm.dao.mapper.sysrole.SysRoleMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.feign.client.knowledge.KnowledgeFeignClient;
import com.msun.csm.feign.client.openapi.OpenApiPlatformFeignClient;
import com.msun.csm.feign.client.thirdplatform.AuthorizationManagementFeignClient;
import com.msun.csm.feign.client.thirdplatform.ThirdPlatformFeignClient;
import com.msun.csm.feign.client.yunying.YunyingFeignClient;
import com.msun.csm.feign.entity.dataapplication.ResDataResp;
import com.msun.csm.feign.entity.openapi.req.ApplyAuthByGroupsReq;
import com.msun.csm.feign.entity.openapi.req.CheckAppReq;
import com.msun.csm.feign.entity.openapi.resp.ApiGroupResp;
import com.msun.csm.feign.entity.openapi.resp.ApiInterfaceResp;
import com.msun.csm.feign.entity.openapi.resp.AppInfoResp;
import com.msun.csm.feign.entity.thirdplatform.req.HospitalReq;
import com.msun.csm.feign.entity.thirdplatform.req.InsertDemandReq;
import com.msun.csm.model.convert.ProjThirdInterfaceConvert;
import com.msun.csm.model.dto.AppGroupApplyDTO;
import com.msun.csm.model.dto.CreateAppAuthorizationDTO;
import com.msun.csm.model.dto.CreateAppDTO;
import com.msun.csm.model.dto.DemandDto;
import com.msun.csm.model.dto.InterfaceExportExcelDTO;
import com.msun.csm.model.dto.InterfaceMainDto;
import com.msun.csm.model.dto.InterfaceMemberParamDTO;
import com.msun.csm.model.dto.InterfaceReviewPageDTO;
import com.msun.csm.model.dto.ProjInterfaceGroupApplyDetailDTO;
import com.msun.csm.model.dto.ProjThirdInterfaceDTO;
import com.msun.csm.model.dto.ProjThirdInterfacePageDTO;
import com.msun.csm.model.dto.SaveDeployDeviceInfoDTO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.dto.ThirdInterfaceImportDTO;
import com.msun.csm.model.dto.UpdateMilestoneDTO;
import com.msun.csm.model.dto.UpdateYunyingDto;
import com.msun.csm.model.dto.user.SysUserDTO;
import com.msun.csm.model.imsp.SysLoginUser;
import com.msun.csm.model.imsp.ThirdInterfaceDTO;
import com.msun.csm.model.imsp.ThirdInterfaceVO;
import com.msun.csm.model.param.MessageParam;
import com.msun.csm.model.req.thirdinterface.CheckInterfaceReq;
import com.msun.csm.model.req.thirdinterface.GetInterfaceApplyDetailReq;
import com.msun.csm.model.req.thirdinterface.InterfaceBaseReq;
import com.msun.csm.model.req.thirdinterface.UpdateAuthFileReq;
import com.msun.csm.model.req.todotask.SaveOrUpdateTodoTaskParam;
import com.msun.csm.model.resp.thirdinterface.ThridWorkflowResourceQueryResp;
import com.msun.csm.model.vo.CheckAppDetailVO;
import com.msun.csm.model.vo.CheckAppVO;
import com.msun.csm.model.vo.CreateAppAuthorizationReesult;
import com.msun.csm.model.vo.DeployDeviceInfoVO;
import com.msun.csm.model.vo.GroupApplyVO;
import com.msun.csm.model.vo.ImspInterfaceDataVO;
import com.msun.csm.model.vo.InterfaceReviewVO;
import com.msun.csm.model.vo.ProjInterfaceRecordLogVo;
import com.msun.csm.model.vo.ProjThirdInterfaceVO;
import com.msun.csm.model.vo.ThirdInterfaceEntryCheckVO;
import com.msun.csm.model.vo.ThirdInterfaceFileVO;
import com.msun.csm.model.vo.ThirdInterfaceGroupVO;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.message.SendMessageService;
import com.msun.csm.service.oldimsp.OldImspInterfaceService;
import com.msun.csm.util.DomainMapUtil;
import com.msun.csm.util.EasyExcelData;
import com.msun.csm.util.EasyExcelUtil;
import com.msun.csm.util.PageHelperUtil;
import com.msun.csm.util.PinyinUtils;
import com.msun.csm.util.SM2Util;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.StringUtils;
import com.msun.csm.util.obs.OBSClientUtils;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/9/12
 */
@Slf4j
@Service
public class ProjThirdInterfaceServiceImpl implements ProjThirdInterfaceService {

    @Resource
    private ProjInterfaceVsDeviceInfoService projInterfaceVsDeviceInfoService;

    @Value("${project.feign.openApi.header-user_value}")
    private String platformUser;

    @Value("${project.feign.authorizationManagement.sm2}")
    private String sm2;

    @Value("${spring.profiles.active}")
    private String activeProfiles;

    @Value("#{'${project.feign.openApi.hospitalIds}'.split(',')}")
    private List<Long> authTestHospitalIds;

    @Value("${project.feign.openApi.env}")
    private String authEnv;

    @Value("${project.feign.openApi.orgId}")
    private Long authTestOrgId;

    @Value("${project.feign.interface-web-demand.url}")
    private String interfaceWebDemand;

    @Value("${project.feign.interface-web-demand.selectInterface-method}")
    private String selectInterfaceMethod;

    @Value("${project.feign.oa.interface-verify}")
    private String interfaceVerify;

    @Resource
    private OpenApiPlatformFeignClient openApiPlatformFeignClient;

    @Resource
    private ThirdPlatformFeignClient thirdPlatformFeignClient;

    @Resource
    private ProjThirdInterfaceMapper projThirdInterfaceMapper;

    @Resource
    private ProjProjectFileMapper projectFileMapper;

    @Autowired
    private ProjProjectPlanMapper projectPlanMapper;

    @Resource
    private ProjThirdInterfaceConvert interfaceConvert;

    @Resource
    private ProjInterfaceVsAuthorMapper interfaceVsAuthorMapper;

    @Resource
    private ProjInterfaceVsAuthorService interfaceVsAuthorService;

    @Resource
    private ProjInterfaceGroupApplyDetailMapper interfaceGroupApplyDetailMapper;

    @Resource
    private ProjHospitalVsProjectTypeMapper hospitalVsProjectTypeMapper;

    @Resource
    private ProjHospitalInfoMapper hospitalInfoMapper;

    @Resource
    private DictInterfaceMapper dictInterfaceMapper;

    @Resource
    private DictInterfaceFirmMapper dictInterfaceFirmMapper;

    @Resource
    private ProjInterfaceRecordLogService interfaceRecordLogService;

    @Resource
    private SysFileMapper sysFileMapper;

    @Resource
    private UserHelper userHelper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private SysDeptMapper sysDeptMapper;

    @Resource
    private ProjCustomInfoMapper customInfoMapper;

    @Resource
    private ProjProjectInfoMapper projectInfoMapper;

    @Resource
    private YunyingFeignClient yunyingFeignClient;

    @Resource
    private DictProductMapper productMapper;

    @Resource
    private SendMessageService sendMessageService;

    @Resource
    private ProjMilestoneInfoService milestoneInfoService;

    @Resource
    private ProjTaskProgressMapper taskProgressMapper;

    @Resource
    private OldImspInterfaceService oldImspInterfaceService;

    @Resource
    private AuthorizationManagementFeignClient authorizationManagementFeignClient;

    @Resource
    private SysRoleMapper sysRoleMapper;

    @Resource
    private ProjFeedbackInterfaceMapper projFeedbackInterfaceMapper;

    @Resource
    private KnowledgeFeignClient knowledgeFeignClient;

    @Resource
    private ProjInterfaceVsDeviceInfoMapper projInterfaceVsDeviceInfoMapper;

    @Value("${project.feign.knowledge.appId}")
    private String knowledgeAppId;

    @Value("${project.feign.knowledge.publicKey}")
    private String knowledgePublicKey;

    @Resource
    private ProjProjectPlanService projProjectPlanService;

    @Resource
    private ProjTodoTaskService todoTaskService;

    @Resource
    private ProjInterfaceVsWorkflowMapper projInterfaceVsWorkflowMapper;
    @Lazy
    @Resource
    private ImplHospitalDomainHolder domainHolder;
    @Lazy
    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;

    @Resource
    private ConfigCustomBackendDetailLimitMapper configCustomBackendDetailLimitMapper;

    @Resource
    private DictProjectRoleMapper dictProjectRoleMapper;

    @Resource
    private ProjProjectMemberMapper projMemberMapper;

    @Lazy
    @Resource
    private SystemSettingWorkflowApi systemSettingWorkflowApi;

    @Lazy
    @Resource
    private ImspProjectMapper imspProjectMapper;

    @Override
    public int deleteByPrimaryKey(Long thirdInterfaceId) {
        return projThirdInterfaceMapper.deleteByPrimaryKey(thirdInterfaceId);
    }

    @Override
    public int insertOrUpdate(ProjThirdInterface record) {
        return projThirdInterfaceMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(ProjThirdInterface record) {
        return projThirdInterfaceMapper.insertOrUpdateSelective(record);
    }

    @Override
    public int insertSelective(ProjThirdInterface record) {
        return projThirdInterfaceMapper.insertSelective(record);
    }

    @Override
    public ProjThirdInterface selectByPrimaryKey(Long thirdInterfaceId) {
        return projThirdInterfaceMapper.selectByPrimaryKey(thirdInterfaceId);
    }

    @Override
    public int updateByPrimaryKeySelective(ProjThirdInterface record) {
        return projThirdInterfaceMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ProjThirdInterface record) {
        return projThirdInterfaceMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<ProjThirdInterface> list) {
        return projThirdInterfaceMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<ProjThirdInterface> list) {
        return projThirdInterfaceMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<ProjThirdInterface> list) {
        return projThirdInterfaceMapper.batchInsert(list);
    }

    /**
     * 查询接口名称字典数据
     *
     * @return
     */
    @Override
    public Result<List<DictInterface>> selectDictInterface() {
        return Result.success(dictInterfaceMapper.selectList(null));
    }

    /**
     * 查询接口厂商字典数据
     *
     * @return
     */
    @Override
    public Result<List<DictInterfaceFirm>> selectDictInterfaceFirm() {
        return Result.success(dictInterfaceFirmMapper.selectList(null));
    }

    /**
     * 查询接口产品字典数据
     *
     * @return
     */
    @Override
    public Result<List<DictProduct>> selectDictProductForInterface() {
        List<DictProduct> dictProducts = productMapper.selectList(new QueryWrapper<DictProduct>()
                .eq("product_type", "4")
        );
        return Result.success(dictProducts);
    }

    /**
     * 分页查询
     *
     * @param dto
     * @return
     */
    @Override
    public Result<PageInfo<ProjThirdInterfaceVO>> selectByPage(ProjThirdInterfacePageDTO dto) {
        return PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> {
            //从运维平台进入列表页处理
            if (ObjectUtil.isNotEmpty(dto.getFeedbackId())) {
                //如果有hospitalId，则表示查询的是修改接口的接口列表，返回该医院下的所有的所有接口
                if (ObjectUtil.isNotEmpty(dto.getHospitalInfoId())) {
                    List<ProjHospitalInfoRelative> hosList = hospitalInfoMapper.findHospitalByCloudHospitalId(dto.getHospitalInfoId());
                    if (CollectionUtil.isNotEmpty(hosList)) {
                        dto.setHospitalInfoId(hosList.get(0).getHospitalInfoId());
                    }
                }
                //如果反馈单已绑定接口，则只返回绑定的一条接口列表
                ProjFeedbackInterface projFeedbackInterface = projFeedbackInterfaceMapper.selectOne(new QueryWrapper<ProjFeedbackInterface>()
                        .eq("feedback_id", dto.getFeedbackId()));
                if (ObjectUtil.isNotEmpty(projFeedbackInterface)) {
                    dto.setThirdInterfaceId(projFeedbackInterface.getInterfaceId());
                }
            }
            //项目工具入口进入时，查询该客户下的所有三方接口
            if (ObjectUtil.isNotEmpty(dto.getSource()) && dto.getSource() == 0) {
                dto.setProjectType(null);
                dto.setProjectInfoId(null);
                dto.setHospitalInfoId(null);
            }
            List<ProjThirdInterfaceVO> projThirdInterfaceVOS = projThirdInterfaceMapper.selectThirdInterface(dto);
            //查询文件信息，封装到VO中
            for (ProjThirdInterfaceVO vo : projThirdInterfaceVOS) {
                // 接口委托书文件
                if (ObjectUtil.isNotEmpty(vo.getAuthLetterFiles())) {
                    vo.setAuthLetterFileLinkList(getUrlList(vo.getAuthLetterFiles()));
                }
                // 三方接口合同文件
                if (ObjectUtil.isNotEmpty(vo.getThirdContractFiles())) {
                    vo.setThirdContractFileLinkList(getUrlList(vo.getThirdContractFiles()));
                }
                if (ObjectUtil.isNotEmpty(vo.getThirdInterfaceFiles())) {
                    vo.setThirdInterfaceFileLinkList(getUrlList(vo.getThirdInterfaceFiles()));
                }
                // 进度详情获取最新一条数据
                List<ProjTaskProgress> projTaskProgresses = taskProgressMapper.selectList(
                        new QueryWrapper<ProjTaskProgress>()
                                .eq("source_type", 3)
                                .eq("source_type_id", vo.getThirdInterfaceId())
                                .orderByDesc("create_time")
                                .last("limit 1")
                );
                vo.setProgressDetail(CollectionUtil.isNotEmpty(projTaskProgresses) && projTaskProgresses.size() > 0
                        ? projTaskProgresses.get(0).getProgressDesc() : "");
                // 对接方式为项目组对接时 拼装 接口开发链接
                if (ObjectUtil.isNotEmpty(vo.getImplementsType()) && vo.getImplementsType() == 2) {
                    // 查询运营平台客户id
                    ProjCustomInfo customInfo = customInfoMapper.selectByPrimaryKey(vo.getCustomInfoId());
                    // 查询医院信息
                    ProjHospitalInfo hospitalInfo = hospitalInfoMapper.selectById(vo.getHospitalInfoId());
                    String url = interfaceWebDemand + selectInterfaceMethod + "?customerId=" + customInfo.getYyCustomerId()
                            + "&orgId=" + hospitalInfo.getOrgId() + "&loginName=" + userHelper.getCurrentUser().getAccount()
                            + "&customerName=" + customInfo.getCustomName();
                    vo.setInterfaceMadeUrl(url);
                }

            }
            return Result.success(new PageInfo<>(projThirdInterfaceVOS));
        });
    }

    /**
     * 保存接口的进度详情
     *
     * @param taskProgress
     * @return
     */
    @Override
    public Result saveInterfaceTaskProgress(ProjTaskProgress taskProgress) {
        taskProgress.setProjTaskProgressId(SnowFlakeUtil.getId());
        taskProgress.setSourceType(3);
        taskProgressMapper.insert(taskProgress);
        return Result.success();
    }

    /**
     * 保存接口信息
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result saveInterface(ProjThirdInterfaceDTO dto) {
        // 检测 接口名称、厂商名称是否来自于字典数据（是否存在id）。当不是选择的数据时  ， 需要保存到字典数据中
        ifHasInterfaceAndFirm(dto);
        ProjThirdInterface ti = interfaceConvert.dto2Po(dto);
        //处理定价分类
        if (ObjectUtil.isEmpty(dto.getYyProductId())) {
            ti.setYyProductId(-1L);
        }
        // 三方接口主键
        Long thirdInterfaceId = SnowFlakeUtil.getId();
        // 保存接口基本信息
        ti.setThirdInterfaceId(thirdInterfaceId);
        //如果feedbackId不为空，则标识接口来源于运维平台反馈单
        String conmments = null;
        if (ObjectUtil.isNotEmpty(dto.getFeedbackId())) {
            //接口来源为：反馈单(2)
            ti.setInterfaceSource(2);
            //处理customInfoId、projectInfoId(无法获取，不存储)、hospitalInfoId
            List<ProjHospitalInfoRelative> hosList = hospitalInfoMapper.findHospitalByCloudHospitalId(dto.getHospitalInfoId());
            if (CollectionUtil.isNotEmpty(hosList)) {
                ti.setHospitalInfoId(hosList.get(0).getHospitalInfoId());
                ti.setCustomInfoId(hosList.get(0).getCustomInfoId());
                //项目类型
                Integer projectType = hospitalVsProjectTypeMapper.getHospitalType(hosList.get(0).getHospitalInfoId());
                if (ObjectUtil.isNotEmpty(projectType)) {
                    ti.setProjectType(projectType);
                } else {
                    ti.setProjectType(-1);
                }
                ti.setProjectInfoId(-1L);

            }
            conmments = "运维平台反馈单添加接口";
        }
        if (ti.getProjectInfoId() == null || ti.getProjectInfoId() <= 0) {
            //如果当前三方接口没有跟项目绑定，找到该客户下的首期项目，将项目跟接口绑定
            ProjHospitalInfo hospitalInfo = new LambdaQueryChainWrapper<>(hospitalInfoMapper)
                    .eq(ProjHospitalInfo::getIsDeleted, 0)
                    .and(i -> i.eq(ProjHospitalInfo::getCloudHospitalId, ti.getHospitalInfoId()).or().eq(ProjHospitalInfo::getHospitalInfoId, ti.getHospitalInfoId()))
                    .last("limit 1")
                    .one();
            if (hospitalInfo != null) {
                Long customInfoId = hospitalInfo.getCustomInfoId();
                ProjProjectInfo proj = new LambdaQueryChainWrapper<>(projectInfoMapper)
                        .select(ProjProjectInfo::getProjectInfoId)
                        .eq(ProjProjectInfo::getIsDeleted, 0)
                        .eq(ProjProjectInfo::getCustomInfoId, customInfoId)
                        .eq(ProjProjectInfo::getHisFlag, 1)
                        .last("limit 1")
                        .one();
                if (proj != null) {
                    ti.setProjectInfoId(proj.getProjectInfoId());
                }
            }
        }
        if (ti.getDictInterfaceId() == null) {
            ti.setDictInterfaceId(-1L);
        }
        if (dto.getInterfaceType() == 2 && ObjectUtil.isNotEmpty(dto.getGroupList())) {
            ti.setInterfaceCategory("埋点类");
            ti.setInterfaceCategoryCode(PinyinUtils.getFirstSpell("埋点类接口新增"));

            if (ti.getDictInterfaceName() == null || "".equals(ti.getDictInterfaceName())) {
                StringBuilder str = new StringBuilder();
                for (ThridWorkflowResourceQueryVO vo : dto.getGroupList()) {
                    str.append(vo.getResourceDes()).append(",");
                }
                ti.setDictInterfaceName(str.substring(0, str.length() - 1));
                ti.setDictInterfaceCode(PinyinUtils.getFirstSpell(ti.getDictInterfaceName()));
            }
        }
        // 交互类的code
        if (dto.getInterfaceType() == 1 && ObjectUtil.isNotEmpty(dto.getInterfaceCategory())) {
            ti.setInterfaceCategoryCode("jhl" + PinyinUtils.getFirstSpell(dto.getInterfaceCategory()));
            ti.setDictInterfaceCode(PinyinUtils.getFirstSpell(dto.getDictInterfaceName()));
        }
        ti.setHistoryFlag(0);
        projThirdInterfaceMapper.insert(ti);
        // 如果是埋点接口，则保存选择的埋点数据集合
        if (dto.getInterfaceType() == 2 && ObjectUtil.isNotEmpty(dto.getGroupList())) {
            List<ThridWorkflowResourceQueryVO> workflowList = dto.getGroupList();
            for (ThridWorkflowResourceQueryVO workflow : workflowList) {
                ProjInterfaceVsWorkflow projInterfaceVsWorkflow = new ProjInterfaceVsWorkflow();
                BeanUtil.copyProperties(workflow, projInterfaceVsWorkflow);
                projInterfaceVsWorkflow.setThirdInterfaceId(thirdInterfaceId);
                projInterfaceVsWorkflow.setInterfaceVsWorkflowId(SnowFlakeUtil.getId());
                projInterfaceVsWorkflowMapper.insert(projInterfaceVsWorkflow);
            }
        }
        // 保存选择的 接口授权分组数据
        if (ObjectUtil.isNotEmpty(dto.getInterfaceGroupApplyDetailDTOList())) {
            // 先保存分组授权信息表
            ProjInterfaceVsAuthor projInterfaceVsAuthor = new ProjInterfaceVsAuthor();
            projInterfaceVsAuthor.setInterfaceVsAuthorId(SnowFlakeUtil.getId());
            projInterfaceVsAuthor.setThirdInterfaceId(ti.getThirdInterfaceId());
            projInterfaceVsAuthor.setFirmName(ti.getDictInterfaceFirmName());
            projInterfaceVsAuthor.setEnvironment(0);
            log.info("三方接口授权信息保存 , {}", JSONUtil.toJsonStr(projInterfaceVsAuthor));
            interfaceVsAuthorMapper.insert(projInterfaceVsAuthor);
            List<ProjInterfaceGroupApplyDetail> detailList = new ArrayList<>();
            // 保存分组授权明细表（前端传入的 只有分组信息。需要根据分组信息查询具体的接口明细数据）
            for (ProjInterfaceGroupApplyDetailDTO detailDTO : dto.getInterfaceGroupApplyDetailDTOList()) {
                List<ApiInterfaceResp> apiList = interfaceVsAuthorService.getApiList(
                        detailDTO.getPfInterfaceGroupId());
                if (CollectionUtil.isEmpty(apiList)) {
                    throw new CustomException("当前接口分类没有接口列表信息，请重新选择接口分类！");
                }
                for (ApiInterfaceResp resp : apiList) {
                    ProjInterfaceGroupApplyDetail applyDetail = getProjInterfaceGroupApplyDetail(
                            detailDTO.getPfInterfaceGroupId(), detailDTO.getPfInterfaceGroupName(), resp, ti);
                    detailList.add(applyDetail);
                }
            }
            log.info("保存接口分组授权明细信息 , {}", JSONUtil.toJsonStr(detailList));
            interfaceGroupApplyDetailMapper.batchInsert(detailList);
        }
        if (ObjectUtil.isNotEmpty(dto.getIp()) || ObjectUtil.isNotEmpty(dto.getMac())) {
            // 保存部署申请信息
            ProjInterfaceVsDeviceInfo projInterfaceVsDeviceInfo = new ProjInterfaceVsDeviceInfo();
            projInterfaceVsDeviceInfo.setThirdInterfaceId(ti.getThirdInterfaceId());
            projInterfaceVsDeviceInfo.setIp(dto.getIp() == null ? " " : dto.getIp());
            projInterfaceVsDeviceInfo.setMac(dto.getMac() == null ? " " : dto.getMac());
            projInterfaceVsDeviceInfo.setPfAppId(" ");
            projInterfaceVsDeviceInfo.setApplicant(userHelper.getCurrentUser().getUserName());
            projInterfaceVsDeviceInfo.setApplicantPhone(userHelper.getCurrentUser().getPhone());
            projInterfaceVsDeviceInfoService.insert(projInterfaceVsDeviceInfo);
        }
        //interfaceRecordLogService.saveInterfaceRecordLog("添加接口", ti.getThirdInterfaceId(), conmments);
        //如果feedbackId不为空，则标识接口来源于运维平台反馈单
        if (ObjectUtil.isNotEmpty(dto.getFeedbackId())) {
            //查询反馈单是否已经保存过
            ProjFeedbackInterface feedbackInterface = projFeedbackInterfaceMapper.selectOne(new QueryWrapper<ProjFeedbackInterface>()
                    .eq("feedback_id", dto.getFeedbackId()).last(" limit 1"));
            if (ObjectUtil.isNotEmpty(feedbackInterface)) {
                throw new CustomException("当前反馈单已经添加过接口，无法继续添加！");
            }
            //保存运维平台反馈单和三方接口对照关系
            ProjFeedbackInterface projFeedbackInterface = new ProjFeedbackInterface();
            projFeedbackInterface.setFeedbackInterfaceId(SnowFlakeUtil.getId());
            projFeedbackInterface.setInterfaceId(ti.getThirdInterfaceId());
            projFeedbackInterface.setFeedbackId(dto.getFeedbackId());
            projFeedbackInterfaceMapper.insert(projFeedbackInterface);
            //回写运维平台反馈单和接口对照关系
            try {
                ThirdInterfaceDTO feedbackDto = new ThirdInterfaceDTO();
                feedbackDto.setFeedbackId(dto.getFeedbackId());
                feedbackDto.setThirdInterfaceId(ti.getThirdInterfaceId());
                String respStr = knowledgeFeignClient.updateThirdInterfaceId(feedbackDto, getAuthorization());
                log.info("======回写运维平台反馈单和接口对照关系返回值======{}", respStr);
                ResponseResult checkResp = JSON.parseObject(respStr, ResponseResult.class);
                if (!checkResp.isSuccess()) {
                    throw new CustomException("回写运维平台反馈单和接口对照关系异常:" + checkResp.getMessage());
                }
            } catch (Exception e) {
                log.error("回写运维平台反馈单和接口对照关系发生错误：{}", e.getMessage());
            }
        }
        projProjectPlanService.updatePlanAndTodoTaskStatusByProjectAndItemCode(dto.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_THIRD_PART, ProjectPlanStatusEnum.UNDERWAY);
        todoTaskService.todoTaskTotalCountSync(dto.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_THIRD_PART.getPlanItemCode());
        todoTaskService.todoTaskTotalCountSync(dto.getProjectInfoId(), DictProjectPlanItemEnum.SCHEDULE_THIRD_PART.getPlanItemCode());
        return Result.success();
    }

    // 1、 请求头秘钥生成
    private String getAuthorization() {
        // 时间戳
        Long timestamp = System.currentTimeMillis();
        // 原始串 【appId、publicKey 为运维分发】
        String old = "appid=" + knowledgeAppId + "&appsecret=" + knowledgePublicKey + "&timestamp=" + timestamp;
        // rsa工具
        RSA rsa = new RSA(null, knowledgePublicKey);
        // 加密
        String sign = rsa.encryptBase64(old, KeyType.PublicKey);
        // 拼接
        String headers = "appid=" + knowledgeAppId + ";sign=" + sign;
        return headers;
    }


    /**
     * 修改/裁定三方接口信息【根据前端传入的标识区分 modifyFlag】
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Result updateInterface(ProjThirdInterfaceDTO dto) {
        try {
            //比对接口分类和对接方式是否修改
            String updateInfo = "";
            if (dto.getModifyFlag() == 1) {
                updateInfo = compareUpdateInfo(dto);
            }
            List<ProjInterfaceGroupApplyDetail> addApplyDetailList = new ArrayList<>();
            // 修改功能与裁定功能逻辑一致，所以直接调用修改方法 （根据标识来进行处理裁定后续逻辑）
            // 修改接口基本信息
            ProjThirdInterface ti = interfaceConvert.dto2Po(dto);
            // 检测 接口名称、厂商名称是否来自于字典数据（是否存在id）。当不是选择的数据时  ， 需要保存到字典数据中
            ifHasInterfaceAndFirm(dto);
            if (ti.getDictInterfaceId() == null) {
                ti.setDictInterfaceId(-1L);
            }
            if (dto.getInterfaceType() == 2 && ObjectUtil.isNotEmpty(dto.getGroupList())) {
                StringBuilder str = new StringBuilder();
                for (ThridWorkflowResourceQueryVO vo : dto.getGroupList()) {
                    str.append(vo.getResourceDes()).append(",");
                }
                ti.setDictInterfaceName(str.substring(0, str.length() - 1));
            }
            projThirdInterfaceMapper.updateById(ti);
            // 如果是埋点接口，则保存选择的埋点数据集合
            if (dto.getInterfaceType() == 2 && ObjectUtil.isNotEmpty(dto.getGroupList())) {
                Map map = new HashMap();
                map.put("third_interface_id", ti.getThirdInterfaceId());
                projInterfaceVsWorkflowMapper.deleteByMap(map);
                List<ThridWorkflowResourceQueryVO> workflowList = dto.getGroupList();
                for (ThridWorkflowResourceQueryVO workflow : workflowList) {
                    ProjInterfaceVsWorkflow projInterfaceVsWorkflow = new ProjInterfaceVsWorkflow();
                    BeanUtil.copyProperties(workflow, projInterfaceVsWorkflow);
                    projInterfaceVsWorkflow.setThirdInterfaceId(ti.getThirdInterfaceId());
                    projInterfaceVsWorkflow.setInterfaceVsWorkflowId(SnowFlakeUtil.getId());
                    projInterfaceVsWorkflowMapper.insert(projInterfaceVsWorkflow);
                }
            }
            if (CollectionUtil.isNotEmpty(dto.getInterfaceGroupApplyDetailDTOList())) {
                // 修改接口分组授权明细信息，不需要修改授权信息表
                //先作废之前保存的接口明细信息
                interfaceGroupApplyDetailMapper.delete(
                        new QueryWrapper<ProjInterfaceGroupApplyDetail>()
                                .eq("third_interface_id", ti.getThirdInterfaceId())
                                .eq("environment", 0)
                );
                for (ProjInterfaceGroupApplyDetailDTO detailDTO : dto.getInterfaceGroupApplyDetailDTOList()) {
                    // 查询新修改数据的接口明细数据
                    List<ApiInterfaceResp> apiList = interfaceVsAuthorService.getApiList(
                            detailDTO.getPfInterfaceGroupId());
                    for (ApiInterfaceResp resp : apiList) {
                        ProjInterfaceGroupApplyDetail applyDetail = getProjInterfaceGroupApplyDetail(
                                detailDTO.getPfInterfaceGroupId(), detailDTO.getPfInterfaceGroupName(), resp, ti);
                        addApplyDetailList.add(applyDetail);
                    }
                }
                if (CollectionUtil.isNotEmpty(addApplyDetailList)) {
                    interfaceGroupApplyDetailMapper.batchInsert(addApplyDetailList);
                }
            }
            // 根据标识判断日志的操作信息
            if (dto.getModifyFlag() == 1) {
                String comments = null;
                // 修改接口状态为 未提交
                ProjThirdInterface projThirdInterface = new ProjThirdInterface();
                projThirdInterface.setThirdInterfaceId(ti.getThirdInterfaceId());
                //当接口分类和对接方式发生更改时，状态修改为未申请，需要重新走流程
                if (ObjectUtil.isNotEmpty(updateInfo)) {
                    projThirdInterface.setStatus(0);
                    comments = updateInfo;
                }
                projThirdInterfaceMapper.updateById(projThirdInterface);
                //修改部署申请信息
                if (ObjectUtil.isNotEmpty(dto.getIp()) || ObjectUtil.isNotEmpty(dto.getMac())) {
                    ProjInterfaceVsDeviceInfo interfaceVsDeviceInfo =
                            projInterfaceVsDeviceInfoMapper.selectOne(new QueryWrapper<ProjInterfaceVsDeviceInfo>()
                                    .eq("third_interface_id", dto.getThirdInterfaceId())
                                    .orderByDesc("create_time")
                                    .last("limit 1"));
                    if (ObjectUtil.isNotEmpty(interfaceVsDeviceInfo)) {
                        interfaceVsDeviceInfo.setIp(dto.getIp() == null ? " " : dto.getIp());
                        interfaceVsDeviceInfo.setMac(dto.getMac() == null ? " " : dto.getMac());
                        projInterfaceVsDeviceInfoMapper.updateById(interfaceVsDeviceInfo);
                    } else {
                        ProjInterfaceVsDeviceInfo projInterfaceVsDeviceInfo = new ProjInterfaceVsDeviceInfo();
                        projInterfaceVsDeviceInfo.setThirdInterfaceId(dto.getThirdInterfaceId());
                        projInterfaceVsDeviceInfo.setIp(dto.getIp() == null ? " " : dto.getIp());
                        projInterfaceVsDeviceInfo.setMac(dto.getMac() == null ? " " : dto.getMac());
                        projInterfaceVsDeviceInfo.setPfAppId(" ");
                        projInterfaceVsDeviceInfo.setApplicant(userHelper.getCurrentUser().getUserName());
                        projInterfaceVsDeviceInfo.setApplicantPhone(userHelper.getCurrentUser().getPhone());
                        projInterfaceVsDeviceInfoService.insert(projInterfaceVsDeviceInfo);
                    }
                }
                String recordName = "修改接口";
                //绑定反馈单和三方接口关系
                if (ObjectUtil.isNotEmpty(dto.getFeedbackId())) {
                    boolean binding = bindingInterface(dto);
                    if (!binding) {
                        throw new CustomException("当前反馈单已绑定其他接口，无法继续绑定当前接口");
                    }
                    recordName = "运维平台反馈单修改接口";
                }

                /*SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
                param.setProjectInfoId(dto.getProjectInfoId());
                param.setHospitalInfoId(dto.getHospitalInfoId());
                param.setUserId(dto.getDirPersonId());
                param.setCode(DictProjectPlanItemEnum.SCHEDULE_THIRD_PART.getPlanItemCode());
                todoTaskService.projectTodoTaskInit(param);*/
                interfaceRecordLogService.saveInterfaceRecordLog(recordName, ti.getThirdInterfaceId(), comments);
                if (dto.getImplementsType() != 2) {
                    // geng xin san fang jie kou dui zhao guan xi ; geng xin yun wei ping tai ping tai zhuang tai wei 9
                    projThirdInterfaceMapper.updateFeedbackByInterfaceId(projThirdInterface.getThirdInterfaceId(), 9);
                }
                return Result.success("修改成功");
            } else {
                //调用裁定逻辑
                if (StringUtils.isEmpty(dto.getComments())) {
                    throw new CustomException("请输入裁定原因");
                }
                CheckInterfaceReq checkInterfaceReq = new CheckInterfaceReq();
                BeanUtil.copyProperties(ti, checkInterfaceReq);
                checkInterfaceReq.setComments(dto.getComments());
                checkInterfaceReq.setFlag(dto.getFlag());
                checkInterfaceReq.setInterfaceGroupList(dto.getInterfaceGroupList());
                //裁定
                Result result = this.checkInterface(checkInterfaceReq);
                if (!result.isSuccess()) {
                    throw new CustomException(result.getMsg());
                } else {
                    if (extractedOne(dto)) {
                        return Result.success();
                    }
                    return Result.success();
                }
            }
        } catch (Exception e) {
            log.error("修改/裁定三方接口信息异常", e);
            throw new CustomException(e.getMessage());
        }

    }

    /**
     * 修改/裁定三方接口信息
     *
     * @param dto
     * @return
     */
    private boolean extractedOne(ProjThirdInterfaceDTO dto) throws CustomException {
        // 埋点类的接口不生成反馈单
        if (dto.getInterfaceType() == 2) {
            return true;
        }
        // 裁定通过的， 项目组对接的， 开启三方接口流程限制， 当前客户为前后端运维的客户
        ProjThirdInterface tiss = projThirdInterfaceMapper.selectById(dto.getThirdInterfaceId());
        dto.setProjectInfoId(tiss.getProjectInfoId());
        //当前客户为前后端运维的客户 1是 0否
        Integer reslutValue = projThirdInterfaceMapper.selectInterfaceInOtherSystemInfo(dto);
        // 开启三方接口流程限制 1开启   0未开启
        Integer resultOpen = projectInfoMapper.queryOpenCustomDataFlowLimit(tiss.getCustomInfoId());
        if (dto.getFlag() == 1 && dto.getImplementsType() == 2 && reslutValue == 1 && resultOpen == 1) {
            try {
                // 查询医院信息
                ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectById(tiss.getHospitalInfoId());
                ProjCustomInfo projCustomInfo = customInfoMapper.selectById(tiss.getCustomInfoId());
                JSONObject feedback = new JSONObject();
                feedback.put("thirdInterfaceId", tiss.getThirdInterfaceId());
                // 只有非埋点类接口才传接口类型code 及 接口字典id
                if (ObjectUtil.isNotEmpty(tiss.getInterfaceType()) && tiss.getInterfaceType() != 2) {
                    feedback.put("thirdInterfaceTypeCode", tiss.getDictInterfaceCode());
                    feedback.put("thirdInterfaceTypeId", tiss.getDictInterfaceId());
                }
                feedback.put("thirdInterfaceTypeName", tiss.getDictInterfaceName());
                feedback.put("userQuestion", tiss.getBusinessDesc());
                if (ObjectUtil.isNotEmpty(hospitalInfo)) {
                    feedback.put("hospitalIdTrue", hospitalInfo.getCloudHospitalId());
                    feedback.put("hospitalNameTrue", hospitalInfo.getHospitalName());
                    feedback.put("programUrl", hospitalInfo.getCloudDomain());
                }
                if (ObjectUtil.isNotEmpty(projCustomInfo)) {
                    //  查询老平台客户信息
                    Customer customer = imspProjectMapper.selectCustomerByYYId(projCustomInfo.getYyCustomerId());
                    if (ObjectUtil.isNotEmpty(customer)) {
                        feedback.put("hospitalId", customer.getCustomerYunyingId());
                    }
                }
                // 用户信息
                SysLoginUser user = imspProjectMapper.selectUserByNewUserId(tiss.getCreaterId());
                if (ObjectUtil.isNotEmpty(user)) {
                    feedback.put("userId", user.getUserId());
                    feedback.put("name", user.getName());
                    feedback.put("deptId", user.getDeptId());
                    feedback.put("phone", user.getPhone());
                } else {
                    feedback.put("userId", tiss.getCreaterId());
                    feedback.put("name", "交付传入");
                    feedback.put("deptId", -1L);
                    feedback.put("phone", "");
                }
                // 查询老平台用户信息
                log.error("======调用客户运维平台前的用户信息======{}", user);
                log.error("======调用客户运维平台入参======{}", feedback);
                ResponseResult respStr = knowledgeFeignClient.addQuestionInfoByKFComeJf(feedback, getAuthorization());
                log.info("======调用运维平台新增反馈单======{}", respStr);
                dto.setFeedbackId(Long.valueOf((String) respStr.getData()));
                //  更新部门id和用户id
                log.error("======更新部门id和用户id==starts====");
                projThirdInterfaceMapper.updateDeptIdAndUserIdByParamers(dto);
                log.error("======更新部门id和用户id==end====");
                //绑定反馈单和三方接口关系
                if (ObjectUtil.isNotEmpty(dto.getFeedbackId())) {
                    // 查询反馈单对应的后端运维人员， 给客服提交人发消息提醒。
                    // 发送企业微信通知接口责任人/ 创建人和 项目经理
                    //3. 企业微信发送消息
                    extracted(dto, tiss);
                    boolean binding = bindingInterface(dto);
                }

            } catch (Exception e) {
                log.error("调用运维平台新增反馈单{}", e);
                throw new CustomException("调用运维平台新增反馈单异常");
            }
        } else if (dto.getFlag() == 1 && dto.getImplementsType() != 2 && reslutValue == 1 && resultOpen == 1) {
            // geng xin san fang jie kou dui zhao guan xi ; geng xin yun wei ping tai ping tai zhuang tai wei 9
            projFeedbackInterfaceMapper.delete(new QueryWrapper<ProjFeedbackInterface>().eq("interface_id", tiss.getThirdInterfaceId()));
            projThirdInterfaceMapper.updateFeedbackByInterfaceId(tiss.getThirdInterfaceId(), 9);
        }
        return false;
    }

    /**
     * 描述：发送企业微信通知接口责任人/ 创建人和 项目经理
     *
     * @param dto
     * @param tiss
     */
    private void extracted(ProjThirdInterfaceDTO dto, ProjThirdInterface tiss) {
        if ("prod".equals(activeProfiles) || !"".equals(activeProfiles)) {
            // 根据反馈单id查询后端处理人、
            SysUser sysUser = sysUserMapper.selectByFeedbackId(dto.getFeedbackId());
            if (!(ObjectUtil.isNotEmpty(sysUser) && ObjectUtil.isNotEmpty(sysUser.getSysUserId()))) {
                log.error("根据反馈单id查询后端处理人为空");
                return;
            }
            MessageParam messageParam = new MessageParam();
            List<Long> sysUserIds = new ArrayList<>();
            // 指定发送人为 责任人（责任人为空时 指定创建人）
            if (ObjectUtil.isNotEmpty(tiss.getCreaterId())) {
                sysUserIds.add(tiss.getCreaterId());
            }
            // 指定项目经理（运维平台反馈单提交的三方接口project_info_id=-1）
            ProjProjectInfo projProjectInfo = projectInfoMapper.selectById(tiss.getProjectInfoId());
            if (ObjectUtil.isNotEmpty(projProjectInfo)) {
                sysUserIds.add(projProjectInfo.getProjectLeaderId());
                messageParam.setProjectInfoId(tiss.getProjectInfoId());
            } else {
                messageParam.setProjectInfoId(null);
            }
            messageParam.setSysUserIds(sysUserIds);
            messageParam.setMessageToCategory(-1);
            messageParam.setMessageTypeId(3001L);
            messageParam.setTitle("三方接口");
            messageParam.setContent("当前项目已进入运维阶段，自动生成反馈单并分配给了后端接口组【" + sysUser.getUserName() + "】，请关注反馈单进度。");
            log.info("当前项目已进入运维阶段，自动生成反馈单并分配给了后端接口组 , {}", JSONUtil.toJsonStr(messageParam));
            try {
                sendMessageService.sendMessage(messageParam, true);
            } catch (Exception e) {
                log.error("当前项目已进入运维阶段，自动生成反馈单并分配给了后端接口组 ，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            }
        }
    }

    /**
     * 比对接口分类/数据集和对接方式是否修改
     *
     * @param dto
     * @return
     */
    private String compareUpdateInfo(ProjThirdInterfaceDTO dto) {
        StringBuffer updateInfo = new StringBuffer("");
        //查询接口信息
        ProjThirdInterface thirdInterface = projThirdInterfaceMapper.selectById(dto.getThirdInterfaceId());
        //修改后的分组授权信息
        if (ObjectUtil.isNotEmpty(dto.getInterfaceGroupApplyDetailDTOList())) {
            //当前接口是交互类
            List<ProjInterfaceGroupApplyDetailDTO> updateGroupApplyDetailList = dto.getInterfaceGroupApplyDetailDTOList();
            List<String> updateGroupList = updateGroupApplyDetailList.stream().map(group -> group.getPfInterfaceGroupName()).collect(Collectors.toList());
            //接口原分组授权信息
            List<ProjInterfaceGroupApplyDetail> orginGroupApplyDetailList = new ArrayList<>();
            orginGroupApplyDetailList =
                    interfaceGroupApplyDetailMapper.selectList(new QueryWrapper<ProjInterfaceGroupApplyDetail>()
                            .eq("third_interface_id", thirdInterface.getThirdInterfaceId())
                            .eq("environment", 0)
                    );
            if (CollectionUtil.isEmpty(orginGroupApplyDetailList)) {
                orginGroupApplyDetailList =
                        interfaceGroupApplyDetailMapper.selectList(new QueryWrapper<ProjInterfaceGroupApplyDetail>()
                                .eq("third_interface_id", thirdInterface.getThirdInterfaceId())
                                .eq("environment", 1)
                        );
            }
            List<String> orginGroupList = orginGroupApplyDetailList.stream().map(group -> group.getPfInterfaceGroupName()).distinct().collect(Collectors.toList());
            //修改前后修改的分组信息不一致，表示更改了接口分类信息
            if (!updateGroupList.stream().sorted().collect(Collectors.toList()).equals(orginGroupList.stream().sorted().collect(Collectors.toList()))) {
                updateInfo.append("接口分类由【").append(orginGroupList.stream().collect(Collectors.joining("，"))).append("】修改为【")
                        .append(updateGroupList.stream().collect(Collectors.joining("，"))).append("】；");
            }
        } else if (ObjectUtil.isNotEmpty(dto.getDataSetId())) {
            //当前接口是上传类
            if (!thirdInterface.getDataSetId().equals(dto.getDataSetId())) {
                updateInfo.append("接口数据集由【").append(thirdInterface.getDataSetName()).append("】修改为【").append(dto.getDataSetName()).append("】；");
            }
        }
        //校验对接方式是否更改
        if (ObjectUtil.isNotEmpty(dto.getImplementsType()) && ObjectUtil.isNotEmpty(thirdInterface.getImplementsType()) && dto.getImplementsType() != thirdInterface.getImplementsType()) {
            String updateImplementsName = "";
            switch (dto.getImplementsType()) {
                case 0:
                    updateImplementsName = "三方接口对接";
                    break;
                case 1:
                    updateImplementsName = "产品对接";
                    break;
                case 2:
                    updateImplementsName = "项目组对接";
                    break;
                default:
                    updateImplementsName = "";
                    break;
            }
            String orginImplementsName = "";
            switch (thirdInterface.getImplementsType()) {
                case 0:
                    orginImplementsName = "三方接口对接";
                    break;
                case 1:
                    orginImplementsName = "产品对接";
                    break;
                case 2:
                    orginImplementsName = "项目组对接";
                    break;
                default:
                    orginImplementsName = "";
                    break;
            }
            updateInfo.append("对接方式由【").append(orginImplementsName).append("】修改为【").append(updateImplementsName).append("】；");
        }
        if (ObjectUtil.isNotEmpty(updateInfo)) {
            return updateInfo.toString().substring(0, updateInfo.toString().length() - 1);
        } else {
            return "";
        }
    }

    /**
     * 三方接口导入模板下载
     *
     * @param response
     * @param projectInfoId
     */
    @Override
    public void downloadThirdInterfaceTemplate(HttpServletResponse response, Long projectInfoId) {
        //查询客户名称
        ProjProjectInfo projectInfo = projectInfoMapper.selectById(projectInfoId);
        ProjCustomInfo customInfo = customInfoMapper.selectById(projectInfo.getCustomInfoId());
        // 创建新的Excel工作簿
        Workbook workbook = new XSSFWorkbook();
        // 创建一个工作表(sheet)
        Sheet sheet = workbook.createSheet("三方接口导入模版");
        // 创建标题行
        Row headerRow = sheet.createRow(0);
        headerRow.setHeightInPoints(25);
        headerRow.createCell(0).setCellValue("*业务分类");
        headerRow.createCell(1).setCellValue("接口分类");
        headerRow.createCell(2).setCellValue("数据集");
        headerRow.createCell(3).setCellValue("*厂商名称");
        headerRow.createCell(4).setCellValue("接口版本");
        headerRow.createCell(5).setCellValue("厂商联系人");
        headerRow.createCell(6).setCellValue("联系人电话");
        headerRow.createCell(7).setCellValue("*期望完成时间");
        headerRow.createCell(8).setCellValue("*来源医院");
        headerRow.createCell(9).setCellValue("上线必备");
        headerRow.createCell(10).setCellValue("*定价分类");
        headerRow.createCell(11).setCellValue("*业务场景描述");
        // 创建样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        headerStyle.setFont(font);
        // 创建带星号(*)的标题行字体颜色为红色
        CellStyle redFontStyle = workbook.createCellStyle();
        Font redFont = workbook.createFont();
        redFont.setColor(IndexedColors.RED.getIndex());
        redFont.setBold(true);
        redFontStyle.setFont(redFont);
        for (Cell headerCell : headerRow) {
            if (headerCell.getStringCellValue().startsWith("*")) {
                headerCell.setCellStyle(redFontStyle);
            } else {
                headerCell.setCellStyle(headerStyle);
            }
        }
        DataValidationHelper helper = sheet.getDataValidationHelper();
        // 创建下拉框区域，从第2-499行，第0列【业务分类】
        String[] interfaceType = new String[] {"上传类", "交互类"};
        CellRangeAddressList interfaceTypeCheckList = new CellRangeAddressList(1, 500, 0, 0);
        DataValidation interfaceTypeDataValidation = helper.createValidation(helper.createExplicitListConstraint(interfaceType), interfaceTypeCheckList);
        interfaceTypeDataValidation.setSuppressDropDownArrow(true);
        interfaceTypeDataValidation.createErrorBox("错误", "请从列表中选择一个选项。");
        interfaceTypeDataValidation.setShowErrorBox(true);
        sheet.addValidationData(interfaceTypeDataValidation);
        //创建下拉框区域，从第2-499行，第1列【接口分类】
        List<ApiGroupResp> apiGroupList = interfaceVsAuthorService.getApiGroupList();
        if (CollectionUtil.isNotEmpty(apiGroupList)) {
            //创建隐藏sheet页，用来绑定超过50个的下拉项
            Sheet hidden1 = workbook.createSheet("hidden1");
            Cell cell = null;
            for (int i = 0; i < apiGroupList.size(); i++) {
                Row row = hidden1.createRow(i);
                cell = row.createCell(0);
                cell.setCellValue(apiGroupList.get(i).getGroupName());
            }
            Name name1 = workbook.createName();
            name1.setNameName("hidden1");
            name1.setRefersToFormula("hidden1!$A$1:$A$" + apiGroupList.size());
            DataValidationConstraint constraint = helper.createFormulaListConstraint(hidden1.getSheetName());
            workbook.setSheetHidden(1, true);
            CellRangeAddressList apiGroup = new CellRangeAddressList(1, 500, 1, 1);
            DataValidation apiGroupDataValidation = helper.createValidation(constraint, apiGroup);
            apiGroupDataValidation.setSuppressDropDownArrow(true);
            apiGroupDataValidation.createErrorBox("错误", "请从列表中选择一个选项。");
            apiGroupDataValidation.setShowErrorBox(true);
            sheet.addValidationData(apiGroupDataValidation);
        }
        //创建下拉框区域，从第2-499行，第2列【数据集】
        List<ResDataResp> resDataList = interfaceVsAuthorService.getResDataList();
        if (CollectionUtil.isNotEmpty(resDataList)) {
            //创建隐藏sheet页，用来绑定超过50个的下拉项
            Sheet hidden2 = workbook.createSheet("hidden2");
            Cell cell = null;
            for (int i = 0; i < resDataList.size(); i++) {
                Row row = hidden2.createRow(i);
                cell = row.createCell(0);
                cell.setCellValue(resDataList.get(i).getDataClassName());
            }
            Name name2 = workbook.createName();
            name2.setNameName("hidden2");
            name2.setRefersToFormula("hidden2!$A$1:$A$" + resDataList.size());
            DataValidationConstraint constraint = helper.createFormulaListConstraint(hidden2.getSheetName());
            workbook.setSheetHidden(2, true);
            CellRangeAddressList resDataAddressList = new CellRangeAddressList(1, 500, 2, 2);
            DataValidation resDataValidation = helper.createValidation(constraint, resDataAddressList);
            resDataValidation.setSuppressDropDownArrow(true);
            resDataValidation.createErrorBox("错误", "请从列表中选择一个选项。");
            resDataValidation.setShowErrorBox(true);
            sheet.addValidationData(resDataValidation);
        }
        //设置下拉框，从第2-499行，第8列【来源医院】
        SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
        selectHospitalDTO.setProjectInfoId(projectInfoId);
        List<ProjHospitalInfo> hospitalInfoList = hospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);
        if (CollectionUtil.isNotEmpty(hospitalInfoList)) {
            Map<Long, String> hospitalInfoChoices = Maps.newHashMap();
            for (ProjHospitalInfo projHospitalInfo : hospitalInfoList) {
                hospitalInfoChoices.put(projHospitalInfo.getHospitalInfoId(), projHospitalInfo.getHospitalName());
            }
            CellRangeAddressList hospitalInfoAddressList = new CellRangeAddressList(1, 500, 8, 8);
            DataValidation hospitalInfoDataValidation =
                    helper.createValidation(helper.createExplicitListConstraint(hospitalInfoChoices.values().toArray(new String[0])), hospitalInfoAddressList);
            hospitalInfoDataValidation.setSuppressDropDownArrow(true);
            hospitalInfoDataValidation.createErrorBox("错误", "请从列表中选择一个选项。");
            hospitalInfoDataValidation.setShowErrorBox(true);
            sheet.addValidationData(hospitalInfoDataValidation);
        }
        // 创建下拉框区域，从第2-499行，第9列【上线必备】
        String[] onlineFlag = new String[] {"是", "否"};
        CellRangeAddressList onlineFlagCheckList = new CellRangeAddressList(1, 500, 9, 9);
        DataValidation onlineFlagDataValidation =
                helper.createValidation(helper.createExplicitListConstraint(onlineFlag), onlineFlagCheckList);
        onlineFlagDataValidation.setSuppressDropDownArrow(true);
        onlineFlagDataValidation.createErrorBox("错误", "请从列表中选择一个选项。");
        onlineFlagDataValidation.setShowErrorBox(true);
        sheet.addValidationData(onlineFlagDataValidation);
        //设置下拉框，从第2-499行，第10列【定价分类】
        Result<List<DictProduct>> result = this.selectDictProductForInterface();
        if (CollectionUtil.isNotEmpty(result.getData())) {
            List<DictProduct> dictProductList = result.getData();
            //创建隐藏sheet页，用来绑定超过50个的下拉项
            Sheet hidden3 = workbook.createSheet("hidden3");
            Cell cell = null;
            for (int i = 0; i < dictProductList.size(); i++) {
                Row row = hidden3.createRow(i);
                cell = row.createCell(0);
                cell.setCellValue(dictProductList.get(i).getProductName());
            }
            Name name3 = workbook.createName();
            name3.setNameName("hidden3");
            name3.setRefersToFormula("hidden3!$A$1:$A$" + dictProductList.size());
            DataValidationConstraint constraint = helper.createFormulaListConstraint(hidden3.getSheetName());
            workbook.setSheetHidden(3, true);
            CellRangeAddressList dictProductAddressList = new CellRangeAddressList(1, 500, 10, 10);
            DataValidation dictProductDataValidation =
                    helper.createValidation(constraint, dictProductAddressList);
            dictProductDataValidation.setSuppressDropDownArrow(true);
            dictProductDataValidation.createErrorBox("错误", "请从列表中选择一个选项。");
            dictProductDataValidation.setShowErrorBox(true);
            sheet.addValidationData(dictProductDataValidation);
        }
        // 设置响应头信息
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode(customInfo.getCustomName() + "-三方接口导入模版.xlsx"));
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        // 写入到文件
        try {
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 检测接口名称与接口厂商是否为字典数据，当不是时 保存到字典中
     *
     * @param dto
     */
    void ifHasInterfaceAndFirm(ProjThirdInterfaceDTO dto) {
        if (ObjectUtil.isEmpty(dto.getDictInterfaceId()) && ObjectUtil.isNotEmpty(dto.getDictInterfaceName())) {
            List<DictInterface> dictInterfaces = dictInterfaceMapper.selectList(new QueryWrapper<DictInterface>()
                    .eq("dict_interface_name", dto.getDictInterfaceName())
            );
            if (CollectionUtil.isEmpty(dictInterfaces)) {
                DictInterface dictInterface = new DictInterface(SnowFlakeUtil.getId(), dto.getDictInterfaceName(),
                        new Date());
                dictInterfaceMapper.insert(dictInterface);
                dto.setDictInterfaceId(dictInterface.getDictInterfaceId());
            } else {
                dto.setDictInterfaceId(dictInterfaces.get(0).getDictInterfaceId());
            }
        }
        if (ObjectUtil.isEmpty(dto.getDictInterfaceFirmId()) && ObjectUtil.isNotEmpty(dto.getDictInterfaceFirmName())) {
            List<DictInterfaceFirm> dictInterfaceFirms = dictInterfaceFirmMapper.selectList(
                    new QueryWrapper<DictInterfaceFirm>()
                            .eq("dict_interface_firm_name", dto.getDictInterfaceFirmName())
            );
            if (CollectionUtil.isEmpty(dictInterfaceFirms)) {
                DictInterfaceFirm dictInterfaceFirm = new DictInterfaceFirm(SnowFlakeUtil.getId(),
                        dto.getDictInterfaceFirmName(), new Date());
                dictInterfaceFirmMapper.insert(dictInterfaceFirm);
                dto.setDictInterfaceFirmId(dictInterfaceFirm.getDictInterfaceFirmId());
            } else {
                dto.setDictInterfaceFirmId(dictInterfaceFirms.get(0).getDictInterfaceFirmId());
            }

        }
    }

    /**
     * 下载模板
     *
     * @param req
     */
    @Override
    public void downloadAuthTemplate(InterfaceBaseReq req, HttpServletResponse response) {
        // 设置响应内容类型为Word文档
        response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        // 设置响应头以指示这是一个文件下载，并设置下载文件的名称
        String headerKey = "Content-Disposition";
        String fileName = "授权书.docx";
        String headerValue = String.format("attachment; filename=\"%s\"", URLEncoder.encode(fileName));
        response.setHeader(headerKey, headerValue);
        //根据参数查询接口名称
        List<ProjThirdInterface> interfaceList = projThirdInterfaceMapper.selectByIds(req.getInterfaceIdList());
        // 获取客户名称
        ProjCustomInfo customInfo = customInfoMapper.selectById(interfaceList.get(0).getCustomInfoId());
        //操作模板，根据名称修改模板内容
        XWPFDocument document = new XWPFDocument();
        //获取文档中第一个段落
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();
        run.setText("接口对接授权委托书");
        run.setFontSize(16);
        paragraph.setAlignment(ParagraphAlignment.CENTER);
        run.addBreak();
        // 添加段落和文本
        XWPFParagraph paragraph1 = document.createParagraph();
        XWPFRun run1 = paragraph1.createRun();
        run1.setText("委托单位：" + customInfo.getCustomName());
        XWPFParagraph paragraph2 = document.createParagraph();
        XWPFRun run2 = paragraph2.createRun();
        run2.setText("受委托单位：众阳健康科技集团有限公司");
        XWPFParagraph paragraph3 = document.createParagraph();
        XWPFRun run3 = paragraph3.createRun();
        run3.setText("委托事由：以下接口的对接工作");
        run3.addBreak();
        // 创建表格
        XWPFTable table = document.createTable(interfaceList.size() + 1, 3); // 5行3列
        // 获取表格的属性
        CTTblPr tblPr = table.getCTTbl().getTblPr();
        // 设置表格居中对齐
        CTJc jc = CTTblPr.Factory.newInstance().addNewJc();
        jc.setVal(STJc.CENTER);
        tblPr.setJc(jc);
        // 设置表格标题
        XWPFTableRow titleRow = table.getRow(0);
        titleRow.getCell(0).setText("  序号  ");
        titleRow.getCell(1).setText("  接口  ");
        titleRow.getCell(2).setText("  三方系统厂商  ");
        // 填充表格数据
        for (int i = 1; i < interfaceList.size() + 1; i++) { // 从第1行开始填入数据
            XWPFTableRow row = table.getRow(i);
            row.getCell(0).setText(Integer.toString(i));
            if (i >= 1) {
                row.getCell(1).setText(interfaceList.get(i - 1).getDictInterfaceName());
                row.getCell(2).setText(interfaceList.get(i - 1).getDictInterfaceFirmName());
            }
        }
        // 遍历表格的每一行
        for (int r = 0; r < table.getRows().size(); r++) {
            XWPFTableRow row = table.getRow(r);
            // 遍历每一行中的单元格
            for (int c = 0; c < row.getTableCells().size(); c++) {
                XWPFTableCell cell = row.getCell(c);
                // 设置单元格宽度
                if (c == 0) { // 第一列
                    cell.setWidth("900");
                } else { // 其他列
                    cell.setWidth("4000");
                }
                // 设置单元格内容居中对齐
                CTPPr ppr = cell.getParagraphs().get(0).getCTP().getPPr();
                if (ppr == null) {
                    ppr = cell.getParagraphs().get(0).getCTP().addNewPPr();
                }
                CTJc jc2 = ppr.isSetJc() ? ppr.getJc() : ppr.addNewJc();
                jc2.setVal(STJc.CENTER); // 设置段落居中对齐
                // 设置单元格垂直居中对齐
                CTTcPr tcPr = cell.getCTTc().getTcPr();
                if (tcPr == null) {
                    tcPr = cell.getCTTc().addNewTcPr();
                }
                CTVerticalJc va = tcPr.isSetVAlign() ? tcPr.getVAlign() : tcPr.addNewVAlign();
                va.setVal(STVerticalJc.CENTER); // 设置单元格垂直居中对齐
            }
        }
        // 添加底部声明
        // 在表格下面添加段落
        XWPFParagraph paragraphAfterTable = document.createParagraph();
        XWPFRun runAfterTable = paragraphAfterTable.createRun();
        runAfterTable.addBreak();
        runAfterTable.addBreak();
        runAfterTable.setText("我单位已与第三方系统厂商签署相关数据安全及保密协议，现委托受托单位，以我单位名义，针对上述接口与第三方"
                + "系统厂商进行对接，授权受托单位提供标准的接口对接方案，受托单位不承担因接口对接而产生的数据泄露等数据安全的法律责任。");
        runAfterTable.addBreak();
        runAfterTable.setText("特此委托证明！");
        runAfterTable.setFontSize(10);
        runAfterTable.addBreak();
        runAfterTable.addBreak();
        XWPFParagraph xwpfParagraph3 = document.createParagraph();
        xwpfParagraph3.setAlignment(ParagraphAlignment.RIGHT); // 设置段落右对齐
        XWPFRun run5 = xwpfParagraph3.createRun();
        run5.setText("");
        run5.addBreak();
        XWPFParagraph xwpfParagraph2 = document.createParagraph();
        xwpfParagraph2.setAlignment(ParagraphAlignment.RIGHT); // 设置段落右对齐
        XWPFRun run4 = xwpfParagraph2.createRun();
        run4.addBreak();
        run4.addBreak();
        run4.setText("                                                      委托单位：          (盖章)");
        run4.addBreak();
        run4.setText("                                                      日期：     年    月    日");
        run4.setFontSize(10);
        run4.setBold(false);
        //输出到客户端
        try {
            // 将文档写入到ServletOutputStream中
            ServletOutputStream out = response.getOutputStream();
            document.write(out);
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 更新授权文件
     *
     * @param req
     * @return
     */
    @Override
    public Result updateAuthLetterFiles(UpdateAuthFileReq req) {
        projThirdInterfaceMapper.updateAuthLetterFiles(req.getFileId(), req.getInterfaceIdList());
        return Result.success();
    }

    /**
     * 查询接口明细数据
     *
     * @param thirdInterfaceId
     * @return
     */
    @Override
    public Result<ProjThirdInterfaceVO> selectThirdInterfaceDetail(Long thirdInterfaceId) {
        ProjThirdInterfacePageDTO projThirdInterfacePageDTO = new ProjThirdInterfacePageDTO();
        projThirdInterfacePageDTO.setThirdInterfaceId(thirdInterfaceId);
        List<ProjThirdInterfaceVO> projThirdInterfaceVOS = projThirdInterfaceMapper.selectThirdInterface(
                projThirdInterfacePageDTO);
        // 文件数据处理
        for (ProjThirdInterfaceVO vo : projThirdInterfaceVOS) {
            // 三方接口合同文件
            if (ObjectUtil.isNotEmpty(vo.getThirdContractFiles())) {
                List<ThirdInterfaceFileVO> thirdInterfaceFileVOList = new ArrayList<>();
                String[] split = vo.getThirdContractFiles().split(",");
                for (String fileId : split) {
                    ThirdInterfaceFileVO thirdInterfaceFileVO = new ThirdInterfaceFileVO();
                    ProjProjectFile projProjectFile = projectFileMapper.selectById(Convert.toLong(fileId));
                    thirdInterfaceFileVO.setProjectFileId(projProjectFile.getProjectFileId());
                    thirdInterfaceFileVO.setName(projProjectFile.getFileName());
                    thirdInterfaceFileVO.setUrl(OBSClientUtils.getTemporaryUrl(projProjectFile.getFilePath(), 3600));
                    thirdInterfaceFileVOList.add(thirdInterfaceFileVO);
                }
                vo.setThirdContractFileVOList(thirdInterfaceFileVOList);
            }
            // 三方接口文档
            if (ObjectUtil.isNotEmpty(vo.getThirdInterfaceFiles())) {
                List<ThirdInterfaceFileVO> thirdInterfaceFileVOList = new ArrayList<>();
                String[] split = vo.getThirdInterfaceFiles().split(",");
                for (String fileId : split) {
                    ThirdInterfaceFileVO thirdInterfaceFileVO = new ThirdInterfaceFileVO();
                    ProjProjectFile projProjectFile = projectFileMapper.selectById(Convert.toLong(fileId));
                    thirdInterfaceFileVO.setProjectFileId(projProjectFile.getProjectFileId());
                    thirdInterfaceFileVO.setName(projProjectFile.getFileName());
                    thirdInterfaceFileVO.setUrl(OBSClientUtils.getTemporaryUrl(projProjectFile.getFilePath(), 3600));
                    thirdInterfaceFileVOList.add(thirdInterfaceFileVO);
                }
                vo.setThirdInterfaceFileVOList(thirdInterfaceFileVOList);
            }
            // 接口授权文档
            if (ObjectUtil.isNotEmpty(vo.getAuthLetterFiles())) {
                List<ThirdInterfaceFileVO> authLetterFiles = new ArrayList<>();
                String[] split = vo.getAuthLetterFiles().split(",");
                for (String fileId : split) {
                    ThirdInterfaceFileVO thirdInterfaceFileVO = new ThirdInterfaceFileVO();
                    ProjProjectFile projProjectFile = projectFileMapper.selectById(Convert.toLong(fileId));
                    thirdInterfaceFileVO.setProjectFileId(projProjectFile.getProjectFileId());
                    thirdInterfaceFileVO.setName(projProjectFile.getFileName());
                    thirdInterfaceFileVO.setUrl(OBSClientUtils.getTemporaryUrl(projProjectFile.getFilePath(), 3600));
                    authLetterFiles.add(thirdInterfaceFileVO);
                }
                vo.setAuthLetterFileVOList(authLetterFiles);
            }
        }
        ProjThirdInterfaceVO projThirdInterfaceVO = projThirdInterfaceVOS.get(0);
        // 获取分组授权信息
        List<ProjInterfaceGroupApplyDetail> thirdInterfaceGroupVOS = new ArrayList<>();
        thirdInterfaceGroupVOS =
                interfaceGroupApplyDetailMapper.selectList(new QueryWrapper<ProjInterfaceGroupApplyDetail>()
                        .eq("third_interface_id", projThirdInterfaceVO.getThirdInterfaceId())
                        .eq("environment", 0)
                );
        if (CollectionUtil.isEmpty(thirdInterfaceGroupVOS)) {
            thirdInterfaceGroupVOS =
                    interfaceGroupApplyDetailMapper.selectList(new QueryWrapper<ProjInterfaceGroupApplyDetail>()
                            .eq("third_interface_id", projThirdInterfaceVO.getThirdInterfaceId())
                            .eq("environment", 1)
                    );
        }
        List<ThirdInterfaceGroupVO> groupVOS = new ArrayList<>();
        // 根据分组id进行分组
        Map<Long, List<ProjInterfaceGroupApplyDetail>> collect = thirdInterfaceGroupVOS.stream().collect(groupingBy(ProjInterfaceGroupApplyDetail::getPfInterfaceGroupId));
        collect.forEach((k, v) -> {
            ThirdInterfaceGroupVO thirdInterfaceGroupVO = new ThirdInterfaceGroupVO();
            thirdInterfaceGroupVO.setPfInterfaceGroupId(v.get(0).getPfInterfaceGroupId());
            thirdInterfaceGroupVO.setPfInterfaceGroupName(v.get(0).getPfInterfaceGroupName());
            groupVOS.add(thirdInterfaceGroupVO);
        });
        projThirdInterfaceVO.setInterfaceGroupApplyDetailDTOList(groupVOS);
        //获取部署申请信息
        List<ProjInterfaceVsDeviceInfo> interfaceVsDeviceInfoList =
                projInterfaceVsDeviceInfoMapper.selectList(new QueryWrapper<ProjInterfaceVsDeviceInfo>()
                        .eq("third_interface_id", thirdInterfaceId)
                        .orderByDesc("create_time")
                        .last("limit 1")
                );
        if (CollectionUtil.isNotEmpty(interfaceVsDeviceInfoList)) {
            ProjInterfaceVsDeviceInfo interfaceVsDeviceInfo = interfaceVsDeviceInfoList.get(0);
            // 转换返回的数据 【和前端传入的数据进行匹配】
            projThirdInterfaceVO.setIp(interfaceVsDeviceInfo.getIp());
            projThirdInterfaceVO.setMac(interfaceVsDeviceInfo.getMac());
        }
        List<ProjInterfaceVsWorkflow> groupList = projInterfaceVsWorkflowMapper.selectList(new QueryWrapper<ProjInterfaceVsWorkflow>().eq("third_interface_id", thirdInterfaceId).eq("is_deleted", 0));
        if (groupList != null) {
            List<ThridWorkflowResourceQueryResp> groupListVo = new ArrayList<>();
            ThridWorkflowResourceQueryResp vo = null;
            for (ProjInterfaceVsWorkflow flow : groupList) {
                vo = new ThridWorkflowResourceQueryResp();
                BeanUtil.copyProperties(flow, vo);
                vo.setId(String.valueOf(flow.getId()));
                vo.setGroupId(String.valueOf(flow.getGroupId()));
                groupListVo.add(vo);
            }
            projThirdInterfaceVO.setGroupList(groupListVo);
        }
        return Result.success(projThirdInterfaceVO);
    }


    /**
     * 删除接口
     *
     * @param thirdInterfaceIdList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result deleteInterfaceById(List<Long> thirdInterfaceIdList) {
        for (Long thirdInterfaceId : thirdInterfaceIdList) {
            //查询接口信息
            ProjThirdInterface thirdInterface = projThirdInterfaceMapper.selectById(thirdInterfaceId);
            //已转接口分公司且未被驳回的接口不允许删除
            if (ObjectUtil.isNotEmpty(thirdInterface.getDirPersonId())) {
                //查询当前责任人是否是接口分公司的人
                SysUser sysUser = sysUserMapper.selectById(thirdInterface.getDirPersonId());
                SysDept sysDept = sysDeptMapper.selectOne(new QueryWrapper<SysDept>()
                        .eq("dept_yunying_id", sysUser.getDeptId()));
                // 判断部门类型是否为 interface
                if ("interface".equals(sysDept.getDeptCategory()) && thirdInterface.getStatus() != 14) {
                    return Result.fail("【" + thirdInterface.getDictInterfaceName() + "】已转接口分公司，不能删除！");
                }
            }
            projThirdInterfaceMapper.deleteById(thirdInterfaceId);
            interfaceRecordLogService.saveInterfaceRecordLog("删除接口", thirdInterface.getThirdInterfaceId(), null);
            projProjectPlanService.addTotalCountByProjectInfoIdAndItemCode(thirdInterface.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_THIRD_PART, -1);
            todoTaskService.todoTaskTotalCountSync(thirdInterface.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_THIRD_PART.getPlanItemCode());
            todoTaskService.todoTaskTotalCountSync(thirdInterface.getProjectInfoId(), DictProjectPlanItemEnum.SCHEDULE_THIRD_PART.getPlanItemCode());
        }
        return Result.success();
    }

    /**
     * 指定责任人，当责任人属于接口分公司时。转到运营平台走接口实施流程
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result updateDirPerson(ProjThirdInterfaceDTO dto) {
        // 支持批量分配责任人
        for (Long thirdInterfaceId : dto.getThirdInterfaceIdList()) {
            dto.setThirdInterfaceId(thirdInterfaceId);
            //查询接口信息
            ProjThirdInterface thirdInterface = projThirdInterfaceMapper.selectById(thirdInterfaceId);
            // 查询项目信息
            ProjCustomInfo customInfo = customInfoMapper.selectById(thirdInterface.getCustomInfoId());
            // 判断电销
            Boolean isTaleCustom = customInfo != null && customInfo.getTelesalesFlag() == 1;
            // 根据人员id查询部门信息 更新三方接口数据
            SysUser sysUser = sysUserMapper.selectById(dto.getDirPersonId());
            if (ObjectUtil.isNotEmpty(sysUser)) {
                // 获取当前登录人的部门信息，判断分配的责任人是否是本团队成员
                Long userDeptId = userHelper.getCurrentUser().getDeptId();
                //如果不是本团队成员，需要检验接口是否维护了分类属性 电销客户需要限制走运营平台流程
                if (!userDeptId.equals(sysUser.getDeptId()) && customInfo != null && isTaleCustom) {
                    if (ObjectUtil.isEmpty(thirdInterface.getYyProductId()) || thirdInterface.getYyProductId() == -1) {
                        return Result.fail("请先在接口详情中维护《定价分类》再分配责任人！");
                    }
                }
                SysDept sysDept = sysDeptMapper.selectOne(new QueryWrapper<SysDept>()
                        .eq("dept_yunying_id", sysUser.getDeptId())
                );
                ProjThirdInterface projThirdInterface = new ProjThirdInterface();
                projThirdInterface.setThirdInterfaceId(thirdInterfaceId);
                projThirdInterface.setDirPersonId(dto.getDirPersonId());
                projThirdInterface.setDirTeamId(sysDept.getSysDeptId());
                // 判断部门类型是否为 interface
                if (ObjectUtil.isNotEmpty(sysDept) && "interface".equals(sysDept.getDeptCategory()) && isTaleCustom) {
                    //转接口分公司时，如果当前已经是转了接口分公司，且不是被驳回状态（status=17），不可重复转
                    if (ObjectUtil.isNotEmpty(thirdInterface.getDirPersonId())) {
                        SysUser oldUser = sysUserMapper.selectById(thirdInterface.getDirPersonId());
                        SysDept oldDept = sysDeptMapper.selectOne(new QueryWrapper<SysDept>()
                                .eq("dept_yunying_id", oldUser.getDeptId())
                        );
                        if ("interface".equals(oldDept.getDeptCategory()) && thirdInterface.getStatus() != 14) {
                            return Result.fail("当前接口已转接口分公司，不可重复操作，如需更换，请先联系运营平台驳回申请！");
                        }
                    }
                    dto.setDirTeamId(sysDept.getSysDeptId());
                    dto.setDirPersonId(sysUser.getSysUserId());
                    dto.setThirdInterfaceId(thirdInterfaceId);
                    // 运营平台接口实施流程（云）对接
                    InterfaceMainDto interfaceMainDto = intoInterfaceMain(dto);
                    Map<String, Object> stringResponseData = yunyingFeignClient.interfaceMain(interfaceMainDto);
                    log.info("转接口分公司 运营平台出参 ：--------------" + JSONObject.toJSONString(stringResponseData));
                    if ("400".equals(stringResponseData.get("code").toString())) {
                        return Result.fail(stringResponseData.get("msg").toString());
                    }
                    Map<String, Object> obj = (Map<String, Object>) stringResponseData.get("obj");
                    //运维平台返回的id
                    Object mainId = obj.get("mainId");
                    projThirdInterface.setMainId(Convert.toLong(mainId));
                }
                projThirdInterfaceMapper.updateById(projThirdInterface);
                SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
                param.setProjectInfoId(thirdInterface.getProjectInfoId());
                param.setHospitalInfoId(thirdInterface.getHospitalInfoId());
                param.setUserId(dto.getDirPersonId());
                param.setCode(DictProjectPlanItemEnum.SCHEDULE_THIRD_PART.getPlanItemCode());
                todoTaskService.projectTodoTaskInit(param);
                // 添加日志信息
                interfaceRecordLogService.saveInterfaceRecordLog("转接口分公司", thirdInterfaceId, null);
            }
        }
        return Result.success();
    }

    /**
     * 接口申请裁定
     *
     * @param thirdInterfaceIdList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result interfaceApplyRuled(List<Long> thirdInterfaceIdList) {
        Boolean success = true;
        StringBuilder sb = new StringBuilder();
        sb.append("接口数据完整性校验失败{：");
        // 检查数据完整性 == 所有必填项校验
        for (Long thirdInterfaceId : thirdInterfaceIdList) {
            Boolean flag = false;
            StringBuilder errorMessage = new StringBuilder();
            ProjThirdInterface projThirdInterface = projThirdInterfaceMapper.selectById(thirdInterfaceId);
            // 查询 接口的分组授权信息是否已经选择
            List<ProjInterfaceGroupApplyDetail> projInterfaceGroupApplyDetails
                    = interfaceGroupApplyDetailMapper.selectList(
                    new QueryWrapper<ProjInterfaceGroupApplyDetail>()
                            .eq("third_interface_id", thirdInterfaceId)
            );
            if (CollectionUtil.isEmpty(projInterfaceGroupApplyDetails) && projThirdInterface.getInterfaceType() == 1) {
                flag = true;
                errorMessage.append("请在详情->修改接口增加接口授权分类,");
            }
            if (ObjectUtil.isEmpty(projThirdInterface.getExpectTime())) {
                flag = true;
                errorMessage.append("期望完成时间为空,");
            }
            if (ObjectUtil.isEmpty(projThirdInterface.getBusinessDesc())) {
                flag = true;
                errorMessage.append("业务描述为空,");
            }
            if (ObjectUtil.isEmpty(projThirdInterface.getYyProductId())) {
                flag = true;
                errorMessage.append("定价分类为空,");
            }
            if (ObjectUtil.isEmpty(projThirdInterface.getAuthLetterFiles())) {
                flag = true;
                errorMessage.append("委托书文件未上传,");
            }
            if (projThirdInterface.getContractTag() == 1 && ObjectUtil.isEmpty(projThirdInterface.getThirdContractFiles())) {
                flag = true;
                errorMessage.append("三方厂商合同未上传,");
            }
            if (flag) {
                String errorStr = errorMessage.substring(0, errorMessage.length() - 1);
                success = false;
                sb.append("【" + projThirdInterface.getDictInterfaceName() + "】:" + errorStr + "；\n");
            }

        }
        // 完整性校验通过后，更新数据
        if (success) {
            for (Long thirdInterfaceId : thirdInterfaceIdList) {
                ProjThirdInterface projThirdInterface = projThirdInterfaceMapper.selectById(thirdInterfaceId);
                projThirdInterface.setStatus(1);
                projThirdInterface.setReviewSubmitter(userHelper.getCurrentUser().getSysUserId());
                projThirdInterface.setReviewSubmitTime(new Date());
                // 更新责任人为当前登录人
                projThirdInterface.setDirPersonId(userHelper.getCurrentUser().getSysUserId());
                if (projThirdInterface.getCreaterId() == 0) {
                    projThirdInterface.setCreaterId(userHelper.getCurrentUser().getSysUserId());
                }
                projThirdInterfaceMapper.updateById(projThirdInterface);
                // 埋点类调用系统管理
                this.saveInterfaceToSystem(projThirdInterface);
                // 添加日志信息
                interfaceRecordLogService.saveInterfaceRecordLog("接口申请裁定", thirdInterfaceId, null);
                // 发送企业微信通知 ****  只有生产环境才能发送企业微信。其他环境不发送
                if ("prod".equals(activeProfiles)) {
                    MessageParam messageParam = new MessageParam();
                    List<Long> sysUserIds = new ArrayList<>();
                    String dirPersonName = "";
                    // 指定发送人为 责任人（责任人为空时 指定创建人。 和 裁定人员）
                    //TODO 不给负责人发消息
//                    if (ObjectUtil.isNotEmpty(projThirdInterface.getDirPersonId())) {
//                        sysUserIds.add(projThirdInterface.getDirPersonId());
//                        SysUser user = sysUserMapper.selectById(projThirdInterface.getDirPersonId());
//                        dirPersonName = user.getUserName();
//                    } else {
//                        sysUserIds.add(projThirdInterface.getCreaterId());
//                        SysUser user = sysUserMapper.selectById(userHelper.getCurrentUser().getSysUserId());
//                        dirPersonName = user.getUserName();
//                    }
                    //TODO 只给提交人发送消息
                    sysUserIds.add(projThirdInterface.getCreaterId());
                    SysUser user = sysUserMapper.selectById(userHelper.getCurrentUser().getSysUserId());
                    dirPersonName = user.getUserName();
                    // 指定裁定角色的人员进行发送消息
                    List<SysRole> sysRoles = sysRoleMapper.selectList(new QueryWrapper<SysRole>()
                            .eq("role_code", "24")
                    );
                    // 查询角色下的人员id
                    List<SysUser> userList = sysUserMapper.selectUserByRoleId(sysRoles.get(0).getSysRoleId());
                    if (CollectionUtil.isNotEmpty(userList)) {
                        userList.stream().forEach(u -> sysUserIds.add(u.getSysUserId()));
                    }
                    messageParam.setSysUserIds(sysUserIds);
                    messageParam.setMessageToCategory(-1);
                    messageParam.setMessageTypeId(3001L);
                    messageParam.setProjectInfoId(projThirdInterface.getProjectInfoId());
                    messageParam.setTitle("三方接口");
                    LambdaQueryWrapper<ProjCustomInfo> wrapper = Wrappers.lambdaQuery(ProjCustomInfo.class);
                    wrapper.select(ProjCustomInfo::getCustomName).eq(ProjCustomInfo::getIsDeleted, 0);
                    if (projThirdInterface.getCustomInfoId() != null && projThirdInterface.getCustomInfoId() > 0) {
                        wrapper.eq(ProjCustomInfo::getCustomInfoId, projThirdInterface.getCustomInfoId());
                    } else {
                        ProjProjectInfo proj = new LambdaQueryChainWrapper<>(projectInfoMapper)
                                .select(ProjProjectInfo::getCustomInfoId)
                                .eq(ProjProjectInfo::getProjectInfoId, projThirdInterface.getProjectInfoId())
                                .one();
                        ProjHospitalInfo projHospitalInfo = new LambdaQueryChainWrapper<>(hospitalInfoMapper)
                                .eq(ProjHospitalInfo::getIsDeleted, 0)
                                .and(i -> i.eq(ProjHospitalInfo::getCloudHospitalId, projThirdInterface.getHospitalInfoId()).or().eq(ProjHospitalInfo::getHospitalInfoId, projThirdInterface.getHospitalInfoId()))
                                .one();
                        if (proj != null) {
                            wrapper.eq(ProjCustomInfo::getCustomInfoId, proj.getCustomInfoId());
                        } else if (projHospitalInfo != null) {
                            wrapper.eq(ProjCustomInfo::getCustomInfoId, projHospitalInfo.getCustomInfoId());
                        } else {
                            log.error("该三方接口无法找到所属客户: {}", JSON.toJSONString(projThirdInterface));
                            throw new CustomException("该三方接口无法找到所属客户");
                        }
                    }
                    wrapper.last("limit 1");
                    ProjCustomInfo customer = customInfoMapper.selectOne(wrapper);
                    messageParam.setContent(dirPersonName + "提交了【" + customer.getCustomName() + "】客户【" + projThirdInterface.getDictInterfaceName()
                            + "】的三方接口裁定申请,请知悉。");
                    log.info("三方接口申请授权 ， 发送企业微信通知责任人与授权人消息 , {}", JSONUtil.toJsonStr(messageParam));
                    sendMessageService.sendMessage(messageParam, true);
                }
                SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
                param.setProjectInfoId(projThirdInterface.getProjectInfoId());
                param.setHospitalInfoId(projThirdInterface.getHospitalInfoId());
                param.setUserId(projThirdInterface.getDirPersonId());
                param.setCode(DictProjectPlanItemEnum.SCHEDULE_THIRD_PART.getPlanItemCode());
                todoTaskService.updateProjectTodoTaskStatus(param);
            }
            return Result.success();
        } else {
            return Result.fail(sb.toString());
        }
    }

    /**
     * 埋点类调用系统管理
     *
     * @param projThirdInterface
     */
    private void saveInterfaceToSystem(ProjThirdInterface projThirdInterface) {
        if (projThirdInterface.getInterfaceType() != 2) {
            return;
        }
        ProjHospitalInfo projHospitalInfo = projHospitalInfoMapper.selectById(projThirdInterface.getHospitalInfoId());
        Long systemIdOne = 1000012L;
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(projHospitalInfo);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        Long groupId = 0L;
        List<WorkflowInstanceCommonDataSaveParam> commonDataList = new ArrayList<>();
        List<ProjInterfaceVsWorkflow> groupList = projInterfaceVsWorkflowMapper.selectList(new QueryWrapper<ProjInterfaceVsWorkflow>().eq("third_interface_id", projThirdInterface.getThirdInterfaceId()).eq("is_deleted", 0));
        if (groupList != null && groupList.size() > 0) {
            for (ProjInterfaceVsWorkflow wf : groupList) {
                WorkflowInstanceCommonDataSaveParam voe = new WorkflowInstanceCommonDataSaveParam();
                voe.setDataId(wf.getId());
                voe.setDataValue(wf.getResourceId());
                voe.setDataRowNum(wf.getId());
                voe.setFieldCode(wf.getResourceCode());
                voe.setFieldDes(wf.getResourceDes());
                commonDataList.add(voe);
                groupId = wf.getGroupId();
            }
        } else {
            throw new CustomException("埋点接口未选择");
        }
        WorkflowInstanceSaveParam dto = new WorkflowInstanceSaveParam();
        dto.setHospitalId(projHospitalInfo.getCloudHospitalId());
        dto.setHisOrgId(projHospitalInfo.getOrgId());
        dto.setBusinessCode("JFPT_MDPZSH");
        Long businessId = SnowFlakeUtil.getId();
        log.error("==systemSettingWorkflowApi.save= thirid=" + projThirdInterface + "; businessId =" + businessId);
        dto.setBusinessId(businessId);
        if ("prod".equals(activeProfiles)) {
            dto.setDataUrl("-");
        } else {
            dto.setDataUrl("-");
        }
        dto.setDeptId(systemIdOne);
        dto.setGroupId(groupId);
        dto.setJsonData(JSON.toJSONString(commonDataList));
        dto.setReason(projThirdInterface.getBusinessDesc());
        dto.setIdentityId(systemIdOne);
        dto.setCommonDataList(commonDataList);
        ResponseResult<WorkflowInstanceVO> result = systemSettingWorkflowApi.save(dto);
        if (result.isSuccess()) {
            WorkflowInstanceVO vo = result.getData();
            projThirdInterface.setInstanceId(vo.getId());
            this.projThirdInterfaceMapper.updateById(projThirdInterface);
        } else {
            log.error("调用系统流程失败:{}", result);
            throw new CustomException("调用系统流程失败");
        }
    }

    /**
     * 查询待评审的接口列表
     *
     * @param dto
     * @return
     */
    @Override
    public Result<PageInfo<InterfaceReviewVO>> selectPageForReviewInterface(InterfaceReviewPageDTO dto) {
        List<InterfaceReviewVO> interfaceReviewVOList = PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> projThirdInterfaceMapper.selectPageForReviewInterface(dto));
        return Result.success(new PageInfo<>(interfaceReviewVOList));
    }

    /**
     * 接口-裁定/驳回/转评审/转需求
     *
     * @param req
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result checkInterface(CheckInterfaceReq req) {
        /**
         * 接口状态【
         *         初始阶段： 0：未申请；1：提交裁定；
         *         裁定阶段： 11：裁定驳回；12、待评审；13：裁定通过；14：接口分公司驳回
         *         测试阶段： 21、测试环境申请授权；22：测试环境授权通过；23：研发中；24、测试环境测试完成；
         *         正式阶段： 31：申请正式环境授权；32：正式环境授权通过；33：研发完成；【下载SDK操作】
         *         50:接口完成 【提交完成按钮触发：三方对接的检测正式环境测试结果；项目组对接 检测正式环境测试结果与是否下载SDK】
         *      】
         *
         *
         *    flag "前端标志 【1-裁定通过 2-裁定驳回 3-待评审 4-转需求 5-重新裁定】")
         */
        // 根据前端传入的 flag修改状态
        switch (req.getFlag()) {
            case 1:
                req.setStatus(13);
                break;
            case 2:
                req.setStatus(11);
                break;
            case 3:
                req.setStatus(12);
                break;
            default:
                req.setStatus(13);
                break;
        }
        ProjThirdInterface param = projThirdInterfaceMapper.selectById(req.getThirdInterfaceId());
        // 裁定结果：埋点类
        if (param.getInterfaceType() == 2 && req.getFlag() != 5 && req.getFlag() != 3) {
            this.progressToSystem(param, req.getFlag(), req.getComments());
        }
        if (param.getInterfaceType() == 2 && req.getFlag() == 5) {
            this.saveInterfaceToSystem(param);
            this.progressToSystem(param, req.getFlag(), req.getComments());
        }
        //2. 裁定通过-非上传类测试环境自动授权；
        if (req.getStatus() == 13) {
            ProjCustomInfo customInfo = customInfoMapper.selectById(param.getCustomInfoId());
            if (req.getInterfaceType() == 1) {
                // 交互类申请
                List<String> groupIds =
                        req.getInterfaceGroupList().stream().map(vo -> Convert.toStr(vo.getId())).collect(Collectors.toList());
                //调用接口开发平台传递数据
                GroupApplyVO groupApplyVO = getInsertDemandReq(param, customInfo, 1, groupIds);
                if (!groupApplyVO.getSuccess()) {
                    Result result = new Result<>();
                    result.setSuccess(false);
                    result.setMsg(groupApplyVO.getMsg());
                    return result;
                }
                // 查询授权信息，如果不存在授权信息则新增授权数据,存在的话则更新授权信息
                List<ProjInterfaceVsAuthor> autherList = interfaceVsAuthorMapper.selectList(new QueryWrapper<ProjInterfaceVsAuthor>()
                        .eq("third_interface_id", param.getThirdInterfaceId())
                        .eq("environment", 0));
                if (CollectionUtil.isEmpty(autherList)) {
                    ProjInterfaceVsAuthor projInterfaceVsAuthor = new ProjInterfaceVsAuthor();
                    projInterfaceVsAuthor.setInterfaceVsAuthorId(SnowFlakeUtil.getId());
                    projInterfaceVsAuthor.setThirdInterfaceId(param.getThirdInterfaceId());
                    projInterfaceVsAuthor.setFirmName(param.getDictInterfaceFirmName());
                    projInterfaceVsAuthor.setEnvironment(0);
                    projInterfaceVsAuthor.setPfAppId(groupApplyVO.getAppId());
                    projInterfaceVsAuthor.setPfPrivateKey(groupApplyVO.getAppSecret());
                    projInterfaceVsAuthor.setPfAuthApplyId(groupApplyVO.getApplyId());
                    interfaceVsAuthorMapper.insert(projInterfaceVsAuthor);
                } else {
                    ProjInterfaceVsAuthor interfaceVsAuthor = new ProjInterfaceVsAuthor();
                    interfaceVsAuthor.setPfAppId(groupApplyVO.getAppId());
                    interfaceVsAuthor.setPfPrivateKey(groupApplyVO.getAppSecret());
                    interfaceVsAuthor.setPfAuthApplyId(groupApplyVO.getApplyId());
                    UpdateWrapper<ProjInterfaceVsAuthor> projInterfaceVsAuthorUpdateWrapper = new UpdateWrapper<>();
                    projInterfaceVsAuthorUpdateWrapper.eq("third_interface_id", param.getThirdInterfaceId());
                    projInterfaceVsAuthorUpdateWrapper.eq("environment", 0);
                    interfaceVsAuthorMapper.update(interfaceVsAuthor, projInterfaceVsAuthorUpdateWrapper);
                }
            } else if (req.getInterfaceType() == 0) {
                // 上传类接口 裁定时 只进行接口数据保存。不进行申请授权
                GroupApplyVO groupApplyVO = getInsertDemandReq(param, customInfo, 1, Arrays.asList("6887381997348458376"));
                if (!groupApplyVO.getSuccess()) {
                    Result result = new Result<>();
                    result.setSuccess(false);
                    result.setMsg(groupApplyVO.getMsg());
                    return result;
                }
            }
            if (req.getInterfaceType() == 2) {
                param.setStatus(13);
            } else {
                // 测试环境直接测试环境授权通过
                param.setStatus(22);
            }

        } else {
            //裁定驳回或者待审核，直接赋值接口状态即可
            param.setStatus(req.getStatus());
        }
        //1. 修改接口状态以及裁定人员信息
        param.setReviewDate(new Date());
        param.setReviewUserId(userHelper.getCurrentUser().getSysUserId());
        String recordName = "";
        if (req.getStatus() == 13) {
            recordName = "接口裁定通过";
        } else if (req.getStatus() == 11) {
            recordName = "接口裁定驳回";
        } else if (req.getStatus() == 12) {
            recordName = "接口待评审";
        } else if (req.getStatus() == 13 && req.getFlag() == 5) {
            recordName = "重新裁定";
        }
        //4. 日志记录
        interfaceRecordLogService.saveInterfaceRecordLog(recordName, req.getThirdInterfaceId(), req.getComments());
        // 发送企业微信通知接口责任人/ 创建人和 项目经理
        //3. 企业微信发送消息
        if ("prod".equals(activeProfiles)) {
            MessageParam messageParam = new MessageParam();
            List<Long> sysUserIds = new ArrayList<>();
            // 指定发送人为 责任人（责任人为空时 指定创建人）
            if (ObjectUtil.isNotEmpty(param.getDirPersonId())) {
                sysUserIds.add(param.getDirPersonId());
            } else {
                sysUserIds.add(param.getCreaterId());
            }
            // 指定项目经理（运维平台反馈单提交的三方接口project_info_id=-1）
            ProjProjectInfo projProjectInfo = projectInfoMapper.selectById(param.getProjectInfoId());
            if (ObjectUtil.isNotEmpty(projProjectInfo)) {
                sysUserIds.add(projProjectInfo.getProjectLeaderId());
                messageParam.setProjectInfoId(param.getProjectInfoId());
            } else {
                messageParam.setProjectInfoId(null);
            }
            messageParam.setSysUserIds(sysUserIds);
            messageParam.setMessageToCategory(-1);
            messageParam.setMessageTypeId(3001L);
            messageParam.setTitle("三方接口");
            messageParam.setContent("【" + param.getDictInterfaceName() + "】已裁定,裁定结果为：【" + recordName + "】。");
            log.info("三方接口裁定 ， 发送企业微信通知责任人与项目经理 , {}", JSONUtil.toJsonStr(messageParam));
            try {
                sendMessageService.sendMessage(messageParam, true);
            } catch (Exception e) {
                log.error("三方接口裁定 ， 发送企业微信通知责任人与项目经理 ，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            }
        }
        projThirdInterfaceMapper.updateById(param);
        // 埋点类接口则审核通过判定为完成。
        if (param.getInterfaceType() == 2) {
            todoTaskService.todoTaskTotalCountSync(param.getProjectInfoId(), DictProjectPlanItemEnum.SCHEDULE_THIRD_PART.getPlanItemCode());
        }
        return Result.success();
    }

    private void progressToSystem(ProjThirdInterface param, Integer flag, String comments) {
        comments = comments != null && !comments.isEmpty() ? comments : "-";
        ProjHospitalInfo projHospitalInfo = projHospitalInfoMapper.selectById(param.getHospitalInfoId());
        Long systemIdOne = 1000012L;
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(projHospitalInfo);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        WorkflowInstanceProcessParam dto = new WorkflowInstanceProcessParam();
        dto.setHospitalId(projHospitalInfo.getCloudHospitalId());
        dto.setHisOrgId(projHospitalInfo.getOrgId());
        // 	审批状态 REJECTED 驳回，STOPED 终止，AGREE 同意
        switch (flag) {
            case 1:
            case 5:
                dto.setStatus("AGREE");
                break;
            default:
                dto.setStatus("REJECTED");
                break;
        }
        dto.setInstanceId(param.getInstanceId());
        dto.setDeptId(systemIdOne);
        dto.setIdentityId(systemIdOne);
        dto.setUserId(systemIdOne);
        dto.setOpinion(comments);
        ResponseResult<WorkflowInstanceVO> result = systemSettingWorkflowApi.process(dto);
        if (result.isSuccess()) {
            log.info("调用系统流程成功:{}", result);
        } else {
            log.error("调用系统流程失败:{}", result);
            throw new CustomException("调用系统流程失败");
        }
    }

    @NotNull
    private ProjInterfaceGroupApplyDetail getProjInterfaceGroupApplyDetail(
            Long groupId, String groupName, ApiInterfaceResp apiInterfaceResp, ProjThirdInterface thirdInterface) {
        ProjInterfaceGroupApplyDetail applyDetail = new ProjInterfaceGroupApplyDetail();
        applyDetail.setInterfaceGroupApplyDetailId(SnowFlakeUtil.getId());
        applyDetail.setPfInterfaceGroupId(groupId);
        applyDetail.setPfInterfaceGroupName(groupName);
        applyDetail.setPfInterfaceId(apiInterfaceResp.getId());
        applyDetail.setPfInterfaceName(apiInterfaceResp.getInterfaceName());
        applyDetail.setThirdInterfaceId(thirdInterface.getThirdInterfaceId());
        applyDetail.setEnvironment(0);
        applyDetail.setAuthFlag(0);
        applyDetail.setIsDeleted(0);
        applyDetail.setCreaterId(userHelper.getCurrentUser().getSysUserId());
        applyDetail.setCreateTime(new Date());
        applyDetail.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
        applyDetail.setUpdateTime(new Date());
        return applyDetail;
    }

    /**
     * 接口裁定、授权时 参数信息组装。接口开发平台参数
     *
     * @param param
     * @param customInfo
     * @param isTestEnv
     * @param groupIds
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public GroupApplyVO getInsertDemandReq(ProjThirdInterface param, ProjCustomInfo customInfo,
                                           Integer isTestEnv, List<String> groupIds) {
        // 接口基本信息组装
        // 查询申请人信息
        SysUser user = sysUserMapper.selectById(param.getCreaterId());
        InsertDemandReq insertDemandReq = new InsertDemandReq();
        DemandDto demandDto = new DemandDto();
        demandDto.setIsHistory(param.getHistoryFlag().toString());
        demandDto.setDemandId(param.getThirdInterfaceId().toString());
        demandDto.setDemandName(param.getDictInterfaceName());
        demandDto.setDevelopMode(param.getImplementsType());
        demandDto.setDemandDescription(param.getBusinessDesc());
        demandDto.setAuthor(ObjectUtil.isNotEmpty(user) ? user.getUserName() : "历史数据迁移");
        demandDto.setSubmitTime(DateUtil.formatDateTime(new Date()));
        demandDto.setDemandState(0);
        demandDto.setDataType(ObjectUtil.isNotEmpty(param.getDataSetId())
                ? param.getDataSetId().toString() : "-1");
        demandDto.setDataTypeName(ObjectUtil.isNotEmpty(param.getDataSetName()) ? param.getDataSetName() : "交互类接口");
        List<ProjHospitalVsProjectType> hospitalVsProjectTypes = hospitalVsProjectTypeMapper.selectByCustomAndType(
                param.getCustomInfoId(), param.getProjectType());
        List<ProjHospitalInfo> hospitalInfoList = hospitalInfoMapper.selectBatchIds(
                hospitalVsProjectTypes.stream().map(ProjHospitalVsProjectType::getHospitalInfoId)
                        .distinct().collect(Collectors.toList()));
        List<HospitalReq> hospitalReqList = hospitalInfoList.stream().map(hospitalInfo -> {
            HospitalReq hospitalReq = new HospitalReq();
            hospitalReq.setHospitalId(ObjectUtil.isNotEmpty(hospitalInfo.getCloudHospitalId())
                    ? hospitalInfo.getCloudHospitalId().toString() : null);
            hospitalReq.setHospitalName(hospitalInfo.getHospitalName());
            hospitalReq.setHospitalHost(ObjectUtil.isNotEmpty(hospitalInfo.getCloudDomain())
                    ? hospitalInfo.getCloudDomain().replace("https://", "").replace("http://", "") : null);
            hospitalReq.setHospitalNetwork(hospitalInfo.getCloudDomain());
            Integer envStatus = hospitalInfo.getHospitalOpenStatus() == 21 ? 1 : 0;
            hospitalReq.setEnvStatus(envStatus.toString());
            return hospitalReq;
        }).collect(Collectors.toList());
        demandDto.setHospitalList(JSONObject.toJSONString(hospitalReqList));
        demandDto.setCustomerId(customInfo.getYyCustomerId().intValue());
        demandDto.setCustomerName(customInfo.getCustomName());
        List<ProjInterfaceVsAuthor> autherList = Lists.newArrayList();
        //测试环境
        if (isTestEnv == 1) {
            demandDto.setOrgId(authTestOrgId.intValue());
            autherList = interfaceVsAuthorMapper.selectList(new QueryWrapper<ProjInterfaceVsAuthor>()
                    .eq("third_interface_id", param.getThirdInterfaceId())
                    .eq("environment", 0)
                    .isNotNull("pf_app_id"));
        } else {
            demandDto.setOrgId(hospitalInfoList.get(0).getOrgId().intValue());
            autherList = interfaceVsAuthorMapper.selectList(new QueryWrapper<ProjInterfaceVsAuthor>()
                    .eq("third_interface_id", param.getThirdInterfaceId())
                    .eq("environment", 1)
                    .isNotNull("pf_app_id"));
        }
        if (ObjectUtil.isNotEmpty(param.getThirdInterfaceFiles()) && "".equals(param.getThirdInterfaceFiles())) {
            String[] fileIdArr = param.getThirdInterfaceFiles().split(",");
            ProjProjectFile projectFile = projectFileMapper.selectByPrimaryKey(Long.valueOf(fileIdArr[0]));
            int expireTime = 604800 * 50;
            demandDto.setAttachedFileUrl(OBSClientUtils.getTemporaryUrl(projectFile.getFilePath(), expireTime));
        }
        //查询是否已经授权过
        if (CollectionUtil.isNotEmpty(autherList)) {
            demandDto.setAppId(autherList.get(0).getPfAppId());
            demandDto.setAppSecret(autherList.get(0).getPfPrivateKey());
        }
        // 创建应用信息组装
        CreateAppDTO createAppDTO = new CreateAppDTO();
        String appName = StringUtils.joinWith(StrUtil.DASHED, customInfo.getCustomName(),
                param.getDictInterfaceFirmName(), param.getDictInterfaceName());
        createAppDTO.setAppName(appName);
        createAppDTO.setDescription(param.getBusinessDesc());
        createAppDTO.setCustomerName(customInfo.getCustomName());
        createAppDTO.setApplicant(ObjectUtil.isNotEmpty(user) ? user.getUserName() : "历史数据迁移");
        createAppDTO.setInterfaceType(param.getInterfaceType().toString());
        //测试环境
        if (isTestEnv == 1) {
            List<String> orgIds = Lists.newArrayList();
            orgIds.add(authTestOrgId.toString());
            createAppDTO.setOrgIds(orgIds);
            List<String> cloudHospitalIds =
                    authTestHospitalIds.stream().map(vo -> Convert.toStr(vo)).collect(Collectors.toList());
            createAppDTO.setHospitalIds(cloudHospitalIds);
        } else {
            List<String> orgIds =
                    hospitalInfoList.stream().map(vo -> Convert.toStr(vo.getOrgId())).collect(Collectors.toList());
            createAppDTO.setOrgIds(orgIds);
            List<String> cloudHospitalIds =
                    hospitalInfoList.stream().map(vo -> Convert.toStr(vo.getCloudHospitalId())).collect(Collectors.toList());
            createAppDTO.setHospitalIds(cloudHospitalIds);
        }
        createAppDTO.setEnv(authEnv);
        insertDemandReq.setCreateAppDTO(createAppDTO);
        // 申请授权信息组装
        AppGroupApplyDTO appGroupApplyDTO = new AppGroupApplyDTO();
        // 查询当前
        appGroupApplyDTO.setGroupIds(groupIds);
        appGroupApplyDTO.setIsTestEnv(isTestEnv);
        insertDemandReq.setAppGroupApplyDTO(appGroupApplyDTO);
        insertDemandReq.setDemandDto(demandDto);
        log.info("调用接口开发平台参数信息组装 =========== , {}", JSONUtil.toJsonStr(insertDemandReq));
        cn.hutool.json.JSONObject object = thirdPlatformFeignClient.insertDemand(insertDemandReq);
        GroupApplyVO groupApplyVO = new GroupApplyVO();
        // 接收返回信息。更新授权数据
        if (0 != object.getInt("code")) {
            log.error("接口申请授权失败,{}", object.get("message"));
            groupApplyVO.setSuccess(false);
            groupApplyVO.setMsg(object.getStr("message"));
            return groupApplyVO;
            //throw new RuntimeException("接口申请授权失败," + object.get("message"));
        } else {
            // 将JSON字符串转换为GroupApplyVO对象
            try {
                Map<String, String> data = (Map<String, String>) object.get("data");
                groupApplyVO.setSuccess(true);
                groupApplyVO.setApplyId(Convert.toLong(data.get("applyId")));
                groupApplyVO.setAppId(Convert.toStr(data.get("appId")));
                groupApplyVO.setAppSecret(Convert.toStr(data.get("appSecret")));
                return groupApplyVO;
            } catch (Exception e) {
                log.error("上传类接口申请授权失败，数据转换异常");
                throw new RuntimeException("上传类接口申请授权失败，数据转换异常");
            }

        }
    }

    /**
     * 接口-授权
     *
     * @param thirdInterfaceIds
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result authInterFace(List<Long> thirdInterfaceIds) {
        // 判断接口是否已经测试通过
        List<ProjThirdInterface> projThirdInterfaces = projThirdInterfaceMapper.selectList(new QueryWrapper<ProjThirdInterface>()
                .notIn("status", Arrays.asList(24))
                .in("third_interface_id", thirdInterfaceIds)
                .in("interface_type", 1)
        );
        if (CollectionUtil.isNotEmpty(projThirdInterfaces)) {
            return Result.fail("交互类接口未在测试环境测试完成，请前往授权功能进行检测完成情况");
        }
        List<ProjThirdInterface> interfaceList = projThirdInterfaceMapper.selectBatchIds(thirdInterfaceIds);
        //判断是否已经部署云资源-医院信息是否已经有hospitalId、domain
        Long cId = interfaceList.get(0).getCustomInfoId();
        Long pId = interfaceList.get(0).getProjectInfoId();
        //pId != -1表示是从运维平台添加的接口，无需校验是否部署了云健康
        if (pId != -1) {
            List<HospitalOnlineInfo> hospitalInfos = hospitalInfoMapper.selectHospitalOnlineDetail(cId, pId);
            if (hospitalInfos.size() == 0
                    || hospitalInfos.stream().filter(i -> i.getHospitalOpenStatus() < 21).count() > 0) {
                return Result.fail("请先部署云资源！");
            }
        }
        //接口类型【0：上传类；1：交互类】  上传类不需要测试
        interfaceList.stream().forEach(item -> {
            // 查询当前接口的 客户信息
            ProjCustomInfo customInfo = customInfoMapper.selectById(item.getCustomInfoId());
            // 判断接口接口类型 0: 上传类（无接口分组数据） ；1：交互类
            // 调用接口开发平台 申请授权
            List<String> groupIds = new ArrayList<>();
            if (item.getInterfaceType() == 1) {
                // 测试环境的分组数据重新保存为一份正式的数据。 并进行申请
                List<ProjInterfaceGroupApplyDetail> interfaceGroupApplyDetailList = interfaceGroupApplyDetailMapper.selectList(
                        new QueryWrapper<ProjInterfaceGroupApplyDetail>()
                                .eq("environment", 0)
                                .eq("third_interface_id", item.getThirdInterfaceId())
                );
                interfaceGroupApplyDetailList.stream().forEach(po -> {
                    // 删除原数据 可能存在多次申请导致的 存在多个数据
                    interfaceGroupApplyDetailMapper.delete(new QueryWrapper<ProjInterfaceGroupApplyDetail>()
                            .eq("third_interface_id", po.getThirdInterfaceId())
                            .eq("environment", 1)
                    );
                    po.setInterfaceGroupApplyDetailId(SnowFlakeUtil.getId());
                    po.setEnvironment(1);
                    interfaceGroupApplyDetailMapper.insert(po);
                });
                groupIds =
                        interfaceGroupApplyDetailList.stream().map(vo -> Convert.toStr(vo.getPfInterfaceGroupId())).distinct().collect(Collectors.toList());
            } else {
                groupIds = Arrays.asList("6887381997348458376");
            }
            // 申请授权
            GroupApplyVO groupApplyVO = getInsertDemandReq(item, customInfo, 0, groupIds);
            if (!groupApplyVO.getSuccess()) {
                throw new RuntimeException("接口申请授权失败," + groupApplyVO.getMsg());
            }
            // 重新创建一条 申请正式环境的数据
            ProjInterfaceVsAuthor interfaceVsAuthor = new ProjInterfaceVsAuthor();
            interfaceVsAuthor.setPfAppId(groupApplyVO.getAppId());
            interfaceVsAuthor.setPfPrivateKey(groupApplyVO.getAppSecret());
            interfaceVsAuthor.setPfAuthApplyId(groupApplyVO.getApplyId());
            interfaceVsAuthor.setInterfaceVsAuthorId(SnowFlakeUtil.getId());
            interfaceVsAuthor.setThirdInterfaceId(item.getThirdInterfaceId());
            interfaceVsAuthor.setEnvironment(1);
            interfaceVsAuthor.setFirmName(item.getDictInterfaceFirmName());
            interfaceVsAuthorMapper.delete(new QueryWrapper<ProjInterfaceVsAuthor>()
                    .eq("third_interface_id", interfaceVsAuthor.getThirdInterfaceId())
                    .eq("environment", 1)
            );
            interfaceVsAuthorMapper.insert(interfaceVsAuthor);
            // 增加正式环境申请授权时的 接口日志
            interfaceRecordLogService.saveInterfaceRecordLog("申请正式环境授权", item.getThirdInterfaceId(), null);
        });
        List<Long> ids = interfaceList.stream().map(ProjThirdInterface::getThirdInterfaceId)
                .collect(Collectors.toList());
        projThirdInterfaceMapper.updateStatusByIds(ids, 32);
        try {
            ProjThirdInterface projThirdInterface1 = projThirdInterfaceMapper.selectById(ids.get(0));
            SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
            param.setProjectInfoId(projThirdInterface1.getProjectInfoId());
            param.setHospitalInfoId(projThirdInterface1.getHospitalInfoId());
            param.setUserId(projThirdInterface1.getDirPersonId());
            param.setCode(DictProjectPlanItemEnum.SCHEDULE_THIRD_PART.getPlanItemCode());
            todoTaskService.updateProjectTodoTaskStatus(param);
        } catch (Exception e) {
            log.error("三方接口授权更新待办，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        return Result.success("申请授权成功");
    }

    /**
     * 测试环境检测是否通过
     *
     * @param item
     * @return
     */
    @Override
    public Result<CheckAppVO> checkApp(GetInterfaceApplyDetailReq item) {
        CheckAppVO checkAppVO = new CheckAppVO();
        // 判断当前接口的状态，当前接口状态在测试阶段时候 才进行检测。当不属于测试阶段不进行检测
        ProjThirdInterface thirdInterface = projThirdInterfaceMapper.selectById(item.getThirdInterfaceId());
        String accessToken = interfaceVsAuthorService.getAccessToken();
        CheckAppReq checkAppReq = new CheckAppReq();
        //判断业务类型，如果是上传类，直接查询正式环境授权信息
        Integer environment = item.getEnvironment();
        /*Integer environment = 0;
        if (thirdInterface.getInterfaceType() == 0) {
            environment = 1;
        }*/
        ProjInterfaceVsAuthor auth = interfaceVsAuthorMapper.selectByThirdInterfaceIdAndEnv(
                item.getThirdInterfaceId(), environment);
        if (ObjectUtil.isEmpty(auth)) {
            return Result.fail("未获取到接口授权信息！");
        }
        checkAppReq.setAppId(auth.getPfAppId());
        String respStr = openApiPlatformFeignClient.checkApp(accessToken, platformUser, checkAppReq);
        log.info("======检测是否通过返回值======{}", respStr);
        ResponseResult checkResp = JSON.parseObject(respStr, ResponseResult.class);
        if (!checkResp.isSuccess()) {
            return Result.fail("检测是否通过异常:" + checkResp.getMessage());
        }
        JSONObject data = JSONObject.parseObject(checkResp.getData().toString());
        if ((Boolean) data.get("checkResult")) {
            if (thirdInterface.getStatus() == 22 || thirdInterface.getStatus() == 23 || thirdInterface.getStatus() == 24) {
                // 当测试完成时，更新接口业务数据的状态值 【只有在测试环境的接口状态才进行更新接口状态】
                ProjThirdInterface projThirdInterface = new ProjThirdInterface();
                projThirdInterface.setThirdInterfaceId(item.getThirdInterfaceId());
                projThirdInterface.setStatus(24);
                projThirdInterfaceMapper.updateById(projThirdInterface);
            }
            // 检测明细数据
            List<CheckAppDetailVO> checkAppDetailVOS = JSONObject.parseArray(JSONObject.toJSONString(data.get("serviceVOList")),
                    CheckAppDetailVO.class);
            checkAppVO.setDetailVOList(checkAppDetailVOS);
            checkAppVO.setTestSuccess(true);
        } else {
            checkAppVO.setDescription(data.get("description").toString());
            checkAppVO.setTestSuccess(false);
            // 检测明细数据
            List<CheckAppDetailVO> checkAppDetailVOS = JSONObject.parseArray(JSONObject.toJSONString(data.get(
                    "serviceVOList")), CheckAppDetailVO.class);
            checkAppVO.setDetailVOList(checkAppDetailVOS);
        }
        checkAppVO.setGroupDoc(data.getString("groupDoc"));
        return Result.success(checkAppVO);

    }

    /**
     * 三方接口调研阶段完成
     *
     * @param milestoneInfoId
     * @return
     */
    @Override
    public Result thirdInterfaceSurveyFinish(Long milestoneInfoId) {
        // 查询里程碑节点信息
        Result<ProjMilestoneInfo> projMilestoneInfoResult = milestoneInfoService.selectById(milestoneInfoId);
        if (projMilestoneInfoResult.getData().getMilestoneStatus() == 1) {
            return Result.fail("当前节点已完成，请勿重复点击");
        }
        List<Integer> statusList = Arrays.asList(0, 1, 11);
        List<ProjThirdInterface> projThirdInterfaces = new LambdaQueryChainWrapper<>(projThirdInterfaceMapper)
                .eq(ProjThirdInterface::getProjectInfoId, projMilestoneInfoResult.getData().getProjectInfoId())
                .eq(ProjThirdInterface::getOnlineFlag, 1)
                .in(ProjThirdInterface::getStatus, statusList)
                .list();
        if (CollectionUtil.isNotEmpty(projThirdInterfaces)) {
            return Result.fail("上线必备接口未全部裁定通过，请先完成裁定。");
        }
        UpdateMilestoneDTO updateMilestoneDTO = new UpdateMilestoneDTO();
        updateMilestoneDTO.setMilestoneInfoId(milestoneInfoId);
        updateMilestoneDTO.setMilestoneStatus(1);
        updateMilestoneDTO.setActualCompTime(new Date());
        updateMilestoneDTO.setNodeHeadId(userHelper.getCurrentUser().getSysUserId());
        milestoneInfoService.updateMilestone(updateMilestoneDTO);
        return Result.success();
    }

    @Override
    public Result<ThirdInterfaceEntryCheckVO> thirdInterfaceEntryCheck(Long projectInfoId) {
        ThirdInterfaceEntryCheckVO thirdInterfaceEntryCheckVO = new ThirdInterfaceEntryCheckVO();
        // 上线必备接口必须测试环境测试通过，哪些接口未测试完成，请尽快完成测试环境测试。否则将影响准备阶段的正式环境授权。
        // 设置哪些状态需要 检测 【实现方式为 三方对接、项目组对接的接口，必须要测试完成及以后状态】
        List<ProjThirdInterface> projThirdInterfaces = projThirdInterfaceMapper.selectList(
                new QueryWrapper<ProjThirdInterface>()
                        .eq("project_info_id", projectInfoId)
                        .eq("online_flag", 1)
                        .eq("interface_type", 1)
                        .notIn("implements_type", Arrays.asList(1, 2))
                        .gt("status", 24)
        );
        if (CollectionUtil.isNotEmpty(projThirdInterfaces)) {
            StringBuilder sb = new StringBuilder();
            for (ProjThirdInterface item : projThirdInterfaces) {
                sb.append(item.getDictInterfaceName() + ",");
            }
            String str = sb.substring(0, sb.length() - 1);
            thirdInterfaceEntryCheckVO.setIsSatisfy(false);
            thirdInterfaceEntryCheckVO.setMsg("以下接口未测试通过，请尽快在测试环境测试通过。否则将影响准备阶段的正式环境授权。接口明细：【" + str + "】");
        } else {
            thirdInterfaceEntryCheckVO.setIsSatisfy(true);
        }
        return Result.success(thirdInterfaceEntryCheckVO);
    }

    /**
     * 三方接口准备阶段完成
     *
     * @param milestoneInfoId
     * @return
     */
    @Override
    public Result thirdInterfacePrepareFinish(Long milestoneInfoId) {
        // 查询里程碑节点信息
        Result<ProjMilestoneInfo> projMilestoneInfoResult = milestoneInfoService.selectById(milestoneInfoId);
        if (projMilestoneInfoResult.getData().getMilestoneStatus() == 1) {
            return Result.fail("当前节点已完成，请勿重复点击");
        }
        // 上线必备的接口要求，除了产品对接的接口。三方对接、项目组对接的 必须是接口完成才能提交节点完成
        // 【10.22 张吉宝】：要求正式环境授权通过后，就可以完成准备节点。不再有接口完成状态
        // 1、上线必备的接口必须完成
        List<ProjThirdInterface> projThirdInterfaces = projThirdInterfaceMapper.selectList(
                new QueryWrapper<ProjThirdInterface>()
                        .eq("project_info_id", projMilestoneInfoResult.getData().getProjectInfoId())
                        .eq("online_flag", 1)
                        .in("interface_type", Arrays.asList(1, 0))
                        .in("implements_type", Arrays.asList(0, 2))
                        .lt("status", 32)
        );
        // 埋点类接口，审核通过即为完成
        List<ProjThirdInterface> projThirdInterfacesMaiDian = projThirdInterfaceMapper.selectList(new QueryWrapper<ProjThirdInterface>().eq("project_info_id", projMilestoneInfoResult.getData().getProjectInfoId()).eq("online_flag", 1).eq("interface_type", 2).ne("status", 13));
        if (CollectionUtil.isNotEmpty(projThirdInterfaces) || CollectionUtil.isNotEmpty(projThirdInterfacesMaiDian)) {
            return Result.fail("上线必备的三方对接、项目组对接的接口未全部在正式环境授权通过，请检查");
        }
        UpdateMilestoneDTO updateMilestoneDTO = new UpdateMilestoneDTO();
        updateMilestoneDTO.setMilestoneInfoId(milestoneInfoId);
        updateMilestoneDTO.setMilestoneStatus(1);
        updateMilestoneDTO.setActualCompTime(new Date());
        updateMilestoneDTO.setNodeHeadId(userHelper.getCurrentUser().getSysUserId());
        milestoneInfoService.updateMilestone(updateMilestoneDTO);
        return Result.success();
    }

    /**
     * 接口分组授权信息组装
     *
     * @param param
     * @param appInfo
     * @param env
     * @return
     */
    @NotNull
    private ProjInterfaceVsAuthor getProjInterfaceVsAuthor(ProjThirdInterface param, AppInfoResp appInfo, Integer env) {
        ProjInterfaceVsAuthor interfaceVsAuthor = new ProjInterfaceVsAuthor();
        interfaceVsAuthor.setThirdInterfaceId(param.getThirdInterfaceId());
        interfaceVsAuthor.setPfAppId(appInfo.getAppKey());
        interfaceVsAuthor.setPfPublicKey(appInfo.getPublicKey());
        interfaceVsAuthor.setPfPrivateKey(appInfo.getPrivateKey());
        interfaceVsAuthor.setFirmName(appInfo.getAppName());
        interfaceVsAuthor.setEnvironment(env);
        return interfaceVsAuthor;
    }

    /**
     * 获取密钥
     *
     * @param appName
     * @param environment 0：测试环境；1：正式环境
     * @return
     */
    private AppInfoResp registerAndGetInfo(String appName, Integer environment, Long orgId, ProjThirdInterface param) {
        //获取密钥
//        RegisterAppReq registerAppReq = getRegisterAppReq(appName, environment, orgId);
//        String accessToken = interfaceVsAuthorService.getAccessToken();
//        String appKeyStr = openApiPlatformFeignClient.registerApp(accessToken, platformUser, registerAppReq);
        CreateAppAuthorizationDTO createAppAuthorizationDTO = new CreateAppAuthorizationDTO();
        createAppAuthorizationDTO.setAppName(appName);
        createAppAuthorizationDTO.setAppDescription(param.getBusinessDesc());
        // 查询客户信息
        ProjCustomInfo customInfo = customInfoMapper.selectById(param.getCustomInfoId());
        createAppAuthorizationDTO.setCustomerName(customInfo.getCustomName());
        // 查询申请人信息
        SysUser user = sysUserMapper.selectById(param.getCreaterId());
        createAppAuthorizationDTO.setApplicant(user.getUserName());
        createAppAuthorizationDTO.setAppScenario(param.getInterfaceType() == 0 ? "2" : "1");
        createAppAuthorizationDTO.setOrgIds(getRegisterAppReq(environment, orgId));
        Result<CreateAppAuthorizationReesult> appAuthorization = createAppAuthorization(createAppAuthorizationDTO);
        log.info("======获取appKey返回值======{}", appAuthorization);
        if (!appAuthorization.isSuccess()) {
            throw new CustomException(appAuthorization.getMsg());
        }
        String appKey = appAuthorization.getData().getObj().getAppId();
        log.info("======获取appKey======{}", appKey);
        //查询appInfo
        String accessToken = interfaceVsAuthorService.getAccessToken();
        String appInfoStr = openApiPlatformFeignClient.getAppInfoByKey(accessToken, platformUser, appKey);
        ResponseResult appInfoResp = JSON.parseObject(appInfoStr, ResponseResult.class);
        if (!appInfoResp.isSuccess()) {
            throw new CustomException("获取密钥失败: " + appInfoResp.getMessage());
        }
        log.info("======获取密钥结果======{}", JSON.toJSONString(appInfoResp));
        return JSONObject.parseObject(JSONObject.toJSONString(appInfoResp.getData()), AppInfoResp.class);
    }

    /**
     * 获取注册参数
     *
     * @param environment
     * @param orgId
     * @return
     */
    @NotNull
    private List<Long> getRegisterAppReq(Integer environment, Long orgId) {
        //测试环境固定机构-10017
        if (environment == 0) {
            return Arrays.asList(authTestOrgId);
        } else {
            if (orgId == null) {
                throw new CustomException("请选择机构");
            }
            return Arrays.asList(orgId);
        }
    }

    /**
     * 获取密钥
     *
     * @param ti
     * @param environment 0：测试环境；1：正式环境
     * @return
     */
    private void authByGroups(ProjThirdInterface ti, Integer environment) {
        //拼接参数
        final String desc = "交付平台分组授权申请";
        ApplyAuthByGroupsReq authByGroupsReq = new ApplyAuthByGroupsReq();
        authByGroupsReq.setApplyUser(userHelper.getCurrentUser().getUserName());
        authByGroupsReq.setEnv(authEnv);
        authByGroupsReq.setDescription(desc);
        // 当接口属于交互类时 。 进行赋值数据操作
        if (ti.getInterfaceType() == 1) {
            // 复制测试环境的授权分组数据到正式环境分组数据
            List<ProjInterfaceGroupApplyDetail> testEnvGroupList = interfaceGroupApplyDetailMapper.selectByThirdIdAndEnv(
                    ti.getThirdInterfaceId(), 0);
            for (ProjInterfaceGroupApplyDetail detail : testEnvGroupList) {
                detail.setInterfaceGroupApplyDetailId(SnowFlakeUtil.getId());
                detail.setEnvironment(1);
                interfaceGroupApplyDetailMapper.insert(detail);
            }
            List<ProjInterfaceGroupApplyDetail> applyDetailList = interfaceGroupApplyDetailMapper.selectByThirdIdAndEnv(
                    ti.getThirdInterfaceId(), environment);
            authByGroupsReq.setGroupIds(applyDetailList.stream().map(e -> e.getPfInterfaceGroupId())
                    .collect(Collectors.toSet()).stream().collect(Collectors.toList()));
        } else {
            // 上传类接口 固定授权的分组数据
            authByGroupsReq.setGroupIds(Arrays.asList(6887381997348458376L));
        }
        // 查询接口类型
        ProjThirdInterface thirdInterface = projThirdInterfaceMapper.selectById(ti.getThirdInterfaceId());
        authByGroupsReq.setInterfaceType(thirdInterface.getInterfaceType());
        ProjInterfaceVsAuthor interfaceVsAuthor = interfaceVsAuthorMapper.selectByThirdInterfaceIdAndEnv(
                ti.getThirdInterfaceId(), environment);
        authByGroupsReq.setAppKey(interfaceVsAuthor.getPfAppId());
        if (environment == 0) {
            //测试环境
            authByGroupsReq.setHospitalIds(authTestHospitalIds);
            authByGroupsReq.setIsTestEnv(1);
        } else {
            List<ProjHospitalVsProjectType> hospitalVsProjectTypes = hospitalVsProjectTypeMapper.selectByCustomAndType(
                    ti.getCustomInfoId(), ti.getProjectType());
            authByGroupsReq.setHospitalIds(hospitalVsProjectTypes.stream().map(e -> e.getHospitalInfoId())
                    .collect(Collectors.toSet()).stream().collect(Collectors.toList()));
            authByGroupsReq.setIsTestEnv(0);
        }
        String accessToken = interfaceVsAuthorService.getAccessToken();
        log.info("======获取授权参数信息======{}", JSON.toJSONString(authByGroupsReq));
        String authStr = openApiPlatformFeignClient.applyAuthByGroups(accessToken, platformUser, authByGroupsReq);
        ResponseResult authResp = JSON.parseObject(authStr, ResponseResult.class);
        log.info("======获取授权结果返回值======{}", JSON.toJSONString(authResp));
        if (!authResp.isSuccess()) {
            throw new CustomException("获取授权失败: " + authResp.getMessage());
        }
        //修改数据库数据proj_interface_vs_author 【 测试环境自动审核通过。直接修改授权状态】
        interfaceVsAuthor.setPfAuthApplyId(Convert.toLong(authResp.getData()));
        interfaceVsAuthorMapper.updateAuthApplyId(interfaceVsAuthor);
        if (environment == 0) {
            // 更新分组授权表数据
            ProjInterfaceGroupApplyDetail projInterfaceGroupApplyDetail = new ProjInterfaceGroupApplyDetail();
            projInterfaceGroupApplyDetail.setAuthFlag(1);
            projInterfaceGroupApplyDetail.setAuthTime(new Date());
            interfaceGroupApplyDetailMapper.update(projInterfaceGroupApplyDetail, new UpdateWrapper<ProjInterfaceGroupApplyDetail>()
                    .eq("third_interface_id", ti.getThirdInterfaceId())
                    .eq("environment", environment)
            );
        }
        log.info("======授权成功======接口数据-{}, 授权信息-{}", ti.getThirdInterfaceId(),
                authResp.getData().toString());
    }

    /**
     * 转接口分公司接口参数组装
     *
     * @param dto
     * @return
     */
    private InterfaceMainDto intoInterfaceMain(ProjThirdInterfaceDTO dto) {
        //根据三方接口id查询 接口明细信息
        ProjThirdInterface projThirdInterface = projThirdInterfaceMapper.selectById(dto.getThirdInterfaceId());
        // 查询客户信息、项目信息
        ProjCustomInfo projCustomInfo = customInfoMapper.selectById(projThirdInterface.getCustomInfoId());
        ProjProjectInfo projProjectInfo = projectInfoMapper.selectById(projThirdInterface.getProjectInfoId());
        SysDept sysDept = sysDeptMapper.selectById(dto.getDirTeamId());
        //接口分公司方向时 ， 期望 时间赋值
        projThirdInterface.getExpectTime().setHours(23);
        projThirdInterface.getExpectTime().setMinutes(59);
        projThirdInterface.getExpectTime().setSeconds(59);
        InterfaceMainDto interfaceMainDto = new InterfaceMainDto();
        interfaceMainDto.setRequestId(projThirdInterface.getThirdInterfaceId());
        interfaceMainDto.setOrderDesc(projProjectInfo.getProjectName() + projThirdInterface.getDictInterfaceName());
        interfaceMainDto.setApplyUserId(Convert.toLong(userHelper.getCurrentUser().getUserYunyingId()));
        interfaceMainDto.setApplyOrgId(Convert.toLong(userHelper.getCurrentUser().getDeptId()));
        interfaceMainDto.setApplyDate(new Date());
        interfaceMainDto.setCustomerId(projCustomInfo.getYyCustomerId());
        interfaceMainDto.setImplOrgId(sysDept.getDeptYunyingId());
        interfaceMainDto.setUrgStatus(1);
        interfaceMainDto.setProductId(projThirdInterface.getYyProductId());
        interfaceMainDto.setInterfaceFunc(projThirdInterface.getBusinessDesc());
        interfaceMainDto.setExpectDate(projThirdInterface.getExpectTime());
        interfaceMainDto.setItemType(2);
        try {
            if (ObjectUtil.isNotEmpty(projThirdInterface.getThirdInterfaceFiles())) {
                ProjProjectFile fileInfo = getFileInfo(projThirdInterface.getThirdInterfaceFiles());
                String fileBase64 = urlBase64(fileInfo.getFilePath());
                interfaceMainDto.setAttachmentFileName(projThirdInterface.getDictInterfaceFirmName() + "_接口文档_" + fileInfo.getFileName());
                interfaceMainDto.setAttachmentFileBase64(fileBase64);
            }
        } catch (Exception e) {
            throw new RuntimeException("接口文档转base64失败");
        }
        log.info("======三方接口转接口分公司参数信息====== , dtoData = {}", JSONUtil.toJsonStr(interfaceMainDto));
        return interfaceMainDto;
    }

    /**
     * @return java.lang.String
     * <AUTHOR>
     * @Description //文件转base64
     * @Date 2023/9/5 17:59
     * @Param [url]
     */
    private String urlBase64(String url) throws IOException {
        /*byte[] bb = IOUtils.toByteArray(new FileInputStream(url));
        String g = new String(Base64.getUrlEncoder().encode(bb), StandardCharsets.UTF_8);
        log.info("接口文件 Base64 ====={}", g);
        return g;*/
        //20241112,之前的转base64方法报错，修改为以下方法
        URL fileUrl = new URL(url);
        InputStream inputStream = fileUrl.openStream();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            outputStream.write(buffer, 0, bytesRead);
        }
        byte[] imageBytes = outputStream.toByteArray();
        String base64String = new String(Base64.getUrlEncoder().encode(imageBytes), StandardCharsets.UTF_8);
        inputStream.close();
        outputStream.close();
        return base64String;
    }

    /**
     * 导入三方接口信息
     *
     * @param file
     * @param customInfoId
     * @param projectType
     * @param projectInfoId
     * @return
     */
    @Override
    public Result excelImportThirdInterface(MultipartFile file, Long customInfoId, Integer projectType,
                                            Long projectInfoId) {
        // 确保上传的是Excel文件
        if (!file.getOriginalFilename().endsWith(".xlsx")) {
            throw new IllegalArgumentException("请检查导入的文件类型");
        }
        try {
            EasyExcelData easyExcelData =
                    EasyExcelUtil.readExcelWithModel(file.getInputStream(), ThirdInterfaceImportDTO.class);
            log.info("excel数据: {}", JSONUtil.toJsonStr(easyExcelData));
            if (easyExcelData.getDatas() instanceof List<?>) {
                if (easyExcelData.getDatas().isEmpty()) {
                    return Result.fail("请检查导入文件是否存在数据");
                }
            }
            log.info("三方接口数据-导入的Excel数据为 ：, {}", JSONUtil.toJsonStr(easyExcelData.getDatas()));
            // 三方接口数据处理
            Result result = saveThirdInterfacePoInfo(easyExcelData.getDatas(), customInfoId, projectType,
                    projectInfoId);
            todoTaskService.todoTaskTotalCountSync(projectInfoId, DictProjectPlanItemEnum.SURVEY_THIRD_PART.getPlanItemCode());
            todoTaskService.todoTaskTotalCountSync(projectInfoId, DictProjectPlanItemEnum.SCHEDULE_THIRD_PART.getPlanItemCode());
            return result;
        } catch (IOException e) {
            log.error("发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("导入失败");
        }
    }

    /**
     * 三方接口导出Excel
     *
     * @param response
     * @param dto
     */
    @Override
    public void thirdInterfaceExportExcel(HttpServletResponse response, ProjThirdInterfaceDTO dto) {
        String excelName = "";
        //  查询项目下的接口数据
        ProjThirdInterfacePageDTO projThirdInterfacePageDTO = new ProjThirdInterfacePageDTO();
        //项目里程碑进入，前端传项目ID
        if (ObjectUtil.isNotEmpty(dto.getProjectInfoId())) {
            projThirdInterfacePageDTO.setProjectInfoId(dto.getProjectInfoId());
            ProjProjectInfo projProjectInfo = projectInfoMapper.selectById(dto.getProjectInfoId());
            excelName = projProjectInfo.getProjectName();
        }
        //项目工具进入，前端传客户ID
        if (ObjectUtil.isNotEmpty(dto.getCustomInfoId())) {
            projThirdInterfacePageDTO.setCustomInfoId(dto.getCustomInfoId());
            ProjCustomInfo projCustomInfo = customInfoMapper.selectById(dto.getCustomInfoId());
            excelName = projCustomInfo.getCustomName();
        }
        List<ProjThirdInterfaceVO> projThirdInterfaceVOS = projThirdInterfaceMapper.selectThirdInterface(projThirdInterfacePageDTO);
        try (ExcelWriter excelWriter = EasyExcelFactory.write(getOutputStream(
                        excelName + "_接口清单导出.xlsx", response),
                InterfaceExportExcelDTO.class).excludeColumnFieldNames(Collections.singleton("region")).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet(excelName + "_接口清单导出").build();
            // 医院导出信息赋值
            List<InterfaceExportExcelDTO> excelDTOList = new ArrayList<>();
            excelDataInfo(excelDTOList, projThirdInterfaceVOS);
            // 导出文件
            excelWriter.write(excelDTOList, writeSheet);
        } catch (Exception e) {
            log.error("发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
    }

    /**
     * 导出数据拼装
     *
     * @param excelDTOList
     * @param projThirdInterfaceVOS
     */
    void excelDataInfo(List<InterfaceExportExcelDTO> excelDTOList, List<ProjThirdInterfaceVO> projThirdInterfaceVOS) {
        projThirdInterfaceVOS.forEach(item -> {
            InterfaceExportExcelDTO excelDTO = new InterfaceExportExcelDTO();
            excelDTO.setDictInterfaceName(item.getDictInterfaceName());
            excelDTO.setHospitalName(item.getHospitalName());
            excelDTO.setDictInterfaceFirmName(item.getDictInterfaceFirmName());
            excelDTO.setThirdInterfaceVersion(item.getThirdInterfaceVersion());
            excelDTO.setFirmContactName(item.getFirmContactName());
            excelDTO.setFirmContactPhone(item.getFirmContactPhone());
            excelDTO.setOnlineFlag(item.getOnlineFlag() == 1 ? "是" : "否");
            excelDTO.setExpectTime(item.getExpectTime());
            excelDTO.setBusinessDesc(item.getBusinessDesc());
            excelDTO.setProductName(item.getProductName());
            // 专门处理状态
            infoStatusName(excelDTO, item);
            // 实现方式处理
            if (ObjectUtil.isNotEmpty(item.getImplementsType())) {
                switch (item.getImplementsType()) {
                    case 0:
                        excelDTO.setImplementsTypeName("三方接口对接");
                        break;
                    case 1:
                        excelDTO.setImplementsTypeName("产品对接");
                        break;
                    case 2:
                        excelDTO.setImplementsTypeName("项目组对接");
                        break;
                    default:
                        excelDTO.setImplementsTypeName("");
                        break;
                }
            } else {
                excelDTO.setImplementsTypeName("");
            }
            excelDTO.setDirTeamName(item.getDirTeamName());
            excelDTO.setDirPersonName(item.getDirPersonName());
            excelDTO.setDataSetName(item.getDataSetName());
            excelDTO.setInterfaceTypeName(item.getInterfaceType() == 1 ? "交互类" : "上传类");
            excelDTO.setInterfaceSource(item.getInterfaceType() == 2 ? "反馈单" : "项目交付");
            if (item.getInterfaceType() == 0) {
                excelDTO.setDataSetName(item.getDataSetName());
            } else {
                excelDTO.setInterfaceClassName(item.getDictInterfaceName());
            }
            excelDTOList.add(excelDTO);
        });
    }

    /**
     * 接口导出时，状态值的处理
     *
     * @param excelDTO
     * @param item
     */
    void infoStatusName(InterfaceExportExcelDTO excelDTO, ProjThirdInterfaceVO item) {
        /**
         * 接口状态【
         *         初始阶段： 0：未申请；1：提交裁定；
         *         裁定阶段： 11：裁定驳回；12、待评审；13：裁定通过；14：接口分公司驳回
         *         测试阶段： 21、测试环境申请授权；22：测试环境授权通过；23：研发中；24、测试环境测试完成；
         *         正式阶段： 31：申请正式环境授权；32：正式环境授权通过；33：研发完成；【下载SDK操作】
         *         50:接口完成 【提交完成按钮触发：三方对接的检测正式环境测试结果；项目组对接 检测正式环境测试结果与是否下载SDK】
         *      】
         */
        switch (item.getStatus()) {
            case 0:
                excelDTO.setStatusName("未申请裁定");
                break;
            case 1:
                excelDTO.setStatusName("已提交裁定申请");
                break;
            case 11:
                excelDTO.setStatusName("裁定驳回");
                break;
            case 12:
                excelDTO.setStatusName("待评审");
                break;
            case 13:
                excelDTO.setStatusName("裁定通过");
                break;
            case 14:
                excelDTO.setStatusName("接口分公司驳回");
                break;
            case 21:
                excelDTO.setStatusName("测试环境申请授权");
                break;
            case 22:
                excelDTO.setStatusName("测试环境授权通过");
                break;
            case 23:
                excelDTO.setStatusName("研发中");
                break;
            case 24:
                excelDTO.setStatusName("测试环境测试完成");
                break;
            case 31:
                excelDTO.setStatusName("申请正式环境授权");
                break;
            case 32:
                excelDTO.setStatusName("正式环境授权通过");
                break;
            case 33:
                excelDTO.setStatusName("研发完成");
                break;
            case 50:
                excelDTO.setStatusName("接口完成");
                break;
            default:
                excelDTO.setStatusName("");
                break;
        }
    }

    /**
     * 构建输出流
     *
     * @param fileName：文件名称
     * @param response：
     * @return
     * @throws Exception
     */
    private OutputStream getOutputStream(String fileName, HttpServletResponse response) throws Exception {
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");
        // 告知浏览器下载，下载附件的形式
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        return response.getOutputStream();
    }

    /**
     * 三方接口导入数据处理
     *
     * @param datas
     * @param customInfoId
     * @param projectType
     * @param projectInfoId
     * @return
     */
    Result saveThirdInterfacePoInfo(List<Object> datas, Long customInfoId, Integer projectType, Long projectInfoId) {
        try {
            // 数据完整性校验
            Result result = checkImportData(datas);
            if (!result.isSuccess()) {
                return Result.fail(result.getMsg());
            }
            for (Object data : datas) {
                ThirdInterfaceImportDTO dto = (ThirdInterfaceImportDTO) data;
                ProjThirdInterface projThirdInterface = new ProjThirdInterface();
                ProjThirdInterfaceDTO projThirdInterfaceDTO = new ProjThirdInterfaceDTO();
                // 根据选择的医院名称查询医院ID
                ProjHospitalInfo hospitalInfo = hospitalInfoMapper.selectOne(new QueryWrapper<ProjHospitalInfo>().eq("hospital_name", dto.getHospitalName()));
                if (!customInfoId.equals(hospitalInfo.getCustomInfoId())) {
                    return Result.fail("导入失败,项目下不存在《" + dto.getHospitalName() + "》，请核对数据！");
                }
                projThirdInterface.setHospitalInfoId(hospitalInfo.getHospitalInfoId());
                //赋值接口名称
                if (ObjectUtil.isNotEmpty(dto.getDataSetName())) {
                    projThirdInterfaceDTO.setDictInterfaceName(dto.getDataSetName());
                    projThirdInterface.setDictInterfaceName(dto.getDataSetName());
                } else {
                    projThirdInterfaceDTO.setDictInterfaceName(dto.getInterfaceClassName());
                    projThirdInterface.setDictInterfaceName(dto.getInterfaceClassName());
                }
                // 检测接口与厂商是否存在
                projThirdInterfaceDTO.setDictInterfaceFirmName(dto.getDictInterfaceFirmName());
                // 检查时 如果存在会返回，不存在就进行了新增。同样返回新增后的id
                ifHasInterfaceAndFirm(projThirdInterfaceDTO);
                projThirdInterface.setThirdInterfaceId(SnowFlakeUtil.getId());
                projThirdInterface.setCustomInfoId(customInfoId);
                projThirdInterface.setProjectType(projectType);
                projThirdInterface.setProjectInfoId(projectInfoId);
                //查询定价分类id
                DictProduct dictProduct = productMapper.selectOne(new QueryWrapper<DictProduct>()
                        .eq("product_type", "4")
                        .eq("product_name", dto.getProductName())
                );
                projThirdInterface.setYyProductId(dictProduct.getYyProductId());
                //查询数据集
                if (ObjectUtil.isNotEmpty(dto.getDataSetName())) {
                    List<ResDataResp> resDataList = interfaceVsAuthorService.getResDataList();
                    if (CollectionUtil.isNotEmpty(resDataList)) {
                        for (ResDataResp resDataResp : resDataList) {
                            if (resDataResp.getDataClassName().equals(dto.getDataSetName())) {
                                projThirdInterface.setDataSetId(Long.valueOf(resDataResp.getDataClassId()));
                                projThirdInterface.setDataSetName(resDataResp.getDataClassName());
                            }
                        }
                    }
                }
                projThirdInterface.setStatus(0);
                projThirdInterface.setDictInterfaceId(projThirdInterfaceDTO.getDictInterfaceId());
                projThirdInterface.setDictInterfaceFirmId(projThirdInterfaceDTO.getDictInterfaceFirmId());
                projThirdInterface.setDictInterfaceFirmName(dto.getDictInterfaceFirmName());
                projThirdInterface.setInterfaceType("上传类".equals(dto.getInterfaceTypeName()) ? 0 : 1);
                projThirdInterface.setThirdInterfaceVersion(dto.getThirdInterfaceVersion());
                projThirdInterface.setFirmContactName(dto.getFirmContactName());
                projThirdInterface.setFirmContactPhone(dto.getFirmContactPhone());
                projThirdInterface.setOnlineFlag("是".equals(dto.getOnlineFlag()) ? 1 : 0);
                projThirdInterface.setExpectTime(new SimpleDateFormat("yyyy/MM/dd").parse(dto.getExpectTime()));
                projThirdInterface.setBusinessDesc(dto.getBusinessDesc());
                projThirdInterface.setIsDeleted(0);
                projThirdInterface.setCreaterId(userHelper.getCurrentUser().getSysUserId());
                projThirdInterface.setCreateTime(new Date());
                projThirdInterface.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
                projThirdInterface.setUpdateTime(new Date());
                projThirdInterface.setHistoryFlag(0);
                projThirdInterfaceMapper.insert(projThirdInterface);
                //查询接口分类（待处理）
                if (ObjectUtil.isNotEmpty(dto.getInterfaceClassName())) {
                    //  设置三方接口授权数据
                    // 先保存分组授权信息表
                    ProjInterfaceVsAuthor projInterfaceVsAuthor = new ProjInterfaceVsAuthor();
                    projInterfaceVsAuthor.setInterfaceVsAuthorId(SnowFlakeUtil.getId());
                    projInterfaceVsAuthor.setThirdInterfaceId(projThirdInterface.getThirdInterfaceId());
                    projInterfaceVsAuthor.setFirmName(projThirdInterface.getDictInterfaceFirmName());
                    projInterfaceVsAuthor.setEnvironment(0);
                    log.info("三方接口授权信息保存 , {}", JSONUtil.toJsonStr(projInterfaceVsAuthor));
                    interfaceVsAuthorMapper.insert(projInterfaceVsAuthor);
                    // 保存分组授权明细表
                    List<ApiGroupResp> apiGroupList = interfaceVsAuthorService.getApiGroupList();
                    if (CollectionUtil.isNotEmpty(apiGroupList)) {
                        for (ApiGroupResp apiGroupResp : apiGroupList) {
                            if (apiGroupResp.getGroupName().equals(dto.getInterfaceClassName())) {
                                List<ApiInterfaceResp> apiList = interfaceVsAuthorService.getApiList(apiGroupResp.getId());
                                List<ProjInterfaceGroupApplyDetail> detailList = new ArrayList<>();
                                for (ApiInterfaceResp apiInterfaceResp : apiList) {
                                    ProjInterfaceGroupApplyDetail applyDetail = getProjInterfaceGroupApplyDetail(
                                            apiGroupResp.getId(), dto.getInterfaceClassName(), apiInterfaceResp, projThirdInterface);
                                    detailList.add(applyDetail);
                                }
                                interfaceGroupApplyDetailMapper.batchInsert(detailList);
                            }
                        }
                    }
                }
                interfaceRecordLogService.saveInterfaceRecordLog("导入接口", projThirdInterface.getThirdInterfaceId(), null);
            }
            return Result.success("成功导入" + datas.size() + "条数据");
        } catch (Exception e) {
            return Result.fail("导入失败 ：" + e.getMessage());
        }
    }

    /**
     * 检查导入数据完整性
     *
     * @param datas
     */
    Result checkImportData(List<Object> datas) {
        Boolean success = true;
        Boolean flag = false;
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < datas.size(); i++) {
            Object data = datas.get(i);
            ThirdInterfaceImportDTO dto = (ThirdInterfaceImportDTO) data;
            StringBuilder errorMessage = new StringBuilder();
            if (ObjectUtil.isEmpty(dto.getInterfaceTypeName())) {
                flag = true;
                errorMessage.append("业务分类不能为空,");
            }
            if ("上传类".equals(dto.getInterfaceTypeName())) {
                if (ObjectUtil.isEmpty(dto.getDataSetName())) {
                    flag = true;
                    errorMessage.append("上传类接口数据集不能为空,");
                }
            } else {
                if (ObjectUtil.isEmpty(dto.getInterfaceClassName())) {
                    flag = true;
                    errorMessage.append("交互类接口分类不能为空,");
                }
            }
            if (ObjectUtil.isEmpty(dto.getDictInterfaceFirmName())) {
                flag = true;
                errorMessage.append("厂商名称不能为空,");
            }
            if (ObjectUtil.isEmpty(dto.getExpectTime())) {
                flag = true;
                errorMessage.append("期望完成时间不能为空,");
            }
            if (ObjectUtil.isEmpty(dto.getHospitalName())) {
                flag = true;
                errorMessage.append("来源医院不能为空,");
            }
            if (ObjectUtil.isEmpty(dto.getOnlineFlag())) {
                flag = true;
                errorMessage.append("上线必备不能为空,");
            }
            if (ObjectUtil.isEmpty(dto.getBusinessDesc())) {
                flag = true;
                errorMessage.append("业务场景描述不能为空");
            }
            if (flag) {
                String errorStr = errorMessage.substring(0, errorMessage.length() - 1);
                success = false;
                sb.append("第【" + i + "】条接口数据缺失必要信息:" + errorStr + "；\n");
            }
        }
        if (success) {
            return Result.success();
        } else {
            return Result.fail(sb.toString());
        }
    }

    /**
     * 文件信息转换。 列表中的文件id转化成可下载地址
     *
     * @param fileStr
     * @return
     */
    private List<String> getUrlList(String fileStr) {
        List<Long> collect = Arrays.stream(fileStr.split(",")).map(Long::valueOf).collect(Collectors.toList());
        List<ProjProjectFile> projProjectFiles = projectFileMapper.selectList(new QueryWrapper<ProjProjectFile>()
                .in("project_file_id", collect)
        );
        List<String> urlList = new ArrayList<>();
        for (ProjProjectFile projProjectFile : projProjectFiles) {
            urlList.add(OBSClientUtils.getTemporaryUrl(projProjectFile.getFilePath(), 3600));
        }
        return urlList;
    }

    /**
     * 文件信息转换。 列表中的文件id转化成可下载地址
     *
     * @param fileStr
     * @return
     */
    private ProjProjectFile getFileInfo(String fileStr) {
        List<Long> collect = Arrays.stream(fileStr.split(",")).map(Long::valueOf).collect(Collectors.toList());
        ProjProjectFile projProjectFile = projectFileMapper.selectOne(new QueryWrapper<ProjProjectFile>()
                .in("project_file_id", collect).last(" limit 1")
        );
        if (ObjectUtil.isNotEmpty(projProjectFile)) {
            projProjectFile.setFilePath(OBSClientUtils.getTemporaryUrl(projProjectFile.getFilePath(), 3600));
        }
        return projProjectFile;
    }


    /**
     * 三方接口老系统数据处理
     *
     * @param projectInfoId
     * @param thirdInterfaceId
     * @return
     */
    @Override
    public Result imspInterfaceForNew(Long projectInfoId, Long thirdInterfaceId) {
        // 查询老系统三方接口数据，与新系统数据进行匹配 [增加接口id参数与项目id参数，可根据参数具体的同步数据]
        List<ImspInterfaceDataVO> imspInterfaceDataVOList =
                projThirdInterfaceMapper.selectImspInterfaceData(projectInfoId, thirdInterfaceId);
        for (ImspInterfaceDataVO item : imspInterfaceDataVOList) {
            oldImspInterfaceService.saveInterfaceData(item);
        }
        return Result.success();
    }

    /**
     * 老系统接口文件上传补偿接口
     *
     * @param thirdInterfaceId
     * @return
     */
    @Override
    public Result thirdInterfaceFileUpload(Long thirdInterfaceId) {
        return oldImspInterfaceService.thirdInterfaceFileUpload(thirdInterfaceId);
    }

    @Override
    public Result<List<ProjInterfaceRecordLogVo>> selectInterfaceRecordLog(Long thirdInterfaceId) {
        Result<List<ProjInterfaceRecordLogVo>> listResult = interfaceRecordLogService.selectInterfaceRecordLog(thirdInterfaceId);
        return listResult;
    }

    /**
     * 部署申请授权
     *
     * @param dto
     * @return
     */
    @Override
    public Result saveDeployDeviceInfo(SaveDeployDeviceInfoDTO dto) {
        for (Long thirdInterfaceId : dto.getInterfaceIdList()) {
            // 查询正式环境的appId
            ProjInterfaceVsAuthor interfaceVsAuthor = interfaceVsAuthorMapper.selectOne(new QueryWrapper<ProjInterfaceVsAuthor>()
                    .eq("third_interface_id", thirdInterfaceId)
                    .eq("environment", 1)
            );
            try {
                // 查询
                dto.setAppId(interfaceVsAuthor.getPfAppId());
                String jsonInputString = JSONUtil.toJsonStr(dto);
                TreeMap treeMap = JSONUtil.toBean(jsonInputString, TreeMap.class);
                String jsonInputString2 = JSONUtil.toJsonStr(treeMap);
                String timestamp = String.valueOf(System.currentTimeMillis());
                String signStr = jsonInputString2 + timestamp;
                String signature = SM2Util.sign(signStr, sm2);
                // 调用授权平台进行部署授权
                cn.hutool.json.JSONObject jsonObject =
                        authorizationManagementFeignClient.saveDeployDeviceInfo(signature, timestamp, dto);
                if (jsonObject.getInt("status") != 200) {
                    throw new RuntimeException("部署申请失败");
                }
                // 修改接口状态为已完成 【20.22 张吉宝: 部署申请提交时不再更新接口状态为已完成。接口完成节点更为(正式环境授权通过)】
//                ProjThirdInterface thirdInterface = new ProjThirdInterface();
//                thirdInterface.setThirdInterfaceId(thirdInterfaceId);
//                thirdInterface.setStatus(50);
//                projThirdInterfaceMapper.updateById(thirdInterface);
                // 保存部署申请信息
                ProjInterfaceVsDeviceInfo interfaceVsDeviceInfo =
                        projInterfaceVsDeviceInfoMapper.selectOne(new QueryWrapper<ProjInterfaceVsDeviceInfo>()
                                .eq("third_interface_id", thirdInterfaceId)
                                .orderByDesc("create_time")
                                .last("limit 1"));
                if (ObjectUtil.isNotEmpty(interfaceVsDeviceInfo)) {
                    interfaceVsDeviceInfo.setPfAppId(dto.getAppId());
                    interfaceVsDeviceInfo.setIp(dto.getIp());
                    interfaceVsDeviceInfo.setMac(dto.getMac());
                    interfaceVsDeviceInfo.setApplicant(dto.getApplicant());
                    interfaceVsDeviceInfo.setApplicantPhone(dto.getApplicantPhone());
                    projInterfaceVsDeviceInfoMapper.updateById(interfaceVsDeviceInfo);
                } else {
                    ProjInterfaceVsDeviceInfo projInterfaceVsDeviceInfo = new ProjInterfaceVsDeviceInfo();
                    projInterfaceVsDeviceInfo.setThirdInterfaceId(thirdInterfaceId);
                    projInterfaceVsDeviceInfo.setIp(dto.getIp());
                    projInterfaceVsDeviceInfo.setMac(dto.getMac());
                    projInterfaceVsDeviceInfo.setPfAppId(dto.getAppId());
                    projInterfaceVsDeviceInfo.setApplicant(dto.getApplicant());
                    projInterfaceVsDeviceInfo.setApplicantPhone(dto.getApplicantPhone());
                    projInterfaceVsDeviceInfoService.insert(projInterfaceVsDeviceInfo);
                }
                ProjThirdInterface projThirdInterface = projThirdInterfaceMapper.selectById(thirdInterfaceId);
                SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
                param.setProjectInfoId(projThirdInterface.getProjectInfoId());
                param.setHospitalInfoId(projThirdInterface.getHospitalInfoId());
                param.setUserId(projThirdInterface.getDirPersonId());
                param.setCode(DictProjectPlanItemEnum.SCHEDULE_THIRD_PART.getPlanItemCode());
                todoTaskService.updateProjectTodoTaskStatus(param);
                // 增加部署申请时的 接口日志
                interfaceRecordLogService.saveInterfaceRecordLog("IP、MAC已授权", thirdInterfaceId, null);
            } catch (Exception e) {
                return Result.fail(e.getMessage());
            }
        }
        return Result.success();
    }

    /**
     * 申请国密授权
     *
     * @param dto
     * @return
     */
    @Override
    public Result<CreateAppAuthorizationReesult> createAppAuthorization(CreateAppAuthorizationDTO dto) {
        try {
            String jsonInputString = JSONUtil.toJsonStr(dto);
            TreeMap treeMap = JSONUtil.toBean(jsonInputString, TreeMap.class);
            String jsonInputString2 = JSONUtil.toJsonStr(treeMap);
            String timestamp = String.valueOf(System.currentTimeMillis());
            String signStr = jsonInputString2 + timestamp;
            String signature = SM2Util.sign(signStr, sm2);
            CreateAppAuthorizationReesult appAuthorization =
                    authorizationManagementFeignClient.createAppAuthorization(signature, timestamp, dto);
            if (appAuthorization.getStatus() == 200) {
                // 存储 appId
                log.info("申请国密授权产生的 appId  , {}", JSONUtil.toJsonStr(appAuthorization));
                return Result.success(appAuthorization);
            } else {
                return Result.fail();
            }
        } catch (Exception e) {
            return Result.fail("创建失败");
        }
    }

    /**
     * 更新数据集
     *
     * @param dto
     * @return
     */
    @Override
    public Result updateDataSet(ProjThirdInterfaceDTO dto) {
        DemandDto demandDto = new DemandDto();
        demandDto.setDemandId(dto.getThirdInterfaceId().toString());
        demandDto.setDataType(dto.getDataSetId().toString());
        demandDto.setDataTypeName(dto.getDataSetName());
        thirdPlatformFeignClient.updateDataType(demandDto);
        // 更新交付平台数据
        ProjThirdInterface projThirdInterface = new ProjThirdInterface();
        projThirdInterface.setThirdInterfaceId(dto.getThirdInterfaceId());
        projThirdInterface.setDataSetId(dto.getDataSetId());
        projThirdInterface.setDataSetName(dto.getDataSetName());
        projThirdInterfaceMapper.updateById(projThirdInterface);
        return Result.success();
    }

    /**
     * 查询部署申请信息
     *
     * @param thirdInterfaceId
     * @return
     */
    @Override
    public Result<SaveDeployDeviceInfoDTO> getDeployDeviceInfo(Long thirdInterfaceId) {
        return projInterfaceVsDeviceInfoService.getInfoDataById(thirdInterfaceId);
    }

    /**
     * 下载SDK
     *
     * @param fileCode
     * @return
     */
    @Override
    public Result<String> downloadSDK(String fileCode) {
        SysFile file = sysFileMapper.selectOne(new QueryWrapper<SysFile>()
                .eq("file_code", fileCode)
        );
        String sdkUrl = OBSClientUtils.getTemporaryUrl(file.getFilePath(), 3600);
        return Result.success(sdkUrl);
    }

    /**
     * 绑定反馈单和三方接口关系
     *
     * @param dto
     */
    private boolean bindingInterface(ProjThirdInterfaceDTO dto) {
        boolean result = true;
        //查询反馈单是否已经保存过
        ProjFeedbackInterface projFeedbackInterface = projFeedbackInterfaceMapper.selectOne(new QueryWrapper<ProjFeedbackInterface>()
                .eq("feedback_id", dto.getFeedbackId()).last(" limit 1"));
        if (ObjectUtil.isNotEmpty(projFeedbackInterface)) {
            //已绑定过接口且不是当前修改接口，返回false
            if (projFeedbackInterface.getInterfaceId() != dto.getThirdInterfaceId()) {
                result = false;
            }
        } else {
            //保存对照关系
            ProjFeedbackInterface feedbackInterface = new ProjFeedbackInterface();
            feedbackInterface.setFeedbackInterfaceId(SnowFlakeUtil.getId());
            feedbackInterface.setInterfaceId(dto.getThirdInterfaceId());
            feedbackInterface.setFeedbackId(dto.getFeedbackId());
            projFeedbackInterfaceMapper.insert(feedbackInterface);
        }
        return result;
    }

    /**
     * 运维平台查询交付平台三方接口列表
     *
     * @param hospitalId
     * @return
     */
    @Override
    public Result<List<ThirdInterfaceVO>> findThirdInterfaceList(Long hospitalId) {
        List<ThirdInterfaceVO> interfaceList = projThirdInterfaceMapper.findThirdInterfaceList(hospitalId);
        return Result.success(interfaceList);
    }

    /**
     * 保存运维平台反馈单与交付平台三方接口对照关系
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result saveInterfaceRelation(ThirdInterfaceDTO dto) {
        //查询反馈单是否已经保存过，如果是，将原来的数据作废掉
        ProjFeedbackInterface projFeedbackInterface = projFeedbackInterfaceMapper.selectOne(new QueryWrapper<ProjFeedbackInterface>()
                .eq("feedback_id", dto.getFeedbackId()));
        if (ObjectUtil.isNotEmpty(projFeedbackInterface)) {
            projFeedbackInterface.setIsDeleted(1);
            projFeedbackInterfaceMapper.updateById(projFeedbackInterface);
        }
        //保存对照关系
        ProjFeedbackInterface feedbackInterface = new ProjFeedbackInterface();
        feedbackInterface.setFeedbackInterfaceId(SnowFlakeUtil.getId());
        feedbackInterface.setInterfaceId(dto.getThirdInterfaceId());
        feedbackInterface.setFeedbackId(dto.getFeedbackId());
        projFeedbackInterfaceMapper.insert(feedbackInterface);
        return Result.success();
    }

    /**
     * 运维平台反馈单完成状态回写
     *
     * @param dto
     */
    @Override
    public Result completeFeeback(ThirdInterfaceDTO dto) {
        ProjFeedbackInterface projFeedbackInterface = projFeedbackInterfaceMapper.selectOne(new QueryWrapper<ProjFeedbackInterface>()
                .eq("feedback_id", dto.getFeedbackId()));
        if (ObjectUtil.isEmpty(projFeedbackInterface)) {
            return Result.fail("未查询到反馈单的接口信息！");
        }
        projFeedbackInterface.setCompleteFlag(1);
        projFeedbackInterface.setUpdateTime(new Date());
        projFeedbackInterfaceMapper.updateById(projFeedbackInterface);
        return Result.success();
    }

    /**
     * 操作手册
     *
     * @param fileCode
     * @return
     */
    @Override
    public Result<String> operateDoc(String fileCode) {
        SysFile file = sysFileMapper.selectOne(new QueryWrapper<SysFile>()
                .eq("file_code", fileCode)
        );
        return Result.success(file.getFilePath());
    }

    /**
     * 更新三方合同文件
     *
     * @param req
     * @return
     */
    @Override
    public Result updateContractFiles(UpdateAuthFileReq req) {
        projThirdInterfaceMapper.updateContractFiles(req.getFileId(), req.getInterfaceIdList());
        return Result.success();
    }

    /**
     * 验证三方接口
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result verifyInterface(ProjThirdInterfaceDTO dto) {
        String recordName = "";
        //查询接口信息
        ProjThirdInterface thirdInterface = projThirdInterfaceMapper.selectById(dto.getThirdInterfaceId());
        if (thirdInterface.getStatus() != 34) {
            return Result.fail("只有申请验证状态的接口才能进行验证！");
        }
        if (ObjectUtil.isEmpty(thirdInterface.getMainId())) {
            return Result.fail("当前接口无运营平台接口信息，无法验证！");
        }
        //调用运营平台接口
        UpdateYunyingDto updateYunyingDto = new UpdateYunyingDto();
        updateYunyingDto.setMainId(thirdInterface.getMainId());
        if (dto.getStatus() == 35) {
            //验证通过
            recordName = "接口验证通过";
            updateYunyingDto.setStatus(2);
        } else {
            //验证不通过
            recordName = "接口验证不通过";
            updateYunyingDto.setStatus(3);
            updateYunyingDto.setVerifyResult(dto.getComments());
        }
        Map<String, Object> responseData = yunyingFeignClient.interfaceVerify(updateYunyingDto);
        log.info("验证三方接口 运营平台出参 ：--------------" + JSONObject.toJSONString(responseData));
        if (!(Boolean) responseData.get("success")) {
            return Result.fail(responseData.get("msg").toString());
        }
        //修改接口状态
        thirdInterface.setStatus(dto.getStatus());
        projThirdInterfaceMapper.updateById(thirdInterface);
        //保存操作日志
        interfaceRecordLogService.saveInterfaceRecordLog(recordName, dto.getThirdInterfaceId(), updateYunyingDto.getVerifyResult());
        try {
            ProjThirdInterface projThirdInterface1 = projThirdInterfaceMapper.selectById(dto.getThirdInterfaceId());
            SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
            param.setProjectInfoId(projThirdInterface1.getProjectInfoId());
            param.setHospitalInfoId(projThirdInterface1.getHospitalInfoId());
            param.setUserId(projThirdInterface1.getDirPersonId());
            param.setCode(DictProjectPlanItemEnum.SCHEDULE_THIRD_PART.getPlanItemCode());
            todoTaskService.updateProjectTodoTaskStatus(param);
        } catch (Exception e) {
            log.error("三方接口对接更新进度，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        return Result.success();
    }

    /**
     * 获取三方接口授权服务器设备信息
     *
     * @param dto
     * @return
     */
    @Override
    public Result<List<DeployDeviceInfoVO>> findDeployDeviceInfo(ProjThirdInterfaceDTO dto) {
        //查询接口正式环境授权appId
        ProjInterfaceVsAuthor interfaceVsAuthor = new LambdaQueryChainWrapper<>(interfaceVsAuthorMapper)
                .eq(ProjInterfaceVsAuthor::getThirdInterfaceId, dto.getThirdInterfaceId())
                .eq(ProjInterfaceVsAuthor::getEnvironment, 1)
                .isNotNull(ProjInterfaceVsAuthor::getPfAppId)
                .last("limit 1")
                .one();
        if (ObjectUtil.isEmpty(interfaceVsAuthor)) {
            return Result.success();
        }
        String timestamp = String.valueOf(System.currentTimeMillis());
        cn.hutool.json.JSONObject jsonObject =
                authorizationManagementFeignClient.getDeployDeviceInfo(timestamp, interfaceVsAuthor.getPfAppId());
        if (jsonObject.getInt("status") != 200) {
            return Result.fail("获取授权服务器信息失败，" + jsonObject.getStr("msg"));
        }
        if (ObjectUtil.isEmpty(jsonObject.get("obj"))) {
            return Result.success();
        }
        List<DeployDeviceInfoVO> deviceInfoList = (List<DeployDeviceInfoVO>) jsonObject.get("obj");
        return Result.success(deviceInfoList);
    }

    /**
     * 查询三方接口在运维平台是否是后端运维的客户
     *
     * @param dto
     * @return
     */
    @Override
    public Result selectInterfaceInOtherSystemInfo(ProjThirdInterfaceDTO dto) {
        Integer reslutValue = projThirdInterfaceMapper.selectInterfaceInOtherSystemInfo(dto);
        return Result.success(reslutValue);
    }


    @Override
    public ThirdInterfaceMemberDept thirdInterfaceMemberDept(ProjectInfoId param) {
        ConfigCustomBackendDetailLimit customBackendDetailLimit = configCustomBackendDetailLimitMapper.getCustomBackendDetailLimit(param.getProjectInfoId(), 15);
        ThirdInterfaceMemberDept thirdInterfaceMemberDept = new ThirdInterfaceMemberDept();
        // 开启了接口后端服务
        if (customBackendDetailLimit != null && Integer.valueOf(1).equals(customBackendDetailLimit.getOpenFlag())) {
            // 接口服务经理
            DictProjectRole dictProjectRole = new DictProjectRole();
            dictProjectRole.setProjectRoleCode("interface-leader");
            DictProjectRole dictProjectRoles = dictProjectRoleMapper.selectDictProjectRole(dictProjectRole).stream().findFirst().orElse(null);
            if (dictProjectRoles != null) {
                List<ProjProjectMember> projProjectMembers = projMemberMapper.selectByProjectIdAndRole(param.getProjectInfoId(), dictProjectRoles.getProjectRoleId());
                ProjProjectMember projProjectMember = projProjectMembers.stream().findFirst().orElse(null);
                if (projProjectMember != null) {
                    thirdInterfaceMemberDept.setSelectedYyDeptId(projProjectMember.getProjectTeamId());
                }
            }
        } else {
            Long currentSysUserId = userHelper.getCurrentSysUserIdWithDefaultValue();
            SysUser sysUser = sysUserMapper.selectOne(
                    new QueryWrapper<SysUser>()
                            .eq("is_deleted", 0)
                            .eq("sys_user_id", currentSysUserId)
            );
            thirdInterfaceMemberDept.setSelectedYyDeptId(sysUser.getDeptId());
        }
        List<SysDept> allDept = sysDeptMapper.selectList(
                new QueryWrapper<SysDept>()
                        .eq("is_deleted", 0)
        );
        List<BaseIdNameResp> collect = allDept.stream().map(item -> new BaseIdNameResp(item.getDeptYunyingId(), item.getDeptName())).collect(Collectors.toList());
        thirdInterfaceMemberDept.setDeptList(collect);
        return thirdInterfaceMemberDept;
    }

    @Override
    public Result<PageInfo<SysUserVO>> thirdInterfaceMember(InterfaceMemberParamDTO dto) {
        List<SysUserVO> userList;
        SysUserDTO userDTO = new SysUserDTO();
        userDTO.setUserName(dto.getUserName());
        if (ObjectUtil.isNotEmpty(dto.getYyDeptId())) {
            // 查询当前部门下的人员信息
            userDTO.setDeptId(dto.getYyDeptId());
        }
        userList = PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> sysUserMapper.selectUserList(userDTO));
        return Result.success(new PageInfo<>(userList));
    }

    /**
     * 后端运维查询页面
     *
     * @param dto
     * @return
     */
    @Override
    public Result<PageInfo<ProjThirdInterfaceVO>> selectByPageToMake(ProjThirdInterfacePageDTO dto) {
        return PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> {
            // 后端运维进入
            dto.setPageSource(1);
            List<ProjThirdInterfaceVO> projThirdInterfaceVOS = projThirdInterfaceMapper.selectThirdInterface(dto);
            //查询文件信息，封装到VO中
            for (ProjThirdInterfaceVO vo : projThirdInterfaceVOS) {
                // 接口委托书文件
                if (ObjectUtil.isNotEmpty(vo.getAuthLetterFiles())) {
                    vo.setAuthLetterFileLinkList(getUrlList(vo.getAuthLetterFiles()));
                }
                // 三方接口合同文件
                if (ObjectUtil.isNotEmpty(vo.getThirdContractFiles())) {
                    vo.setThirdContractFileLinkList(getUrlList(vo.getThirdContractFiles()));
                }
                if (ObjectUtil.isNotEmpty(vo.getThirdInterfaceFiles())) {
                    vo.setThirdInterfaceFileLinkList(getUrlList(vo.getThirdInterfaceFiles()));
                }
                // 进度详情获取最新一条数据
                List<ProjTaskProgress> projTaskProgresses = taskProgressMapper.selectList(
                        new QueryWrapper<ProjTaskProgress>()
                                .eq("source_type", 3)
                                .eq("source_type_id", vo.getThirdInterfaceId())
                                .orderByDesc("create_time")
                                .last("limit 1")
                );
                vo.setProgressDetail(CollectionUtil.isNotEmpty(projTaskProgresses) && projTaskProgresses.size() > 0
                        ? projTaskProgresses.get(0).getProgressDesc() : "");
                // 对接方式为项目组对接时 拼装 接口开发链接
                if (ObjectUtil.isNotEmpty(vo.getImplementsType()) && vo.getImplementsType() == 2) {
                    // 查询运营平台客户id
                    ProjCustomInfo customInfo = customInfoMapper.selectByPrimaryKey(vo.getCustomInfoId());
                    // 查询医院信息
                    ProjHospitalInfo hospitalInfo = hospitalInfoMapper.selectById(vo.getHospitalInfoId());
                    String url = interfaceWebDemand + selectInterfaceMethod + "?customerId=" + customInfo.getYyCustomerId()
                            + "&orgId=" + hospitalInfo.getOrgId() + "&loginName=" + userHelper.getCurrentUser().getAccount()
                            + "&customerName=" + customInfo.getCustomName();
                    vo.setInterfaceMadeUrl(url);
                }

            }
            return Result.success(new PageInfo<>(projThirdInterfaceVOS));
        });
    }
}
