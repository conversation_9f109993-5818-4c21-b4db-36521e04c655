package com.msun.csm.service.proj;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.msun.csm.dao.entity.proj.ProjEquipRecord;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/10
 */

public interface ProjEquipRecordService extends IService<ProjEquipRecord> {

    int deleteByPrimaryKey(Long equipRecordId);

    int insert(ProjEquipRecord record);

    int insertOrUpdate(ProjEquipRecord record);

    int insertOrUpdateSelective(ProjEquipRecord record);

    int insertSelective(ProjEquipRecord record);

    ProjEquipRecord selectByPrimaryKey(Long equipRecordId);

    int updateByPrimaryKeySelective(ProjEquipRecord record);

    int updateByPrimaryKey(ProjEquipRecord record);

    int updateBatch(List<ProjEquipRecord> list);

    int updateBatchSelective(List<ProjEquipRecord> list);

    int batchInsert(List<ProjEquipRecord> list);

}
