package com.msun.csm.service.proj;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import com.msun.core.component.implementation.api.imsp.dto.AvailableLabQueryDTO;
import com.msun.core.component.implementation.api.imsp.dto.LisEquipmentItemDTO;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.extension.service.IService;
import com.msun.core.component.implementation.api.imsp.dto.EquipmentStatusDto;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjEquipRecordVsLis;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjSurveyPlan;
import com.msun.csm.model.dto.LisCloudEquipSelectDTO;
import com.msun.csm.model.dto.LisEquipSelectDTO;
import com.msun.csm.model.dto.OldEquipImportDTO;
import com.msun.csm.model.dto.OldLisEquipImgDTO;
import com.msun.csm.model.dto.OldLisImportDTO;
import com.msun.csm.model.dto.ProjEquipRecordVsLisDTO;
import com.msun.csm.model.dto.ProjEquipRecordVsLisListSaveDTO;
import com.msun.csm.model.dto.ProjEquipRecordVsLisSaveDTO;
import com.msun.csm.model.dto.ProjEquipSendToCloudDTO;
import com.msun.csm.model.dto.ProjEquipVsProductFinishDTO;
import com.msun.csm.model.dto.UpdateLisCloudEquipDTO;
import com.msun.csm.model.dto.ViewProjEquipRecordDTO;
import com.msun.csm.model.dto.device.EquipDetailItemDTO;
import com.msun.csm.model.dto.tip.ProjTipRecordDto;
import com.msun.csm.model.vo.BaseCodeNameVO;
import com.msun.csm.model.vo.CloudEquipVO;
import com.msun.csm.model.vo.LisEquipVo;
import com.msun.csm.model.vo.ProjEquipCheckVsLisVO;
import com.msun.csm.model.vo.ProjEquipRecordLisResultVO;
import com.msun.csm.model.vo.ProjEquipRecordVsLisVO;
import com.msun.csm.model.vo.ProjEquipRecordVsLisWrapperVO;
import com.msun.csm.model.vo.device.EquipLisDetailItemVO;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/10
 */

public interface ProjEquipRecordVsLisService extends IService<ProjEquipRecordVsLis> {

    int insert(ProjEquipRecordVsLis record);

    int insertOrUpdate(ProjEquipRecordVsLis record);

    int insertOrUpdateSelective(ProjEquipRecordVsLis record);

    int insertSelective(ProjEquipRecordVsLis record);

    int batchInsert(List<ProjEquipRecordVsLis> list);


    /**
     * 查询Lis设备列表数据
     *
     * @param dto
     * @return
     */
    Result<ProjEquipRecordLisResultVO> selectLisEquipData(LisEquipSelectDTO dto);

    /**
     * 设备信息发送到LIS解析平台
     *
     * @param equipRecordVsLisIds
     * @return
     */
    Result equipSendToLis(List<Long> equipRecordVsLisIds);

    /**
     * Lis平台回调分配接口制作人
     * @param equipRecordId
     * @param userYunyingId
     * @return
     */
    Result<?> lisApiDevAssignCallback(Long equipRecordVsLisId, String userYunyingId);

    /**
     * 删除LIS设备信息
     *
     * @param equipRecordVsLisId
     * @return
     */
    Result deleteEquipToLis(Long equipRecordVsLisId);

    /**
     * 撤销LIS接口申请
     *
     * @param equipRecordVsLisId
     * @return
     */
    Result equipRevokeToLis(Long equipRecordVsLisId);

    /**
     * 新增LIS设备信息
     *
     * @param dto
     * @return
     */
    Result saveOrUpdateEquipToLis(ProjEquipRecordVsLisDTO dto);

    /**
     * 新增LIS设备信息
     *
     * @param dto 请求保存内容, 含是否是手机端标识
     * @return 结果, 返回失败成功
     */
    Result<String> saveDevice(ProjEquipRecordVsLisSaveDTO dto);

    /**
     * 查看LIS设备信息
     *
     * @param dto
     * @return
     */
    Result<ProjEquipRecordVsLisVO> viewEquipToLis(ViewProjEquipRecordDTO dto);

    /**
     * 查看LIS设备信息
     *
     * @param dto 请求值
     * @return 结果, 带数据, 新增、编辑使用
     */
    Result<ProjEquipRecordVsLisWrapperVO> getEquipToLis(ViewProjEquipRecordDTO dto);

    /**
     * 查询Lis系统云健康设备数据
     *
     * @param selectDTO 请求参数
     * @return Result<Map < Long, List < CloudEquipVO>>>
     */
//    Result<Map<Long, List<CloudEquipVO>>> selectCloudEquipData(LisCloudEquipSelectDTO selectDTO);

    /**
     * 查询LIS系统的云健康设备数据
     *
     * @param selectDTO
     * @return
     */
    Map<Long, List<CloudEquipVO>> selectCloudEquipData(LisCloudEquipSelectDTO selectDTO);

    /**
     * Lis设备自动对照云健康设备信息（根据设备名称进行匹配）
     *
     * @param dto
     * @return
     */
    Result<List<LisEquipVo>> compareEquipmentToLis(LisEquipSelectDTO dto);

    /**
     * 调研阶段提交完成
     *
     * @return
     */
    Result commitFinish(ProjEquipVsProductFinishDTO dto);

    /**
     * 调用Lis开放平台进行设备检测
     *
     * @param equipRecordVsLisIds
     * @return
     */
    Result checkEquipToLisAnalyManager(List<Long> equipRecordVsLisIds);

    /**
     * LIS系统修改云健康对照设备
     *
     * @param dtoList 对照关系
     * @return 成功失败结果
     */
    default Result<String> updateCloudEquipToLis(List<UpdateLisCloudEquipDTO> dtoList) {
        return null;
    }

    /**
     * 一键检测
     *
     * @param dto
     * @return
     */
    Result checkSendToMsunVsLis(ProjEquipSendToCloudDTO dto);

    /**
     * 一键检测lis设备
     * <p>
     * 可根据updateStatusFlag选择是否更新设备状态, 也会用于保存设备对照时获取检测结果详情使用
     * </p>
     *
     * @param dto
     * @param updateStatusFlag
     * @return
     */
    Result<String> checkSendToMsunVsLis(ProjEquipSendToCloudDTO dto, Integer updateStatusFlag);

    /**
     * 查看检测进度
     *
     * @param dto
     * @return
     */
    Result<List<ProjEquipCheckVsLisVO>> viewCheckProgress(ProjEquipSendToCloudDTO dto);

    /**
     * 保存设备进度
     * <p>
     * 删除设备结果, 保存最新记录
     * </p>
     *
     * @param equipment    新的设备信息
     * @param hospitalInfo 医院信息
     */
    void batchInsertLisCheck(EquipmentStatusDto equipment, ProjHospitalInfo hospitalInfo);

    /**
     * Lis设备下载模版
     *
     * @param response
     * @param projectInfoId
     */
    void downloadTemplate(HttpServletResponse response, Long projectInfoId);

    /**
     * 导入模版数据
     *
     * @param multipartFile
     * @param customInfoId
     * @param projectInfoId
     * @return
     */
    Result importExcelDatas(MultipartFile multipartFile, Long customInfoId, Long projectInfoId);

    /**
     * 导出数据
     *
     * @param dto
     */
    void exportExcelDatas(LisEquipSelectDTO dto, HttpServletResponse response);

    /**
     * 老换新设备字典获取执行
     *
     * @param dto
     * @return
     */
    Result executeImport(OldEquipImportDTO dto);

    /**
     * 老系统Lis设备迁移到新系统
     *
     * @param projectInfoId
     * @param oldEquipId
     * @return
     */
    Result sendEquipToOldLis(Long projectInfoId, Long oldEquipId);

    /**
     * 生成对应产品的里程碑设备任务
     *
     * @param userId
     * @param projSurveyPlan
     * @param code
     */
    void addEquipSurveyTask(Long userId, ProjSurveyPlan projSurveyPlan, String code);

    /**
     * 保存老换新设备字典获取操作已读
     *
     * @param dto
     * @return
     */
    Result saveManualRead(ProjTipRecordDto dto);

    /**
     * 查询老换新设备字典获取操作是否已读
     *
     * @param dto
     * @return
     */
    Result<Boolean> getManualRead(ProjTipRecordDto dto);

    /**
     * 老系统Lis设备图片迁移到新系统
     *
     * @param dto
     * @return
     */
    Result sendEquipImgToOldLis(OldLisEquipImgDTO dto);

    /**
     * Lis老换新导入的设备确认资源库设备信息
     *
     * @param dto
     * @return
     */
    Result updateResourceEquipData(OldLisImportDTO dto);

    /**
     * 查询通讯方式及要展示的字段内容
     *
     * @param equipAttributesCode 属性类别, 这里使用 communt_mode:通讯方式, 用于lis设备
     * @return 返回通讯方式机器延展是的字段内容
     */
    Result<List<BaseCodeNameVO>> selectSurveyAttributesInfo(String equipAttributesCode);

    /**
     * 获取LIS设备明细项
     *
     * @param equipDetailItemDTO 设备型号id或名称
     * @return 明细项
     */
    Result<EquipLisDetailItemVO> findEquipDetail(EquipDetailItemDTO equipDetailItemDTO);

    /**
     * 列表保存
     *
     * @param dto 请求参数
     * @return 是否成功
     */
    Result<String> saveDeviceList(ProjEquipRecordVsLisListSaveDTO dto);

    /**
     * 查询手麻(Lis)系统云健康设备数据
     *
     * @param dto 请求参数, 客户id和项目类型
     * @return 按照医院隔离查询的内瓤, key为云健康医院id
     */
    Result<Map<String, List<CloudEquipVO>>> selectCloudEquipDataTransfer(LisCloudEquipSelectDTO dto);

    /**
     * 获取Lis设备推荐检查项目
     * @param dto
     * @return
     */
    Result<List<LisEquipmentItemDTO>> queryLabItemByEquipment(AvailableLabQueryDTO dto);
}
