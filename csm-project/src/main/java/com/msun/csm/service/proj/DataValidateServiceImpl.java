package com.msun.csm.service.proj;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.msun.csm.common.model.dto.PageList;
import com.msun.csm.dao.entity.SysConfig;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjOnlineStep;
import com.msun.csm.dao.mapper.config.SysConfigMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOnlineStepMapper;
import com.msun.csm.model.dto.BaseCloudDataValidateDTO;
import com.msun.csm.model.dto.BaseDataValidateDTO;
import com.msun.csm.model.dto.BaseOldDataValidateDTO;
import com.msun.csm.model.dto.InpatientDepositDataDTO;
import com.msun.csm.model.dto.InpatientDepositInfoDataDTO;
import com.msun.csm.model.dto.OldInpatientCostDTO;
import com.msun.csm.model.dto.OldInpatientInfoDTO;
import com.msun.csm.model.dto.OldOutpatientInfoDTO;
import com.msun.csm.model.dto.PatientDataValidateDTO;
import com.msun.csm.model.dto.PatientInfoDTO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.param.BaseDataValidateParam;
import com.msun.csm.model.param.CompleteConfirmParam;
import com.msun.csm.model.param.InpatientDataValidateParam;
import com.msun.csm.model.param.PatientDataValidateParam;
import com.msun.csm.model.param.ProjectInfoIdParam;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.common.CommonSearchCloudDbService;
import com.msun.csm.util.PageUtil;

import lombok.extern.slf4j.Slf4j;


@Slf4j
@Service
public class DataValidateServiceImpl implements DataValidateService {

    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;

    @Resource
    private CommonSearchCloudDbService commonSearchCloudDbService;

    @Resource
    private ProjCustomInfoService customInfoService;

    @Resource
    private UserHelper userHelper;

    @Resource
    private ProjOnlineStepMapper projOnlineStepMapper;

    @Resource
    private SysConfigMapper sysConfigMapper;

    private static final String HTTP = "http://";

    /**
     * 药库/材料库
     */
    private static final String YAO_KU_CAI_LIAO_KU = "/api/QueryStorageTotal";

    /**
     * 药房库存
     */
    private static final String YAO_FANG = "/api/QueryPharmacyTotal?deptType=1";

    /**
     * 二级库
     */
    private static final String ER_JI_KUN = "/api/QueryPharmacyTotal?deptType=2";

    /**
     * 在院患者费用金
     */
    private static final String ZAI_YUAN_HUAN_ZHE_FEI_YONG_JIN = "/api/QueryInPatDetail";

    /**
     * 门诊患者
     */
    private static final String OUTPATIENT_TOTAL = "/api/QueryOutPatTotal";

    /**
     * 住院患者
     */
    private static final String INPATIENT_TOTAL = "/api/QueryInPatTotal";


    /**
     * 将字符串转为数字类型
     *
     * @param param    参数实体，此字段为null时返回"0"
     * @param function 参数实体中需要转换的字段
     * @param <T>      泛型
     * @return 数字类型字符串
     */
    private <T> String convertNumber(T param, Function<? super T, String> function) {
        if (null == param) {
            return "0";
        }

        String stringValue = function.apply(param);

        if (StringUtils.isBlank(stringValue)) {
            return "0";
        }

        try {
            BigDecimal number = new BigDecimal(stringValue).setScale(5, RoundingMode.HALF_UP).stripTrailingZeros();
            return number.toPlainString();
        } catch (Exception e) {
            return stringValue;
        }
    }


    private String calculateDifference(String cloudNumber, String oldNumber) {
        return new BigDecimal(cloudNumber).subtract(new BigDecimal(oldNumber)).setScale(5, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
    }

    private Boolean existDifference(String stocksCountDifference, String stocksAmountDifference) {
        BigDecimal zero = new BigDecimal("0");
        return zero.compareTo(new BigDecimal(stocksCountDifference)) != 0 || zero.compareTo(new BigDecimal(stocksAmountDifference)) != 0;
    }

    @Override
    public List<BaseDataValidateDTO> baseDataValidate(BaseDataValidateParam param) {
        ProjectInfoIdParam projectInfoIdParam = new ProjectInfoIdParam();
        projectInfoIdParam.setProjectInfoId(param.getProjectInfoId());
        projectInfoIdParam.setHospitalInfoId(param.getHospitalInfoId());
        List<ProjHospitalInfo> projHospitalInfoList = this.getProjHospitalInfoList(projectInfoIdParam);
        if (CollectionUtils.isEmpty(projHospitalInfoList)) {
            return new ArrayList<>();
        }
        // 获取客户信息
        ProjCustomInfo customInfo = customInfoService.selectByPrimaryKey(param.getCustomInfoId());
        if (null == customInfo) {
            throw new IllegalArgumentException(String.format("没有获取到客户信息，customInfoId=%s", param.getCustomInfoId()));
        }
        ProjHospitalInfo projHospitalInfo = projHospitalInfoList.get(0);
        List<BaseDataValidateDTO> result = new ArrayList<>();
        String requestResult;
        String sqlCode;
        // 1-药库/材料库；2-药房；3-二级库
        if ("1".equals(param.getType())) {
            requestResult = param.getYaoKuResult();
            sqlCode = "DrugAndMaterialStorage";
        } else if ("2".equals(param.getType())) {
            requestResult = param.getYaoFangResult();
            sqlCode = "PharmacyStorage";
        } else {
            requestResult = param.getErJiKuResult();
            sqlCode = "SecondLevelStoreroom";
        }
        List<BaseOldDataValidateDTO> oldDataList = StringUtils.isNotBlank(requestResult) ? JSON.parseArray(requestResult, BaseOldDataValidateDTO.class) : new ArrayList<>();
        // 获取云健康数据
        List<BaseCloudDataValidateDTO> newDataList = new ArrayList<>();
        try {
            newDataList = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, sqlCode, BaseCloudDataValidateDTO.class);
        } catch (Exception e) {
            log.error("数据核对获取新系统数据，发生异常，sqlCode={}，errMsg={}，stackInfo=", sqlCode, e.getMessage(), e);
        }
        for (ProjHospitalInfo hospitalInfo : projHospitalInfoList) {
            List<BaseCloudDataValidateDTO> cloudDataValidateDTOList = newDataList.stream().filter(item -> hospitalInfo.getHospitalName().equals(item.getHospitalName())).collect(Collectors.toList());
            for (BaseCloudDataValidateDTO cloudDataValidateDTO : cloudDataValidateDTOList) {
                if (cloudDataValidateDTO != null) {
                    BaseOldDataValidateDTO oldDataValidateDTO = oldDataList.stream().filter(item -> hospitalInfo.getHospitalName().equals(item.getHospitalName()) && cloudDataValidateDTO.getDeptId().equals(item.getDeptId())).findFirst().orElse(null);
                    // 云健康库存数量
                    String cloudStocksCount = this.convertNumber(cloudDataValidateDTO, BaseCloudDataValidateDTO::getStockAmount);
                    // 老系统库存数量
                    String oldStocksCount = this.convertNumber(oldDataValidateDTO, BaseOldDataValidateDTO::getStockTotal);
                    // 库存差额
                    String stocksCountDifference = this.calculateDifference(cloudStocksCount, oldStocksCount);
                    // 云健康库存金额
                    String cloudStocksAmount = this.convertNumber(cloudDataValidateDTO, BaseCloudDataValidateDTO::getRetailPrice);
                    // 老系统库存金额
                    String oldStocksAmount = this.convertNumber(oldDataValidateDTO, BaseOldDataValidateDTO::getPriceTotal);
                    // 库存金额差额
                    String stocksAmountDifference = this.calculateDifference(cloudStocksAmount, oldStocksAmount);
                    BaseDataValidateDTO build = BaseDataValidateDTO
                            .builder()
                            .hospitalInfoId(String.valueOf(hospitalInfo.getHospitalInfoId()))
                            .hospitalName(hospitalInfo.getHospitalName())
                            .itemId(cloudDataValidateDTO.getDeptId())
                            .itemName(cloudDataValidateDTO.getDeptName())
                            .cloudStocksCount(cloudStocksCount)
                            .oldStocksCount(oldStocksCount)
                            .stocksCountDifference(stocksCountDifference)
                            .cloudStocksAmount(cloudStocksAmount)
                            .oldStocksAmount(oldStocksAmount)
                            .stocksAmountDifference(stocksAmountDifference)
                            .existDifference(this.existDifference(stocksCountDifference, stocksAmountDifference))
                            .build();
                    result.add(build);
                }
            }
        }
        // 先过滤数据
        if (Boolean.TRUE.equals(param.getOnlyDifference())) {
            result = result.stream().filter(item -> Boolean.TRUE.equals(item.getExistDifference())).collect(Collectors.toList());
        }
        List<BaseDataValidateDTO> resultBack = new ArrayList<>();
        // 先根据医院ID分组
        Map<String, List<BaseDataValidateDTO>> groupByHospital = result.stream().collect(Collectors.groupingBy(BaseDataValidateDTO::getHospitalInfoId));
        // 将医院ID进行排序
        List<String> hospitalInfoIdSet = groupByHospital.keySet().stream().sorted().collect(Collectors.toList());
        for (String hospitalInfoId : hospitalInfoIdSet) {
            List<BaseDataValidateDTO> baseDataValidateList = groupByHospital.get(hospitalInfoId);
            // 存在差异
            List<BaseDataValidateDTO> existDifferenceList = baseDataValidateList.stream().filter(item -> Boolean.TRUE.equals(item.getExistDifference())).collect(Collectors.toList());
            // 不存在差异
            List<BaseDataValidateDTO> notExistDifferenceList = baseDataValidateList.stream().filter(item -> !Boolean.TRUE.equals(item.getExistDifference())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(existDifferenceList)) {
                resultBack.addAll(existDifferenceList);
            }
            if (!CollectionUtils.isEmpty(notExistDifferenceList)) {
                resultBack.addAll(notExistDifferenceList);
            }
        }
        return resultBack;
    }


    @Override
    public List<PatientDataValidateDTO> patientDataValidate(PatientDataValidateParam param) {
        ProjectInfoIdParam projectInfoIdParam = new ProjectInfoIdParam();
        projectInfoIdParam.setProjectInfoId(param.getProjectInfoId());
        projectInfoIdParam.setHospitalInfoId(param.getHospitalInfoId());
        List<ProjHospitalInfo> projHospitalInfoList = this.getProjHospitalInfoList(projectInfoIdParam);
        if (CollectionUtils.isEmpty(projHospitalInfoList)) {
            return new ArrayList<>();
        }
        ProjHospitalInfo hospitalInfo1 = new ProjHospitalInfo();
        hospitalInfo1.setHospitalName("全部医院");
        projHospitalInfoList.add(hospitalInfo1);
        // 获取客户信息
        ProjCustomInfo customInfo = customInfoService.selectByPrimaryKey(param.getCustomInfoId());
        if (null == customInfo) {
            throw new IllegalArgumentException(String.format("没有获取到客户信息，customInfoId=%s", param.getCustomInfoId()));
        }
        ProjHospitalInfo projHospitalInfo = projHospitalInfoList.get(0);
        List<PatientDataValidateDTO> result = new ArrayList<>();
        // 获取老系统数据
        String requestResultForOutpatient = param.getOutpatientResult();
        String requestResultForInpatient = param.getInpatientResult();
        List<OldOutpatientInfoDTO> oldOutpatientInfoDTOS = StringUtils.isNotBlank(requestResultForOutpatient) ? JSON.parseArray(requestResultForOutpatient, OldOutpatientInfoDTO.class) : new ArrayList<>();
        List<OldInpatientInfoDTO> oldInpatientInfoDTOS = StringUtils.isNotBlank(requestResultForInpatient) ? JSON.parseArray(requestResultForInpatient, OldInpatientInfoDTO.class) : new ArrayList<>();
        // 门诊患者档案数量
        List<PatientInfoDTO> outpatientCount = new ArrayList<>();
        try {
            outpatientCount = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "OutpatientCount", PatientInfoDTO.class);
        } catch (Exception e) {
            log.error("数据核对获取新系统数据，发生异常，sqlCode=OutpatientCount，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        // 卡充值余额
        List<PatientInfoDTO> hisCardRecharge = new ArrayList<>();
        try {
            hisCardRecharge = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "HisCardRecharge", PatientInfoDTO.class);
        } catch (Exception e) {
            log.error("数据核对获取新系统数据，发生异常，sqlCode=HisCardRecharge，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        // 在院患者人数
        List<PatientInfoDTO> inpatientCount = new ArrayList<>();
        try {
            inpatientCount = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "InpatientCount", PatientInfoDTO.class);
        } catch (Exception e) {
            log.error("数据核对获取新系统数据，发生异常，sqlCode=InpatientCount，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        // 在院住院患者押金总额
        List<PatientInfoDTO> hospitalizationDeposit = new ArrayList<>();
        try {
            hospitalizationDeposit = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "HospitalizationDeposit", PatientInfoDTO.class);
        } catch (Exception e) {
            log.error("数据核对获取新系统数据，发生异常，sqlCode=HospitalizationDeposit，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        // 在院患者总费用
        List<PatientInfoDTO> inpatientCostTotal = new ArrayList<>();
        try {
            inpatientCostTotal = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "InpatientCostTotal", PatientInfoDTO.class);
        } catch (Exception e) {
            log.error("数据核对获取新系统数据，发生异常，sqlCode=InpatientCostTotal，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        for (ProjHospitalInfo hospitalInfo : projHospitalInfoList) {
            OldOutpatientInfoDTO oldOutpatientInfoDTO = oldOutpatientInfoDTOS.stream().filter(item -> hospitalInfo.getHospitalName().equals(item.getHospitalName())).findFirst().orElse(null);
            PatientInfoDTO outpatientCountResult = outpatientCount.stream().filter(item -> hospitalInfo.getHospitalName().equals(item.getHospitalName())).findFirst().orElse(null);
            String cloudCount11 = this.convertNumber(outpatientCountResult, PatientInfoDTO::getCount);
            String oldCount11 = this.convertNumber(oldOutpatientInfoDTO, OldOutpatientInfoDTO::getPatCount);
            String stocksCountDifference11 = this.calculateDifference(cloudCount11, oldCount11);
            PatientDataValidateDTO build11 = PatientDataValidateDTO
                    .builder()
                    .hospitalInfoId(String.valueOf(hospitalInfo.getHospitalInfoId()))
                    .hospitalName(hospitalInfo.getHospitalName())
                    .itemName("门诊患者档案数量")
                    .cloudCount(cloudCount11)
                    .oldCount(oldCount11)
                    .countDifference(stocksCountDifference11)
                    .existDifference(new BigDecimal("0").compareTo(new BigDecimal(stocksCountDifference11)) != 0)
                    .build();
            result.add(build11);
            PatientInfoDTO hisCardRechargeResult = hisCardRecharge.stream().filter(item -> hospitalInfo.getHospitalName().equals(item.getHospitalName())).findFirst().orElse(null);
            String cloudCount22 = this.convertNumber(hisCardRechargeResult, PatientInfoDTO::getCount);
            String oldCount22 = this.convertNumber(oldOutpatientInfoDTO, OldOutpatientInfoDTO::getAccountBalanceTotal);
            String stocksCountDifference22 = this.calculateDifference(cloudCount22, oldCount22);
            PatientDataValidateDTO build22 = PatientDataValidateDTO
                    .builder()
                    .hospitalInfoId(String.valueOf(hospitalInfo.getHospitalInfoId()))
                    .hospitalName(hospitalInfo.getHospitalName())
                    .itemName("卡充值余额")
                    .cloudCount(cloudCount22)
                    .oldCount(oldCount22)
                    .countDifference(stocksCountDifference22)
                    .existDifference(new BigDecimal("0").compareTo(new BigDecimal(stocksCountDifference22)) != 0)
                    .build();
            result.add(build22);

            OldInpatientInfoDTO oldInpatientInfoDTO = oldInpatientInfoDTOS.stream().filter(item -> hospitalInfo.getHospitalName().equals(item.getHospitalName())).findFirst().orElse(null);

            PatientInfoDTO inpatientCountResult = inpatientCount.stream().filter(item -> hospitalInfo.getHospitalName().equals(item.getHospitalName())).findFirst().orElse(null);
            String cloudCount33 = this.convertNumber(inpatientCountResult, PatientInfoDTO::getCount);
            String oldCount33 = this.convertNumber(oldInpatientInfoDTO, OldInpatientInfoDTO::getPatCount);
            String stocksCountDifference33 = this.calculateDifference(cloudCount33, oldCount33);
            PatientDataValidateDTO build33 = PatientDataValidateDTO
                    .builder()
                    .hospitalInfoId(String.valueOf(hospitalInfo.getHospitalInfoId()))
                    .hospitalName(hospitalInfo.getHospitalName())
                    .itemName("在院患者人数")
                    .cloudCount(cloudCount33)
                    .oldCount(oldCount33)
                    .countDifference(stocksCountDifference33)
                    .existDifference(new BigDecimal("0").compareTo(new BigDecimal(stocksCountDifference33)) != 0)
                    .build();
            result.add(build33);
            PatientInfoDTO hospitalizationDepositResult = hospitalizationDeposit.stream().filter(item -> hospitalInfo.getHospitalName().equals(item.getHospitalName())).findFirst().orElse(null);
            String cloudCount44 = this.convertNumber(hospitalizationDepositResult, PatientInfoDTO::getCount);
            String oldCount44 = this.convertNumber(oldInpatientInfoDTO, OldInpatientInfoDTO::getPrepayTotal);
            String stocksCountDifference44 = this.calculateDifference(cloudCount44, oldCount44);
            PatientDataValidateDTO build44 = PatientDataValidateDTO
                    .builder()
                    .hospitalInfoId(String.valueOf(hospitalInfo.getHospitalInfoId()))
                    .hospitalName(hospitalInfo.getHospitalName())
                    .itemName("在院住院患者押金总额")
                    .cloudCount(cloudCount44)
                    .oldCount(oldCount44)
                    .countDifference(stocksCountDifference44)
                    .existDifference(new BigDecimal("0").compareTo(new BigDecimal(stocksCountDifference44)) != 0)
                    .build();
            result.add(build44);
            PatientInfoDTO inpatientCostTotalResult = inpatientCostTotal.stream().filter(item -> hospitalInfo.getHospitalName().equals(item.getHospitalName())).findFirst().orElse(null);
            String cloudCount55 = this.convertNumber(inpatientCostTotalResult, PatientInfoDTO::getCount);
            String oldCount55 = this.convertNumber(oldInpatientInfoDTO, OldInpatientInfoDTO::getCostTotal);
            String stocksCountDifference55 = this.calculateDifference(cloudCount55, oldCount55);
            PatientDataValidateDTO build55 = PatientDataValidateDTO
                    .builder()
                    .hospitalInfoId(String.valueOf(hospitalInfo.getHospitalInfoId()))
                    .hospitalName(hospitalInfo.getHospitalName())
                    .itemName("在院患者总费用")
                    .cloudCount(cloudCount55)
                    .oldCount(oldCount55)
                    .countDifference(stocksCountDifference55)
                    .existDifference(new BigDecimal("0").compareTo(new BigDecimal(stocksCountDifference55)) != 0)
                    .build();
            result.add(build55);

        }
        // 先过滤数据
        if (Boolean.TRUE.equals(param.getOnlyDifference())) {
            result = result.stream().filter(item -> Boolean.TRUE.equals(item.getExistDifference())).collect(Collectors.toList());
        }
        // 处理门诊患者档案数量在老系统中无法区分医院所以显示为全部医院的情况
        SysConfig specialHospital = sysConfigMapper.selectConfigByName("DATA_VALIDATE_SPECIAL_HOSPITAL");
        SysConfig specialItem = sysConfigMapper.selectConfigByName("DATA_VALIDATE_SPECIAL_ITEM");
        // 全部医院仅保留门诊患者档案数量
        result.removeIf(item -> specialHospital.getConfigValue().equals(item.getHospitalName()) && !specialItem.getConfigValue().equals(item.getItemName()));

        // 门诊患者档案数量仅保留医院名称为全部医院的数据
        result.removeIf(item -> !specialHospital.getConfigValue().equals(item.getHospitalName()) && specialItem.getConfigValue().equals(item.getItemName()));

        List<PatientDataValidateDTO> resultBack = new ArrayList<>();
        // 先根据医院ID分组
        Map<String, List<PatientDataValidateDTO>> groupByHospital = result.stream().collect(Collectors.groupingBy(PatientDataValidateDTO::getHospitalInfoId));
        // 将医院ID进行排序
        List<String> hospitalInfoIdSet = groupByHospital.keySet().stream().sorted().collect(Collectors.toList());
        for (String hospitalInfoId : hospitalInfoIdSet) {
            List<PatientDataValidateDTO> baseDataValidateList = groupByHospital.get(hospitalInfoId);
            // 存在差异
            List<PatientDataValidateDTO> existDifferenceList = baseDataValidateList.stream().filter(item -> Boolean.TRUE.equals(item.getExistDifference())).collect(Collectors.toList());
            // 不存在差异
            List<PatientDataValidateDTO> notExistDifferenceList = baseDataValidateList.stream().filter(item -> !Boolean.TRUE.equals(item.getExistDifference())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(existDifferenceList)) {
                resultBack.addAll(existDifferenceList);
            }
            if (!CollectionUtils.isEmpty(notExistDifferenceList)) {
                resultBack.addAll(notExistDifferenceList);
            }
        }
        return resultBack;
    }

    @Override
    public InpatientDepositInfoDataDTO inpatientDepositDataValidate(InpatientDataValidateParam param) {
        ProjectInfoIdParam projectInfoIdParam = new ProjectInfoIdParam();
        projectInfoIdParam.setProjectInfoId(param.getProjectInfoId());
        projectInfoIdParam.setHospitalInfoId(param.getHospitalInfoId());
        List<ProjHospitalInfo> projHospitalInfoList = this.getProjHospitalInfoList(projectInfoIdParam);
        if (CollectionUtils.isEmpty(projHospitalInfoList)) {
            InpatientDepositInfoDataDTO inpatientDepositInfoDataDTO = new InpatientDepositInfoDataDTO();
            inpatientDepositInfoDataDTO.setTotalData(0);
            inpatientDepositInfoDataDTO.setList(new ArrayList<>());
            inpatientDepositInfoDataDTO.setOldTotalAmount("0");
            inpatientDepositInfoDataDTO.setCloudTotalAmount("0");
            return inpatientDepositInfoDataDTO;
        }
        // 获取客户信息
        ProjCustomInfo customInfo = customInfoService.selectByPrimaryKey(param.getCustomInfoId());
        if (null == customInfo) {
            throw new IllegalArgumentException(String.format("没有获取到客户信息，customInfoId=%s", param.getCustomInfoId()));
        }
        ProjHospitalInfo projHospitalInfo = projHospitalInfoList.get(0);
        List<InpatientDepositDataDTO> result = new ArrayList<>();
        String sqlCode = "InpatientDepositData";
        // 获取老系统数据
        String requestResult = param.getZaiYuanResult();
        List<OldInpatientCostDTO> oldDataList = StringUtils.isNotBlank(requestResult) ? JSON.parseArray(requestResult, OldInpatientCostDTO.class) : new ArrayList<>();
        // 获取云健康数据
        List<OldInpatientCostDTO> newDataList = new ArrayList<>();
        try {
            newDataList = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, sqlCode, OldInpatientCostDTO.class);
        } catch (Exception e) {
            log.error("数据核对获取新系统数据，发生异常，sqlCode={}，errMsg={}，stackInfo=", sqlCode, e.getMessage(), e);
        }
        for (ProjHospitalInfo hospitalInfo : projHospitalInfoList) {
            List<OldInpatientCostDTO> collect = newDataList.stream().filter(item -> hospitalInfo.getHospitalName().equals(item.getHospitalName())).collect(Collectors.toList());
            for (OldInpatientCostDTO cloudData : collect) {
                if (cloudData != null) {
                    OldInpatientCostDTO oldData = oldDataList.stream().filter(item -> hospitalInfo.getHospitalName().equals(item.getHospitalName()) && cloudData.getPatInHosId().equals(item.getPatInHosId())).findFirst().orElse(null);
                    // 云健康金额
                    String cloudAmount = this.convertNumber(cloudData, OldInpatientCostDTO::getCostAmount);
                    // 老系统金额
                    String oldAmount = this.convertNumber(oldData, OldInpatientCostDTO::getCostAmount);
                    // 云健康-老系统的金额差额
                    String amountDifference = this.calculateDifference(cloudAmount, oldAmount);
                    InpatientDepositDataDTO build = InpatientDepositDataDTO
                            .builder()
                            .hospitalInfoId(String.valueOf(hospitalInfo.getHospitalInfoId()))
                            .hospitalName(hospitalInfo.getHospitalName())
                            .patInHosId(cloudData.getPatInHosId())
                            .patName(cloudData.getPatName())
                            .cloudAmount(cloudAmount)
                            .oldAmount(oldAmount)
                            .amountDifference(amountDifference)
                            .existDifference(new BigDecimal("0").compareTo(new BigDecimal(amountDifference)) != 0)
                            .build();
                    result.add(build);
                }
            }
        }
        // 先过滤数据
        if (Boolean.TRUE.equals(param.getOnlyDifference())) {
            result = result.stream().filter(item -> Boolean.TRUE.equals(item.getExistDifference())).collect(Collectors.toList());
        }
        // 按照姓名进行过滤
        if (StringUtils.isNotBlank(param.getName())) {
            result = result.stream().filter(item -> item.getPatName().contains(param.getName())).collect(Collectors.toList());
        }
        List<InpatientDepositDataDTO> resultBack = new ArrayList<>();
        // 先根据医院ID分组
        Map<String, List<InpatientDepositDataDTO>> groupByHospital = result.stream().collect(Collectors.groupingBy(InpatientDepositDataDTO::getHospitalInfoId));
        // 将医院ID进行排序
        List<String> hospitalInfoIdSet = groupByHospital.keySet().stream().sorted().collect(Collectors.toList());
        for (String hospitalInfoId : hospitalInfoIdSet) {
            List<InpatientDepositDataDTO> baseDataValidateList = groupByHospital.get(hospitalInfoId);
            // 存在差异
            List<InpatientDepositDataDTO> existDifferenceList = baseDataValidateList.stream().filter(item -> Boolean.TRUE.equals(item.getExistDifference())).collect(Collectors.toList());
            // 不存在差异
            List<InpatientDepositDataDTO> notExistDifferenceList = baseDataValidateList.stream().filter(item -> !Boolean.TRUE.equals(item.getExistDifference())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(existDifferenceList)) {
                List<InpatientDepositDataDTO> collect = existDifferenceList.stream().sorted(Comparator.comparing(InpatientDepositDataDTO::getPatName).reversed()).collect(Collectors.toList());
                resultBack.addAll(collect);
            }
            if (!CollectionUtils.isEmpty(notExistDifferenceList)) {
                List<InpatientDepositDataDTO> collect = notExistDifferenceList.stream().sorted(Comparator.comparing(InpatientDepositDataDTO::getPatName).reversed()).collect(Collectors.toList());
                resultBack.addAll(collect);
            }
        }
        BigDecimal oldTotalAmount = new BigDecimal("0");
        BigDecimal cloudTotalAmount = new BigDecimal("0");
        for (InpatientDepositDataDTO item : resultBack) {
            oldTotalAmount = oldTotalAmount.add(new BigDecimal(item.getOldAmount()));
            cloudTotalAmount = cloudTotalAmount.add(new BigDecimal(item.getCloudAmount()));
        }
        int totalData = resultBack.size();
        PageList<InpatientDepositDataDTO> pageList = PageUtil.getPageList(resultBack, param.getPageNum(), param.getPageSize());
        InpatientDepositInfoDataDTO inpatientDepositInfoDataDTO = new InpatientDepositInfoDataDTO();
        inpatientDepositInfoDataDTO.setTotalData(totalData);
        inpatientDepositInfoDataDTO.setTotalPage(pageList.getPagination().getTotal());
        inpatientDepositInfoDataDTO.setList(pageList.getPageResultList());
        inpatientDepositInfoDataDTO.setOldTotalAmount(oldTotalAmount.stripTrailingZeros().toPlainString());
        inpatientDepositInfoDataDTO.setCloudTotalAmount(cloudTotalAmount.stripTrailingZeros().toPlainString());
        return inpatientDepositInfoDataDTO;
    }

    private List<ProjHospitalInfo> getProjHospitalInfoList(ProjectInfoIdParam param) {
        if (null != param.getHospitalInfoId()) {
            List<ProjHospitalInfo> list = new ArrayList<>();
            QueryWrapper<ProjHospitalInfo> queryWrapper = new QueryWrapper<ProjHospitalInfo>()
                    .eq("hospital_info_id", param.getHospitalInfoId());
            ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectOne(queryWrapper);
            if (null == hospitalInfo) {
                return new ArrayList<>();
            }
            list.add(hospitalInfo);
            return list;
        }
        // 查询当前项目下的医院信息
        SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
        selectHospitalDTO.setProjectInfoId(param.getProjectInfoId());
        // 查询当前项目下的医院信息(医院名称、终端数)
        return projHospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);
    }

    @Override
    public boolean completeConfirm(CompleteConfirmParam param) {
        SysUserVO currentUser = null;
        try {
            currentUser = userHelper.getCurrentUser();
        } catch (Exception e) {
            log.error("，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        long sysUserId;
        if (currentUser != null && currentUser.getSysUserId() != null) {
            sysUserId = currentUser.getSysUserId();
        } else {
            sysUserId = -1L;
        }
        UpdateWrapper<ProjOnlineStep> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("proj_online_step_id", param.getProjOnlineStepId());
        ProjOnlineStep projOnlineStep = new ProjOnlineStep();
        projOnlineStep.setStatus(1);
        projOnlineStep.setUpdaterId(sysUserId);
        projOnlineStep.setUpdateTime(new Date());
        int update = projOnlineStepMapper.update(projOnlineStep, updateWrapper);
        return update == 1;
    }

}
