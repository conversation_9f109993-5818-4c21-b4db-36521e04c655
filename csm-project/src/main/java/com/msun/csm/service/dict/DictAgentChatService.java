package com.msun.csm.service.dict;

import java.util.List;

import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.dict.DictAgentChat;
import com.msun.csm.model.req.DictAgentChatReq;
import com.msun.csm.model.resp.DictAgentChatScenarioConfigResp;
import com.msun.csm.model.vo.DictAgentChatVO;

/**
 * AI智能体配置服务接口
 * <AUTHOR>
 * @date 2024/10/10
 */
public interface DictAgentChatService {

    /**
     * 查询所有AI检测图片配置
     * @return 配置列表
     */
    Result<List<DictAgentChatVO>> getAllAiImageDetectionConfigs();

    /**
     * 根据场景编码查询配置
     * @param dictAgentChatReq 请求参数
     * @return 配置信息
     */
    Result<DictAgentChatScenarioConfigResp> getConfigByScenario(DictAgentChatReq dictAgentChatReq);

    /**
     * 发送图片检测消息
     * @param dictAgentChatReq 请求参数
     * @return 检测结果
     */
    Result sendChartMessage(DictAgentChatReq dictAgentChatReq);
}
