package com.msun.csm.service.dict;

import java.util.List;

import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.dict.DictProductVsArrange;
import com.msun.csm.model.dto.DictProductVsArrangeDTO;
import com.msun.csm.model.vo.dict.DictProductVsArrangeVO;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/24
 */

public interface DictProductVsArrangeService {

    int deleteByPrimaryKey(Long contrastId);

    int insert(DictProductVsArrange record);

    int insertOrUpdate(DictProductVsArrange record);

    int insertOrUpdateSelective(DictProductVsArrange record);

    int insertSelective(DictProductVsArrange record);

    DictProductVsArrange selectByPrimaryKey(Long contrastId);

    int updateByPrimaryKeySelective(DictProductVsArrange record);

    int updateByPrimaryKey(DictProductVsArrange record);

    int updateBatch(List<DictProductVsArrange> list);

    int updateBatchSelective(List<DictProductVsArrange> list);

    int batchInsert(List<DictProductVsArrange> list);

    /**
     * 查询工单产品对照部署产品列表
     * @param orderProductName
     * @param arrangeProductName
     * @return
     */
    Result<List<DictProductVsArrangeVO>> findDictProductVsArrangeList(String orderProductName, String arrangeProductName);

    /**
     * 新增工单产品与部署产品对照
     * @param dto
     * @return
     */
    Result saveDictProductVsArrange(DictProductVsArrangeDTO dto);

    /**
     * 删除工单产品与部署产品对照
     * @param dto
     * @return
     */
    Result deleteDictProductVsArrange(DictProductVsArrangeDTO dto);
}
