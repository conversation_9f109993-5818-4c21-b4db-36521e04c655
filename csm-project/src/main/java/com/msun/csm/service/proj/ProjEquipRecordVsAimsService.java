package com.msun.csm.service.proj;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.AimsCloudEquipSelectDTO;
import com.msun.csm.model.dto.AimsComparedEquipSelectDTO;
import com.msun.csm.model.dto.AimsEquipSelectDTO;
import com.msun.csm.model.dto.OldEquipImportDTO;
import com.msun.csm.model.dto.ProjEquipRecordVsAimsDTO;
import com.msun.csm.model.dto.ProjEquipVsProductFinishDTO;
import com.msun.csm.model.dto.UpdateAimsCloudEquipDTO;
import com.msun.csm.model.vo.CloudEquipVO;
import com.msun.csm.model.vo.ProjEquipRecordAimsResultVO;
import com.msun.csm.model.vo.ProjEquipRecordVsAimsVO;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/12/02/15:37
 */
public interface ProjEquipRecordVsAimsService {

    /**
     * 查询手麻设备数据
     *
     * @param dto
     * @return
     */
    Result<ProjEquipRecordAimsResultVO> selectEquipRecordVsAimsList(AimsEquipSelectDTO dto);

    /**
     * 根据手麻设备记录id查询手麻设备数据
     *
     * @param equipRecordVsAimsId
     * @return
     */
    Result<ProjEquipRecordVsAimsVO> getEquipRecordVsAimsById(Long equipRecordVsAimsId, Integer isMobile);

    /**
     * 新增或更新保存手麻设备信息
     *
     * @param dto
     * @return
     */
    Result saveOrUpdateAimsEquip(ProjEquipRecordVsAimsDTO dto);

    /**
     * 根据主键id删除手麻设备
     *
     * @param equipRecordVsAimsId
     * @return
     */
    Result deleteAimsEquip(Long equipRecordVsAimsId);

    /**
     * 申请/一键提交申请
     *
     * @param dto
     * @return
     */
    Result equipSendToAimsAnaly(AimsEquipSelectDTO dto);

    /**
     * 撤销手麻接口申请
     *
     * @param equipRecordVsLisId
     * @return
     */
    Result equipRevokeToAims(Long equipRecordVsLisId);

    /**
     * 老换新设备字典获取
     *
     * @param dto
     * @return
     */
    Result executeImportForAims(OldEquipImportDTO dto);

    /**
     * 一键检测
     *
     * @param dto
     * @return
     */
    Result<String> aimsEquipCheckForLisAnaly(AimsEquipSelectDTO dto);

    /**
     * 提交完成
     *
     * @return
     */
    Result commitFinish(ProjEquipVsProductFinishDTO dto);

    /**
     * 发送到云健康
     *
     * @param dto
     * @return
     */
    Result<String> aimsEquipSendCloudEquip(AimsEquipSelectDTO dto);

    /**
     * 手麻设备下载模版
     *
     * @param response
     * @param projectInfoId
     */
    void downloadTemplateForAims(HttpServletResponse response, Long projectInfoId);

    /**
     * 导入模版数据
     *
     * @param multipartFile
     * @param customInfoId
     * @param projectInfoId
     * @return
     */
    Result importExcelDatas(MultipartFile multipartFile, Long customInfoId, Long projectInfoId);

    /**
     * 导出数据
     *
     * @param dto
     * @param response
     */
    void exportExcelDatas(AimsEquipSelectDTO dto, HttpServletResponse response);

    /**
     * 获取云健康手麻设备信息
     *
     * @param dto 含查询设备需要的查询条件
     * @return 返回云健康查询结果, 经过处理, 会通过医院信息隔离
     */
    Map<Long, List<CloudEquipVO>> selectCloudEquipData(AimsCloudEquipSelectDTO dto);

    /**
     * 返回前端. 获取云健康设备信息
     *
     * @param selectDTO 客户及项目类型
     * @return Map集合, Key为string类型,,云健康医院id
     */
    Result<Map<String, List<CloudEquipVO>>> selectCloudEquipDataTransfer(AimsCloudEquipSelectDTO selectDTO);

    /**
     * 查询保存的手麻设备对照数据
     *
     * @param dto 客户与项目信息
     * @return 结果查询
     */
    Result<List<ProjEquipRecordVsAimsVO>> compareEquipmentToAims(AimsComparedEquipSelectDTO dto);

    /**
     * 手麻设备对照保存
     *
     * @param dtoList 需要保存更新的数据
     * @return result 返回结果保存正确错误
     */
    Result<String> updateCloudEquipToAims(List<UpdateAimsCloudEquipDTO> dtoList);
}
