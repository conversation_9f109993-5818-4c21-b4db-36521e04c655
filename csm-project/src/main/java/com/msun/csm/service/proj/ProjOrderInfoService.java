package com.msun.csm.service.proj;

import java.util.List;

import com.msun.csm.dao.entity.proj.ProjOrderInfo;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/24
 */

public interface ProjOrderInfoService {

    int deleteByPrimaryKey(Long orderInfoId);

    int insert(ProjOrderInfo record);

    int insertOrUpdate(ProjOrderInfo record);

    int insertOrUpdateSelective(ProjOrderInfo record);

    int insertSelective(ProjOrderInfo record);

    ProjOrderInfo selectByPrimaryKey(Long orderInfoId);

    int updateByPrimaryKeySelective(ProjOrderInfo record);

    int updateByPrimaryKey(ProjOrderInfo record);

    int updateBatch(List<ProjOrderInfo> list);

    int updateBatchSelective(List<ProjOrderInfo> list);

    int batchInsert(List<ProjOrderInfo> list);
}
