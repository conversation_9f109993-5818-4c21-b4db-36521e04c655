package com.msun.csm.service.proj.applyorder;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.enums.HospitalOpenStatusEnum;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.projapplyorder.DeployModEnum;
import com.msun.csm.common.enums.projapplyorder.HospitalTypeEnum;
import com.msun.csm.common.enums.projapplyorder.ProductOpenStatusEnum;
import com.msun.csm.common.enums.projapplyorder.ProjApplyOrderResultTypeEnum;
import com.msun.csm.common.enums.projapplyorder.ProjApplyTypeEnum;
import com.msun.csm.common.enums.projprojectinfo.ProjectTypeEnums;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.dao.entity.SysConfig;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.proj.ProductInfo;
import com.msun.csm.dao.entity.proj.ProjApplyOrder;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjCustomVsProjectTypeEntity;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfoRelative;
import com.msun.csm.dao.entity.proj.ProjHospitalVsProjectType;
import com.msun.csm.dao.entity.proj.ProjOrderProduct;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.rule.RuleProductRuleConfig;
import com.msun.csm.dao.mapper.config.SysConfigMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomVsProjectTypeMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalVsProjectTypeMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.model.dto.ApplyOrderNodeRecordParamDTO;
import com.msun.csm.model.dto.DeployEnvApplyDTO;
import com.msun.csm.model.dto.DeployResourceApplyCountryDTO;
import com.msun.csm.model.dto.DeployResourceApplyHospitalDTO;
import com.msun.csm.model.dto.ProjHospitalInfoDTO;
import com.msun.csm.model.dto.ProjHospitalInfoExamDTO;
import com.msun.csm.model.dto.applyorder.ApplyOrderHospitalDTO;
import com.msun.csm.model.dto.applyorder.ProjApplyOrderDTO;
import com.msun.csm.service.common.CommonService;
import com.msun.csm.service.proj.ProjHospitalInfoService;
import com.msun.csm.service.proj.ProjProjectSettlementCheckMainService;
import com.msun.csm.service.rule.RuleProductRuleConfigService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 调运维详情. 会写事务, 被调用(方便报错后回滚事务)
 */
@Slf4j
@Service
public class ProjApplyOrderHospitalYunweiService {


    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;

    @Resource
    private ProjApplyOrderService applyOrderService;

    @Resource
    private UserUtil userUtil;

    @Resource
    private ProjHospitalVsProjectTypeMapper hospitalVsProjectTypeMapper;
    @Resource
    private ProjApplyOrderHospitalService applyOrderHospitalService;

    @Resource
    private ProjCustomInfoMapper projCustomInfoMapper;

    @Resource
    private ProjHospitalInfoService hospitalInfoService;

    @Resource
    private RuleProductRuleConfigService ruleProductRuleConfigService;
    @Lazy
    @Resource
    private ProjCustomVsProjectTypeMapper projCustomVsProjectTypeMapper;

    @Lazy
    @Resource
    private ProjProjectInfoMapper projProjectInfoMapper;

    @Resource
    @Lazy
    private SysConfigMapper sysConfigMapper;

    @Resource
    @Lazy
    private SysUserMapper sysUserMapper;

    /**
     * 获取未开通的医院
     * 条件：域名为空, 未开通
     *
     * @param hospitalInfoRelatives 请求参数
     * @return List<ProjHospitalInfoRelative>
     */
    public List<ProjHospitalInfoRelative> getNotOpenHospital(List<ProjHospitalInfoRelative> hospitalInfoRelatives) {
        return hospitalInfoRelatives.stream()
                .filter(e -> {
                    ProjHospitalInfo hospitalInfo = new ProjHospitalInfo();
                    hospitalInfo.setHospitalOpenStatus(e.getHospitalOpenStatus());
                    return hospitalNotOpen(hospitalInfo);
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取开通的医院
     * 条件：域名不为空, 已开通
     *
     * @param hospitalInfoRelatives 请求参数
     * @return List<ProjHospitalInfoRelative>
     */
    public static List<ProjHospitalInfoRelative> getOpenHospital(List<ProjHospitalInfoRelative> hospitalInfoRelatives) {
        return hospitalInfoRelatives.stream()
                .filter(e -> {
                    ProjHospitalInfo hospitalInfo = new ProjHospitalInfo();
                    hospitalInfo.setHospitalOpenStatus(e.getHospitalOpenStatus());
                    hospitalInfo.setCloudDomain(e.getCloudDomain());
                    return hospitalOpen(hospitalInfo);
                })
                .collect(Collectors.toList());
    }

    /**
     * 查询已开通医院
     *
     * @param projectInfoId 项目id
     * @return List<ProjHospitalInfo>
     */
    public List<ProjHospitalInfo> findOpenedHospitalInfo(Long projectInfoId) {
        List<ProjHospitalInfo> hospitalInfosAll = applyOrderHospitalService.findHospitalInfo(projectInfoId);
        return CommonService.getOpenHospitalInfo(hospitalInfosAll);
    }

    /**
     * 查询未开通的医院
     *
     * @param projectInfoId 项目id
     * @return List<ProjHospitalInfo>
     */
    public List<ProjHospitalInfo> findNotOpenHospitalInfo(Long projectInfoId) {
        List<ProjHospitalInfo> hospitalInfosAll = applyOrderHospitalService.findHospitalInfo(projectInfoId);
        if (CollUtil.isEmpty(hospitalInfosAll)) {
            return CollUtil.newArrayList();
        }
        return getNotOpenHospitalInfo(hospitalInfosAll);
    }

    /**
     * 获取未开通的医院
     * 条件：域名不为空, 已开通
     *
     * @param hospitalInfos 请求参数
     * @return List<ProjHospitalInfo>
     */
    private static List<ProjHospitalInfo> getNotOpenHospitalInfo(List<ProjHospitalInfo> hospitalInfos) {
        if (CollUtil.isEmpty(hospitalInfos)) {
            return CollUtil.newArrayList();
        }
        return hospitalInfos.stream()
                .filter(ProjApplyOrderHospitalYunweiService::hospitalNotOpen)
                .collect(Collectors.toList());
    }

    /**
     * 通用, 若医院处于未开通且域名为空时可进行开通
     *
     * @param hospitalInfo 医院信息
     * @return boolean
     */
    public static boolean hospitalNotOpen(ProjHospitalInfo hospitalInfo) {
//        return HospitalOpenStatusEnum.NOT_OPEN.getCode() == hospitalInfo.getHospitalOpenStatus() && StrUtil.isBlank(hospitalInfo.getCloudDomain());
        // 有域名不一定已申请（提前占用域名）
        return HospitalOpenStatusEnum.NOT_OPEN.getCode() == hospitalInfo.getHospitalOpenStatus() || HospitalOpenStatusEnum.OFFLINE.getCode() == hospitalInfo.getHospitalOpenStatus();
    }

    /**
     * 获取已开通产品
     * 按项目id查询
     *
     * @param projectInfoId 项目id
     * @return List<ProductInfo>
     */
    public List<ProductInfo> productOpened(Long projectInfoId) {
        List<ProductInfo> productInfos = applyOrderHospitalService.getProductInfoList(projectInfoId);
        return productOpened(productInfos);
    }

    /**
     * 通用, 产品是否开通
     *
     * @param productInfos 产品信息
     * @return boolean
     */
    public List<ProductInfo> productOpened(List<ProductInfo> productInfos) {
        List<ProductInfo> productInfoList =
                productInfos.stream().filter(ProjApplyOrderHospitalYunweiService::productOpened).collect(Collectors.toList());
        return filterOutBranchProduct(productInfoList);
    }

    /**
     * 通用, 产品是否开通
     *
     * @param productInfos 产品信息
     * @return boolean
     */
    public List<ProductInfo> orderProductNotOpen(List<ProductInfo> productInfos) {
        List<ProductInfo> productInfoList =
                productInfos.stream().filter(ProjApplyOrderHospitalYunweiService::productNotOpen).collect(Collectors.toList());
        return filterOutBranchProduct(productInfoList);
    }

    /**
     * 过滤调分院产品
     *
     * @param productInfoList 产品集合
     * @return 过滤后的产品
     */
    private List<ProductInfo> filterOutBranchProduct(List<ProductInfo> productInfoList) {
        List<RuleProductRuleConfig> ruleProductRuleConfigs =
                ruleProductRuleConfigService.findRuleProductRuleConfigForBranch();
        if (CollUtil.isNotEmpty(ruleProductRuleConfigs)) {
            productInfoList =
                    productInfoList.stream().filter(e -> ruleProductRuleConfigs.stream().noneMatch(f -> f.getYyProductId().longValue() == e.getYyOrderProductId())).collect(Collectors.toList());
        }
        return productInfoList;
    }

    /**
     * 通用, 产品开通
     *
     * @param orderProducts 产品信息
     * @return boolean
     */
    public static List<ProjOrderProduct> orderProductOpen(List<ProjOrderProduct> orderProducts) {
        if (CollUtil.isEmpty(orderProducts)) {
            return CollUtil.newArrayList();
        }
        return orderProducts.stream().filter(e -> {
            ProductInfo productInfo = new ProductInfo();
            productInfo.setProductOpenStatus(e.getProductOpenStatus());
            return productOpen(productInfo);
        }).collect(Collectors.toList());
    }

    /**
     * 通用, 产品是否开通
     *
     * @param projOrderProduct 产品信息
     * @return boolean
     */
    public static boolean productNotOpen(ProductInfo projOrderProduct) {
        return ProductOpenStatusEnum.NOT_OPEN.getCode() == projOrderProduct.getProductOpenStatus();
    }

    /**
     * 通用, 产品是否开通
     *
     * @param projOrderProduct 产品信息
     * @return boolean
     */
    public static boolean productOpened(ProductInfo projOrderProduct) {
        return ProductOpenStatusEnum.OPENED.getCode() == projOrderProduct.getProductOpenStatus();
    }

    /**
     * 通用, 产品是否开通
     *
     * @param projOrderProduct 产品信息
     * @return boolean
     */
    public static boolean productOpen(ProductInfo projOrderProduct) {
        return ProductOpenStatusEnum.OPENED.getCode() == projOrderProduct.getProductOpenStatus();
    }


    /**
     * 通用, 若医院处于未开通且域名为空时可进行开通
     *
     * @param hospitalInfo 医院信息
     * @return boolean
     */
    public static boolean hospitalOpen(ProjHospitalInfo hospitalInfo) {
        return (HospitalOpenStatusEnum.ONLINE.getCode() == hospitalInfo.getHospitalOpenStatus() || HospitalOpenStatusEnum.OPENED.getCode() == hospitalInfo.getHospitalOpenStatus()) && StrUtil.isNotBlank(hospitalInfo.getCloudDomain());
    }

    @Resource
    private ProjProjectSettlementCheckMainService mainService;

    @Transactional(rollbackFor = Throwable.class)
    public ApplyOrderHospitalDTO applyHospital(ProjHospitalInfoDTO dto, String applyOrderNum) {
        // 创建工单
        ProjApplyOrderDTO applyOrderDTO = new ProjApplyOrderDTO();
        applyOrderDTO.setApplyType(ProjApplyTypeEnum.HOSPITAL_APPLY.getCode());
        applyOrderDTO.setProjectInfoId(dto.getProjectInfoId());
        applyOrderDTO.setCustomInfoId(dto.getCustomInfoId());
        ProjApplyOrder applyOrder = applyOrderService.insertApplyOrder(applyOrderDTO, applyOrderNum);        // - 添加产品记录
        applyOrderService.addProductRecordsForHospitalApply(applyOrder);
        // - 查询要申请的未开通的医院
        List<ProjHospitalInfoRelative> hospitalInfoList =
                applyOrderHospitalService.getApplyOrderHospitals(dto.getProjectInfoId());
        List<ProjHospitalInfoRelative> hospitalInfoListNot = getNotOpenHospital(hospitalInfoList);
        if (CollUtil.isEmpty(hospitalInfoListNot)) {
            throw new RuntimeException("未查询到未开通的医院. projectInfoId: " + dto.getProjectInfoId());
        }
        List<ProjHospitalInfoRelative> hospitalInfoListOpen = getOpenHospital(hospitalInfoList);
        if (CollUtil.isEmpty(hospitalInfoListOpen)) {
            throw new RuntimeException("未查询到已开通的医院.");
        }

        // - 保存节点日志 - 提示下一步预计谁负责审核，电话是多少
        SysConfig config = sysConfigMapper.selectConfigByName("env_deployment_reviewer");
        SysUser sysUser = null;
        if (config != null) {
            try {
                List<SysUser> listUser = sysUserMapper.selectList(new QueryWrapper<SysUser>().eq("user_yunying_id", config.getConfigValue()).eq("is_deleted", 0));
                sysUser = listUser.get(0);
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        ProjHospitalInfoRelative hospitalInfoOpen = hospitalInfoListOpen.get(0);
        applyOrderService.insertApplyOrderHospitalRecord(hospitalInfoListNot, applyOrder);
        ApplyOrderNodeRecordParamDTO applyOrderNodeRecordParamDTO =
                ApplyOrderNodeRecordParamDTO.builder().refusedReason(StrUtil.EMPTY)
                        .rejectReason(StrUtil.EMPTY)
                        .operateContent(userUtil.getCurrentUserName() + ProjApplyOrderResultTypeEnum.APPLYED)
                        .telephone(StrUtil.EMPTY)
                        .operator(userUtil.getCurrentUserName())
                        .build();
        StringBuffer sb = new StringBuffer(";");
        if (ObjectUtil.isNotEmpty(sysUser.getUserName())) {
            sb.append("下一步审核人:").append(sysUser.getUserName()).append(";");
        }
        if (ObjectUtil.isNotEmpty(sysUser.getPhone())) {
            sb.append("联系电话:").append(sysUser.getPhone()).append(";");
        }
        applyOrderNodeRecordParamDTO.setOperateContent(applyOrderNodeRecordParamDTO.getOperateContent() + sb.toString());
        applyOrderService.addApplyOrderNodeRecord(applyOrder, applyOrderNodeRecordParamDTO);
        // - 更新域名信息
        // -- 查询域名, 查询主院
        ProjProjectInfo projectInfo = mainService.getProjectInfo(dto.getProjectInfoId());
        boolean isRegion = projectInfo.getProjectType() == ProjectTypeEnums.REGION.getCode().intValue();
        ProjHospitalInfo mainHospital = getMainHospital(projectInfo);
        // 更新老系统url相关信息
        applyOrderHospitalService.updateHospitalRelativeInfo(mainHospital, null, mainHospital.getCloudDomain(),
                hospitalInfoListNot.stream().map(ProjHospitalInfoRelative::getHospitalInfoId).collect(Collectors.toList()));
        // 拼接参数, 进行接口调用
        DeployModEnum deployModeEnum = DeployModEnum.getEnumByCode(projectInfo.getProjectType());
        assert deployModeEnum != null;
        DeployEnvApplyDTO applyDTO = getOnlineHospitalParam(deployModeEnum.getDevCode(), applyOrder, hospitalInfoOpen);
        // - 添加区县(内嵌医院)
        // 查询主院
        List<DeployResourceApplyCountryDTO> deployResourceApplyCountryDTOList =
                getApplyCountryList(hospitalInfoOpen.getCloudDomain(), projectInfo.getProjectType(),
                        hospitalInfoListNot, mainHospital);
        applyDTO.setDeployResourceApplyCountryDTOList(deployResourceApplyCountryDTOList);
        // 获取调用结果
        return new ApplyOrderHospitalDTO(isRegion, applyOrderService.getDepPlatformResult(applyDTO),
                hospitalInfoOpen, mainHospital);
    }

    /**
     * 获取主院
     *
     * @param projectInfo 项目
     * @return ProjHospitalInfo
     */
    public ProjHospitalInfo getMainHospital(ProjProjectInfo projectInfo) {
        List<ProjHospitalVsProjectType> hospitalVsProjectTypes =
                hospitalVsProjectTypeMapper.selectList(new QueryWrapper<ProjHospitalVsProjectType>().
                        eq("project_type", projectInfo.getProjectType()).eq("custom_info_id",
                                projectInfo.getCustomInfoId()));
        List<Long> hospitalInfoIds =
                hospitalVsProjectTypes.stream().map(ProjHospitalVsProjectType::getHospitalInfoId).collect(Collectors.toList());
        List<ProjHospitalInfo> projHospitalInfos =
                projHospitalInfoMapper.selectList(new QueryWrapper<ProjHospitalInfo>().in("hospital_info_id",
                        hospitalInfoIds).eq("health_bureau_flag", NumberEnum.NO_1.num()));
        if (CollUtil.isEmpty(projHospitalInfos)) {
            log.warn("未检测到主院. 查询已开通医院. projectInfoId: {}", projectInfo.getProjectInfoId());
            projHospitalInfos = findOpenedHospitalInfo(projectInfo.getProjectInfoId());
            if (CollUtil.isEmpty(projHospitalInfos)) {
                log.warn("未查询开通的医院 projectInfoId: {}", projectInfo.getProjectInfoId());
                throw new CustomException("未查询开通的医院.");
            }

        }
        if (projHospitalInfos.size() > 1) {
            log.warn("检测到有多个主院. projectType: {}, projectInfoId: {}", projectInfo.getProjectType(),
                    projectInfo.getProjectInfoId());
        }
        return projHospitalInfos.get(0);
    }

    /**
     * 拼接参数。获取区县集合
     *
     * @param cloudDomain           参数
     * @param projectType           参数
     * @param hospitalInfoRelatives 参数
     * @return List<DeployResourceApplyCountryDTO>
     */
    public List<DeployResourceApplyCountryDTO> getApplyCountryList(String cloudDomain, int projectType,
                                                                   List<ProjHospitalInfoRelative> hospitalInfoRelatives, ProjHospitalInfo mainHospital) {
        List<DeployResourceApplyCountryDTO> deployResourceApplyCountryDTOList = new ArrayList<>();
        DeployResourceApplyCountryDTO deployResourceApplyCountryDTO = new DeployResourceApplyCountryDTO();
        // 获取区划编码拼接
        String administrativeCode = splitRegionCode(StrUtil.toString(mainHospital.getProvinceId()),
                StrUtil.toString(mainHospital.getCityId()), StrUtil.toString(mainHospital.getTownId()));
        // 获取区划名称拼接
        String administrativeDivisions =
                hospitalInfoService.selectProvinceAndCityAndTownName(mainHospital.getProvinceId(),
                        mainHospital.getCityId(), mainHospital.getTownId());
        deployResourceApplyCountryDTO.setAdministrativeCode(administrativeCode);
        deployResourceApplyCountryDTO.setAdministrativeDivisions(administrativeDivisions);
        AtomicInteger bedCound = new AtomicInteger();
        AtomicInteger dayClinic = new AtomicInteger();
        AtomicInteger clinicCount = new AtomicInteger();
        AtomicInteger terminal = new AtomicInteger();
        for (ProjHospitalInfoRelative hospitalInfoRelative : hospitalInfoRelatives) {
            bedCound.addAndGet(ObjectUtil.isNotEmpty(hospitalInfoRelative.getHospitalBedCount())
                    ? hospitalInfoRelative.getHospitalBedCount().intValue() : 0);
            dayClinic.addAndGet(ObjectUtil.isNotEmpty(hospitalInfoRelative.getHospitalOutPatientCount())
                    ? hospitalInfoRelative.getHospitalOutPatientCount().intValue() : 0);
            clinicCount.addAndGet(ObjectUtil.isNotEmpty(hospitalInfoRelative.getClinicCount())
                    ? hospitalInfoRelative.getClinicCount() : 0);
            terminal.addAndGet(ObjectUtil.isNotEmpty(hospitalInfoRelative.getTerminalCount())
                    ? hospitalInfoRelative.getTerminalCount() : 0);
        }
        deployResourceApplyCountryDTO.setBedCount(bedCound.intValue());
        deployResourceApplyCountryDTO.setDayClinicCount(dayClinic.get());
        deployResourceApplyCountryDTO.setDomainName(cloudDomain);
        deployResourceApplyCountryDTO.setRemarks(StrUtil.EMPTY);
        deployResourceApplyCountryDTO.setClinicCount(clinicCount.get());
        deployResourceApplyCountryDTO.setTerminalCount(terminal.get());
        deployResourceApplyCountryDTO.setDeployResourceApplyHospitalDTOList(getApplyHospitalList(projectType,
                hospitalInfoRelatives, mainHospital));
        deployResourceApplyCountryDTOList.add(deployResourceApplyCountryDTO);
        return deployResourceApplyCountryDTOList;
    }

    /**
     * 拼接区划编码
     *
     * @param provinceId 省
     * @param cityId     市
     * @param townId     区
     * @return 拼接结果
     */
    private static String splitRegionCode(String provinceId, String cityId, String townId) {
        return provinceId + "/" + cityId + "/"
                + townId + "/";
    }

    /**
     * 拼接参数获取医院集合
     *
     * @param projectType           项目类型
     * @param hospitalInfoRelatives 医院
     * @return List<DeployResourceApplyHospitalDTO>
     */
    private List<DeployResourceApplyHospitalDTO> getApplyHospitalList(int projectType,
                                                                      List<ProjHospitalInfoRelative> hospitalInfoRelatives, ProjHospitalInfo mainHospital) {
        List<DeployResourceApplyHospitalDTO> deployResourceApplyHospitalDTOList = new ArrayList<>();
        for (ProjHospitalInfoRelative hospitalInfoRelative : hospitalInfoRelatives) {
            DeployResourceApplyHospitalDTO deployResourceApplyHospitalDTO = new DeployResourceApplyHospitalDTO();
            // 获取区划编码拼接
            String administrativeCode = splitRegionCode(StrUtil.toString(hospitalInfoRelative.getProvinceId()),
                    StrUtil.toString(hospitalInfoRelative.getCityId()),
                    StrUtil.toString(hospitalInfoRelative.getTownId()));
            // 获取区划名称拼接
            String administrativeDivisions =
                    hospitalInfoService.selectProvinceAndCityAndTownName(hospitalInfoRelative.getProvinceId(),
                            hospitalInfoRelative.getCityId(), hospitalInfoRelative.getTownId());
            deployResourceApplyHospitalDTO.setAdministrativeCode(administrativeCode);
            deployResourceApplyHospitalDTO.setAdministrativeDivisions(administrativeDivisions);
            deployResourceApplyHospitalDTO.setHospitalName(StrUtil.isEmpty(hospitalInfoRelative.getHospitalName())
                    ? StrUtil.EMPTY : hospitalInfoRelative.getHospitalName());
            String hospitalType;
            if (ProjectTypeEnums.REGION.getCode() == projectType) {
                hospitalType = HospitalTypeEnum.HOSPITAL.getDevCode();
            } else {
                hospitalType = HospitalTypeEnum.HospitalTypeBranchEnum.SINGLE_BRANCH.getDevCode();
                deployResourceApplyHospitalDTO.setMainHospitalId(mainHospital.getCloudHospitalId());
                deployResourceApplyHospitalDTO.setMainHospitalName(mainHospital.getHospitalName());
            }
            deployResourceApplyHospitalDTO.setHospitalType(hospitalType);
            deployResourceApplyHospitalDTO.setCustomerInfoId(hospitalInfoRelative.getHospitalInfoId());
            deployResourceApplyHospitalDTO.setHospitalId(hospitalInfoRelative.getCloudHospitalId());
            deployResourceApplyHospitalDTOList.add(deployResourceApplyHospitalDTO);
        }
        return deployResourceApplyHospitalDTOList;
    }

    /**
     * 拼接主参数
     *
     * @param applyOrder
     * @param hospitalInfo
     * @return
     */
    private DeployEnvApplyDTO getOnlineHospitalParam(String deployMode, ProjApplyOrder applyOrder,
                                                     ProjHospitalInfoRelative hospitalInfo) {
        // 查询医院信息
        ProjCustomInfo costomInfo = projCustomInfoMapper.selectOne(
                new QueryWrapper<ProjCustomInfo>().eq("custom_info_id", hospitalInfo.getCustomInfoId()));
        DeployEnvApplyDTO deployEnvApplyDTO = new DeployEnvApplyDTO();
        deployEnvApplyDTO.setDeployMod(deployMode);
        deployEnvApplyDTO.setDeployType(ProjApplyTypeEnum.HOSPITAL_APPLY.getDevCode());
        deployEnvApplyDTO.setEnvId(hospitalInfo.getEnvId());
        deployEnvApplyDTO.setEnvName(hospitalInfo.getEnvName());
        deployEnvApplyDTO.setDeliverPlatformApplyId(applyOrder.getApplyNum());
        deployEnvApplyDTO.setCountryCount(1);
        deployEnvApplyDTO.setCustomerId(costomInfo.getYyCustomerId());
        deployEnvApplyDTO.setCustomerName(costomInfo.getCustomName());
        deployEnvApplyDTO.setSubmitName(applyOrder.getApplicant());

        try {
            // 查询提交申请占用域名preSubmitId
            ProjProjectInfo projectInfo = projProjectInfoMapper.selectById(applyOrder.getProjectInfoId());
            // 查询该项目是否已提前申请过域名
            List<ProjCustomVsProjectTypeEntity> listType =
                    projCustomVsProjectTypeMapper.selectList(new QueryWrapper<ProjCustomVsProjectTypeEntity>()
                            .eq("custom_info_id", projectInfo.getCustomInfoId())
                            .eq("project_type", projectInfo.getProjectType()));
            if (CollUtil.isNotEmpty(listType) && listType.size() > 0) {
                deployEnvApplyDTO.setPreSubmitId(listType.get(0).getPreSubmitId());
            }
        } catch (Exception e) {
            log.error("提前获取域名===" + e.getMessage());
        }
        return deployEnvApplyDTO;
    }

    @Transactional(rollbackFor = Throwable.class)
    public ApplyOrderHospitalDTO applyExamHospital(ProjHospitalInfoExamDTO dto, String applyOrderNum, List<ProjHospitalInfoRelative> hospitalInfoList) {
        // 创建工单
        ProjApplyOrderDTO applyOrderDTO = new ProjApplyOrderDTO();
        applyOrderDTO.setApplyType(ProjApplyTypeEnum.HOSPITAL_APPLY.getCode());
        applyOrderDTO.setProjectInfoId(dto.getExamProjectInfoId());
        applyOrderDTO.setCustomInfoId(dto.getExamCustomInfoId());
        ProjApplyOrder applyOrder = applyOrderService.insertApplyOrder(applyOrderDTO, applyOrderNum);        // - 添加产品记录
        applyOrderService.addProductRecordsForHospitalApply(applyOrder);
        List<ProjHospitalInfoRelative> hospitalInfoListNot = getNotOpenHospital(hospitalInfoList);
        if (CollUtil.isEmpty(hospitalInfoListNot)) {
            throw new RuntimeException("未查询到未开通的医院. projectInfoId: " + dto.getExamProjectInfoId());
        }
        List<ProjHospitalInfoRelative> hospitalInfoListOpen = getOpenHospital(hospitalInfoList);
        if (CollUtil.isEmpty(hospitalInfoListOpen)) {
            throw new RuntimeException("未查询到已开通的医院.");
        }
        ProjHospitalInfoRelative hospitalInfoOpen = hospitalInfoListOpen.get(0);
        applyOrderService.insertApplyOrderHospitalRecord(hospitalInfoListNot, applyOrder);
        ApplyOrderNodeRecordParamDTO applyOrderNodeRecordParamDTO =
                ApplyOrderNodeRecordParamDTO.builder().refusedReason(StrUtil.EMPTY)
                        .rejectReason(StrUtil.EMPTY)
                        .operateContent(userUtil.getCurrentUserName() + ProjApplyOrderResultTypeEnum.APPLYED)
                        .telephone(StrUtil.EMPTY)
                        .operator(userUtil.getCurrentUserName())
                        .build();
        applyOrderService.addApplyOrderNodeRecord(applyOrder, applyOrderNodeRecordParamDTO);
        // - 更新域名信息
        // -- 查询域名, 查询主院
        ProjProjectInfo projectInfo = mainService.getProjectInfo(dto.getExamProjectInfoId());
        boolean isRegion = projectInfo.getProjectType() == ProjectTypeEnums.REGION.getCode().intValue();
        ProjHospitalInfo mainHospital = getMainHospital(projectInfo);
        // 更新老系统url相关信息
        applyOrderHospitalService.updateHospitalRelativeInfo(mainHospital, null, mainHospital.getCloudDomain(),
                hospitalInfoListNot.stream().map(ProjHospitalInfoRelative::getHospitalInfoId).collect(Collectors.toList()));
        // 拼接参数, 进行接口调用
        DeployModEnum deployModeEnum = DeployModEnum.getEnumByCode(projectInfo.getProjectType());
        assert deployModeEnum != null;
        DeployEnvApplyDTO applyDTO = getOnlineHospitalParam(deployModeEnum.getDevCode(), applyOrder, hospitalInfoOpen);
        // - 添加区县(内嵌医院)
        // 查询主院
        List<DeployResourceApplyCountryDTO> deployResourceApplyCountryDTOList =
                getApplyCountryList(hospitalInfoOpen.getCloudDomain(), projectInfo.getProjectType(),
                        hospitalInfoListNot, mainHospital);
        applyDTO.setDeployResourceApplyCountryDTOList(deployResourceApplyCountryDTOList);
        // 获取调用结果
        return new ApplyOrderHospitalDTO(isRegion, applyOrderService.getDepPlatformResult(applyDTO),
                hospitalInfoOpen, mainHospital);
    }
}
