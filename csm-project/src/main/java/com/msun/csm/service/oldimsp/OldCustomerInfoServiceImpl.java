package com.msun.csm.service.oldimsp;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.oldimsp.OldCustomerInfo;
import com.msun.csm.dao.mapper.oldimsp.OldCustomerInfoMapper;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/05/06/14:07
 */
@Service
public class OldCustomerInfoServiceImpl extends ServiceImpl<OldCustomerInfoMapper, OldCustomerInfo>
        implements OldCustomerInfoService {

    @Resource
    private OldCustomerInfoMapper oldCustomerInfoMapper;
    /**
     * 增加医院
     *
     * @param customerInfo
     * @return
     */
    @Override
    public Result addCustomerInfo(OldCustomerInfo customerInfo) {
        return this.save(customerInfo) ? Result.success() : Result.fail();
    }

    /**
     * 更新医院
     *
     * @param customerInfo
     * @return
     */
    @Override
    public Result updateCustomerInfo(OldCustomerInfo customerInfo) {
        return this.updateById(customerInfo) ? Result.success() : Result.fail();
    }

    /**
     * 删除医院
     *
     * @param customerInfo
     * @return
     */
    @Override
    public Result deleteCustomerInfo(OldCustomerInfo customerInfo) {
        return this.removeById(customerInfo) ? Result.success() : Result.fail();
    }

    /**
     * 查询医院
     *
     * @param customerInfo
     * @return
     */
    @Override
    public Result<OldCustomerInfo> selectCustomerInfo(OldCustomerInfo customerInfo) {
        return this.getById(customerInfo.getCustomerId()) != null
                ? Result.success(this.getById(customerInfo.getCustomerId())) : Result.fail();
    }

    /**
     * 根据条件查询医院信息
     *
     * @param customerInfo
     * @return
     */
    @Override
    public Result<List<OldCustomerInfo>> selectCustomerInfoList(OldCustomerInfo customerInfo) {
    /*    List<OldCustomerInfo> list = this.list(new QueryWrapper<OldCustomerInfo>()
                .eq(ObjectUtil.isNotEmpty(customerInfo.getCustomerName()), "customer_name", customerInfo.getCustomerName())
                .eq(ObjectUtil.isNotEmpty(customerInfo.getProjectId()), "project_id", customerInfo.getProjectId())
        );*/

        List<OldCustomerInfo> list = oldCustomerInfoMapper.selectListByParamer(customerInfo);
        return Result.success(list);
    }

    /**
     * 根据csm参数查询单个医院
     *
     * @param customerInfo
     * @return
     */
    @Override
    public Result<OldCustomerInfo> selectCustomerInfoByCsm(OldCustomerInfo customerInfo) {
        OldCustomerInfo oldCustomerInfo = this.getOne(new QueryWrapper<OldCustomerInfo>()
                .eq("project_id", customerInfo.getProjectId())
                .eq("csm_hospital_info_id", customerInfo.getCsmHospitalInfoId())
        );
        return Result.success(oldCustomerInfo);
    }
}
