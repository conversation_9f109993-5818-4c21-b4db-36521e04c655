package com.msun.csm.service.proj;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Name;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.imsp.dto.EquipmentStatusWrapperResult;
import com.msun.core.component.implementation.api.imsp.dto.ProductEquipmentDto;
import com.msun.core.component.implementation.filter.ImplHospitalDomainHolder;
import com.msun.csm.common.constants.DictEquipTypeConsts;
import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.enums.MilestoneNodeEnum;
import com.msun.csm.common.enums.ProductEquipSurveyMenuEnum;
import com.msun.csm.common.enums.ProjectPlanStatusEnum;
import com.msun.csm.common.enums.projproduct.ProjProductEnum;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.dict.DictProduct;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjEquipRecord;
import com.msun.csm.dao.entity.proj.ProjEquipRecordLog;
import com.msun.csm.dao.entity.proj.ProjEquipRecordVsEcg;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjMilestoneTask;
import com.msun.csm.dao.entity.proj.ProjMilestoneTaskDetail;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipRecordVsEcgMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneTaskDetailMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneTaskMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.model.dto.EquipRecordVsEcgExcelDTO;
import com.msun.csm.model.dto.ProjEquipRecordVsEcgDTO;
import com.msun.csm.model.dto.ProjEquipRecordVsEcgSelectDTO;
import com.msun.csm.model.dto.ProjEquipVsProductFinishDTO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.vo.DictEquipInfoVO;
import com.msun.csm.model.vo.EcgEquipSendCloudDataVO;
import com.msun.csm.model.vo.ProjEquipRecordEcgResultVO;
import com.msun.csm.model.vo.ProjEquipRecordVsEcgVO;
import com.msun.csm.service.dict.DictEquipCommService;
import com.msun.csm.util.DomainMapUtil;
import com.msun.csm.util.EasyExcelData;
import com.msun.csm.util.EasyExcelUtil;
import com.msun.csm.util.SnowFlakeUtil;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/12/17/14:36
 */
@Service
@Slf4j
public class ProjEquipRecordVsEcgServiceImpl implements ProjEquipRecordVsEcgService {

    private static ValidatorFactory validatorFactory = Validation.buildDefaultValidatorFactory();

    private static Validator validator = validatorFactory.getValidator();

    @Resource
    private ProjEquipRecordVsEcgMapper equipRecordVsEcgMapper;

    @Resource
    private ProjEquipRecordMapper equipRecordMapper;

    @Resource
    private DictEquipCommService dictEquipCommService;

    @Resource
    private ProjHospitalInfoMapper hospitalInfoMapper;

    @Resource
    private ImplHospitalDomainHolder domainHolder;

    @Resource
    private ProjEquipSummaryService equipSummaryService;

    @Resource
    private ProjMilestoneInfoService milestoneInfoService;

    @Resource
    private ProjMilestoneTaskMapper milestoneTaskMapper;

    @Resource
    private DictProductMapper productMapper;

    @Resource
    private ProjMilestoneTaskDetailMapper milestoneTaskDetailMapper;

    @Resource
    private ProjEquipRecordCommonService equipRecordCommonService;

    @Resource
    private ProjEquipRecordLogService projEquipRecordLogService;

    @Resource
    private ProjTodoTaskService projTodoTaskService;

    @Resource
    private ProjProjectInfoMapper projectInfoMapper;

    @Resource
    private ProjCustomInfoMapper customInfoMapper;

    /**
     * 查询列表数据
     *
     * @param dto
     * @return
     */
    @Override
    public Result<ProjEquipRecordEcgResultVO> selectEcgEquipData(ProjEquipRecordVsEcgSelectDTO dto) {
        //查询Pacs产品id
        DictProduct product = productMapper.selectOne(new QueryWrapper<DictProduct>()
                .eq("product_name", ProjProductEnum.ECG.getProductName())
                .last(" limit 1"));
        List<ProjEquipRecordVsEcgVO> projEquipRecordVsEcgVOS = equipRecordVsEcgMapper.selectEcgEquipData(dto);
        ProjEquipRecordEcgResultVO ecgResultVO = new ProjEquipRecordEcgResultVO();
        ecgResultVO.setProductId(product.getYyProductId());
        //数据处理
        for (ProjEquipRecordVsEcgVO vo : projEquipRecordVsEcgVOS) {
            vo.setEquipModelNameStr(vo.getEquipFactoryName() + "/" + vo.getEquipModelName());
            vo.setRequiredFlagBool(vo.getRequiredFlag() == 1);
            if (ObjectUtil.isNotEmpty(vo.getEquipStatus())) {
                if (vo.getEquipStatus() == 5) {
                    vo.setEquipStatusName("测试成功");
                } else if (vo.getEquipStatus() == 6) {
                    vo.setEquipStatusName("测试失败");
                } else {
                    vo.setEquipStatus(0);
                    vo.setEquipStatusName("未测试");
                }
            } else {
                vo.setEquipStatus(0);
                vo.setEquipStatusName("未测试");
            }
        }
        ecgResultVO.setRecordList(projEquipRecordVsEcgVOS);
        return Result.success(ecgResultVO);
    }

    /**
     * 根据id查询单个心电设备数据
     *
     * @param dto
     * @return
     */
    @Override
    public Result<ProjEquipRecordVsEcgVO> viewEquipToEcg(ProjEquipRecordVsEcgSelectDTO dto) {
        List<ProjEquipRecordVsEcgVO> projEquipRecordVsEcgVOS = equipRecordVsEcgMapper.selectEcgEquipData(dto);
        //数据处理
        for (ProjEquipRecordVsEcgVO vo : projEquipRecordVsEcgVOS) {
            vo.setEquipModelNameStr(vo.getEquipFactoryName() + "/" + vo.getEquipModelName());
            vo.setRequiredFlagBool(vo.getRequiredFlag() == 1);
        }
        return Result.success(projEquipRecordVsEcgVOS.get(0));
    }

    /**
     * 删除心电设备数据
     *
     * @param dto
     * @return
     */
    @Override
    public Result deleteEquipToEcg(ProjEquipRecordVsEcgSelectDTO dto) {
        ProjEquipRecordVsEcg projEquipRecordVsEcg = equipRecordVsEcgMapper.selectById(dto.getEquipRecordVsEcgId());
        equipRecordVsEcgMapper.deleteById(dto.getEquipRecordVsEcgId());
        ProjEquipRecord projEquipRecord = equipRecordMapper.selectById(projEquipRecordVsEcg.getEquipRecordId());
        equipRecordMapper.deleteById(projEquipRecordVsEcg.getEquipRecordId());
        equipRecordCommonService.updateEquipProjectTodoTaskStatus(projEquipRecord.getProjectInfoId(), projEquipRecord.getHospitalInfoId(), ProductEquipSurveyMenuEnum.ECGEQUIP.getYyProductId());
        return Result.success();
    }

    /**
     * 新增或修改心电设备数据
     *
     * @param dto
     * @return
     */
    @Override
    public Result saveOrUpdateEquipToEcg(ProjEquipRecordVsEcgDTO dto) {
        dto.setYyProductId(ProductEquipSurveyMenuEnum.ECGEQUIP.getYyProductId());
        //设备厂商
        dto.setEquipFactoryId(dictEquipCommService.getDictEquipByName(DictEquipTypeConsts.DICT_EQUIP_FACTORY,
                dto.getEquipFactoryName()));
        //设备型号
        dto.setEquipInfoId(dictEquipCommService.getDictEquipByName(DictEquipTypeConsts.DICT_EQUIP_INFO,
                dto.getEquipModelName()));

        ProjEquipRecord equipRecord = new ProjEquipRecord();
        BeanUtil.copyProperties(dto, equipRecord);
        // 判断是否存在id。存在id为更新
        if (ObjectUtil.isNotEmpty(dto.getEquipRecordVsEcgId())) {
            // 先更新公共表数据
            equipRecordMapper.updateById(equipRecord);
            // 更新心电子表数据
            ProjEquipRecordVsEcg projEquipRecordVsEcg = new ProjEquipRecordVsEcg();
            BeanUtil.copyProperties(dto, projEquipRecordVsEcg);
            equipRecordVsEcgMapper.updateById(projEquipRecordVsEcg);
        } else {
            // 先保存公共表数据
            equipRecord.setEquipRecordId(SnowFlakeUtil.getId());
            equipRecordMapper.insert(equipRecord);
            // 保存心电子表数据
            ProjEquipRecordVsEcg projEquipRecordVsEcg = new ProjEquipRecordVsEcg();
            BeanUtil.copyProperties(dto, projEquipRecordVsEcg);
            projEquipRecordVsEcg.setEquipRecordId(equipRecord.getEquipRecordId());
            projEquipRecordVsEcg.setEquipRecordVsEcgId(SnowFlakeUtil.getId());
            equipRecordVsEcgMapper.insert(projEquipRecordVsEcg);
            //保存操作日志
            ProjEquipRecordLog projEquipRecordLog = new ProjEquipRecordLog();
            projEquipRecordLog.setYyProductId(ProductEquipSurveyMenuEnum.ECGEQUIP.getYyProductId());
            projEquipRecordLog.setEquipRecordBusinessId(projEquipRecordVsEcg.getEquipRecordVsEcgId());
            projEquipRecordLog.setOperateName("申请设备");
            projEquipRecordLogService.saveLog(projEquipRecordLog);
        }
        equipRecordCommonService.updateEquipProjectTodoTaskStatus(equipRecord.getProjectInfoId(), equipRecord.getHospitalInfoId(), ProductEquipSurveyMenuEnum.ECGEQUIP.getYyProductId());
        return Result.success();
    }

    /**
     * 发送到云健康
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> ecgEquipSendCloudEquip(ProjEquipRecordVsEcgSelectDTO dto) {
        // 查询项目类型
        List<ProjHospitalInfo> hospitalInfoList = equipRecordCommonService.findHospitalInfos(dto.getCustomInfoId(),
                dto.getProjectInfoId());
        if (CollectionUtil.isEmpty(hospitalInfoList)) {
            return Result.fail("请检查客户下的医院信息");
        }
        if (ObjectUtil.isEmpty(hospitalInfoList.get(0).getCloudHospitalId())) {
            return Result.fail("该项目未部署,禁止对照");
        }
        ProjHospitalInfo hospitalInfo = hospitalInfoList.get(0);
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        //查询发送到云健康设备列列表
        List<ProductEquipmentDto> equipments = new ArrayList<>();
        List<EcgEquipSendCloudDataVO> ecgEquipSendCloudDataVOS =
                equipRecordVsEcgMapper.selectEcgEquipSendCloudData(dto);
        // 处理需要发送云健康的数据
        for (EcgEquipSendCloudDataVO d : ecgEquipSendCloudDataVOS) {
            ProductEquipmentDto productEquipmentDto = new ProductEquipmentDto();
            productEquipmentDto.setId(d.getId());
            productEquipmentDto.setHospitalId(d.getHospitalId());
            productEquipmentDto.setHisOrgId(d.getOrgId());
            productEquipmentDto.setProductCode(d.getProductCode());
            productEquipmentDto.setDeviceId(d.getDeviceId());
            productEquipmentDto.setDeviceName(d.getDeviceName());
            productEquipmentDto.setProductId(Integer.parseInt(ProductEquipSurveyMenuEnum.ECGEQUIP.getYyProductId().toString()));
            productEquipmentDto.setLocation(d.getEquipPosition());
            productEquipmentDto.setFirm(d.getFirm());
            equipments.add(productEquipmentDto);
        }
        // 发送云健康设备信息
        ResponseResult<String> responseResult = equipRecordCommonService.syncEquipment("ECG", hospitalInfo, equipments);
        if (responseResult.isSuccess()) {
            //更新设备的发送云健康状态, 以及自动更新云健康设备id
            for (Long equipRecordVsEcgId : dto.getEquipRecordVsEcgIdList()) {
                // 查询设备明细数据
                ProjEquipRecordVsEcg equipRecordVsEcg = equipRecordVsEcgMapper.selectById(equipRecordVsEcgId);
                // 修改设备的发送状态
                ProjEquipRecordVsEcg updateEcgEquip = new ProjEquipRecordVsEcg();
                updateEcgEquip.setEquipRecordVsEcgId(equipRecordVsEcgId);
                updateEcgEquip.setSendCloudFlag(1);
                int count = equipRecordVsEcgMapper.updateById(updateEcgEquip);
                log.info("心电设备发送云健康更新发送状态. count: {}, detail: {}", count, updateEcgEquip);
                // 以及云健康设备id、名称
                ProjEquipRecord projEquipRecord1 = equipRecordMapper.selectById(equipRecordVsEcg.getEquipRecordId());
                ProjEquipRecord projEquipRecord = new ProjEquipRecord();
                projEquipRecord.setCloudEquipId(equipRecordVsEcg.getEquipRecordVsEcgId());
                projEquipRecord.setCloudEquipName(projEquipRecord1.getEquipModelName());
                projEquipRecord.setEquipRecordId(projEquipRecord1.getEquipRecordId());
                equipRecordMapper.updateById(projEquipRecord);
                log.info("心电设备发送云健康更新发送状态. count: {}, detail: {}", count, updateEcgEquip);
                //保存操作日志
                ProjEquipRecordLog projEquipRecordLog = new ProjEquipRecordLog();
                projEquipRecordLog.setYyProductId(ProductEquipSurveyMenuEnum.ECGEQUIP.getYyProductId());
                projEquipRecordLog.setEquipRecordBusinessId(equipRecordVsEcgId);
                projEquipRecordLog.setOperateName("发送到云健康");
                projEquipRecordLogService.saveLog(projEquipRecordLog);
            }
        } else {
            return Result.fail("发送到云健康失败，失败原因：" + responseResult.getMessage());
        }
        return null;
    }

    /**
     * 心电设备一键检测
     *
     * @param dto
     * @return
     */
    @Override
    public Result<String> ecgEquipCheckForCloud(ProjEquipRecordVsEcgSelectDTO dto) {
        //查询设备记录(不包含已检测通过的记录)
        List<ProductEquipmentDto> equipmentDataDto = equipRecordVsEcgMapper.findToMsunEquipRecord(dto);
        if (CollectionUtil.isEmpty(equipmentDataDto)) {
            return Result.success();
        }

        // 有云健康设备id的设备（发送对照或从云健康手动对照）的正常走流程，没有云健康设备id的进行提示进行对照
        List<ProductEquipmentDto> equipmentData = equipmentDataDto.stream().filter(e -> ObjectUtil.isNotEmpty(e.getOldEquipId())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(equipmentData)) {
            // 查询当前客户下的主院数据
            List<ProjHospitalInfo> hospitalInfoList = equipRecordCommonService.findHospitalInfos(dto.getCustomInfoId(),
                    dto.getProjectInfoId());
            if (CollectionUtil.isEmpty(hospitalInfoList)) {
                return Result.fail("请检查客户下的医院信息");
            }
            ProjHospitalInfo hospitalInfo = hospitalInfoList.get(0);
            Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
            log.info("设定医院信息:{}", domainMap);
            domainHolder.refresh(domainMap);
            domainMap.clear();
            String productCode = ProjProductEnum.ECG.getProductCode();
            try {
                //调用接口进行验证
                Map<Long, EquipmentStatusWrapperResult> mapData = equipRecordCommonService.getEquipmentStatusBatch(
                        productCode,
                        hospitalInfo, equipmentData);
                log.info("设备检测返回结果: {}", mapData);
                if (MapUtil.isEmpty(mapData)) {
                    log.warn(productCode + "主动检测, " + "未查询到设备检测状态");
                    return Result.fail("未查询到设备检测状态.");
                }
                equipRecordCommonService.updateEquipStatus(mapData, hospitalInfoList, dto.getProjectInfoId(),
                        productCode, equipmentData);
                equipRecordCommonService.updateEquipProjectTodoTaskStatus(dto.getProjectInfoId(), null, ProductEquipSurveyMenuEnum.ECGEQUIP.getYyProductId());
            } catch (Exception e) {
                log.error("设备主动检测异常. message: {}, e=", e.getMessage(), e);
                return Result.fail(e.getMessage());
            }
        }

        // 未对照过云健康设备的进行提示
        List<ProductEquipmentDto> unSendCloudEquipData = equipmentDataDto.stream().filter(e -> ObjectUtil.isEmpty(e.getOldEquipId())).collect(Collectors.toList());
        StringBuilder sb = new StringBuilder();
        for (ProductEquipmentDto item : unSendCloudEquipData) {
            if (ObjectUtil.isEmpty(item.getOldEquipId())) {
                sb.append(item.getModel() + "未对照云健康设备 \n");
            }
        }
        if (sb.length() > 0) {
//            return Result.fail(sb.toString());
            return Result.fail(102, "存在未对照的设备，请进行云健康设备对照后进行检测");
        }
        return Result.success();
    }

    /**
     * 心电设备下载模版
     *
     * @param response
     * @param projectInfoId
     */
    @Override
    public void downloadTemplateForEcg(HttpServletResponse response, Long projectInfoId) {
        //查询客户名称
        ProjProjectInfo projectInfo = projectInfoMapper.selectById(projectInfoId);
        ProjCustomInfo customInfo = customInfoMapper.selectById(projectInfo.getCustomInfoId());
        // 创建新的Excel工作簿
        Workbook workbook = new XSSFWorkbook();
        // 创建一个工作表(sheet)
        Sheet sheet = workbook.createSheet("心电设备导入模版");
        // 创建标题行样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        // 创建带星号(*)的标题行字体颜色为红色
        CellStyle redFontStyle = workbook.createCellStyle();
        Font redFont = workbook.createFont();
        redFont.setColor(IndexedColors.RED.getIndex());
        redFont.setBold(true);
        redFontStyle.setFont(redFont);
        // 创建标题行
        Row headerRow = sheet.createRow(0);
        headerRow.setHeightInPoints(25);
        String[] headers = {"*设备型号", "*设备厂商", "*设备位置", "*来源医院", "*能否导出fda_xml文件", "*是否对接", "备注说明"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            if (headers[i].startsWith("*")) {
                cell.setCellStyle(redFontStyle);
            } else {
                cell.setCellStyle(headerStyle);
            }
        }
        // 设置下拉框的数据有效性
        DataValidationHelper helper = sheet.getDataValidationHelper();
        // 创建是否的下拉框区域，从第2-499行，第1列【设备型号】
        Result<List<DictEquipInfoVO>> equipInfoResult = equipSummaryService.selectSurveyEquipInfo("ECG", null);
        List<DictEquipInfoVO> equipInfoData = equipInfoResult.getData();
        if (CollectionUtil.isNotEmpty(equipInfoData)) {
            //创建隐藏sheet页，用来绑定超过50个的下拉项
            Sheet hidden1 = workbook.createSheet("hidden1");
            Cell cell = null;
            for (int i = 0; i < equipInfoData.size(); i++) {
                Row row = hidden1.createRow(i);
                cell = row.createCell(0);
                cell.setCellValue(equipInfoData.get(i).getEquipModelName());
            }
            Name name1 = workbook.createName();
            name1.setNameName("hidden1");
            name1.setRefersToFormula("hidden1!$A$1:$A$" + equipInfoData.size());
            DataValidationConstraint equipInfoConstraint = helper.createFormulaListConstraint(hidden1.getSheetName());
            workbook.setSheetHidden(1, true);
            CellRangeAddressList apiGroup = new CellRangeAddressList(1, 500, 0, 0);
            DataValidation apiGroupDataValidation = helper.createValidation(equipInfoConstraint, apiGroup);
            apiGroupDataValidation.setSuppressDropDownArrow(true);
            apiGroupDataValidation.createErrorBox("错误", "请从列表中选择一个选项。");
            apiGroupDataValidation.setShowErrorBox(true);
            sheet.addValidationData(apiGroupDataValidation);
        }
        // 创建是否的下拉框区域，从第2-499行，第2列【设备厂商】
        Result<List<BaseIdNameResp>> equipFactoryResult = equipSummaryService.selectSurveyEquipFactory("ECG", null);
        List<BaseIdNameResp> equipFactoryData = equipFactoryResult.getData();
        if (CollectionUtil.isNotEmpty(equipFactoryData)) {
            String[] equipFactoryListChoices = new String[equipFactoryData.size()];
            for (int i = 0; i < equipFactoryData.size(); i++) {
                equipFactoryListChoices[i] = equipFactoryData.get(i).getName();
            }
            CellRangeAddressList equipFactoryListAddress = new CellRangeAddressList(1, 500, 1, 1);
            DataValidation equipFactoryDataValidation =
                    helper.createValidation(helper.createExplicitListConstraint(equipFactoryListChoices),
                            equipFactoryListAddress);
            equipFactoryDataValidation.setSuppressDropDownArrow(true);
            equipFactoryDataValidation.createErrorBox("错误", "请从列表中选择一个选项。");
            equipFactoryDataValidation.setShowErrorBox(true);
            sheet.addValidationData(equipFactoryDataValidation);
        }
        // 创建是否的下拉框区域，从第2-499行，第4列【来源医院】
        SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
        selectHospitalDTO.setProjectInfoId(projectInfoId);
        List<ProjHospitalInfo> hospitalInfoList = hospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);
        if (CollectionUtil.isNotEmpty(hospitalInfoList)) {
            String[] hospitalInfoListChoices = new String[hospitalInfoList.size()];
            for (int i = 0; i < hospitalInfoList.size(); i++) {
                hospitalInfoListChoices[i] = hospitalInfoList.get(i).getHospitalName();
            }
            CellRangeAddressList hospitalInfoListAddress = new CellRangeAddressList(1, 500, 3, 3);
            DataValidation hospitalInfoDataValidation =
                    helper.createValidation(helper.createExplicitListConstraint(hospitalInfoListChoices),
                            hospitalInfoListAddress);
            hospitalInfoDataValidation.setSuppressDropDownArrow(true);
            hospitalInfoDataValidation.createErrorBox("错误", "请从列表中选择一个选项。");
            hospitalInfoDataValidation.setShowErrorBox(true);
            sheet.addValidationData(hospitalInfoDataValidation);
        }
        // 创建一个下拉框的区域，从第2-500行，第5-6列【能否导出fda_xml文件、是否对接】
        CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(1, 500, 4, 5);
        // 设置下拉框的数据有效性
        DataValidationConstraint constraint = helper.createExplicitListConstraint(new String[]{"是", "否"});
        DataValidation dataValidation = helper.createValidation(constraint, cellRangeAddressList);
        // 设置下拉框
        dataValidation.setSuppressDropDownArrow(true);
        dataValidation.createErrorBox("错误", "请从列表中选择一个选项。");
        dataValidation.setShowErrorBox(true);
        sheet.addValidationData(dataValidation);
        // 设置响应头信息
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode(customInfo.getCustomName() + "-心电设备导入模版.xlsx"));
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        // 写入到文件
        try {
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 导入模版数据
     *
     * @param multipartFile
     * @param customInfoId
     * @param projectInfoId
     * @return
     */
    @Override
    public Result importExcelDatas(MultipartFile multipartFile, Long customInfoId, Long projectInfoId) {
        // 确保上传的是Excel文件
        if (!multipartFile.getOriginalFilename().endsWith(".xlsx")) {
            throw new IllegalArgumentException("导入失败,请选择.xlsx格式的Excel文件");
        }
        try {
            EasyExcelData easyExcelData =
                    EasyExcelUtil.readExcelWithModel(multipartFile.getInputStream(), EquipRecordVsEcgExcelDTO.class);
            if (easyExcelData.getDatas() instanceof List<?>) {
                if (easyExcelData.getDatas().isEmpty()) {
                    return Result.fail("导入失败,请检查导入文件是否存在数据");
                }
            }
            //导入Excel数据
            Result result = saveDatas(easyExcelData.getDatas(), customInfoId, projectInfoId);
            return result;
        } catch (IOException e) {
            log.error("发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("导入失败，失败信息：" + e.getMessage());
        }
    }

    /**
     * 保存模版导入数据
     *
     * @param dtoList
     * @param customInfoId
     * @param projectInfoId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result saveDatas(List<Object> dtoList, Long customInfoId, Long projectInfoId) {
        for (Object dto : dtoList) {
            EquipRecordVsEcgExcelDTO excelDTO = (EquipRecordVsEcgExcelDTO) dto;
            //检验数据中必填选项
            Set<ConstraintViolation<EquipRecordVsEcgExcelDTO>> violations = validator.validate(excelDTO);
            if (!violations.isEmpty()) {
                return Result.fail("导入失败，Excel中存在必填的选项为空，请检查标题前带*的内容是否填写完整！");
            }
            //封装设备记录实体
            ProjEquipRecord projEquipRecord = new ProjEquipRecord();
            BeanUtil.copyProperties(excelDTO, projEquipRecord);
            //查询医院id
            ProjHospitalInfo hospitalInfo = hospitalInfoMapper.selectOne(new QueryWrapper<ProjHospitalInfo>()
                    .eq("hospital_name", excelDTO.getHospitalName()).last("limit 1"));
            if (ObjectUtil.isEmpty(hospitalInfo)) {
                return Result.fail("导入失败,《" + excelDTO.getHospitalName() + "》在系统中未匹配到对应医院，请核对数据！");
            }
            if (!customInfoId.equals(hospitalInfo.getCustomInfoId())) {
                return Result.fail("导入失败,项目下不存在《" + excelDTO.getHospitalName() + "》，请核对数据！");
            }
            projEquipRecord.setHospitalInfoId(hospitalInfo.getHospitalInfoId());
            //设备厂商
            projEquipRecord.setEquipFactoryId(dictEquipCommService.getDictEquipByName(DictEquipTypeConsts.DICT_EQUIP_FACTORY, projEquipRecord.getEquipFactoryName()));
            //设备型号
            projEquipRecord.setEquipInfoId(dictEquipCommService.getDictEquipByName(DictEquipTypeConsts.DICT_EQUIP_INFO, projEquipRecord.getEquipModelName()));
            //产品ID
            projEquipRecord.setYyProductId(ProductEquipSurveyMenuEnum.ECGEQUIP.getYyProductId());
            //是否对接
            projEquipRecord.setRequiredFlag("是".equals(excelDTO.getRequiredFlagExcel()) ? 1 : 0);
            // 设备位置
            projEquipRecord.setEquipPosition(excelDTO.getEquipPositionName());
            //保存设备记录信息
            projEquipRecord.setEquipRecordId(SnowFlakeUtil.getId());
            projEquipRecord.setCustomInfoId(customInfoId);
            projEquipRecord.setProjectInfoId(projectInfoId);
            projEquipRecord.setYyProductId(ProductEquipSurveyMenuEnum.AIMSEQUIP.getYyProductId());
            equipRecordMapper.insert(projEquipRecord);
            //保存心电设备记录
            ProjEquipRecordVsEcg projEquipRecordVsEcg = new ProjEquipRecordVsEcg();
            projEquipRecordVsEcg.setEquipRecordVsEcgId(SnowFlakeUtil.getId());
            projEquipRecordVsEcg.setEquipRecordId(projEquipRecord.getEquipRecordId());
            projEquipRecordVsEcg.setExportFdaFlag("是".equals(excelDTO.getExportFdaFlag()) ? 1 : 0);
            equipRecordVsEcgMapper.insert(projEquipRecordVsEcg);
        }
        projTodoTaskService.todoTaskTotalCountSync(projectInfoId, DictProjectPlanItemEnum.SURVEY_DEVICE.getPlanItemCode());
        projTodoTaskService.todoTaskTotalCountSync(projectInfoId, DictProjectPlanItemEnum.PREPARAT_DEVICE.getPlanItemCode());
        return Result.success();
    }

    /**
     * 导出数据
     *
     * @param dto
     * @param response
     */
    @Override
    public void exportExcelDatas(ProjEquipRecordVsEcgSelectDTO dto, HttpServletResponse response) {
        List<ProjEquipRecordVsEcgVO> aimsVOList = equipRecordVsEcgMapper.selectEcgEquipData(dto);
        // 创建新的Excel工作簿
        Workbook workbook = new XSSFWorkbook();
        // 创建一个工作表(sheet)
        Sheet sheet = workbook.createSheet("心电设备记录");
        // 创建标题行
        Row headerRow = sheet.createRow(0);
        headerRow.setHeightInPoints(25);
        headerRow.createCell(0).setCellValue("设备型号");
        headerRow.createCell(1).setCellValue("设备厂商");
        headerRow.createCell(2).setCellValue("设备位置");
        headerRow.createCell(3).setCellValue("医院名称");
        headerRow.createCell(4).setCellValue("能否导出fda文件");
        headerRow.createCell(5).setCellValue("是否对接");
        headerRow.createCell(6).setCellValue("备注说明");
        // 创建样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        headerStyle.setFont(font);
        for (Cell headerCell : headerRow) {
            headerCell.setCellStyle(headerStyle);
        }
        //填充数据
        for (int i = 0; i < aimsVOList.size(); i++) {
            ProjEquipRecordVsEcgVO ecgVO = aimsVOList.get(i);
            Row rowData = sheet.createRow(i + 1);
            rowData.createCell(0).setCellValue(ecgVO.getEquipModelName());
            rowData.createCell(1).setCellValue(ecgVO.getEquipFactoryName());
            rowData.createCell(2).setCellValue(ecgVO.getEquipPosition());
            rowData.createCell(3).setCellValue(ecgVO.getHospitalInfoName());
            rowData.createCell(4).setCellValue(ecgVO.getExportFdaFlag() == 1 ? "是" : "否");
            rowData.createCell(5).setCellValue(ecgVO.getRequiredFlag() == 1 ? "是" : "否");
            rowData.createCell(6).setCellValue(ecgVO.getMemo());
        }
        // 设置响应头信息
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode("心电设备记录.xlsx"));
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        // 写入到文件
        try {
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 提交完成
     *
     * @param dto
     * @return
     */
    @Override
    public Result commitFinish(ProjEquipVsProductFinishDTO dto) {
        //校验是否可以提交完成
        // 判断节点类型【调研阶段、准备阶段】
        Result<ProjMilestoneInfo> milestoneInfoResult = milestoneInfoService.selectById(dto.getMilestoneInfoId());
        String milestoneNodeCode = milestoneInfoResult.getData().getMilestoneNodeCode();
        Long projectInfoId1 = milestoneInfoResult.getData().getProjectInfoId();
        String nodeCode = "";
        if (MilestoneNodeEnum.SURVEY_PRODUCT.getCode().equals(milestoneNodeCode)) {
            nodeCode = MilestoneNodeEnum.SURVEY_DEVICE.getCode();
        } else if (MilestoneNodeEnum.PREPARAT_PRODUCT.getCode().equals(milestoneNodeCode)) {
            List<Integer> statusList = Lists.newArrayList();
            statusList.add(5);
            Integer count = equipRecordVsEcgMapper.getNotApplyRecordCount(statusList, projectInfoId1);
            if (count > 0) {
                return Result.fail("存在需要对接的设备未测试通过,请全部测试通过后再进此操作");
            }
            nodeCode = MilestoneNodeEnum.PREPARAT_DEVICE.getCode();
        }
        //更新任务计划明细状态
        List<ProjMilestoneTask> projMilestoneTasks =
                milestoneTaskMapper.selectList(new QueryWrapper<ProjMilestoneTask>()
                        .eq("project_info_id", milestoneInfoResult.getData().getProjectInfoId())
                        .eq("milestone_node_code", nodeCode)
                );
        if (CollectionUtil.isEmpty(projMilestoneTasks)) {
            return Result.fail("请先分配产品业务调研后再进行提交完成");
        }
        //查询心电产品id
        DictProduct product = productMapper.selectOne(new QueryWrapper<DictProduct>().eq("product_name",
                ProjProductEnum.ECG.getProductName()).last(" limit 1"));
        List<Long> collect =
                projMilestoneTasks.stream().map(ProjMilestoneTask::getMilestoneTaskId).collect(Collectors.toList());
        List<ProjMilestoneTaskDetail> projMilestoneTaskDetails =
                milestoneTaskDetailMapper.selectList(new QueryWrapper<ProjMilestoneTaskDetail>()
                        .eq("product_deliver_id", product.getYyProductId())
                        .in("milestone_task_id", collect)
                );
        if (CollectionUtil.isEmpty(projMilestoneTaskDetails)) {
            return Result.fail(MilestoneNodeEnum.SURVEY_PRODUCT.getCode().equals(milestoneNodeCode)
                    ? "心电产品未查询到对应任务,请在产品调研重新指定责任人" : "心电产品未查询到对应任务");
        }
        for (ProjMilestoneTaskDetail taskDetail : projMilestoneTaskDetails) {
            taskDetail.setCompleteStatus(1);
            milestoneTaskDetailMapper.updateById(taskDetail);
        }
        projTodoTaskService.updateTodoTaskStatusOne(projectInfoId1, DictProjectPlanItemEnum.SURVEY_DEVICE, product.getYyProductId(), ProjectPlanStatusEnum.FINISHED);
        return Result.success();
    }
}
