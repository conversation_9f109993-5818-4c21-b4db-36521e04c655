package com.msun.csm.service.dict;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.dict.DictCity;
import com.msun.csm.dao.mapper.dict.DictCityMapper;
import com.msun.csm.model.convert.DictCityConvert;
import com.msun.csm.model.dto.DictCityDTO;
import com.msun.csm.model.vo.DictCityVO;

import cn.hutool.core.util.ObjectUtil;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/25
 */

@Service
public class DictCityServiceImpl implements DictCityService {

    @Resource
    private DictCityMapper dictCityMapper;

    @Resource
    private DictCityConvert convert;

    @Override
    public int deleteByPrimaryKey(Long dictCityId) {
        return dictCityMapper.deleteByPrimaryKey(dictCityId);
    }

    @Override
    public int insert(DictCity record) {
        return dictCityMapper.insert(record);
    }

    @Override
    public int insertOrUpdate(DictCity record) {
        return dictCityMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(DictCity record) {
        return dictCityMapper.insertOrUpdateSelective(record);
    }

    @Override
    public int insertSelective(DictCity record) {
        return dictCityMapper.insertSelective(record);
    }

    @Override
    public DictCity selectByPrimaryKey(Long dictCityId) {
        return dictCityMapper.selectByPrimaryKey(dictCityId);
    }

    @Override
    public int updateByPrimaryKeySelective(DictCity record) {
        return dictCityMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(DictCity record) {
        return dictCityMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<DictCity> list) {
        return dictCityMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<DictCity> list) {
        return dictCityMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<DictCity> list) {
        return dictCityMapper.batchInsert(list);
    }

    /**
     * 城市列表查询
     *
     * @param dto
     * @return
     */
    @Override
    public Result<List<DictCityVO>> selectCityList(DictCityDTO dto) {
        List<DictCity> dictCities = dictCityMapper.selectList(new QueryWrapper<DictCity>()
                .eq(ObjectUtil.isNotEmpty(dto.getDictProvinceId()), "dict_province_id", dto.getDictProvinceId())
        );
        List<DictCityVO> dictCityVOS = convert.po2Vo(dictCities);
        return Result.success(dictCityVOS);
    }
}
