package com.msun.csm.service.proj.disastercloud;

import static com.msun.csm.service.proj.disastercloud.IDocVarConst.VAR_CUSTOM_NAME;
import static com.msun.csm.service.proj.disastercloud.IDocVarConst.VAR_END_DAY;
import static com.msun.csm.service.proj.disastercloud.IDocVarConst.VAR_END_MONTH;
import static com.msun.csm.service.proj.disastercloud.IDocVarConst.VAR_END_YEAR;
import static com.msun.csm.service.proj.disastercloud.IDocVarConst.VAR_START_DAY;
import static com.msun.csm.service.proj.disastercloud.IDocVarConst.VAR_START_MONTH;
import static com.msun.csm.service.proj.disastercloud.IDocVarConst.VAR_START_YEAR;
import static com.msun.csm.service.proj.disastercloud.IDocVarConst.VAR_TERM_MONTH;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.io.FileUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.cloudservice.CloudServiceKit;
import com.msun.csm.common.enums.DisasterRecoveryApplyStatusEnum;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.api.yunying.OrderTypeEnums;
import com.msun.csm.common.enums.could.CloudPhaseTypeEnum;
import com.msun.csm.common.enums.sysfile.SysFileEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysFile;
import com.msun.csm.dao.entity.proj.ProjContractInfo;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjDisasterRecoveryApply;
import com.msun.csm.dao.entity.proj.ProjDisasterRecoveryApplyLog;
import com.msun.csm.dao.entity.proj.ProjDisasterRecoveryInfo;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.extend.ProjProjectFileExtend;
import com.msun.csm.dao.mapper.proj.ProjContractInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjDisasterRecoveryApplyLogMapper;
import com.msun.csm.dao.mapper.proj.ProjDisasterRecoveryApplyMapper;
import com.msun.csm.dao.mapper.proj.ProjDisasterRecoveryInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectFileMapper;
import com.msun.csm.dao.mapper.sysfile.SysFileMapper;
import com.msun.csm.feign.entity.yunying.enums.OrderStepEnum;
import com.msun.csm.model.dto.yunweiplatform.YunweiDisasterCloudSyncTimeDTO;
import com.msun.csm.model.imsp.HospitalUpdateStatusAndProductDeployDTO;
import com.msun.csm.model.imsp.SyncYunyingApplicationDTO;
import com.msun.csm.model.req.projectfile.UploadFileReq;
import com.msun.csm.model.yunying.SyncDisasterRecoveryCheckDTO;
import com.msun.csm.service.api.ApiYunyingService;
import com.msun.csm.service.common.BaseYunweiApplySaveApplyHistory;
import com.msun.csm.service.common.CommonService;
import com.msun.csm.service.common.ExceptionMessageService;
import com.msun.csm.service.operlog.SysOperLogService;
import com.msun.csm.service.proj.ProjProjectFileService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderServiceImpl;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.doc.BaseDocUtils;
import com.msun.csm.util.obs.OBSClientUtils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2024-10-11 08:39:18
 */
@Slf4j
@Service
public class ProjDisasterRecoveryApplyServiceImpl extends BaseYunweiApplySaveApplyHistory<ProjDisasterRecoveryApply> implements ProjDisasterRecoveryApplyService {
    private static final String PROJECT_DISASTERCLOUD = "project_disasterCloud";
    /**
     * 文件名称
     */
    private static final String CONFIRMATION_FILE_NAME = "云容灾交付确认函.docx";

    @Resource
    private ProjDisasterRecoveryDocService recoveryDocService;

    @Value("${project.obs.bucketName}")
    private String bucketName;

    @Value("${project.obs.prePath}")
    private String prePath;

    @Resource
    private SysFileMapper sysFileMapper;

    @Resource
    private CommonService commonService;

    @Resource
    private ProjProjectFileService projectFileService;

    @Resource
    private ProjProjectFileMapper projectFileMapper;

    @Resource
    private ProjContractInfoMapper contractInfoMapper;

    @Resource
    private ProjDisasterRecoveryApplyLogMapper applyLogMapper;

    @Resource
    private ProjDisasterRecoveryApplyMapper disasterRecoveryApplyMapper;

    @Resource
    private ProjDisasterRecoveryInfoMapper recoveryInfoMapper;

    @Resource
    private ExceptionMessageService exceptionMessageService;

    @Resource
    private ProjDisasterRecoveryInfoMapper disasterRecoveryInfoMapper;

    @Resource
    private SysOperLogService sysOperLogService;

    @Resource
    private DisasterCloudCommonService disasterCloudCommonService;

    @Resource
    private ApiYunyingService apiYunyingService;


    @Override
    public Result<String> syncDisasterRecoveryCheck(SyncDisasterRecoveryCheckDTO dto) {
        // 根据合同id查询容灾工单
        Long contractNum = dto.getContractNum();
        ProjContractInfo contractInfo = contractInfoMapper.selectOne(new QueryWrapper<ProjContractInfo>().eq(
                "yy_contract_id",
                contractNum));
        try {
            if (ObjectUtil.isEmpty(contractInfo)) {
                sysOperLogService.apiOperLogInsertObjAry("运营回调交付平台同步云容灾验收状态. ", "异常",
                        Log.LogOperType.ADD.getCode(), "未查询到云容灾合同信息", dto);
                log.warn("未查询到云容灾合同信息. dto:{}", dto);
                return Result.fail("未查询到云容灾合同信息.");
            }
            // 根据合同查询云容灾
            ProjDisasterRecoveryInfo recoveryInfo =
                    disasterRecoveryInfoMapper.selectOne(new QueryWrapper<ProjDisasterRecoveryInfo>().eq(
                            "contract_info_id", contractInfo.getContractInfoId()).eq("yy_order_id",
                            Long.parseLong(dto.getYyProjId())));
            if (ObjectUtil.isEmpty(recoveryInfo)) {
                sysOperLogService.apiOperLogInsertObjAry("运营回调交付平台同步云容灾验收状态. ", "异常",
                        Log.LogOperType.ADD.getCode(), "未查询到云容灾台账信息", dto);
                log.warn("未查询到云容灾台账信息. dto:{}", dto);
                return Result.fail("未查询到云容灾台账信息.");
            }
            // 查询申请单
            ProjDisasterRecoveryApply apply =
                    disasterRecoveryApplyMapper.selectOne(new QueryWrapper<ProjDisasterRecoveryApply>().eq(
                            "proj_disaster_recovery_info_id", recoveryInfo.getProjDisasterRecoveryInfoId()));
            if (ObjectUtil.isEmpty(recoveryInfo)) {
                sysOperLogService.apiOperLogInsertObjAry("运营回调交付平台同步云容灾验收状态. ", "异常",
                        Log.LogOperType.ADD.getCode(), "未查询到云容灾申请单信息", dto);
                log.warn("未查询到云容灾申请单信息. dto:{}", dto);
                return Result.fail("未查询到云容灾申请单信息.");
            }
            // 插入日志
            DisasterRecoveryApplyStatusEnum resultType;
            if (dto.getCode() == NumberEnum.NO_1.num().intValue()) {
                resultType = DisasterRecoveryApplyStatusEnum.CHECK_APPLYED;
            } else {
                resultType = DisasterRecoveryApplyStatusEnum.CHECK_REJECTED;
            }
            // 添加工单日志
            ProjDisasterRecoveryApplyLog applyLog = ProjDisasterRecoveryApplyLog.builder()
                    .projDisasterRecoveryApplyLogId(SnowFlakeUtil.getId())
                    .projDisasterRecoveryApplyId(apply.getProjDisasterRecoveryApplyId())
                    .operateTime(new Date())
                    .operateType(resultType.getCode())
                    .operaterName("运营部")
                    .operateResult(DisasterRecoveryApplyStatusEnum.APPLYED.getDesc())
                    .build();
            int count = applyLogMapper.insert(applyLog);
            log.info("运营验收同步云容灾申请单, 新增日志. count: {}", count);
            // 更新申请单
            ProjDisasterRecoveryApply updateApply = new ProjDisasterRecoveryApply();
            updateApply.setCheckUser(dto.getUserName());
            updateApply.setCheckTime(new Date());
            updateApply.setStatus(resultType.getCode());
            updateApply.setProjDisasterRecoveryApplyId(apply.getProjDisasterRecoveryApplyId());
            count = disasterRecoveryApplyMapper.updateById(updateApply);
            log.info("运营验收同步云容灾申请单, 更新申请单. count: {}", count);
        } catch (Throwable e) {
            log.error("云容灾运营向交付同步验收状态异常. message: {}, e=", e.getMessage(), e);
            exceptionMessageService.sendToSystemManager(-1L, "合同号:" + contractNum + StrUtil.DASHED
                    + "云容灾运营向交付同步验收状态异常.");
            throw new CustomException("云容灾运营向交付同步验收状态异常.");
        }
        return Result.success();
    }

    @Override
    public Result<String> handleAudit(ProjDisasterRecoveryApply applyObj, HospitalUpdateStatusAndProductDeployDTO dto) {
        // 更新工单
        updateApplyInfo(applyObj, dto, DisasterRecoveryApplyStatusEnum.AUDITED);
        ProjDisasterRecoveryInfo disasterRecoveryInfo =
                disasterCloudCommonService.getDisasterRecoveryInfo(applyObj.getProjDisasterRecoveryInfoId());
        // 同步部署状态
        disasterCloudCommonService.updateNodeStatus(disasterRecoveryInfo.getOrderInfoId(),
                OrderStepEnum.DISASTER_CLOUD_DEPLOY);
        return Result.success();
    }

    @Override
    protected Result<String> handleRejected(ProjDisasterRecoveryApply applyObj,
                                            HospitalUpdateStatusAndProductDeployDTO dto) {
        updateApplyInfo(applyObj, dto, DisasterRecoveryApplyStatusEnum.REJECTED);
        return Result.success();
    }

    @Override
    protected Result<String> handleDeployed(ProjDisasterRecoveryApply applyObj,
                                            HospitalUpdateStatusAndProductDeployDTO dto) {
        updateApplyInfo(applyObj, dto, DisasterRecoveryApplyStatusEnum.DEPLOYED);
        // 更新状态
        ProjDisasterRecoveryInfo recoveryInfo =
                disasterCloudCommonService.getDisasterRecoveryInfo(applyObj.getProjDisasterRecoveryInfoId());
        disasterCloudCommonService.updateNodeStatus(recoveryInfo.getOrderInfoId(), OrderStepEnum.DISASTER_CLOUD_DEPLOY);
        return Result.success();
    }

    @Override
    protected Result<String> handleDeploying(ProjDisasterRecoveryApply applyObj,
                                             HospitalUpdateStatusAndProductDeployDTO dto) {
        updateApplyInfo(applyObj, dto, DisasterRecoveryApplyStatusEnum.DEPLOYING);
        return Result.success();
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Result<String> handleDelivered(ProjDisasterRecoveryApply applyObj,
                                          HospitalUpdateStatusAndProductDeployDTO dto) {
        // 更新申请单, 添加日志
        updateApplyInfo(applyObj, dto, DisasterRecoveryApplyStatusEnum.DELIVERED);
        Date startDate = new Date();
        ProjDisasterRecoveryInfo recoveryInfo =
                disasterRecoveryInfoMapper.selectOne(new QueryWrapper<ProjDisasterRecoveryInfo>().eq(
                        "proj_disaster_recovery_info_id", applyObj.getProjDisasterRecoveryInfoId()));
        Date endTime = CloudServiceKit.getEndTime(startDate, recoveryInfo.getServiceTerm().longValue());
        // 更新云容灾
        ProjDisasterRecoveryInfo updateRecoverInfo = ProjDisasterRecoveryInfo.builder()
                .projDisasterRecoveryInfoId(applyObj.getProjDisasterRecoveryInfoId())
                .deployManufactor(dto.getDeployCloudVendor())
                .msunPayFlag(dto.getIsMsunPay())
                .destEnvId(dto.getDestEnvId())
                .destEnvName(dto.getDestEnvName())
                .originalEnvId(dto.getOriginalEnvId())
                .originalEnvName(dto.getOriginalEnvName())
                .startTime(startDate)
                .endTime(endTime)
                .build();
        // 同步运维平台到期时间
        int count = recoveryInfoMapper.updateById(updateRecoverInfo);
        log.info("更新云容灾台账. count: {}", count);
        // 生成确认涵
        ProjProjectFile projectFile = createConfirmationFile(applyObj);
        // 更新云容灾确认涵地址
        updateRecoverInfo = ProjDisasterRecoveryInfo.builder()
                .projDisasterRecoveryInfoId(applyObj.getProjDisasterRecoveryInfoId())
                .confirmationFileId(projectFile.getProjectFileId())
                .build();
        // 同步运维平台到期时间
        count = recoveryInfoMapper.updateById(updateRecoverInfo);
        log.info("更新云容灾台账群人涵地址. count: {}", count);
        // 重新获取云容灾台账信息
        recoveryInfo = disasterCloudCommonService.getDisasterRecoveryInfo(applyObj.getProjDisasterRecoveryInfoId());
        ProjContractInfo contractInfo = commonService.getContractInfo(recoveryInfo.getContractInfoId());
        SyncYunyingApplicationDTO yunApplicationDTO = SyncYunyingApplicationDTO.builder()
                .contractType(contractInfo.getContractType())
                .yyWoId(recoveryInfo.getYyOrderId())
                .pemcusssolType(recoveryInfo.getSolutionType())
                .phaseType(CloudPhaseTypeEnum.FIRST.getCode())
                .productType(StrUtil.toString(OrderTypeEnums.DISASTER_RECOVERY.getCode()))
                .build();
        // 同步运营云容灾资源附件信息
        commonService.syncIsMsunCloudDisRecover(recoveryInfo, dto.getDeployCloudVendor(),
                StrUtil.toString(dto.getIsMsunPay()));
        // 同步运营平台到期时间
        apiYunyingService.sendTimeToYunYingCloudTime(startDate, startDate, yunApplicationDTO);
        // 更新运营平台工单状态
        disasterCloudCommonService.updateNodeStatus(recoveryInfo.getOrderInfoId(), OrderStepEnum.DISASTER_CLOUD_OPEN);
        // 同步运维平台时间
        YunweiDisasterCloudSyncTimeDTO yunweiDisasterCloudSyncTimeDTO = YunweiDisasterCloudSyncTimeDTO.builder()
                .domainName(recoveryInfo.getCloudDomain())
                .destHome(recoveryInfo.getDestEnvName())
                .originalHome(recoveryInfo.getOriginalEnvName())
                .endTime(DateUtil.formatDateTime(recoveryInfo.getEndTime()))
                .startTime(DateUtil.formatDateTime(recoveryInfo.getStartTime()))
                .build();
        ProjCustomInfo customInfo = commonService.getCustomInfo(recoveryInfo.getCustomInfoId());
        commonService.callYunweiSyncEndTime(yunweiDisasterCloudSyncTimeDTO, updateRecoverInfo, customInfo);
        return Result.success();
    }

    /**
     * 生成确认函
     *
     * @param applyObj 工单
     */
    public ProjProjectFile createConfirmationFile(ProjDisasterRecoveryApply applyObj) {
        String tmpPath = createConfirmationFileImpl(applyObj);
        // 查询容灾台账
        ProjProjectFileExtend projectFile;
        String fileNameDesc = "云容灾交付确认函";
        // 替换模板参数
        String downloadFileName = fileNameDesc + ".docx";
        String obskey = OBSClientUtils.getObsProjectPath(prePath, PROJECT_DISASTERCLOUD,
                StrUtil.toString(applyObj.getProjDisasterRecoveryInfoId())) + downloadFileName;
        try {
            OBSClientUtils.uploadFile(new File(tmpPath), obskey);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            try {
                FileUtils.forceDelete(new File(tmpPath));
            } catch (IOException e) {
                log.warn("强制删除临时文件异常.");
            }
        }
        UploadFileReq uploadFileReq = new UploadFileReq();
        uploadFileReq.setProjectInfoId(-1L);
        uploadFileReq.setMilestoneCode(StrUtil.EMPTY);
        projectFile = new ProjProjectFileExtend(uploadFileReq, StrUtil.EMPTY,
                downloadFileName, SnowFlakeUtil.getId(),
                obskey, fileNameDesc);
        int count = projectFileMapper.insert(projectFile);
        log.info("新增文件关联. count: {}", count);
        return projectFile;
    }

    /**
     * 创建确认函
     *
     * @param applyObj 申请单
     * @return string
     */
    public String createConfirmationFileImpl(ProjDisasterRecoveryApply applyObj) {
        SysFile sysFile = sysFileMapper.selectOne(new QueryWrapper<SysFile>().eq("file_code",
                SysFileEnum.CONFIRMATION_MODEL.getFileCode()).eq("business_code",
                SysFileEnum.CONFIRMATION_MODEL.getBusinessCode()));
        // 获取确认涵模板
        FileSystemResource fileSystemResource = ProjApplyOrderServiceImpl.getFileSystemResource(bucketName,
                StrUtil.toString(applyObj.getProjDisasterRecoveryApplyId()), sysFile.getFilePath());
        ProjDisasterRecoveryInfo recoveryInfo =
                recoveryInfoMapper.selectOne(new QueryWrapper<ProjDisasterRecoveryInfo>().eq(
                        "proj_disaster_recovery_info_id", applyObj.getProjDisasterRecoveryInfoId()));
        String[] startTime = DisasterCloudCommonService.getTimeSplit(recoveryInfo.getStartTime());
        String[] endTime = DisasterCloudCommonService.getTimeSplit(recoveryInfo.getEndTime());
        String termMonth = StrUtil.toString(recoveryInfo.getServiceTerm());
        ProjCustomInfo customInfo = commonService.getCustomInfo(recoveryInfo.getCustomInfoId());
        try {
            BaseDocUtils.DocStrategy docStrategy = new BaseDocUtils.DocStrategy() {
                @Override
                public void handle(XWPFRun run, String originText) {
                    // 客户名称
                    DisasterCloudCommonService.setText(run, originText, VAR_CUSTOM_NAME, customInfo.getCustomName());
                    // 实际开通、结束时间
                    DisasterCloudCommonService.setText(run, originText, VAR_START_YEAR, startTime[0]);
                    DisasterCloudCommonService.setText(run, originText, VAR_START_MONTH, startTime[1]);
                    DisasterCloudCommonService.setText(run, originText, VAR_START_DAY, startTime[2]);
                    DisasterCloudCommonService.setText(run, originText, VAR_END_YEAR, endTime[0]);
                    DisasterCloudCommonService.setText(run, originText, VAR_END_MONTH, endTime[1]);
                    DisasterCloudCommonService.setText(run, originText, VAR_END_DAY, endTime[2]);
                    // 开通期限
                    DisasterCloudCommonService.setText(run, originText, VAR_TERM_MONTH, termMonth);
                }
            };
            return recoveryDocService.baseReplace(fileSystemResource.getInputStream(),
                    StrUtil.toString(applyObj.getProjDisasterRecoveryApplyId()), CONFIRMATION_FILE_NAME, docStrategy);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) throws IOException {
        File file = new File("C:\\Users\\<USER>\\Desktop\\abc.docx");
        FileInputStream fis = new FileInputStream(file);
        XWPFDocument document = new XWPFDocument(fis);
        // 获取模板中所有段落
        List<XWPFParagraph> paragraphs = document.getParagraphs();
// 遍历段落，替换变量
        for (XWPFParagraph paragraph : paragraphs) {
            List<XWPFRun> runs = paragraph.getRuns();
            for (XWPFRun run : runs) {
                String text = run.getText(0);
                if (text != null && text.contains("${startYear}")) {
                    run.setText(text.replace("${startYear}", "John Doe"), 0);
                }
            }
        }
        // 生成新的Word文件
        FileOutputStream fos = new FileOutputStream("C:\\Users\\<USER>\\Desktop\\output.docx");
        document.write(fos);
        fos.close();

    }

    /**
     * 更新状态
     *
     * @param applyObj 工单数据
     * @param dto      申请参数内容
     */
    private void updateApplyInfo(ProjDisasterRecoveryApply applyObj,
                                 HospitalUpdateStatusAndProductDeployDTO dto,
                                 DisasterRecoveryApplyStatusEnum typeEnum) {
        // 添加日志
        ProjDisasterRecoveryApplyLog applyLog = ProjDisasterRecoveryApplyLog.builder()
                .projDisasterRecoveryApplyId(applyObj.getProjDisasterRecoveryApplyId())
                .projDisasterRecoveryApplyLogId(SnowFlakeUtil.getId())
                .operateResult(ObjectUtil.isEmpty(dto.getRemark()) ? typeEnum.getDesc()
                        : dto.getRemark())
                .operaterName(dto.getOperateName())
                .operateType(typeEnum.getCode())
                .operateTime(new Date())
                .build();
        int count = applyLogMapper.insert(applyLog);
        log.info("新增审核日志. count: {}", count);
        // 更新申请单
        ProjDisasterRecoveryApply recoveryApply = ProjDisasterRecoveryApply.builder()
                .projDisasterRecoveryApplyId(applyObj.getProjDisasterRecoveryApplyId())
                .status(typeEnum.getCode())
                .build();
        count = disasterRecoveryApplyMapper.updateById(recoveryApply);
        log.info("更新申请单. count: {}", count);
    }
}
