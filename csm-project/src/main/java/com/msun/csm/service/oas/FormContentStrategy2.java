package com.msun.csm.service.oas;

import org.springframework.stereotype.Component;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 */
@Component
@HandleType(value = HandleTypeEnum.HANDLE_TYPE_2)
public class FormContentStrategy2 implements FormContentStrategy {
    @Override
    public String handle(String formContentPc) {
        // 判断formContentPc是不是空或空白
        if (StrUtil.isBlank(formContentPc)) {
            return formContentPc;
        }
        try {
            // 原始字符串
            String originalStr = formContentPc;

            // 目标字符串
            String targetValue = "集成测试医院";

            // 正则表达式模式，用于匹配 "value": "任意中文字符"
            // 这里假设"value"前面可能有空白字符，后面一定跟着":"和一个空格，然后是双引号包围的医院名称。
            String regex = "\"value\"\\s*:\\s*\"[^\"]+医院\"";

            // 替换后的字符串
            String replacedStr = originalStr.replaceAll(regex, "\"value\":\"" + targetValue + "\"");
            return replacedStr;
        } catch (Exception e) {
            // 捕获JSON解析异常，返回原始内容
            return formContentPc;
        }
    }
}
