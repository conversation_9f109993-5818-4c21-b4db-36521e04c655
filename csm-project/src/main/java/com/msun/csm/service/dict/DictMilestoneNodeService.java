package com.msun.csm.service.dict;

import java.util.List;

import com.msun.csm.dao.entity.dict.DictMilestoneNode;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/25
 */

public interface DictMilestoneNodeService {

    int deleteByPrimaryKey(Long id);

    int insert(DictMilestoneNode record);

    int insertOrUpdate(DictMilestoneNode record);

    int insertOrUpdateSelective(DictMilestoneNode record);

    int insertSelective(DictMilestoneNode record);

    DictMilestoneNode selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DictMilestoneNode record);

    int updateByPrimaryKey(DictMilestoneNode record);

    int updateBatch(List<DictMilestoneNode> list);

    int updateBatchSelective(List<DictMilestoneNode> list);

    int batchInsert(List<DictMilestoneNode> list);
}
