package com.msun.csm.service.dict;

import java.util.List;

import com.msun.csm.dao.entity.dict.DictEquipFactory;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/10
 */

public interface DictEquipFactoryService {

    int deleteByPrimaryKey(Long equipFactoryId);

    int insert(DictEquipFactory record);

    int insertOrUpdate(DictEquipFactory record);

    int insertOrUpdateSelective(DictEquipFactory record);

    int insertSelective(DictEquipFactory record);

    DictEquipFactory selectByPrimaryKey(Long equipFactoryId);

    int updateByPrimaryKeySelective(DictEquipFactory record);

    int updateByPrimaryKey(DictEquipFactory record);

    int updateBatch(List<DictEquipFactory> list);

    int updateBatchSelective(List<DictEquipFactory> list);

    int batchInsert(List<DictEquipFactory> list);

}
