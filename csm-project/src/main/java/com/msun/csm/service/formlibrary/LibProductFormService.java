package com.msun.csm.service.formlibrary;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.msun.core.component.implementation.api.formrepository.formstructure.entity.vo.DocumentBase64VO;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.formlibrary.LibProductForm;
import com.msun.csm.model.req.formlibrary.LibFormParamerReq;
import com.msun.csm.model.req.formlibrary.LibFormReq;
import com.msun.csm.model.req.formlibrary.PreviewStyleReq;
import com.msun.csm.model.req.formlibrary.ToolLimitReq;
import com.msun.csm.model.resp.formlibrary.LibProductFormPageResp;
import com.msun.csm.model.resp.formlibrary.LibProductFormResp;
import com.msun.csm.model.resp.formlibrary.ProjProductFormLogResp;

/**
 * @ClassName: DataImportService
 * @Description:
 * @Author: Yhongmin
 * @Date: 13:54 2024/5/28
 */
public interface LibProductFormService extends IService<LibProductForm> {

    /**
     * 分页查询报表信息
     *
     * @param libFormReq
     * @return
     */
    Result<LibProductFormPageResp<LibProductFormResp>> selectLibFormByPage(LibFormReq libFormReq);

    /**
     * 更新表单资源库信息
     *
     * @param libFormReq
     * @return
     */
    Result updateLibFormData(LibFormParamerReq libFormReq);

    /**
     * 发送表单数据
     * @param libFormReq
     * @return
     */
    Result sendLibFormData(LibFormParamerReq libFormReq);

    /**
     * 分页查询日志信息
     * @param libFormReq
     * @return
     */
    Result<PageInfo<ProjProductFormLogResp>> selectLibFormLogByPage(LibFormReq libFormReq);

    /**
     * 获取跳转路径数据
     * @param libFormReq
     * @return
     */
    Result getLibFormJumpPathData(LibFormParamerReq libFormReq);

    /**
     * 获取限制项目工具列表数据
     * @param libFormReq
     * @return
     */
    Result getIsToolLimit(ToolLimitReq libFormReq);

    /**
     * 预览样式
     * @param previewStyleReq
     * @return
     */
    Result<DocumentBase64VO> getPreviewStyle(PreviewStyleReq previewStyleReq);

    /**
     * 定时转换数据,将资源库grf转为text
     */
    void regularlyConvertData();

    /**
     * 根据任务id查询任务状态及结果
     */
    void getTaskStatusByTaskId();

    /**
     * 定时根据任务, 预处理打印报表资源库推荐
     */
    void regularlyPrintReportConvertData();
}
