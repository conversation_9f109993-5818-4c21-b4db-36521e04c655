package com.msun.csm.service.proj.projform;


import java.util.List;

import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.extension.service.IService;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.projform.ProjSurveyForm;
import com.msun.csm.model.req.projform.ProjSurveyFormAddReq;
import com.msun.csm.model.req.projform.ProjSurveyFormReq;
import com.msun.csm.model.req.projform.ProjSurveyFormResponsibilitiesReq;
import com.msun.csm.model.req.projform.ProjSurveyFormUpdateReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportReviewExamineReq;
import com.msun.csm.model.resp.projform.ProjSurveyFormMenuResp;
import com.msun.csm.model.resp.projform.ProjSurveyFormParamerResp;
import com.msun.csm.model.resp.projform.ProjSurveyFormResp;
import com.msun.csm.model.resp.projform.ProjSurveyReprotFormPageResp;
import com.msun.csm.model.resp.projreport.ProjBusinessExamineLogResp;

/**
 * <AUTHOR>
 * @description 针对表【proj_survey_form(表单主表)】的数据库操作Service
 * @createDate 2024-09-14 15:15:49
 */
public interface ProjSurveyFormService extends IService<ProjSurveyForm> {

    /**
     * 分页查询表单信息
     *
     * @param projSurveyFormReq
     * @return
     */
    Result<ProjSurveyReprotFormPageResp<ProjSurveyFormResp>> selectSurveyFormByPage(ProjSurveyFormReq projSurveyFormReq);

    /**
     * 删除表单信息
     *
     * @param projSurveyFormReq
     * @return
     */
    Result deleteSurveyForm(ProjSurveyFormUpdateReq projSurveyFormReq);

    /**
     * 设计制作
     *
     * @param projSurveyFormReq
     * @return
     */
    Result formMark(ProjSurveyFormUpdateReq projSurveyFormReq);

    /**
     * 表单审核驳回
     *
     * @param projSurveyFormReq
     * @return
     */
    Result updateExamineFormStatus(ProjSurveyFormUpdateReq projSurveyFormReq);

    /**
     * 批量分配责任人
     *
     * @param projSurveyFormReq
     * @return
     */
    Result updateFormResponsibilities(ProjSurveyFormResponsibilitiesReq projSurveyFormReq);

    /**
     * 查询项目下表单内容模块数据
     *
     * @param projSurveyFormReq
     * @return
     */
    Result<List<ProjSurveyFormMenuResp>> getFormMenuByProjectId(ProjSurveyFormUpdateReq projSurveyFormReq);

    /**
     * 查询所需参数
     *
     * @param projSurveyFormReq
     * @return
     */
    Result<ProjSurveyFormParamerResp> getFormParamer(ProjSurveyFormUpdateReq projSurveyFormReq);

    /**
     * 批量新增
     *
     * @param projSurveyFormAddReqs
     * @return
     */
    Result batchFormSave(List<ProjSurveyFormAddReq> projSurveyFormAddReqs);

    /**
     * 新增
     *
     * @param projSurveyFormReq
     * @return
     */
    Result updateOrAdd(ProjSurveyFormAddReq projSurveyFormReq);

    /**
     * 修改完成状态
     * @param projSurveyFormUpdateReq
     * @return
     */
    Result updateFormFinishStatus(ProjSurveyFormUpdateReq projSurveyFormUpdateReq);

    /**
     * 批量分配审核人
     * @param projSurveyFormReq
     * @return
     */
    Result updateReportReviewer(ProjSurveyFormResponsibilitiesReq projSurveyFormReq);

    /**
     * 批量审核
     * @param projSurveyFormReq
     * @return
     */
    Result updateReportExamine(ProjSurveyReportReviewExamineReq projSurveyFormReq);

    /**
     * 提交运维审核
     * @param projSurveyReportExamineReq
     * @return
     */
    Result updateReportExamineStatus(ProjSurveyReportReviewExamineReq projSurveyReportExamineReq);

    /**
     * 查询审核记录
     * @param id
     * @return
     */
    Result<List<ProjBusinessExamineLogResp>> selectLogById(Long id);

    /**
     * 报表打印导出Excel
     * @param response
     * @param dto
     */
    void reportPrintExportExcel(HttpServletResponse response, ProjSurveyFormReq dto);

    Result<Void> updateFormIdentifier(ProjSurveyFormResponsibilitiesReq projSurveyFormReq);

    boolean verificationPassed(ProjSurveyFormResponsibilitiesReq param);
}
