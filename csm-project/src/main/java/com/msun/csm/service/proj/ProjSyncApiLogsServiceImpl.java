package com.msun.csm.service.proj;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.msun.csm.dao.entity.proj.ProjSyncApiLogs;
import com.msun.csm.dao.mapper.proj.ProjSyncApiLogsMapper;


/**
 * @Description:
 * @Author: zhou<PERSON><PERSON><PERSON>
 * @Date: 2024/4/24
 */
@Service
public class ProjSyncApiLogsServiceImpl implements ProjSyncApiLogsService {

    @Resource
    ProjSyncApiLogsMapper projSyncApiLogsMapper;

    @Override
    public void insertData(ProjSyncApiLogs ey) {
        projSyncApiLogsMapper.insert(ey);
    }
}
