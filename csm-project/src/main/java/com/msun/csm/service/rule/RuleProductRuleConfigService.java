package com.msun.csm.service.rule;

import java.util.List;

import com.msun.csm.dao.entity.rule.RuleProductRuleConfig;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/5/28
 */

public interface RuleProductRuleConfigService {

    int deleteByPrimaryKey(Long productRuleConfigId);

    /**
     * 查询分院模式下通用产品配置
     *
     * @return List<RuleProductRuleConfig>
     */
    List<RuleProductRuleConfig> findRuleProductRuleConfigForBranch();

    int insert(RuleProductRuleConfig record);

    int insertOrUpdate(RuleProductRuleConfig record);

    int insertOrUpdateSelective(RuleProductRuleConfig record);

    int insertSelective(RuleProductRuleConfig record);

    RuleProductRuleConfig selectByPrimaryKey(Long productRuleConfigId);

    int updateByPrimaryKeySelective(RuleProductRuleConfig record);

    int updateByPrimaryKey(RuleProductRuleConfig record);

    int updateBatch(List<RuleProductRuleConfig> list);

    int updateBatchSelective(List<RuleProductRuleConfig> list);

    int batchInsert(List<RuleProductRuleConfig> list);
}
