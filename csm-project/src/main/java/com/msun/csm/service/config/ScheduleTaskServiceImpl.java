package com.msun.csm.service.config;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.config.ScheduleTask;
import com.msun.csm.dao.mapper.config.ScheduleTaskMapper;
import com.msun.csm.model.dto.ScheduleTaskDTO;
import com.msun.csm.model.vo.ScheduleTaskVO;
import com.msun.csm.schedule.TaskSchedule;
import com.msun.csm.util.RedisUtil;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/06/25/14:33
 */
@Slf4j
@Service
public class ScheduleTaskServiceImpl implements ScheduleTaskService {

    @Resource
    private ScheduleTaskMapper scheduleTaskMapper;

    @Resource
    private TaskSchedule taskScheduler;

    @Resource
    private RedisUtil redisUtil;

    /**
     * 查询定时任务数据
     *
     * @param dto
     * @return
     */
    @Override
    public Result<List<ScheduleTaskVO>> selectScheduleTask(ScheduleTaskDTO dto) {
        List<ScheduleTaskVO> vos = new ArrayList<>();
        List<ScheduleTask> scheduleTasks = scheduleTaskMapper.selectList(new QueryWrapper<ScheduleTask>()
                .like(ObjectUtil.isNotEmpty(dto.getTaskName()), "task_name", dto.getTaskName())
                .orderByDesc("schedule_task_id")
        );
        // 赋值数据
        for (ScheduleTask task : scheduleTasks) {
            ScheduleTaskVO scheduleTaskVO = new ScheduleTaskVO();
            scheduleTaskVO.setScheduleTaskId(task.getScheduleTaskId());
            scheduleTaskVO.setTaskCode(task.getTaskCode());
            scheduleTaskVO.setTaskMessage(task.getTaskMessage());
            scheduleTaskVO.setTaskName(task.getTaskName());
            // 计划执行时间返回给前端 HH:mm:ss
            DateFormat timeFormat = new SimpleDateFormat("HH:mm:ss");
            String timeString = timeFormat.format(task.getPlanRunTime());
            scheduleTaskVO.setPlanRunTime(timeString);
            scheduleTaskVO.setEnableBool(task.getEnableFlag() == 1);
            scheduleTaskVO.setTriggerTime(task.getTriggerTime());
            scheduleTaskVO.setIntervalTimeUnit(task.getIntervalTimeUnit());
            vos.add(scheduleTaskVO);
        }
        return Result.success(vos);
    }

    /**
     * 修改定时任务
     *
     * @param dto
     * @return
     */
    @Override
    public Result updateScheduleTask(ScheduleTaskDTO dto) {
        // 设置实体数据
        dto.setEnableFlag(dto.getEnableBool() ? 1 : 0);
        // 判断库中 计划时间时间是否被修改了。当被修改后 重置部分参数
        ScheduleTask scheduleTask = scheduleTaskMapper.selectById(dto.getScheduleTaskId());
        if (ObjectUtil.isNotEmpty(scheduleTask.getPlanRunTime()) && !scheduleTask.getPlanRunTime().toString()
                .equals(dto.getPlanRunTime())) {
//            dto.setPlanRunTimeForPG(LocalTime.parse(dto.getPlanRunTime()));
            dto.setTaskUpdateTime(new Date());
            dto.setLastRunTimeFlag(1);
            // 计算 计划时间的下次执行日期
            String[] split = dto.getPlanRunTime().split(":");
            // dto的 PlanRunTimeForPG 的时分秒替换为分组后的 1 (小时)、2(分钟)、3（秒）;年月日为原年月日不变
            Date planRunTimeForPG = scheduleTask.getPlanRunTime();
            planRunTimeForPG.setHours(Convert.toInt(split[0]));
            planRunTimeForPG.setMinutes(Convert.toInt(split[1]));
            planRunTimeForPG.setSeconds(Convert.toInt(split[2]));
            dto.setPlanRunTimeForPG(planRunTimeForPG);
        }
        scheduleTaskMapper.updateForTask(dto);
        return Result.success();
    }

    /**
     * 手动触发定时任务
     *
     * @param dto
     * @return
     */
    @Override
    public Result triggerScheduleTask(ScheduleTaskDTO dto) {
        // 检测 redis 是否存在 key
        Object taskCode = redisUtil.get(dto.getTaskCode());
        if (ObjectUtil.isNotEmpty(taskCode)) {
            return Result.fail("任务正在执行中");
        }
        // 根据不同的code 触发不同任务
        switch (dto.getTaskCode()) {
            case "user_task":
                taskScheduler.userTask();
                break;
            case "dept_task":
                taskScheduler.deptTask();
                break;
            case "product_task":
                taskScheduler.productsTask();
                break;
            case "custom_task":
                taskScheduler.customersTask();
                break;
            case "sync_form_ctrl_task":
                taskScheduler.syncCustomFormCtrlTask();
                break;
            case "division_province_task":
                taskScheduler.divisionProvinceTask();
                break;
            case "report_authorization_statistics":
                taskScheduler.syncReportAuthorizationStatistics();
                break;
            case "risk_project_info":
                taskScheduler.sendRiskProjectInfosMessageTask();
                break;
            case "query_lose_project_data":
                taskScheduler.queryLoseProjectData();
                break;
            case "sync_env_info":
                taskScheduler.syncEnvInfoTask();
                break;
            case "cancel_auth":
                taskScheduler.scheduleCancelAuth();
                break;
            case "exam_recycle_task":
                taskScheduler.recycleExamHospital();
            default:
                log.info("任务code不存在");
                break;
        }
        return Result.success();
    }
}
