package com.msun.csm.service.config;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.config.ConfigProjResearch;
import com.msun.csm.dao.mapper.config.ConfigProjResearchMapper;

import cn.hutool.core.collection.CollUtil;

/**
 * <AUTHOR>
 * @since 2024-05-10 02:23:49
 */

@Service
public class ConfigProjResearchServiceImpl implements ConfigProjResearchService {

    @Resource
    private ConfigProjResearchMapper configProjResearchMapper;
//    @Override
//    public Result<Map<Integer, String>> selectProjSearchConfigMap() {
//        List<ConfigProjResearch> researchList = configProjResearchMapper.selectList(null);
//        if (CollUtil.isEmpty(researchList)) {
//            return Result.success(new HashMap<>());
//        }
//        Map<Integer, String> map = new HashMap<>();
//        for (ConfigProjResearch configProjResearch : researchList) {
//            map.put(configProjResearch.getResearchCode(), configProjResearch.getResearchName());
//        }
//        return Result.success(map);
//    }

    @Override
    public Result<List<ConfigProjResearch>> selectProjSearchConfigListResult() {
        List<ConfigProjResearch> list = configProjResearchMapper.selectList(new QueryWrapper<ConfigProjResearch>().eq("is_deleted", 0));
        if (CollUtil.isEmpty(list)) {
            return Result.success(new ArrayList<>());
        }
        return Result.success(list);
    }

    @Override
    public List<ConfigProjResearch> selectProjSearchConfigList() {
        List<ConfigProjResearch> list = configProjResearchMapper.selectList(new QueryWrapper<ConfigProjResearch>().eq("is_deleted", 0));
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list;
    }


}
