package com.msun.csm.service.proj;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.msun.csm.dao.entity.proj.ProjNetSurveyResult;
import com.msun.csm.dao.mapper.proj.ProjNetSurveyResultMapper;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/21
 */

@Service
public class ProjNetSurveyResultServiceImpl implements ProjNetSurveyResultService {

    @Autowired
    private ProjNetSurveyResultMapper projNetSurveyResultMapper;

    @Override
    public int deleteByPrimaryKey(Long netSurveyResultId) {
        return projNetSurveyResultMapper.deleteByPrimaryKey(netSurveyResultId);
    }

    @Override
    public int insert(ProjNetSurveyResult record) {
        return projNetSurveyResultMapper.insert(record);
    }

    @Override
    public int insertOrUpdate(ProjNetSurveyResult record) {
        return projNetSurveyResultMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(ProjNetSurveyResult record) {
        return projNetSurveyResultMapper.insertOrUpdateSelective(record);
    }

    @Override
    public int insertSelective(ProjNetSurveyResult record) {
        return projNetSurveyResultMapper.insertSelective(record);
    }

    @Override
    public ProjNetSurveyResult selectByPrimaryKey(Long netSurveyResultId) {
        return projNetSurveyResultMapper.selectByPrimaryKey(netSurveyResultId);
    }

    @Override
    public int updateByPrimaryKeySelective(ProjNetSurveyResult record) {
        return projNetSurveyResultMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ProjNetSurveyResult record) {
        return projNetSurveyResultMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<ProjNetSurveyResult> list) {
        return projNetSurveyResultMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<ProjNetSurveyResult> list) {
        return projNetSurveyResultMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<ProjNetSurveyResult> list) {
        return projNetSurveyResultMapper.batchInsert(list);
    }

}
