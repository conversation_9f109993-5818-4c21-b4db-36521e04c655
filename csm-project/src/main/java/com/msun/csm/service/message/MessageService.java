package com.msun.csm.service.message;

import java.util.List;

import com.msun.csm.model.param.GetMessageListParam;
import com.msun.csm.model.param.MarkMessageToReadParam;
import com.msun.csm.model.resp.project.BriefMessageReminder;
import com.msun.csm.model.resp.project.MessageQueryResult;

public interface MessageService {

    /**
     * 发送项目日报消息
     */
    List<BriefMessageReminder> getBriefMessageReminder();

    void markMessageToRead(MarkMessageToReadParam param);

    MessageQueryResult getMessageList(GetMessageListParam param);

}
