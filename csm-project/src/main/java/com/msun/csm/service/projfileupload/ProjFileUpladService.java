package com.msun.csm.service.projfileupload;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.projfileupload.FindCustomFilePageDTO;
import com.msun.csm.model.dto.projfileupload.FindProjectFileDTO;
import com.msun.csm.model.dto.projfileupload.ProjPlanFileDelDTO;
import com.msun.csm.model.dto.projfileupload.ProjectFileUploadDTO;
import com.msun.csm.model.dto.projfileupload.ProjectPlanFileDTO;
import com.msun.csm.model.vo.projfileupload.FindCustomFileVO;
import com.msun.csm.model.vo.projfileupload.FindProjectFileVO;
import com.msun.csm.model.vo.projfileupload.ProjPlanUploadFileVO;
import com.msun.csm.model.vo.projfileupload.ProjectPlanFileVO;

public interface ProjFileUpladService {

    /**
     * 查询项目计划上传资料信息
     * @param projectPlanFileDTO
     * @return
     */
    Result<ProjectPlanFileVO> findProjectPlanFiles(ProjectPlanFileDTO projectPlanFileDTO);

    /**
     * 上传附件
     * @param projectFileUploadDTO
     * @return
     */
    Result<ProjPlanUploadFileVO> uploadProjectPlanFile(ProjectFileUploadDTO projectFileUploadDTO);

    /**
     * 删除附件
     * @param dto
     * @return
     */
    Result deleteProjectPlanFile(ProjPlanFileDelDTO dto);

    /**
     * 校验附件完整性
     * @param projectPlanFileDTO
     * @return
     */
    Result<Boolean> checkProjectFileFull(ProjectPlanFileDTO projectPlanFileDTO);

    /**
     * 分页查询客户上传资料
     * @param pageDTO
     * @return
     */
    Result<PageInfo<FindCustomFileVO>> findCustomFilesByPage(FindCustomFilePageDTO pageDTO);

    /**
     * 查询项目计划阶段上传资料
     * @param dto
     * @return
     */
    Result<List<FindProjectFileVO>> findProjectFiles(FindProjectFileDTO dto);
}
