package com.msun.csm.service.config.projectreview;


import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.projectreview.ConfigProjectReview;
import com.msun.csm.model.req.projectreview.ConfigProjectReviewReq;
import com.msun.csm.model.resp.projectreview.ConfigProjectReviewResp;

/**
 * <AUTHOR>
 * @description 针对表【config_project_review(项目审核模式配置表)】的数据库操作Service
 * @createDate 2025-06-18 08:30:31
 */
public interface ConfigProjectReviewService extends IService<ConfigProjectReview> {

    /**
     * 查询项目审核模式配置列表
     * @param dto
     * @return
     */
    Result<PageInfo<ConfigProjectReviewResp>> findDataPage(ConfigProjectReviewReq dto);

    /**
     * 项目审核模式配置启用作废
     * @param dto
     * @return
     */
    Result<String> updateDelData(ConfigProjectReviewReq dto);

    /**
     * 项目审核配置字典保存修改
     * @param dto
     * @return
     */
    Result<String> saveData(ConfigProjectReviewReq dto);
}
