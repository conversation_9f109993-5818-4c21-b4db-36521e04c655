package com.msun.csm.service.proj;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.msun.csm.util.PageHelperUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.autotest.AutoTestApi;
import com.msun.core.component.implementation.api.autotest.vo.AutoTestPharmacyDeptInfoVO;
import com.msun.core.component.implementation.api.imsp.dto.SystemConfigDto;
import com.msun.csm.common.enums.AutoTestSceneTypeEnum;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.ProjAutoTestStatusEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.AutoTestResult;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjAutoTestLog;
import com.msun.csm.dao.entity.proj.ProjAutoTestRecord;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalPharmacyRecord;
import com.msun.csm.dao.mapper.proj.ProjAutoTestLogMapper;
import com.msun.csm.dao.mapper.proj.ProjAutoTestRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalPharmacyRecordMapper;
import com.msun.csm.feign.client.aotocheck.AutoCheckClient;
import com.msun.csm.model.dto.autotest.ExecuteDetectDTO;
import com.msun.csm.model.dto.autotest.ManuChangeStatusDTO;
import com.msun.csm.model.dto.autotest.ProjAutoTestRecordDTO;
import com.msun.csm.model.dto.autotest.ProjAutoTestRecordProjMainDTO;
import com.msun.csm.model.dto.autotest.ProjAutoTestRecordQueryListDTO;
import com.msun.csm.model.dto.autotest.feign.TaskBatchRunDTO;
import com.msun.csm.model.dto.autotest.feign.TaskBatchRunErrorContentDTO;
import com.msun.csm.model.dto.autotest.feign.TaskBatchRunHospitalInfoDTO;
import com.msun.csm.model.vo.AutoTestProjectMainVO;
import com.msun.csm.model.vo.ProjAutoTestRecordVO;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.common.CommonService;
import com.msun.csm.util.SnowFlakeUtil;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2025-02-18 03:07:31
 */
@Slf4j
@Service
public class ProjAutoTestRecordServiceImpl implements ProjAutoTestRecordService {

    /**
     * 测试报告后缀
     */
    private static final String DETECT_REPORT_NAME_SUFFIX = "测试报告";
    private static final String DETECT_REPORT_DETAIL_NAME_SUFFIX = "测试报告详情";

    @Resource
    private ProjAutoTestRecordMapper autoTestRecordMapper;

    @Resource
    private CommonService commonService;

    @Resource
    private AutoCheckClient autoCheckClient;

    @Resource
    private ProjAutoTestLogService autoTestLogService;

    @Resource
    private ProjAutoTestLogMapper autoTestLogMapper;

    @Resource
    private UserHelper userHelper;

    @Resource
    private ProjHospitalPharmacyRecordMapper pharmacyRecordMapper;

    @Resource
    private AutoTestApi autoTestApi;

    @Resource
    private ProjHospitalInfoMapper hospitalInfoMapper;

    /**
     * 查询云健康科室信息. 提供前端选择科室使用
     *
     * @param hospitalInfoId 医院id
     * @return 科室合集
     */
    public Result<List<AutoTestPharmacyDeptInfoVO>> findPharmacyDeptInfo(Long hospitalInfoId) {
//        AutoTestPharmacyDeptInfoVO vo = new AutoTestPharmacyDeptInfoVO();
//        vo.setId(106L);
//        vo.setName("病房西药房");
//        AutoTestPharmacyDeptInfoVO vo2 = new AutoTestPharmacyDeptInfoVO();
//        vo2.setId(200326L);
//        vo2.setName("草药房");
//        AutoTestPharmacyDeptInfoVO vo3 = new AutoTestPharmacyDeptInfoVO();
//        vo3.setId(105L);
//        vo3.setName("门诊药房");
//        AutoTestPharmacyDeptInfoVO vo4 = new AutoTestPharmacyDeptInfoVO();
//        vo4.setId(200398L);
//        vo4.setName("门诊综合药房");
//        return Result.success(CollUtil.newArrayList(vo, vo2, vo3, vo4));
        ResponseResult<List<AutoTestPharmacyDeptInfoVO>> result;
        try {
            // 刷新域名缓存
            ProjHospitalInfo hospitalInfo = hospitalInfoMapper.selectById(hospitalInfoId);
            commonService.refreshDomain(hospitalInfo);
            // 调接口获取科室
            result = autoTestApi.findPharmacyDept(getPharmacyDeptParam(hospitalInfo));
            if (ObjectUtil.isEmpty(result) || !result.isSuccess()) {
                log.error("获取科室信息异常. hospitalInfo: {}, req: {}, res: {}", JSONUtil.toJsonStr(hospitalInfo),
                        hospitalInfoId,
                        JSONUtil.toJsonStr(result));
                return Result.fail("获取科室信息异常.");
            }
        } catch (Throwable e) {
            log.error("获取科室信息异常. message: {}, e=", e.getMessage(), e);
            return Result.fail("获取科室信息异常.");
        }
        return Result.success(result.getData());
    }

    /**
     * 获取参数.
     *
     * @param hospitalInfo 医院信息, 提供医院定位用的医院id机构id等
     */
    private SystemConfigDto<Long> getPharmacyDeptParam(ProjHospitalInfo hospitalInfo) {
        SystemConfigDto<Long> configDto = new SystemConfigDto<>();
        configDto.setHospitalId(hospitalInfo.getCloudHospitalId());
        configDto.setHisOrgId(hospitalInfo.getOrgId());
        configDto.setOrgId(hospitalInfo.getOrgId());
        configDto.setData(CollUtil.newArrayList(hospitalInfo.getCloudHospitalId()));
        return configDto;
    }

    @Override
    public Result<String> manuChangeDetectResult(ManuChangeStatusDTO dto) {
        ProjAutoTestRecord record =
                ProjAutoTestRecord.builder().projAutoTestRecordId(dto.getProjAutoTestRecordId()).build();
        // 如果是通过
        String remark = StrUtil.EMPTY;
        Integer status;
        if (dto.getDetectStatusFlag() == NumberEnum.NO_1.num().intValue()) {
            log.info("人工判定通过. dto: {}", JSONUtil.toJsonStr(dto));
            remark = dto.getPassReason();
            status = ProjAutoTestStatusEnum.DETECT_PASS_BY_ARTIFICIAL.getCode();
        } else {
            status = ProjAutoTestStatusEnum.DETECT_FAILED.getCode();
            log.info("人工恢复为不通过. dto: {}", JSONUtil.toJsonStr(dto));
        }
        record.setRemark(remark);
        record.setStatus(status);
        int count = autoTestRecordMapper.updateById(record);
        log.info("人工更改自动化测试结果. count: {}, detail: {}", count, JSONUtil.toJsonStr(record));
        return Result.success();
    }


    @Override
    public Result<PageInfo<ProjAutoTestRecordVO>> findAutoTestDataList(ProjAutoTestRecordDTO dto) {
        // 查询已开通的医院
        List<ProjHospitalInfo> openedHospitalInfos =
                commonService.findOpendHospitalInfoSimple(dto.getCustomInfoId(),
                        dto.getProjectType());
        // 查询已开通的医院
        ProjAutoTestRecordQueryListDTO queryListDTO = Convert.convert(ProjAutoTestRecordQueryListDTO.class, dto);
        // 查询医院检测记录
        List<ProjAutoTestRecordVO> list = PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> findAutoTestDataList(queryListDTO, openedHospitalInfos));
        if (CollUtil.isEmpty(list)) {
            return Result.success(new PageInfo<>());
        }
        // 查询记录, 赋值每一个检测状态
        if (CollUtil.isNotEmpty(list)) {
            for (ProjAutoTestRecordVO projAutoTestRecordVO : list) {
                // 处理返回值
                voHandler(projAutoTestRecordVO);
            }
        }
        PageInfo<ProjAutoTestRecordVO> pageInfoVO = new PageInfo<>(list);
        return Result.success(pageInfoVO);
    }

    /**
     * 处理返回值
     *
     * @param projAutoTestRecordVO 数据库内容
     */
    private void voHandler(ProjAutoTestRecordVO projAutoTestRecordVO) {
        // 设置未检测状态
        String statusDesc = ProjAutoTestStatusEnum.getDescByCode(projAutoTestRecordVO.getStatus());
        Integer status = projAutoTestRecordVO.getStatus();
        projAutoTestRecordVO.setStatus(status);
        projAutoTestRecordVO.setStatusDesc(statusDesc);
        // 设置测试报告名称
        if (StrUtil.isNotBlank(projAutoTestRecordVO.getTestReportUrl())) {
            projAutoTestRecordVO.setTestReportName(projAutoTestRecordVO.getHospitalName() + DETECT_REPORT_NAME_SUFFIX);
        }
        if (StrUtil.isNotBlank(projAutoTestRecordVO.getTestReportDetailUrl())) {
            projAutoTestRecordVO.setTestReportDetailName(projAutoTestRecordVO.getHospitalName() + DETECT_REPORT_DETAIL_NAME_SUFFIX);
        }
        // 设置回显
        if (ObjectUtil.isNotEmpty(projAutoTestRecordVO.getDeptSaveStatus()) && projAutoTestRecordVO.getDeptSaveStatus() != NumberEnum.NO_0.num()) {
            projAutoTestRecordVO.setCnInPharmacy(BaseIdNameResp.builder()
                    .id(projAutoTestRecordVO.getCnInPharmacyId())
                    .name(projAutoTestRecordVO.getCnInPharmacyName())
                    .build());
            projAutoTestRecordVO.setEnInPharmacy(BaseIdNameResp.builder()
                    .id(projAutoTestRecordVO.getEnInPharmacyId())
                    .name(projAutoTestRecordVO.getEnInPharmacyName())
                    .build());
            projAutoTestRecordVO.setCnOutPharmacy(BaseIdNameResp.builder()
                    .id(projAutoTestRecordVO.getCnOutPharmacyId())
                    .name(projAutoTestRecordVO.getCnOutPharmacyName())
                    .build());
            projAutoTestRecordVO.setEnOutPharmacy(BaseIdNameResp.builder()
                    .id(projAutoTestRecordVO.getEnOutPharmacyId())
                    .name(projAutoTestRecordVO.getEnOutPharmacyName())
                    .build());
        }
    }

    /**
     * 查询医院检测记录
     *
     * @param queryListDTO 含必要信息. 如客户id, 项目类型等
     * @return 空或者医院检测记录
     */
    private List<ProjAutoTestRecordVO> findAutoTestDataList(ProjAutoTestRecordQueryListDTO queryListDTO) {
        // 查询已开通的医院
        List<ProjHospitalInfo> openedHospitalInfos =
                commonService.findOpendHospitalInfoSimple(queryListDTO.getCustomInfoId(),
                        queryListDTO.getProjectType());
        return findAutoTestDataList(queryListDTO, openedHospitalInfos);
    }

    /**
     * 查询医院检测记录
     *
     * @param queryListDTO 含必要信息. 如客户id, 项目类型等
     * @return 空或者医院检测记录
     */
    private List<ProjAutoTestRecordVO> findAutoTestDataList(ProjAutoTestRecordQueryListDTO queryListDTO,
                                                            List<ProjHospitalInfo> openedHospitalInfos) {
        // 若未查询到医院返回空
        if (CollUtil.isEmpty(openedHospitalInfos)) {
            return CollUtil.newArrayList();
        }
        queryListDTO.setOpenedCloudHospitalIds(openedHospitalInfos.stream().map(ProjHospitalInfo::getCloudHospitalId).collect(Collectors.toList()));
        return autoTestRecordMapper.findAutoTestDataList(queryListDTO);
    }

    @Override
    public Result<AutoTestProjectMainVO> getProjectMainInfo(ProjAutoTestRecordProjMainDTO dto) {
        List<ProjHospitalInfo> openedHospitalInfos = commonService.findOpendHospitalInfoSimple(dto.getCustomInfoId(),
                dto.getProjectType());
        // 若未查询到医院返回空
        if (CollUtil.isEmpty(openedHospitalInfos)) {
            return Result.success(AutoTestProjectMainVO.builder().build());
        }
        return Result.success(AutoTestProjectMainVO.builder().cloudDomain(openedHospitalInfos.get(0).getCloudDomain()).build());
    }

    /**
     * 执行记录返回值传输类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ExecuteRecordRes {
        /**
         * 可测试的医院
         */
        private List<ProjAutoTestRecordVO> availableAutoTestRecord;
        /**
         * 记录未能检测的医院信息
         */
        private StringBuilder rememberUndetectMsgInfo;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Result<String> executeDetect(ExecuteDetectDTO dto) {
        ProjAutoTestRecordQueryListDTO queryListDTO = Convert.convert(ProjAutoTestRecordQueryListDTO.class, dto);
        // 查询测试医院信息
        List<ProjAutoTestRecordVO> list = findAutoTestDataList(queryListDTO);
        if (CollUtil.isEmpty(list)) {
            log.warn("未查询到待检测的医院. dto: {}", JSONUtil.toJsonStr(dto));
            return Result.success("未查询到待检测的医院.");
        }
        // 过滤出需要检测的医院
        ExecuteRecordRes recordRes;
        try {
            recordRes = findCanDetectHosList(list, dto);
        } catch (Throwable e) {
            return Result.success(e.getMessage());
        }
        List<ProjAutoTestRecordVO> needTestHospitals = recordRes.getAvailableAutoTestRecord();
        // 生成记录record|log. 判断是否存在此医院, 不存在则新增， 存在则更新
        updateOrSaveReordAndLog(needTestHospitals, dto);
        // 拼接参数
        TaskBatchRunDTO taskBatchRunDTO = TaskBatchRunDTO.builder()
                .hospitalInfoList(findRunHospitalInfo(needTestHospitals))
                .build();
        // 调接口
        try {
            log.info("执行自动化测试接口, 入參: {}", JSONUtil.toJsonStr(taskBatchRunDTO));
            AutoTestResult<Object, TaskBatchRunErrorContentDTO> autoTestResult =
                    autoCheckClient.taskBatchRun(taskBatchRunDTO);
            log.info("执行自动化测试接口, 出參: {}", JSONUtil.toJsonStr(autoTestResult));
            if (ObjectUtil.isEmpty(autoTestResult) || autoTestResult.getCode() == -1) {
                log.error("测试执行异常, 接口返回值异常. detail: {}", JSONUtil.toJsonStr(autoTestResult));
                if (ObjectUtil.isNotEmpty(autoTestResult)) {
                    // 处理异常
                    handleResponseError(needTestHospitals, autoTestResult.getErrorList());
                }
                return Result.success("测试执行异常.");
            }
        } catch (Throwable e) {
            log.error("测试执行异常. message: {}, e=", e.getMessage(), e);
            handleError(needTestHospitals, e.getMessage());
            return Result.success("测试执行异常.");
        }
        Result<String> result = Result.success();
        result.setData(recordRes.getRememberUndetectMsgInfo().toString());
        return result;
    }

    private ExecuteRecordRes findCanDetectHosList(List<ProjAutoTestRecordVO> list, ExecuteDetectDTO dto) {
        List<ProjAutoTestRecordVO> needTestHospitals =
                list.stream().filter(e -> e.getStatus() != ProjAutoTestStatusEnum.DETECTING.getCode()).collect(Collectors.toList());
        StringBuilder msg = new StringBuilder();
        if (CollUtil.isEmpty(needTestHospitals)) {
            setTip(list, msg);
            log.warn("过滤后, 未查询到待检测的医院. list:{}, dto: {}", JSONUtil.toJsonStr(list), JSONUtil.toJsonStr(dto));
            throw new CustomException(msg.toString());
        }
        // 如果执行单个医院检测, 进行过滤
        needTestHospitals = filterSingleHospital(list, needTestHospitals, dto, msg);
        // 判断如果是业务流程模拟, 则判断若未选择科室, 不进行检测
        if (dto.getSceneType() == AutoTestSceneTypeEnum.BUSINESS.getCode()) {
            needTestHospitals =
                    needTestHospitals.stream().filter(e -> judgeCanDetect(e, msg)).collect(Collectors.toList());
            if (CollUtil.isEmpty(needTestHospitals)) {
                log.warn("流程测试, 未查询到待检测的医院. list:{}, dto: {}", JSONUtil.toJsonStr(list), JSONUtil.toJsonStr(dto));
                throw new CustomException((StrUtil.isNotBlank(msg) ? msg.toString() : StrUtil.EMPTY));
            }
        } else {
            if (CollUtil.isEmpty(needTestHospitals)) {
                log.warn("基础数据测试, 未查询到待检测的医院. list:{}, dto: {}", JSONUtil.toJsonStr(list), JSONUtil.toJsonStr(dto));
                setTip(list, msg);
                throw new CustomException(msg.toString());
            }
            if (needTestHospitals.size() < list.size()) {
                // 提示检测中的医院
                List<ProjAutoTestRecordVO> finalNeedTestHospitals = needTestHospitals;
                List<ProjAutoTestRecordVO> detectingRecord =
                        list.stream().filter(e -> finalNeedTestHospitals.stream().noneMatch(f ->
                                f.getCloudHospitalId().longValue() == e.getCloudHospitalId())).collect(Collectors.toList());
                setTip(detectingRecord, msg);
            }
        }
        ExecuteRecordRes recordRes = ExecuteRecordRes.builder()
                .availableAutoTestRecord(needTestHospitals)
                .rememberUndetectMsgInfo(msg)
                .build();
        return recordRes;
    }

    /**
     * 设置不能检测的医院提示
     *
     * @param list 医院集合
     * @param msg  提示载体
     */
    private void setTip(List<ProjAutoTestRecordVO> list, StringBuilder msg) {
        list.stream().forEach(e -> {
            splitTip(msg, e);
            msg.append("正在检测中, 请稍后再试!\n");
        });
    }

    private List<ProjAutoTestRecordVO> filterSingleHospital(List<ProjAutoTestRecordVO> list,
                                                            List<ProjAutoTestRecordVO> needTestHospitals,
                                                            ExecuteDetectDTO dto, StringBuilder msg) {
        if (ObjectUtil.isNotEmpty(dto.getCloudHospitalId())) {
            if (ObjectUtil.isEmpty(dto.getProjAutoTestRecordId())) {
                needTestHospitals =
                        needTestHospitals.stream().filter(e ->
                                e.getCloudHospitalId().longValue() == dto.getCloudHospitalId()).collect(Collectors.toList());
            } else {
                needTestHospitals =
                        needTestHospitals.stream().filter(e -> ObjectUtil.isNotEmpty(e.getProjAutoTestRecordId())
                                && e.getProjAutoTestRecordId() == dto.getProjAutoTestRecordId().longValue()).collect(Collectors.toList());
            }
            log.info("执行了单个医院的检测. dto: {}, hospitals: {}", JSONUtil.toJsonStr(dto),
                    JSONUtil.toJsonStr(needTestHospitals));
            //
            if (CollUtil.isEmpty(needTestHospitals)) {
                log.warn("执行单个医院的检测, 未查询到待检测的医院. list:{}, dto: {}", JSONUtil.toJsonStr(list), JSONUtil.toJsonStr(dto));
                setTip(list, msg);
                throw new CustomException(msg.toString());
            }
        }
        return needTestHospitals;
    }

    private boolean judgeCanDetect(ProjAutoTestRecordVO e, StringBuilder msg) {
        boolean deptF = e.getDeptSaveStatus().intValue() != NumberEnum.NO_0.num();
        boolean dataTestF = false;
        // 查询是否通过基础数据测试
        Long customInfoId = e.getCustomInfoId();
        Integer projectType = e.getProjectType();
        Long cloudHospitalId = e.getCloudHospitalId();
        ProjAutoTestRecord dataTestRecord =
                autoTestRecordMapper.selectOne(new QueryWrapper<ProjAutoTestRecord>()
                        .eq("custom_info_id", customInfoId)
                        .eq("project_type", projectType)
                        .eq("cloud_hospital_id", cloudHospitalId)
                        .eq("scene_type", AutoTestSceneTypeEnum.BASE_DATA.getCode()));
        if (ObjectUtil.isNotEmpty(dataTestRecord)
                && (dataTestRecord.getStatus() == ProjAutoTestStatusEnum.DETECT_PASS.getCode()
                || dataTestRecord.getStatus() == ProjAutoTestStatusEnum.DETECT_PASS_BY_ARTIFICIAL.getCode())) {
            dataTestF = true;
        }
        boolean flag = false;
        String deptMsg = StrUtil.EMPTY;
        String dataMsg = StrUtil.EMPTY;
        // 记录未选择科室医院
        if (!deptF) {
            deptMsg = "未选择科室,";
            flag = true;
        }
        // 记录未完成基础测试医院
        if (!dataTestF) {
            dataMsg = "未通过基础数据测试.";
            flag = true;
        }
        if (flag) {
            splitTip(msg, e);
            msg.append(deptMsg).append(dataMsg).append("\n");
        }
        return deptF && dataTestF;
    }

    /**
     * 拼接提示
     *
     * @param tipBuilder 提示体
     * @param e          医院
     */
    public static void splitTip(StringBuilder tipBuilder, ProjAutoTestRecordVO e) {
        tipBuilder.append(e.getHospitalName())
                .append("(")
                .append(e.getCloudHospitalId())
                .append(")");
    }

    /**
     * 获取接口入参需要的医院数据.
     *
     * @param needTestHospitals
     * @return 批量的医院数据
     */
    private List<TaskBatchRunHospitalInfoDTO> findRunHospitalInfo(List<ProjAutoTestRecordVO> needTestHospitals) {
        List<TaskBatchRunHospitalInfoDTO> taskBatchRunHospitalInfoDTOS = CollUtil.newArrayList();
        for (ProjAutoTestRecordVO needTestHospital : needTestHospitals) {
            TaskBatchRunHospitalInfoDTO runHospitalInfoDTO = TaskBatchRunHospitalInfoDTO.builder()
                    .orgId(needTestHospital.getOrgId())
                    .hospitalName(needTestHospital.getHospitalName())
                    .domain(needTestHospital.getCloudDomain())
                    .hospitalId(needTestHospital.getCloudHospitalId())
                    .operatorUser(userHelper.getCurrentUser().getUserName())
                    .sceneType(needTestHospital.getSceneType())
                    .testLogId(needTestHospital.getTestLogId())
                    .build();
            taskBatchRunHospitalInfoDTOS.add(runHospitalInfoDTO);
        }
        return taskBatchRunHospitalInfoDTOS;
    }

    /**
     * 更新或新增测试记录和日志
     *
     * @param needTestHospitals 需要测试的医院
     * @param dto               入参
     */
    private void updateOrSaveReordAndLog(List<ProjAutoTestRecordVO> needTestHospitals, ExecuteDetectDTO dto) {
        Integer sceneType = dto.getSceneType();
        for (ProjAutoTestRecordVO needTestHospital : needTestHospitals) {
            ProjAutoTestRecord testRecord = autoTestRecordMapper.selectOne(new QueryWrapper<ProjAutoTestRecord>()
                    .eq("custom_info_id", needTestHospital.getCustomInfoId())
                    .eq("cloud_hospital_id", needTestHospital.getCloudHospitalId())
                    .eq("project_type", needTestHospital.getProjectType())
                    .eq("scene_type", needTestHospital.getSceneType()));
            SysUserVO sysUserVO = userHelper.getCurrentUser();
            // 创建临时记录, 会用于日志及记录的新增, 不含主键id
            ProjAutoTestRecord tmpTestRecord = ProjAutoTestRecord.builder()
                    .cloudHospitalId(needTestHospital.getCloudHospitalId())
                    .executorUserId(sysUserVO.getSysUserId())
                    .executorUserName(sysUserVO.getUserName())
                    .sceneType(sceneType)
                    .customInfoId(needTestHospital.getCustomInfoId())
                    .projectType(needTestHospital.getProjectType())
                    .status(ProjAutoTestStatusEnum.DETECTING.getCode())
                    .projectInfoId(dto.getProjectInfoId())
                    .build();
            // 新增日志
            ProjAutoTestLog testLog = Convert.convert(ProjAutoTestLog.class, tmpTestRecord);
            testLog.setProjAutoTestLogId(SnowFlakeUtil.getId());
            // 判断如果是否是业务流程, 保存科室
            // 查询医院对应的科室信息
            if (sceneType == AutoTestSceneTypeEnum.BUSINESS.getCode()) {
                ProjHospitalPharmacyRecord hospitalPharmacyRecord =
                        pharmacyRecordMapper.selectOne(new QueryWrapper<ProjHospitalPharmacyRecord>().eq(
                                "cloud_hospital_id",
                                needTestHospital.getCloudHospitalId()));
                testLog.setEnInPharmacyId(hospitalPharmacyRecord.getEnInPharmacyId());
                testLog.setEnInPharmacyName(hospitalPharmacyRecord.getEnInPharmacyName());
                testLog.setEnOutPharmacyId(hospitalPharmacyRecord.getEnOutPharmacyId());
                testLog.setEnOutPharmacyName(hospitalPharmacyRecord.getEnOutPharmacyName());
                testLog.setCnInPharmacyId(hospitalPharmacyRecord.getCnInPharmacyId());
                testLog.setCnInPharmacyName(hospitalPharmacyRecord.getCnInPharmacyName());
                testLog.setCnOutPharmacyId(hospitalPharmacyRecord.getCnOutPharmacyId());
                testLog.setCnOutPharmacyName(hospitalPharmacyRecord.getCnOutPharmacyName());
                testLog.setProjectInfoId(dto.getProjectInfoId());
                log.info("已向日志记录赋值医院科室信息. detail: {}", JSONUtil.toJsonStr(testLog));
            }
            ProjAutoTestRecord changeRecord;
            if (ObjectUtil.isEmpty(testRecord)) {
                // 新增
                changeRecord = Convert.convert(ProjAutoTestRecord.class, tmpTestRecord);
                changeRecord.setTestLogId(testLog.getProjAutoTestLogId());
                int count = autoTestRecordMapper.insert(changeRecord);
                log.info("新增自动化测试记录. count: {}", count, JSONUtil.toJsonStr(changeRecord));
            } else {
                // 更新
                changeRecord = Convert.convert(ProjAutoTestRecord.class, testRecord);
                // 置空
                changeRecord.setRemark(null);
                changeRecord.setExecutorUserId(sysUserVO.getSysUserId());
                changeRecord.setExecutorUserName(sysUserVO.getUserName());
                changeRecord.setReportTime(null);
                changeRecord.setStatus(ProjAutoTestStatusEnum.DETECTING.getCode());
                changeRecord.setTestFailReason(null);
                changeRecord.setUpdateTime(new Date());
                changeRecord.setTestReportUrl(null);
                changeRecord.setUpdaterId(sysUserVO.getSysUserId());
                changeRecord.setTestLogId(testLog.getProjAutoTestLogId());
                changeRecord.setProjectInfoId(null);
                int count = autoTestRecordMapper.updateRecord(changeRecord);
                log.info("重置自动化测试记录. count: {}", count, JSONUtil.toJsonStr(changeRecord));
            }
            // 更新日志的记录表主键字段
            testLog.setAutoTestRecordId(changeRecord.getProjAutoTestRecordId());
            int logCount = autoTestLogMapper.insert(testLog);
            log.info("新增自动化测试日志. count: {}, detail: {}", logCount, JSONUtil.toJsonStr(testLog));
            // 赋值logId
            needTestHospital.setTestLogId(testLog.getProjAutoTestLogId());
        }
    }

    /**
     * 处理调用接口返回值异常
     *
     * @param needTestHospitals   批量测试的医院
     * @param runErrorContentDTOS 返回的异常数据. 含异常信息
     */
    private void handleResponseError(List<ProjAutoTestRecordVO> needTestHospitals,
                                     List<TaskBatchRunErrorContentDTO> runErrorContentDTOS) {
        // 更新接口执行异常记录
        for (ProjAutoTestRecordVO recordVO : needTestHospitals) {
            String message;
            if (CollUtil.isEmpty(runErrorContentDTOS)) {
                message = "自动化测试平台接口异常!";
            } else {
                // 获取异常更新记录
                TaskBatchRunErrorContentDTO runErrorContentDTO = runErrorContentDTOS.stream()
                        .filter(e -> ObjectUtil.isNotEmpty(e.getHospitalId()) && e.getHospitalId().longValue() == recordVO
                                .getCloudHospitalId()).findFirst().get();
                message = runErrorContentDTO.getErrMsg();
            }
            // 更新异常
            updateErrMsg(recordVO.getProjAutoTestRecordId(), message);
        }
    }

    /**
     * 处理异常. 接口调用失败会执行
     *
     * @param needTestHospitals
     * @param failReason
     */
    private void handleError(List<ProjAutoTestRecordVO> needTestHospitals, String failReason) {
        // 更新接口执行异常记录
        for (ProjAutoTestRecordVO recordVO : needTestHospitals) {
            updateErrMsg(recordVO.getProjAutoTestRecordId(), failReason);
        }
    }

    /**
     * 更新异常. 会更新记录表和日志表, 更新状态, 更新异常原因
     *
     * @param autoTestRecordId 记录表主键
     * @param errMsg           异常信息
     */
    private void updateErrMsg(Long autoTestRecordId, String errMsg) {
        ProjAutoTestRecord testRecord = ProjAutoTestRecord.builder()
                .projAutoTestRecordId(autoTestRecordId)
                .testFailReason(errMsg)
                .status(ProjAutoTestStatusEnum.DETECT_EXEC_FAILED.getCode())
                .build();
        int count = autoTestRecordMapper.updateById(testRecord);
        log.info("自动化测试记录更新异常原因. count: {}, detail: {}", count, JSONUtil.toJsonStr(testRecord));
        ProjAutoTestLog testLog = Convert.convert(ProjAutoTestLog.class, testRecord);
        testLog.setProjAutoTestLogId(testRecord.getTestLogId());
        count = autoTestLogMapper.updateById(testLog);
        log.info("自动化测试日志更新异常原因. count: {}, detail: {}", count, JSONUtil.toJsonStr(testLog));
    }
}
