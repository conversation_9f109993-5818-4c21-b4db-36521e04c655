package com.msun.csm.service.proj;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.msun.core.component.implementation.api.imsp.dto.EquipAutoCheckDTO;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.model.dto.CheckEquipToAnalyManagerDTO;
import com.msun.csm.model.dto.ProjEquipDocDTO;
import com.msun.csm.model.dto.ProjEquipSummaryDTO;
import com.msun.csm.model.dto.UrgeProcessingDTO;
import com.msun.csm.model.vo.DictEquipInfoVO;
import com.msun.csm.model.vo.EquipSummaryVo;
import com.msun.csm.model.vo.OldToEquipConfigVO;
import com.msun.csm.model.vo.ProjEquipDocVO;
import com.msun.csm.model.vo.ProjEquipReadySummaryVO;
import com.msun.csm.model.vo.ProjEquipRecordLogVO;
import com.msun.csm.model.vo.ProjEquipSummaryVO;
import com.msun.csm.model.vo.ProjProjectFileRuleVO;

/**
 * @description: 设备调研汇总Service
 * @fileName: ProjEquipSummaryService.java
 * @author: lius3
 * @createAt: 2024/10/11 9:22
 * @updateBy: lius3
 * @remark: Copyright
 */
public interface ProjEquipSummaryService {

    /**
     * 查询设备调研汇总信息
     *
     * @param projEquipSummaryDTO
     * @return
     */
    Result<List<ProjEquipSummaryVO>> findProjEquipSummaryInfo(ProjEquipSummaryDTO projEquipSummaryDTO);

    /**
     * 获取项目的设备动态菜单
     *
     * @param projectInfoId
     * @return
     */
    Result<List<EquipSummaryVo>> selectSurveyEquipMenu(Long projectInfoId);

    /**
     * 手麻设备自动检测
     *
     * @param dto          请求参数
     * @param hospitalInfo 医院信息
     * @return 返回结果
     */
    Result<String> aimsEquipAutoCheck(EquipAutoCheckDTO dto, ProjHospitalInfo hospitalInfo);

    /**
     * Pacs设备自动检测
     *
     * @param dto          来自pacs的检测信息
     * @param hospitalInfo 设备所处医院
     * @return 处理后的返回结果
     */
    Result<String> pacsEquipAutoCheck(EquipAutoCheckDTO dto, ProjHospitalInfo hospitalInfo);

    /**
     * Lis设备自动检测
     *
     * @param dto          来自产品的设备检测结果
     * @param hospitalInfo 设备所处医院
     * @return 处理后的反馈信息, 成功或失败
     */
    Result<String> lisEquipAutoCheck(EquipAutoCheckDTO dto, ProjHospitalInfo hospitalInfo);

    /**
     * 更新设备调研 / 设备准备里程碑节点状态
     *
     * @param milestoneInfoId
     * @return
     */
    Result updateMilestoneInfo(Long milestoneInfoId);

    /**
     * 获取设备分类
     *
     * @param projectInfoId
     * @return
     */
    Result<List<BaseIdNameResp>> selectSurveyEquipClass(@Param("projectInfoId") Long projectInfoId);

    /**
     * 获取设备厂商
     *
     * @return
     */
    Result<List<BaseIdNameResp>> selectSurveyEquipFactory(String productCode, Long id);

    /**
     * 获取设备类型
     *
     * @return
     */
    Result<List<BaseIdNameResp>> selectSurveyEquipType(String productCode, Long id);

    /**
     * 获取设备字典
     *
     * @return
     */
    Result<List<DictEquipInfoVO>> selectSurveyEquipInfo(String productCode, Long id);

    /**
     * 获取设备属性字典
     *
     * @param equipAttributesCode
     * @return
     */
    Result<List<BaseCodeNameResp>> selectSurveyAttributesInfo(String equipAttributesCode);

    /**
     * 查询附件配置信息
     *
     * @param sceneCode
     * @param isMobile
     * @return
     */
    Result<List<ProjProjectFileRuleVO>> selectProjectFileConfig(String sceneCode, Integer isMobile);

    /**
     * 调用云健康进行设备检测
     *
     * @param dtoList
     * @return
     */
    Result checkEquipToLisAnalyManager(List<CheckEquipToAnalyManagerDTO> dtoList);

    /**
     * 查询设备准备汇总信息
     *
     * @param projEquipSummaryDTO
     * @return
     */
    Result<List<ProjEquipReadySummaryVO>> findProjEquipReadySummaryInfo(ProjEquipSummaryDTO projEquipSummaryDTO);

    /**
     * 查询设备文档
     *
     * @param dto
     * @return
     */
    Result<List<ProjEquipDocVO>> findProjEquipDoc(ProjEquipDocDTO dto);

    /**
     * 查询老换新设备字典配置信息
     *
     * @param customInfoId
     * @return
     */
    Result<OldToEquipConfigVO> getOldToEquipConfig(Long customInfoId);

    /**
     * 客户的首期项目上没有Lis、Pacs时特批添加
     *
     * @param customInfoId
     * @return
     */
    Result saveProductFroHisProjectInfo(Long customInfoId);

    /**
     * 各产品设备自动检测
     *
     * @param dto 请求参数
     * @return Result<String>
     */
    Result<String> csmAutoCheckEquip(EquipAutoCheckDTO dto);

    /**
     * 各产品设备自动检测
     *
     * @param dto 请求参数
     * @return Result<String>
     */
    Result<String> csmAutoCheckEquipWrapper(EquipAutoCheckDTO dto);

    /**
     * 查询老换新ip,端口，下载路径
     * @param customInfoId
     * @return
     */
    Result<OldToEquipConfigVO> getOldPlatformToolConfig(Long customInfoId);

    /**
     * 查询设备操作日志
     * @param equipRecordId
     * @param equipRecordBusinessId
     * @return
     */
    Result<List<ProjEquipRecordLogVO>> findEquipOperateLog(Long equipRecordId, Long equipRecordBusinessId);

    /**
     * 催办
     * @param dto
     * @return
     */
    Result urgeProcessing(UrgeProcessingDTO dto);
}
