package com.msun.csm.service.proj;

import java.util.List;

import com.msun.csm.dao.entity.proj.ProjNetPortMapping;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/21
 */

public interface ProjNetPortMappingService {

    int deleteByPrimaryKey(Long portMappingId);

    int insert(ProjNetPortMapping record);

    int insertOrUpdate(ProjNetPortMapping record);

    int insertOrUpdateSelective(ProjNetPortMapping record);

    int insertSelective(ProjNetPortMapping record);

    ProjNetPortMapping selectByPrimaryKey(Long portMappingId);

    int updateByPrimaryKeySelective(ProjNetPortMapping record);

    int updateByPrimaryKey(ProjNetPortMapping record);

    int updateBatch(List<ProjNetPortMapping> list);

    int updateBatchSelective(List<ProjNetPortMapping> list);

    int batchInsert(List<ProjNetPortMapping> list);

}
