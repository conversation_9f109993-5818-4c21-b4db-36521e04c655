package com.msun.csm.service.config;

import java.util.List;

import com.msun.csm.dao.entity.config.ConfigProjectPlanItem;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2025/1/3
 */

public interface ConfigProjectPlanItemService {

    int deleteByPrimaryKey(Long configProjectPlanItemId);

    int insert(ConfigProjectPlanItem record);

    int insertOrUpdate(ConfigProjectPlanItem record);

    int insertOrUpdateSelective(ConfigProjectPlanItem record);

    int insertSelective(ConfigProjectPlanItem record);

    ConfigProjectPlanItem selectByPrimaryKey(Long configProjectPlanItemId);

    int updateByPrimaryKeySelective(ConfigProjectPlanItem record);

    int updateByPrimaryKey(ConfigProjectPlanItem record);

    int updateBatch(List<ConfigProjectPlanItem> list);

    int updateBatchSelective(List<ConfigProjectPlanItem> list);

    int batchInsert(List<ConfigProjectPlanItem> list);

}
