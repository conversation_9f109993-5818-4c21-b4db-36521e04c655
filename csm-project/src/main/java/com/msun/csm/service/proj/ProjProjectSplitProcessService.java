package com.msun.csm.service.proj;

import java.util.List;

import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.SimpleId;
import com.msun.csm.dao.entity.proj.ProjProjectSplitProcess;
import com.msun.csm.model.req.project.AddSplitProcessReq;
import com.msun.csm.model.req.project.AuditSplitProcessReq;
import com.msun.csm.model.resp.project.SplitProcessDetailResp;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/5/23
 */

public interface ProjProjectSplitProcessService {

    int deleteByPrimaryKey(Long id);

    int insert(ProjProjectSplitProcess record);

    int insertOrUpdate(ProjProjectSplitProcess record);

    int insertOrUpdateSelective(ProjProjectSplitProcess record);

    int insertSelective(ProjProjectSplitProcess record);

    ProjProjectSplitProcess selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProjProjectSplitProcess record);

    int updateByPrimaryKey(ProjProjectSplitProcess record);

    int updateBatch(List<ProjProjectSplitProcess> list);

    int updateBatchSelective(List<ProjProjectSplitProcess> list);

    int batchInsert(List<ProjProjectSplitProcess> list);

    /**
     * 新增申请单
     *
     * @param req
     * @return
     */
    Result addProcess(AddSplitProcessReq req);

    /**
     * 获取申请单详情
     *
     * @param id
     * @return
     */
    Result<SplitProcessDetailResp> getProcessDetail(SimpleId id);

    /**
     * 审核申请单pmo、风控
     *
     * @param req
     * @return
     */
    Result auditProcess(AuditSplitProcessReq req);


    /**
     * 取消申请
     *
     * @param id
     * @return
     */
    Result cancelProcess(SimpleId id);
}
