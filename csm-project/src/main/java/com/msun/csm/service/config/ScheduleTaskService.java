package com.msun.csm.service.config;

import java.util.List;

import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.ScheduleTaskDTO;
import com.msun.csm.model.vo.ScheduleTaskVO;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/06/25/14:24
 */
public interface ScheduleTaskService {

    /**
     * 查询定时任务数据
     *
     * @param dto
     * @return
     */
    Result<List<ScheduleTaskVO>> selectScheduleTask(ScheduleTaskDTO dto);

    /**
     * 修改定时任务
     *
     * @param dto
     * @return
     */
    Result updateScheduleTask(ScheduleTaskDTO dto);

    /**
     * 手动触发定时任务
     *
     * @param dto
     * @return
     */
    Result triggerScheduleTask(ScheduleTaskDTO dto);
}
