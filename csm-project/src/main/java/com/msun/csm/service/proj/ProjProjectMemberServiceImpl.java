package com.msun.csm.service.proj;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.msun.csm.util.PageHelperUtil;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.enums.ProjectMemberRoleEnums;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysDept;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.dict.DictProjectRole;
import com.msun.csm.dao.entity.oldimsp.ImspProject;
import com.msun.csm.dao.entity.oldimsp.ImspSysUser;
import com.msun.csm.dao.entity.oldimsp.UserProjectRelation;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectMember;
import com.msun.csm.dao.entity.proj.extend.ProjProjectMemberExtend;
import com.msun.csm.dao.entity.tmp.TmpProjectNewVsOld;
import com.msun.csm.dao.mapper.dict.DictProjectRoleMapper;
import com.msun.csm.dao.mapper.oldimsp.ImspProjectMapper;
import com.msun.csm.dao.mapper.oldimsp.ImspSysUserMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectMemberMapper;
import com.msun.csm.dao.mapper.sysdept.SysDeptMapper;
import com.msun.csm.dao.mapper.sysrole.SysRoleMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.dao.mapper.tmp.TmpProjectNewVsOldMapper;
import com.msun.csm.feign.client.yunying.YunyingFeignClient;
import com.msun.csm.model.convert.ProjProjectMemberConvert;
import com.msun.csm.model.dto.AddProjectMemberDTO;
import com.msun.csm.model.dto.ImspProjectUserDTO;
import com.msun.csm.model.dto.ProjProjectMemberDTO;
import com.msun.csm.model.dto.ProjectMemberInsertToParamDTO;
import com.msun.csm.model.dto.role.SysRoleDTO;
import com.msun.csm.model.dto.user.SysUserDTO;
import com.msun.csm.model.imsp.UpdateProjectUserRelationReq;
import com.msun.csm.model.param.SelectProjectMemberParam;
import com.msun.csm.model.req.project.ProjectMemberReq;
import com.msun.csm.model.req.project.ProjectMemberToYunYingReq;
import com.msun.csm.model.vo.ProjProjectMemberVO;
import com.msun.csm.model.vo.ProjectMemberAndUserParamVO;
import com.msun.csm.model.vo.UpdateMemberToParamVO;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.oldimsp.UserProjectRelationService;
import com.msun.csm.service.operlog.SysOperLogService;
import com.msun.csm.service.yunying.YunYingServiceImpl;
import com.msun.csm.util.FilterUtil;
import com.msun.csm.util.SnowFlakeUtil;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/24
 */

@Slf4j
@Service
public class ProjProjectMemberServiceImpl implements ProjProjectMemberService {

    private final UserHelper userHelper;
    @Resource
    private DictProjectRoleMapper projectRoleMapper;
    @Resource
    private ProjProjectMemberMapper projProjectMemberMapper;
    @Resource
    private SysDeptMapper sysDeptMapper;
    @Resource
    private SysUserMapper userMapper;
    @Resource
    private ProjProjectMemberConvert convert;
    @Resource
    private DictProjectRoleMapper dictProjectRoleMapper;
    @Resource
    private ProjProjectInfoMapper projProjectInfoMapper;
    @Resource
    private UserProjectRelationService userProjectRelationService;
    @Resource
    private ImspSysUserMapper imspSysUserMapper;
    @Resource
    private ImspProjectMapper imspProjectMapper;
    @Resource
    private TmpProjectNewVsOldMapper tmpProjectNewVsOldMapper;

    @Resource
    private ProjCustomInfoService customInfoService;

    @Resource
    private SysRoleMapper sysRoleMapper;

    @Resource
    private YunyingFeignClient yunyingFeignClient;

    @Lazy
    @Resource
    private SysOperLogService sysOperLogService;

    public ProjProjectMemberServiceImpl(UserHelper userHelper) {
        this.userHelper = userHelper;
    }

    @Override
    public int deleteByPrimaryKey(Long projectMemberInfoId) {
        return projProjectMemberMapper.deleteByPrimaryKey(projectMemberInfoId);
    }

    @Override
    public int insert(ProjProjectMember record) {
        return projProjectMemberMapper.insert(record);
    }

    @Override
    public int insertOrUpdate(ProjProjectMember record) {
        return projProjectMemberMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(ProjProjectMember record) {
        return projProjectMemberMapper.insertOrUpdateSelective(record);
    }

    @Override
    public int insertSelective(ProjProjectMember record) {
        return projProjectMemberMapper.insertSelective(record);
    }

    @Override
    public ProjProjectMember selectByPrimaryKey(Long projectMemberInfoId) {
        return projProjectMemberMapper.selectByPrimaryKey(projectMemberInfoId);
    }

    @Override
    public int updateByPrimaryKeySelective(ProjProjectMember record) {
        return projProjectMemberMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ProjProjectMember record) {
        return projProjectMemberMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<ProjProjectMember> list) {
        return projProjectMemberMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<ProjProjectMember> list) {
        return projProjectMemberMapper.updateBatchSelective(list);
    }

    @Override
    public List<ProjProjectMemberVO> getProjMemberListByProjectInfoId(String projectInfoId) {
        List<ProjProjectMember> members =
                projProjectMemberMapper.selectByProjectIds(Collections.singletonList(Long.valueOf(projectInfoId)));
        return convert.po2Vo(members);
    }

    @Override
    public int batchInsert(List<ProjProjectMember> list) {
        return projProjectMemberMapper.batchInsert(list);
    }

    /**
     * 根据条件查询项目下人员信息
     *
     * @param param
     * @return
     */
    @Override
    public Result<ProjectMemberAndUserParamVO> selectProjectMember(SelectProjectMemberParam param) {
        ProjProjectMemberDTO dto = new ProjProjectMemberDTO();
        dto.setProjectInfoId(param.getProjectInfoId());

        List<ProjProjectMemberVO> projProjectMemberVOS = projProjectMemberMapper.selectMemberVO(dto);
        // 修改项目组成员时参数列表
        Result<UpdateMemberToParamVO> updateMemberToParamVOResult = updateMemberToParam(param);
        this.dealCanEdit(projProjectMemberVOS, updateMemberToParamVOResult.getData());
        ProjectMemberAndUserParamVO projectMemberAndUserParamVO = new ProjectMemberAndUserParamVO();
        projectMemberAndUserParamVO.setProjProjectMemberVOList(projProjectMemberVOS);
        projectMemberAndUserParamVO.setUpdateMemberToParamVO(updateMemberToParamVOResult.getData());
        return Result.success(projectMemberAndUserParamVO);
    }

    /**
     * 处理当前登录人是否可以编辑成员信息，分公司经理，前端项目经理，后端项目经理，后端运维经理前
     *
     * @param operationType         操作类型，1表示分公司经理或者前端项目经理维护前端成员信息；2表示后端运维经理或者后端项目经理维护后端成员信息
     * @param projProjectMemberVOS
     * @param updateMemberToParamVO
     */
    private void dealCanEdit(List<ProjProjectMemberVO> projProjectMemberVOS, UpdateMemberToParamVO updateMemberToParamVO) {
        if (CollectionUtils.isEmpty(projProjectMemberVOS)) {
            return;
        }
        for (ProjProjectMemberVO item : projProjectMemberVOS) {
            // 前端角色，当前登录人员是分公司经理
            item.setCanEdit(convertEdit(item, updateMemberToParamVO));

        }
    }

    private boolean convertEdit(ProjProjectMemberVO item, UpdateMemberToParamVO updateMemberToParamVO) {
        // 不再区分前后端绝色
//        // 前端角色
//        if (Integer.valueOf(1).equals(item.getRoleType())) {
//            // 如果是分公司经理或者项目经理，可以编辑
//            return Integer.valueOf(1).equals(updateMemberToParamVO.getIsBranchManagerFlag()) || Integer.valueOf(1).equals(updateMemberToParamVO.getIsLeaderFlag());
//            // 后端角色
//        } else if (Integer.valueOf(2).equals(item.getRoleType())) {
//            // 如果是运维经理或者后端项目经理，可以编辑
//            return Integer.valueOf(1).equals(updateMemberToParamVO.getIsOperationsManagerFlag()) || Integer.valueOf(1).equals(updateMemberToParamVO.getIsBackLeaderFlag());
//        }
        // 分公司经理
        if (Integer.valueOf(1).equals(updateMemberToParamVO.getIsBranchManagerFlag())) {
            return true;
        }
        // 运维经理
        if (Integer.valueOf(1).equals(updateMemberToParamVO.getIsOperationsManagerFlag())) {
            return true;
        }
        // 前端项目经理
        if (Integer.valueOf(1).equals(updateMemberToParamVO.getIsLeaderFlag())) {
            return true;
        }
        // 后端项目经理
        if (Integer.valueOf(1).equals(updateMemberToParamVO.getIsBackLeaderFlag())) {
            return true;
        }
        return false;
    }

    /**
     * 添加项目人员
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result insertMember(AddProjectMemberDTO dto) {
        // 查询当前项目对应的老项目id
        TmpProjectNewVsOld tmpProjectNewVsOld = tmpProjectNewVsOldMapper.selectOne(
                new QueryWrapper<TmpProjectNewVsOld>()
                        .eq("new_project_info_id", dto.getProjectInfoId())
        );
        // 后端项目经理操作时默认为后端工程师
        List<DictProjectRole> dictProjectRoles;
        if (Integer.valueOf(2).equals(dto.getOperationType())) {
            dictProjectRoles = dictProjectRoleMapper.selectList(new QueryWrapper<DictProjectRole>().eq("project_role_code", "back-member"));
        } else {
            // 其他情况默认为项目成员
            dictProjectRoles = dictProjectRoleMapper.selectList(new QueryWrapper<DictProjectRole>().eq("project_role_code", "member"));
        }

        // 向新系统添加人员信息的List
        List<ProjProjectMember> newProjectMemberList = new ArrayList<>();
        // 向老系统添加人员信息的List
        List<ImspProjectUserDTO> oldProjectMemberAddList = new ArrayList<>();
        for (SysUserDTO userDTO : dto.getDtoList()) {
            // 要添加的项目中存在已经在项目中的人员，则返回失败不允许重复添加
            Long aLong = projProjectMemberMapper.selectCount(new QueryWrapper<ProjProjectMember>()
                    .eq("project_info_id", dto.getProjectInfoId())
                    .eq("project_member_id", userDTO.getSysUserId())
            );
            if (aLong > 0) {
                return Result.fail("存在已添加人员，请勿重复添加");
            }
            ProjProjectMember projProjectMember = new ProjProjectMember();
            projProjectMember.setProjectMemberInfoId(SnowFlakeUtil.getId());
            projProjectMember.setProjectInfoId(dto.getProjectInfoId());
            projProjectMember.setProjectMemberId(userDTO.getSysUserId());
            projProjectMember.setProjectMemberName(userDTO.getUserName());
            projProjectMember.setProjectTeamId(userDTO.getDeptId());
            projProjectMember.setProjectTeamName(userDTO.getDeptName());
            projProjectMember.setProjectMemberRoleId(dictProjectRoles.get(0).getProjectRoleId());
            projProjectMember.setPhone(userDTO.getPhone());
            projProjectMember.setIsDeleted(0);
            projProjectMember.setCreaterId(userHelper.getCurrentUser().getSysUserId());
            projProjectMember.setCreateTime(new Date());
            projProjectMember.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
            projProjectMember.setUpdateTime(new Date());
            newProjectMemberList.add(projProjectMember);
            if (tmpProjectNewVsOld != null) {
                // 老系统增加人员参数拼装
                ImspProjectUserDTO imspProjectUserDTO = new ImspProjectUserDTO();
                imspProjectUserDTO.setUserYyId(Integer.parseInt(userDTO.getUserYunyingId()));
                imspProjectUserDTO.setOldProjectId(tmpProjectNewVsOld.getOldProjectInfoId());
                imspProjectUserDTO.setIsProjectManager(false);
                oldProjectMemberAddList.add(imspProjectUserDTO);
            }
        }
        projProjectMemberMapper.batchInsert(newProjectMemberList);
        // 同步向老系统中添加项目成员信息
        if (!CollectionUtils.isEmpty(oldProjectMemberAddList)) {
            saveImspProjectUser(oldProjectMemberAddList);
        }
        return Result.success();
    }

    /**
     * 修改项目组成员
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result updateMember(ProjProjectMemberDTO dto) {

        ProjectMemberReq dtoSS = new ProjectMemberReq();
        SysUser sysUser = sysUserMapper.selectById(dto.getProjectMemberId());
        // 非首期项目，不限制项目经理认证通过的才允许做项目经理，后面需要和产品认证关联起来
        if (dto.getProjectInfoId() != null && ProjectMemberRoleEnums.LEADER.getDesc().equals(dto.getProjectRoleCode())) {
            ProjProjectInfo projProjectInfo = projProjectInfoMapper.selectById(dto.getProjectInfoId());
            if (projProjectInfo.getHisFlag() == 1) {
                dtoSS.setUserYunyingId(sysUser.getUserYunyingId());
                Result<Boolean> result = this.selectProjectMemberFlag(dtoSS);
                if (!result.getData()) {
                    return Result.fail("当前账号尚未完成项目经理资质认证。请完成相关认证流程，通过审核后即可切换至项目经理权限。");
                }
            }
        }

        // 查询项目经理角色信息
        DictProjectRole projectManager = dictProjectRoleMapper.selectOne(new QueryWrapper<DictProjectRole>()
                .eq("project_role_code", ProjectMemberRoleEnums.LEADER.getDesc()));

        DictProjectRole backProjectManager = dictProjectRoleMapper.selectOne(new QueryWrapper<DictProjectRole>()
                .eq("project_role_code", ProjectMemberRoleEnums.BACK_LEADER.getDesc()));
        // 不能修改自己 (项目经理修改自己、分公司经理修改自己)
        Long sysUserId = userHelper.getCurrentUser().getSysUserId();
        // 修改角色
        if (ObjectUtil.isNotEmpty(dto.getProjectMemberRoleId())) {
            // 8.9 分公司经理、项目经理可以修改自己
            //  项目经理不能修改分公司经理
            // 当前登录人的信息
            ProjProjectMember currentUser =
                    projProjectMemberMapper.selectOne(new QueryWrapper<ProjProjectMember>()
                            .eq("project_member_id", sysUserId)
                            .eq("project_info_id", dto.getProjectInfoId())
                    );

            // 当前登录人是项目经理时不可修改他人为项目经理
            if (ObjectUtil.isNotEmpty(currentUser) && currentUser.getProjectMemberRoleId().equals(projectManager.getProjectRoleId())
                    && dto.getProjectRoleCode().equals(ProjectMemberRoleEnums.LEADER.getDesc())) {
                return Result.fail("项目经理不能修改项目经理");
            }

            // 当前登录人是后端项目经理时不可修改他人为后端项目经理
            if (ObjectUtil.isNotEmpty(currentUser) && currentUser.getProjectMemberRoleId().equals(backProjectManager.getProjectRoleId())
                    && dto.getProjectRoleCode().equals(ProjectMemberRoleEnums.BACK_LEADER.getDesc())) {
                return Result.fail("业务服务经理不能修改业务服务经理");
            }

            DictProjectRole dataManager = dictProjectRoleMapper.selectOne(new QueryWrapper<DictProjectRole>()
                    .eq("project_role_code", ProjectMemberRoleEnums.DATA_LEADER.getDesc()));

            // 当前登录人是后端项目经理时不可修改他人为后端项目经理
            if (ObjectUtil.isNotEmpty(currentUser) && currentUser.getProjectMemberRoleId().equals(dataManager.getProjectRoleId())
                    && dto.getProjectRoleCode().equals(ProjectMemberRoleEnums.DATA_LEADER.getDesc())) {
                return Result.fail("数据服务经理不能修改数据服务经理");
            }

            DictProjectRole interfaceManager = dictProjectRoleMapper.selectOne(new QueryWrapper<DictProjectRole>()
                    .eq("project_role_code", ProjectMemberRoleEnums.INTERFACE_LEADER.getDesc()));

            // 当前登录人是后端项目经理时不可修改他人为后端项目经理
            if (ObjectUtil.isNotEmpty(currentUser) && currentUser.getProjectMemberRoleId().equals(interfaceManager.getProjectRoleId())
                    && dto.getProjectRoleCode().equals(ProjectMemberRoleEnums.INTERFACE_LEADER.getDesc())) {
                return Result.fail("接口服务经理不能修改接口服务经理");
            }
            // 查询修改的人员信息是否是项目经理角色。当是项目经理的角色时  修改 projectInfo表中的 项目经理
            if (ObjectUtil.isNotEmpty(dto) && dto.getProjectMemberRoleId().equals(projectManager.getProjectRoleId())) {
                ProjProjectInfo projProjectInfo = new ProjProjectInfo();
                projProjectInfo.setProjectLeaderId(dto.getProjectMemberId());
                projProjectInfo.setProjectInfoId(dto.getProjectInfoId());
                projProjectInfoMapper.updateById(projProjectInfo);
                // 原来的项目经理
                ProjProjectMember xiangMuJingLi =
                        projProjectMemberMapper.selectOne(new QueryWrapper<ProjProjectMember>()
                                .eq("project_member_role_id", projectManager.getProjectRoleId())
                                .eq("project_info_id", dto.getProjectInfoId())
                        );
                TmpProjectNewVsOld tmpProjectNewVsOld =
                        tmpProjectNewVsOldMapper.selectOne(new QueryWrapper<TmpProjectNewVsOld>()
                                .eq("new_project_info_id", dto.getProjectInfoId())
                        );
                // 老系统 项目表中项目经理id同步进行修改
                SysUser user = sysUserMapper.selectById(dto.getProjectMemberId());
                if (ObjectUtil.isEmpty(user)) {
                    return Result.fail("用户不存在，请检查该账号还是否在使用。删除后重新添加");
                }
                ImspSysUser imspSysUser = imspSysUserMapper.selectOne(new QueryWrapper<ImspSysUser>()
                        .eq("user_yunying_id", Convert.toLong(user.getUserYunyingId()))
                        .eq("status", "ENABLE")
                );
                ImspProject imspProject = new ImspProject();
                imspProject.setId(tmpProjectNewVsOld.getOldProjectInfoId());
                imspProject.setProjectManagerId(imspSysUser.getUserId());
                imspProject.setContact(imspSysUser.getName());
                imspProjectMapper.updateById(imspProject);
                // 如果原来已经有项目经理了，把原来的项目经理设定为项目成员
                if (null != xiangMuJingLi) {
                    // 项目成员角色ID
                    DictProjectRole xiangMuChengYuan = dictProjectRoleMapper.selectOne(
                            new QueryWrapper<DictProjectRole>()
                                    .eq("project_role_code", "member"));
                    ProjProjectMember updateParam = new ProjProjectMember();
                    updateParam.setProjectMemberInfoId(xiangMuJingLi.getProjectMemberInfoId());
                    updateParam.setProjectMemberRoleId(xiangMuChengYuan.getProjectRoleId());
                    projProjectMemberMapper.updateByPrimaryKeySelective(updateParam);
                }
            }

            // 修改后端项目经理
            if (dto.getProjectMemberRoleId().equals(backProjectManager.getProjectRoleId())) {
                // 原来的后端项目经理
                List<ProjProjectMember> existBackProjectManager =
                        projProjectMemberMapper.selectList(
                                new QueryWrapper<ProjProjectMember>()
                                        .eq("project_member_role_id", backProjectManager.getProjectRoleId())
                                        .eq("project_info_id", dto.getProjectInfoId())
                        );

                if (!CollectionUtils.isEmpty(existBackProjectManager)) {
                    // 项目成员角色ID
                    DictProjectRole backMember = dictProjectRoleMapper.selectOne(
                            new QueryWrapper<DictProjectRole>()
                                    .eq("project_role_code", "back-member")
                    );
                    for (ProjProjectMember projProjectMember : existBackProjectManager) {
                        ProjProjectMember updateParam = new ProjProjectMember();
                        updateParam.setProjectMemberInfoId(projProjectMember.getProjectMemberInfoId());
                        updateParam.setProjectMemberRoleId(backMember.getProjectRoleId());
                        projProjectMemberMapper.updateByPrimaryKeySelective(updateParam);
                    }
                }
            }

            // 修改后端项目经理
            if (dto.getProjectMemberRoleId().equals(dataManager.getProjectRoleId())) {
                // 原来的后端项目经理
                List<ProjProjectMember> existBackProjectManager =
                        projProjectMemberMapper.selectList(
                                new QueryWrapper<ProjProjectMember>()
                                        .eq("project_member_role_id", dataManager.getProjectRoleId())
                                        .eq("project_info_id", dto.getProjectInfoId())
                        );

                if (!CollectionUtils.isEmpty(existBackProjectManager)) {
                    // 项目成员角色ID
                    DictProjectRole backMember = dictProjectRoleMapper.selectOne(
                            new QueryWrapper<DictProjectRole>()
                                    .eq("project_role_code", ProjectMemberRoleEnums.DATA_MEMBER.getDesc())
                    );
                    for (ProjProjectMember projProjectMember : existBackProjectManager) {
                        ProjProjectMember updateParam = new ProjProjectMember();
                        updateParam.setProjectMemberInfoId(projProjectMember.getProjectMemberInfoId());
                        updateParam.setProjectMemberRoleId(backMember.getProjectRoleId());
                        projProjectMemberMapper.updateByPrimaryKeySelective(updateParam);
                    }
                }
            }

            // 修改后端项目经理
            if (dto.getProjectMemberRoleId().equals(interfaceManager.getProjectRoleId())) {
                // 原来的后端项目经理
                List<ProjProjectMember> existBackProjectManager =
                        projProjectMemberMapper.selectList(
                                new QueryWrapper<ProjProjectMember>()
                                        .eq("project_member_role_id", interfaceManager.getProjectRoleId())
                                        .eq("project_info_id", dto.getProjectInfoId())
                        );

                if (!CollectionUtils.isEmpty(existBackProjectManager)) {
                    // 项目成员角色ID
                    DictProjectRole backMember = dictProjectRoleMapper.selectOne(
                            new QueryWrapper<DictProjectRole>()
                                    .eq("project_role_code", ProjectMemberRoleEnums.INTERFACE_MEMBER.getDesc())
                    );
                    for (ProjProjectMember projProjectMember : existBackProjectManager) {
                        ProjProjectMember updateParam = new ProjProjectMember();
                        updateParam.setProjectMemberInfoId(projProjectMember.getProjectMemberInfoId());
                        updateParam.setProjectMemberRoleId(backMember.getProjectRoleId());
                        projProjectMemberMapper.updateByPrimaryKeySelective(updateParam);
                    }
                }
            }
        }
        projProjectMemberMapper.updateById(convert.dto2Po(dto));
        return Result.success();
    }

    /**
     * 删除项目组成员
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result deleteMember(ProjProjectMemberDTO dto) {
        // 查询要删除的项目组成员信息
        ProjProjectMember projProjectMember = projProjectMemberMapper.selectById(dto.getProjectMemberInfoId());
        // 不允许删除自己
        Long sysUserId = userHelper.getCurrentUser().getSysUserId();
        if (projProjectMember.getProjectMemberId().equals(sysUserId)) {
            return Result.fail("不能删除自己");
        }
        // 查询当前人员的运营id
        SysUser user = userMapper.selectById(projProjectMember.getProjectMemberId());
        if (ObjectUtil.isNotEmpty(user)) {
            // 根据当前人员的运营id查询老项目的人员id
            List<ImspSysUser> imspSysUserList = imspSysUserMapper.selectList(new QueryWrapper<ImspSysUser>()
                    .eq("user_yunying_id", Convert.toLong(user.getUserYunyingId()))
                    .eq("status", "ENABLE")
            );
            if (!CollectionUtils.isEmpty(imspSysUserList)) {
                // 查询当前项目对应的老项目id
                TmpProjectNewVsOld tmpProjectNewVsOld =
                        tmpProjectNewVsOldMapper.selectOne(new QueryWrapper<TmpProjectNewVsOld>()
                                .eq("new_project_info_id", projProjectMember.getProjectInfoId())
                        );
                List<Long> collect = imspSysUserList.stream().map(ImspSysUser::getUserId).collect(Collectors.toList());
                // 同步删除老系统
                userProjectRelationService.remove(new QueryWrapper<UserProjectRelation>()
                        .in("user_id", collect)
                        .eq("project_id", tmpProjectNewVsOld.getOldProjectInfoId())
                );
            }
        }
        // 删除当前项目成员
        projProjectMemberMapper.deleteByPrimaryKey(dto.getProjectMemberInfoId());
        return Result.success();
    }


    @Resource
    private SysUserMapper sysUserMapper;

    /**
     * 判断当前登录用户在当前项目下是否为分公司经理
     *
     * @param sysUserId     当前登录用户ID
     * @param projectInfoId 项目ID
     * @return true-是；false-不是
     */
    private boolean isBranchManager(Long sysUserId, Long projectInfoId) {
        // 项目信息
        ProjProjectInfo projProjectInfo = projProjectInfoMapper.selectOne(
                new QueryWrapper<ProjProjectInfo>().eq("project_info_id", projectInfoId));
        // 实施地客户信息
        ProjCustomInfo customInfo = customInfoService.selectByPrimaryKey(projProjectInfo.getCustomInfoId());
        // 当前实施地客户对应的客服团队数据。根据团队的负责人是分公司经理 判断当前人是否为团队负责人
        Long infoCustomTeamId = customInfo.getCustomTeamId();
        SysDept sysDept = sysDeptMapper.selectOne(new QueryWrapper<SysDept>().eq("dept_yunying_id", infoCustomTeamId));
        SysUser sysUser = sysUserMapper.selectOne(
                new QueryWrapper<SysUser>().eq("user_yunying_id", String.valueOf(sysDept.getDeptLeaderYunyingId())));
        // 查到的数据的userId不是当前登录用户的userId，则不是分公司经理
        if (null != sysUser && sysUser.getSysUserId().equals(sysUserId)) {
            return true;
        }
        List<SysRoleDTO> resRoles = sysRoleMapper.selectRoleByUserId(sysUserId);
        // 没查询到角色信息，不是分公司经理
        if (CollectionUtils.isEmpty(resRoles)) {
            return false;
        }
        // 当前用户拥有角色的角色编码
        List<String> currentUserRoleCodeList = resRoles.stream().map(SysRoleDTO::getRoleCode)
                .collect(Collectors.toList());
        // 需要认定为分公司经理的角色编码：1-系统管理员；11-客服中心总经理；12-客服分公司经理
        // 新需求： 【董博培】 系统管理员不应该有修改成员的权限
        final List<String> roleCodeList = Arrays.asList("11", "12");
        for (String roleCode : roleCodeList) {
            // 拥有任何一个角色，就认定为分公司经理
            if (currentUserRoleCodeList.contains(roleCode)) {
                return true;
            }
        }
        return false;
    }

    private boolean isBackendLeader(ProjProjectMemberVO projProjectMember) {
        if (ProjectMemberRoleEnums.BACK_LEADER.getDesc().equals(projProjectMember.getProjectRoleCode())) {
            return true;
        }
        if (ProjectMemberRoleEnums.DATA_LEADER.getDesc().equals(projProjectMember.getProjectRoleCode())) {
            return true;
        }
        return ProjectMemberRoleEnums.INTERFACE_LEADER.getDesc().equals(projProjectMember.getProjectRoleCode());
    }

    @Override
    public Result<UpdateMemberToParamVO> updateMemberToParam(SelectProjectMemberParam param) {
        UpdateMemberToParamVO updateMemberToParamVO = new UpdateMemberToParamVO();
        List<DictProjectRole> roleList = new ArrayList<>();
        // 登录人id 与项目人id一致 。 判断登录人在项目中的角色是不是项目经理与分公司经理
        Long sysUserId = userHelper.getCurrentUser().getSysUserId();
        // 判断当前登录人是否是分公司经理
        updateMemberToParamVO.setIsBranchManagerFlag(this.isBranchManager(sysUserId, param.getProjectInfoId()) ? 1 : 0);
        ProjProjectMemberDTO queryParam = new ProjProjectMemberDTO();
        queryParam.setProjectInfoId(param.getProjectInfoId());
        queryParam.setProjectMemberId(sysUserId);
        List<ProjProjectMemberVO> projProjectMemberVOS = projProjectMemberMapper.selectMemberVO(queryParam);
        if (CollUtil.isNotEmpty(projProjectMemberVOS)) {
            ProjProjectMemberVO projProjectMember = projProjectMemberVOS.get(0);
            // 判断当前登录人是否是项目经理
            updateMemberToParamVO.setIsLeaderFlag(
                    ProjectMemberRoleEnums.LEADER.getDesc().equals(projProjectMember.getProjectRoleCode()) ? 1 : 0);
            // 当前登录人是项目经理时 ， 角色列表数据
            if (updateMemberToParamVO.getIsLeaderFlag() == 1) {
                DictProjectRole dictProjectRoleParam = new DictProjectRole();
                dictProjectRoleParam.setProjectRoleCode(ProjectMemberRoleEnums.LEADER.getDesc());
                List<DictProjectRole> dictProjectRoles = projectRoleMapper.selectDictProjectRole(dictProjectRoleParam);
                roleList.addAll(dictProjectRoles);
            }

            // 判断当前登录人是否是后端项目经理
            updateMemberToParamVO.setIsBackLeaderFlag(isBackendLeader(projProjectMember) ? 1 : 0);

            // 当前登录人是后端项目经理时 ， 角色列表数据
            if (updateMemberToParamVO.getIsBackLeaderFlag() == 1) {
                DictProjectRole dictProjectRoleParam = new DictProjectRole();
                List<DictProjectRole> dictProjectRoles = projectRoleMapper.selectDictProjectRole(dictProjectRoleParam);
                roleList.addAll(dictProjectRoles);
            }
        }
        //分公司经理时 角色列表数据
        if (updateMemberToParamVO.getIsBranchManagerFlag() == 1) {
            DictProjectRole dictProjectRoleParam = new DictProjectRole();
            List<DictProjectRole> dictProjectRoles = projectRoleMapper.selectDictProjectRole(dictProjectRoleParam);
            roleList.addAll(dictProjectRoles);
        }

        // 判断当前登录人是否是分公司经理
        updateMemberToParamVO.setIsOperationsManagerFlag(this.isOperationsManagerFlag(sysUserId) ? 1 : 0);

        if (updateMemberToParamVO.getIsOperationsManagerFlag() == 1) {
            DictProjectRole dictProjectRoleParam = new DictProjectRole();
            List<DictProjectRole> dictProjectRoles = projectRoleMapper.selectDictProjectRole(dictProjectRoleParam);
            roleList.addAll(dictProjectRoles);
        }
        List<DictProjectRole> collect = roleList.stream().filter(FilterUtil.distinctByKey(DictProjectRole::getProjectRoleCode)).collect(Collectors.toList());
        updateMemberToParamVO.setRoleList(collect);
        return Result.success(updateMemberToParamVO);
    }

    /**
     * 判断当前登录人是否为运维经理，true-是。false-不是
     */
    private boolean isOperationsManagerFlag(Long sysUserId) {
        List<SysRoleDTO> resRoles = sysRoleMapper.selectRoleByUserId(sysUserId);
        // 没查询到角色信息，不是运维经理
        if (CollectionUtils.isEmpty(resRoles)) {
            return false;
        }
        // 当前用户拥有角色的角色编码
        List<String> currentUserRoleCodeList = resRoles.stream().map(SysRoleDTO::getRoleCode)
                .collect(Collectors.toList());
        // 拥有运维经理角色即返回true
        return currentUserRoleCodeList.contains("OperationsManager");
    }


    /**
     * 项目人员添加时参数列表
     *
     * @param dto
     * @return
     */
    @Override
    public Result<PageInfo<SysUserVO>> projectMemberInsertToParam(ProjectMemberInsertToParamDTO dto) {
        List<SysUserVO> userList;
        SysUserDTO userDTO = new SysUserDTO();
        userDTO.setUserName(dto.getUserName());
        if (ObjectUtil.isNotEmpty(dto.getIsSelectTeamFlag()) && dto.getIsSelectTeamFlag() == 1) {
            // 获取当前登录人的部门信息
            Long userDeptId = userHelper.getCurrentUser().getDeptId();
            // 查询当前部门下的人员信息
            userDTO.setDeptId(userDeptId);
        }
        userList = PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> userMapper.selectUserList(userDTO));
        return Result.success(new PageInfo<>(userList));
    }

    /**
     * 向老系统添加项目成员
     *
     * @param dtoList
     * @return
     */
    @SneakyThrows
    @Override
    public Result saveImspProjectUser(List<ImspProjectUserDTO> dtoList) {
        try {
            for (ImspProjectUserDTO addMemberInfo : dtoList) {
                // 查询人员id
                QueryWrapper<ImspSysUser> userYunyingId = new QueryWrapper<ImspSysUser>()
                        .eq("user_yunying_id", addMemberInfo.getUserYyId())
                        .eq("status", "ENABLE");
                ImspSysUser imspSysUser = imspSysUserMapper.selectOne(userYunyingId);
                // 判断是否为项目经理，当为项目经理时 先修改项目表中 项目经理信息
                if (Boolean.TRUE.equals(addMemberInfo.getIsProjectManager())) {
                    ImspProject imspProject = new ImspProject();
                    imspProject.setId(addMemberInfo.getOldProjectId());
                    imspProject.setProjectManagerId(imspSysUser.getUserId());
                    imspProject.setContact(imspSysUser.getName());
                    imspProjectMapper.updateById(imspProject);
                } else {
                    // 当为项目成员时，更新项目成员表中人员信息
                    UserProjectRelation userProjectRelation = new UserProjectRelation();
                    userProjectRelation.setUserId(imspSysUser.getUserId());
                    userProjectRelation.setProjectId(addMemberInfo.getOldProjectId());
                    userProjectRelationService.saveOrUpdate(userProjectRelation);
                }
            }
            return Result.success();
        } catch (Exception e) {
            log.error("向老系统添加项目人员，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            throw new Exception("添加项目人员失败");
        }
    }

    /**
     * 保存项目成员关系
     *
     * @param req
     * @return
     */
    @Override
    public Result updateProjectUserRelation(UpdateProjectUserRelationReq req) {
        List<TmpProjectNewVsOld> projectNewVsOldList = tmpProjectNewVsOldMapper.selectByOldProjectIds(
                Collections.singletonList(req.getProjectId()));
        if (CollectionUtil.isEmpty(projectNewVsOldList)) {
            return Result.success("项目对照不存在");
        }
        Long projectInfoId = projectNewVsOldList.get(0).getNewProjectInfoId();
        // 查询项目成员
        QueryWrapper<SysUser> sysUserQueryWrapper = new QueryWrapper<>();
        sysUserQueryWrapper.in("user_yunying_id", req.getUserYYId());
        List<SysUser> sysUsers = userMapper.selectList(sysUserQueryWrapper);
        List<ProjProjectMember> projProjectMembers = new ArrayList<>();
        Date now = new Date();
        if (req.getType() == 2) {
            // 删除
            projProjectMemberMapper.deleteByParam(projectInfoId,
                    sysUsers.stream().map(SysUser::getSysUserId).collect(Collectors.toList()));
        } else if (req.getType() == 1) {
            // 添加
            sysUsers.stream().forEach(sysUser -> {
                ProjProjectMember projectMember = new ProjProjectMemberExtend(projectInfoId, sysUser, -1L, now);
                projProjectMembers.add(projectMember);
            });
            projProjectMemberMapper.batchInsert(projProjectMembers);
        }
        return Result.success();
    }

    /**
     * 项目成员下拉列表
     *
     * @param dto
     * @return
     */
    @Override
    public Result<List<BaseIdNameResp>> selectProjectMemberDropDownList(ProjProjectMemberDTO dto) {
        List<ProjProjectMemberVO> projProjectMemberVOS = new ArrayList<>();
        if (dto.getProjectInfoId() == null) {
            projProjectMemberVOS = projProjectMemberMapper.selectMemberVOAll(dto);
        } else {
            projProjectMemberVOS = projProjectMemberMapper.selectMemberVO(dto);
        }

        List<BaseIdNameResp> list = projProjectMemberVOS.stream().map(projProjectMemberVO -> {
            return new BaseIdNameResp(projProjectMemberVO.getProjectMemberId(),
                    projProjectMemberVO.getProjectMemberName());
        }).collect(Collectors.toList());
        return Result.success(list);
    }

    /**
     * 查询是否可修改为项目经理
     *
     * @param dto
     * @return
     */
    @Override
    public Result<Boolean> selectProjectMemberFlag(ProjectMemberReq dto) {
        Boolean flag = false;
        String uuid = StrUtil.uuid().replace(StrUtil.DASHED, StrUtil.EMPTY);
        try {
            ProjectMemberToYunYingReq req = new ProjectMemberToYunYingReq();
            req.setToken(YunYingServiceImpl.TOKEN);
            req.setUserId(Long.parseLong(dto.getUserYunyingId()));
            // 调用运营平台接口查询是否项目经理
            // 获取登录人账号
            log.error("查询是否可修改为项目经理，调用运营平台接口查询是否项目经理，req={}", req);
            sysOperLogService.apiOperLogInsertObjAry("查询是否可修改为项目经理==>selectProjectMemberFlag===>入", uuid, Log.LogOperType.OTHER.getCode(), req);
            String loginUserAccount = userHelper.getCurrentUser().getAccount();
            String result = yunyingFeignClient.selectProjectMemberFlag(loginUserAccount, req);
            sysOperLogService.apiOperLogInsertObjAry("查询是否可修改为项目经理==>selectProjectMemberFlag==>出", uuid, Log.LogOperType.OTHER.getCode(), result);
            log.error("查询是否可修改为项目经理，调用运营平台接口查询是否项目经理，result={}", result);
            if (StrUtil.isNotBlank(result)) {
                JSONObject jsonObject = JSONObject.parseObject(result);
                if (ObjectUtil.isNotEmpty(jsonObject) && ((boolean) jsonObject.get("success"))) {
                    JSONObject data = (JSONObject) jsonObject.get("obj");
                    Object status = data != null ? data.get("status") : "";
                    if (("1".equals(String.valueOf(status)) || "2".equals(String.valueOf(status)))) {
                        flag = true;
                    }
                }
            }
        } catch (Exception e) {
            log.error("查询是否可修改为项目经理，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            sysOperLogService.apiOperLogInsertObjAry("查询是否可修改为项目经理==>selectProjectMemberFlag==>异常", uuid, Log.LogOperType.OTHER.getCode(), dto);
        }
        // 查询当前人是否是系统管理员
        List<SysUser> currentUser = sysUserMapper.getUserVluesByYunyingId(dto.getUserYunyingId());
        if (currentUser != null && currentUser.size() > 0) {
            flag = true;
        }
        return Result.success(flag);
    }

    @Override
    public boolean isBackendLeader(Long projectInfoId) {
        Long currentUser = userHelper.getCurrentSysUserIdWithDefaultValue();
        log.info("判断当前登录人是否为后端项目经理，当前登录人ID={}", currentUser);
        // 当前人在项目成员中的信息
        ProjProjectMember projProjectMember = projProjectMemberMapper.selectByProjectIdAndMemberId(projectInfoId, currentUser);

        // 不在项目成员中时，直接返回没有后端项目经理权限
        if (projProjectMember == null) {
            return false;
        }

        // 对应的项目成员字典信息
        DictProjectRole dictProjectRole = dictProjectRoleMapper.selectByPrimaryKey(projProjectMember.getProjectMemberRoleId());

        // 项目成员角色不在字典中时，直接返回没有后端项目经理权限
        if (dictProjectRole == null) {
            return false;
        }
        return "backendManager".equals(dictProjectRole.getRolePermission());
    }
}
