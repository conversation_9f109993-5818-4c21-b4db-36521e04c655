package com.msun.csm.service.proj;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.msun.csm.dao.entity.proj.ProjSpecialProductRecord;
import com.msun.csm.dao.mapper.proj.ProjSpecialProductRecordMapper;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/24
 */

@Service
public class ProjSpecialProductRecordServiceImpl implements ProjSpecialProductRecordService {

    @Resource
    private ProjSpecialProductRecordMapper projSpecialProductRecordMapper;

    @Override
    public int deleteByPrimaryKey(Long specialProductRecordId) {
        return projSpecialProductRecordMapper.deleteByPrimaryKey(specialProductRecordId);
    }

    @Override
    public int insert(ProjSpecialProductRecord record) {
        return projSpecialProductRecordMapper.insert(record);
    }

    @Override
    public int insertOrUpdate(ProjSpecialProductRecord record) {
        return projSpecialProductRecordMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(ProjSpecialProductRecord record) {
        return projSpecialProductRecordMapper.insertOrUpdateSelective(record);
    }

    @Override
    public int insertSelective(ProjSpecialProductRecord record) {
        return projSpecialProductRecordMapper.insertSelective(record);
    }

    @Override
    public ProjSpecialProductRecord selectByPrimaryKey(Long specialProductRecordId) {
        return projSpecialProductRecordMapper.selectByPrimaryKey(specialProductRecordId);
    }

    @Override
    public int updateByPrimaryKeySelective(ProjSpecialProductRecord record) {
        return projSpecialProductRecordMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ProjSpecialProductRecord record) {
        return projSpecialProductRecordMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<ProjSpecialProductRecord> list) {
        return projSpecialProductRecordMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<ProjSpecialProductRecord> list) {
        return projSpecialProductRecordMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<ProjSpecialProductRecord> list) {
        return projSpecialProductRecordMapper.batchInsert(list);
    }
}
