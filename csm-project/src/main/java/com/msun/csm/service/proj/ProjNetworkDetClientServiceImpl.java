package com.msun.csm.service.proj;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import javax.annotation.Resource;

import com.msun.csm.util.PageHelperUtil;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjNetworkDetClient;
import com.msun.csm.dao.entity.proj.ProjNetworkDetClientRelative;
import com.msun.csm.dao.mapper.proj.ProjNetworkDetClientMapper;
import com.msun.csm.model.convert.ProjNetworkDetClientConvert;
import com.msun.csm.model.convert.ProjNetworkDetClientRelativeConvert;
import com.msun.csm.model.dto.networkdetect.ProjNetworkDetClientDTO;
import com.msun.csm.model.param.ProjNetworkDetClientParam;
import com.msun.csm.model.vo.networkdetected.ProjNetworkDetClientRelativeVO;
import com.msun.csm.util.SnowFlakeUtil;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;

/**
 * <AUTHOR>
 * @since 2024-05-16 04:01:06
 */

@Service
public class ProjNetworkDetClientServiceImpl implements ProjNetworkDetClientService {

    @Resource
    private ProjNetworkDetClientMapper projNetworkDetClientMapper;

    @Resource
    private ProjNetworkDetClientConvert clientConvert;

    @Resource
    private ProjNetworkDetClientRelativeConvert clientRelativeConvert;

    @Override
    public Result<PageInfo<ProjNetworkDetClientRelativeVO>> findNetworkDetClientInfoList(ProjNetworkDetClientDTO dto) {
        List<ProjNetworkDetClientRelative> clients = PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> findNetworkDetClientInfoListImpl(dto));
        PageInfo<ProjNetworkDetClientRelative> pageInfo = new PageInfo<>(clients);
        List<ProjNetworkDetClientRelativeVO> vos = clientRelativeConvert.po2Vo(clients);
        PageInfo<ProjNetworkDetClientRelativeVO> pageInfoVO = PageInfo.of(vos);
        pageInfoVO.setTotal(pageInfo.getTotal());
        return Result.success(pageInfoVO);
    }

    /**
     * 查询数据
     *
     * @param dto
     * @return
     */
    public List<ProjNetworkDetClientRelative> findNetworkDetClientInfoListImpl(ProjNetworkDetClientDTO dto) {
        ProjNetworkDetClientParam param = BeanUtil.copyProperties(dto, ProjNetworkDetClientParam.class);
        return projNetworkDetClientMapper.findNetworkDetClientInfoList(param);
    }

    @Override
    public int updateClient(ProjNetworkDetClientDTO clientDTO) {
        ProjNetworkDetClient client = clientConvert.dto2Po(clientDTO);
        client.setHospitalInfoId(Long.valueOf(clientDTO.getLogId()));
        // 查询是否存在此医院
        List<ProjNetworkDetClient> clients = projNetworkDetClientMapper.findCountNetworkDetClientInfo(Long.valueOf(clientDTO.getLogId()),
                client.getLocalIpAddress(), client.getRandomCode());
        if (CollUtil.isEmpty(clients)) {
            client.setId(SnowFlakeUtil.getId());
            return projNetworkDetClientMapper.insert(client);
        } else {
            return projNetworkDetClientMapper.update(client,
                    new QueryWrapper<ProjNetworkDetClient>().eq("random_code", client.getRandomCode())
                            .eq("hospital_info_id", client.getHospitalInfoId())
                            .eq("local_ip_address", client.getLocalIpAddress()));
        }
    }

    public List<ProjNetworkDetClient> findCountNetworkDetClientInfoByHospitalId(Long hospitalInfoId, Long randomCode) {
        return projNetworkDetClientMapper.findCountNetworkDetClientInfoByHospitalId(hospitalInfoId, randomCode);
    }

    @Override
    public List<ProjNetworkDetClient> findClient(ProjNetworkDetClient client) {
        return projNetworkDetClientMapper.findClient(client);
    }

    @Override
    public int getCurrentDetClientCount(ProjNetworkDetClientDTO clientDTO) {
        int clientCount;
        ProjNetworkDetClient detClient = new ProjNetworkDetClient();
        detClient.setHospitalInfoId(Long.valueOf(clientDTO.getLogId()));
        List<ProjNetworkDetClient> clients = findClient(detClient);
        if (CollUtil.isEmpty(clients)) {
            clientCount = 1;
        } else {
            AtomicInteger count = new AtomicInteger();
            if (clients.stream().anyMatch(e -> e.getHospitalInfoId().longValue() == Long.valueOf(clientDTO.getLogId()).longValue()
                    && e.getRandomCode().longValue() != Long.valueOf(clientDTO.getRandomCode()).longValue())) {
                count.addAndGet(1);
            }
            clientCount = count.addAndGet(1);
        }
        return clientCount;
    }

    @Override
    public void deleteNotThieDet(Long hospitalId, String localIpAddress, Long randomCode) {
        projNetworkDetClientMapper.deleteNotThieDet(hospitalId, localIpAddress, randomCode);
    }

    @Override
    public ProjNetworkDetClient getByRandomCode(Long randomCode) {
        return projNetworkDetClientMapper.getByRandomCode(randomCode);
    }
}
