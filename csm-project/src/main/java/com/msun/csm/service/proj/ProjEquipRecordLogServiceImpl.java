package com.msun.csm.service.proj;

import java.util.Date;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjEquipRecordLog;
import com.msun.csm.dao.mapper.proj.ProjEquipRecordLogMapper;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.util.SnowFlakeUtil;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2025/01/14/16:15
 */
@Service
@Slf4j
public class ProjEquipRecordLogServiceImpl implements ProjEquipRecordLogService {

    @Resource
    private ProjEquipRecordLogMapper projEquipRecordLogMapper;

    @Resource
    private UserHelper userHelper;

    /**
     * 保存设备记录日志
     *
     * @param log
     * @return
     */
    @Override
    public Result saveLog(ProjEquipRecordLog log) {
        if (ObjectUtil.isEmpty(log.getOperatorId())) {
            log.setOperatorId(userHelper.getCurrentUser().getSysUserId());
            log.setOperatorName(userHelper.getCurrentUser().getUserName());
            log.setTel(userHelper.getCurrentUser().getPhone());
        }
        log.setEquipRecordLogId(SnowFlakeUtil.getId());
        log.setOperateTime(new Date());
        projEquipRecordLogMapper.insert(log);
        return Result.success();
    }
}
