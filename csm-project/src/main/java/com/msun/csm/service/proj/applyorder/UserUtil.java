package com.msun.csm.service.proj.applyorder;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.msun.csm.model.vo.user.UserHelper;

import cn.hutool.core.util.StrUtil;

/**
 * 申请工单工具类
 */
@Component
public class UserUtil {

    @Resource
    private UserHelper userHelper;

    public String getCurrentUserName() {
        String applicat = StrUtil.EMPTY;
        try {
            // 测试使用, 未登录时抛出异常
            applicat = userHelper.getCurrentUser().getUserName();
        } catch (Throwable e) {
        }
        return applicat;
    }

    /**
     * 获取当前登录人员id
     *
     * @return Long
     */
    public Long getCurrentUserId() {
        Long sysUserId = null;
        try {
            // 测试使用, 未登录时抛出异常
            sysUserId = userHelper.getCurrentUser().getSysUserId();
        } catch (Throwable e) {
        }
        return sysUserId;
    }
}
