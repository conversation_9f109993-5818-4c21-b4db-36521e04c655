package com.msun.csm.service.proj.disastercloud;

import static com.msun.csm.service.proj.disastercloud.IDocVarConst.VAR_APPLY_DAY;
import static com.msun.csm.service.proj.disastercloud.IDocVarConst.VAR_APPLY_MONTH;
import static com.msun.csm.service.proj.disastercloud.IDocVarConst.VAR_APPLY_YEAR;
import static com.msun.csm.service.proj.disastercloud.IDocVarConst.VAR_CUSTOM_NAME;
import static com.msun.csm.service.proj.disastercloud.IDocVarConst.VAR_DOWN_DAY;
import static com.msun.csm.service.proj.disastercloud.IDocVarConst.VAR_DOWN_MONTH;
import static com.msun.csm.service.proj.disastercloud.IDocVarConst.VAR_DOWN_YEAR;
import static com.msun.csm.service.proj.disastercloud.IDocVarConst.VAR_START_DAY;
import static com.msun.csm.service.proj.disastercloud.IDocVarConst.VAR_START_MONTH;
import static com.msun.csm.service.proj.disastercloud.IDocVarConst.VAR_START_YEAR;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.FileUtils;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.cloudservice.CloudServiceKit;
import com.msun.csm.common.constants.ObsExpireTimeConsts;
import com.msun.csm.common.enums.DisasterRecoveryApplyStatusEnum;
import com.msun.csm.common.enums.DispatchTypeEnum;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.YunweiPaysignePreFlagTypeEnum;
import com.msun.csm.common.enums.api.yunying.OrderTypeEnums;
import com.msun.csm.common.enums.config.SerialNumType;
import com.msun.csm.common.enums.could.CloudPhaseTypeEnum;
import com.msun.csm.common.enums.could.DisasterCloudMainStatusEnum;
import com.msun.csm.common.enums.message.DictMessageTypeEnum;
import com.msun.csm.common.enums.message.MsgToCategory;
import com.msun.csm.common.enums.projprojectinfo.ProjectTypeEnums;
import com.msun.csm.common.enums.sysfile.SysFileEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.dto.CloudServiceRenewalTimeDTO;
import com.msun.csm.common.model.dto.CloudServiceTimeResult;
import com.msun.csm.common.model.dto.ProjectFileInfoDTO;
import com.msun.csm.common.model.yunying.YunyingResult;
import com.msun.csm.dao.entity.SysFile;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.proj.ProjContractInfo;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjDisasterRecoveryApply;
import com.msun.csm.dao.entity.proj.ProjDisasterRecoveryApplyLog;
import com.msun.csm.dao.entity.proj.ProjDisasterRecoveryInfo;
import com.msun.csm.dao.entity.proj.ProjDisasterRecoveryInfoRelative;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfoRelative;
import com.msun.csm.dao.entity.proj.ProjOrderProduct;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.extend.ProjProjectFileExtend;
import com.msun.csm.dao.mapper.proj.ProjContractInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjDisasterRecoveryApplyLogMapper;
import com.msun.csm.dao.mapper.proj.ProjDisasterRecoveryApplyMapper;
import com.msun.csm.dao.mapper.proj.ProjDisasterRecoveryInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderProductMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectFileMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.sysfile.SysFileMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.feign.client.yunying.YunyingFeignClient;
import com.msun.csm.feign.entity.yunying.enums.OrderStepEnum;
import com.msun.csm.feign.entity.yunying.req.FileReq;
import com.msun.csm.feign.entity.yunying.req.ProductReq;
import com.msun.csm.feign.entity.yunying.req.YunyingDisasterCloudCheckApplyDTO;
import com.msun.csm.feign.entity.yunying.req.YunyingDisasterCloudPaysignageDTO;
import com.msun.csm.feign.entity.yunying.req.YunyingPaysignageDataDTO;
import com.msun.csm.model.convert.ProjDisasterRecoveryInfoRelativeConvert;
import com.msun.csm.model.dto.WorkOrderDisasterCloudDTO;
import com.msun.csm.model.dto.disastercloud.ProjDisasterRecoveryApplyCheckDTO;
import com.msun.csm.model.dto.disastercloud.ProjDisasterRecoveryApplyOpenDTO;
import com.msun.csm.model.dto.disastercloud.ProjDisasterRecoveryDownloadDTO;
import com.msun.csm.model.dto.disastercloud.ProjDisasterRecoveryInfoQueryDTO;
import com.msun.csm.model.dto.disastercloud.ProjDisasterRecoveryMainInfoQueryDTO;
import com.msun.csm.model.dto.yunweiplatform.YunweiDisasterCloudApplyDTO;
import com.msun.csm.model.dto.yunweiplatform.YunweiDisasterCloudSyncTimeDTO;
import com.msun.csm.model.imsp.HospitalUpdateStatusAndProductDeployDTO;
import com.msun.csm.model.imsp.SyncYunyingApplicationDTO;
import com.msun.csm.model.req.projectfile.UploadFileReq;
import com.msun.csm.model.vo.DisasterRecoveryInfoBasicInfoVO;
import com.msun.csm.model.vo.DisasterRecoveryInfoCheckInfoVO;
import com.msun.csm.model.vo.DisasterRecoveryInfoMainInfoVO;
import com.msun.csm.model.vo.DisasterRecoveryInfoOpenInfoVO;
import com.msun.csm.model.vo.DisasterRecoveryInfoRecieptFileVO;
import com.msun.csm.model.vo.ProjDisasterRecoveryApplyLogVO;
import com.msun.csm.model.vo.ProjDisasterRecoveryInfoRelativeVO;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.model.yunying.OrderDTO;
import com.msun.csm.service.api.ApiYunyingService;
import com.msun.csm.service.common.BaseYunweiApplySaveApplyHistory;
import com.msun.csm.service.common.CommonService;
import com.msun.csm.service.common.ExceptionMessageService;
import com.msun.csm.service.config.ConfigSerialNumberService;
import com.msun.csm.service.operlog.SysOperLogService;
import com.msun.csm.service.proj.ProjHospitalInfoService;
import com.msun.csm.service.proj.ProjProjectSettlementCheckMainService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderHospitalService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderHospitalYunweiService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderServiceImpl;
import com.msun.csm.service.yunwei.YunWeiPlatFormService;
import com.msun.csm.service.yunying.YunYingCommonService;
import com.msun.csm.service.yunying.YunYingServiceImpl;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.doc.BaseDocUtils;
import com.msun.csm.util.obs.OBSClientUtils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2024-10-11 08:39:18
 */
@Slf4j
@Service
public class ProjDisasterRecoveryInfoServiceImpl implements ProjDisasterRecoveryInfoService {

    private static final String DISASTER_CLOUD_PDF = "disaster_cloud_pdf";

    public static final String TMP_FILE_NAME = "tmp.docx";

    public static final String TMP_DISASTER_CLOUD_FILE_NAME = "tmp_disaster_cloud.docx";

    public static final String DISASTER_CLOUD_CONFIRM_FILE_NAME = "云容灾交付确认涵.pdf";

    public static final String DISASTER_CLOUD_RECIEPT_FILE_NAME = "云容灾交付回执单.pdf";
    /**
     * 回执单上传
     */
    private static final String RECIEPTUPLOAD = "recieptupload";

    @Value("${project.obs.prePath}")
    private String prePath;

    @Value("${project.feign.yunwei.disasterCloudApplyOpen-method}")
    private String disasterCloudApplyOpenUrl;

    @Value("${project.feign.yunwei.url}")
    private String devUrl;


    @Resource
    private ProjDisasterRecoveryDocService recoveryDocService;


    @Resource
    private ProjDisasterRecoveryInfoMapper disasterRecoveryInfoMapper;

    @Resource
    private ProjDisasterRecoveryApplyLogMapper disasterRecoveryApplyLogMapper;

    @Resource
    private UserHelper userHelper;

    @Resource
    private ProjContractInfoMapper contractInfoMapper;

    @Resource
    private YunyingFeignClient yunyingFeignClient;

    @Resource
    private YunYingCommonService yunYingCommonService;

    @Resource
    private ExceptionMessageService exceptionMessageService;

    @Resource
    private SysOperLogService sysOperLogService;

    @Resource
    private CommonService commonService;

    @Resource
    private ProjCustomInfoMapper customInfoMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private ProjDisasterRecoveryApplyMapper disasterRecoveryApplyMapper;

    @Resource
    private ProjProjectFileMapper projectFileMapper;

    @Resource
    private SysFileMapper sysFileMapper;

    @Resource
    private ConfigSerialNumberService configSerialNumberService;

    @Resource
    private ProjProjectInfoMapper projectInfoMapper;

    @Resource
    private ProjApplyOrderHospitalService applyOrderHospitalService;

    @Resource
    private YunWeiPlatFormService yunWeiPlatFormService;

    @Resource
    private ProjDisasterRecoveryInfoRelativeConvert disasterRecoveryInfoRelativeConvert;

    @Resource
    private ProjOrderProductMapper projOrderProductMapper;

    @Resource
    private DisasterCloudCommonService disasterCloudCommonService;

    @Resource
    private ProjDisasterRecoveryApplyService disasterRecoveryApplyService;

    @Resource
    private ApiYunyingService apiYunyingService;

    @Resource
    private ProjHospitalInfoService hospitalInfoService;

    @Resource
    private ProjProjectSettlementCheckMainService mainService;

    @Override
    public Result<PageInfo<ProjDisasterRecoveryInfoRelativeVO>> projDisasterRecoveryInfoService(ProjDisasterRecoveryInfoQueryDTO dto) {
        // 分页
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        // 获取数据
        List<ProjDisasterRecoveryInfoRelative> disasterRecoveryInfoList =
                disasterRecoveryInfoMapper.findDisasterRecoveryInfo(dto);
        PageInfo<ProjDisasterRecoveryInfoRelative> pageInfo = new PageInfo<>(disasterRecoveryInfoList);
        List<ProjDisasterRecoveryInfoRelativeVO> disasterRecoveryInfoVOS =
                disasterRecoveryInfoRelativeConvert.po2Vo(disasterRecoveryInfoList);
        if (CollUtil.isNotEmpty(disasterRecoveryInfoVOS)) {
            for (ProjDisasterRecoveryInfoRelativeVO disasterRecoveryInfoVO : disasterRecoveryInfoVOS) {
                if (ObjectUtil.isEmpty(disasterRecoveryInfoVO.getStatus())) {
                    continue;
                }
                // 设置状态描述
                DisasterRecoveryApplyStatusEnum statusEnum =
                        DisasterRecoveryApplyStatusEnum.getEnumByCode(disasterRecoveryInfoVO.getStatus());
                disasterRecoveryInfoVO.setStatusDesc(ObjectUtil.isNotEmpty(statusEnum)
                        ? Objects.requireNonNull(statusEnum).getDesc() : StrUtil.EMPTY);
                // 开通按钮控制
                disasterRecoveryInfoVO.setCanOpenApply(canOpenApply(disasterRecoveryInfoVO.getStatus()));
            }
        }
        // 分页
        PageInfo<ProjDisasterRecoveryInfoRelativeVO> pageInfoVO = PageInfo.of(disasterRecoveryInfoVOS);
        pageInfoVO.setTotal(pageInfo.getTotal());
        return Result.success(pageInfoVO);
    }

    @Override
    public Result<DisasterRecoveryInfoMainInfoVO> getDisasterRecoveryMainInfo(ProjDisasterRecoveryMainInfoQueryDTO dto) {
        ProjDisasterRecoveryInfo disasterRecoveryInfo =
                disasterRecoveryInfoMapper.selectOne(new QueryWrapper<ProjDisasterRecoveryInfo>().eq(
                        "proj_disaster_recovery_info_id", dto.getProjDisasterRecoveryInfoId()));
        if (ObjectUtil.isEmpty(disasterRecoveryInfo)) {
            throw new CustomException("未查询到云容灾台账信息.");
        }
        // 主内容
        DisasterRecoveryInfoMainInfoVO mainInfoVO = DisasterRecoveryInfoMainInfoVO.builder().build();
        // 基础信息
        DisasterRecoveryInfoBasicInfoVO basicInfoVO = setBasicInfo(disasterRecoveryInfo);
        ProjDisasterRecoveryApply disasterRecoveryApply =
                disasterRecoveryApplyMapper.selectOne(new QueryWrapper<ProjDisasterRecoveryApply>().eq(
                        "proj_disaster_recovery_info_id", dto.getProjDisasterRecoveryInfoId()));
        // 开通信息
        DisasterRecoveryInfoOpenInfoVO openInfoVO = setOpenInfo(disasterRecoveryInfo, disasterRecoveryApply,
                basicInfoVO);
        // 验收信息
        DisasterRecoveryInfoCheckInfoVO checkInfoVO = setCheckInfo(disasterRecoveryInfo, disasterRecoveryApply);
        // 日志信息
        List<ProjDisasterRecoveryApplyLog> applyLogs =
                setLogInfo(ObjectUtil.isNotEmpty(disasterRecoveryApply)
                        ? disasterRecoveryApply.getProjDisasterRecoveryApplyId()
                        : null);
        // 设置参数
        mainInfoVO.setBasicInfoVO(basicInfoVO);
        mainInfoVO.setOpenInfoVO(openInfoVO);
        mainInfoVO.setCheckInfoVO(checkInfoVO);
        mainInfoVO.setRecoveryInfoLogs(applyLogs);
        return Result.success(mainInfoVO);
    }

    /**
     * 设置日志信息
     *
     * @param applyId 申请单id
     * @return List<ProjDisasterRecoveryApplyLog>
     */
    public List<ProjDisasterRecoveryApplyLog> setLogInfo(Long applyId) {
        if (ObjectUtil.isEmpty(applyId)) {
            return CollUtil.newArrayList();
        }
        List<ProjDisasterRecoveryApplyLog> applyLogs =
                disasterRecoveryApplyLogMapper.selectList(new QueryWrapper<ProjDisasterRecoveryApplyLog>().eq(
                        "proj_disaster_recovery_apply_id", applyId).orderByAsc(
                        "create_time"));
        if (CollUtil.isEmpty(applyLogs)) {
            return CollUtil.newArrayList();
        }
        // 设置状态名称
        List<ProjDisasterRecoveryApplyLogVO> disasterRecoveryApplyLogVOS = CollUtil.newArrayList();
        BeanUtils.copyProperties(applyLogs, disasterRecoveryApplyLogVOS);
        for (ProjDisasterRecoveryApplyLogVO disasterRecoveryApplyLogVO : disasterRecoveryApplyLogVOS) {
            DisasterRecoveryApplyStatusEnum applyStatusEnum =
                    DisasterRecoveryApplyStatusEnum.getEnumByCode(disasterRecoveryApplyLogVO.getOperateType());
            if (ObjectUtil.isNotEmpty(applyStatusEnum)) {
                assert applyStatusEnum != null;
                String result;
                if (applyStatusEnum.getStatusEnum().getCode() == NumberEnum.NO_1.num()) {
                    result = disasterRecoveryApplyLogVO.getOperateResult();
                } else {
                    result = applyStatusEnum.getStatusEnum().getDesc()
                            + StrUtil.COMMA + "原因：" + disasterRecoveryApplyLogVO.getOperateResult();
                }
                disasterRecoveryApplyLogVO.setOperateResult(result);
            }
        }
        return applyLogs;
    }

    /**
     * 设置验收信息
     *
     * @param disasterRecoveryInfo  容灾信息
     * @param disasterRecoveryApply 申请单信息
     */
    public DisasterRecoveryInfoCheckInfoVO setCheckInfo(ProjDisasterRecoveryInfo disasterRecoveryInfo,
                                                        ProjDisasterRecoveryApply disasterRecoveryApply) {
        DisasterRecoveryInfoCheckInfoVO checkInfoVO = DisasterRecoveryInfoCheckInfoVO.builder().build();
        if (ObjectUtil.isEmpty(disasterRecoveryApply)) {
            return checkInfoVO;
        }
        // 设置回执单信息
        checkInfoVO.setRecoveryInfoRecieptFileVO(setRecieptInfo(disasterRecoveryInfo));
        // 设置是否可申请验收
        boolean canCheckApply = false;
        if (ObjectUtil.isNotEmpty(disasterRecoveryApply)) {
            if (ObjectUtil.isNotEmpty(disasterRecoveryApply.getStatus())) {
                canCheckApply =
                        disasterRecoveryApply.getStatus() == DisasterRecoveryApplyStatusEnum.DELIVERED.getCode()
                                || disasterRecoveryApply.getStatus() == DisasterRecoveryApplyStatusEnum.CHECK_REJECTED.getCode();
            }
        }
        checkInfoVO.setCanCheckApply(canCheckApply);
        if (!canCheckApply) {
            return checkInfoVO;
        }
        // 设置验收结果
        String checkResult = StrUtil.EMPTY;
        if (ObjectUtil.isNotEmpty(disasterRecoveryApply.getStatus())) {
            DisasterRecoveryApplyStatusEnum statusEnum =
                    DisasterRecoveryApplyStatusEnum.getEnumByCode(disasterRecoveryApply.getStatus());
            if (ObjectUtil.isNotEmpty(statusEnum)) {
                assert statusEnum != null;
                if (statusEnum.getCode() == DisasterRecoveryApplyStatusEnum.CHECK_REJECTED.getCode()
                        || statusEnum.getCode() == DisasterRecoveryApplyStatusEnum.CHECKED.getCode()) {
                    assert statusEnum != null;
                    checkResult = statusEnum.getDesc();
                }
            }
        }
        checkInfoVO.setCheckPersonName(ObjectUtil.isNotEmpty(disasterRecoveryApply.getCheckUser())
                ? disasterRecoveryApply.getCheckUser() : StrUtil.EMPTY);
        checkInfoVO.setCheckTime(ObjectUtil.isNotEmpty(disasterRecoveryApply.getCheckTime())
                ? DateUtil.formatDateTime(disasterRecoveryApply.getCheckTime()) : StrUtil.EMPTY);
        checkInfoVO.setCheckResult(checkResult);
        return checkInfoVO;
    }

    /**
     * 设置回执单信息
     *
     * @param disasterRecoveryInfo 云容灾信息
     * @return DisasterRecoveryInfoRecieptFileVO
     */
    public DisasterRecoveryInfoRecieptFileVO setRecieptInfo(ProjDisasterRecoveryInfo disasterRecoveryInfo) {
        SysFile sysFile = sysFileMapper.selectOne(new QueryWrapper<SysFile>().eq("file_code",
                SysFileEnum.RECIEPT_MODEL.getFileCode()).eq("business_code",
                SysFileEnum.RECIEPT_MODEL.getBusinessCode()));
        DisasterRecoveryInfoRecieptFileVO recieptFileVO = DisasterRecoveryInfoRecieptFileVO.builder().build();
        if (ObjectUtil.isNotEmpty(sysFile)) {
            String recieptModeUrl = OBSClientUtils.getTemporaryUrl(sysFile.getFilePath(),
                    ObsExpireTimeConsts.SEVEN_DAY);
            recieptFileVO.setReceiptModelUrl(recieptModeUrl);
            // 设置模板id
            recieptFileVO.setReceiptModeFileId(sysFile.getSysFileId());
            // 上传的项目文件回执单名称
            String uploadRecieptFileName = StrUtil.EMPTY;
            String uploadRecieptFileUploadTime = StrUtil.EMPTY;
            String uploadRecieptFileUploadPersonName = StrUtil.EMPTY;
            // 处理已经上传的回执单信息
            if (ObjectUtil.isNotEmpty(disasterRecoveryInfo.getReceiptFileId())) {
                ProjProjectFile projectFile = projectFileMapper.selectOne(new QueryWrapper<ProjProjectFile>().eq(
                        "project_file_id",
                        disasterRecoveryInfo.getReceiptFileId()));
                if (ObjectUtil.isNotEmpty(projectFile)) {

                    uploadRecieptFileName = projectFile.getFileName();
                    uploadRecieptFileUploadTime = DateUtil.formatDateTime(projectFile.getUpdateTime());
                    SysUser sysUser = sysUserMapper.selectOne(new QueryWrapper<SysUser>().eq("sys_user_id",
                            projectFile.getUpdaterId()));
                    if (ObjectUtil.isNotEmpty(sysUser)) {
                        uploadRecieptFileUploadPersonName = sysUser.getUserName();
                    }
                }
            }
            recieptFileVO.setReceiptProjectFileName(uploadRecieptFileName);
            recieptFileVO.setUploadTime(uploadRecieptFileUploadTime);
            recieptFileVO.setUploadPersonName(uploadRecieptFileUploadPersonName);
        }
        return recieptFileVO;
    }

    private void getFileUrl() {

    }

    /**
     * 设置基础信息
     *
     * @param disasterRecoveryInfo 容灾信息
     * @return DisasterRecoveryInfoBasicInfoVO
     */
    public DisasterRecoveryInfoBasicInfoVO setBasicInfo(ProjDisasterRecoveryInfo disasterRecoveryInfo) {
        ProjCustomInfo customInfo = customInfoMapper.selectOne(new QueryWrapper<ProjCustomInfo>().eq("custom_info_id",
                disasterRecoveryInfo.getCustomInfoId()));
        // 设置周期信息
        String periodTime = StrUtil.EMPTY;
        if (!ObjectUtil.isAllEmpty(disasterRecoveryInfo.getStartTime(), disasterRecoveryInfo.getEndTime())) {
            periodTime =
                    DateUtil.formatDateTime(disasterRecoveryInfo.getStartTime()) + " ~ " + DateUtil.formatDateTime(disasterRecoveryInfo.getEndTime());
        }
        // 设置责任人
        SysUser sysUser = sysUserMapper.selectOne(new QueryWrapper<SysUser>().eq("sys_user_id",
                disasterRecoveryInfo.getResponsiblePersonId()));
        String userName = ObjectUtil.isNotEmpty(sysUser) ? sysUser.getUserName() : StrUtil.EMPTY;
        // 设置续签期限
        String subscribeTerm = StrUtil.EMPTY;
        if (ObjectUtil.isNotEmpty(disasterRecoveryInfo.getServiceTerm())) {
            int term = disasterRecoveryInfo.getServiceTerm();
            if (term > NumberEnum.NO_12.num()) {
                int month = term % NumberEnum.NO_12.num();
                int year = term / NumberEnum.NO_12.num();
                subscribeTerm = year + "年" + month + "个月";
            } else if (term > NumberEnum.NO_0.num()) {
                subscribeTerm = NumberEnum.NO_1.description() + "年";
            } else {
                subscribeTerm = NumberEnum.NO_0.description() + "个月";
            }
        }
        // 设置页面状态
        int status = DisasterCloudMainStatusEnum.BASIC.getCode();
        ProjDisasterRecoveryApply apply =
                disasterCloudCommonService.getDisasterRecoveryApply(disasterRecoveryInfo.getProjDisasterRecoveryInfoId());
        if (ObjectUtil.isNotEmpty(apply) && ObjectUtil.isNotEmpty(apply.getStatus())) {
            if (apply.getStatus() == DisasterRecoveryApplyStatusEnum.DISPATCH.getCode()
                    || apply.getStatus() == DisasterRecoveryApplyStatusEnum.REJECTED.getCode()
            ) {
                status = DisasterCloudMainStatusEnum.BASIC.getCode();
            }
            if (apply.getStatus() == DisasterRecoveryApplyStatusEnum.APPLYED.getCode()
                    || apply.getStatus() == DisasterRecoveryApplyStatusEnum.AUDITED.getCode()
                    || apply.getStatus() == DisasterRecoveryApplyStatusEnum.DEPLOYING.getCode()
                    || apply.getStatus() == DisasterRecoveryApplyStatusEnum.DEPLOYED.getCode()
                    || apply.getStatus() == DisasterRecoveryApplyStatusEnum.DELIVERED.getCode()
                    || apply.getStatus() == DisasterRecoveryApplyStatusEnum.OPENED.getCode()
                    || apply.getStatus() == DisasterRecoveryApplyStatusEnum.CHECK_REJECTED.getCode()
            ) {
                status = DisasterCloudMainStatusEnum.OPEN.getCode();
            }
            if (apply.getStatus() == DisasterRecoveryApplyStatusEnum.RENEWED.getCode()
                    || apply.getStatus() == DisasterRecoveryApplyStatusEnum.CHECK_APPLYED.getCode()
                    || apply.getStatus() == DisasterRecoveryApplyStatusEnum.CHECKED.getCode()
            ) {
                status = DisasterCloudMainStatusEnum.CHECK.getCode();
            }
            if (apply.getStatus() == DisasterRecoveryApplyStatusEnum.SYS_AUTO_RENEWAL.getCode()) {
                status = DisasterCloudMainStatusEnum.AUTO_RENEWAL.getCode();
            }
        }
        // 设置解决方案名称
        String implementationTypeName =
                ObjectUtil.isNotEmpty(ProjectTypeEnums.getEnum(
                        disasterRecoveryInfo.getSolutionType()))
                        ? Objects.requireNonNull(ProjectTypeEnums.getEnum(disasterRecoveryInfo.getSolutionType()))
                        .getName()
                        : StrUtil.EMPTY;
        return DisasterRecoveryInfoBasicInfoVO.builder()
                .customName(customInfo.getCustomName())
                .cloudDomain(disasterRecoveryInfo.getCloudDomain())
                .startTime(ObjectUtil.isNotEmpty(disasterRecoveryInfo.getStartTime())
                        ? DateUtil.formatDateTime(disasterRecoveryInfo.getStartTime()) : StrUtil.EMPTY)
                .endTime(ObjectUtil.isNotEmpty(disasterRecoveryInfo.getEndTime())
                        ? DateUtil.formatDateTime(disasterRecoveryInfo.getEndTime()) : StrUtil.EMPTY)
                .implementationType(disasterRecoveryInfo.getSolutionType())
                .implementationTypeName(implementationTypeName)
                .periodTime(periodTime)
                .responsiblePersonName(userName)
                .subscribeTerm(subscribeTerm)
                .status(status)
                .build();
    }

    /**
     * 设置开通信息
     *
     * @param disasterRecoveryInfo  容灾信息
     * @param disasterRecoveryApply 容灾申请单信息
     * @param basicInfoVO           基础信息
     * @return DisasterRecoveryInfoOpenInfoVO
     */
    public DisasterRecoveryInfoOpenInfoVO setOpenInfo(ProjDisasterRecoveryInfo disasterRecoveryInfo,
                                                      ProjDisasterRecoveryApply disasterRecoveryApply,
                                                      DisasterRecoveryInfoBasicInfoVO basicInfoVO) {
        if (ObjectUtil.isEmpty(disasterRecoveryApply)) {
            return DisasterRecoveryInfoOpenInfoVO.builder().build();
        }
        // 开通信息
        String applyOpenStartTime = StrUtil.EMPTY;
        // 设置申请开通时间
        if (ObjectUtil.isNotEmpty(disasterRecoveryApply.getDeployApplicationTime())) {
            applyOpenStartTime = DateUtil.formatDateTime(disasterRecoveryApply.getDeployApplicationTime());
        }
        boolean canOpenApply = canOpenApply(disasterRecoveryApply.getStatus());
        // 设置确认函
        ProjProjectFile projectFile = projectFileMapper.selectOne(new QueryWrapper<ProjProjectFile>().eq(
                "project_file_id", disasterRecoveryInfo.getConfirmationFileId()));
        Long deliveryEnsureFileId = null;
        if (ObjectUtil.isNotEmpty(disasterRecoveryInfo.getStartTime())) {
            deliveryEnsureFileId = projectFile.getProjectFileId();
        }
        return DisasterRecoveryInfoOpenInfoVO.builder()
                .applyStartTime(applyOpenStartTime)
                .canOpenApply(canOpenApply)
                .deliveryEnsureFileId(deliveryEnsureFileId)
                .startTime(basicInfoVO.getStartTime())
                .build();
    }

    /**
     * @param status 申请单状态
     * @return boolean true, 可申请; false, 不可申请
     */
    private boolean canOpenApply(Integer status) {
        if (ObjectUtil.isEmpty(status)) {
            return false;
        }
        return status == DisasterRecoveryApplyStatusEnum.DISPATCH.getCode()
                || status == DisasterRecoveryApplyStatusEnum.REJECTED.getCode();
    }

    /**
     * 获取确认函pdfUrl
     *
     * @param disasterRecoveryInfo 云容灾台账
     * @param projectFilePath      proj_project_file.file_path
     * @return string
     */
    public String getEnsureFileObsKey(ProjDisasterRecoveryInfo disasterRecoveryInfo, String projectFilePath) {
        DisasterCloudCommonService.getTimeSplit(disasterRecoveryInfo.getStartTime());
        String[] downTime = DisasterCloudCommonService.getTimeSplit(new Date());
        BaseDocUtils.DocStrategy docStrategy = new BaseDocUtils.DocStrategy() {
            @Override
            public void handle(XWPFRun run, String originText) {
                // 获取开通时间
                DisasterCloudCommonService.setText(run, originText, VAR_DOWN_YEAR, downTime[0]);
                DisasterCloudCommonService.setText(run, originText, VAR_DOWN_MONTH, downTime[1]);
                DisasterCloudCommonService.setText(run, originText, VAR_DOWN_DAY, downTime[2]);
            }
        };
        return getPdfUrlObsKey(disasterRecoveryInfo, projectFilePath, docStrategy,
                DISASTER_CLOUD_CONFIRM_FILE_NAME);
    }


    /**
     * 获取obs回执单路径(pdf格式）
     *
     * @param disasterRecoveryInfo 云容灾台账
     * @return string
     */
    public String getRecieptFileObsKey(ProjDisasterRecoveryInfo disasterRecoveryInfo, String filePath) {
        String[] downTime = DisasterCloudCommonService.getTimeSplit(new Date());
        String[] startTime = DisasterCloudCommonService.getTimeSplit(disasterRecoveryInfo.getStartTime());
        ProjCustomInfo customInfo = commonService.getCustomInfo(disasterRecoveryInfo.getCustomInfoId());
        if (ObjectUtil.isEmpty(disasterRecoveryInfo.getConfirmationFileDownTime())) {
            throw new CustomException("请先下载云容灾交付确认涵.");
        }
        String[] confirmDownTime =
                DisasterCloudCommonService.getTimeSplit(disasterRecoveryInfo.getConfirmationFileDownTime());
        BaseDocUtils.DocStrategy docStrategy = new BaseDocUtils.DocStrategy() {
            @Override
            public void handle(XWPFRun run, String originText) {
                // 落款日期
                DisasterCloudCommonService.setText(run, originText, VAR_APPLY_YEAR, confirmDownTime[0]);
                DisasterCloudCommonService.setText(run, originText, VAR_APPLY_MONTH, confirmDownTime[1]);
                DisasterCloudCommonService.setText(run, originText, VAR_APPLY_DAY, confirmDownTime[2]);
                // 下载日期
                DisasterCloudCommonService.setText(run, originText, VAR_DOWN_YEAR, downTime[0]);
                DisasterCloudCommonService.setText(run, originText, VAR_DOWN_MONTH, downTime[1]);
                DisasterCloudCommonService.setText(run, originText, VAR_DOWN_DAY, downTime[2]);
                // 开通日期
                DisasterCloudCommonService.setText(run, originText, VAR_START_YEAR, startTime[0]);
                DisasterCloudCommonService.setText(run, originText, VAR_START_MONTH, startTime[1]);
                DisasterCloudCommonService.setText(run, originText, VAR_START_DAY, startTime[2]);
                // 客户信息
                DisasterCloudCommonService.setText(run, originText, VAR_CUSTOM_NAME, customInfo.getCustomName());
            }
        };
        return getPdfUrlObsKey(disasterRecoveryInfo, filePath, docStrategy,
                DISASTER_CLOUD_RECIEPT_FILE_NAME);
    }


    /**
     * 获取确认涵pdf链接
     *
     * @param disasterRecoveryInfo 云容灾台账
     * @param filePath             项目文件路径
     * @return string
     */
    public String getPdfUrlObsKey(ProjDisasterRecoveryInfo disasterRecoveryInfo,
                                  String filePath, BaseDocUtils.DocStrategy docStrategy, String pdfFileName) {
        String tmpFileUrl = StrUtil.EMPTY;
        if (StrUtil.isBlank(filePath)) {
            return tmpFileUrl;
        }
        String businessId = StrUtil.toString(disasterRecoveryInfo.getProjDisasterRecoveryInfoId());
        String tmpPath1 = ProjApplyOrderServiceImpl.getDirFilePath(businessId) + StrUtil.SLASH + TMP_FILE_NAME;
        String tmpPath2 = null;
        String tmpPath3 = null;
        String pdfUrlObsKey;
        try {
            FileOutputStream outputStream = new FileOutputStream(tmpPath1);
            OBSClientUtils.downloadDirectFile(CollUtil.newArrayList(filePath), outputStream);
            FileInputStream fileInputStream = new FileInputStream(tmpPath1);
            tmpPath2 = recoveryDocService.baseReplace(fileInputStream, businessId, TMP_DISASTER_CLOUD_FILE_NAME,
                    docStrategy);
            // 转换为pdf
            ByteArrayOutputStream byteArrayOutputStream = null;
            FileInputStream fis = null;
            FileOutputStream out = null;
            try {
                tmpPath3 = ProjApplyOrderServiceImpl.getDirFilePath(businessId) + StrUtil.SLASH + pdfFileName;
                // 读取Word文档
                fis = new FileInputStream(tmpPath2);
                out = new FileOutputStream(tmpPath3);
                WordToPdfConverter.convert(fis, out);
                log.info("Word文档转换成PDF成功！");
            } catch (Exception e) {
                log.error("word转换pdf异常. message:{}, err=", e.getMessage(), e);
            } finally {
                if (ObjectUtil.isNotEmpty(byteArrayOutputStream)) {
                    byteArrayOutputStream.close();
                }
                if (ObjectUtil.isNotEmpty(out)) {
                    out.close();
                }
                if (ObjectUtil.isNotEmpty(fis)) {
                    fis.close();
                }
            }
            pdfUrlObsKey = OBSClientUtils.getObsProjectPath(prePath,
                    DISASTER_CLOUD_PDF, businessId) + pdfFileName;
            OBSClientUtils.uploadFile(new File(tmpPath3), pdfUrlObsKey);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            deleteTempFile(CollUtil.newArrayList(tmpPath1, tmpPath2, tmpPath3));
        }
        return pdfUrlObsKey;
    }

    public String getPdfUrl(ProjDisasterRecoveryInfo disasterRecoveryInfo,
                            String filePath, BaseDocUtils.DocStrategy docStrategy, String pdfFileName) {
        String pdfUrlObsKey = getPdfUrlObsKey(disasterRecoveryInfo, filePath, docStrategy, pdfFileName);
        return OBSClientUtils.getTemporaryUrl(pdfUrlObsKey, ObsExpireTimeConsts.SEVEN_DAY);
    }

    /**
     * 轮询删除文件
     *
     * @param filePaths
     */
    private void deleteTempFile(List<String> filePaths) {
        if (CollUtil.isEmpty(filePaths)) {
            return;
        }
        for (String filePath : filePaths) {
            if (StrUtil.isNotBlank(filePath)) {
                try {
                    if (StrUtil.isNotBlank(filePath)) {
                        assert filePath != null;
                        FileUtils.forceDelete(new File(filePath));
                    }
                } catch (IOException e) {
                    log.error("删除文件异常. e.message: {}, err=", e.getMessage(), e);
                }
            }
        }

    }


    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Result<String> applyDisasterRecoveryOpen(ProjDisasterRecoveryApplyOpenDTO dto) {
        ProjDisasterRecoveryInfo disasterRecoveryInfo = getDisasterRecoveryInfo(dto.getProjDisasterRecoveryInfoId());
        if (ObjectUtil.isEmpty(disasterRecoveryInfo)) {
            throw new CustomException("开通环境异常. 未查询到云容灾信息.");
        }
        Long yyContractId = commonService.getContractInfo(disasterRecoveryInfo.getContractInfoId()).getYyContractId();
        // 判断首付款吉缴纳
        YunyingDisasterCloudPaysignageDTO paysignageDTO = YunyingDisasterCloudPaysignageDTO.builder()
                .projId(StrUtil.toString(disasterRecoveryInfo.getYyOrderId()))
                .conOgIdList(CollUtil.newArrayList(StrUtil.toString(yyContractId)))
                .token(YunYingServiceImpl.TOKEN)
                .build();
        Result<String> result = isPaySignageToYunyingGet(paysignageDTO, disasterRecoveryInfo);
        if (!result.isSuccess()) {
            return result;
        }
        // 查询域名
        String domainName;
        // 查询客户下对应解决方案类型的项目
        List<ProjProjectInfo> projectInfos = projectInfoMapper.selectList(new QueryWrapper<ProjProjectInfo>().eq(
                "custom_info_id",
                disasterRecoveryInfo.getCustomInfoId()).eq("project_type", disasterRecoveryInfo.getSolutionType()));
        if (CollUtil.isEmpty(projectInfos)) {
            throw new CustomException("开通环境异常. 未查询到项目信息.");
        }
        // 取一个项目id查询已开通的域名
        List<ProjHospitalInfo> openedHospitalInfo =
                applyOrderHospitalService.findOpenedHospitalInfos(projectInfos.get(0).getProjectInfoId());
        if (CollUtil.isEmpty(openedHospitalInfo)) {
            throw new CustomException("开通环境异常. 未查询到已开通的医院.");
        }
        // 过滤医院, 若存在多个域名抛出异常
        List<String> domains =
                openedHospitalInfo.stream().map(ProjHospitalInfo::getCloudDomain).distinct().collect(Collectors.toList());
        if (domains.size() > 1) {
            throw new CustomException("开通环境异常. 域名存在多个.");
        }
        domainName = domains.get(0);
        // 查工单
        ProjDisasterRecoveryApply apply =
                disasterCloudCommonService.getDisasterRecoveryApply(disasterRecoveryInfo.getProjDisasterRecoveryInfoId());
        if (ObjectUtil.isEmpty(apply)) {
            throw new CustomException("未查询到已派工工单信息.");
        }
        // 创建工单
        ProjDisasterRecoveryApply disasterRecoveryApply = ProjDisasterRecoveryApply.builder()
                .projDisasterRecoveryApplyId(apply.getProjDisasterRecoveryApplyId())
                .projDisasterRecoveryInfoId(disasterRecoveryInfo.getProjDisasterRecoveryInfoId())
                .deployApplicant(userHelper.getCurrentUser().getUserName())
                .status(DisasterRecoveryApplyStatusEnum.APPLYED.getCode())
                .deployApplicationTime(new Date())
                .build();
        int count = disasterRecoveryApplyMapper.updateById(disasterRecoveryApply);
        log.info("云容灾申请开通. 创建工单. count: {}", count);
        // 添加工单日志
        ProjDisasterRecoveryApplyLog applyLog = ProjDisasterRecoveryApplyLog.builder()
                .projDisasterRecoveryApplyLogId(SnowFlakeUtil.getId())
                .projDisasterRecoveryApplyId(disasterRecoveryApply.getProjDisasterRecoveryApplyId())
                .operateTime(new Date())
                .operateType(DisasterRecoveryApplyStatusEnum.APPLYED.getCode())
                .operaterName(userHelper.getCurrentUser().getUserName())
                .operateResult(DisasterRecoveryApplyStatusEnum.APPLYED.getDesc())
                .build();
        count = disasterRecoveryApplyLogMapper.insert(applyLog);
        log.info("云容灾申请开通. 添加工单日志. count: {}", count);
        // 获取客户信息
        ProjCustomInfo customInfo = commonService.getCustomInfo(disasterRecoveryInfo.getCustomInfoId());
        YunweiDisasterCloudApplyDTO disasterCloudDTO = YunweiDisasterCloudApplyDTO.builder()
                .customerId(customInfo.getYyCustomerId())
                .customerName(customInfo.getCustomName())
                .deliverPlatformApplyId(Long.parseLong(apply.getApplyNum()))
                .domainName(domainName)
                .serviceTerm(disasterRecoveryInfo.getServiceTerm())
                .submitName(userHelper.getCurrentUser().getUserName())
                .build();
        // 调用接口申请开通
        callDisasterCloudApplyOpen(disasterCloudDTO, disasterRecoveryInfo, customInfo);
        // 更新运营平台工单状态
        disasterCloudCommonService.updateNodeStatus(disasterRecoveryInfo.getOrderInfoId(),
                OrderStepEnum.DISASTER_CLOUD_APPLY);
        return Result.success();
    }

    /**
     * 调用运营平台接口获取首付款是否缴纳
     *
     * @return Result<String>
     */
    private Result<String> isPaySignageToYunyingGet(YunyingDisasterCloudPaysignageDTO paysignageDTO,
                                                    ProjDisasterRecoveryInfo recoveryInfo) {
        // 调用运营平台接口, 判断是否缴纳首付款
        String userLoginName = userHelper.getCurrentUser().getAccount();
        sysOperLogService.apiOperLogInsertObjAry("获取云容灾首付款缴纳信息入参", StrUtil.EMPTY, Log.LogOperType.SEARCH.getCode(),
                userLoginName, paysignageDTO);
        YunyingResult<List<YunyingPaysignageDataDTO>> payResult =
                yunyingFeignClient.getDisasterCloudPaysignage(userLoginName, paysignageDTO);
        sysOperLogService.apiOperLogInsertObjAry("获取云容灾首付款缴纳信息返回结果", StrUtil.EMPTY, Log.LogOperType.SEARCH.getCode(),
                payResult);
        if (ObjectUtil.isEmpty(payResult)) {
            log.error("未在运营平台查询到首付款信息. 调用运营平台接口异常.");
            return Result.fail("未在运营平台查询到首付款信息");
        }
        if (!payResult.isSuccess()) {
            log.warn("查询运营平台首付款信息. 返回值: {}", payResult.getMsg());
            return Result.fail("运营平台查询到首付款数据异常");
        }
        if (ObjectUtil.isEmpty(payResult.getObj())) {
            log.warn("查询运营平台首付款信息. 返回值: {}", payResult.getMsg());
            return Result.fail("运营平台查询到数据异常.");
        }
        // 云容灾只存在一个合同, 获取首个元素即可
        Integer confPreFlag = payResult.getObj().get(0).getConPreFlag();
        // 判断是否缴纳首付款
        YunweiPaysignePreFlagTypeEnum preFlagTypeEnum = YunweiPaysignePreFlagTypeEnum.getEnum(confPreFlag);
        if (ObjectUtil.isEmpty(preFlagTypeEnum)) {
            log.warn("查询运营平台首付款信息. 返回值: {}", payResult.getMsg());
            return Result.fail("运营平台查询到首付款数据异常.");
        }
        assert preFlagTypeEnum != null;
        if (preFlagTypeEnum.getCode() == YunweiPaysignePreFlagTypeEnum.NOT_PASS.getCode()) {
            // 发送消息.
            ProjContractInfo contractInfo = commonService.getContractInfo(recoveryInfo.getContractInfoId());
            commonService.sendMessageToSysPerson(DictMessageTypeEnum.DISASTER_RECOVERY_APPLY.getId(), null, -1L,
                    null, MsgToCategory.SINGLE_PERSON.getCode(),
                    "审核首付款, 运营平台合同号为: " + contractInfo.getContractNo());
            // 调首付款代办展示接口
            SysUser sysUser = new SysUser();
            sysUser.setAccount(userHelper.getCurrentUser().getAccount());
            mainService.prePaymentFeeBack(sysUser, recoveryInfo.getYyOrderId(), "云容灾申请开通时, 调用运营平台首付款未缴纳提醒接口异常.", -1L,
                    CollUtil.newArrayList(StrUtil.toString(contractInfo.getYyContractId())));
            String tip = "平台检测到首付款未满足, 请联系运营部确认合同首付款达到部署要求.";
            log.info(tip);
            return Result.fail(tip);
        } else if (preFlagTypeEnum.getCode() == YunweiPaysignePreFlagTypeEnum.ING.getCode()) {
            log.info("平台检测到首付款未满足, 请联系运营部确认合同首付款达到部署要求. 运营处理中.");
            return Result.fail("平台检测到首付款未满足, 请联系运营部确认合同首付款达到部署要求.");
        }
        return Result.success();
    }

    /**
     * 云容灾申请开通
     *
     * @param disasterCloudDTO     请求参数
     * @param disasterRecoveryInfo 云融灾信息
     * @param customInfo           客户信息
     */
    private void callDisasterCloudApplyOpen(YunweiDisasterCloudApplyDTO disasterCloudDTO,
                                            ProjDisasterRecoveryInfo disasterRecoveryInfo, ProjCustomInfo customInfo) {
        HttpHeaders headers = commonService.getYunweiHeaders();
        HttpEntity<YunweiDisasterCloudApplyDTO> httpRequest =
                new HttpEntity<>(disasterCloudDTO, headers);
        sysOperLogService.apiOperLogInsertObjAry("云容灾开通申请入参", StrUtil.EMPTY, Log.LogOperType.ADD.getCode(),
                disasterCloudDTO);
        ResponseEntity<Result> response =
                new RestTemplate().postForEntity(devUrl + disasterCloudApplyOpenUrl,
                        httpRequest, Result.class);
        sysOperLogService.apiOperLogInsertObjAry("云容灾开通返回结果", StrUtil.EMPTY, Log.LogOperType.ADD.getCode(),
                response);
        if (ObjectUtil.isEmpty(response) || ObjectUtil.isEmpty(response.getBody()) || Objects.requireNonNull(response.getBody()).getCode() != 200) {
            String messageKeyword = StrUtil.EMPTY;
            try {
                ProjectTypeEnums projectTypeEnums = ProjectTypeEnums.getEnum(disasterRecoveryInfo.getSolutionType());
                String solutionTypeName = StrUtil.EMPTY;
                if (ObjectUtil.isNotEmpty(projectTypeEnums)) {
                    assert projectTypeEnums != null;
                    solutionTypeName = projectTypeEnums.getName();
                }
                messageKeyword = customInfo.getCustomName() + StrUtil.DASHED + solutionTypeName;
            } catch (Throwable e) {
                log.error("拼接参数异常. message: {}, e=", e.getMessage(), e);
            }
            exceptionMessageService.sendToSystemManager(-1L, messageKeyword + StrUtil.COMMA + "云容灾申请开通接口调用异常, 请及时处理.");
        }
    }

    /**
     * 获取云容灾台账信息
     *
     * @param projDisasterRecoveryInfoId 云容灾台账id
     * @return ProjDisasterRecoveryInfo
     */
    private ProjDisasterRecoveryInfo getDisasterRecoveryInfo(Long projDisasterRecoveryInfoId) {
        return disasterRecoveryInfoMapper.selectOne(new QueryWrapper<ProjDisasterRecoveryInfo>().eq(
                "proj_disaster_recovery_info_id", projDisasterRecoveryInfoId));
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Result<String> applyDisasterRecoveryCheck(ProjDisasterRecoveryApplyCheckDTO dto) {
        // 获取操作用户
        String operateUserName = StrUtil.EMPTY;
        SysUserVO sysUserVO = userHelper.getCurrentUser();
        if (ObjectUtil.isNotEmpty(sysUserVO)) {
            operateUserName = sysUserVO.getAccount();
        }
        // 获取运营平台合同id
        ProjDisasterRecoveryInfo disasterRecoveryInfo =
                disasterRecoveryInfoMapper.selectOne(new QueryWrapper<ProjDisasterRecoveryInfo>().eq(
                        "proj_disaster_recovery_info_id", dto.getProjDisasterRecoveryInfoId()));
        ProjContractInfo contractInfo = contractInfoMapper.selectOne(new QueryWrapper<ProjContractInfo>().eq(
                "contract_info_id",
                disasterRecoveryInfo.getContractInfoId()));
        // 更新回执单文件路径
        UploadFileReq uploadFileReq = new UploadFileReq();
        uploadFileReq.setProjectInfoId(-1L);
        uploadFileReq.setMilestoneCode(StrUtil.EMPTY);
        String[] names = dto.getFilePath().split(StrUtil.SLASH);
        String fileName = names[names.length - 1];
        ProjProjectFileExtend projectFile = new ProjProjectFileExtend(uploadFileReq, StrUtil.EMPTY, fileName,
                SnowFlakeUtil.getId(),
                dto.getFilePath(), "云容灾验收申请回执单");
        int count = projectFileMapper.insert(projectFile);
        log.info("新增文件关联. count: {}", count);
        // 更新云容灾信息
        ProjDisasterRecoveryInfo disasterRecoveryInfoUpdate = ProjDisasterRecoveryInfo.builder()
                .projDisasterRecoveryInfoId(disasterRecoveryInfo.getProjDisasterRecoveryInfoId())
                .receiptFileId(projectFile.getProjectFileId())
                .build();
        count = disasterRecoveryInfoMapper.updateById(disasterRecoveryInfoUpdate);
        log.info("更新容灾信息回执单文件id, count: {}", count);
        // 获取回执单url
        ProjectFileInfoDTO projectFileInfoDTO =
                yunYingCommonService.getDownloadFileInfo(projectFile.getProjectFileId());
        //产品信息
        List<ProductReq> productReqList = projOrderProductMapper.getYyProductByOrderInfoId(
                disasterRecoveryInfo.getOrderInfoId());
        YunyingDisasterCloudCheckApplyDTO applyDTO = YunyingDisasterCloudCheckApplyDTO.builder()
                .token(YunYingServiceImpl.TOKEN)
                .contractNum(contractInfo.getYyContractId())
                .projectNum(disasterRecoveryInfo.getYyOrderId())
                .workOrderId(disasterRecoveryInfo.getYyOrderId())
                .stepId(StrUtil.toString(OrderStepEnum.DISASTER_CLOUD_CHECK.getStepId()))
                .fileList(Collections.singletonList(FileReq.builder().
                        fileName(projectFileInfoDTO.getFileName())
                        .fileUrl(projectFileInfoDTO.getFileUrl()).build()))
                .product(productReqList)
                .currentTimes(NumberEnum.NO_1.num())
                .build();
        // 修改工单状态
        ProjDisasterRecoveryApply updateApply = new ProjDisasterRecoveryApply();
        updateApply.setCheckApplicant(userHelper.getCurrentUser().getUserName());
        updateApply.setCheckApplicationTime(new Date());
        updateApply.setStatus(DisasterRecoveryApplyStatusEnum.CHECK_APPLYED.getCode());
        count = disasterRecoveryApplyMapper.update(updateApply, new QueryWrapper<ProjDisasterRecoveryApply>().eq(
                "proj_disaster_recovery_info_id", dto.getProjDisasterRecoveryInfoId()));
        log.info("申请验收, 申请单更新. count: {}", count);
        // 添加申请日志
        // 查询申请单id
        ProjDisasterRecoveryApply apply =
                disasterRecoveryApplyMapper.selectOne(new QueryWrapper<ProjDisasterRecoveryApply>().eq(
                        "proj_disaster_recovery_info_id", dto.getProjDisasterRecoveryInfoId()));
        ProjDisasterRecoveryApplyLog applyLog = ProjDisasterRecoveryApplyLog.builder()
                .projDisasterRecoveryApplyLogId(SnowFlakeUtil.getId())
                .projDisasterRecoveryApplyId(apply.getProjDisasterRecoveryApplyId())
                .operateResult(DisasterRecoveryApplyStatusEnum.CHECK_APPLYED.getDesc())
                .operaterName(userHelper.getCurrentUser().getUserName())
                .operateType(DisasterRecoveryApplyStatusEnum.CHECK_APPLYED.getCode())
                .operateTime(new Date())
                .build();
        count = disasterRecoveryApplyLogMapper.insert(applyLog);
        log.info("云容灾申请验收, 添加申请单日志. count: {}", count);
        // 添加请求日志
        sysOperLogService.apiOperLogInsertObjAry("云容灾验收申请入参", StrUtil.EMPTY, Log.LogOperType.ADD.getCode(),
                operateUserName, applyDTO);
        Result<String> result = yunyingFeignClient.disasterCloudCheckApply(operateUserName, applyDTO);
        sysOperLogService.apiOperLogInsertObjAry("云容灾验收申请出参", StrUtil.EMPTY, Log.LogOperType.ADD.getCode(),
                result);
        // 判断如果请求异常发送消息提醒
        if (ObjectUtil.isEmpty(result) || !result.isSuccess()) {
            String customName = StrUtil.EMPTY;
            String solutionTypeName = StrUtil.EMPTY;
            try {
                ProjCustomInfo customInfo = commonService.getCustomInfo(disasterRecoveryInfo.getCustomInfoId());
                customName = customInfo.getCustomName();
                ProjectTypeEnums projectTypeEnums = ProjectTypeEnums.getEnum(disasterRecoveryInfo.getSolutionType());
                assert projectTypeEnums != null;
                solutionTypeName = projectTypeEnums.getName();
            } catch (Throwable e) {
                log.error("云容灾验收申请-获取用户信息异常. message: {}, e=", e.getMessage(), e);
            }
            String messageKeyword = customName + StrUtil.DASHED + solutionTypeName;
            exceptionMessageService.sendToSystemManager(-1L, messageKeyword + StrUtil.COMMA + "云容灾验收向运营平台提交申请异常请及时处理.");
            throw new CustomException("云容灾验收向运营平台提交申请异常.");
        }
        return Result.success();
    }

    /**
     * 处理派工容灾
     *
     * @param disasterCloudDTO 请求参数
     */
    public void syncWorkOrderHandleDisasterCloud(WorkOrderDisasterCloudDTO disasterCloudDTO) {
        int projectType = disasterCloudDTO.getProjectType();
        // 接收云容灾的派工
        OrderDTO order = disasterCloudDTO.getOrder();
        // 根据yyOrderId倒叙查询最新的工单再根据创建时间倒叙
        List<ProjDisasterRecoveryInfo> recoveryInfos =
                disasterRecoveryInfoMapper.selectList(new QueryWrapper<ProjDisasterRecoveryInfo>().eq("custom_info_id",
                        disasterCloudDTO.getCustomInfo().getCustomInfoId()).eq("solution_type", projectType).orderByDesc("yy_order_id").orderByDesc("create_time"));
        // 此处派工的云容灾产品只会有一个
        ProjOrderProduct orderProduct = disasterCloudDTO.getOrderProducts().get(0);
        // 生成续期台账数据
        ProjDisasterRecoveryInfo addRecoveryInfo = ProjDisasterRecoveryInfo.builder()
                .projDisasterRecoveryInfoId(SnowFlakeUtil.getId())
                .customInfoId(disasterCloudDTO.getCustomInfo().getCustomInfoId())
                .contractInfoId(disasterCloudDTO.getContractInfoId())
                .solutionType(projectType)
                .serviceTerm(orderProduct.getProductSubscribeTerm().intValue())
                .contractCustomInfoId(disasterCloudDTO.getContractCustomInfoExt().getContractCustomInfoId())
                .yyOrderId(order.getWorkOrderId())
                .responsiblePersonId(order.getProjectManagerId().longValue())
                .orderInfoId(disasterCloudDTO.getOrderInfoId())
                .build();
        boolean isRenewal = disasterCloudDTO.getDisaststerRenewal();
        if (isRenewal) {
            // 查询
            ProjDisasterRecoveryInfo lastDisaster = recoveryInfos.get(0);
            // 查询上一次派工云容灾的合同类型
            ProjContractInfo lastContractInfo = commonService.getContractInfo(lastDisaster.getContractInfoId());
            CloudServiceRenewalTimeDTO.LastCloudServiceData lastCloudServiceData =
                    CloudServiceRenewalTimeDTO.LastCloudServiceData.builder()
                            .contractType(lastContractInfo.getContractType())
                            .planStartTime(lastDisaster.getStartTime())
                            .serviceSubscribeTerm(lastDisaster.getServiceTerm())
                            .subscribeStartTime(lastDisaster.getStartTime())
                            .build();
            CloudServiceRenewalTimeDTO timeDTO = CloudServiceRenewalTimeDTO.builder()
                    .lastCloudServiceData(lastCloudServiceData)
                    .serviceSubscribeTerm(orderProduct.getProductSubscribeTerm().intValue())
                    .build();
            CloudServiceTimeResult timeResult = CloudServiceKit.getCloudServiceRenewalTime(timeDTO);
            addRecoveryInfo.setEndTime(timeResult.getSubscribeEndTime());
            addRecoveryInfo.setStartTime(timeResult.getSubscribeStartTime());
            addRecoveryInfo.setDeployManufactor(lastDisaster.getDeployManufactor());
            addRecoveryInfo.setConfirmationFileId(lastDisaster.getConfirmationFileId());
            addRecoveryInfo.setReceiptFileId(lastDisaster.getReceiptFileId());
            addRecoveryInfo.setCloudDomain(getCloudDomain(disasterCloudDTO.getCustomInfo(), projectType));
            addRecoveryInfo.setDispatchType(DispatchTypeEnum.RENEWAL.getCode());
            addRecoveryInfo.setOriginalEnvId(lastDisaster.getOriginalEnvId());
            addRecoveryInfo.setOriginalEnvName(lastDisaster.getOriginalEnvName());
            addRecoveryInfo.setDestEnvId(lastDisaster.getDestEnvId());
            addRecoveryInfo.setDestEnvName(lastDisaster.getDestEnvName());
        } else {
            addRecoveryInfo.setCloudDomain(getCloudDomain(disasterCloudDTO.getCustomInfo(), projectType));
            addRecoveryInfo.setDispatchType(DispatchTypeEnum.FIRST.getCode());
        }
        int count = disasterRecoveryInfoMapper.insert(addRecoveryInfo);
        log.info("新增云容灾数据. count: {}", count);
        // 创建申请单
        ProjDisasterRecoveryApply recoveryApply = ProjDisasterRecoveryApply.builder()
                .projDisasterRecoveryInfoId(addRecoveryInfo.getProjDisasterRecoveryInfoId())
                .status(DisasterRecoveryApplyStatusEnum.DISPATCH.getCode())
                .build();
        insertApplyInfo(recoveryApply);
        // 同步运维到期时间
        if (isRenewal) {
            // 更新工单状态为自动续期
            ProjDisasterRecoveryApply renewalApply = ProjDisasterRecoveryApply.builder()
                    .projDisasterRecoveryApplyId(recoveryApply.getProjDisasterRecoveryApplyId())
                    .status(DisasterRecoveryApplyStatusEnum.SYS_AUTO_RENEWAL.getCode())
                    .build();
            count = disasterRecoveryApplyMapper.updateById(renewalApply);
            log.info("云容灾续期更新工单状态. count: {}", count);
            // 拼接参数
            ProjDisasterRecoveryInfo recoveryInfo = recoveryInfos.get(0);
            YunweiDisasterCloudSyncTimeDTO dto = YunweiDisasterCloudSyncTimeDTO.builder()
                    .domainName(recoveryInfo.getCloudDomain())
                    .destHome(addRecoveryInfo.getDestEnvName())
                    .originalHome(addRecoveryInfo.getOriginalEnvName())
                    .endTime(DateUtil.formatDateTime(addRecoveryInfo.getEndTime()))
                    .startTime(DateUtil.formatDateTime(addRecoveryInfo.getStartTime()))
                    .build();
            ProjCustomInfo customInfo = commonService.getCustomInfo(addRecoveryInfo.getCustomInfoId());
            // 运维平台同步时间
            commonService.callYunweiSyncEndTime(dto, addRecoveryInfo, customInfo);
            ProjContractInfo contractInfo = commonService.getContractInfo(addRecoveryInfo.getContractInfoId());
            SyncYunyingApplicationDTO yunApplicationDTO = SyncYunyingApplicationDTO.builder()
                    .contractType(contractInfo.getContractType())
                    .yyWoId(addRecoveryInfo.getYyOrderId())
                    .pemcusssolType(addRecoveryInfo.getSolutionType())
                    .phaseType(CloudPhaseTypeEnum.FIRST.getCode())
                    .productType(StrUtil.toString(OrderTypeEnums.DISASTER_RECOVERY.getCode()))
                    .build();
            // 同步运营平台到期时间
            apiYunyingService.sendTimeToYunYingCloudTime(addRecoveryInfo.getStartTime(), addRecoveryInfo.getStartTime(),
                    yunApplicationDTO);
        }
    }

    public String getCloudDomain(ProjCustomInfo customInfo, Integer projectType) {
        List<ProjHospitalInfoRelative> hospitalInfoRelatives =
                hospitalInfoService.findHospitalInfo(customInfo.getCustomInfoId(),
                        projectType);
        String errKeyMsg = StrUtil.EMPTY;
        try {
            errKeyMsg = customInfo.getCustomName() + StrUtil.DASHED
                    + Objects.requireNonNull(ProjectTypeEnums.getEnum(projectType)).getName() + StrUtil.COMMA;
        } catch (Throwable e) {
            log.error("异常关键词提醒缺失. message: {}, e=", e.getMessage(), e);
        }
        // 异常发消息, 未查询到医院的情况
        if (CollUtil.isEmpty(hospitalInfoRelatives)) {
            log.error("新环境未部署. 请核实.");
            errKeyMsg += "云容灾首期派工异常. 新环境未部署, 未查询到可用的医院获取域名. 请尽快处理.";
            exceptionMessageService.sendToSystemManager(-1L, errKeyMsg);
            throw new CustomException("新环境未部署. 请核实.");
        }
        List<ProjHospitalInfoRelative> openedHospitals =
                ProjApplyOrderHospitalYunweiService.getOpenHospital(hospitalInfoRelatives);
        // 过滤是否存在多个域名, 若存在抛出异常
        List<String> domainList =
                openedHospitals.stream().map(ProjHospitalInfoRelative::getCloudDomain).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(domainList)) {
            String content = errKeyMsg + "获取到域名为空, 请核实.";
            log.error(content);
            exceptionMessageService.sendToSystemManager(-1L, content);
            throw new CustomException("未查询到客户环境域名, 请联系交付平台核实.");
        }
        // 判断是否有多个域名
        if (domainList.size() > 1) {
            throw new CustomException("部署环境查询到多个, 请联系交付平台处理.");
        }
        return domainList.get(0);
    }

    public void insertApplyInfo(ProjDisasterRecoveryApply apply) {
        String applyNum = configSerialNumberService.createNum(SerialNumType.APPLY_ORDER);
        apply.setApplyNum(applyNum);
        apply.setProjDisasterRecoveryApplyId(SnowFlakeUtil.getId());
        int count = disasterRecoveryApplyMapper.insert(apply);
        log.info("新增云容灾申请单. count: {}", count);
    }

    @Override
    public Result<String> uploadRecieptFile(Long projDisasterRecoveryInfoId, MultipartFile file) {
        String uploadTempFile = OBSClientUtils.getObsProjectPath(prePath, RECIEPTUPLOAD,
                StrUtil.toString(projDisasterRecoveryInfoId)) + file.getOriginalFilename();
        // 上传至obs
        try {
            OBSClientUtils.uploadFileStream(file.getInputStream(), uploadTempFile, false);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return Result.success(uploadTempFile);
    }

    @Override
    public Result<String> saveApplyHistoryLog(HospitalUpdateStatusAndProductDeployDTO dto) {
        ProjDisasterRecoveryApply apply =
                disasterRecoveryApplyMapper.selectOne(new QueryWrapper<ProjDisasterRecoveryApply>().eq("apply_num",
                        StrUtil.toString(dto.getDeliverPlatformApplyId())));
        if (ObjectUtil.isEmpty(apply)) {
            return Result.fail("未查询到云容灾申请单.");
        }
        // 转换节点状态, 运营平台与交付对照
        return ((BaseYunweiApplySaveApplyHistory) disasterRecoveryApplyService).handle(apply, dto);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void downloadFile(HttpServletResponse response, ProjDisasterRecoveryDownloadDTO downloadDTO) {
        // 设置头文件
        setResponseHeader(response);
        // 获取台账
        ProjDisasterRecoveryInfo recoveryInfo =
                disasterCloudCommonService.getDisasterRecoveryInfo(downloadDTO.getProjDisasterRecoveryInfoId());
        if (ObjectUtil.isEmpty(recoveryInfo)) {
            throw new CustomException("未查询到台账信息.");
        }
        // 处理下载
        if (downloadDTO.getType() == NumberEnum.NO_1.num().intValue()) {
            // 更新落款信息
            int count = disasterRecoveryInfoMapper.updateById(ProjDisasterRecoveryInfo.builder()
                    .projDisasterRecoveryInfoId(recoveryInfo.getProjDisasterRecoveryInfoId())
                    .confirmationFileDownTime(new Date())
                    .build());
            log.info("更新云容灾台账落款时间. count: {}", count);
            ProjProjectFile projectFile = projectFileMapper.selectOne(new QueryWrapper<ProjProjectFile>().eq(
                    "project_file_id", recoveryInfo.getConfirmationFileId()));
            String pdfObsKey = getEnsureFileObsKey(recoveryInfo, projectFile.getFilePath());
            downloadFile(response, pdfObsKey);
        } else if (downloadDTO.getType() == NumberEnum.NO_2.num().intValue()) {
            SysFile sysFile = sysFileMapper.selectOne(new QueryWrapper<SysFile>().eq("sys_file_id",
                    downloadDTO.getFileId()));
            String pdfObsKey = getRecieptFileObsKey(recoveryInfo, sysFile.getFilePath());
            downloadFile(response, pdfObsKey);
        }
    }

    /**
     * 下载obs数据
     *
     * @param response 返回流
     * @param filePath 文件路径
     */
    private void downloadFile(HttpServletResponse response, String filePath) {
        int idx = filePath.lastIndexOf(StrUtil.SLASH);
        String downloadFileName = filePath.substring(idx + 1);
        OBSClientUtils.downloadDirectFile(response, filePath, downloadFileName);
    }

    /**
     * 设置头文件
     *
     * @param response 返回流
     */
    private void setResponseHeader(HttpServletResponse response) {
        response.setContentType("application/octet-stream");
        // 设置前端跨域情况进行暴露的头名称
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("云容灾交付确认函.pdf"));
    }

}
