package com.msun.csm.service.proj;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.dao.entity.proj.flowable.ProjBackendApproveRecord;
import com.msun.csm.dao.entity.proj.flowable.ProjBackendApproveType;
import com.msun.csm.dao.mapper.proj.flowable.ProjBackendApproveRecordMapper;
import com.msun.csm.dao.mapper.proj.flowable.ProjBackendApproveTypeMapper;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.port.HospitalApi;
import com.msun.core.component.implementation.api.port.dto.CustFormCtrlDTO;
import com.msun.core.component.implementation.api.port.dto.CustFormCtrlSysCode;
import com.msun.core.component.implementation.filter.ImplHospitalDomainHolder;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.enums.AcceptanceStatusEnum;
import com.msun.csm.common.enums.AcceptanceTypeEnum;
import com.msun.csm.common.enums.ExcutionStatusEnum;
import com.msun.csm.common.enums.FrontVisibleEnum;
import com.msun.csm.common.enums.MilestoneNodeEnum;
import com.msun.csm.common.enums.MilestoneStatusEnum;
import com.msun.csm.common.enums.message.MsgToCategory;
import com.msun.csm.common.enums.projprojectinfo.ProjectDeliverStatusEnums;
import com.msun.csm.common.enums.rule.CustomerLimitProductEnum;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysConfig;
import com.msun.csm.dao.entity.SysDept;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.config.ConfigCustomFormLimit;
import com.msun.csm.dao.entity.config.ConfigCustomLimit;
import com.msun.csm.dao.entity.config.extend.ConfigCustomFormLimitExtend;
import com.msun.csm.dao.entity.proj.ProductInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjOrderInfo;
import com.msun.csm.dao.entity.proj.ProjProductEmpowerRecord;
import com.msun.csm.dao.entity.proj.ProjProjectAcceptance;
import com.msun.csm.dao.entity.proj.ProjProjectAcceptanceLog;
import com.msun.csm.dao.entity.proj.ProjProjectAcceptanceRule;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.report.ReportCustomInfo;
import com.msun.csm.dao.mapper.config.ConfigCustomFormLimitMapper;
import com.msun.csm.dao.mapper.config.ConfigCustomLimitMapper;
import com.msun.csm.dao.mapper.config.SysConfigMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderProductMapper;
import com.msun.csm.dao.mapper.proj.ProjProductEmpowerRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectAcceptanceMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectAcceptanceRuleMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectFileMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.report.ReportCustomInfoMapper;
import com.msun.csm.dao.mapper.sysdept.SysDeptMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.dao.mapper.tmp.TmpHospitalLimitMapper;
import com.msun.csm.feign.client.yunying.YunyingFeignClient;
import com.msun.csm.feign.entity.yunying.req.CheckApplySoftwareReq;
import com.msun.csm.feign.entity.yunying.req.FileReq;
import com.msun.csm.model.dto.OldAcceptDataDTO;
import com.msun.csm.model.dto.OldAcceptFileDataDTO;
import com.msun.csm.model.dto.ProductInfoDTO;
import com.msun.csm.model.dto.ProjectAcceptanceDTO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.dto.UpdateMilestoneDTO;
import com.msun.csm.model.dto.applyorder.ProjApplyAcceptanceDTO;
import com.msun.csm.model.imsp.ProjProjectAcceptanceDTO;
import com.msun.csm.model.imsp.SyncAcceptResult;
import com.msun.csm.model.param.MessageParam;
import com.msun.csm.model.req.project.ProjectMemberToYunYingReq;
import com.msun.csm.model.vo.AcceptanceResVO;
import com.msun.csm.model.vo.AcceptanceTimesResVO;
import com.msun.csm.model.vo.AcceptanceTimesVO;
import com.msun.csm.model.vo.ProjApplyAcceptanceVO;
import com.msun.csm.model.vo.ProjProjectAcceptanceRuleVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.message.SendMessageService;
import com.msun.csm.service.operlog.SysOperLogService;
import com.msun.csm.service.rule.RuleProjectRuleConfigService;
import com.msun.csm.service.yunying.YunYingServiceImpl;
import com.msun.csm.util.DomainMapUtil;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.StringUtils;
import com.msun.csm.util.obs.OBSClientUtils;
import com.obs.services.model.PutObjectResult;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import feign.Request;
import lombok.extern.slf4j.Slf4j;

/**
 * @description:
 * @fileName:
 * @author:zhouzhaoyu
 * @updateBy:
 * @Date:Created in 14:40 2024/5/21
 * @remark:
 */
@Service
@Slf4j
public class ProjProjectAcceptanceServiceImpl implements ProjProjectAcceptanceService {
    /**
     * 表单限制天数
     */
    private static final Integer DAYS_AFTER_CHECK_CLOSE = 30;

    /**
     * 二次验收限制天数
     */
    private static final Integer SECOND_APPLY_DAYS_LIMIT = 21;
    /**
     * 开启关闭开关，0：开，1：关
     */
    private static final Integer SWITCH_FLAG = 1;
    @Resource
    YunyingFeignClient yunyingFeignClient;
    @Resource
    UserHelper userHelper;
    @Resource
    ProjOrderProductMapper projOrderProductMapper;
    @Resource
    ProjProjectAcceptanceLogService projProjectAcceptanceLogService;
    @Resource
    SysUserMapper sysUserMapper;
    @Resource
    RuleProjectRuleConfigService ruleProjectRuleConfigService;
    @Resource
    ProjProjectInfoService projProjectInfoService;
    @Resource
    @Lazy
    private ProjOrderInfoMapper orderInfoMapper;
    @Resource
    private ProjProjectAcceptanceMapper projProjectAcceptanceMapper;
    @Resource
    private ProjProjectInfoMapper projProjectInfoMapper;
    @Resource
    private ProjProjectAcceptanceRuleMapper projProjectAcceptanceRuleMapper;
    @Resource
    private ProjOrderProductService projOrderProductService;
    @Autowired
    private ProjBackendApproveRecordMapper projBackendApproveRecordMapper;
    @Autowired
    private ProjBackendApproveTypeMapper projBackendApproveTypeMapper;


    @Resource
    private ProjMilestoneInfoService projMilestoneInfoService;

    @Resource
    private SendMessageService sendMessageService;

    @Resource
    private SysDeptMapper sysDeptMapper;

    @Resource
    private ProjProjectInfoMapper projectInfoMapper;

    @Resource
    private ProjHospitalInfoMapper hospitalInfoMapper;
    @Resource
    private ProjProductEmpowerRecordMapper productEmpowerRecordMapper;

    @Resource
    private ImplHospitalDomainHolder domainHolder;

    @Qualifier("com.msun.core.component.implementation.api.port.HospitalApi")
    @Resource
    private HospitalApi hospitalApi;

    @Resource
    private ConfigCustomFormLimitMapper configCustomFormLimitMapper;
    @Resource
    private ReportCustomInfoMapper reportCustomInfoMapper;

    @Resource
    private TmpHospitalLimitMapper tmpHospitalLimitMapper;

    @Value("${project.obs.prePath}")
    private String prePath;

    @Resource
    private ProjProjectFileMapper projectFileMapper;

    @Lazy
    @Resource
    private SysConfigMapper sysConfigMapper;

    @Lazy
    @Resource
    private ConfigCustomLimitMapper configCustomLimitMapper;

    @Resource
    private ProjExamService projExamService;

    @Lazy
    @Resource
    private SysOperLogService sysOperLogService;

    /**
     * 组装projectInfo参数
     *
     * @param applyAcceptanceVO
     * @param projectInfoId
     * @return
     */
    @NotNull
    private static ProjProjectInfo getProjectInfo(ProjApplyAcceptanceVO applyAcceptanceVO, Long projectInfoId) {
        //最后一次的外部验收时间
        Date externalAcceptanceTime = applyAcceptanceVO.getExternalAcceptanceTime();
        //第一次的提交验收申请时间
        ProjProjectInfo projectInfo = new ProjProjectInfo();
        projectInfo.setExternalAcceptTime(externalAcceptanceTime);
        projectInfo.setProjectInfoId(projectInfoId);
        projectInfo.setAcceptTime(applyAcceptanceVO.getAcceptanceTime());
        projectInfo.setProjectDeliverStatus(ProjectDeliverStatusEnums.ACCEPTED.getCode());
        return projectInfo;
    }

    @Override
    public ProjApplyAcceptanceVO getAcceptanceByProjectId(ProjectAcceptanceDTO dto) {
        //先给产品查询赋值避免空条件
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        productInfoDTO.setProjectInfoId(dto.getProjectInfoId());
        //查询项目是否上线
        ProjProjectInfo projectInfo = projProjectInfoMapper.selectByPrimaryKey(dto.getProjectInfoId());
        //查询申请单表是否有该项目的申请单信息
        List<ProjProjectAcceptance> acceptance = projProjectAcceptanceMapper.getAcceptance(dto.getProjectInfoId());
        if (!CollectionUtils.isEmpty(acceptance)) {
            dto.setProjectInfoId(null);
        }
        //根据项目id和申请单Id获取页面初始化信息
        ProjApplyAcceptanceVO resp = projProjectAcceptanceMapper.getAcceptanceByProjectId(dto);
        resp.setIsOnline(projectInfo.getProjectDeliverStatus().equals(ProjectDeliverStatusEnums.ONLINE.getCode())
                || projectInfo.getProjectDeliverStatus().equals(ProjectDeliverStatusEnums.ACCEPTED.getCode())
                || projectInfo.getProjectDeliverStatus().equals(ProjectDeliverStatusEnums.FIRST_ACCEPTED.getCode()));
        //组装产品列表信息
        List<ProductInfo> allList = projOrderProductMapper.findAllList(productInfoDTO);
        List<ProductInfo> resList = allList.stream().filter(v -> !StringUtils.isEmpty(v.getProductName()))
                .collect(Collectors.toList());
        String name = resList.stream().map(ProductInfo::getProductName).distinct().collect(Collectors.toList()).stream()
                .collect(Collectors.joining(","));
        resp.setProductNames(name);
        resp.setProductCount(resList.size());
        resp.setAcceptanceStatusName(AcceptanceStatusEnum.getDescByCode(
                resp.getAcceptanceStatus() == null ? 0 : resp.getAcceptanceStatus()));
        if (ObjectUtil.isNotEmpty(resp.getAccepterUserId())) {
            resp.setAccepterUserName(
                    sysUserMapper.selectUserIdByYungyingIdIgnoreDeleted(
                                    Convert.toStr(resp.getAccepterUserId()))
                            .getUserName());
        }
        //初始化状态组装数据
        if (ObjectUtil.isEmpty(resp.getAcceptanceStatus())) {
            resp.setButtonDisplayFlag(FrontVisibleEnum.DISPLAY.getCode());
            resp.setAcceptanceStatus(AcceptanceStatusEnum.NOT_APPLY.getCode());
            resp.setAcceptanceTimes(0);
            resp.setCreaterId(userHelper.getCurrentUser().getSysUserId());
            resp.setCreaterName(userHelper.getCurrentUser().getUserName());
            resp.setCreateTime(new Date());
        } else {
            //判断前端展示信息  验收通过和验收不通过两种状态  前端展示验收信息
            if (resp.getAcceptanceStatus().equals(AcceptanceStatusEnum.ACCEPT_NOT_PASS.getCode())
                    || resp.getAcceptanceStatus().equals(AcceptanceStatusEnum.ACCEPT_PASS.getCode())) {
                resp.setAccepDisplayFlag(FrontVisibleEnum.DISPLAY.getCode());
            } else {
                resp.setAccepDisplayFlag(FrontVisibleEnum.NOT_DISPLAY.getCode());
            }
            boolean firstPass = resp.getRequiredAcceptanceTimes() == 2 && resp.getCurrentTimes() == 1
                    && resp.getAcceptanceStatus().equals(AcceptanceStatusEnum.ACCEPT_PASS.getCode());
            //未申请状态 和申请被驳回状态 和验收不通过 是展示申请验收按钮的
            if (resp.getAcceptanceStatus().equals(AcceptanceStatusEnum.NOT_APPLY.getCode())
                    || resp.getAcceptanceStatus().equals(AcceptanceStatusEnum.APPLY_REJECT.getCode())
                    || resp.getAcceptanceStatus().equals(AcceptanceStatusEnum.ACCEPT_NOT_PASS.getCode()) || firstPass) {
                resp.setButtonDisplayFlag(FrontVisibleEnum.DISPLAY.getCode());
            } else {
                resp.setButtonDisplayFlag(FrontVisibleEnum.NOT_DISPLAY.getCode());
            }
            //需要二次验收，并且当前为第一次验收通过状态 展示二次申请按钮
            boolean secondNoPass = resp.getRequiredAcceptanceTimes() == 2 && resp.getCurrentTimes() == 2
                    && resp.getAcceptanceStatus().equals(AcceptanceStatusEnum.APPLY_REJECT.getCode());
            if (firstPass || secondNoPass) {
                resp.setTwiceApplyFlag(1);
                //判断是否有二次验收的权限-第一次验收通过后，至少三周后才能申请二次验收
                if (firstPass) {
                    Date acceptanceTime = resp.getAcceptanceTime();
                    Date now = new Date();
                    if (DateUtil.between(now, acceptanceTime, DateUnit.DAY) >= SECOND_APPLY_DAYS_LIMIT) {
                        resp.setCanApplyTwiceFlag(1);
                    } else {
                        resp.setCanApplyTwiceFlag(0);
                    }
                } else if (secondNoPass) {
                    resp.setCanApplyTwiceFlag(1);
                }
            } else {
                resp.setTwiceApplyFlag(0);
            }
        }
        return resp;
    }

    @Override
    public int updateAcceptanceStatus(ProjApplyAcceptanceVO updateParam) {
        return projProjectAcceptanceMapper.updateAcceptanceStatus(updateParam);
    }

    @Override
    public AcceptanceTimesResVO getTimesAndlogs(ProjectAcceptanceDTO dto) {
        List<AcceptanceTimesVO> resList = new ArrayList<>();
        Long currentVoid = null;
        List<ProjProjectAcceptance> acceptance = projProjectAcceptanceMapper.getAcceptance(dto.getProjectInfoId())
                .stream().sorted(Comparator.comparing(ProjProjectAcceptance::getAcceptanceTimes))
                .collect(Collectors.toList());
        for (ProjProjectAcceptance projProjectAcceptance : acceptance) {
            AcceptanceTimesVO vo = new AcceptanceTimesVO();
            vo.setId(projProjectAcceptance.getProjectAcceptanceId());
            vo.setName("第" + (projProjectAcceptance.getAcceptanceTimes() == null ? 0
                    : projProjectAcceptance.getAcceptanceTimes()) + "次申请验收");
            currentVoid = projProjectAcceptance.getProjectAcceptanceId();
            resList.add(vo);
        }
        List<ProjProjectAcceptanceLog> logs = projProjectAcceptanceLogService.getLogsByAcceptanceId(
                dto.getProjectInfoId());
        Integer finalStatus = 0;
        if (!ObjectUtil.isEmpty(acceptance)) {
            Optional<ProjProjectAcceptance> first = acceptance.stream().findFirst();
            if (first.isPresent()) {
                finalStatus = first.get().getAcceptanceStatus();
            }
        }
        AcceptanceTimesResVO vo = new AcceptanceTimesResVO();
        vo.setVos(resList);
        vo.setCurrentVo(currentVoid);
        vo.setLogs(logs);
        vo.setCurrentStatus(AcceptanceStatusEnum.getDescByCode(finalStatus));
        return vo;
    }

    @Override
    @Transactional
    public Result<AcceptanceResVO> applyAcceptance(ProjApplyAcceptanceDTO dto) {
        Long acceptanceId = SnowFlakeUtil.getId();
        AcceptanceResVO resVO = new AcceptanceResVO();
        Date crtTime = new Date();
        //如果是初始化保存或者申请未通过的进行新增一条并提交
        int currentTimes = 1;
        ProjProjectAcceptance acceptance;
        if (dto.getAcceptanceStatus().equals(AcceptanceStatusEnum.NOT_APPLY.getCode())) {
            acceptance = getAcceptance(dto, acceptanceId, crtTime);
            acceptance.setCurrentTimes(currentTimes);
            projProjectAcceptanceMapper.insert(acceptance);
            log.info("申请验收表插入申请信息成功！！！");
            resVO.setProjectAcceptanceId(acceptanceId);
            //更新rule数据，更新申请单id
            dto.getRules().forEach(v -> {
                v.setProjectAcceptanceId(acceptanceId);
                v.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
                v.setUpdateTime(crtTime);
                v.setProjectAcceptanceId(acceptanceId);
            });
            projProjectAcceptanceRuleMapper.batchUpdate(dto.getRules());
        } else {
            acceptance = projProjectAcceptanceMapper.getAcceptance(dto.getProjectInfoId()).get(0);
            if (acceptance == null) {
                throw new RuntimeException("未查询到申请信息");
            }
            if ((acceptance.getAcceptanceStatus().equals(AcceptanceStatusEnum.ACCEPT_PASS.getCode())
                    && acceptance.getCurrentTimes() == 1) || (
                    acceptance.getAcceptanceStatus().equals(AcceptanceStatusEnum.APPLY_REJECT.getCode())
                            && acceptance.getCurrentTimes() == 2)) {
                currentTimes = 2;
            }
            dto.setCurrentTimes(currentTimes);
            dto.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
            dto.setUpdateTime(crtTime);
            dto.setAcceptanceStatus(AcceptanceStatusEnum.SUBMIT_APPLY.getCode());
            projProjectAcceptanceMapper.updateById(dto);
            log.info("申请单信息修改成功！！！");
            //更改模版数据
            dto.getRules().forEach(v -> {
                v.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
                v.setUpdateTime(crtTime);
            });
            projProjectAcceptanceRuleMapper.batchUpdate(dto.getRules());
            resVO.setProjectAcceptanceId(dto.getProjectAcceptanceId());
        }
        //获取工单信息
        ProjProjectInfo projProjectInfo = projectInfoMapper.selectOne(
                new QueryWrapper<ProjProjectInfo>().eq("project_info_id", dto.getProjectInfoId()));
        ProjOrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(projProjectInfo.getOrderInfoId());
        if (orderInfo == null) {
            throw new RuntimeException("未查询到运营平台工单信息");
        }
        //回更项目申请时间
        //只有第一次验收申请更新申请验收时间
        if (currentTimes == 1) {
            ProjProjectInfo projectInfo = new ProjProjectInfo();
            projectInfo.setProjectInfoId(dto.getProjectInfoId());
            projectInfo.setApplyAcceptTime(crtTime);
            projectInfo.setExternalAcceptTime(acceptance.getExternalAcceptanceTime());
            projProjectInfoMapper.updateById(projectInfo);
        }
        //插入日志
        ProjProjectAcceptanceLog acceptanceLog = getProjProjectAcceptanceLog(dto, acceptanceId);
        projProjectAcceptanceLogService.batchInsert(Collections.singletonList(acceptanceLog));
        log.info("插入日志表信息成功！！！");
        //给调用运营平台接口组织参数
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        productInfoDTO.setProjectInfoId(dto.getProjectInfoId());
        productInfoDTO.setDeliveryOrderNo(orderInfo.getDeliveryOrderNo());
        List<ProductInfo> allList = projOrderProductMapper.findProductList(productInfoDTO);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        CheckApplySoftwareReq softwareReq = new CheckApplySoftwareReq(dto, allList, orderInfo);
        softwareReq.setCurrentTimes(currentTimes);
        softwareReq.setProjCheckName(dto.getProjectName() + "验收申请");
        softwareReq.setOperTime(format.format(crtTime));
        //只有第一次验收申请需要传输文件
        if (currentTimes == 1) {
            List<FileReq> fileReqList = new ArrayList<>();
            dto.getRules().forEach(v -> {
                if (StringUtils.isNotEmpty(v.getFilePath())) {
                    fileReqList.add(new FileReq(v.getFileName(), getObsFilePath(v.getFilePath())));
                }
            });
            //文件改造
            softwareReq.setFileList(fileReqList);
        }
        List<ProjBackendApproveRecord> unionApproves = new LambdaQueryChainWrapper<>(projBackendApproveRecordMapper)
                .eq(ProjBackendApproveRecord::getProjectInfoId, dto.getProjectInfoId())
                .eq(ProjBackendApproveRecord::getIsDeleted, 0)
                .list();
        List<JSONObject> projUnionList = new ArrayList<>();
        for (ProjBackendApproveRecord item : unionApproves) {
            ProjBackendApproveType approveType = new LambdaQueryChainWrapper<>(projBackendApproveTypeMapper)
                    .eq(ProjBackendApproveType::getApproveTypeId, item.getApproveTypeId())
                    .one();
            if (!StrUtil.equals("unionImpl", approveType.getApproveTypeCode())) {
                //非联合实施的就跳过
                continue;
            }
            //发现实施模式还未审批完成，那么不允许提交
            if (Boolean.FALSE.equals(item.getCompleted())) {
                throw new CustomException(StrUtil.format("实施模式【{}】还未审批完成", item.getApproveTypeName()));
            }
            if (item.getCompleteStatus() != 2) {
                continue;
            }
            //组装联合实施模式下的部门分成比例
            JSONArray formArr = JSON.parseArray(item.getFormData());
            int size = formArr.size();
            for (int i = 0; i < size; i++) {
                JSONObject formObj = formArr.getJSONObject(i);
                JSONObject dept = new JSONObject();
                dept.put("orgId", formObj.getLongValue("deptYunyingId"));
                dept.put("unionRate", formObj.getString("proportion"));
                projUnionList.add(dept);
            }
        }
        softwareReq.setProjUnionList(projUnionList);
        String account = userHelper.getCurrentUser().getAccount();
        log.info("调用运营平台软件申请接口-参数-{}", JSON.toJSONString(softwareReq));
        // 创建options对象-设置超时时间-解决大文件超时问题
        Request.Options options = new Request.Options(20, TimeUnit.SECONDS, 20, TimeUnit.MINUTES, true);
        String resp = yunyingFeignClient.checkApplySoftware(options, account, softwareReq);
        log.info("调用运营平台软件申请接口-返回值-{}", resp);
        JSONObject responseResult = JSON.parseObject(resp);
        if (!responseResult.getBoolean("success")) {
            throw new RuntimeException("调用运营平台软件验收接口报错，错误信息：" + responseResult.getString("msg"));
        }
        if (currentTimes == 1) {
            //首次申请验收时检验是否申请过学习环境，如有，则标记学习环境医院状态为待回收
            projExamService.handleExamHospital(dto.getProjectInfoId());
        }
        return Result.success(resVO);
    }

    @NotNull
    private ProjProjectAcceptance getAcceptance(ProjApplyAcceptanceDTO dto, Long acceptanceId, Date crtTime) {
        ProjProjectAcceptance acceptance = new ProjProjectAcceptance();
        acceptance.setProjectAcceptanceId(acceptanceId);
        acceptance.setYyOrderId(dto.getYyOrderId());
        acceptance.setProjectInfoId(dto.getProjectInfoId());
        acceptance.setExpectedAcceptanceTime(dto.getExpectedAcceptanceTime());
        acceptance.setExternalAcceptanceType(dto.getExternalAcceptanceType());
        acceptance.setExternalAcceptanceTime(dto.getExternalAcceptanceTime());
        acceptance.setApplyAcceptanceTime(crtTime);
        acceptance.setCreateTime(crtTime);
        acceptance.setCreaterId(userHelper.getCurrentUser().getSysUserId());
        acceptance.setAcceptanceTimes(dto.getAcceptanceTimes() + 1);
        acceptance.setAcceptanceStatus(AcceptanceStatusEnum.SUBMIT_APPLY.getCode());
        acceptance.setAccepterUserId(dto.getAccepterUserId());
        acceptance.setExternalAcceptanceType(dto.getExternalAcceptanceType());
        return acceptance;
    }

    @NotNull
    private ProjProjectAcceptanceLog getProjProjectAcceptanceLog(ProjApplyAcceptanceDTO dto, Long acceptanceId) {
        ProjProjectAcceptanceLog acceptanceLog = new ProjProjectAcceptanceLog();
        acceptanceLog.setProjectAcceptanceLogId(SnowFlakeUtil.getId());
        acceptanceLog.setProjectAcceptanceId(acceptanceId);
        acceptanceLog.setProjectInfoId(dto.getProjectInfoId());
        acceptanceLog.setOperateTitle(userHelper.getCurrentUser().getUserName() + "申请项目验收操作");
        acceptanceLog.setOperateTime(new Date());
        acceptanceLog.setOperateContent(userHelper.getCurrentUser().getUserName() + "提交了申请");
        acceptanceLog.setCreaterId(userHelper.getCurrentUser().getSysUserId());
        acceptanceLog.setCreateTime(new Date());
        acceptanceLog.setIsDeleted(0);
        acceptanceLog.setOperaterUserId(userHelper.getCurrentUser().getSysUserId());
        acceptanceLog.setOperaterUserName(userHelper.getCurrentUser().getUserName());
        acceptanceLog.setYyOrderId(dto.getYyOrderId());
        return acceptanceLog;
    }

    @Override
    public List<ProjProjectAcceptanceRuleVO> getAcceptanceRules(ProjectAcceptanceDTO dto) {
        List<ProjProjectAcceptanceRuleVO> rules =
                projProjectAcceptanceRuleMapper.getAcceptanceRulesByProjectInfoId(dto.getProjectInfoId());
        if (CollectionUtils.isEmpty(rules)) {
            ProjProjectInfo projectInfo = projProjectInfoService.selectByPrimaryKey(dto.getProjectInfoId());
            rules = ruleProjectRuleConfigService.getTemplateByCode(projectInfo);
        }
        rules.forEach(v -> {
            if (StringUtils.isNotEmpty(v.getFilePath())) {
                String temporaryUrlFile = OBSClientUtils.getTemporaryUrl(v.getFilePath(), 3600);
                v.setFilePath(temporaryUrlFile);
            }
            if (StringUtils.isNotEmpty(v.getTemplateUrl())) {
                String temporaryUrlTemplate = OBSClientUtils.getTemporaryUrl(v.getTemplateUrl(), 3600);
                v.setTemplateUrl(temporaryUrlTemplate);
            }
        });
        return rules;
    }

    @Override
    public List<ProjApplyAcceptanceVO> selectAcceptanceByYyOrderId(Long yyOrderId, Long projectInfoId) {
        return projProjectAcceptanceMapper.selectAcceptanceByYyOrderId(yyOrderId, projectInfoId);
    }

    /**
     * 根据运营平台的参数要求   将文件的obs路径 "?" 以前的参数进行decode操作，获得新的url
     *
     * @param obsfilePath
     * @return
     */
    @Deprecated
    public String getNewFilePath(String obsfilePath) {
        //转码只替换中文汉字
        int index = obsfilePath.indexOf("?");
        String oldString = obsfilePath.substring(0, index);
//        String newString = URLDecoder.decode(oldString, "UTF-8");
        // 正则表达式，匹配汉字的url编码
        String regex = "%[eE][4-9A-Fa-f]%[8-9A-Fa-f][0-9A-Fa-f]%[8-9A-Fa-f][0-9A-Fa-f]";
        // 编译正则表达式
        Pattern pattern = Pattern.compile(regex);
        // 创建matcher对象
        Matcher matcher = pattern.matcher(oldString);
        // StringBuilder用于构建最终的解码字符串
        StringBuilder decodedText = new StringBuilder();
        int lastMatchEnd = 0; // 上一个匹配结束的位置
        // 查找所有匹配项
        while (matcher.find()) {
            // 将匹配项之间的未处理文本添加到decodedText中
            decodedText.append(oldString, lastMatchEnd, matcher.start());
            // 解码当前匹配项
            try {
                String decoded = URLDecoder.decode(matcher.group(), "UTF-8");
                decodedText.append(decoded);
                // 更新上一个匹配结束的位置
                lastMatchEnd = matcher.end();
            } catch (UnsupportedEncodingException e) {
                // 在这里处理编码异常，虽然UTF-8是广泛支持的
                log.error("，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            }
        }
        // 添加最后一个匹配项之后的任何剩余文本
        decodedText.append(oldString, lastMatchEnd, oldString.length());
        String newString = decodedText.toString();
        return obsfilePath.replace(oldString, newString);
    }

    /**
     * 获取传递给运营平台的obs路径 如果是加密后的则直接进行转换   加密前的则加密后进行转换
     *
     * @param filePath
     */
    public String getObsFilePath(String filePath) {
        String path;
        //判断传给运营平台的路径是否经过加密
        if (filePath.contains("https:")) {
            path = filePath;
        } else {
            path = OBSClientUtils.getTemporaryUrl(filePath, 3600);

        }
        return path;

    }

    @Override
    @Transactional
    public Result syncAcceptResult(SyncAcceptResult result) {
        log.info("同步验收结果参数 -> {}", JSON.toJSONString(result));
        ProjProjectAcceptanceDTO dto = new ProjProjectAcceptanceDTO(result);
        List<Long> projectInfoIds = projProjectInfoService.getProjectInfoIdByOrderId(result.getWorkOrderId());
        if (CollectionUtils.isEmpty(projectInfoIds)) {
            return Result.success();
        }
        Long projectInfoId = projectInfoIds.get(0);
        dto.setProjectInfoId(projectInfoId);
        //根据工单id和项目 id 更新 项目验收表状态
        List<ProjApplyAcceptanceVO> vos = this.selectAcceptanceByYyOrderId(dto.getYyOrderId(), dto.getProjectInfoId());
        MessageParam messageParam = new MessageParam();
        //获取到当前项目当前工单id下最新和最老的一条数据-已经倒叙排列了所以货确定是最新数据
        Optional<ProjApplyAcceptanceVO> first = vos.stream().findFirst();
        ProjApplyAcceptanceVO applyAcceptanceVO = new ProjApplyAcceptanceVO();
        if (first.isPresent()) {
            applyAcceptanceVO = first.get();
        }
        SysUser sysUser = sysUserMapper.selectUserIdByYungyingId(Convert.toStr(result.getAccepterId()));
        if (ObjectUtil.isEmpty(sysUser)) {
            log.info("csm平台不存在该用户{}", result.getAccepterId());
            throw new RuntimeException("用户不存在！！！");
        }
        /*如果运营平台是内部驳回  即csm平台申请验收表的最新一条数据状态不是已申请验收状态，
         * 此时调用驳回接口  则视为运营平台的内部驳回流程
         * 只需要给项目经理发送消息即可，无需更改我们表里面的数据
         * */
        //todo  内部驳回流程暂时取消
//        if (result.getStatus() == 3
//                && !lastapplyAcceptanceVO.getAcceptanceStatus().equals(AcceptanceStatusEnum.SUBMIT_APPLY.getCode())) {
//            log.info("运营平台内部驳回流程{}{}", result.getStatus(), lastapplyAcceptanceVO.getAcceptanceStatus());
//            try {
//                ProjProjectInfo projProjectInfo = mainService.getProjectInfo(projectInfoId);
//                // 首期项目发送消息
//                if (projProjectInfo.getHisFlag() == 1) {
//                    messageParam = this.handleSendMessage(projectInfoId, lastapplyAcceptanceVO.getAcceptanceStatus());
//                    sendMessageService.sendMessage(messageParam);
//                }
//            } catch (Exception e) {
//                log.error("运营平台内部驳回流程,只发送消息,异常信息{}", e.getMessage());
//            }
//            return Result.success("运营平台内部驳回流程,只发送消息");
//        }
        //审核未通过的产品集合
        if (StringUtils.isNotEmpty(dto.getUnpassProductIds())) {
            List<String> unpassList = Arrays.asList(dto.getUnpassProductIds().split(","));
            List<Integer> list = unpassList.stream()
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            log.info("运营平台返回未通过验收产品{}", list);
            //更新工单产品表产品信息状态
            projOrderProductService.updateExcutionStatusByOrderId(dto.getYyOrderId(), list,
                    ExcutionStatusEnum.ACCEPT_NOT_PASS.getCode(), ExcutionStatusEnum.ACCEPT_PASS.getCode());
        }
        ProjApplyAcceptanceVO updateParam = new ProjApplyAcceptanceVO();
        updateParam.setProjectAcceptanceId(applyAcceptanceVO.getProjectAcceptanceId());
        updateParam.setRequiredAcceptanceTimes(dto.getRequiredAcceptanceTimes());
        updateParam.setCurrentTimes(dto.getCurrentTimes());
        updateParam.setAccepterUserId(dto.getAccepterUserId());
        updateParam.setAccepterUserName(
                sysUserMapper.selectUserIdByYungyingId(String.valueOf(dto.getAccepterUserId())).getUserName());
        updateParam.setAcceptanceScore(dto.getAcceptanceScore());
        updateParam.setAcceptanceTime(dto.getAcceptanceTime());
        updateParam.setAcceptanceStatus(
                AcceptanceStatusEnum.convertStatus(dto.getAcceptanceStatus(), Convert.toInt(dto.getAcceptanceScore())));
        this.updateAcceptanceStatus(updateParam);
        //验收时间更新
        applyAcceptanceVO.setAcceptanceTime(dto.getAcceptanceTime());
        int requiredAcceptanceTimes = result.getRequiredAcceptanceTimes();
        int currentTimes = result.getCurrentTimes();
        //项目最终验收通过
        if (updateParam.getAcceptanceStatus().equals(AcceptanceStatusEnum.ACCEPT_PASS.getCode())
                && currentTimes == requiredAcceptanceTimes) {
            ProjMilestoneInfo milestoneInfo = projMilestoneInfoService.getMilestoneInfo(projectInfoId,
                    MilestoneNodeEnum.PROJECT_ACCEPT.getCode());
            if (ObjectUtil.isNotNull(milestoneInfo)) {
                log.error("项目{}不存在节点信息", projectInfoId);
                UpdateMilestoneDTO updateDTO = new UpdateMilestoneDTO();
                updateDTO.setMilestoneInfoId(milestoneInfo.getMilestoneInfoId());
                updateDTO.setMilestoneStatus(Convert.toInt(MilestoneStatusEnum.COMPLETED.getCode()));
                updateDTO.setNodeHeadId(
                        sysUserMapper.selectUserIdByYungyingId(result.getAccepterId().toString()).getSysUserId());
                projMilestoneInfoService.updateMilestone(updateDTO);
            }
            //更新项目表外部验收时间和提交验收申请时间
            ProjProjectInfo projectInfo = getProjectInfo(applyAcceptanceVO, projectInfoId);
            projProjectInfoService.updateByPrimaryKeySelective(projectInfo);
            projectInfo = projectInfoMapper.selectById(projectInfoId);
            try {
                // 首期项目发送消息
                if (projectInfo.getHisFlag() == 1) {
                    messageParam = this.handleSendMessage(projectInfoId, AcceptanceStatusEnum.ACCEPT_PASS.getCode());
                    sendMessageService.sendMessage(messageParam);
                }
            } catch (Exception e) {
                log.error("运营平台验收通过流程,发送消息,异常信息{}", e.getMessage());
            }
            //调用系统管理，限制表单操作
            syncSystem(projectInfo);
            //首期项目-修改统计表最后申请验收时间
            if (projectInfo.getHisFlag() == 1) {
                ReportCustomInfo updateData = new ReportCustomInfo();
                updateData.setProjectInfoId(projectInfoId);
                updateData.setLastApplyAcceptTime(projectInfo.getApplyAcceptTime());
                updateData.setUpdateTime(new Date());
                updateData.setUpdaterId(sysUser.getSysUserId());
                int count = reportCustomInfoMapper.updateByProjectInfoId(updateData);
                log.info("修改统计表最后申请验收时间,影响条数:{}, 项目id {}", count, projectInfoId);
                // 向tmp_hospital_limit添加数据，限制三方接口及制作报表。
                // saveTmpHospitalLimit(projectInfoId);
                // 向config_custom_form_limit 表添加数据，限制三方接口及制作报表、表单。
                saveConfigCustomFormLimit(projectInfo);
            }
            // 项目验收通过后，判断是否是正式项目经理，不是正式项目经理且不存在其他项目是项目经理的情况下，调用运营平台回收项目经理权限
            this.deleteProjectMemberFlag(projectInfo);
        }
        if (updateParam.getAcceptanceStatus().equals(AcceptanceStatusEnum.ACCEPT_PASS.getCode())
                && currentTimes < requiredAcceptanceTimes) {
            log.info("运营平台返回验收通过,但是当前验收次数小于总验收次数,当前验收次数:{},总验收次数: {}", currentTimes,
                    requiredAcceptanceTimes);
            //一次验收通过
            ProjProjectInfo projectInfo = new ProjProjectInfo();
            projectInfo.setProjectDeliverStatus(ProjectDeliverStatusEnums.FIRST_ACCEPTED.getCode());
            projectInfo.setProjectInfoId(projectInfoId);
            projProjectInfoService.updateByPrimaryKeySelective(projectInfo);
        }
        //组装日志参数
        ProjProjectAcceptanceLog acceptanceLog = handleAcceptanceLog(dto);
        acceptanceLog.setProjectAcceptanceId(applyAcceptanceVO.getProjectAcceptanceId());
        log.info("日志组装参数{}", acceptanceLog);
        projProjectAcceptanceLogService.batchInsert(Collections.singletonList(acceptanceLog));
        return Result.success();
    }

    /**
     * 项目验收通过后，判断是否是正式项目经理，不是正式项目经理且不存在其他项目是项目经理的情况下，调用运营平台回收项目经理权限
     *
     * @param projectInfo
     */
    @Override
    public void deleteProjectMemberFlag(ProjProjectInfo projectInfo) {
        String uuid = StrUtil.uuid().replace(StrUtil.DASHED, StrUtil.EMPTY);
        try {
            Boolean booleanFlag = false;
            String loginUserAccount = userHelper.getCurrentUser().getAccount();
            SysUser sysUser = sysUserMapper.selectById(projectInfo.getProjectLeaderId());
            if (sysUser != null) {
                ProjectMemberToYunYingReq req = new ProjectMemberToYunYingReq();
                req.setToken(YunYingServiceImpl.TOKEN);
                req.setUserId(Long.parseLong(sysUser.getUserYunyingId()));
                log.error("查询是否可修改为项目经理，调用运营平台接口查询是否项目经理yanshou，req={}", req);
                // 调用运营平台接口查询是否项目经理
                String result = yunyingFeignClient.selectProjectMemberFlag(loginUserAccount, req);
                log.error("查询是否可修改为项目经理，调用运营平台接口查询是否项目经理，result={}", result);
                if (StrUtil.isNotBlank(result)) {
                    JSONObject jsonObject = JSONObject.parseObject(result);
                    if (ObjectUtil.isNotEmpty(jsonObject) && ((boolean) jsonObject.get("success"))) {
                        JSONObject data = (JSONObject) jsonObject.get("obj");
                        Object status = data != null ? data.get("status") : "";
                        if ("2".equals(String.valueOf(status))) {
                            booleanFlag = true;
                        }
                    }
                }
                // 根据项目经理id查询所有未验收状态的项目
                List<ProjProjectInfo> list = projectInfoMapper.selectListByLeaderId(projectInfo.getProjectLeaderId());
                // 当前人员是临时项目经理并且不存在其他未验收项目的项目经理，调用运营平台回收项目经理权限
                if (booleanFlag && (list == null || list.isEmpty())) {
                    log.info("调用运营平台回收项目经理权限,项目id:{} 入参 {}", projectInfo.getProjectInfoId(), req);
                    sysOperLogService.apiOperLogInsertObjAry("调用运营平台回收项目经理权限==>deleteProjectMemberFlag==>入参", uuid, Log.LogOperType.OTHER.getCode(), req);
                    String resultDelete = yunyingFeignClient.deleteProjectMemberFlag(loginUserAccount, req);
                    log.error("调用运营平台回收项目经理权限,result={}", resultDelete);
                    sysOperLogService.apiOperLogInsertObjAry("调用运营平台回收项目经理权限==>deleteProjectMemberFlag==>出参result", uuid, Log.LogOperType.OTHER.getCode(), resultDelete);
                }
            }
        } catch (Exception e) {
            log.error("调用运营平台回收项目经理权限,异常信息{}", e.getMessage());
            sysOperLogService.apiOperLogInsertObjAry("调用运营平台回收项目经理权限==>deleteProjectMemberFlag==>失败", uuid, Log.LogOperType.OTHER.getCode(), projectInfo);
        }
    }

    /**
     * 向config_custom_form_limit 表添加数据，限制三方接口及制作报表、表单。
     *
     * @param projectInfo
     */
    @Override
    public void saveConfigCustomFormLimit(ProjProjectInfo projectInfo) {
        // 限制客服工作，默认不限制 TODO 从表查询配置数据
        // 0 开启  1 关闭
        int limitCustomer = 1;
        int daysAfterCheck = 30;
        try {
            String configCode = "customLimit";
            SysConfig limitCustomerSysConfig = sysConfigMapper.selectConfigByName(configCode);
            if (limitCustomerSysConfig != null) {
                try {
                    limitCustomer = Integer.parseInt(limitCustomerSysConfig.getConfigValue());
                } catch (Exception e) {
                    log.error("获取配置表数据异常，配置编码：{}，配置值：{}", configCode, limitCustomerSysConfig.getConfigValue());
                }
            }
            configCode = "customLimitDay";
            SysConfig daysAfterCheckSysConfig = sysConfigMapper.selectConfigByName(configCode);
            if (daysAfterCheckSysConfig != null) {
                try {
                    daysAfterCheck = Integer.parseInt(daysAfterCheckSysConfig.getConfigValue());
                } catch (Exception e) {
                    log.error("获取配置表数据异常，配置编码：{}，配置值：{}", configCode, daysAfterCheckSysConfig.getConfigValue());
                }
            }
            List<ConfigCustomLimit> list = configCustomLimitMapper.selectList(new QueryWrapper<ConfigCustomLimit>().eq("custom_info_id", projectInfo.getCustomInfoId()));
            if (list != null && list.size() > 0) {
                for (ConfigCustomLimit configCustomLimit : list) {
                    configCustomLimit.setSwitchFlag(limitCustomer);
                    configCustomLimitMapper.updateById(configCustomLimit);
                }
            } else {
                ConfigCustomLimit configCustomLimit = new ConfigCustomLimit();
                configCustomLimit.setCustomLimitId(SnowFlakeUtil.getId());
                configCustomLimit.setCustomInfoId(projectInfo.getCustomInfoId());
                configCustomLimit.setSwitchFlag(limitCustomer);
                configCustomLimitMapper.insert(configCustomLimit);
            }

            // 查询医院数据
            SelectHospitalDTO dto = new SelectHospitalDTO();
            dto.setProjectInfoId(projectInfo.getProjectInfoId());
            List<ProjHospitalInfo> hospitalInfoList = hospitalInfoMapper.getHospitalInfoByProjectId(dto);
            List<ConfigCustomFormLimit> haveList = configCustomFormLimitMapper.selectListByParamer(hospitalInfoList);
            List<String> haveDataFlagList = new ArrayList<>();
            if (haveList != null && haveList.size() > 0) {
                int finalLimitCustomer = limitCustomer;
                int finalDaysAfterCheck = daysAfterCheck;
                haveList.forEach(item -> {
                    item.setSwitchFlag(finalLimitCustomer);
                    item.setDaysAfterCheck(finalDaysAfterCheck);
                    item.setHasDeal(0);
                    configCustomFormLimitMapper.updateByPrimaryKeySelective(item);
                    String haveDataFlag = item.getHospitalInfoId() + item.getMsunHealthModuleCode();
                    if (!haveDataFlagList.contains(haveDataFlag)) {
                        haveDataFlagList.add(haveDataFlag);
                    }

                });
            }

            List<ConfigCustomFormLimit> configCustomFormLimits = new ArrayList<>();
            if (hospitalInfoList != null && hospitalInfoList.size() > 0) {

                for (ProjHospitalInfo hospital : hospitalInfoList) {
                    for (CustomerLimitProductEnum value : CustomerLimitProductEnum.values()) {
                        String haveDataFlag = hospital.getHospitalInfoId() + value.getCode();
                        if (!haveDataFlagList.contains(haveDataFlag)) {
                            ConfigCustomFormLimit limit = new ConfigCustomFormLimit();
                            Date currentTime = new Date();
                            limit.setCustomFormLimitId(SnowFlakeUtil.getId());
                            limit.setCustomInfoId(hospital.getCustomInfoId());
                            limit.setHospitalInfoId(hospital.getHospitalInfoId());
                            limit.setOrderProductId(value.getYyProductId());
                            limit.setMsunHealthModuleCode(value.getCode());
                            limit.setSwitchFlag(limitCustomer);
                            limit.setDaysAfterCheck(daysAfterCheck);
                            limit.setIsDeleted(0);
                            limit.setCreaterId(projectInfo.getProjectLeaderId());
                            limit.setUpdaterId(projectInfo.getProjectLeaderId());
                            limit.setCreateTime(currentTime);
                            limit.setUpdateTime(currentTime);
                            //定时任务是否处理标识
                            limit.setHasDeal(0);
                            //验收时间
                            limit.setAcceptTime(projectInfo.getAcceptTime());

                            configCustomFormLimits.add(limit);
                            haveDataFlagList.add(haveDataFlag);
                        }

                    }
                }

                if (configCustomFormLimits != null && configCustomFormLimits.size() > 0) {
                    configCustomFormLimitMapper.batchInsert(configCustomFormLimits);
                }
            }


        } catch (Exception e) {
            log.error("向config_custom_form_limit 表添加数据，限制三方接口及制作报表、表单异常,异常信息{}", e.getMessage());
        }

    }

    /**
     * 保存数据
     *
     * @param projectInfoId
     */
    @Override
    public void saveTmpHospitalLimit(Long projectInfoId) {
        try {
            tmpHospitalLimitMapper.saveDataByProjectInfoId(projectInfoId);
        } catch (Exception e) {
            log.error("保存TmpHospitalLimit异常,异常信息{}", e.getMessage());
        }
    }

    /**
     * 调用系统管理，限制表单操作
     *
     * @param projectInfo
     */
    private void syncSystem(ProjProjectInfo projectInfo) {
        log.info("同步验收结果-调用系统管理，限制表单操作-开始");
        //1. 根据项目查询医院、产品
        SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
        selectHospitalDTO.setProjectInfoId(projectInfo.getProjectInfoId());
        List<ProjHospitalInfo> hospitalInfoList = hospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);
        //2. 拼装参数
        List<ProjProductEmpowerRecord> empowerRecords = productEmpowerRecordMapper.findEmpowerRecordByProjectIds(
                Collections.singletonList(projectInfo.getProjectInfoId()));
        log.info("同步验收结果-调用系统管理，限制表单操作，查询到的授权产品数据:{}",
                JSONObject.toJSONString(empowerRecords));
        if (!CollectionUtils.isEmpty(empowerRecords)) {
            Set<String> sysCode = empowerRecords.stream().map(ProjProductEmpowerRecord::getMsunHealthModuleCode)
                    .collect(Collectors.toSet());
            String[] sysCodeArray = sysCode.toArray(new String[sysCode.size()]);
            CustFormCtrlDTO custFormCtrlDTO = new CustFormCtrlDTO();
            custFormCtrlDTO.setCheckAcceptTime(DateUtil.formatDateTime(projectInfo.getAcceptTime()));
            //限制表单操作天数
            custFormCtrlDTO.setDaysAfterCheckClose(DAYS_AFTER_CHECK_CLOSE);
            //默认是否开启限制
            custFormCtrlDTO.setSwitchFlag(SWITCH_FLAG);
            //2.1 拼装参数
            List<CustFormCtrlSysCode> custFormCtrlSysCodeList = hospitalInfoList.stream().map(hospitalInfo -> {
                CustFormCtrlSysCode custFormCtrlSysCode = new CustFormCtrlSysCode();
                custFormCtrlSysCode.setHospitalId(hospitalInfo.getCloudHospitalId().intValue());
                custFormCtrlSysCode.setSystemCodeList(sysCodeArray);
                return custFormCtrlSysCode;
            }).collect(Collectors.toList());
            custFormCtrlDTO.setSysList(custFormCtrlSysCodeList);
            //3. 调用接口
            //3.1 获取医院信息
            ProjHospitalInfo hospitalInfo = Optional.ofNullable(hospitalInfoList.stream()
                    .filter(projHospitalInfo -> 1 == projHospitalInfo.getHealthBureauFlag()).findFirst()
                    .orElse(null)).orElse(hospitalInfoList.get(0));
            Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
            log.info("设定医院信息:{}", domainMap);
            domainHolder.refresh(domainMap);
            custFormCtrlDTO.setHisOrgId(hospitalInfo.getOrgId());
            custFormCtrlDTO.setHospitalId(hospitalInfo.getCloudHospitalId());
            log.info("调用接口限制报表操作 参数:{}", JSONObject.toJSONString(custFormCtrlDTO));
            ResponseResult responseResult = hospitalApi.saveCustFormCtrl(custFormCtrlDTO);
            log.info("调用接口限制报表操作 返回结果:{}", JSONObject.toJSONString(responseResult));
            domainMap.clear();

            hospitalInfoList.forEach(hospital -> {
                //保存数据到config表
                List<ConfigCustomFormLimit> configCustomFormLimits = new ArrayList<>();

                empowerRecords.forEach(record -> configCustomFormLimits.add(new ConfigCustomFormLimitExtend(projectInfo, hospital, record, custFormCtrlDTO)));

                configCustomFormLimitMapper.batchInsert(configCustomFormLimits);
            });
        }
    }

    /**
     * 组装日志参数
     *
     * @param dto
     * @return
     */
    public ProjProjectAcceptanceLog handleAcceptanceLog(ProjProjectAcceptanceDTO dto) {
        ProjProjectAcceptanceLog acceptanceLog = new ProjProjectAcceptanceLog();
        acceptanceLog.setProjectAcceptanceLogId(SnowFlakeUtil.getId());
        acceptanceLog.setProjectInfoId(dto.getProjectInfoId());
        acceptanceLog.setOperateTime(new Date());
        acceptanceLog.setRemark(dto.getRemark());
        acceptanceLog.setIsDeleted(0);
        acceptanceLog.setCreaterId(-1L);
        acceptanceLog.setCreateTime(new Date());
        acceptanceLog.setOperaterUserId(dto.getAccepterUserId());
        acceptanceLog.setYyOrderId(dto.getYyOrderId());
        String operaterUserName = sysUserMapper.selectUserIdByYungyingId(String.valueOf(dto.getAccepterUserId()))
                .getUserName();
        acceptanceLog.setOperaterUserName(operaterUserName);
        //驳回
        String explanation = "";
        if (dto.getRequiredAcceptanceTimes() == 2) {
            if (dto.getCurrentTimes() == 1) {
                explanation = "第一次验收申请";
            }
            if (dto.getCurrentTimes() == 2) {
                explanation = "第二次验收申请";
            }
        }
        if (dto.getAcceptanceStatus() == Integer.parseInt(AcceptanceTypeEnum.CHECK_OVER.getCode())) {
            acceptanceLog.setOperateTitle(
                    operaterUserName + AcceptanceTypeEnum.CHECK_OVER.getDesc() + "-" + explanation);
            acceptanceLog.setOperateContent(operaterUserName + "对该项目下的-" + dto.getYyOrderId() + "-工单"
                    + explanation + AcceptanceTypeEnum.CHECK_OVER.getDesc());
        } else if (dto.getAcceptanceStatus() == Integer.parseInt(AcceptanceTypeEnum.APPLY_RECEIVE.getCode())) {
            acceptanceLog.setOperateTitle(operaterUserName + AcceptanceTypeEnum.APPLY_RECEIVE.getDesc());
            acceptanceLog.setOperateContent(operaterUserName + "对该项目下的-" + dto.getYyOrderId() + "-工单"
                    + AcceptanceTypeEnum.APPLY_RECEIVE.getDesc());
        } else {
            acceptanceLog.setOperateTitle(
                    operaterUserName + AcceptanceTypeEnum.REJECT_CHECK.getDesc() + "-" + explanation);
            acceptanceLog.setOperateContent(operaterUserName + "对该项目下的-" + dto.getYyOrderId() + "-工单"
                    + explanation + AcceptanceTypeEnum.REJECT_CHECK.getDesc());
        }
        return acceptanceLog;

    }

    /**
     * 给项目经理发送消息
     *
     * @param projectInfoId
     * @param status
     * @return
     */
    public MessageParam handleSendMessage(Long projectInfoId, Integer status) {
        ProjProjectInfo projProjectInfo = projectInfoMapper.selectOne(
                new QueryWrapper<ProjProjectInfo>().eq("project_info_id", projectInfoId));
        Integer type = projProjectInfo.getUpgradationType();
        String content = "";
        String typeName;
        if (Integer.valueOf("1").equals(type)) {
            typeName = "老换新";
        } else {
            typeName = "新客户";
        }
        SysDept sysDept = sysDeptMapper.selectOne(
                new QueryWrapper<SysDept>().eq("dept_yunying_id", projProjectInfo.getProjectTeamId()));
        SysUser sysUser2 = sysUserMapper.selectOne(
                new QueryWrapper<SysUser>().eq("sys_user_id", projProjectInfo.getProjectLeaderId()));
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        productInfoDTO.setProjectInfoId(projProjectInfo.getProjectInfoId());
        //组装产品列表信息
        List<ProductInfo> allList = projOrderProductMapper.findAllList(productInfoDTO);
        List<ProductInfo> resList = allList.stream().filter(v -> !StringUtils.isEmpty(v.getProductName()))
                .collect(Collectors.toList());
        // 验收通过  发送消息
        if (status.equals(AcceptanceStatusEnum.ACCEPT_PASS.getCode())) {
            // xxx项目（老换新）已派工，实施团队xxx，项目经理xxx，本次上线xx个产品，请您知晓！
            content = projProjectInfo.getProjectName() + "(" + typeName + ")" + "已验收通过，验收时间"
                    + DateUtil.formatDateTime(new Date()) + "，实施团队" + sysDept.getDeptName() + "，项目经理"
                    + sysUser2.getUserName() + "，本次上线" + resList.size() + "个产品，请您知晓！";
        } else {
            content = projProjectInfo.getProjectName() + "(" + typeName + ")" + "，运营平台内部已驳回，请您知晓！";
        }
        MessageParam messageParam = new MessageParam();
        messageParam.setProjectInfoId(projectInfoId);
        messageParam.setContent(content);
        messageParam.setMessageTypeId(4002L);
        messageParam.setMessageToCategory(MsgToCategory.TO_ROLE.getCode());
        return messageParam;
    }

    /**
     * 临时存储文件
     *
     * @param dto
     * @return
     */
    @Override
    public Result tempStorageFile(ProjApplyAcceptanceDTO dto) {
        Long projectInfoId = dto.getProjectInfoId();
        int delCount = projProjectAcceptanceRuleMapper.deleteByProjectInfoId(projectInfoId);
        log.info("删除{}条规则", delCount);
        Date crtTime = new Date();
        dto.getRules().forEach(v -> {
            v.setProjectAcceptanceRuleId(SnowFlakeUtil.getId());
            v.setCreaterId(userHelper.getCurrentUser().getSysUserId());
            v.setCreateTime(crtTime);
            v.setProjectInfoId(dto.getProjectInfoId());
        });
        int saveCount = projProjectAcceptanceRuleMapper.batchInsert(dto.getRules());
        return Result.success(saveCount, "保存成功");
    }

    /**
     * 删除临时存储文件-删除关系
     *
     * @param acceptanceRuleId
     * @return
     */
    @Override
    public Result deleteTemp(Long acceptanceRuleId) {
        int count = projProjectAcceptanceRuleMapper.deleteFileById(acceptanceRuleId);
        return Result.success(count, "删除成功");
    }

    /**
     * 老系统迁移验收数据到csm
     *
     * @param projectInfoId
     * @return
     */
    @Override
    public Result sendOldAcceptFileDataForCsm(Long projectInfoId) {
        List<OldAcceptFileDataDTO> oldAcceptFileDataDTOS = projProjectAcceptanceMapper.selectOldAcceptFileData(projectInfoId);
        if (CollectionUtil.isNotEmpty(oldAcceptFileDataDTOS)) {
            // 根据项目id分组
            Map<Long, List<OldAcceptFileDataDTO>> oldAcceptFileDataMap =
                    oldAcceptFileDataDTOS.stream().collect(Collectors.groupingBy(OldAcceptFileDataDTO::getNewProjectInfoId));
            // 根据项目id查询新系统项目是否已经存在了验收文件。当存在时 跳过
            for (Map.Entry<Long, List<OldAcceptFileDataDTO>> entry : oldAcceptFileDataMap.entrySet()) {
                Long newProjectInfoId = entry.getKey();
                List<ProjProjectAcceptanceRuleVO> acceptanceRulesByProjectInfoId =
                        projProjectAcceptanceRuleMapper.getAcceptanceRulesByProjectInfoId(newProjectInfoId);
                if (CollectionUtil.isEmpty(acceptanceRulesByProjectInfoId)) {
                    List<OldAcceptFileDataDTO> oldAcceptFileDataDTOList = entry.getValue();
                    // 验收文件数据保存
                    saveAcceptFileDataForCsm(oldAcceptFileDataDTOList, newProjectInfoId);
                }
            }
        } else {
            return Result.success("没有需要迁移的文件数据");
        }
        return null;
    }

    /**
     * 老系统验收数据保存到csm
     *
     * @param oldAcceptFileDataDTOList
     */
    void saveAcceptFileDataForCsm(List<OldAcceptFileDataDTO> oldAcceptFileDataDTOList, Long newProjectInfoId) {
        Long id = null;
        // 查询是否存在验收信息。当不存在时候进行创建
        List<ProjProjectAcceptance> projProjectAcceptances = projProjectAcceptanceMapper.selectList(
                new QueryWrapper<ProjProjectAcceptance>()
                        .eq("project_info_id", newProjectInfoId)
                        .orderByDesc("create_time")
                        .last("limit 1")
        );
        if (CollectionUtil.isEmpty(projProjectAcceptances)) {
            // 先迁移验收信息后 ， 再迁移文件
            OldAcceptDataDTO oldAcceptDataDTO = projProjectAcceptanceMapper.selectOldAcceptData(newProjectInfoId);
            ProjProjectAcceptance projProjectAcceptance = new ProjProjectAcceptance();
            projProjectAcceptance.setProjectAcceptanceId(SnowFlakeUtil.getId());
            projProjectAcceptance.setYyOrderId(oldAcceptDataDTO.getWorkId());
            projProjectAcceptance.setProjectInfoId(newProjectInfoId);
            projProjectAcceptance.setApplyAcceptanceTime(oldAcceptDataDTO.getApplyTime());
            projProjectAcceptance.setExpectedAcceptanceTime(ObjectUtil.isNotEmpty(oldAcceptDataDTO.getDesireAcceptTime())
                    ? oldAcceptDataDTO.getDesireAcceptTime() : new Date());
            projProjectAcceptance.setExternalAcceptanceType(ObjectUtil.isNotEmpty(oldAcceptDataDTO.getExternalAcceptanceTime())
                    ? 1 : 2);
            projProjectAcceptance.setExternalAcceptanceTime(ObjectUtil.isNotEmpty(oldAcceptDataDTO.getExternalAcceptanceTime())
                    ? oldAcceptDataDTO.getExternalAcceptanceTime() : new Date());
            // 状态处理
            // 老系统 ：          0 草稿 1 提交 2 通过 3 驳回  4 接受申请  5 第一次验收驳回  6  第一次验收通过
            // 新系统 ： 申请状态：0.未申请；1.提交申请验收；5.申请驳回；11.申请已接收； 12第一次验收驳回；13 第一次验收通过；20 第二次验收申请21.验收不通过;25.验收通过
            switch (oldAcceptDataDTO.getStatus()) {
                case 0:
                    projProjectAcceptance.setAcceptanceStatus(0);
                    break;
                case 1:
                    projProjectAcceptance.setAcceptanceStatus(1);
                    break;
                case 2:
                    projProjectAcceptance.setAcceptanceStatus(25);
                    break;
                case 3:
                    projProjectAcceptance.setAcceptanceStatus(5);
                    break;
                case 4:
                    projProjectAcceptance.setAcceptanceStatus(11);
                    break;
                case 5:
                    projProjectAcceptance.setAcceptanceStatus(12);
                    break;
                case 6:
                    projProjectAcceptance.setAcceptanceStatus(13);
                    break;
                default:
                    throw new RuntimeException("错误的验收状态,newProjectInfoId=" + newProjectInfoId);
            }
            projProjectAcceptance.setAccepterUserId(oldAcceptDataDTO.getYyUserId());
            projProjectAcceptance.setAcceptanceTime(oldAcceptDataDTO.getAcceptTime());
            projProjectAcceptance.setAcceptanceScore(oldAcceptDataDTO.getAcceptScore());
            projProjectAcceptance.setAcceptanceTimes(1);
            projProjectAcceptance.setRemark(oldAcceptDataDTO.getRemark());
            projProjectAcceptance.setRequiredAcceptanceTimes(1);
            projProjectAcceptance.setCurrentTimes(1);
            projProjectAcceptanceMapper.insert(projProjectAcceptance);
            id = projProjectAcceptance.getProjectAcceptanceId();
        } else {
            id = projProjectAcceptances.get(0).getProjectAcceptanceId();
        }
        // 查询是否已经存在了验收文件。当存在时 跳过
        List<ProjProjectAcceptanceRule> projProjectAcceptanceRules = projProjectAcceptanceRuleMapper.selectList(
                new QueryWrapper<ProjProjectAcceptanceRule>()
                        .eq("project_acceptance_id", id)
        );
        if (CollectionUtil.isEmpty(projProjectAcceptanceRules)) {
            for (OldAcceptFileDataDTO dto : oldAcceptFileDataDTOList) {
                ProjProjectAcceptanceRule projProjectAcceptanceRule = new ProjProjectAcceptanceRule();
                projProjectAcceptanceRule.setProjectAcceptanceRuleId(SnowFlakeUtil.getId());
                projProjectAcceptanceRule.setProjectInfoId(dto.getNewProjectInfoId());
                //文件类型处理  项目验收  ACCEPTANCE
                // 项目节点日期修正表 ACCEPTANCE_PROJECT_NODE 。 科室负责人及联系方式 ACCEPTANCE_DEPT_HEADER .
                // 暂缓验收医院审批附件  ACCEPTANCE_HOSPITAL_ANNEX
                // 老系统中 文件类型定义 文件类型 对应新系统文件
                // 1. 项目验收报告  ACCEPTANCE_REPORT 项目验收报告
                // 2.应用调查表   ACCEPTANCE_RESEACHE 应用调查表
                // 3. 验收自检表     ACCEPTANCE_SELF_CHECK   验收自检表
                // 4. 项目节点日期修正表
                // 5. 其他自定义附件
                // 6. 质管验收通过附件
                switch (dto.getFileType()) {
                    case 1:
                        //项目验收报告
                        projProjectAcceptanceRule.setProjectRuleCode("ACCEPTANCE_REPORT");
                        projProjectAcceptanceRule.setProjectRuleContent("项目验收报告");
                        projProjectAcceptanceRule.setVerityWay("upload");
                        projProjectAcceptanceRule.setRequiredFlag(1);
                        projProjectAcceptanceRule.setTemplateFlag(1);
                        projProjectAcceptanceRule.setTemplateFileCode("projectAcctpeReport");
                        projProjectAcceptanceRule.setOrderNo(1);
                        break;
                    case 2:
                        //应用调查表
                        projProjectAcceptanceRule.setProjectRuleCode("ACCEPTANCE_RESEACHE");
                        projProjectAcceptanceRule.setProjectRuleContent("应用调查表");
                        projProjectAcceptanceRule.setVerityWay("upload");
                        projProjectAcceptanceRule.setRequiredFlag(1);
                        projProjectAcceptanceRule.setTemplateFlag(1);
                        projProjectAcceptanceRule.setTemplateFileCode("searchReport");
                        projProjectAcceptanceRule.setOrderNo(2);
                        break;
                    case 3:
                        //验收自检表
                        projProjectAcceptanceRule.setProjectRuleCode("ACCEPTANCE_SELF_CHECK");
                        projProjectAcceptanceRule.setProjectRuleContent("验收自检表");
                        projProjectAcceptanceRule.setVerityWay("upload");
                        projProjectAcceptanceRule.setRequiredFlag(1);
                        projProjectAcceptanceRule.setTemplateFlag(1);
                        projProjectAcceptanceRule.setTemplateFileCode("selfCheckReport");
                        projProjectAcceptanceRule.setOrderNo(3);
                        break;
                    case 4:
                        // 项目节点日期修正表
                        projProjectAcceptanceRule.setProjectRuleCode("ACCEPTANCE_PROJECT_NODE");
                        projProjectAcceptanceRule.setProjectRuleContent("项目节点日期修正表");
                        projProjectAcceptanceRule.setVerityWay("upload");
                        projProjectAcceptanceRule.setRequiredFlag(1);
                        projProjectAcceptanceRule.setTemplateFlag(1);
                        projProjectAcceptanceRule.setTemplateFileCode("nodeDateFixReport");
                        projProjectAcceptanceRule.setOrderNo(4);
                        break;
                    case 5:
                        // 其他自定义附件
                        projProjectAcceptanceRule.setProjectRuleCode("OTHER");
                        projProjectAcceptanceRule.setProjectRuleContent("其他附件");
                        projProjectAcceptanceRule.setVerityWay("no");
                        projProjectAcceptanceRule.setRequiredFlag(1);
                        projProjectAcceptanceRule.setTemplateFlag(0);
                        projProjectAcceptanceRule.setTemplateFileCode("other");
                        projProjectAcceptanceRule.setOrderNo(7);
                        break;
                    case 6:
                        break;
                    default:
                        throw new RuntimeException("错误的文件类型，请检查");
                }
                projProjectAcceptanceRule.setProjectAcceptanceId(id);
                // 保存到项目文件表中
                Long projectFileId = saveObsFileDataForCsm(dto, id);
                projProjectAcceptanceRule.setProjectFileId(projectFileId);
                projProjectAcceptanceRuleMapper.insert(projProjectAcceptanceRule);
            }
        }

    }

    /**
     * 老系统文件存入obs中保存到新系统
     */
    Long saveObsFileDataForCsm(OldAcceptFileDataDTO dto, Long projectAcceptanceId) {
        log.info("图片地址 =========== , " + dto.getFileUrl() + " ========" + dto.getFileName());
        // 图片上传到obs上
        String objectKey =
                prePath + dto.getNewProjectInfoId() + StrUtil.SLASH + "old_project_acceptance" + StrUtil.SLASH
                        + dto.getFileName();
        // 转换Obs文件地址
        try {
            InputStream inputStream = new FileInputStream(new File(dto.getFileUrl()));
            PutObjectResult putObjectResult = OBSClientUtils.uploadFileStream(inputStream, objectKey, true);
            log.info("文件上传成功,文件地址==={},验收主表id==={}", putObjectResult.getObjectUrl(), projectAcceptanceId);
            // 接口文件存入 proj_file 表
            ProjProjectFile projProjectFile = new ProjProjectFile();
            projProjectFile.setProjectFileId(SnowFlakeUtil.getId());
            projProjectFile.setProjectInfoId(dto.getNewProjectInfoId());
            projProjectFile.setProjectStageCode("old_project_acceptance");
            projProjectFile.setMilestoneNodeCode("old_project_acceptance");
            projProjectFile.setFileName(dto.getFileName());
            projProjectFile.setFilePath(objectKey);
            projProjectFile.setFileDesc("");
            log.info("保存附件数据 ========, {}", JSONUtil.toJsonStr(projProjectFile));
            projectFileMapper.insert(projProjectFile);
            return projProjectFile.getProjectFileId();
        } catch (Exception e) {
            e.printStackTrace();
            log.info("图片上传失败, 验收主表id===={}, 错误明细信息 ==== {}", projectAcceptanceId, e);
            return -1L;
        }
    }
}
