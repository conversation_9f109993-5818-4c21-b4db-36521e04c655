package com.msun.csm.service.projtool;


import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.msun.core.component.implementation.api.androidout.dto.NoLoginIdentityQueryVO;
import com.msun.csm.common.model.ResponseData;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysConfig;
import com.msun.csm.dao.mapper.config.SysConfigMapper;
import com.msun.csm.model.req.projtool.AndroidOutToolReq;
import com.msun.csm.util.HttpClientUtils;
import com.obs.services.ObsClient;
import com.obs.services.model.ObsObject;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description 去球
 * @createDate 2024-09-14 15:15:49
 */
@Service
@Slf4j
public class AndroidOutApiServiceImpl implements AndroidOutApiService {

    @Lazy
    @Resource
    private SysConfigMapper sysConfigMapper;

    @Value("${AndroidOutApi.url}")
    private String androidoutapi;
    @Value("${chis.systemId}")
    private String systemId;
    @Value("${chis.secret}")
    private String secret;

    @Value("${proxy.networkOutUrl}")
    private String proxyNetworkUrl;



    /**
     * 下载
     *
     * @param androidOutToolReq
     * @param response
     */
    @Override
    public Result<String> downloadOfLinux(AndroidOutToolReq androidOutToolReq, HttpServletResponse response) {
        try {
            ObsClient obsClient1 = null;
            ObsObject obsObject = null;
            // 写活账号密码
            List<String> configCodes = new ArrayList<>();
            configCodes.add("androidAppObsAccount");
            List<SysConfig> limitCustomerSysConfig = sysConfigMapper.selectConfigByCodes(configCodes);
            String accessKey = null;
            String secretKey = null;
            if (limitCustomerSysConfig != null && limitCustomerSysConfig.size() > 0) {
                accessKey = limitCustomerSysConfig.get(0).getConfigName();
                secretKey = limitCustomerSysConfig.get(0).getConfigValue();
            }
            obsClient1 = new ObsClient(accessKey, secretKey, "obs.cn-north-4.myhuaweicloud.com");
            obsObject = obsClient1.getObject("chis-electron", "msun-guidance-callplatform/" + androidOutToolReq.getCloudHospitalId() + "/" + "app-debug.apk");
            InputStream input = obsObject.getObjectContent();
            //缓冲文件输出流
            BufferedOutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            //重置
            response.reset();
            //编码
            response.setCharacterEncoding("UTF-8");
            //文件名称
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("app-debug.apk", "UTF-8"));
            response.setContentType("application/octet-stream");
            //循环读取
            byte[] buff = new byte[1024];
            BufferedInputStream bis = null;
            OutputStream os = null;
            try {
                os = outputStream;
                bis = new BufferedInputStream(input);
                int i = 0;
                while ((i = bis.read(buff)) != -1) {
                    os.write(buff, 0, i);
                    os.flush();
                }
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                try {
                    assert bis != null;
                    bis.close();
                    os.close();
                } catch (IOException e) {
                    //log.error("缓冲流关闭异常！", e);
                    // e.printStackTrace();
                    log.error(e.getMessage());
                }
            }
            return Result.success("下载成功");
        } catch (Exception e) {
            log.error("====>" + e.getMessage());
            return Result.fail("下载失败,请确认已进行打包操作");
        }
    }

    /**
     * 打包
     * @param androidOutToolReq
     * @param response
     * @return
     */
    @Override
    public Result<String> reviseHospitalConfiguration(AndroidOutToolReq androidOutToolReq, HttpServletResponse response) {

        Map<String, Object> map = new HashMap<>();
        map.put("url", androidOutToolReq.getUrl().split("://")[1]);
        map.put("hospitalId", androidOutToolReq.getCloudHospitalId());
        map.put("electronId", androidOutToolReq.getCloudHospitalId());
        map.put("packAndroid", androidOutToolReq.getPackVersion());
        HttpClientUtils clientUtils = new HttpClientUtils();
        try {
            Map<String, String> param = new HashMap<>();
            //鉴权id
            param.put("systemId", systemId);
            //鉴权密钥
            param.put("secret", secret);
            NoLoginIdentityQueryVO noLoginIdentityQueryVO = new NoLoginIdentityQueryVO();
            noLoginIdentityQueryVO.setDeviceCode("tigc_login_common_code_2022");
            // noLoginIdentityQueryVO.setDeviceName("分诊H5公共屏幕");
            noLoginIdentityQueryVO.setHospitalId(androidOutToolReq.getCloudHospitalId());
            noLoginIdentityQueryVO.setOrgId(androidOutToolReq.getOrgId());
            noLoginIdentityQueryVO.setHisOrgId(androidOutToolReq.getOrgId());
            param.put("body", JSONObject.toJSONString(noLoginIdentityQueryVO));
            param.put("methodUrl", "/msun-middle-base-common/noSecretLogin/findByPage");
            param.put("address", proxyNetworkUrl);
            param.put("hisOrgId", androidOutToolReq.getOrgId().toString());
            param.put("hospitalId", androidOutToolReq.getCloudHospitalId().toString());
            //3.发送数据
            try {
                log.error("参数paramUser==========>" + param);
                String s = clientUtils.post(param);
                //打印基础中台返回的数据
                log.info("调用系统管理登陆接口返回信息: {}", s);
                ResponseData<Map<String, Object>> resultvalue = JSONObject.parseObject(s, ResponseData.class);
                if (resultvalue.getSuccess()) {
                    if (resultvalue.getData().size() != 0) {
                        List<Map<String, Object>> loginIdentityVOS = (List<Map<String, Object>>) resultvalue.getData().get("list");
                        if (loginIdentityVOS.size() != 0) {
                            map.put("loginId", loginIdentityVOS.get(0).get("loginId"));
                        } else {
                            return Result.fail("打包失败，未获取到登陆信息");
                        }
                    } else {
                        return Result.fail("打包失败，未获取到登陆信息");
                    }
                } else {
                    log.error("调用系统管理接口失败/msun-middle-base-common/noSecretLogin/findByPage");
                    log.info("错误信息: {}", s);
                    return Result.fail("打包失败，对接系统管理接口错误");
                }
            } catch (Exception e) {
                log.error("paramUser 打包失败，对接系统管理接口错误==========>" + e.getMessage());
                return Result.fail("打包失败，获取登陆信息出错");
            }
            param.put("body", JSONObject.toJSONString(map));
            param.put("address", androidoutapi);
            param.put("methodUrl", "/AndroidOutApi/reviseHospitalConfiguration");
            log.error("param::::::调用接口：：：：：" + param);
            Object responseObj = null;
            try {
                responseObj = clientUtils.post(param);
            } catch (Exception e) {
                log.info("==打包失败，对接打包接口错误====" + e.getMessage());
                return Result.fail("打包失败，对接打包接口错误");
            }
            String code = JSONObject.parseObject(responseObj.toString()).getString("code");
            if (code.equals("200")) {
                return Result.success("打包成功");
            } else {
                return Result.fail("打包失败");
            }
        } catch (Exception e) {
            log.error("====>" + e.getMessage());
            return Result.fail("打包失败");
        }
    }

    /**
     * 批量安装工具
     * @param androidOutToolReq
     * @param response
     * @return
     */
    @Override
    public Result<String> batchInstallationTool(AndroidOutToolReq androidOutToolReq, HttpServletResponse response) {
        List<String> configCodes = new ArrayList<>();
        configCodes.add("androidAppObsDownloadUrl");
        List<SysConfig> limitCustomerSysConfig = sysConfigMapper.selectConfigByCodes(configCodes);
        String secretKey = "https://chis-other.obs.cn-north-4.myhuaweicloud.com/imsp/%E5%AE%89%E5%8D%93%E7%BB%88%E7%AB%AF%E6%8E%A7%E5%88%B6%E7%AE%A1%E7%90%86%E5%99%A8_V1.0.4%281%29.rar";
        if (limitCustomerSysConfig != null && limitCustomerSysConfig.size() > 0) {
            secretKey = limitCustomerSysConfig.get(0).getConfigValue();
        }
        return Result.success(secretKey);
    }
}
