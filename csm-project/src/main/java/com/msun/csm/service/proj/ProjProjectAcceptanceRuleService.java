package com.msun.csm.service.proj;

import java.util.List;

import com.msun.csm.model.vo.ProjProjectAcceptanceRuleVO;

/**
 *
 */
public interface ProjProjectAcceptanceRuleService {


   /**
    * 获取申请项目信息附件信息
    * @param acceptanceRuleId
    * @return
    */
   List<ProjProjectAcceptanceRuleVO> getAcceptanceRulesByAcceptanceId(Long acceptanceRuleId);

   /**
    * 批量插入文件信息
    * @param rules
    * @return
    */
   int batchInsert(List<ProjProjectAcceptanceRuleVO> rules);

    /**
     * 批量更新文件信息
     * @param rules
     * @return
     */
    void batchUpdate(List<ProjProjectAcceptanceRuleVO> rules);
}
