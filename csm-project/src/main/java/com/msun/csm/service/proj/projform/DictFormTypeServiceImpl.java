package com.msun.csm.service.proj.projform;


import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.msun.csm.dao.entity.proj.projform.DictFormType;
import com.msun.csm.dao.mapper.projform.DictFormTypeMapper;

/**
 * <AUTHOR>
 * @description 针对表【dict_form_type(表单类型字典表)】的数据库操作Service实现
 * @createDate 2024-09-14 15:15:50
 */
@Service
public class DictFormTypeServiceImpl extends ServiceImpl<DictFormTypeMapper, DictFormType>
        implements DictFormTypeService {

}
