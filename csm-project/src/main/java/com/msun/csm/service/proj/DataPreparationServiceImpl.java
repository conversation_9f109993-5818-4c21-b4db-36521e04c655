package com.msun.csm.service.proj;

import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.enums.DataPrepareMenuEnum;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.dto.ExcelHeaderRowDTO;
import com.msun.csm.common.model.dto.ExcelSheetInfoDTO;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjProductDeliverRecord;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProductDeliverRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.model.dto.HisBaseDataInfoDTO;
import com.msun.csm.model.dto.HisBedCountDTO;
import com.msun.csm.model.dto.HisChargeCountDTO;
import com.msun.csm.model.dto.HisDepartmentCountDTO;
import com.msun.csm.model.dto.HisDrugCountDTO;
import com.msun.csm.model.dto.HisInpatientWardCountDTO;
import com.msun.csm.model.dto.HisMaterialsCountDTO;
import com.msun.csm.model.dto.HisPersonCountDTO;
import com.msun.csm.model.dto.InpatientEmrBaseDataInfoDTO;
import com.msun.csm.model.dto.InpatientMedicalOrderTemplateCountDataDTO;
import com.msun.csm.model.dto.InpatientMedicalRecordTemplateCountDataDTO;
import com.msun.csm.model.dto.LisBaseDataInfoDTO;
import com.msun.csm.model.dto.LisCheckedMedicalOrderCountDataDTO;
import com.msun.csm.model.dto.LisEquipmentCountDataDTO;
import com.msun.csm.model.dto.LisItemCountDataDTO;
import com.msun.csm.model.dto.LisLabItemCountDataDTO;
import com.msun.csm.model.dto.LisReportTypeCountDataDTO;
import com.msun.csm.model.dto.LisSampleClassCountDataDTO;
import com.msun.csm.model.dto.LisWorkGroupCountDataDTO;
import com.msun.csm.model.dto.OutpatientEmrBaseDataInfoDTO;
import com.msun.csm.model.dto.OutpatientMedicalRecordAndOrderTemplateCountDataDTO;
import com.msun.csm.model.dto.PacsBaseDataInfoDTO;
import com.msun.csm.model.dto.PacsBodyPartCountDataDTO;
import com.msun.csm.model.dto.PacsCheckedMedicalOrderBodyPartCountDataDTO;
import com.msun.csm.model.dto.PacsConsultingRoomCountDataDTO;
import com.msun.csm.model.dto.PacsEquipmentCountDataDTO;
import com.msun.csm.model.dto.PacsModalityCountDataDTO;
import com.msun.csm.model.dto.PacsStudyMethodCountDataDTO;
import com.msun.csm.model.dto.PacsUserCountDataDTO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.param.ProjectInfoIdParam;
import com.msun.csm.model.vo.DataPrepareMenuVO;
import com.msun.csm.service.common.CommonSearchCloudDbService;
import com.msun.csm.util.ExcelUtil;

import lombok.extern.slf4j.Slf4j;


@Slf4j
@Service
public class DataPreparationServiceImpl implements DataPreparationService {

    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;

    @Resource
    private CommonSearchCloudDbService commonSearchCloudDbService;

    @Resource
    private ProjProjectInfoMapper projProjectInfoMapper;

    @Resource
    private ProjProductDeliverRecordMapper productDeliverRecordMapper;

    /**
     * 获取数据准备菜单列表
     * [由老换新公卫数据导入节点引发（原为 只要是区域就进行展示），返回菜单列表  张吉宝]
     *
     * @param projectInfoId
     * @return
     */
    @Override
    public Result<List<DataPrepareMenuVO>> getDataPrepareMenuList(Long projectInfoId) {
        List<DataPrepareMenuVO> voList = new ArrayList<>();
        // 项目类型获取
        ProjProjectInfo projProjectInfo = projProjectInfoMapper.selectById(projectInfoId);
        // 单体区域获取
        // 实施产品获取
        List<ProjProductDeliverRecord> projProductDeliverRecords = productDeliverRecordMapper.selectList(
                new QueryWrapper<ProjProductDeliverRecord>()
                        .eq("project_info_id", projectInfoId)
        );
        // 判断是否包含公卫产品 （5063 (云)公卫系统(200张以下)； 5969 (云)公卫系统(200张以上)）
        boolean isPublicHealth =
                projProductDeliverRecords.stream().anyMatch(item -> item.getProductDeliverId().equals(5969L) || item.getProductDeliverId().equals(5063L));
        if (projProjectInfo.getUpgradationType() == 1) {
            DataPrepareMenuVO dataPrepareMenuVO = new DataPrepareMenuVO();
            dataPrepareMenuVO.setTitle(DataPrepareMenuEnum.OldSysDataQualityDetect.getMenuName());
            dataPrepareMenuVO.setName(DataPrepareMenuEnum.OldSysDataQualityDetect.getMenuCode());
            voList.add(dataPrepareMenuVO);
            DataPrepareMenuVO dataPrepareMenuVO1 = new DataPrepareMenuVO();
            dataPrepareMenuVO1.setTitle(DataPrepareMenuEnum.OldSysVersionDetect.getMenuName());
            dataPrepareMenuVO1.setName(DataPrepareMenuEnum.OldSysVersionDetect.getMenuCode());
            voList.add(dataPrepareMenuVO1);
            DataPrepareMenuVO dataPrepareMenuVO2 = new DataPrepareMenuVO();
            dataPrepareMenuVO2.setTitle(DataPrepareMenuEnum.DictCompareToolsConfig.getMenuName());
            dataPrepareMenuVO2.setName(DataPrepareMenuEnum.DictCompareToolsConfig.getMenuCode());
            voList.add(dataPrepareMenuVO2);
            if (isPublicHealth) {
                DataPrepareMenuVO dataPrepareMenuVO3 = new DataPrepareMenuVO();
                dataPrepareMenuVO3.setTitle(DataPrepareMenuEnum.PublicHealthDataImport.getMenuName());
                dataPrepareMenuVO3.setName(DataPrepareMenuEnum.PublicHealthDataImport.getMenuCode());
                voList.add(dataPrepareMenuVO3);
            }
        } else {
            DataPrepareMenuVO dataPrepareMenuVO4 = new DataPrepareMenuVO();
            dataPrepareMenuVO4.setTitle(DataPrepareMenuEnum.NewOnlineBasicDataImport.getMenuName());
            dataPrepareMenuVO4.setName(DataPrepareMenuEnum.NewOnlineBasicDataImport.getMenuCode());
            voList.add(dataPrepareMenuVO4);
        }
        return Result.success(voList);
    }

    @Override
    public List<HisBaseDataInfoDTO> getHisData(ProjectInfoIdParam param) {
        List<ProjHospitalInfo> projHospitalInfoList = this.getProjHospitalInfoList(param);
        if (CollectionUtils.isEmpty(projHospitalInfoList)) {
            return new ArrayList<>();
        }
        ProjHospitalInfo projHospitalInfo = projHospitalInfoList.get(0);
        // 科室
        List<HisDepartmentCountDTO> departmentCountData = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "DepartmentCountData", HisDepartmentCountDTO.class);
        // 人员
        List<HisPersonCountDTO> personCountData = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "PersonCountData", HisPersonCountDTO.class);
        // 病区
        List<HisInpatientWardCountDTO> inpatientWardCountData = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "InpatientWardCountData", HisInpatientWardCountDTO.class);
        // 床位
        List<HisBedCountDTO> bedCountData = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "BedCountData", HisBedCountDTO.class);
        // 计价
        List<HisChargeCountDTO> chargeCountData = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "ChargeCountData", HisChargeCountDTO.class);
        // 药品
        List<HisDrugCountDTO> drugCountData = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "DrugCountData", HisDrugCountDTO.class);
        // 材料
        List<HisMaterialsCountDTO> materialsCountData = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "MaterialsCountData", HisMaterialsCountDTO.class);
        // 返回结果
        List<HisBaseDataInfoDTO> list = new ArrayList<>();
        for (ProjHospitalInfo hospitalInfoItem : projHospitalInfoList) {
            HisDepartmentCountDTO hisDepartmentCountDTO = departmentCountData.stream().filter(item -> String.valueOf(hospitalInfoItem.getCloudHospitalId()).equals(item.getHospitalId())).findFirst().orElse(null);
            HisPersonCountDTO personCountDTO = personCountData.stream().filter(item -> String.valueOf(hospitalInfoItem.getCloudHospitalId()).equals(item.getHospitalId())).findFirst().orElse(null);
            HisInpatientWardCountDTO hisInpatientWardCountDTO = inpatientWardCountData.stream().filter(item -> String.valueOf(hospitalInfoItem.getCloudHospitalId()).equals(item.getHospitalId())).findFirst().orElse(null);
            HisBedCountDTO bedCountDTO = bedCountData.stream().filter(item -> String.valueOf(hospitalInfoItem.getCloudHospitalId()).equals(item.getHospitalId())).findFirst().orElse(null);
            HisChargeCountDTO chargeCountDTO = chargeCountData.stream().filter(item -> String.valueOf(hospitalInfoItem.getCloudHospitalId()).equals(item.getHospitalId())).findFirst().orElse(null);
            HisDrugCountDTO drugCountDTO = drugCountData.stream().filter(item -> String.valueOf(hospitalInfoItem.getCloudHospitalId()).equals(item.getHospitalId())).findFirst().orElse(null);
            HisMaterialsCountDTO materialsCountDTO = materialsCountData.stream().filter(item -> String.valueOf(hospitalInfoItem.getCloudHospitalId()).equals(item.getHospitalId())).findFirst().orElse(null);
            HisBaseDataInfoDTO hisBaseDataInfoDTO = HisBaseDataInfoDTO
                    .builder()
                    .hospitalInfoId(String.valueOf(hospitalInfoItem.getHospitalInfoId()))
                    .hospitalName(hospitalInfoItem.getHospitalName())
                    .departmentCount(hisDepartmentCountDTO != null ? hisDepartmentCountDTO.getAllDepartmentCount() : "0")
                    .allPersonCount(personCountDTO != null ? personCountDTO.getAllPersonCount() : "0")
                    .unassignedRoleCount(personCountDTO != null ? personCountDTO.getUnassignedRoleCount() : "0")
                    .inpatientWardCount(hisInpatientWardCountDTO != null ? hisInpatientWardCountDTO.getInpatientWardCount() : "0")
                    .allBedCount(bedCountDTO != null ? bedCountDTO.getAllBedCount() : "0")
                    .noChargesBedCount(bedCountDTO != null ? bedCountDTO.getNoChargesBedCount() : "0")
                    .allChargeCount(chargeCountDTO != null ? chargeCountDTO.getAllChargeCount() : "0")
                    .allDrugCount(drugCountDTO != null ? drugCountDTO.getAllDrugCount() : "0")
                    .checkedMedicalInsuranceDrugCount(drugCountDTO != null ? drugCountDTO.getCheckedMedicalInsuranceDrugCount() : "0")
                    .antibioticsDrugCount(drugCountDTO != null ? drugCountDTO.getAntibioticsDrugCount() : "0")
                    .allMaterialsCount(materialsCountDTO != null ? materialsCountDTO.getAllMaterialsCount() : "0")
                    .checkedMedicalInsuranceMaterialsCount(materialsCountDTO != null ? materialsCountDTO.getCheckedMedicalInsuranceMaterialsCount() : "0")
                    .electronicSignatureCount(personCountDTO != null && personCountDTO.getElectronicSignatureCount() != null ? personCountDTO.getElectronicSignatureCount() : "0")
                    .build();
            list.add(hisBaseDataInfoDTO);
        }
        return list;
    }


    @Override
    public List<LisBaseDataInfoDTO> getLisData(ProjectInfoIdParam param) {
        List<ProjHospitalInfo> projHospitalInfoList = this.getProjHospitalInfoList(param);
        if (CollectionUtils.isEmpty(projHospitalInfoList)) {
            return new ArrayList<>();
        }
        ProjHospitalInfo projHospitalInfo = projHospitalInfoList.get(0);
        // 工作组
        List<LisWorkGroupCountDataDTO> lisWorkGroupCountData = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "LisWorkGroupCountData", LisWorkGroupCountDataDTO.class);
        // 样本类型
        List<LisSampleClassCountDataDTO> lisSampleClassCountData = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "LisSampleClassCountData", LisSampleClassCountDataDTO.class);
        // 报告单类型
        List<LisReportTypeCountDataDTO> lisReportTypeCountData = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "LisReportTypeCountData", LisReportTypeCountDataDTO.class);
        // 明细项目
        List<LisItemCountDataDTO> lisItemCountData = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "LisItemCountData", LisItemCountDataDTO.class);
        // 组合项目
        List<LisLabItemCountDataDTO> lisLabItemCountData = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "LisLabItemCountData", LisLabItemCountDataDTO.class);
        // 设备维护
        List<LisEquipmentCountDataDTO> lisEquipmentCountData = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "LisEquipmentCountData", LisEquipmentCountDataDTO.class);
        // 医嘱对照组合项目
        List<LisCheckedMedicalOrderCountDataDTO> lisCheckedMedicalOrderCountData = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "LisCheckedMedicalOrderCountData", LisCheckedMedicalOrderCountDataDTO.class);
        List<LisBaseDataInfoDTO> list = new ArrayList<>();
        for (ProjHospitalInfo hospitalInfoItem : projHospitalInfoList) {
            LisWorkGroupCountDataDTO lisWorkGroupCountDataDTO = lisWorkGroupCountData.stream().filter(item -> String.valueOf(hospitalInfoItem.getCloudHospitalId()).equals(item.getHospitalId())).findFirst().orElse(null);
            LisSampleClassCountDataDTO lisSampleClassCountDataDTO = lisSampleClassCountData.stream().filter(item -> String.valueOf(hospitalInfoItem.getCloudHospitalId()).equals(item.getHospitalId())).findFirst().orElse(null);
            LisReportTypeCountDataDTO lisReportTypeCountDataDTO = lisReportTypeCountData.stream().filter(item -> String.valueOf(hospitalInfoItem.getCloudHospitalId()).equals(item.getHospitalId())).findFirst().orElse(null);
            LisItemCountDataDTO lisItemCountDataDTO = lisItemCountData.stream().filter(item -> String.valueOf(hospitalInfoItem.getCloudHospitalId()).equals(item.getHospitalId())).findFirst().orElse(null);
            LisLabItemCountDataDTO lisLabItemCountDataDTO = lisLabItemCountData.stream().filter(item -> String.valueOf(hospitalInfoItem.getCloudHospitalId()).equals(item.getHospitalId())).findFirst().orElse(null);
            LisEquipmentCountDataDTO lisEquipmentCountDataDTO = lisEquipmentCountData.stream().filter(item -> String.valueOf(hospitalInfoItem.getCloudHospitalId()).equals(item.getHospitalId())).findFirst().orElse(null);
            LisCheckedMedicalOrderCountDataDTO lisCheckedMedicalOrderCountDataDTO = lisCheckedMedicalOrderCountData.stream().filter(item -> String.valueOf(hospitalInfoItem.getCloudHospitalId()).equals(item.getHospitalId())).findFirst().orElse(null);
            LisBaseDataInfoDTO hisBaseDataInfoDTO = LisBaseDataInfoDTO
                    .builder()
                    .hospitalInfoId(String.valueOf(hospitalInfoItem.getHospitalInfoId()))
                    .hospitalName(hospitalInfoItem.getHospitalName())
                    .lisWorkGroupCount(lisWorkGroupCountDataDTO != null ? lisWorkGroupCountDataDTO.getTotal() : "0")
                    .lisSampleClassCount(lisSampleClassCountDataDTO != null ? lisSampleClassCountDataDTO.getTotal() : "0")
                    .lisReportTypeCount(lisReportTypeCountDataDTO != null ? lisReportTypeCountDataDTO.getTotal() : "0")
                    .lisItemCount(lisItemCountDataDTO != null ? lisItemCountDataDTO.getTotal() : "0")
                    .lisLabItemCount(lisLabItemCountDataDTO != null ? lisLabItemCountDataDTO.getTotal() : "0")
                    .lisEquipmentCount(lisEquipmentCountDataDTO != null ? lisEquipmentCountDataDTO.getTotal() : "0")
                    .lisCheckedMedicalOrderCount(lisCheckedMedicalOrderCountDataDTO != null ? lisCheckedMedicalOrderCountDataDTO.getTotal() : "0")
                    .build();
            list.add(hisBaseDataInfoDTO);
        }
        return list;
    }

    @Override
    public List<PacsBaseDataInfoDTO> getPacsData(ProjectInfoIdParam param) {
        List<ProjHospitalInfo> projHospitalInfoList = this.getProjHospitalInfoList(param);
        if (CollectionUtils.isEmpty(projHospitalInfoList)) {
            return new ArrayList<>();
        }
        ProjHospitalInfo projHospitalInfo = projHospitalInfoList.get(0);
        // 人员
        List<PacsUserCountDataDTO> pacsUserCountData = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "PacsUserCountData", PacsUserCountDataDTO.class);
        // 诊室
        List<PacsConsultingRoomCountDataDTO> pacsConsultingRoomCountData = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "PacsConsultingRoomCountData", PacsConsultingRoomCountDataDTO.class);
        // 设备
        List<PacsEquipmentCountDataDTO> pacsEquipmentCountData = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "PacsEquipmentCountData", PacsEquipmentCountDataDTO.class);
        // 模态
        List<PacsModalityCountDataDTO> pacsModalityCountData = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "PacsModalityCountData", PacsModalityCountDataDTO.class);
        // 检查方法
        List<PacsStudyMethodCountDataDTO> pacsStudyMethodCountData = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "PacsStudyMethodCountData", PacsStudyMethodCountDataDTO.class);
        // 检查部位
        List<PacsBodyPartCountDataDTO> pacsBodyPartCountData = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "PacsBodyPartCountData", PacsBodyPartCountDataDTO.class);
        // 对照医嘱部位
        List<PacsCheckedMedicalOrderBodyPartCountDataDTO> pacsCheckedMedicalOrderBodyPartCountData = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "PacsCheckedMedicalOrderBodyPartCountData", PacsCheckedMedicalOrderBodyPartCountDataDTO.class);
        List<PacsBaseDataInfoDTO> list = new ArrayList<>();
        for (ProjHospitalInfo hospitalInfoItem : projHospitalInfoList) {
            PacsUserCountDataDTO pacsUserCountDataDTO = pacsUserCountData.stream().filter(item -> String.valueOf(hospitalInfoItem.getCloudHospitalId()).equals(item.getHospitalId())).findFirst().orElse(null);
            PacsConsultingRoomCountDataDTO pacsConsultingRoomCountDataDTO = pacsConsultingRoomCountData.stream().filter(item -> String.valueOf(hospitalInfoItem.getCloudHospitalId()).equals(item.getHospitalId())).findFirst().orElse(null);
            PacsEquipmentCountDataDTO pacsEquipmentCountDataDTO = pacsEquipmentCountData.stream().filter(item -> String.valueOf(hospitalInfoItem.getCloudHospitalId()).equals(item.getHospitalId())).findFirst().orElse(null);
            PacsModalityCountDataDTO pacsModalityCountDataDTO = pacsModalityCountData.stream().filter(item -> String.valueOf(hospitalInfoItem.getCloudHospitalId()).equals(item.getHospitalId())).findFirst().orElse(null);
            PacsStudyMethodCountDataDTO pacsStudyMethodCountDataDTO = pacsStudyMethodCountData.stream().filter(item -> String.valueOf(hospitalInfoItem.getCloudHospitalId()).equals(item.getHospitalId())).findFirst().orElse(null);
            PacsBodyPartCountDataDTO pacsBodyPartCountDataDTO = pacsBodyPartCountData.stream().filter(item -> String.valueOf(hospitalInfoItem.getCloudHospitalId()).equals(item.getHospitalId())).findFirst().orElse(null);
            PacsCheckedMedicalOrderBodyPartCountDataDTO pacsCheckedMedicalOrderBodyPartCountDataDTO = pacsCheckedMedicalOrderBodyPartCountData.stream().filter(item -> String.valueOf(hospitalInfoItem.getCloudHospitalId()).equals(item.getHospitalId())).findFirst().orElse(null);
            PacsBaseDataInfoDTO hisBaseDataInfoDTO = PacsBaseDataInfoDTO
                    .builder()
                    .hospitalInfoId(String.valueOf(hospitalInfoItem.getHospitalInfoId()))
                    .hospitalName(hospitalInfoItem.getHospitalName())
                    .pacsUser(pacsUserCountDataDTO != null ? pacsUserCountDataDTO.getUserCount() : "0")
                    .pacsConsultingRoom(pacsConsultingRoomCountDataDTO != null ? pacsConsultingRoomCountDataDTO.getTotal() : "0")
                    .pacsEquipment(pacsEquipmentCountDataDTO != null ? pacsEquipmentCountDataDTO.getTotal() : "0")
                    .pacsModality(pacsModalityCountDataDTO != null ? pacsModalityCountDataDTO.getTotal() : "0")
                    .pacsStudyMethod(pacsStudyMethodCountDataDTO != null ? pacsStudyMethodCountDataDTO.getTotal() : "0")
                    .pacsBodyPart(pacsBodyPartCountDataDTO != null ? pacsBodyPartCountDataDTO.getTotal() : "0")
                    .pacsBodyPartCheckedOrder(pacsCheckedMedicalOrderBodyPartCountDataDTO != null ? pacsCheckedMedicalOrderBodyPartCountDataDTO.getTotal() : "0")
                    .build();
            list.add(hisBaseDataInfoDTO);
        }
        return list;
    }

    @Override
    public List<OutpatientEmrBaseDataInfoDTO> getOutpatientEmrData(ProjectInfoIdParam param) {
        List<ProjHospitalInfo> projHospitalInfoList = this.getProjHospitalInfoList(param);
        if (CollectionUtils.isEmpty(projHospitalInfoList)) {
            return new ArrayList<>();
        }
        ProjHospitalInfo projHospitalInfo = projHospitalInfoList.get(0);
        // 科室级病历模板、医嘱模板
        List<OutpatientMedicalRecordAndOrderTemplateCountDataDTO> outpatientMedicalRecordAndMedicalOrderTemplateCountData = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "OutpatientMedicalRecordAndMedicalOrderTemplateCountData", OutpatientMedicalRecordAndOrderTemplateCountDataDTO.class);
        List<OutpatientEmrBaseDataInfoDTO> list = new ArrayList<>();
        for (ProjHospitalInfo hospitalInfoItem : projHospitalInfoList) {
            OutpatientMedicalRecordAndOrderTemplateCountDataDTO dataDTO = outpatientMedicalRecordAndMedicalOrderTemplateCountData.stream().filter(item -> String.valueOf(hospitalInfoItem.getCloudHospitalId()).equals(item.getHospitalId())).findFirst().orElse(null);
            OutpatientEmrBaseDataInfoDTO hisBaseDataInfoDTO = OutpatientEmrBaseDataInfoDTO
                    .builder()
                    .hospitalInfoId(String.valueOf(hospitalInfoItem.getHospitalInfoId()))
                    .hospitalName(hospitalInfoItem.getHospitalName())
                    .medicalRecordCount(dataDTO != null ? dataDTO.getMedicalRecordCount() : "0")
                    .medicalOrderCount(dataDTO != null ? dataDTO.getMedicalOrderCount() : "0")
                    .build();
            list.add(hisBaseDataInfoDTO);
        }
        return list;
    }

    @Override
    public List<InpatientEmrBaseDataInfoDTO> getInpatientEmrData(ProjectInfoIdParam param) {
        List<ProjHospitalInfo> projHospitalInfoList = this.getProjHospitalInfoList(param);
        if (CollectionUtils.isEmpty(projHospitalInfoList)) {
            return new ArrayList<>();
        }
        ProjHospitalInfo projHospitalInfo = projHospitalInfoList.get(0);
        // 科室级病历模板
        List<InpatientMedicalRecordTemplateCountDataDTO> inpatientMedicalRecordTemplateCountData = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "InpatientMedicalRecordTemplateCountData", InpatientMedicalRecordTemplateCountDataDTO.class);
        // 医嘱模板
        List<InpatientMedicalOrderTemplateCountDataDTO> inpatientMedicalOrderTemplateCountData = commonSearchCloudDbService.commonSearchCloudDb(projHospitalInfo, "InpatientMedicalOrderTemplateCountData", InpatientMedicalOrderTemplateCountDataDTO.class);
        List<InpatientEmrBaseDataInfoDTO> list = new ArrayList<>();
        for (ProjHospitalInfo hospitalInfoItem : projHospitalInfoList) {
            InpatientMedicalRecordTemplateCountDataDTO inpatientMedicalRecordTemplateCountDataDTO = inpatientMedicalRecordTemplateCountData.stream().filter(item -> String.valueOf(hospitalInfoItem.getCloudHospitalId()).equals(item.getHospitalId())).findFirst().orElse(null);
            InpatientMedicalOrderTemplateCountDataDTO inpatientMedicalOrderTemplateCountDataDTO = inpatientMedicalOrderTemplateCountData.stream().filter(item -> String.valueOf(hospitalInfoItem.getCloudHospitalId()).equals(item.getHospitalId())).findFirst().orElse(null);
            InpatientEmrBaseDataInfoDTO hisBaseDataInfoDTO = InpatientEmrBaseDataInfoDTO
                    .builder()
                    .hospitalInfoId(String.valueOf(hospitalInfoItem.getHospitalInfoId()))
                    .hospitalName(hospitalInfoItem.getHospitalName())
                    .medicalRecordTemplateCount(inpatientMedicalRecordTemplateCountDataDTO != null ? inpatientMedicalRecordTemplateCountDataDTO.getMedicalRecordTemplateCount() : "0")
                    .medicalOrderTemplateCount(inpatientMedicalOrderTemplateCountDataDTO != null ? inpatientMedicalOrderTemplateCountDataDTO.getMedicalOrderTemplateCount() : "0")
                    .build();
            list.add(hisBaseDataInfoDTO);
        }
        return list;
    }

    private List<ProjHospitalInfo> getProjHospitalInfoList(ProjectInfoIdParam param) {
        if (null != param.getHospitalInfoId()) {
            List<ProjHospitalInfo> list = new ArrayList<>();
            QueryWrapper<ProjHospitalInfo> queryWrapper = new QueryWrapper<ProjHospitalInfo>()
                    .eq("hospital_info_id", param.getHospitalInfoId());
            ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectOne(queryWrapper);
            if (null == hospitalInfo) {
                return new ArrayList<>();
            }
            list.add(hospitalInfo);
            return list;
        }
        // 查询当前项目下的医院信息
        SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
        selectHospitalDTO.setProjectInfoId(param.getProjectInfoId());
        // 查询当前项目下的医院信息(医院名称、终端数)
        return projHospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);

    }

    @Override
    public void exportDataExcel(String type, ProjectInfoIdParam param, OutputStream outputStream) {
        List<ExcelSheetInfoDTO> list = new ArrayList<>();
        if ("all".equals(type) || "his".equals(type)) {
            List<HisBaseDataInfoDTO> hisData = getHisData(param);
            ExcelSheetInfoDTO<HisBaseDataInfoDTO> hisDataSheet = new ExcelSheetInfoDTO<>();
            hisDataSheet.setSheetName("HIS基础数据");
            hisDataSheet.setSheetData(hisData);
            ExcelHeaderRowDTO[] headerRow1 = {
                    new ExcelHeaderRowDTO("医院名称", "hospitalName"),
                    new ExcelHeaderRowDTO("科室", "departmentCount"),
                    new ExcelHeaderRowDTO("人员", "allPersonCount"),
                    new ExcelHeaderRowDTO("未分配角色的人员数量", "unassignedRoleCount"),
                    new ExcelHeaderRowDTO("病区", "inpatientWardCount"),
                    new ExcelHeaderRowDTO("已维护床位总数", "allBedCount"),
                    new ExcelHeaderRowDTO("未维护床位费的床位数量", "noChargesBedCount"),
                    new ExcelHeaderRowDTO("计价", "allChargeCount"),
                    new ExcelHeaderRowDTO("已维护药品总数", "allDrugCount"),
                    new ExcelHeaderRowDTO("已对照医保贯标码药品数量", "checkedMedicalInsuranceDrugCount"),
                    new ExcelHeaderRowDTO("抗菌药物数量", "antibioticsDrugCount"),
                    new ExcelHeaderRowDTO("已维护材料总数", "allMaterialsCount"),
                    new ExcelHeaderRowDTO("已对照医保贯标码材料数量", "checkedMedicalInsuranceMaterialsCount"),
            };
            hisDataSheet.setHeaderRowArray(headerRow1);
            list.add(hisDataSheet);
        }
        if ("all".equals(type) || "lis".equals(type)) {
            List<LisBaseDataInfoDTO> lisData = getLisData(param);
            ExcelSheetInfoDTO<LisBaseDataInfoDTO> lisDataSheet = new ExcelSheetInfoDTO<>();
            lisDataSheet.setSheetName("LIS基础数据");
            lisDataSheet.setSheetData(lisData);
            ExcelHeaderRowDTO[] headerRow2 = {
                    new ExcelHeaderRowDTO("医院名称", "hospitalName"),
                    new ExcelHeaderRowDTO("工作组", "lisWorkGroupCount"),
                    new ExcelHeaderRowDTO("样本类型", "lisSampleClassCount"),
                    new ExcelHeaderRowDTO("报告单类型", "lisReportTypeCount"),
                    new ExcelHeaderRowDTO("明细项目", "lisItemCount"),
                    new ExcelHeaderRowDTO("组合项目", "lisLabItemCount"),
                    new ExcelHeaderRowDTO("设备维护", "lisEquipmentCount"),
                    new ExcelHeaderRowDTO("已对照医嘱组合项目", "lisCheckedMedicalOrderCount"),
            };
            lisDataSheet.setHeaderRowArray(headerRow2);
            list.add(lisDataSheet);
        }
        if ("all".equals(type) || "pacs".equals(type)) {
            List<PacsBaseDataInfoDTO> pacsData = getPacsData(param);
            ExcelSheetInfoDTO<PacsBaseDataInfoDTO> pacsDataSheet = new ExcelSheetInfoDTO<>();
            pacsDataSheet.setSheetName("PACS基础数据");
            pacsDataSheet.setSheetData(pacsData);
            ExcelHeaderRowDTO[] headerRow3 = {
                    new ExcelHeaderRowDTO("医院名称", "hospitalName"),
                    new ExcelHeaderRowDTO("人员", "pacsUser"),
                    new ExcelHeaderRowDTO("诊室", "pacsConsultingRoom"),
                    new ExcelHeaderRowDTO("设备", "pacsEquipment"),
                    new ExcelHeaderRowDTO("模态", "pacsModality"),
                    new ExcelHeaderRowDTO("检查方法", "pacsStudyMethod"),
                    new ExcelHeaderRowDTO("检查部位", "pacsBodyPart"),
                    new ExcelHeaderRowDTO("已对照医嘱部位", "pacsBodyPartCheckedOrder"),
            };
            pacsDataSheet.setHeaderRowArray(headerRow3);
            list.add(pacsDataSheet);
        }
        if ("all".equals(type) || "outpatient".equals(type)) {
            List<OutpatientEmrBaseDataInfoDTO> outpatientEmrData = getOutpatientEmrData(param);
            ExcelSheetInfoDTO<OutpatientEmrBaseDataInfoDTO> outpatientEmrDataSheet = new ExcelSheetInfoDTO<>();
            outpatientEmrDataSheet.setSheetName("门诊电子病历基础数据");
            outpatientEmrDataSheet.setSheetData(outpatientEmrData);
            ExcelHeaderRowDTO[] headerRow4 = {
                    new ExcelHeaderRowDTO("医院名称", "hospitalName"),
                    new ExcelHeaderRowDTO("门诊科室级病历模板维护数量", "medicalRecordCount"),
                    new ExcelHeaderRowDTO("门诊医嘱模板维护数量", "medicalOrderCount"),
            };
            outpatientEmrDataSheet.setHeaderRowArray(headerRow4);
            list.add(outpatientEmrDataSheet);
        }
        if ("all".equals(type) || "inpatient".equals(type)) {
            List<InpatientEmrBaseDataInfoDTO> inpatientEmrData = getInpatientEmrData(param);
            ExcelSheetInfoDTO<InpatientEmrBaseDataInfoDTO> inpatientEmrDataSheet = new ExcelSheetInfoDTO<>();
            inpatientEmrDataSheet.setSheetName("住院电子病历基础数据");
            inpatientEmrDataSheet.setSheetData(inpatientEmrData);
            ExcelHeaderRowDTO[] headerRow5 = {
                    new ExcelHeaderRowDTO("医院名称", "hospitalName"),
                    new ExcelHeaderRowDTO("住院科室级病历模板维护数量", "medicalRecordTemplateCount"),
                    new ExcelHeaderRowDTO("住院医嘱模板维护数量", "medicalOrderTemplateCount"),
            };
            inpatientEmrDataSheet.setHeaderRowArray(headerRow5);
            list.add(inpatientEmrDataSheet);
        }
        ExcelUtil.exportExcelBySheets(list, outputStream);

    }


}
