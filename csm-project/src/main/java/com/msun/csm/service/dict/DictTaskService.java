package com.msun.csm.service.dict;

import java.util.List;

import com.msun.csm.dao.entity.dict.DictTask;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/25
 */

public interface DictTaskService {

    int deleteByPrimaryKey(Long id);

    int insert(DictTask record);

    int insertOrUpdate(DictTask record);

    int insertOrUpdateSelective(DictTask record);

    int insertSelective(DictTask record);

    DictTask selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DictTask record);

    int updateByPrimaryKey(DictTask record);

    int updateBatch(List<DictTask> list);

    int updateBatchSelective(List<DictTask> list);

    int batchInsert(List<DictTask> list);
}
