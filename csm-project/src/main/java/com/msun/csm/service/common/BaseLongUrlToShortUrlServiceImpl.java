package com.msun.csm.service.common;

import java.net.URI;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import com.msun.core.component.implementation.api.deviceanaysis.dto.ResponseData;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.model.csm.LongUrlToShortUrlDTO;
import com.msun.csm.model.imsp.CustomerAndHospitalNotOnlineDataVO;
import com.msun.csm.model.imsp.CustomerNotOnlineVO;
import com.msun.csm.model.imsp.HospitalNotOnlineVO;
import com.msun.csm.model.statis.ProjCsmHostReq;
import com.msun.csm.model.statis.ProjCustomReq;
import com.msun.csm.util.RedisUtil;
import com.msun.csm.util.SnowFlakeUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description:
 * @Author: zd
 * @Date: 2024/10/10
 */
@Service
@Slf4j
public class BaseLongUrlToShortUrlServiceImpl implements BaseLongUrlToShortUrlService {

    private static final String BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    private static final String REDIS_KEY_PREFIX = "short_url:";
    @Value("${thisProjectUrl}")
    private String urlRefix;
    @Resource
    private RedisUtil redisUtil;

    @Resource
    @Lazy
    private ProjCustomInfoMapper projCustomInfoMapper;

    // 将自增ID转换为Base62字符串
    public static String encode(long num) {
        StringBuilder sb = new StringBuilder();
        while (num > 0) {
            sb.insert(0, BASE62.charAt((int) (num % 62)));
            num /= 62;
        }
        return sb.toString();
    }

    /**
     * @param dto
     * @return
     */
    @Override
    public Result<String> longUrlToShortUrlFunction(LongUrlToShortUrlDTO dto) {
        String shortCode = getShortCode();
        // 默认超时时间为1分钟
        long timeout = dto.getTimeout() == null ? 1 : dto.getTimeout();
        redisUtil.set(REDIS_KEY_PREFIX + shortCode, dto.getLongUrl(), timeout, TimeUnit.MINUTES);
        return Result.success(urlRefix + "/csmOutApi/getLongUrl?shortCode=" + shortCode);
    }

    /**
     * 获取短链接
     *
     * @return
     */
    private String getShortCode() {
        // 生成唯一的短链接,最大循环10次
        return generateUniqueShortCode(10);
    }

    /**
     * 生成唯一的短链接
     *
     * @param maxRetries
     * @return
     */
    private String generateUniqueShortCode(int maxRetries) {
        if (maxRetries <= 0) {
            throw new RuntimeException("Failed to generate unique short code after maximum retries");
        }
        try {
            Long id = SnowFlakeUtil.getId();
            String shortCode = encode(id);
            if (redisUtil.hasKey(REDIS_KEY_PREFIX + shortCode)) {
                return generateUniqueShortCode(maxRetries - 1); // 递归重新生成短链接
            }
            return shortCode;
        } catch (Exception e) {
            // 记录异常日志
            log.error("Error generating short code", e);
            return generateUniqueShortCode(maxRetries - 1); // 递归重新生成短链接
        }
    }

    /**
     * @param shortCode
     * @return
     */
    @Override
    public ResponseEntity<Void> getLongUrl(String shortCode) {
        if (redisUtil.hasKey(REDIS_KEY_PREFIX + shortCode)) {
            String longUrl = redisUtil.get(REDIS_KEY_PREFIX + shortCode).toString();
            return ResponseEntity.status(HttpStatus.FOUND).location(URI.create(longUrl)).build();
        }
        return ResponseEntity.notFound().build();
    }

    /**
     * 获取交付平台测试环境域名或正式环境域名
     *
     * @param dto
     * @return
     */
    @Override
    public ResponseData getCsmHost(ProjCsmHostReq dto) {
        Map<String, String> map = new HashMap<>();
        String host = urlRefix.replace("/csm", "");
        map.put("host", host);
        if (dto.getBusinessType() == null || (dto.getBusinessType() != null && "app".equals(dto.getBusinessType()) || "".equals(dto.getBusinessType()))) {
            map.put("url", urlRefix + "/qyWeChat/redirectUrl");
        }
        return ResponseData.success(map);
    }

    @Override
    public Result<List<CustomerNotOnlineVO>> getNotOnlineCustomer() {
        ProjCustomReq dto = new ProjCustomReq();
        List<CustomerAndHospitalNotOnlineDataVO> list = projCustomInfoMapper.getNotOnlineCustomer(dto);
        List<CustomerNotOnlineVO> resultList = new ArrayList<>();
        List<Long> repeatList = new ArrayList<>();
        if (list != null && list.size() > 0) {
            for (CustomerAndHospitalNotOnlineDataVO vo : list) {
                if (!repeatList.contains(vo.getCustomInfoId())) {
                    CustomerNotOnlineVO vo1 = new CustomerNotOnlineVO();
                    vo1.setCustomInfoId(vo.getCustomInfoId());
                    vo1.setCustomerName(vo.getCustomName());
                    resultList.add(vo1);
                    repeatList.add(vo.getCustomInfoId());
                }
            }
        }
        return Result.success(resultList);
    }

    @Override
    public Result<List<HospitalNotOnlineVO>> getNotOnlineHospitalData(ProjCustomReq dto) {
        List<CustomerAndHospitalNotOnlineDataVO> list = projCustomInfoMapper.getNotOnlineCustomer(dto);
        List<HospitalNotOnlineVO> resultList = new ArrayList<>();
        List<Long> repeatList = new ArrayList<>();
        if (list != null && !list.isEmpty()) {
            for (CustomerAndHospitalNotOnlineDataVO vo : list) {
                if (!repeatList.contains(vo.getHospitalId())) {
                    HospitalNotOnlineVO vo1 = new HospitalNotOnlineVO();
                    vo1.setHospitalId(vo.getHospitalId());
                    vo1.setHospitalName(vo.getHospitalName());
                    vo1.setOrgId(vo.getOrgId());
                    vo1.setHospitalInfoId(vo.getHospitalInfoId());
                    vo1.setCustomInfoId(vo.getCustomInfoId());
                    vo1.setCustomerName(vo.getCustomName());
                    resultList.add(vo1);
                    repeatList.add(vo.getHospitalId());
                }
            }
        }
        return Result.success(resultList);
    }
}
