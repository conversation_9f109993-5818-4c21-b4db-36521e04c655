package com.msun.csm.service.proj;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.dao.entity.dict.DictProjectPlanItem;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjPrereleaseMsg;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectPlan;
import com.msun.csm.dao.entity.proj.ProjTodoTask;
import com.msun.csm.dao.mapper.dict.DictProjectPlanItemMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjPrereleaseMsgMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectPlanMapper;
import com.msun.csm.dao.mapper.proj.ProjTodoTaskMapper;
import com.msun.csm.model.param.SendMessageParam;
import com.msun.csm.service.dict.DictProductService;
import com.msun.csm.service.message.SendMessageService;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class ProjPrereleaseMsgServiceImpl implements ProjPrereleaseMsgService {

    @Resource
    private ProjPrereleaseMsgMapper projPrereleaseMsgMapper;

    @Resource
    private DictProjectPlanItemMapper dictProjectPlanItemMapper;

    @Resource
    private ProjProjectPlanMapper projectPlanMapper;

    @Resource
    private SendMessageService sendMessageService;

    @Resource
    private ProjTodoTaskMapper projTodoTaskMapper;

    @Resource
    private ProjProjectInfoService projProjectInfoService;

    @Resource
    private ProjCustomInfoMapper customInfoMapper;

    @Resource
    private DictProductService dictProductService;

    @Override
    public boolean sendPrereleaseMsg(Long projectInfoId, Long yyProductId, Long projectPlanId) {
        // 当前完成的待办对应的项目信息
        ProjProjectInfo projProjectInfo = projProjectInfoService.selectByPrimaryKey(projectInfoId);
        ProjCustomInfo projCustomInfo = customInfoMapper.selectByPrimaryKey(projProjectInfo.getCustomInfoId());


        // 当前完成的待办对应的项目计划
        ProjProjectPlan projProjectPlan = projectPlanMapper.selectByPrimaryKey(projectPlanId);

        // 当前完成的待办对应的项目计划所属的工作项字典信息
        DictProjectPlanItem dictProjectPlanItem = dictProjectPlanItemMapper.selectByPrimaryKey(projProjectPlan.getProjectPlanItemId());

        // 获取当前项目下所有存在前置节点的工作计划
        List<ProjProjectPlan> projectPlanByProject = projectPlanMapper.getProjectPlanByProject(projectInfoId);

        // 以当前待办对应的项目计划工作项为前置节点的项目计划
        List<ProjProjectPlan> projectPlan = this.getProjectPlan(dictProjectPlanItem, projectPlanByProject);

        // 遍历当前待办所对应的所有后续计划节点
        for (ProjProjectPlan plan : projectPlan) {

            // 后续计划所属的工作项字典信息
            DictProjectPlanItem dictProjectPlanItem2 = dictProjectPlanItemMapper.selectByPrimaryKey(plan.getProjectPlanItemId());


            // 当前前置节点对应的预发消息
            List<ProjPrereleaseMsg> projPrereleaseMsgs = projPrereleaseMsgMapper.selectList(new QueryWrapper<ProjPrereleaseMsg>()
                    .eq("is_deleted", 0)
                    .eq("msg_class_id", 100001L)
                    .eq("plan_item_code", dictProjectPlanItem2.getProjectPlanItemCode())
                    .eq("project_info_id", projectInfoId)
                    .eq(yyProductId != null, "yy_product_id", yyProductId)

            );
            // 对查询到的消息预发记录进行循环遍历
            for (ProjPrereleaseMsg projPrereleaseMsg : projPrereleaseMsgs) {
                // 预发节点对应的前置节点
                String priorProjectPlanItemId = plan.getPriorProjectPlanItemId();
                String[] split = priorProjectPlanItemId.split(",");
                List<String> list = Arrays.asList(split);
                List<Long> collect = list.stream().map(Long::valueOf).collect(Collectors.toList());
                // 所有前置节点的计划
                List<ProjProjectPlan> projProjectPlans = projectPlanMapper.getProjectPlanByProjectAndItemIds(projectInfoId, collect);
                if (this.allCompleted(projProjectPlans)) {
                    List<ProjTodoTask> sendMessageTodoList = projTodoTaskMapper.getTodoTaskByProjectAndPlan(projectInfoId, plan.getProjectPlanId());
                    for (ProjTodoTask sendMessageTodo : sendMessageTodoList) {
                        // 消息内容对应的替换参数
                        Map<String, String> messageContentParam = new HashMap<>();
                        messageContentParam.put("customName", projCustomInfo.getCustomName());
                        messageContentParam.put("projectNumber", projProjectInfo.getProjectNumber());
                        if (Integer.valueOf(1).equals(projProjectInfo.getHisFlag())) {
                            messageContentParam.put("hisFlag", "（首期）");
                        } else {
                            messageContentParam.put("hisFlag", "");
                        }
                        messageContentParam.put("title", sendMessageTodo.getTitle());
                        if (sendMessageTodo.getYyProductId() == null) {
                            messageContentParam.put("productName", "");
                        } else {
                            String productName = dictProductService.getProductNameByYyProductId(sendMessageTodo.getYyProductId());
                            if (StringUtils.isBlank(productName)) {
                                messageContentParam.put("productName", "");
                            } else {
                                messageContentParam.put("productName", "（" + productName + "）");
                            }
                        }
                        messageContentParam.put("planTime", sendMessageTodo.getPlanTime() == null ? "计划日期" : DateUtil.formatDate(sendMessageTodo.getPlanTime()));

                        List<Long> sysUserIds = new ArrayList<>();
                        if (sendMessageTodo.getBackendEngineerId() != null) {
                            sysUserIds.add(sendMessageTodo.getBackendEngineerId());
                        }
                        if (sendMessageTodo.getImplementationEngineerId() != null) {
                            sysUserIds.add(sendMessageTodo.getImplementationEngineerId());
                        }
                        if (!CollectionUtils.isEmpty(sysUserIds)) {
                            SendMessageParam messageParam = new SendMessageParam();
                            messageParam.setProjectInfoId(projectInfoId);
                            messageParam.setMessageTypeId(projPrereleaseMsg.getMsgClassId());
                            messageParam.setMessageContentParam(messageContentParam);
                            messageParam.setSysUserIds(sysUserIds);
                            messageParam.setPictureUrl(null);
                            messageParam.setUrl(null);
                            sendMessageService.sendMessage2(messageParam);

                            // 消息发送成功之后，将消息预发表的记录删除
                            projPrereleaseMsgMapper.updateDataToDeleted(projPrereleaseMsg.getPrereleaseMsgId());
                        }
                    }
                }
            }
        }


        return false;
    }

    private boolean allCompleted(List<ProjProjectPlan> projProjectPlans) {
        if (CollectionUtils.isEmpty(projProjectPlans)) {
            return true;
        }
        return projProjectPlans.stream().allMatch(projProjectPlan -> Integer.valueOf(1).equals(projProjectPlan.getStatus()));
    }

    /**
     * 获取以指定工作项为前置节点的项目编码
     *
     * @param dictProjectPlanItem
     * @param projectPlanByProject
     * @return
     */
    private List<ProjProjectPlan> getProjectPlan(DictProjectPlanItem dictProjectPlanItem, List<ProjProjectPlan> projectPlanByProject) {
        if (CollectionUtils.isEmpty(projectPlanByProject)) {
            return new ArrayList<>();
        }
        // 获取以当前工作项为前置节点的项目计划
        return projectPlanByProject.stream().filter(item -> {
            String[] split = item.getPriorProjectPlanItemId().split(",");
            List<String> list = Arrays.asList(split);
            return list.contains(String.valueOf(dictProjectPlanItem.getProjectPlanItemId()));
        }).collect(Collectors.toList());
    }
}
