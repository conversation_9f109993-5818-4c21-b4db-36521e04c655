package com.msun.csm.service.proj;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.msun.csm.util.PageHelperUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.ProjectPlanStatusEnum;
import com.msun.csm.common.enums.PurchaseModeEnum;
import com.msun.csm.common.enums.SpecialApprovalTypeEnum;
import com.msun.csm.common.enums.active.ActiveEnum;
import com.msun.csm.common.enums.message.DictMessageTypeEnum;
import com.msun.csm.common.model.Result;
import com.msun.csm.config.Global;
import com.msun.csm.dao.entity.dict.DictProduct;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjOrderInfo;
import com.msun.csm.dao.entity.proj.ProjOrderProduct;
import com.msun.csm.dao.entity.proj.ProjProductSupplementaryRecord;
import com.msun.csm.dao.entity.proj.ProjProductSupplementaryRecordDetail;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjSpecialProductRecord;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderProductMapper;
import com.msun.csm.dao.mapper.proj.ProjProductSupplementaryRecordDetailMapper;
import com.msun.csm.dao.mapper.proj.ProjProductSupplementaryRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectFileMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjSpecialProductRecordMapper;
import com.msun.csm.model.convert.ProjProductSupplementaryRecordConvert;
import com.msun.csm.model.dto.InitProductSupplementaryRecordDTO;
import com.msun.csm.model.dto.ProjOnlineStepDTO;
import com.msun.csm.model.dto.ProjProductSupplementaryRecordDTO;
import com.msun.csm.model.param.SendMessageParam;
import com.msun.csm.model.vo.ProjProductSupplementaryRecordVO;
import com.msun.csm.model.vo.product.InitProductSupplementaryRecordVO;
import com.msun.csm.model.vo.product.ProductSupplementaryRecordDetailVO;
import com.msun.csm.service.api.ApiYunyingService;
import com.msun.csm.service.message.SendMessageService;
import com.msun.csm.util.DateUtil;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.StringUtils;
import com.msun.csm.util.obs.OBSClientUtils;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 特批/工单产品补录记录表(ProjProductSupplementaryRecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-04 16:43:18
 */
@Slf4j
@Service("projProductSupplementaryRecordService")
public class ProjProductSupplementaryRecordServiceImpl implements ProjProductSupplementaryRecordService {

    @Resource
    private ProjProductSupplementaryRecordMapper projProductSupplementaryRecordMapper;
    @Resource
    private ProjOrderInfoMapper projOrderInfoMapper;
    @Resource
    private ProjProductSupplementaryRecordConvert projProductSupplementaryRecordConvert;
    @Resource
    private ProjCustomInfoMapper projCustomInfoMapper;
    @Resource
    private DictProductMapper dictProductMapper;
    @Resource
    private ProjProjectInfoMapper projProjectInfoMapper;
    @Resource
    private ProjOrderProductMapper prodOrderProductMapper;
    @Resource
    private ProjSpecialProductRecordMapper projSpecialProductRecordMapper;
    @Resource
    private ProjProductSupplementaryRecordDetailMapper prodProductSupplementaryRecordDetailMapper;
    @Resource
    private ApiYunyingService apiYunyingService;
    @Resource
    private ProjProjectFileMapper projectFileMapper;
    @Resource
    private OnlineStepService onlineStepService;

    @Value("${spring.profiles.active}")
    private String activeProfiles;

    @Lazy
    @Resource
    private SendMessageService sendMessageService;

    /**
     * 说明: 特批/工单产品补录记录表初始化接口
     *
     * @param supplementaryRecordDTO
     * @return:com.msun.csm.common.model.Result<com.msun.csm.model.vo.product.InitProductSupplementaryRecordVO>
     * @author: Yhongmin
     * @createAt: 2024/7/5 9:15
     * @remark: Copyright
     */
    @Override
    public Result<InitProductSupplementaryRecordVO> initProductSupplementaryRecord(
            InitProductSupplementaryRecordDTO supplementaryRecordDTO) {
        InitProductSupplementaryRecordVO initProductSupplementaryRecord = new InitProductSupplementaryRecordVO();
        //获取所有客户选项
        List<ProjCustomInfo> customInfoList = projCustomInfoMapper.selectList(new QueryWrapper<>());
        List<InitProductSupplementaryRecordVO.DictSupplementaryRecord> customInfoListVO =
                customInfoList.stream().map(item -> {
                    InitProductSupplementaryRecordVO.DictSupplementaryRecord baseIdNameResp =
                            new InitProductSupplementaryRecordVO.DictSupplementaryRecord();
                    baseIdNameResp.setId(StringUtils.nvl(item.getCustomInfoId()));
                    baseIdNameResp.setName(item.getCustomName());
                    return baseIdNameResp;
                }).collect(Collectors.toList());
        initProductSupplementaryRecord.setCustomInfoList(customInfoListVO);
        //购买模式选项
        PurchaseModeEnum[] purchaseModes = PurchaseModeEnum.getEnumArray();
        List<InitProductSupplementaryRecordVO.DictSupplementaryRecord> purchaseModeList =
                Arrays.stream(purchaseModes).map(item -> {
                    InitProductSupplementaryRecordVO.DictSupplementaryRecord baseIdNameResp =
                            new InitProductSupplementaryRecordVO.DictSupplementaryRecord();
                    baseIdNameResp.setId(item.getCode());
                    baseIdNameResp.setName(item.getDesc());
                    return baseIdNameResp;
                }).collect(Collectors.toList());
        initProductSupplementaryRecord.setPurchaseModeList(purchaseModeList);
        //特批类型选项
        SpecialApprovalTypeEnum[] specialApprovalTypes = SpecialApprovalTypeEnum.getEnumArray();
        List<InitProductSupplementaryRecordVO.DictSupplementaryRecord> specialApprovalTypeList =
                Arrays.stream(specialApprovalTypes).map(item -> {
                    InitProductSupplementaryRecordVO.DictSupplementaryRecord baseIdNameResp =
                            new InitProductSupplementaryRecordVO.DictSupplementaryRecord();
                    baseIdNameResp.setId(item.getCode());
                    baseIdNameResp.setName(item.getDesc());
                    return baseIdNameResp;
                }).collect(Collectors.toList());
        initProductSupplementaryRecord.setSpecialApprovalTypeList(specialApprovalTypeList);
        //特批产品
        List<InitProductSupplementaryRecordVO.DictSupplementaryRecord> productList =
                dictProductMapper.selectList(new QueryWrapper<DictProduct>().eq("yy_is_cloud", 1)).stream()
                        .map(item -> {
                            InitProductSupplementaryRecordVO.DictSupplementaryRecord baseIdNameResp
                                    = new InitProductSupplementaryRecordVO.DictSupplementaryRecord();
                            baseIdNameResp.setId(StringUtils.nvl(item.getYyProductId()));
                            baseIdNameResp.setName(item.getProductName());
                            return baseIdNameResp;
                        }).collect(Collectors.toList());
        initProductSupplementaryRecord.setProductList(productList);
        //表格数据-数组
        initProductSupplementaryRecord.setSupplementaryRecordList(
                selectProductWithSupplementary(supplementaryRecordDTO));
        return Result.success(initProductSupplementaryRecord);
    }

    /**
     * 说明: 产品信息-根据客户id查询所有工单信息
     *
     * @param customerInfoId
     * @return:com.msun.csm.common.model.Result<com.msun.csm.common.model.BaseIdNameResp>
     * @author: Yhongmin
     * @createAt: 2024/7/5 9:04
     * @remark: Copyright
     */
    @Override
    public Result<List<InitProductSupplementaryRecordVO.DictSupplementaryRecord>> getOrderInfoDeliveryOrderNo(
            Long customerInfoId) {
        List<ProjOrderInfo> orderInfoList = projOrderInfoMapper.findProjOrderInfoByCustomInfoId(customerInfoId);
        if (CollectionUtils.isNotEmpty(orderInfoList)) {
            List<InitProductSupplementaryRecordVO.DictSupplementaryRecord> orderInfoListVO =
                    orderInfoList.stream().map(item -> {
                        InitProductSupplementaryRecordVO.DictSupplementaryRecord baseIdNameResp =
                                new InitProductSupplementaryRecordVO.DictSupplementaryRecord();
                        baseIdNameResp.setId(StringUtils.nvl(item.getOrderInfoId()));
                        baseIdNameResp.setName(item.getDeliveryOrderNo());
                        return baseIdNameResp;
                    }).collect(Collectors.toList());
            return Result.success(orderInfoListVO);
        }
        return Result.fail("未查询到工单信息");
    }

    /**
     * 说明: 分页查询特批/工单产品补录记录表
     *
     * @param supplementaryRecordDTO
     * @return:com.msun.csm.common.model.Result<com.github.pagehelper.PageInfo<com.msun.csm.model.vo.ProjProductSupplementaryRecordVO>>
     * @author: Yhongmin
     * @createAt: 2024/7/5 11:53
     * @remark: Copyright
     */
    @Override
    public PageInfo<ProjProductSupplementaryRecordVO> selectProductWithSupplementary(
            InitProductSupplementaryRecordDTO supplementaryRecordDTO) {
        // 查询 医院信息
        List<ProjProductSupplementaryRecordVO> supplementaryRecordList =
                PageHelperUtil.queryPage(supplementaryRecordDTO.getPageNum(), supplementaryRecordDTO.getPageSize(), page -> prodProductSupplementaryRecordDetailMapper.selectProductWithSupplementary(supplementaryRecordDTO));
        supplementaryRecordList.stream().forEach(item -> {
            //工单处理类型
            if (item.getSpecialApprovalType() != null) {
                SpecialApprovalTypeEnum specialApprovalTypeEnum =
                        SpecialApprovalTypeEnum.getEnum(item.getSpecialApprovalType());
                item.setSpecialApprovalTypeName(ObjectUtils.isEmpty(specialApprovalTypeEnum) ? StrUtil.EMPTY
                        : specialApprovalTypeEnum.getDesc());
            }
            //购买模式
            if (item.getPurchaseMode() != null) {
                PurchaseModeEnum purchaseModeEnum = PurchaseModeEnum.getEnum(item.getPurchaseMode());
                item.setPurchaseModeName(ObjectUtils.isEmpty(purchaseModeEnum) ? StrUtil.EMPTY
                        : purchaseModeEnum.getDesc());
            }
            List<ProductSupplementaryRecordDetailVO> recordDetailVOList =
                    prodProductSupplementaryRecordDetailMapper.selectListByProductSupplementaryRecordId(
                            item.getProductSupplementaryRecordId());
            List<String> yyProductIdList =
                    recordDetailVOList.stream().map(detail -> StringUtils.nvl(detail.getYyProductId()))
                            .collect(Collectors.toList());
            String[] productIdList = yyProductIdList.toArray(new String[yyProductIdList.size()]);
            // 查询yyProjId
            if (StrUtil.isNotBlank(item.getSpecialApprovalType())
                    && item.getSpecialApprovalType().equals(SpecialApprovalTypeEnum.WORK_ORDER_PRODUCT.getCode())
                    && ArrayUtil.isNotEmpty(productIdList)) {
                ProjOrderProduct orderProduct =
                        prodOrderProductMapper.selectOne(new QueryWrapper<ProjOrderProduct>().eq(
                                "order_info_id",
                                item.getOrderInfoId()).eq("yy_order_product_id",
                                Long.parseLong(yyProductIdList.get(0))));
                if (ObjectUtils.isNotEmpty(orderProduct)) {
                    item.setYyProjId(orderProduct.getYyProjId());
                }
            }
            item.setRecordDetailDTOList(productIdList);
            item.setProductName(recordDetailVOList.stream().map(detail -> detail.getProductName())
                    .collect(Collectors.joining(",")));
            item.setDeploymentProductName(recordDetailVOList.stream().map(detail -> detail.getDeploymentProductName())
                    .collect(Collectors.joining(",")));
            if (ObjectUtils.isNotEmpty(item.getProjectFileId())) {
                List<Long> projectFileId =
                        Arrays.stream(item.getProjectFileId().split(",")).map(Long::valueOf)
                                .collect(Collectors.toList());
                List<ProjProjectFile> projectFiles =
                        projectFileMapper.selectList(new QueryWrapper<ProjProjectFile>().in("project_file_id",
                                projectFileId));
                item.setProductCertificate(projectFiles.stream().map(item1 -> {
                    InitProductSupplementaryRecordVO.DictSupplementaryPath baseIdNameResp =
                            new InitProductSupplementaryRecordVO.DictSupplementaryPath();
                    baseIdNameResp.setId(StringUtils.nvl(item1.getProjectFileId()));
                    baseIdNameResp.setName(item1.getFileName());
                    baseIdNameResp.setUrl(OBSClientUtils.getTemporaryUrl(item1.getFilePath(), 3600));
                    return baseIdNameResp;
                }).collect(Collectors.toList()));
            }
        });
        PageInfo<ProjProductSupplementaryRecordVO> pageInfo = new PageInfo<>(supplementaryRecordList);
        return pageInfo;
    }

    /**
     * 通过ID查询单条数据
     *
     * @param productSupplementaryRecordId 主键
     * @return 实例对象
     */
    @Override
    public ProjProductSupplementaryRecordVO getProjProductSupplementaryRecordById(Long productSupplementaryRecordId) {
        //1.根据id获取详细信息
        ProjProductSupplementaryRecord po = null;
        if (po == null) {
            return null;
        }
        //2.转换为vo后返回
        ProjProductSupplementaryRecordVO entity = this.projProductSupplementaryRecordConvert.po2Vo(po);
        ////封装字符串==>数组转换
        return entity;

    }


    @Resource
    private ProjProjectPlanService projProjectPlanService;

    @Resource
    private ProjTodoTaskService todoTaskService;

    /**
     * 新增数据
     *
     * @param entity 实例对象
     * @return 成功为1，失败为0
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> save(ProjProductSupplementaryRecordDTO entity) {
        ProjProjectInfo projectInfo = projProjectInfoMapper.selectOne(new QueryWrapper<ProjProjectInfo>().eq(
                "order_info_id", entity.getOrderInfoId()));

        //判断新增还是修改
        if (entity.getProductSupplementaryRecordId() == null) {
            Date date = new Date();
            ProjProductSupplementaryRecord supplementaryRecord = new ProjProductSupplementaryRecord();
            BeanUtils.copyProperties(entity, supplementaryRecord);
            if (entity.getProductCertificate().size() > 0) {
                String projectFileId =
                        entity.getProductCertificate().stream().map(item -> item.getId()).collect(Collectors.joining(
                                ","));
                supplementaryRecord.setProjectFileId(projectFileId);
            }
            supplementaryRecord.setCreateTime(date);
            supplementaryRecord.setUpdateTime(date);
            supplementaryRecord.setUpdaterId(Global.getSysUserVO().getSysUserId());
            supplementaryRecord.setCreaterId(Global.getSysUserVO().getSysUserId());
            projProductSupplementaryRecordMapper.insert(supplementaryRecord);
            entity.setProductSupplementaryRecordId(supplementaryRecord.getProductSupplementaryRecordId());

            //子表以及产品表新增
            saveProductSupplementaryRecordDetail(entity, date, projectInfo);
            //处理上线步骤
            ProjOnlineStepDTO projOnlineStepDTO = new ProjOnlineStepDTO();
            projOnlineStepDTO.setProjectInfoId(projectInfo.getProjectInfoId());
            projOnlineStepDTO.setCustomInfoId(projectInfo.getCustomInfoId());
            onlineStepService.saveOnlineStep(projOnlineStepDTO);
        } else {
            this.update(entity);
        }
        projProjectPlanService.updatePlanTotalAndCompleteCountByProjectAndItemCode(projectInfo.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_PRODUCT);
        projProjectPlanService.updatePlanAndTodoTaskStatusByProjectAndItemCode(projectInfo.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_PRODUCT, ProjectPlanStatusEnum.UNDERWAY);

        // 发送企业微信消息给项目经理
        try {
            if (entity != null && entity.getRecordDetailDTOList() != null && entity.getRecordDetailDTOList().length != 0) {
                List<Long> longs = new ArrayList<>();
                for (String s : entity.getRecordDetailDTOList()) {
                    longs.add(Long.parseLong(s));
                }
                List<DictProduct> list = dictProductMapper.selectList(new QueryWrapper<DictProduct>().in("yy_product_id", longs).eq("is_deleted", 0));
                String productNames = list.stream().map(DictProduct::getProductName).collect(Collectors.joining(","));
                List<Long> userIds = new ArrayList<>();
                userIds.add(464993509700489218L);
                if (ActiveEnum.PROD.getActive().equals(activeProfiles)) {
                    userIds.add(projectInfo.getProjectLeaderId());
                }
                // 消息内容替换的参数
                Map<String, String> messageContentParam = new HashMap<>();
                messageContentParam.put("projectName", projectInfo.getProjectName());
                messageContentParam.put("projectNumber", projectInfo.getProjectNumber());
                messageContentParam.put("number", String.valueOf(longs.size()));
                if (Integer.valueOf(1).equals(projectInfo.getHisFlag())) {
                    messageContentParam.put("hisFlag", "（首期）");
                    messageContentParam.put("projectNode", "云资源划分及部署");
                } else {
                    messageContentParam.put("hisFlag", "");
                    messageContentParam.put("projectNode", "产品申请开通");
                }
                // 产品名称
                messageContentParam.put("productNames", productNames);

                SendMessageParam messageParam = new SendMessageParam();
                // 消息类型
                messageParam.setMessageTypeId(DictMessageTypeEnum.SPECIAL_PRODUCT_MESSAGING.getId());
                messageParam.setProjectInfoId(projectInfo.getProjectInfoId());
                messageParam.setMessageContentParam(messageContentParam);
                messageParam.setSysUserIds(userIds);
                sendMessageService.sendMessage2(messageParam);

            }
        } catch (Exception e) {
            log.error("发送企业微信消息给项目经理失败", e);
        }

        return Result.success();
    }

    /**
     * 修改数据
     *
     * @param entity 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> update(ProjProductSupplementaryRecordDTO entity) {
        //获取主表信息
        ProjProductSupplementaryRecord po =
                this.projProductSupplementaryRecordMapper.selectById(entity.getProductSupplementaryRecordId());
        ProjProjectInfo projectInfo = projProjectInfoMapper.selectOne(new QueryWrapper<ProjProjectInfo>().eq(
                "order_info_id", po.getOrderInfoId()));
        List<ProjProductSupplementaryRecordDetail> recordDetails =
                prodProductSupplementaryRecordDetailMapper.selectList(
                        new QueryWrapper<ProjProductSupplementaryRecordDetail>().eq("product_supplementary_record_id",
                                entity.getProductSupplementaryRecordId()));
        if (recordDetails.size() == 0) {
            return Result.fail("请先添加产品明细");
        }
        List<Long> yyProductIdDelete = recordDetails.stream().map(item -> {
            return item.getYyProductId();
        }).collect(Collectors.toList());
        if (NumberEnum.NO_1.num().equals(po.getSpecialApprovalType())) {
            //工单产品
            prodOrderProductMapper.deletedOrderProductByYyOrderProductIdsAndProjectInfoId(yyProductIdDelete,
                    projectInfo.getProjectInfoId());
        } else {
            //特批产品
            projSpecialProductRecordMapper.deletedSpecialByYyOrderProductIdsAndProjectInfoId(yyProductIdDelete,
                    projectInfo.getProjectInfoId());
        }
        //根据补录主表id删除特批产品记录，工单产品记录，补录明细表
        prodProductSupplementaryRecordDetailMapper.delete(
                new QueryWrapper<ProjProductSupplementaryRecordDetail>().eq("product_supplementary_record_id",
                        entity.getProductSupplementaryRecordId()));
        //删除历史数据完成，开始新增子表和产品表
        Date date = new Date();
        //子表以及产品表新增
        saveProductSupplementaryRecordDetail(entity, date, projectInfo);
        //主表修改
        ProjProductSupplementaryRecord supplementaryRecord = new ProjProductSupplementaryRecord();
        BeanUtils.copyProperties(entity, supplementaryRecord);
        supplementaryRecord.setUpdateTime(date);
        if (entity.getProductCertificate().size() > 0) {
            String projectFileId =
                    entity.getProductCertificate().stream().map(item -> item.getId()).collect(Collectors.joining(","));
            supplementaryRecord.setProjectFileId(projectFileId);
        }
        //更新主表
        projProductSupplementaryRecordMapper.updateById(supplementaryRecord);
        int update = 0;
        return Result.success();
    }

    /**
     * 修改数据
     *
     * @param id@return 成功为1，失败为0
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> delete(Long id) {
        //获取主表信息
        ProjProductSupplementaryRecord po = this.projProductSupplementaryRecordMapper.selectById(id);
        ProjProjectInfo projectInfo = projProjectInfoMapper.selectOne(new QueryWrapper<ProjProjectInfo>().eq(
                "order_info_id", po.getOrderInfoId()));
        List<ProjProductSupplementaryRecordDetail> recordDetails =
                prodProductSupplementaryRecordDetailMapper.selectList(
                        new QueryWrapper<ProjProductSupplementaryRecordDetail>().eq("product_supplementary_record_id",
                                id));
        if (recordDetails.size() == 0) {
            return Result.fail("请先添加产品明细");
        }
        List<Long> yyProductIdDelete = recordDetails.stream().map(item -> {
            return item.getYyProductId();
        }).collect(Collectors.toList());
        if (NumberEnum.NO_1.num().equals(po.getSpecialApprovalType())) {
            //工单产品
            prodOrderProductMapper.deletedOrderProductByYyOrderProductIdsAndProjectInfoId(yyProductIdDelete,
                    projectInfo.getProjectInfoId());
        } else {
            //特批产品
            projSpecialProductRecordMapper.deletedSpecialByYyOrderProductIdsAndProjectInfoId(yyProductIdDelete,
                    projectInfo.getProjectInfoId());
        }
        ProjProductSupplementaryRecord supplementaryRecord = new ProjProductSupplementaryRecord();
        supplementaryRecord.setIsDeleted(1);
        supplementaryRecord.setProductSupplementaryRecordId(id);
        int del = projProductSupplementaryRecordMapper.updateById(supplementaryRecord);
        if (del > 0) {
            //处理产品对照
            apiYunyingService.dealProducts(projectInfo.getProjectInfoId());
            return Result.success();
        }
        return Result.fail("删除失败");
    }

    public void saveProductSupplementaryRecordDetail(ProjProductSupplementaryRecordDTO entity, Date date,
                                                     ProjProjectInfo projectInfo) {
        if (entity == null || entity.getRecordDetailDTOList() == null || entity.getRecordDetailDTOList().length == 0) {
            return;
        }
        List<ProjSpecialProductRecord> productRecords = new ArrayList<>();
        List<ProjOrderProduct> orderProducts = new ArrayList<>();
        List<String> longs = Arrays.asList(entity.getRecordDetailDTOList());
        List<ProjProductSupplementaryRecordDetail> supplementaryRecordDetails = new ArrayList<>();
        for (String detailDTO : longs) {
            ProjProductSupplementaryRecordDetail detail = new ProjProductSupplementaryRecordDetail();
            detail.setProductSupplementaryRecordId(entity.getProductSupplementaryRecordId());
            detail.setCreateTime(date);
            detail.setUpdateTime(date);
            detail.setCreaterId(Global.getSysUserVO().getSysUserId());
            detail.setUpdaterId(Global.getSysUserVO().getSysUserId());
            detail.setYyProductId(Long.parseLong(detailDTO));
            detail.setIsDeleted(0);
            detail.setProductSupplementaryRecordDetailId(SnowFlakeUtil.getId());
            supplementaryRecordDetails.add(detail);
            if (NumberEnum.NO_2.num().equals(entity.getSpecialApprovalType())) {
                getProjSpecialProductRecord(productRecords, entity, date, detail, projectInfo);
            } else {
                getProjOrderProduct(orderProducts, entity, date, detail, projectInfo);
            }
        }
        if (orderProducts.size() > 0) {
            List<ProjOrderProduct> projOrderProducts =
                    prodOrderProductMapper.selectList(new QueryWrapper<ProjOrderProduct>().eq("project_info_id",
                            projectInfo.getProjectInfoId()));
            if (projOrderProducts.size() > 0) {
                orderProducts = orderProducts.stream().filter(ord -> projOrderProducts.stream()
                                .noneMatch(ord1 -> ord1.getYyOrderProductId().equals(ord.getYyOrderProductId())))
                        .collect(Collectors.toList());
            }
            if (orderProducts.size() > 0) {
                //过滤解决方案不是0 的数据
                ProjOrderProduct orderProduct = projOrderProducts.stream()
                        .filter(ord -> !ord.getProductResolveTypeId().equals(0L))
                        .findFirst().orElse(null);
                Long resolveTypeId = orderProduct == null ? 0L : orderProduct.getProductResolveTypeId();
                orderProducts.stream().forEach(ord -> ord.setProductResolveTypeId(resolveTypeId));
                prodOrderProductMapper.batchInsert(orderProducts);
            }
        }
        if (productRecords.size() > 0) {
            projSpecialProductRecordMapper.batchInsert(productRecords);
        }
        if (supplementaryRecordDetails.size() > 0) {
            prodProductSupplementaryRecordDetailMapper.batchInsert(supplementaryRecordDetails);

        }
        //处理产品对照
        apiYunyingService.dealProducts(projectInfo.getProjectInfoId());

    }

    /**
     * 说明: 特批产品数据处理
     *
     * @param productRecords
     * @param entity
     * @param date
     * @param detail
     * @return:void
     * @author: Yhongmin
     * @createAt: 2024/7/8 10:35
     * @remark: Copyright
     */
    public void getProjSpecialProductRecord(List<ProjSpecialProductRecord> productRecords,
                                            ProjProductSupplementaryRecordDTO entity, Date date,
                                            ProjProductSupplementaryRecordDetail detail, ProjProjectInfo projectInfo) {
        ProjSpecialProductRecord process = new ProjSpecialProductRecord();
        process.setSpecialProductRecordId(detail.getProductSupplementaryRecordDetailId());
        process.setSpecialProductId(detail.getYyProductId());
        process.setCreaterId(Global.getSysUserVO().getSysUserId());
        process.setCreateTime(date);
        process.setUpdateTime(date);
        process.setUpdaterId(Global.getSysUserVO().getSysUserId());
        process.setIsDeleted(0);
        process.setProductBuyMode(entity.getPurchaseMode());
        process.setProductSubscribeStartTime(entity.getEffectiveStartTime());
        process.setProductSubscribeEndTime(entity.getEffectiveCompTime());
        process.setOrderInfoId(entity.getOrderInfoId());
        process.setSpecialInfo(StrUtil.EMPTY);
        process.setProductExcutionStatus(0);
        process.setProductOpenStatus(0);
        process.setProjectInfoId(projectInfo.getProjectInfoId());
        productRecords.add(process);

    }

    /**
     * 说明: 交付工单产品信息数据处理
     *
     * @param orderProducts
     * @param entity
     * @param date
     * @param detail
     * @return:void
     * @author: Yhongmin
     * @createAt: 2024/7/8 10:41
     * @remark: Copyright
     */
    public void getProjOrderProduct(List<ProjOrderProduct> orderProducts, ProjProductSupplementaryRecordDTO entity,
                                    Date date, ProjProductSupplementaryRecordDetail detail,
                                    ProjProjectInfo projectInfo) {
        ProjOrderProduct orderProduct = new ProjOrderProduct();
        orderProduct.setOrderProductId(detail.getProductSupplementaryRecordDetailId());
        orderProduct.setOrderInfoId(entity.getOrderInfoId());
        orderProduct.setCreaterId(Global.getSysUserVO().getSysUserId());
        orderProduct.setCreateTime(date);
        orderProduct.setUpdateTime(date);
        orderProduct.setUpdaterId(Global.getSysUserVO().getSysUserId());
        orderProduct.setIsDeleted(0);
        orderProduct.setProductBuyMode(entity.getPurchaseMode());
        if (entity.getEffectiveStartTime() != null && entity.getEffectiveCompTime() != null) {
            Long term = Long.valueOf(DateUtil.fromDayToDayNum(entity.getEffectiveStartTime(),
                    entity.getEffectiveCompTime()));
            orderProduct.setProductSubscribeTerm(term);
        } else {
            orderProduct.setProductSubscribeTerm(0L);
        }
        orderProduct.setProductSubscribeTime(ObjectUtils.isEmpty(entity.getEffectiveStartTime()) ? date
                : entity.getEffectiveStartTime());
        orderProduct.setYyOrderProductId(detail.getYyProductId());
        orderProduct.setProductOpenStatus(0);
        orderProduct.setProductExcutionStatus(0);
        orderProduct.setProjectInfoId(projectInfo.getProjectInfoId());
        orderProduct.setProductSettleStatus(StrUtil.EMPTY);
        orderProduct.setProductSettleProp(BigDecimal.ZERO);
        orderProduct.setProductSettleAmount(BigDecimal.ZERO);
        orderProduct.setProductSubscribeTerm(0L);
        orderProduct.setProductSubscribeStatus(0);
        orderProduct.setProductSubscribeType(0);
        orderProduct.setProductResolveTypeId(0L);
        orderProduct.setYyOrderProductNumber(1);
        orderProduct.setYyProjId(entity.getYyProjId());
        orderProducts.add(orderProduct);
    }


}
