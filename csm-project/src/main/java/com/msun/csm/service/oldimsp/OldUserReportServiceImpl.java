package com.msun.csm.service.oldimsp;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.msun.csm.dao.entity.oldimsp.OldUserReport;
import com.msun.csm.dao.mapper.oldimsp.OldUserReportMapper;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/07/09/10:40
 */
@Service
public class OldUserReportServiceImpl extends ServiceImpl<OldUserReportMapper, OldUserReport> implements OldUserReportService {
    @Override
    public OldUserReport selectUserReport(OldUserReport userReport) {
        OldUserReport one = this.getOne(new QueryWrapper<OldUserReport>()
                .eq("user_id", userReport.getUserId())
        );
        return one;
    }
}
