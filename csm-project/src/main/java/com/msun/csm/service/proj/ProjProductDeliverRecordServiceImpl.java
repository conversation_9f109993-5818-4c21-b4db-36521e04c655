package com.msun.csm.service.proj;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.dao.entity.dict.DictProductExtend;
import com.msun.csm.dao.entity.proj.ProjProductDeliverRecord;
import com.msun.csm.dao.mapper.dict.DictProductExtendMapper;
import com.msun.csm.dao.mapper.proj.ProjProductDeliverRecordMapper;
import com.msun.csm.util.FilterUtil;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/24
 */

@Service
public class ProjProductDeliverRecordServiceImpl implements ProjProductDeliverRecordService {

    @Resource
    private ProjProductDeliverRecordMapper projProductDeliverRecordMapper;

    @Resource
    private DictProductExtendMapper dictProductExtendMapper;

    public List<ProjProductDeliverRecord> findByProjInfoIdAndCustomerInfoId(String projInfoId, String customerInfoId) {
        return projProductDeliverRecordMapper.findByProjInfoIdAndCustomerInfoId(Long.valueOf(projInfoId), Long.valueOf(customerInfoId));
    }

    @Override
    public int deleteByPrimaryKey(Long productDeliverRecordId) {
        return projProductDeliverRecordMapper.deleteByPrimaryKey(productDeliverRecordId);
    }

    @Override
    public int insert(ProjProductDeliverRecord record) {
        return projProductDeliverRecordMapper.insert(record);
    }

    @Override
    public int insertOrUpdate(ProjProductDeliverRecord record) {
        return projProductDeliverRecordMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(ProjProductDeliverRecord record) {
        return projProductDeliverRecordMapper.insertOrUpdateSelective(record);
    }

    @Override
    public int insertSelective(ProjProductDeliverRecord record) {
        return projProductDeliverRecordMapper.insertSelective(record);
    }

    @Override
    public ProjProductDeliverRecord selectByPrimaryKey(Long productDeliverRecordId) {
        return projProductDeliverRecordMapper.selectByPrimaryKey(productDeliverRecordId);
    }

    @Override
    public int updateByPrimaryKeySelective(ProjProductDeliverRecord record) {
        return projProductDeliverRecordMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ProjProductDeliverRecord record) {
        return projProductDeliverRecordMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<ProjProductDeliverRecord> list) {
        return projProductDeliverRecordMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<ProjProductDeliverRecord> list) {
        return projProductDeliverRecordMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<ProjProductDeliverRecord> list) {
        return projProductDeliverRecordMapper.batchInsert(list);
    }

    @Override
    public List<ProjProductDeliverRecord> getSurveyProductDeliverRecord(Long projectInfoId, Boolean needSurvey) {
        //查询所有实施产品
        List<ProjProductDeliverRecord> productDeliverRecord = projProductDeliverRecordMapper.findNeedSurveyProductDeliverRecordByProjectInfoId(projectInfoId);

        if (CollectionUtils.isEmpty(productDeliverRecord)) {
            return new ArrayList<>();
        }

        // 去重
        productDeliverRecord = productDeliverRecord.stream().filter(FilterUtil.distinctByKey(ProjProductDeliverRecord::getProductDeliverId)).collect(Collectors.toList());
        if (null == needSurvey) {
            return productDeliverRecord;
        }

        // 不需要调研的数据
        List<DictProductExtend> dictProductExtendList = dictProductExtendMapper.selectList(
                new QueryWrapper<DictProductExtend>()
                        .eq("is_deleted", 0)
                        .eq("survey_flag", 0)
        );
        List<Long> notNeedSurveyYyProductIdList = dictProductExtendList.stream().map(DictProductExtend::getYyProductId).collect(Collectors.toList());

        if (needSurvey) {
            return productDeliverRecord.stream().filter(item -> !notNeedSurveyYyProductIdList.contains(item.getProductDeliverId())).collect(Collectors.toList());
        }
        return productDeliverRecord.stream().filter(item -> notNeedSurveyYyProductIdList.contains(item.getProductDeliverId())).collect(Collectors.toList());
    }

}
