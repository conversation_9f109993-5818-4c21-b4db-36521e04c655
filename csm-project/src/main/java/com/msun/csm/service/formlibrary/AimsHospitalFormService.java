package com.msun.csm.service.formlibrary;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.req.formlibrary.AimsHospitalFormImportParamerReq;
import com.msun.csm.model.req.formlibrary.AimsHospitalFormLookParamerReq;
import com.msun.csm.model.req.formlibrary.AimsHospitalFormParamerReq;
import com.msun.csm.model.req.formlibrary.AimsHospitalFormSelectParamerReq;
import com.msun.csm.model.req.formlibrary.ProjSelectApplicationFormPageReq;
import com.msun.csm.model.req.formlibrary.ProjSelectApplicationFormUpdateReq;
import com.msun.csm.model.resp.formlibrary.AimsHospitalFormImportResultResp;
import com.msun.csm.model.resp.formlibrary.AimsHospitalFormLookResp;
import com.msun.csm.model.resp.formlibrary.AimsHospitalFormResp;
import com.msun.csm.model.resp.formlibrary.ProjSelectApplicationFormResp;

/**
 * @ClassName: DataImportService
 * @Description:
 * @Author: Yhongmin
 * @Date: 13:54 2024/5/28
 */
public interface AimsHospitalFormService {

    Result<PageInfo<AimsHospitalFormResp>> selectAimsHospitalData(AimsHospitalFormParamerReq aimsHospitalFormParamerReq);

    /**
     * 导入手麻数据
     *
     * @param aimsHospitalFormParamerReq
     * @return
     */
    Result<AimsHospitalFormImportResultResp> importAimsFormData(AimsHospitalFormImportParamerReq aimsHospitalFormParamerReq);

    /**
     * 查询选择的手麻数据
     *
     * @param aimsHospitalFormParamerReq
     * @return
     */
    Result<PageInfo<ProjSelectApplicationFormResp>> selectAimsSelectFormHospitalData(ProjSelectApplicationFormPageReq aimsHospitalFormParamerReq);

    /**
     * 移除数据
     * @param aimsHospitalFormParamerReq
     * @return
     */
    Result removeAimsSelectFormHospitalData(ProjSelectApplicationFormUpdateReq aimsHospitalFormParamerReq);

    /**
     * 选择手麻数据
     *
     * @param aimsHospitalFormParamerReq
     * @return
     */
    Result seleltAimsFormListToCsmData(AimsHospitalFormSelectParamerReq aimsHospitalFormParamerReq);

    /**
     * 查询手麻资源库数据
     *
     * @param aimsHospitalFormParamerReq
     * @return
     */
    Result<PageInfo<AimsHospitalFormResp>> selectAimsHospitalFormLibData(AimsHospitalFormParamerReq aimsHospitalFormParamerReq);

    /**
     * 查询手麻表单类型
     * @return
     */
    List<BaseCodeNameResp> queryAimsLibFormType();

    Result<AimsHospitalFormLookResp> lookAimsForm(AimsHospitalFormLookParamerReq aimsHospitalFormParamerReq);
}
