package com.msun.csm.service.proj;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.msun.csm.util.PageHelperUtil;
import org.springframework.stereotype.Service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.msun.csm.common.constants.ObsExpireTimeConsts;
import com.msun.csm.common.enums.projprojectinfo.ProjectTypeEnums;
import com.msun.csm.common.enums.projsettlement.CheckResultEnum;
import com.msun.csm.common.enums.projsettlement.SettlementStatusEnum;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjProjectSettlementUnCheckRelative;
import com.msun.csm.dao.mapper.proj.ProjProjectSettlementUnCheckMapper;
import com.msun.csm.model.convert.ProjProjectSettlementUnCheckRelativeConvert;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementUnCheckDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementUnCheckExcelDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementUnCheckRelativeDTO;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementUnCheckRelativeVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.util.EasyExcelUtil;
import com.msun.csm.util.obs.OBSClientUtils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2024-06-28 04:46:47
 */
@Slf4j
@Service
public class ProjProjectSettlementUnCheckServiceImpl implements ProjProjectSettlementUnCheckService {

    @Resource
    private ProjProjectSettlementUnCheckMapper settlementUnCheckMapper;

    @Resource
    private ProjProjectSettlementUnCheckRelativeConvert settlementUnCheckRelativeConvert;

    @Resource
    private UserHelper userHelper;

    @Override
    public Result<PageInfo<ProjProjectSettlementUnCheckRelativeVO>> findSettlementUnCheckData(ProjProjectSettlementUnCheckDTO dto) {
        return PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> {
            // 查询当前用户id
            ProjProjectSettlementUnCheckRelativeDTO relativeDTO = BeanUtil.copyProperties(dto, ProjProjectSettlementUnCheckRelativeDTO.class);
            String userYunyingId = userHelper.getCurrentUser().getUserYunyingId();
            relativeDTO.setUnCheckUserId(Long.parseLong(userYunyingId));
            List<ProjProjectSettlementUnCheckRelative> relatives = findSettlementUnCheckDataImpl(relativeDTO).stream().map(e -> {
                if (StrUtil.isNotBlank(e.getFilePath())) {
                    e.setFilePath(OBSClientUtils.getTemporaryUrl(e.getFilePath(), ObsExpireTimeConsts.SEVEN_DAY));
                }
                return e;
            }).collect(Collectors.toList());
            List<ProjProjectSettlementUnCheckRelativeVO> checkVOS = settlementUnCheckRelativeConvert.po2Vo(relatives);
            PageInfo<ProjProjectSettlementUnCheckRelativeVO> pageInfoVO = PageInfo.of(checkVOS);
            PageInfo<ProjProjectSettlementUnCheckRelative> pageInfo = new PageInfo<>(relatives);
            pageInfoVO.setTotal(pageInfo.getTotal());
            return Result.success(pageInfoVO);
        });
    }

    @Override
    public void settlementUnCheckExportExcel(ProjProjectSettlementUnCheckDTO dto, HttpServletResponse response) {
        ProjProjectSettlementUnCheckRelativeDTO relativeDTO = BeanUtil.copyProperties(dto, ProjProjectSettlementUnCheckRelativeDTO.class);
        relativeDTO.setUnCheckUserId(getCurrentUserId());
        List<ProjProjectSettlementUnCheckRelative> relatives = findSettlementUnCheckDataImpl(relativeDTO);
        String fileName = "分公司经理审核数据导出.xlsx";
        // 查询项目名称
        try (ExcelWriter excelWriter = EasyExcelFactory.write(EasyExcelUtil.getOutputStream(
                        fileName, response),
                ProjProjectSettlementUnCheckExcelDTO.class).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet("分公司经理审核数据导出").build();
            // 医院导出信息赋值
            List<ProjProjectSettlementUnCheckExcelDTO> excelDTOList = new ArrayList<>();
            excelDataInfo(excelDTOList, relatives);
            // 导出文件
            excelWriter.write(excelDTOList, writeSheet);
        } catch (Exception e) {
            log.error("发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }

    }

    @Override
    public void insert() {
    }

    /**
     * 设置导出所需要的数据
     *
     * @param unCheckExcelDTOS excel导出所需集合数据
     * @param unCheckRelatives 需要导出的数据
     */
    void excelDataInfo(List<ProjProjectSettlementUnCheckExcelDTO> unCheckExcelDTOS, List<ProjProjectSettlementUnCheckRelative> unCheckRelatives) {
        for (ProjProjectSettlementUnCheckRelative unCheckRelative : unCheckRelatives) {
            CheckResultEnum checkResultEnum = CheckResultEnum.getCheckResultEnumByCode(unCheckRelative.getCheckResult());
            String checkResultStr = null;
            if (ObjectUtil.isNotEmpty(checkResultEnum)) {
                checkResultStr = StrUtil.EMPTY;
            }
            ProjProjectSettlementUnCheckExcelDTO excelDTO = ProjProjectSettlementUnCheckExcelDTO.builder()
                    .customName(unCheckRelative.getCustomName()).checkResultStr(checkResultStr)
                    .cloudResFormDesc(unCheckRelative.getCloudResFormDesc())
                    .commitUserName(unCheckRelative.getCommitUserName())
                    .commitDateStr(ObjectUtil.isNotEmpty(unCheckRelative.getCommitTime()) ? DateUtil.formatDate(unCheckRelative.getCommitTime()) : StrUtil.EMPTY)
                    .checkResultStr(unCheckRelative.getCheckResultStr())
                    .checkContent(StrUtil.isBlank(unCheckRelative.getCheckContent()) ? StrUtil.EMPTY : unCheckRelative.getCheckContent())
                    .build();
            unCheckExcelDTOS.add(excelDTO);
        }
    }

    /**
     * 获取当前用户id
     *
     * @return Long
     */
    private Long getCurrentUserId() {
        Long userId = null;
        if (userHelper.getCurrentUser() != null) {
            userId = userHelper.getCurrentUser().getSysUserId();
        }
        return userId;
    }

    private List<ProjProjectSettlementUnCheckRelative> findSettlementUnCheckDataImpl(ProjProjectSettlementUnCheckRelativeDTO dto) {
        return settlementUnCheckMapper.findUncheckData(dto).stream().
                peek(e -> {
                    e.setCloudResFormDesc(e.getProjectName() + "(" + e.getContractNo() + "/" + e.getDeliveryOrderNo() + ")");
                    if (ObjectUtil.isNotEmpty(e.getSettlementStatus())) {
                        SettlementStatusEnum settlementStatusEnum = SettlementStatusEnum.getSettlementStatusByCode(e.getSettlementStatus());
                        if (ObjectUtil.isNotEmpty(settlementStatusEnum)) {
                            if (ObjectUtil.isNotEmpty(settlementStatusEnum.getCheckResultEnum())) {
                                e.setCheckResult(settlementStatusEnum.getCheckResultEnum().getCode());
                                e.setCheckResultStr(settlementStatusEnum.getCheckResultEnum().getDesc());
                            } else {
                                e.setCheckResult(CheckResultEnum.NOT_AUDIT.getCode());
                                e.setCheckResultStr(CheckResultEnum.NOT_AUDIT.getDesc());
                            }
                        }
                    } else {
                        e.setCheckResult(CheckResultEnum.NOT_AUDIT.getCode());
                        e.setCheckResultStr(CheckResultEnum.NOT_AUDIT.getDesc());
                    }
                    if (ObjectUtil.isNotEmpty(e.getProjectType())) {
                        ProjectTypeEnums projectTypeEnums = ProjectTypeEnums.getEnum(e.getProjectType());
                        if (ObjectUtil.isNotEmpty(projectTypeEnums)) {
                            e.setProjectTypeName(projectTypeEnums.getName());
                        }
                    }
                }).collect(Collectors.toList());
    }
}
