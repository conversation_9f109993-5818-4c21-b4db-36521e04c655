package com.msun.csm.service.proj;

import static com.msun.csm.service.proj.ProjCustomInfoServiceImpl.ROLE_CODE_ADMIN;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.dto.PageList;
import com.msun.csm.dao.entity.SysConfig;
import com.msun.csm.dao.entity.SysDept;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.config.ConfigProjectDailyReportDetailItem;
import com.msun.csm.dao.entity.proj.DailyReportProjectInfoVO;
import com.msun.csm.dao.entity.proj.GetProjectDailyReportParam;
import com.msun.csm.dao.entity.proj.GetProjectDailyReportRecordParam;
import com.msun.csm.dao.entity.proj.GetProjectDailyReportRecordParamPO;
import com.msun.csm.dao.entity.proj.GetProjectDailyReportRecordParamPO2;
import com.msun.csm.dao.entity.proj.InitProjectDailyReport;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjProjectDailyReportComments;
import com.msun.csm.dao.entity.proj.ProjProjectDailyReportCommentsVO;
import com.msun.csm.dao.entity.proj.ProjProjectDailyReportDetail;
import com.msun.csm.dao.entity.proj.ProjProjectDailyReportDetailVO;
import com.msun.csm.dao.entity.proj.ProjProjectDailyReportRecord;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjectDailyReportInfo;
import com.msun.csm.dao.entity.proj.ProjectDailyReportRecordVO;
import com.msun.csm.dao.entity.proj.SaveProjProjectDailyReportCommentsParam;
import com.msun.csm.dao.entity.proj.SaveProjectDailyReportParam;
import com.msun.csm.dao.entity.proj.UpdateProjProjectDailyReportDetail;
import com.msun.csm.dao.entity.proj.UpdateProjProjectDailyReportRecord;
import com.msun.csm.dao.mapper.conf.ConfigProjectDailyReportDetailItemMapper;
import com.msun.csm.dao.mapper.config.SysConfigMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectDailyReportCommentsMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectDailyReportDetailMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectDailyReportRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.sysdept.SysDeptMapper;
import com.msun.csm.dao.mapper.sysrole.SysRoleMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.model.dto.ProjCustomInfoDTO;
import com.msun.csm.model.dto.ProjProjectInfoDTO;
import com.msun.csm.model.dto.role.SysRoleDTO;
import com.msun.csm.model.param.SendMessageParam;
import com.msun.csm.model.vo.ProjCustomInfoVO;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.config.ProjMessageInfoService;
import com.msun.csm.service.message.SendMessageService;
import com.msun.csm.util.PageUtil;
import com.msun.csm.util.RedisUtil;
import com.msun.csm.util.SnowFlakeUtil;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class ProjectDailyRecordServiceImpl implements ProjectDailyRecordService {

    @Value("${project.current.url}")
    private String currentUrl;

    @Value("${project.current.qyWeChat-Auth-url}")
    private String weChatAuthUrl;

    @Resource
    private ProjMessageInfoService messageInfoService;

    @Resource
    private ProjProjectInfoMapper projectInfoMapper;

    @Resource
    private ProjCustomInfoMapper projCustomInfoMapper;

    @Resource
    private SendMessageService messageService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private SysDeptMapper sysDeptMapper;

    @Resource
    private UserHelper userHelper;

    @Resource
    private ConfigProjectDailyReportDetailItemMapper configProjectDailyReportDetailItemMapper;

    @Resource
    private ProjProjectDailyReportRecordMapper projProjectDailyReportRecordMapper;

    @Resource
    private ProjProjectDailyReportDetailMapper projProjectDailyReportDetailMapper;

    @Resource
    private ProjProjectDailyReportCommentsMapper projProjectDailyReportCommentsMapper;

    @Resource
    private SysConfigMapper sysConfigMapper;


    @Override
    public InitProjectDailyReport initProjectDailyReportDetailPage(Long projectInfoId) {
        // 当前登录人
        SysUserVO currentUser = userHelper.getCurrentUser();

        // 配置的不同项目状态的日报节点
        List<ConfigProjectDailyReportDetailItem> configProjectDailyReportDetailItems = configProjectDailyReportDetailItemMapper.getConfigProjectDailyReportDetailItem(null);

        // 项目状态集合
        List<Integer> projectDeliverStatusList = configProjectDailyReportDetailItems.stream().map(ConfigProjectDailyReportDetailItem::getProjectDeliverStatus).distinct().collect(Collectors.toList());

        // 当前人是项目经理的符合写日报条件的项目
        List<DailyReportProjectInfoVO> projProjectInfos = projectInfoMapper.selectDailyReportProject(currentUser.getSysUserId(), projectDeliverStatusList);
        List<BaseIdNameResp> projectList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(projProjectInfos)) {
            projectList = projProjectInfos.stream().map(projProjectInfo -> {
                BaseIdNameResp simpleProjProjectInfo = new BaseIdNameResp();
                simpleProjProjectInfo.setId(projProjectInfo.getProjectInfoId());
                simpleProjProjectInfo.setName(projProjectInfo.getCustomName() + "（" + projProjectInfo.getProjectNumber() + "）");
                return simpleProjProjectInfo;
            }).collect(Collectors.toList());
        }

        InitProjectDailyReport initProjectDailyReport = new InitProjectDailyReport();
        initProjectDailyReport.setProjectList(projectList);
        if (projectInfoId != null) {
            initProjectDailyReport.setSelectedProjectInfoId(projectInfoId);
        }
        if (projectInfoId == null && projectList.size() == 1) {
            initProjectDailyReport.setSelectedProjectInfoId(projectList.get(0).getId());
        }

        return initProjectDailyReport;
    }

    @Override
    public ProjectDailyReportInfo getProjectDailyReportDetail(GetProjectDailyReportParam param) {
        if (param.getProjectInfoId() == null) {
            ProjectDailyReportInfo projectDailyReportInfo = new ProjectDailyReportInfo();
            projectDailyReportInfo.setNodeList(new ArrayList<>());
            projectDailyReportInfo.setCommentsList(new ArrayList<>());
            projectDailyReportInfo.setCommentsCount(0);
            projectDailyReportInfo.setReportStatus(0);
            projectDailyReportInfo.setCanEdit(true);
            projectDailyReportInfo.setCanComment(false);
            return projectDailyReportInfo;
        }
        ProjProjectInfo projProjectInfo = projectInfoMapper.selectByPrimaryKey(param.getProjectInfoId());
        Long currentUserId = userHelper.getCurrentSysUserIdWithDefaultValue();
        boolean isProjectLeader = currentUserId.equals(projProjectInfo.getProjectLeaderId());
        if (StringUtils.isBlank(param.getReportSendTime())) {
            param.setReportSendTime(DateUtil.formatDate(new Date()));
        }

        ProjCustomInfo projCustomInfo = projCustomInfoMapper.selectByPrimaryKey(projProjectInfo.getCustomInfoId());
        ProjProjectDailyReportRecord projProjectDailyReportRecord = projProjectDailyReportRecordMapper.selectOne(
                new QueryWrapper<ProjProjectDailyReportRecord>()
                        .eq("is_deleted", 0)
                        .eq("project_info_id", param.getProjectInfoId())
                        .ge("create_time", DateUtil.beginOfDay(DateUtil.parseDate(param.getReportSendTime())))
                        .le("create_time", DateUtil.endOfDay(DateUtil.parseDate(param.getReportSendTime())))
        );

        // 配置的不同项目状态的日报节点
        List<ConfigProjectDailyReportDetailItem> configProjectDailyReportDetailItems = configProjectDailyReportDetailItemMapper.getConfigProjectDailyReportDetailItem(projProjectInfo.getProjectDeliverStatus());
        configProjectDailyReportDetailItems = configProjectDailyReportDetailItems.stream().sorted(Comparator.comparing(ConfigProjectDailyReportDetailItem::getSortNo)).collect(Collectors.toList());

        if (projProjectDailyReportRecord == null) {
            List<ProjProjectDailyReportDetailVO> nodeList = configProjectDailyReportDetailItems.stream().map(item -> new ProjProjectDailyReportDetailVO(null, item.getNodeCode(), item.getNodeName(), null)).collect(Collectors.toList());
            ProjectDailyReportInfo projectDailyReportInfo = new ProjectDailyReportInfo();
            projectDailyReportInfo.setProjectDailyReportRecordId(null);
            projectDailyReportInfo.setCustomName(projCustomInfo.getCustomName());
            projectDailyReportInfo.setProjectInfoId(param.getProjectInfoId());
            projectDailyReportInfo.setProjectNumber(projProjectInfo.getProjectNumber());
            projectDailyReportInfo.setProjectDeliverStatus(projProjectInfo.getProjectDeliverStatus());
            projectDailyReportInfo.setProjectDeliverStatusDesc(this.convertProjectDeliverStatusDesc(projProjectInfo.getProjectDeliverStatus()));
            projectDailyReportInfo.setOnlineTime(projProjectInfo.getOnlineTime());
            projectDailyReportInfo.setOnlineTimeStr(projProjectInfo.getOnlineTime() == null ? "" : DateUtil.formatDateTime(projProjectInfo.getOnlineTime()));
            projectDailyReportInfo.setReportTitle(projCustomInfo.getCustomName() + " " + DateUtil.formatDate(new Date()) + " 项目日报");
            projectDailyReportInfo.setProjectType(projProjectInfo.getProjectType());
            projectDailyReportInfo.setProjectTypeDesc(this.convertProjectTypeDesc(projProjectInfo.getProjectType()));
            projectDailyReportInfo.setRiskLevel(null);
            projectDailyReportInfo.setRiskLevelStr(null);
            projectDailyReportInfo.setNodeList(nodeList);
            projectDailyReportInfo.setCommentsList(new ArrayList<>());
            projectDailyReportInfo.setCommentsCount(0);
            projectDailyReportInfo.setReportStatus(0);
            projectDailyReportInfo.setCanEdit(isProjectLeader);
            projectDailyReportInfo.setCanComment(false);
            return projectDailyReportInfo;
        }
        ProjectDailyReportInfo projectDailyReportInfo = new ProjectDailyReportInfo();
        projectDailyReportInfo.setProjectDailyReportRecordId(projProjectDailyReportRecord.getProjectDailyReportRecordId());
        projectDailyReportInfo.setCustomName(projCustomInfo.getCustomName());
        projectDailyReportInfo.setProjectInfoId(projProjectDailyReportRecord.getProjectInfoId());
        projectDailyReportInfo.setProjectNumber(projProjectInfo.getProjectNumber());
        projectDailyReportInfo.setProjectDeliverStatus(projProjectInfo.getProjectDeliverStatus());
        projectDailyReportInfo.setProjectDeliverStatusDesc(this.convertProjectDeliverStatusDesc(projProjectInfo.getProjectDeliverStatus()));
        projectDailyReportInfo.setOnlineTime(projProjectInfo.getOnlineTime());
        projectDailyReportInfo.setOnlineTimeStr(projProjectInfo.getOnlineTime() == null ? "" : DateUtil.formatDateTime(projProjectInfo.getOnlineTime()));
        projectDailyReportInfo.setReportTitle(projProjectDailyReportRecord.getReportTitle());
        projectDailyReportInfo.setProjectType(projProjectInfo.getProjectType());
        projectDailyReportInfo.setProjectTypeDesc(this.convertProjectTypeDesc(projProjectInfo.getProjectType()));
        projectDailyReportInfo.setRiskLevel(projProjectDailyReportRecord.getRiskLevel());
        projectDailyReportInfo.setRiskLevelStr(this.convertRiskLevelStr(projProjectDailyReportRecord.getRiskLevel()));
        List<ProjProjectDailyReportDetailVO> projProjectDailyReportDetails = projProjectDailyReportDetailMapper.selectReportDetailByReportId(projProjectDailyReportRecord.getProjectDailyReportRecordId());

        List<ProjProjectDailyReportDetailVO> nodeList = configProjectDailyReportDetailItems.stream().map(item -> {
                    ProjProjectDailyReportDetailVO projProjectDailyReportDetailVO = projProjectDailyReportDetails.stream().filter(detailItem -> detailItem.getNodeCode().equals(item.getNodeCode())).findFirst().orElse(null);
                    if (projProjectDailyReportDetailVO == null) {
                        return new ProjProjectDailyReportDetailVO(null, item.getNodeCode(), item.getNodeName(), null);
                    }
                    return new ProjProjectDailyReportDetailVO(projProjectDailyReportDetailVO.getProjectDailyReportDetailId(), item.getNodeCode(), item.getNodeName(), projProjectDailyReportDetailVO.getReportDetailContent());
                }
        ).collect(Collectors.toList());
        projectDailyReportInfo.setNodeList(nodeList);
        List<ProjProjectDailyReportCommentsVO> projProjectDailyReportCommentsVOS = projProjectDailyReportCommentsMapper.selectReportCommentsByReportId(projProjectDailyReportRecord.getProjectDailyReportRecordId());
        projectDailyReportInfo.setCommentsList(projProjectDailyReportCommentsVOS);
        projectDailyReportInfo.setCommentsCount(projProjectDailyReportCommentsVOS.size());
        projectDailyReportInfo.setReportStatus(projProjectDailyReportRecord.getReportStatus());
        projectDailyReportInfo.setCanEdit(isProjectLeader && Integer.valueOf(1).equals(projProjectDailyReportRecord.getReportStatus()));
        projectDailyReportInfo.setCanComment(Integer.valueOf(2).equals(projProjectDailyReportRecord.getReportStatus()) || Integer.valueOf(3).equals(projProjectDailyReportRecord.getReportStatus()));
        return projectDailyReportInfo;
    }

    private String convertRiskLevelStr(Integer riskLevel) {
        if (Integer.valueOf(0).equals(riskLevel)) {
            return "无风险";
        } else if (Integer.valueOf(1).equals(riskLevel)) {
            return "低风险";
        } else if (Integer.valueOf(2).equals(riskLevel)) {
            return "中风险";
        } else {
            return "高风险";
        }
    }

    @Override
    @Transactional
    public boolean saveProjectDailyReportDetail(SaveProjectDailyReportParam param) {
        Date now = new Date();
        // 第一次保存，新增操作
        if (param.getProjectDailyReportRecordId() == null) {
            Long id = SnowFlakeUtil.getId();
            ProjProjectDailyReportRecord projProjectDailyReportRecord = new ProjProjectDailyReportRecord();
            projProjectDailyReportRecord.setProjectDailyReportRecordId(id);
            projProjectDailyReportRecord.setProjectInfoId(param.getProjectInfoId());
            projProjectDailyReportRecord.setReportTitle(param.getReportTitle());
            projProjectDailyReportRecord.setRiskLevel(param.getRiskLevel());
            projProjectDailyReportRecord.setReportContent(this.convertReportContent(param.getNodeList()));
            projProjectDailyReportRecord.setReportStatus(param.getReportStatus());
            if (Integer.valueOf(2).equals(param.getReportStatus())) {
                projProjectDailyReportRecord.setReportSendTime(now);
            }
            projProjectDailyReportRecord.setCreaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
            projProjectDailyReportRecord.setCreateTime(now);
            projProjectDailyReportRecord.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
            projProjectDailyReportRecord.setUpdateTime(now);

            projProjectDailyReportRecordMapper.insert(projProjectDailyReportRecord);

            for (ProjProjectDailyReportDetailVO item : param.getNodeList()) {
                ProjProjectDailyReportDetail projProjectDailyReportDetail = new ProjProjectDailyReportDetail();
                projProjectDailyReportDetail.setProjectDailyReportDetailId(SnowFlakeUtil.getId());
                projProjectDailyReportDetail.setProjectDailyReportRecordId(id);
                projProjectDailyReportDetail.setNodeCode(item.getNodeCode());
                projProjectDailyReportDetail.setReportDetailContent(item.getReportDetailContent());
                projProjectDailyReportDetail.setCreaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                projProjectDailyReportDetail.setCreateTime(now);
                projProjectDailyReportDetail.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                projProjectDailyReportDetail.setUpdateTime(now);
                projProjectDailyReportDetailMapper.insert(projProjectDailyReportDetail);
            }

        } else {
            // 修改操作
            ProjProjectDailyReportRecord dailyReport = projProjectDailyReportRecordMapper.getDailyReportById(param.getProjectDailyReportRecordId());
            UpdateProjProjectDailyReportRecord updateProjProjectDailyReportRecord = new UpdateProjProjectDailyReportRecord();
            updateProjProjectDailyReportRecord.setProjectDailyReportRecordId(param.getProjectDailyReportRecordId());
            updateProjProjectDailyReportRecord.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
            updateProjProjectDailyReportRecord.setReportTitle(param.getReportTitle());
            updateProjProjectDailyReportRecord.setRiskLevel(param.getRiskLevel());
            updateProjProjectDailyReportRecord.setReportContent(this.convertReportContent(param.getNodeList()));
            updateProjProjectDailyReportRecord.setReportStatus(param.getReportStatus());
            if (Integer.valueOf(2).equals(param.getReportStatus())) {
                updateProjProjectDailyReportRecord.setReportSendTime(now);
            }
            int i = projProjectDailyReportRecordMapper.updateDailyReportById(updateProjProjectDailyReportRecord);

            List<ProjProjectDailyReportDetailVO> projProjectDailyReportDetails = projProjectDailyReportDetailMapper.selectReportDetailByReportId(param.getProjectDailyReportRecordId());
            for (ProjProjectDailyReportDetailVO item : param.getNodeList()) {
                ProjProjectDailyReportDetailVO projProjectDailyReportDetailVO = projProjectDailyReportDetails.stream().filter(detailItem -> detailItem.getNodeCode().equals(item.getNodeCode())).findFirst().orElse(null);
                if (projProjectDailyReportDetailVO == null) {
                    ProjProjectDailyReportDetail projProjectDailyReportDetail = new ProjProjectDailyReportDetail();
                    projProjectDailyReportDetail.setProjectDailyReportDetailId(SnowFlakeUtil.getId());
                    projProjectDailyReportDetail.setProjectDailyReportRecordId(param.getProjectDailyReportRecordId());
                    projProjectDailyReportDetail.setNodeCode(item.getNodeCode());
                    projProjectDailyReportDetail.setReportDetailContent(item.getReportDetailContent());
                    projProjectDailyReportDetail.setCreaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                    projProjectDailyReportDetail.setCreateTime(now);
                    projProjectDailyReportDetail.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                    projProjectDailyReportDetail.setUpdateTime(now);
                    int insert = projProjectDailyReportDetailMapper.insert(projProjectDailyReportDetail);
                } else {
                    UpdateProjProjectDailyReportDetail updateProjProjectDailyReportDetail = new UpdateProjProjectDailyReportDetail();
                    updateProjProjectDailyReportDetail.setProjectDailyReportDetailId(projProjectDailyReportDetailVO.getProjectDailyReportDetailId());
                    updateProjProjectDailyReportDetail.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                    updateProjProjectDailyReportDetail.setReportDetailContent(item.getReportDetailContent());
                    int i1 = projProjectDailyReportDetailMapper.updateReportDetailById(updateProjProjectDailyReportDetail);
                }
            }
        }

        // 发送日报
        if (Integer.valueOf(2).equals(param.getReportStatus())) {
            try {
                ProjProjectDailyReportRecord projProjectDailyReportRecord1 = projProjectDailyReportRecordMapper.selectOne(
                        new QueryWrapper<ProjProjectDailyReportRecord>()
                                .eq("project_daily_report_record_id", param.getProjectDailyReportRecordId())
                );
                ProjProjectInfo projectInfo = projectInfoMapper.selectByPrimaryKey(projProjectDailyReportRecord1.getProjectInfoId());
                SysUser sysUser = sysUserMapper.selectOne(
                        new QueryWrapper<SysUser>()
                                .eq("is_deleted", 0)
                                .eq("sys_user_id", projectInfo.getProjectLeaderId())
                );
                SysDept sysDept = sysDeptMapper.selectOne(
                        new QueryWrapper<SysDept>()
                                .eq("is_deleted", 0)
                                .eq("dept_yunying_id", sysUser.getDeptId())
                );
                ProjCustomInfo projCustomInfo = projCustomInfoMapper.selectByPrimaryKey(projectInfo.getCustomInfoId());
                Map<String, String> messageContentParam = new HashMap<>();
                messageContentParam.put("deptName", sysDept.getDeptName());
                messageContentParam.put("leaderName", sysUser.getUserName());
                messageContentParam.put("customName", projCustomInfo.getCustomName());
                messageContentParam.put("date", DateUtil.format(projProjectDailyReportRecord1.getReportSendTime(), DatePattern.CHINESE_DATE_PATTERN));
                SendMessageParam messageParam = new SendMessageParam();
                messageParam.setMessageTypeId(100021L);
                messageParam.setProjectInfoId(projProjectDailyReportRecord1.getProjectInfoId());
                messageParam.setMessageContentParam(messageContentParam);
                List<Long> leaderIds = new ArrayList<>();
                this.getDeptLeaderYunyingIds(sysDept.getDeptYunyingId(), leaderIds);
                messageParam.setSysUserIds(leaderIds);

                Long sysUserId = userHelper.getCurrentSysUserIdWithDefaultValue();
                String domain = currentUrl.endsWith("/") ? currentUrl : currentUrl + "/";
                // 业务链接地址
                String businessUrl = domain + "projectDailyWrite?projectInfoId=" + projProjectDailyReportRecord1.getProjectInfoId() + "&reportSendTime=" + DateUtil.formatDate(now);
                // 记录实际业务跳转路径
                Long msgInfoId = messageInfoService.insert(businessUrl, sysUserId);
                String url = weChatAuthUrl + "?state=" + msgInfoId;
                messageParam.setUrl(url);
                messageService.sendMessage2(messageParam);
            } catch (Exception e) {
                log.error("评论时发送项目日报评论消息，发生异常，日报ID={}，errMsg={}，stackInfo=", param.getProjectDailyReportRecordId(), e.getMessage(), e);
            }
        }
        // 发送日报
        return true;
    }

    private String convertReportContent(List<ProjProjectDailyReportDetailVO> list) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            ProjProjectDailyReportDetailVO item = list.get(i);
            result.append(i + 1).append(". ").append(item.getNodeName()).append("：").append(StringUtils.isBlank(item.getReportDetailContent()) ? "" : item.getReportDetailContent()).append("。");
        }
        return result.toString();
    }

    public void getDeptLeaderYunyingIds(Long deptId, List<Long> leaderIds) {
        // 查找当前部门对象
        SysDept dept = sysDeptMapper.selectOne(
                new QueryWrapper<SysDept>()
                        .eq("is_deleted", 0)
                        .eq("dept_yunying_id", deptId)
        );

        if (dept != null && !"众阳健康".equals(dept.getDeptName())) {
            SysUser sysUser = sysUserMapper.selectOne(
                    new QueryWrapper<SysUser>()
                            .eq("is_deleted", 0)
                            .eq("user_yunying_id", dept.getDeptLeaderYunyingId())
            );
            if (sysUser != null && sysUser.getSysUserId() != null) {
                // 添加当前部门的运营负责人账号
                leaderIds.add(sysUser.getSysUserId());
            }

            // 如果有上级部门，则递归调用获取上级部门
            if (dept.getPid() != null) {
                getDeptLeaderYunyingIds(dept.getPid(), leaderIds);
            }
        }
    }

    @Override
    @Transactional
    public boolean saveProjectDailyReportComments(SaveProjProjectDailyReportCommentsParam param) {
        Date nowDate = new Date();

        ProjProjectDailyReportComments projProjectDailyReportComments = new ProjProjectDailyReportComments();
        projProjectDailyReportComments.setProjProjectDailyReportCommentsId(SnowFlakeUtil.getId());
        projProjectDailyReportComments.setProjectDailyReportRecordId(param.getProjectDailyReportRecordId());
        projProjectDailyReportComments.setReportCommentsContent(param.getReportCommentsContent());
        projProjectDailyReportComments.setCreaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
        projProjectDailyReportComments.setCreateTime(nowDate);
        projProjectDailyReportComments.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
        projProjectDailyReportComments.setUpdateTime(nowDate);
        projProjectDailyReportComments.setIsDeleted(0);
        int insert = projProjectDailyReportCommentsMapper.insert(projProjectDailyReportComments);

        UpdateProjProjectDailyReportRecord updateProjProjectDailyReportRecord = new UpdateProjProjectDailyReportRecord();
        updateProjProjectDailyReportRecord.setReportStatus(3);
        updateProjProjectDailyReportRecord.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
        updateProjProjectDailyReportRecord.setProjectDailyReportRecordId(param.getProjectDailyReportRecordId());
        int update = projProjectDailyReportRecordMapper.updateDailyReportById(updateProjProjectDailyReportRecord);

        try {
            ProjProjectDailyReportRecord projProjectDailyReportRecord1 = projProjectDailyReportRecordMapper.selectOne(new QueryWrapper<ProjProjectDailyReportRecord>().eq("project_daily_report_record_id", param.getProjectDailyReportRecordId()));
            ProjProjectInfo projectInfo = projectInfoMapper.selectByPrimaryKey(projProjectDailyReportRecord1.getProjectInfoId());
            ProjCustomInfo projCustomInfo = projCustomInfoMapper.selectByPrimaryKey(projectInfo.getCustomInfoId());
            SysUserVO currentUser = userHelper.getCurrentUser();
            Map<String, String> messageContentParam = new HashMap<>();
            messageContentParam.put("leaderName", currentUser.getUserName());
            messageContentParam.put("projectName", projCustomInfo.getCustomName());
            messageContentParam.put("date", DateUtil.formatDate(projProjectDailyReportRecord1.getReportSendTime()));
            SendMessageParam messageParam = new SendMessageParam();
            messageParam.setMessageTypeId(100020L);
            messageParam.setProjectInfoId(projProjectDailyReportRecord1.getProjectInfoId());
            messageParam.setMessageContentParam(messageContentParam);

            Long sysUserId = userHelper.getCurrentSysUserIdWithDefaultValue();
            String domain = currentUrl.endsWith("/") ? currentUrl : currentUrl + "/";
            // 业务链接地址
            String businessUrl = domain + "projectDailyWrite?projectInfoId=" + projProjectDailyReportRecord1.getProjectInfoId() + "&reportSendTime=" + DateUtil.formatDate(nowDate);
            // 记录实际业务跳转路径
            Long msgInfoId = messageInfoService.insert(businessUrl, sysUserId);
            String url = weChatAuthUrl + "?state=" + msgInfoId;
            messageParam.setUrl(url);
            messageService.sendMessage2(messageParam);
        } catch (Exception e) {
            log.error("评论时发送项目日报评论消息，发生异常，日报ID={}，errMsg={}，stackInfo=", param.getProjectDailyReportRecordId(), e.getMessage(), e);
        }
        return true;
    }

    @Override
    public PageList<ProjectDailyReportRecordVO> getProjectDailyReportRecord(GetProjectDailyReportRecordParam param) {

        ProjCustomInfoDTO projCustomInfoDTO = new ProjCustomInfoDTO();
        projCustomInfoDTO.setPageNum(1);
        projCustomInfoDTO.setPageSize(9999);
        long startTime1 = System.currentTimeMillis();
        log.info("查询项目日报填写记录，查询客户列表开始");
        Result<PageInfo<ProjCustomInfoVO>> pageInfoResult = projCustomInfoService.selectCustomInfoList(projCustomInfoDTO);
        log.info("查询项目日报填写记录，查询客户列表结束，耗时={}", System.currentTimeMillis() - startTime1);
        if (pageInfoResult == null || pageInfoResult.getData() == null || CollectionUtils.isEmpty(pageInfoResult.getData().getList())) {
            return PageUtil.getPageList(new ArrayList<>(), param.getPageNum(), param.getPageSize());
        }


        // 配置的不同项目状态的日报节点
        List<ConfigProjectDailyReportDetailItem> configProjectDailyReportDetailItems = configProjectDailyReportDetailItemMapper.getConfigProjectDailyReportDetailItem(null);

        Long currentSysUserIdWithDefaultValue = userHelper.getCurrentSysUserIdWithDefaultValue();
        // 项目状态集合
        List<Integer> projectDeliverStatusList = configProjectDailyReportDetailItems.stream().map(ConfigProjectDailyReportDetailItem::getProjectDeliverStatus).distinct().collect(Collectors.toList());

        List<ProjectDailyReportRecordVO> projectDailyReportRecord = new ArrayList<>();

        GetProjectDailyReportRecordParamPO paramPO = new GetProjectDailyReportRecordParamPO();
        paramPO.setCustomName(param.getCustomName());
        paramPO.setOnlineTimeStart(param.getOnlineTimeStart() == null ? null : DateUtil.beginOfDay(DateUtil.parse(param.getOnlineTimeStart())));
        paramPO.setOnlineTimeEnd(param.getOnlineTimeEnd() == null ? null : DateUtil.endOfDay(DateUtil.parse(param.getOnlineTimeEnd())));
        paramPO.setReportSendTimeStart(param.getReportSendTimeStart() == null ? null : DateUtil.beginOfDay(DateUtil.parse(param.getReportSendTimeStart())));
        paramPO.setReportSendTimeEnd(param.getReportSendTimeEnd() == null ? null : DateUtil.endOfDay(DateUtil.parse(param.getReportSendTimeEnd())));
        paramPO.setReportStatus(param.getReportStatus());
        paramPO.setDeliverStatusList(projectDeliverStatusList);

        // 需要提交日报的项目
        List<ProjectDailyReportRecordVO> needSubmitDailyReportProject = projProjectDailyReportRecordMapper.getNeedWriteDailyReportProject(paramPO);
        needSubmitDailyReportProject = this.dealNeedSubmitDailyReportProject(needSubmitDailyReportProject);

        GetProjectDailyReportRecordParamPO2 param2 = new GetProjectDailyReportRecordParamPO2();
        param2.setDeliverStatusList(projectDeliverStatusList);
        param2.setReportSendTimeStart(DateUtil.beginOfDay(new Date()));
        param2.setReportSendTimeEnd(DateUtil.endOfDay(new Date()));

        // 查询当天的日报填写记录
        List<ProjProjectDailyReportRecord> submitProjectDailyReportRecord = projProjectDailyReportRecordMapper.getWriteProjectDailyReportRecord(param2);
        List<Long> finishedProjectInfoId = submitProjectDailyReportRecord.stream().filter(item -> item != null && item.getProjectInfoId() != null).map(ProjProjectDailyReportRecord::getProjectInfoId).collect(Collectors.toList());

        // 当天没有填写日报的数据
        List<ProjectDailyReportRecordVO> collect = needSubmitDailyReportProject.stream().filter(item -> !finishedProjectInfoId.contains(item.getProjectInfoId())).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(collect)) {
            collect.forEach(item -> {
                item.setReportStatus(0);
            });
            projectDailyReportRecord.addAll(collect);
        }

        List<ProjectDailyReportRecordVO> reportRecord = projProjectDailyReportRecordMapper.getProjectDailyReportRecord(paramPO);
        if (CollectionUtils.isNotEmpty(reportRecord)) {
            projectDailyReportRecord.addAll(reportRecord);
        }

        if (CollectionUtils.isNotEmpty(projectDailyReportRecord)) {
            List<Long> customInfoIdList = pageInfoResult.getData().getList().stream().map(ProjCustomInfoVO::getCustomInfoId).collect(Collectors.toList());

            projectDailyReportRecord = projectDailyReportRecord.stream().filter(item -> customInfoIdList.contains(item.getCustomInfoId())).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(projectDailyReportRecord)) {
            projectDailyReportRecord.forEach(item -> {
                item.setOnlineTimeStr(item.getOnlineTime() == null ? null : DateUtil.format(item.getOnlineTime(), "yyyy-MM-dd HH:mm"));
                item.setReportSendTimeStr(item.getReportSendTime() == null ? null : DateUtil.format(item.getReportSendTime(), "yyyy-MM-dd HH:mm"));
                item.setCanEdit(currentSysUserIdWithDefaultValue.equals(item.getProjectLeaderId()) && this.convertCanEdit(item.getReportStatus()));
                item.setProjectTypeDesc(this.convertProjectTypeDesc(item.getProjectType()));
                item.setProjectDeliverStatusDesc(convertProjectDeliverStatusDesc(item.getProjectDeliverStatus()));
                item.setReportDateStr(item.getCreateTime() == null ? DateUtil.format(new Date(), "yyyy-MM-dd") : DateUtil.format(item.getCreateTime(), "yyyy-MM-dd"));
            });
        }

        if (CollectionUtils.isNotEmpty(projectDailyReportRecord) && param.getReportStatus() != null) {
            projectDailyReportRecord = projectDailyReportRecord.stream().filter(item -> param.getReportStatus().equals(item.getReportStatus())).collect(Collectors.toList());
        }


        PageList<ProjectDailyReportRecordVO> pageList = PageUtil.getPageList(projectDailyReportRecord, param.getPageNum(), param.getPageSize());
        log.info("项目日报查询得到的分页结果={}", JSON.toJSONString(pageList));
        return pageList;
    }

    /**
     * 提取代码  将用户对于项目的筛选权限提取出来
     *
     * @param dto
     * @return
     */
    public ProjProjectInfoDTO handleProjProjectInfoDTO(ProjProjectInfoDTO dto) {
        SysUserVO sysUserVO = userHelper.getCurrentUser();
        List<SysRoleDTO> resRoles = sysRoleMapper.selectRoleByUserId(sysUserVO.getSysUserId());
        // 当前登录用户的角色编码
        Set<String> currentUserRoleCodeSet = resRoles.stream().map(SysRoleDTO::getRoleCode).collect(Collectors.toSet());
        //if (Arrays.stream(ROLE_CODE_ADMIN).anyMatch(currentUserRoleCodeSet::contains) || Arrays.stream
        // (ROLE_CODE_DEPT).anyMatch(currentUserRoleCodeSet::contains)) {
        if (Arrays.stream(ROLE_CODE_ADMIN).anyMatch(currentUserRoleCodeSet::contains)) {
            dto.setProjectMemberId(null);
        } else {
            dto.setProjectMemberId(sysUserVO.getSysUserId());
        }

        // 当前登录用户单独指定的拥有查询权限的部门ID
        List<String> dataRangeList = new ArrayList<>();
        for (SysRoleDTO sysRoleDTO : resRoles) {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(sysRoleDTO.getDataRange())) {
                dataRangeList.addAll(Arrays.asList(sysRoleDTO.getDataRange().split(",")));
            }
        }

        // 如果单独指定的拥有查询权限的部门ID不是空的，遍历获取所有对应部门以及对应部门的的下属部门
        if (!org.springframework.util.CollectionUtils.isEmpty(dataRangeList)) {
            // 将部门ID转为Long类型
            Set<Long> needQueryDeptIdSet = dataRangeList.stream().map(Long::valueOf).collect(Collectors.toSet());

            // 所有部门
            List<SysDept> allDeptList = sysDeptMapper.selectList(new QueryWrapper<SysDept>().eq("is_deleted", 0));

            List<SysDept> needQueryDept = getDeptList(needQueryDeptIdSet, allDeptList);

            List<Long> collect = needQueryDept.stream().map(SysDept::getDeptYunyingId).distinct().collect(Collectors.toList());
            log.info("获取项目信息时，获取查询部门以及下属部门={}", collect);

            dto.setDataRange(collect);
        }
        return dto;
    }

    // 获取给定部门ID获取所有的对应部门以及下属部门（包括该部门本身）
    public List<SysDept> getDeptList(Set<Long> finalQuery, List<SysDept> allCategories) {
        List<SysDept> result = new ArrayList<>();
        for (Long id : finalQuery) {
            findDeptList(id, result, allCategories);
        }
        return result;
    }

    // 递归查找下级部门
    private void findDeptList(Long id, List<SysDept> result, List<SysDept> allCategories) {
        for (SysDept category : allCategories) {
            if (id.equals(category.getPid())) {
                result.add(category); // 找到子分类，加入结果
                findDeptList(category.getDeptYunyingId(), result, allCategories); // 递归查找该子分类的下级分类
            }
        }
    }

    @Resource
    private ProjCustomInfoService projCustomInfoService;

    @Resource
    private SysRoleMapper sysRoleMapper;

    private boolean convertCanEdit(Integer reportStatus) {
        if (Integer.valueOf(0).equals(reportStatus)) {
            return true;
        }
        return Integer.valueOf(1).equals(reportStatus);
    }

    private String convertProjectTypeDesc(Integer projectType) {
        if (Integer.valueOf(1).equals(projectType)) {
            return "单体";
        }
        return "区域";
    }

    private String convertProjectDeliverStatusDesc(Integer projectDeliverStatus) {
        if (Integer.valueOf(1).equals(projectDeliverStatus)) {
            return "已派工";
        }
        if (Integer.valueOf(2).equals(projectDeliverStatus)) {
            return "已调研";
        }
        if (Integer.valueOf(3).equals(projectDeliverStatus)) {
            return "已入驻";
        }
        if (Integer.valueOf(4).equals(projectDeliverStatus)) {
            return "准备完成";
        }
        if (Integer.valueOf(5).equals(projectDeliverStatus)) {
            return "已上线";
        }
        if (Integer.valueOf(6).equals(projectDeliverStatus)) {
            return "已验收";
        }
        return "已启动";
    }

    @Override
    @Transactional
    public boolean sendProjectDailyReportMessage() {
        redisUtil.set("project_daily_report_message_task", "project_daily_report_message_task", 20, TimeUnit.MINUTES);

        try {
            SysConfig config = sysConfigMapper.selectConfigByName("PROJECT_DAILY_REPORT_REMIND");
            if (config == null || StringUtils.isBlank(config.getConfigValue())) {
                return false;
            }
            String[] timeArray = config.getConfigValue().split(",");
            Date now = new Date();
            for (String time : timeArray) {
                Date timeDate = DateUtil.parse(DateUtil.formatDate(new Date()) + " " + time, "yyyy-MM-dd HH:mm");
                Date startTime = DateUtil.beginOfMinute(DateUtil.offsetMinute(timeDate, -3));
                Date endTime = DateUtil.endOfMinute(DateUtil.offsetMinute(timeDate, 3));
                if (now.after(startTime) && now.before(endTime)) {
                    sendMessage();
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.error("发送项目日报提交提醒，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        } finally {
            redisUtil.del("project_daily_report_message_task");
        }
        return false;
    }

    private void sendMessage() {
        Date now = new Date();
        // 配置的不同项目状态的日报节点
        List<ConfigProjectDailyReportDetailItem> configProjectDailyReportDetailItems = configProjectDailyReportDetailItemMapper.selectList(null);

        // 项目状态集合
        List<Integer> projectDeliverStatusList = configProjectDailyReportDetailItems.stream().map(ConfigProjectDailyReportDetailItem::getProjectDeliverStatus).distinct().collect(Collectors.toList());

        GetProjectDailyReportRecordParamPO2 paramPO = new GetProjectDailyReportRecordParamPO2();
        paramPO.setDeliverStatusList(projectDeliverStatusList);
        paramPO.setReportSendTimeStart(DateUtil.beginOfDay(now));
        paramPO.setReportSendTimeEnd(DateUtil.endOfDay(now));

        List<ProjectDailyReportRecordVO> projectDailyReportRecord = this.projectDailyReportRecord(paramPO);
        if (CollectionUtils.isEmpty(projectDailyReportRecord)) {
            log.info("没有需要填写日报的项目");
            return;
        }
        List<ProjectDailyReportRecordVO> collect = projectDailyReportRecord.stream().filter(item -> !StringUtils.isBlank(item.getProjectLeaderAccount())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(collect)) {
            log.info("没有符合条件的填写日报的项目");
            return;
        }

        collect.forEach(item -> {
            Map<String, String> messageContentParam = new HashMap<>();
            messageContentParam.put("customName", item.getCustomName());
            messageContentParam.put("projectNumber", item.getProjectNumber());
            SendMessageParam messageParam = new SendMessageParam();
            messageParam.setMessageTypeId(100022L);
            messageParam.setProjectInfoId(item.getProjectInfoId());
            messageParam.setMessageContentParam(messageContentParam);
            List<Long> leaderIds = new ArrayList<>();
            leaderIds.add(item.getProjectLeaderId());
            messageParam.setSysUserIds(leaderIds);

            Long sysUserId = item.getProjectLeaderId();
            String domain = currentUrl.endsWith("/") ? currentUrl : currentUrl + "/";
            // 业务链接地址
            String businessUrl = domain + "projectDailyWrite?projectInfoId=" + item.getProjectInfoId() + "&reportSendTime=" + DateUtil.formatDate(now);
            // 记录实际业务跳转路径
            Long msgInfoId = messageInfoService.insert(businessUrl, sysUserId);
            String url = weChatAuthUrl + "?state=" + msgInfoId;
            messageParam.setUrl(url);
            messageService.sendMessage2(messageParam);
        });
    }

    private List<ProjectDailyReportRecordVO> projectDailyReportRecord(GetProjectDailyReportRecordParamPO2 paramPO) {
        List<ProjectDailyReportRecordVO> needSubmitDailyReportProject = projProjectDailyReportRecordMapper.getSendDailyReportMessageProject(paramPO.getDeliverStatusList());
        needSubmitDailyReportProject = this.dealNeedSubmitDailyReportProject(needSubmitDailyReportProject);
        List<ProjProjectDailyReportRecord> submitProjectDailyReportRecord = projProjectDailyReportRecordMapper.getSentProjectDailyReportRecord(paramPO);
        List<Long> finishedProjectInfoId = submitProjectDailyReportRecord.stream().filter(item -> item != null && item.getProjectInfoId() != null).map(ProjProjectDailyReportRecord::getProjectInfoId).collect(Collectors.toList());
        List<ProjectDailyReportRecordVO> collect = needSubmitDailyReportProject.stream().filter(item -> !finishedProjectInfoId.contains(item.getProjectInfoId())).collect(Collectors.toList());
        return collect;
    }

    private List<ProjectDailyReportRecordVO> dealNeedSubmitDailyReportProject(List<ProjectDailyReportRecordVO> needSubmitDailyReportProject) {
        if (CollectionUtils.isEmpty(needSubmitDailyReportProject)) {
            return new ArrayList<>();
        }

        int offset = 30;

        try {
            SysConfig config = sysConfigMapper.selectConfigByName("PROJECT_DAILY_REPORT_OFFSET_DAY");
            if (config != null && StringUtils.isNotBlank(config.getConfigValue())) {
                offset = Integer.parseInt(config.getConfigValue());
            }
        } catch (NumberFormatException e) {
            log.error("项目日报上线超过多少天的不再推送消息提醒，获取天数配置，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }

        int finalOffset = offset;
        return needSubmitDailyReportProject.stream().filter(item -> {
            if (item != null && item.getOnlineTime() != null) {
                DateTime dateTime = DateUtil.offsetDay(item.getOnlineTime(), finalOffset);
                return !dateTime.before(new Date());
            }
            return true;
        }).collect(Collectors.toList());
    }

}

