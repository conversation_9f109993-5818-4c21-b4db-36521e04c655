package com.msun.csm.service.dict;

import java.util.List;

import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.dict.DictCity;
import com.msun.csm.model.dto.DictCityDTO;
import com.msun.csm.model.vo.DictCityVO;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/25
 */

public interface DictCityService {

    int deleteByPrimaryKey(Long dictCityId);

    int insert(DictCity record);

    int insertOrUpdate(DictCity record);

    int insertOrUpdateSelective(DictCity record);

    int insertSelective(DictCity record);

    DictCity selectByPrimaryKey(Long dictCityId);

    int updateByPrimaryKeySelective(DictCity record);

    int updateByPrimaryKey(DictCity record);

    int updateBatch(List<DictCity> list);

    int updateBatchSelective(List<DictCity> list);

    int batchInsert(List<DictCity> list);

    /**
     * 查询城市列表
     *
     * @param dto
     * @return
     */
    Result<List<DictCityVO>> selectCityList(DictCityDTO dto);
}
