package com.msun.csm.service.dict;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.dict.DictProduct;
import com.msun.csm.dao.entity.dict.DictProductLog;
import com.msun.csm.dao.entity.dict.DictProductVsArrange;
import com.msun.csm.dao.mapper.dict.DictProductLogMapper;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.dao.mapper.dict.DictProductVsArrangeMapper;
import com.msun.csm.model.dto.DictProductVsArrangeDTO;
import com.msun.csm.model.vo.dict.DictProductVsArrangeVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.util.SnowFlakeUtil;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/24
 */

@Service
public class DictProductVsArrangeServiceImpl implements DictProductVsArrangeService {

    @Resource
    private DictProductVsArrangeMapper dictProductVsArrangeMapper;

    @Resource
    private DictProductLogMapper dictProductLogMapper;

    @Resource
    private UserHelper userHelper;

    @Resource
    private DictProductMapper dictProductMapper;

    @Override
    public int deleteByPrimaryKey(Long contrastId) {
        return dictProductVsArrangeMapper.deleteByPrimaryKey(contrastId);
    }

    @Override
    public int insert(DictProductVsArrange record) {
        return dictProductVsArrangeMapper.insert(record);
    }

    @Override
    public int insertOrUpdate(DictProductVsArrange record) {
        return dictProductVsArrangeMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(DictProductVsArrange record) {
        return dictProductVsArrangeMapper.insertOrUpdateSelective(record);
    }

    @Override
    public int insertSelective(DictProductVsArrange record) {
        return dictProductVsArrangeMapper.insertSelective(record);
    }

    @Override
    public DictProductVsArrange selectByPrimaryKey(Long contrastId) {
        return dictProductVsArrangeMapper.selectByPrimaryKey(contrastId);
    }

    @Override
    public int updateByPrimaryKeySelective(DictProductVsArrange record) {
        return dictProductVsArrangeMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(DictProductVsArrange record) {
        return dictProductVsArrangeMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<DictProductVsArrange> list) {
        return dictProductVsArrangeMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<DictProductVsArrange> list) {
        return dictProductVsArrangeMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<DictProductVsArrange> list) {
        return dictProductVsArrangeMapper.batchInsert(list);
    }

    /**
     * 查询工单产品对照部署产品列表
     * @param orderProductName
     * @param arrangeProductName
     * @return
     */
    @Override
    public Result<List<DictProductVsArrangeVO>> findDictProductVsArrangeList(String orderProductName, String arrangeProductName) {
        return Result.success(dictProductVsArrangeMapper.findDictProductVsArrangeList(orderProductName, arrangeProductName));
    }

    /**
     * 新增工单产品与部署产品对照
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result saveDictProductVsArrange(DictProductVsArrangeDTO dto) {
        List<DictProductVsArrange> arrangeList = dictProductVsArrangeMapper.selectList(new QueryWrapper<DictProductVsArrange>()
                .eq("order_product_id", dto.getOrderProductId())
                .eq("arrange_product_id", dto.getArrangeProductId()));
        if (CollectionUtil.isNotEmpty(arrangeList)) {
            return Result.fail("当前对照关系已存在，请勿重复添加！");
        }
        DictProductVsArrange dictProductVsArrange = new DictProductVsArrange();
        BeanUtils.copyProperties(dto, dictProductVsArrange);
        dictProductVsArrange.setProductVsArrangeId(SnowFlakeUtil.getId());
        dictProductVsArrangeMapper.insert(dictProductVsArrange);
        //保存日志
        DictProduct orderProduct = dictProductMapper.selectOne(new QueryWrapper<DictProduct>()
                .eq("yy_product_id", dto.getOrderProductId()));
        DictProduct arrangeProduct = dictProductMapper.selectOne(new QueryWrapper<DictProduct>()
                .eq("yy_product_id", dto.getArrangeProductId()));
        DictProductLog dictProductLog = new DictProductLog();
        dictProductLog.setDictProductLogId(SnowFlakeUtil.getId());
        dictProductLog.setOperateModule("arrange");
        dictProductLog.setOperateModuleName("工单产品与部署产品对照");
        dictProductLog.setOperateType(1);
        dictProductLog.setOperateContent("新增工单产品与部署产品对照,工单产品：" + orderProduct.getProductName()
                + ",部署产品：" + arrangeProduct.getProductName());
        dictProductLog.setCreateName(userHelper.getCurrentUser().getUserName());
        dictProductLogMapper.insert(dictProductLog);
        return Result.success();
    }

    /**
     * 删除工单产品与部署产品对照
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result deleteDictProductVsArrange(DictProductVsArrangeDTO dto) {
        DictProductVsArrange dictProductVsArrange = dictProductVsArrangeMapper.selectById(dto.getProductVsArrangeId());
        dictProductVsArrangeMapper.deleteById(dto.getProductVsArrangeId());

        //保存日志
        String orderProductName = "";
        DictProduct orderProduct = dictProductMapper.selectOne(new QueryWrapper<DictProduct>()
                .eq("yy_product_id", dictProductVsArrange.getOrderProductId()));
        if (ObjectUtil.isNotEmpty(orderProduct)) {
            orderProductName = orderProduct.getProductName();
        }
        String arrangeProductName = "";
        DictProduct arrangeProduct = dictProductMapper.selectOne(new QueryWrapper<DictProduct>()
                .eq("yy_product_id", dictProductVsArrange.getArrangeProductId()));
        if (ObjectUtil.isNotEmpty(arrangeProduct)) {
            arrangeProductName = arrangeProduct.getProductName();
        }
        DictProductLog dictProductLog = new DictProductLog();
        dictProductLog.setDictProductLogId(SnowFlakeUtil.getId());
        dictProductLog.setOperateModule("arrange");
        dictProductLog.setOperateModuleName("工单产品与部署产品对照");
        dictProductLog.setOperateType(3);
        dictProductLog.setOperateContent("删除工单产品与部署产品对照,工单产品：" + orderProductName
                + ",部署产品：" + arrangeProductName);
        dictProductLog.setCreateName(userHelper.getCurrentUser().getUserName());
        dictProductLogMapper.insert(dictProductLog);
        return Result.success();
    }
}
