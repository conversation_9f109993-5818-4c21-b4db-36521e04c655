package com.msun.csm.service.dict;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.msun.csm.dao.entity.dict.DictEquipType;
import com.msun.csm.dao.mapper.dict.DictEquipTypeMapper;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/10
 */

@Service
public class DictEquipTypeServiceImpl implements DictEquipTypeService {

    @Autowired
    private DictEquipTypeMapper dictEquipTypeMapper;

    @Override
    public int deleteByPrimaryKey(Long equipTypeId) {
        return dictEquipTypeMapper.deleteByPrimaryKey(equipTypeId);
    }

    @Override
    public int insert(DictEquipType record) {
        return dictEquipTypeMapper.insert(record);
    }

    @Override
    public int insertOrUpdate(DictEquipType record) {
        return dictEquipTypeMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(DictEquipType record) {
        return dictEquipTypeMapper.insertOrUpdateSelective(record);
    }

    @Override
    public int insertSelective(DictEquipType record) {
        return dictEquipTypeMapper.insertSelective(record);
    }

    @Override
    public DictEquipType selectByPrimaryKey(Long equipTypeId) {
        return dictEquipTypeMapper.selectByPrimaryKey(equipTypeId);
    }

    @Override
    public int updateByPrimaryKeySelective(DictEquipType record) {
        return dictEquipTypeMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(DictEquipType record) {
        return dictEquipTypeMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<DictEquipType> list) {
        return dictEquipTypeMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<DictEquipType> list) {
        return dictEquipTypeMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<DictEquipType> list) {
        return dictEquipTypeMapper.batchInsert(list);
    }

}
