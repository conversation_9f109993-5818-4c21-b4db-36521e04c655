package com.msun.csm.service.proj;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.constants.ObsExpireTimeConsts;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.productempower.AuthorizationTypeEnum;
import com.msun.csm.common.enums.productempower.DbAuthFlagEnum;
import com.msun.csm.common.enums.productempower.ProductEmpowerScopeEnum;
import com.msun.csm.common.enums.projprojectinfo.ProjectTypeEnums;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.dict.DictProduct;
import com.msun.csm.dao.entity.dict.DictProductVsEmpower;
import com.msun.csm.dao.entity.dict.DictProductVsEmpowerRelative;
import com.msun.csm.dao.entity.proj.ProductInfo;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfoRelative;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.productempower.ProjProductEmpowerAddRecord;
import com.msun.csm.dao.entity.proj.productempower.ProjProductEmpowerAddRecordDetail;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.dao.mapper.dict.DictProductVsEmpowerMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProductEmpowerAddRecordDetailMapper;
import com.msun.csm.dao.mapper.proj.ProjProductEmpowerAddRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectFileMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.model.dto.empower.CancelEmpowerRecord;
import com.msun.csm.model.dto.empower.EmpowerRecord;
import com.msun.csm.model.dto.empower.ProductEmpowerAddRecordProductDto;
import com.msun.csm.model.dto.empower.ProjProductEmpowerAddRecordDto;
import com.msun.csm.model.dto.empower.ProjProductEmpowerAddRecordSaveDto;
import com.msun.csm.model.dto.productempower.ProductEmpowerDTO;
import com.msun.csm.model.dto.productempower.ProductEmpowerImplDTO;
import com.msun.csm.model.vo.dict.DictValue;
import com.msun.csm.model.vo.dict.DictValuePath;
import com.msun.csm.model.vo.productempower.ProjProductEmpowerAddRecordVO;
import com.msun.csm.service.common.CommonService;
import com.msun.csm.service.common.ExceptionMessageService;
import com.msun.csm.service.operlog.SysOperLogService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderHospitalService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderProductService;
import com.msun.csm.util.PageHelperUtil;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.StringUtils;
import com.msun.csm.util.obs.OBSClientUtils;

/**
 * <AUTHOR>
 * @since 2024-09-27 10:09:59
 */
@Slf4j
@Service
public class ProjProductEmpowerAddRecordServiceImpl implements ProjProductEmpowerAddRecordService {

    @Resource
    private ProjProductEmpowerAddRecordMapper productEmpowerAddRecordMapper;

    @Resource
    private ProjProductEmpowerAddRecordDetailMapper productEmpowerAddRecordDetailMapper;

    @Resource
    private ProjApplyOrderProductService applyOrderProductService;


    @Resource
    private ProjOrderProductService orderProductService;

    @Resource
    private ProjProjectInfoMapper projectInfoMapper;

    @Resource
    private ProjApplyOrderHospitalService applyOrderHospitalService;

    @Resource
    private DictProductVsEmpowerMapper dictProductVsEmpowerMapper;

    @Resource
    private ProjCustomInfoMapper customInfoMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private DictProductMapper dictProductMapper;

    @Resource
    private ProjCustomInfoMapper projCustomInfoMapper;

    @Resource
    private ProjProjectFileMapper projectFileMapper;

    @Resource
    private SysOperLogService sysOperLogService;

    @Resource
    private ExceptionMessageService exceptionMessageService;

    @Resource
    private CommonService commonService;

    /**
     * 查询客户信息（所有）
     *
     * @return List<DictValue>
     */
    public List<DictValue> findCustomInfo() {
        List<ProjCustomInfo> customInfoList = projCustomInfoMapper.selectList(new QueryWrapper<>());
        return customInfoList.stream().map(item -> DictValue.builder().id(StringUtils.nvl(item.getCustomInfoId())).name(item.getCustomName()).build()).collect(Collectors.toList());
    }

    @Resource
    private ProjHospitalInfoMapper hospitalInfoMapper;

    @Override
    public Result<PageInfo<ProjProductEmpowerAddRecordVO>> findProductEmpowerAddRecord(ProjProductEmpowerAddRecordDto dto) {
        List<ProjProductEmpowerAddRecord> relatives =
                PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(),
                        page -> productEmpowerAddRecordMapper.selectList(
                                new QueryWrapper<ProjProductEmpowerAddRecord>()
                                        .eq(ObjectUtil.isNotEmpty(dto.getCustomInfoId()), "custom_info_id", dto.getCustomInfoId())
                                        .eq(ObjectUtil.isNotEmpty(dto.getAuthorizationType()), "authorization_type", dto.getAuthorizationType())
                                        .orderByDesc("create_time")
                        )
                );
        PageInfo<ProjProductEmpowerAddRecord> pageInfo = new PageInfo<>(relatives);
        List<ProjProductEmpowerAddRecordVO> relativeVOS = relatives.stream().map(e -> BeanUtil.copyProperties(e,
                ProjProductEmpowerAddRecordVO.class)).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(relativeVOS)) {
            for (ProjProductEmpowerAddRecordVO relativeVO : relativeVOS) {
                // 查询明细表数据
                List<ProjProductEmpowerAddRecordDetail> details =
                        productEmpowerAddRecordDetailMapper.selectList(new QueryWrapper<ProjProductEmpowerAddRecordDetail>().eq("product_empower_add_record_id", relativeVO.getProductEmpowerAddRecordId()));
                ProjCustomInfo customInfo = customInfoMapper.selectOne(new QueryWrapper<ProjCustomInfo>().eq(
                        "custom_info_id", relativeVO.getCustomInfoId()));
                // 填写客户名称
                if (ObjectUtil.isNotEmpty(customInfo)) {
                    relativeVO.setCustomName(customInfo.getCustomName());
                }
                // 设置操作人
                if (ObjectUtil.isNotEmpty(relativeVO.getCreaterId())) {
                    SysUser sysUser = sysUserMapper.selectOne(new QueryWrapper<SysUser>().eq("sys_user_id",
                            relativeVO.getCreaterId()));
                    if (ObjectUtil.isNotEmpty(sysUser)) {
                        relativeVO.setCreaterName(sysUser.getUserName());
                    } else {
                        relativeVO.setCreaterName("系统自动授权");
                    }
                }
                // 设置医院
                if (StrUtil.isNotBlank(relativeVO.getHospitalIds())) {
                    List<ProjHospitalInfo> hospitalInfos =
                            hospitalInfoMapper.selectBatchIds(Arrays.stream(relativeVO.getHospitalIds().split(StrUtil.COMMA)).map(Long::parseLong).collect(Collectors.toList()));
                    if (CollUtil.isNotEmpty(hospitalInfos)) {
                        relativeVO.setHospitalIdNames(hospitalInfos.stream().map(e -> {
                            BaseIdNameResp baseIdNameResp = new BaseIdNameResp();
                            baseIdNameResp.setName(e.getHospitalName());
                            baseIdNameResp.setId(e.getHospitalInfoId());
                            return baseIdNameResp;
                        }).collect(Collectors.toList()));
                    }
                }
                // 设置授权时间范围
                String startTime = ObjectUtil.isNotEmpty(relativeVO.getAuthorizationStartTime())
                        ? DateUtil.formatDateTime(relativeVO.getAuthorizationStartTime()) : null;
                String compTime = ObjectUtil.isNotEmpty(relativeVO.getAuthorizationCompTime())
                        ? DateUtil.formatDateTime(relativeVO.getAuthorizationCompTime()) : null;
                if (!StrUtil.isAllBlank(startTime, compTime)) {
                    relativeVO.setAuthorizationTime(CollUtil.newArrayList(startTime, compTime));
                } else {
                    relativeVO.setAuthorizationTime(CollUtil.newArrayList());
                }
                // 授权类型
                AuthorizationTypeEnum authorizationTypeEnum =
                        AuthorizationTypeEnum.getEnumByCode(relativeVO.getAuthorizationType());
                if (ObjectUtil.isNotEmpty(authorizationTypeEnum)) {
                    assert authorizationTypeEnum != null;
                    relativeVO.setAuthorizationTypeName(authorizationTypeEnum.getDesc());
                }
                // 获取上传的文件
                if (StrUtil.isNotBlank(relativeVO.getProjectFileId())) {
                    List<DictValuePath> dictValuePaths = CollUtil.newArrayList();
                    relativeVO.setCertificate(dictValuePaths);
                    List<Long> projectFileIds =
                            CollUtil.newArrayList(relativeVO.getProjectFileId().split(StrUtil.COMMA)).stream().map(Long::parseLong).collect(Collectors.toList());
                    List<ProjProjectFile> projectFiles =
                            projectFileMapper.selectList(new QueryWrapper<ProjProjectFile>().in("project_file_id",
                                    projectFileIds));
                    if (CollUtil.isNotEmpty(projectFiles)) {
                        relativeVO.setCertificate(projectFiles.stream().map(e -> DictValuePath.builder().id(StrUtil.toString(e.getProjectFileId())).name(e.getFileName()).url(OBSClientUtils.getTemporaryUrl(e.getFilePath(), ObsExpireTimeConsts.SEVEN_DAY)).build()).collect(Collectors.toList()));
                    }
                }
                // 设置产品id集合
                List<Long> yyProductIds =
                        details.stream().map(ProjProductEmpowerAddRecordDetail::getYyProductId).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(yyProductIds)) {
                    relativeVO.setYyProductIds(details.stream().map(e -> StrUtil.toString(e.getYyProductId())).collect(Collectors.toList()));
                    // 查询产品名称
                    List<DictProduct> dictProducts = dictProductMapper.selectList(new QueryWrapper<DictProduct>().in(
                            "yy_product_id", yyProductIds));
                    if (CollUtil.isNotEmpty(dictProducts)) {
                        StringBuilder productNames = new StringBuilder();
                        for (DictProduct dictProduct : dictProducts) {
                            productNames.append(dictProduct.getProductName()).append(StrUtil.COMMA);
                        }
                        relativeVO.setYyProductNames(productNames.toString());
                    }
                }
                // 处理明细
                if (CollUtil.isNotEmpty(details)) {
                    handleDetail(details, relativeVO);
                }
            }
        }
        PageInfo<ProjProductEmpowerAddRecordVO> pageInfoVO = new PageInfo<>();
        pageInfoVO.setList(relativeVOS);
        pageInfoVO.setTotal(pageInfo.getTotal());
        return Result.success(pageInfoVO);
    }

    /**
     * 拼接明细表数据
     *
     * @param details    子表内容
     * @param relativeVO 主表内容
     */
    public void handleDetail(List<ProjProductEmpowerAddRecordDetail> details,
                             ProjProductEmpowerAddRecordVO relativeVO) {
        // 设置授权产品id集合
        StringBuilder moduleCodes = new StringBuilder();
        StringBuilder moduleNames = new StringBuilder();
        // 菜单字典
        List<String> dictVsEmpowerIds = CollUtil.newArrayList();
        details.forEach(e -> {
            if (StrUtil.isNotBlank(e.getProductModuleCodes())) {
                List<String> modules = CollUtil.newArrayList(e.getProductModuleCodes().split(StrUtil.COMMA));
                for (String moduleCode : modules) {
                    moduleCodes.append(moduleCode).append(StrUtil.COMMA);
                }
                // 查询模块名称
                List<DictProductVsEmpower> dictProductVsEmpowers =
                        dictProductVsEmpowerMapper.selectList(new QueryWrapper<DictProductVsEmpower>().eq(
                                "order_product_id", e.getYyProductId()).in("msun_health_module_code", modules));
                if (CollUtil.isNotEmpty(dictProductVsEmpowers)) {
                    for (DictProductVsEmpower dictProductVsEmpower : dictProductVsEmpowers) {
                        moduleNames.append(dictProductVsEmpower.getMsunHealthModule()).append(StrUtil.COMMA);
                    }
                }
                // 处理授权菜单范]
                if (relativeVO.getProductEmpowerScope() == ProductEmpowerScopeEnum.MODULE.getCode()) {
                    // 获取授权对照字典
                    if (StrUtil.isNotBlank(e.getDictVsEmpowerIds())) {
                        List<Long> ids =
                                CollUtil.newArrayList(e.getDictVsEmpowerIds().split(StrUtil.COMMA)).stream().map(Long::parseLong).collect(Collectors.toList());
                        List<DictProductVsEmpowerRelative> dictProductVsEmpowerRelatives =
                                dictProductVsEmpowerMapper.selectListRelative(ids);
                        if (CollUtil.isNotEmpty(dictProductVsEmpowerRelatives)) {
                            dictVsEmpowerIds.addAll(dictProductVsEmpowerRelatives.stream().map(f -> StrUtil.toString(f.getProductVsEmpowerId())).collect(Collectors.toList()));
                        }
                    }
                }
            }
        });
        relativeVO.setDictVsEmpowerIds(dictVsEmpowerIds);
        relativeVO.setProductModuleNames(moduleNames.toString());
        relativeVO.setProductModuleCodes(moduleCodes.toString());
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Result<List<EmpowerRecord>> save(ProjProductEmpowerAddRecordSaveDto dto) {
        // 验证必填项
        validated(dto);
        DbAuthFlagEnum dbAuthFlagEnum = DbAuthFlagEnum.getEnumByCode(dto.getDbAuthFlag());
        if (ObjectUtil.isEmpty(dbAuthFlagEnum)) {
            throw new CustomException("未查询到对应的数据库开放权限标识.");
        }
        // 设置授权开始结束时间
        dto.setTime();
        // 根据客户和实施类型查询需要授权的产品
        List<ProductInfo> productInfos = findProduct(dto.getImplementationType(), dto.getCustomInfoId());
        if (CollUtil.isEmpty(productInfos)) {
            throw new CustomException("未查询到产品信息.");
        }
        if (ObjectUtil.isEmpty(dto.getProductEmpowerAddRecordId())) {
            // 新增
            return addProductEmpowerRecord(dto, productInfos);
        }
        // 更新
        return updateProductEmpowerRecord(dto, productInfos);
    }

    private void validated(ProjProductEmpowerAddRecordSaveDto dto) {
        if (dto.getProductEmpowerScope() == ProductEmpowerScopeEnum.MODULE.getCode()) {
            if (CollUtil.isEmpty(dto.getDictVsEmpowerIds())) {
                throw new CustomException("授权菜单不能为空.");
            }
            // 若授权菜单, 不对数据库做处理
            dto.setDbAuthFlag(DbAuthFlagEnum.DONT_HANDLE.getCode());
        } else {
            if (CollUtil.isEmpty(dto.getYyProductIds())) {
                throw new CustomException("授权产品不能为空.");
            }
            if (ObjectUtil.isEmpty(dto.getDbAuthFlag())) {
                throw new CustomException("数据库权限标识`不能为空.");
            }
        }
    }

    /**
     * 查询产品根据实施类型和客户id
     *
     * @param implementationType 实施类型 1. 单体, 2. 区域
     * @param customInfoId       客户id
     * @return List<ProductInfo>
     */
    public List<ProductInfo> findProduct(Integer implementationType, Long customInfoId) {
        List<ProjProjectInfo> projectInfos = projectInfoMapper.selectList(new QueryWrapper<ProjProjectInfo>().eq(
                "custom_info_id", customInfoId).eq("project_type", implementationType));
        if (CollUtil.isEmpty(projectInfos)) {
            throw new CustomException("未查询到客户相关的项目信息.");
        }
        // 过滤项目id
        List<Long> projectInfoIds =
                projectInfos.stream().map(ProjProjectInfo::getProjectInfoId).collect(Collectors.toList());
        // 过滤要授权的产品
        List<ProductInfo> productInfos = orderProductService.findProductListByCustomInfoId(customInfoId);
        if (CollUtil.isEmpty(productInfos)) {
            throw new CustomException("未查询到客户产品信息.");
        }
        // 过滤对应的实施类型产品
        productInfos =
                productInfos.stream().filter(e -> projectInfoIds.stream().anyMatch(f -> f.longValue() == e.getProjectInfoId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(productInfos)) {
            throw new CustomException("未查询到客户项目相关产品信息.");
        }
        return productInfos;
    }

    @Override
    public Result<List<DictValue>> findProductNeedEmpower(ProductEmpowerAddRecordProductDto dto) {
        List<ProductInfo> productInfos = findProduct(dto.getImplementationType(), dto.getCustomInfoId());
        if (CollUtil.isEmpty(productInfos)) {
            return Result.success(CollUtil.newArrayList());
        }
        List<DictValue> dictValues =
                productInfos.stream().map(e -> DictValue.builder().id(StrUtil.toString(e.getYyOrderProductId())).name(e.getProductName()).build()).collect(Collectors.toList());
        return Result.success(dictValues);
    }

    @Override
    public Result<List<DictValue>> findProductModuleNeedEmpower() {
        List<DictProductVsEmpowerRelative> dictProductVsEmpowerRelatives =
                dictProductVsEmpowerMapper.selectListRelative(null);
        if (CollUtil.isEmpty(dictProductVsEmpowerRelatives)) {
            return Result.success(CollUtil.newArrayList());
        }
        return Result.success(toDictValueProductModule(dictProductVsEmpowerRelatives));
    }

    @Override
    public void cancelAuth() {
        String nowDateTime = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATE_PATTERN);
        // 查询已经到期的新增授权记录
        List<ProjProductEmpowerAddRecord> records =
                productEmpowerAddRecordMapper.selectList(new QueryWrapper<ProjProductEmpowerAddRecord>()
                        .isNotNull(
                                "authorization_comp_time").lt("authorization_comp_time",
                                DateUtil.parse(nowDateTime))
                        .eq("authorization_type", AuthorizationTypeEnum.ADD_AUTHORIZATION.getCode()).and(wrapper -> {
                            wrapper.eq("option_flag", NumberEnum.NO_0.num()).or().isNull("option_flag");
                        }));
        if (CollUtil.isEmpty(records)) {
            log.info("未查询到待取消的授权记录. time: {}", nowDateTime);
            return;
        }
        List<CancelEmpowerRecord> eventList = CollUtil.newArrayList();
        log.info("开始取消授权记录. time: {}", nowDateTime);
        // 轮询取消授权
        for (ProjProductEmpowerAddRecord record : records) {
            log.info("待取消授权的记录: {}", record);
            CancelEmpowerRecord cancelEmpowerRecord = CancelEmpowerRecord.builder().build();
            eventList.add(cancelEmpowerRecord);
            String message = StrUtil.EMPTY;
            // 取消授权结果集
            List<EmpowerRecord> errorEmpower = CollUtil.newArrayList();
            String keyword = StrUtil.EMPTY;
            ProjCustomInfo customInfo = commonService.getCustomInfo(record.getCustomInfoId());
            if (ObjectUtil.isNotEmpty(customInfo)) {
                message = keyword
                        + customInfo.getCustomName() + StrUtil.DASHED
                        + ProjectTypeEnums.getEnum(record.getImplementationType()).getName()
                        + StrUtil.COMMA + StrUtil.SPACE;
            }
            try {
                Result<List<EmpowerRecord>> result = cancelAuthEach(record);
                // 取消授权结果描述
                if (ObjectUtil.isEmpty(result)) {
                    message += "取消授权返回值异常.";
                    log.error("取消授权返回值异常. result: {}", result);
                    setEventRecord(cancelEmpowerRecord, NumberEnum.NO_0.num(), record, message);
                    continue;
                }
                if (CollUtil.isEmpty(result.getData())) {
                    message += "取消授权返回值中结果异常.";
                    log.error("取消授权返回值中结果异常. result: {}", result);
                    setEventRecord(cancelEmpowerRecord, NumberEnum.NO_0.num(), record, message);
                    continue;
                }
                if (!result.isSuccess()) {
                    message += "取消授权未成功.";
                    log.error("取消授权未成功. result: {}", result);
                    setEventRecord(cancelEmpowerRecord, NumberEnum.NO_0.num(), record, message);
                    continue;
                }
                // 排查所有医院是否授权成功
                for (EmpowerRecord empowerRecord : result.getData()) {
                    if (empowerRecord.getStatus() == NumberEnum.NO_0.num()) {
                        errorEmpower.add(empowerRecord);
                    }
                }
                if (CollUtil.isNotEmpty(errorEmpower)) {
                    message += "存在医院取消授权失败.";
                    log.error("存在医院取消授权失败. result: {}", errorEmpower);
                    setEventRecord(cancelEmpowerRecord, NumberEnum.NO_0.num(), record, message);
                } else {
                    message += "授权成功.";
                    log.info("授权成功. require: {}", record);
                    setEventRecord(cancelEmpowerRecord, NumberEnum.NO_1.num(), record, message);
                }
                cancelEmpowerRecord.setEmpowerRecords(result.getData());
            } catch (Throwable e) {
                message += "授权异常.";
                log.error("取消授权异常. message: {}, e=", e.getMessage(), e);
                setEventRecord(cancelEmpowerRecord, NumberEnum.NO_0.num(), record, message);
            }
        }
        List<CancelEmpowerRecord> errRecords =
                eventList.stream().filter(e -> e.getStatus() == NumberEnum.NO_0.num().intValue()).collect(Collectors.toList());
        List<CancelEmpowerRecord> rightRecords =
                eventList.stream().filter(e -> e.getStatus() == NumberEnum.NO_1.num().intValue()).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(errRecords)) {
            sysOperLogService.apiOperLogInsert(errRecords, "定时取消授权异常", StrUtil.EMPTY, Log.LogOperType.ADD.getCode());
            StringBuilder message = new StringBuilder();
            for (CancelEmpowerRecord errRecord : errRecords) {
                message.append(errRecord.getMessage());
            }
            exceptionMessageService.sendToSystemManager(-1L, "定时取消授权异常. " + message);
            // 更新失败标识
            updateOptionFlag(errRecords, NumberEnum.NO_0.num());
        }
        if (CollUtil.isNotEmpty(rightRecords)) {
            // 更新成功标识
            updateOptionFlag(rightRecords, NumberEnum.NO_1.num());
        }
    }

    /**
     * 更新操作标识
     *
     * @param cancelEmpowerRecords 取消授权记录
     * @param optionFlag           标识
     */
    private void updateOptionFlag(List<CancelEmpowerRecord> cancelEmpowerRecords, Integer optionFlag) {
        for (CancelEmpowerRecord record : cancelEmpowerRecords) {
            ProjProductEmpowerAddRecord addRecord = new ProjProductEmpowerAddRecord();
            addRecord.setProductEmpowerAddRecordId(record.getAddRecord().getProductEmpowerAddRecordId());
            addRecord.setOptionFlag(optionFlag);
            productEmpowerAddRecordMapper.updateById(addRecord);
        }
    }

    /**
     * 设置时间记录
     *
     * @param cancelEmpowerRecord 事件集合
     * @param status              授权状态
     * @param addRecord           申请记录
     */
    private void setEventRecord(CancelEmpowerRecord cancelEmpowerRecord, Integer status,
                                ProjProductEmpowerAddRecord addRecord, String message) {
        cancelEmpowerRecord.setAddRecord(addRecord);
        cancelEmpowerRecord.setMessage(message);
        cancelEmpowerRecord.setStatus(status);
    }

    /**
     * 取消授权, 单条记录
     *
     * @param record 单条记录
     */
    private Result<List<EmpowerRecord>> cancelAuthEach(ProjProductEmpowerAddRecord record) {
        // 查询产品明细
        List<ProjProductEmpowerAddRecordDetail> addRecordDetails =
                productEmpowerAddRecordDetailMapper.selectList(new QueryWrapper<ProjProductEmpowerAddRecordDetail>().eq(
                        "product_empower_add_record_id", record.getProductEmpowerAddRecordId()));
        if (CollUtil.isEmpty(addRecordDetails)) {
            log.error("取消授权");
            throw new CustomException("取消授权时未查询到产品.");
        }
        // 转运营产品id集合
        List<Long> yyProductIds =
                addRecordDetails.stream().map(ProjProductEmpowerAddRecordDetail::getYyProductId).collect(Collectors.toList());
        // 获取上传图片
        List<DictValuePath> certificate = null;
        if (StrUtil.isNotBlank(record.getProjectFileId())) {
            certificate =
                    Arrays.stream(record.getProjectFileId().split(StrUtil.COMMA)).map(e -> DictValuePath.builder()
                            .id(e)
                            .build()).collect(Collectors.toList());
        }
        // 获取授权菜单
        List<Long> dictVsEmpowerIds = CollUtil.newArrayList();
        if (record.getProductEmpowerScope() == ProductEmpowerScopeEnum.MODULE.getCode()) {
            for (ProjProductEmpowerAddRecordDetail addRecordDetail : addRecordDetails) {
                if (StrUtil.isNotBlank(addRecordDetail.getDictVsEmpowerIds())) {
                    dictVsEmpowerIds.addAll(Arrays.stream(addRecordDetail.getDictVsEmpowerIds()
                            .split(StrUtil.COMMA)).map(Long::parseLong).collect(Collectors.toList()));
                }
            }
        }
        List<Long> hospitalInfoList = CollUtil.newArrayList();
        // 获取医院信息
        if (StrUtil.isNotBlank(record.getHospitalIds())) {
            hospitalInfoList.addAll(Arrays.stream(record.getHospitalIds().split(StrUtil.COMMA)).map(Long::parseLong).collect(Collectors.toList()));
        }
        // 取消授权.
        ProjProductEmpowerAddRecordSaveDto saveDto = ProjProductEmpowerAddRecordSaveDto.builder()
                .dbAuthFlag(DbAuthFlagEnum.DONT_HANDLE.getCode())
                .productEmpowerScope(record.getProductEmpowerScope())
                .authorizationReason("定时取消授权: 取消授权时间: " + DateUtil.now() + ", 授权期限: "
                        + (ObjectUtil.isNotEmpty(record.getAuthorizationStartTime())
                        ? DateUtil.formatDate(record.getAuthorizationStartTime()) : StrUtil.EMPTY)
                        + " ~ "
                        + (ObjectUtil.isNotEmpty(record.getAuthorizationCompTime())
                        ? DateUtil.formatDate(record.getAuthorizationCompTime()) : StrUtil.EMPTY))
                .yyProductIds(yyProductIds)
                .certificate(certificate)
                .customInfoId(record.getCustomInfoId())
                .implementationType(record.getImplementationType())
                .projectFileId(record.getProjectFileId())
                .authorizationType(AuthorizationTypeEnum.CANCAL_AUTHORIZATION.getCode())
                .dictVsEmpowerIds(dictVsEmpowerIds)
                .hospitalInfoIdList(hospitalInfoList)
                .build();
        log.info("本次取消授权的医院有, count: {}, 分别是: {}", hospitalInfoList.size(), hospitalInfoList);
        log.info("定时取消授权开始. request: {}", saveDto);
        Result<List<EmpowerRecord>> result = productEmpowerAddRecordService.save(saveDto);
        log.info("定时取消授权结束. request: {}, result: {}", record, result);
        return result;
    }

    @Resource
    private ProjProductEmpowerAddRecordService productEmpowerAddRecordService;

    /**
     * 转换产品模块为id, name方式
     *
     * @param dictProductVsEmpowerRelatives 查询结果
     * @return List<DictValue>
     */
    public static List<DictValue> toDictValueProductModule(List<DictProductVsEmpowerRelative> dictProductVsEmpowerRelatives) {
        return dictProductVsEmpowerRelatives.stream().map(e -> DictValue.builder().id(StrUtil.toString(e.getProductVsEmpowerId())).name(e.getProductName() + "(" + e.getOrderProductId() + ") - " + e.getMsunHealthModule() + "(" + e.getMsunHealthModuleCode() + ")").build()).collect(Collectors.toList());
    }

    /**
     * 更新产品授权记录
     *
     * @param dto 请求参数
     * @return Result<String
     */
    public Result<List<EmpowerRecord>> updateProductEmpowerRecord(ProjProductEmpowerAddRecordSaveDto dto,
                                                                  List<ProductInfo> productInfos) {
        int count =
                productEmpowerAddRecordDetailMapper.delete(new QueryWrapper<ProjProductEmpowerAddRecordDetail>().eq(
                        "product_empower_add_record_id", dto.getProductEmpowerAddRecordId()));
        log.info("删除产品授权记录明细. count: {}", count);
        count = productEmpowerAddRecordMapper.delete(new QueryWrapper<ProjProductEmpowerAddRecord>().eq(
                "product_empower_add_record_id", dto.getProductEmpowerAddRecordId()));
        log.info("删除产品授权记录. count: {}", count);
        return addProductEmpowerRecord(dto, productInfos);
    }

    /**
     * 新增产品授权记录
     *
     * @param dto 请求参数
     * @return 结果
     */
    public Result<List<EmpowerRecord>> addProductEmpowerRecord(ProjProductEmpowerAddRecordSaveDto dto,
                                                               List<ProductInfo> productInfos) {
        // 新增补录主表信息
        ProjProductEmpowerAddRecord addRecord = insertRecord(dto);
        // 查询医院
        List<ProjHospitalInfo> openHospitalInfo =
                hospitalInfoMapper.selectBatchIds(Arrays.stream(addRecord.getHospitalIds().split(StrUtil.COMMA)).map(Long::parseLong).collect(Collectors.toList()));
        log.info("授权指定医院: {}",
                openHospitalInfo.stream().map(e -> e.getHospitalName() + "(" + e.getCloudHospitalId() + ")").collect(Collectors.toList()));
        if (dto.getProductEmpowerScope() == ProductEmpowerScopeEnum.MODULE.getCode()) {
            return moduleEmpower(addRecord, productInfos, dto, openHospitalInfo);
        } else {
            return productEmpower(addRecord, productInfos, dto, openHospitalInfo);
        }
    }

    @Resource
    private ProjHospitalInfoService hospitalInfoService;

    /**
     * 模块授权
     *
     * @param addRecord    主表记录
     * @param productInfos 产品信息
     * @param dto          传参
     */
    private Result<List<EmpowerRecord>> moduleEmpower(ProjProductEmpowerAddRecord addRecord,
                                                      List<ProductInfo> productInfos,
                                                      ProjProductEmpowerAddRecordSaveDto dto,
                                                      List<ProjHospitalInfo> openedHospitalInfos) {
        Long projectInfoId = productInfos.get(0).getProjectInfoId();
        // 获取对应的运营产品id
        List<DictProductVsEmpower> dictProductVsEmpowers =
                dictProductVsEmpowerMapper.selectList(new QueryWrapper<DictProductVsEmpower>().in(
                        "product_vs_empower_id", dto.getDictVsEmpowerIds()));
        // 获取授权产品信息
        List<ProductEmpowerDTO> productEmpowerDTOS =
                dictProductVsEmpowers.stream().map(e -> ProductEmpowerDTO.builder().yyOrderProductId(e.getOrderProductId()).msunHealthModule(e.getMsunHealthModule()).msunHealthModuleCode(e.getMsunHealthModuleCode()).build()).collect(Collectors.toList());
        if (CollUtil.isEmpty(dictProductVsEmpowers)) {
            throw new CustomException("未查询到授权产品字段信息.");
        }
        List<Long> yyProductIds =
                CollUtil.newArrayList(dictProductVsEmpowers.stream().map(DictProductVsEmpower::getOrderProductId).collect(Collectors.toSet()));
        // 新增补录表信息
        insertRecordDetail(addRecord, yyProductIds, productEmpowerDTOS, dictProductVsEmpowers);
        // 更新明细表信息
        updateDetail(openedHospitalInfos, projectInfoId, addRecord, yyProductIds);
        ProductEmpowerImplDTO implDTO =
                ProductEmpowerImplDTO.builder().hospitalInfos(openedHospitalInfos).yyProductIds(yyProductIds).productEmpowerDTOS(productEmpowerDTOS).dbAuthFlagEnum(DbAuthFlagEnum.getEnumByCode(dto.getDbAuthFlag())).openAuth(dto.getAuthorizationType() == AuthorizationTypeEnum.ADD_AUTHORIZATION.getCode()).productEmpowerScope(dto.getProductEmpowerScope()).implementationType(addRecord.getImplementationType()).customInfoId(addRecord.getCustomInfoId()).projectInfoId(-1L).build();
        return empowerImpl(implDTO);
    }

    /**
     * 产品授权
     *
     * @param addRecord    主表记录
     * @param productInfos 产品信息
     * @param dto          传参
     */
    public Result<List<EmpowerRecord>> productEmpower(ProjProductEmpowerAddRecord addRecord,
                                                      List<ProductInfo> productInfos,
                                                      ProjProductEmpowerAddRecordSaveDto dto,
                                                      List<ProjHospitalInfo> openedHospitalInfos) {
        // 根据请求产品过滤
        productInfos =
                productInfos.stream().filter(e -> dto.getYyProductIds().stream().anyMatch(f -> f.longValue() == e.getYyOrderProductId().longValue())).collect(Collectors.toList());
        if (CollUtil.isEmpty(productInfos)) {
            throw new CustomException("未查询到客户相关运营产品信息.");
        }
        log.info("待授权的运营产品id. {}",
                productInfos.stream().map(ProductInfo::getYyOrderProductId).collect(Collectors.toList()));
        // 根据产品查询工单
        Map<Long, List<Long>> projectYyInfoIdMap = MapUtil.newHashMap();
        // 分组产品id按照项目id
        for (ProductInfo productInfo : productInfos) {
            if (!projectYyInfoIdMap.containsKey(productInfo.getProjectInfoId())) {
                projectYyInfoIdMap.put(productInfo.getProjectInfoId(),
                        CollUtil.newArrayList(productInfo.getYyOrderProductId()));
            } else {
                projectYyInfoIdMap.get(productInfo.getProjectInfoId()).add(productInfo.getYyOrderProductId());
            }
        }
        if (MapUtil.isEmpty(projectYyInfoIdMap)) {
            throw new CustomException("授权类型异常.");
        }
        // 异常医院收集
        List<EmpowerRecord> empExMsgList = CollUtil.newArrayList();
        for (Long projectInfoId : projectYyInfoIdMap.keySet()) {
            List<Long> yyProductIds = projectYyInfoIdMap.get(projectInfoId);
            // 检测是否符合开通条件, 只对医院检测, 不对产品检测, 产品可以重复开通
            Result<Boolean> canOpenProductResult = applyOrderProductService.canOpenProduct(projectInfoId, false, true);
            if (canOpenProductResult.isSuccess() && !canOpenProductResult.getData()) {
                log.warn("项目id: {}, 不能不符合产品授权开通条件, 原因: {}", projectInfoId, canOpenProductResult.getMsg());
                ProjProjectInfo projectInfo = projectInfoMapper.selectOne(new QueryWrapper<ProjProjectInfo>().eq(
                        "project_info_id", projectInfoId));
                EmpowerRecord empowerRecord =
                        EmpowerRecord.builder().status(NumberEnum.NO_0.num()).message(projectInfo.getProjectName() + StrUtil.DASHED + projectInfo.getProjectNumber() + "不符合产品授权开通条件, 原因: " + ProjOrderProductServiceImpl.getExMsgSubscribe(canOpenProductResult.getMsg())).build();
                empExMsgList.add(empowerRecord);
                continue;
            }
            // 获取授权产品信息, 跳过分院产品
            List<ProductEmpowerDTO> productEmpowerDTOS =
                    orderProductService.findProjEmpowerProductRecord(projectInfoId, yyProductIds);
            // 新增补录表信息
            insertRecordDetail(addRecord, yyProductIds, productEmpowerDTOS, null);
            // 更新明细表信息
            updateDetail(openedHospitalInfos, projectInfoId, addRecord, yyProductIds);
            // 产品授权
            ProductEmpowerImplDTO implDTO =
                    ProductEmpowerImplDTO.builder().projectInfoId(projectInfoId).hospitalInfos(openedHospitalInfos).yyProductIds(yyProductIds).productEmpowerDTOS(productEmpowerDTOS).dbAuthFlagEnum(DbAuthFlagEnum.getEnumByCode(dto.getDbAuthFlag())).openAuth(dto.getAuthorizationType() == AuthorizationTypeEnum.ADD_AUTHORIZATION.getCode()).productEmpowerScope(dto.getProductEmpowerScope()).implementationType(addRecord.getImplementationType()).customInfoId(addRecord.getCustomInfoId()).build();
            // 搜集医院信息
            Result<List<EmpowerRecord>> result = empowerImpl(implDTO);
            if (ObjectUtil.isNotEmpty(result)) {
                empExMsgList.addAll(result.getData());
            } else {
                log.info("未采集授权记录信息. projectInfoId: {}", projectInfoId);
            }
        }
        log.info("采集授权记录结果: {}", empExMsgList);
        return Result.success(empExMsgList);
    }

    /**
     * 授权
     *
     * @param implDTO 请求参数
     * @return Result<String>
     */
    private Result<List<EmpowerRecord>> empowerImpl(ProductEmpowerImplDTO implDTO) {
        try {
            return orderProductService.applyEmpowers(implDTO);
        } catch (Throwable e) {
            log.error("手动授权异常. message: {}, e=", e.getMessage(), e);
            throw new CustomException("手动授权异常.");
        }
    }

    /**
     * 新增补录表记录
     *
     * @param dto 请求参数
     */
    private ProjProductEmpowerAddRecord insertRecord(ProjProductEmpowerAddRecordSaveDto dto) {
        ProjProductEmpowerAddRecord addRecord = new ProjProductEmpowerAddRecord();
        BeanUtils.copyProperties(dto, addRecord);
        addRecord.setProductEmpowerAddRecordId(SnowFlakeUtil.getId());
        // 保存文件id
        if (CollUtil.isNotEmpty(dto.getCertificate())) {
            StringBuilder projectFileIds = new StringBuilder();
            for (DictValuePath valuePath : dto.getCertificate()) {
                projectFileIds.append(valuePath.getId()).append(StrUtil.COMMA);
            }
            addRecord.setProjectFileId(projectFileIds.toString());
        }
        // 获取要授权的医院
        String hospitalIds;
        List<ProjHospitalInfoRelative> openedHospitalInfos =
                hospitalInfoService.findOpenHospitalInfo(dto.getCustomInfoId(),
                        dto.getImplementationType());
        // 验证指定的医院是否为开通状态
        if (CollUtil.isEmpty(openedHospitalInfos)) {
            log.error("根据项目Id未查询到已开通的医院. customInfoId: {}, projectType: {}", dto.getCustomInfoId(),
                    dto.getImplementationType());
            throw new CustomException("未查询到已开通的医院.");
        }
        if (CollUtil.isNotEmpty(dto.getHospitalInfoIdList())) {
            // 验证医院是否已开通
            List<ProjHospitalInfoRelative> tmpOpenedHospitalInfos =
                    openedHospitalInfos.stream().filter(e -> dto.getHospitalInfoIdList().stream().anyMatch(f -> f.longValue() == e.getHospitalInfoId())).collect(Collectors.toList());
            if (CollUtil.isEmpty(tmpOpenedHospitalInfos)) {
                log.error("未检测到已开通的医院. tmpOpenedHospitalInfos: {}, dto.hospitalInfoIdList: {}, openHospitalList: {}",
                        tmpOpenedHospitalInfos,
                        dto.getHospitalInfoIdList(),
                        openedHospitalInfos);
                throw new CustomException("未查询到已开通的医院.");
            }
            StringBuilder ids = new StringBuilder();
            for (ProjHospitalInfoRelative relative : tmpOpenedHospitalInfos) {
                ids.append(StrUtil.toString(relative.getHospitalInfoId())).append(StrUtil.COMMA);
            }
            hospitalIds = ids.toString();
        } else {
            StringBuilder ids = new StringBuilder();
            for (ProjHospitalInfoRelative relative : openedHospitalInfos) {
                ids.append(StrUtil.toString(relative.getHospitalInfoId())).append(StrUtil.COMMA);
            }
            hospitalIds = ids.toString();
        }
        if (StrUtil.isBlank(hospitalIds)) {
            log.error("未筛查到已开通的医院. dto: {}", dto);
            throw new CustomException("未查询到已开通的医院.");
        }
        addRecord.setHospitalIds(hospitalIds);
        // 添加开通的医院记录
        int count = productEmpowerAddRecordMapper.insert(addRecord);
        log.info("新增产品授权记录. count: {}", count);
        return addRecord;
    }

    /**
     * 新增记录明细
     *
     * @param addRecord    主表记录
     * @param yyProductIds 产品id
     */
    private void insertRecordDetail(ProjProductEmpowerAddRecord addRecord, List<Long> yyProductIds,
                                    List<ProductEmpowerDTO> productEmpowerDTOS,
                                    List<DictProductVsEmpower> dictProductVsEmpowers) {
        // 设置并新增明细表
        Map<Long, StringBuilder> empowerModuleCodeMap = null;
        Map<Long, StringBuilder> dictProductVsEmpowerIdMap = null;
        if (CollUtil.isNotEmpty(productEmpowerDTOS)) {
            // 存记录明细, 获取yyProductId及对应的模块编码序列
            empowerModuleCodeMap = MapUtil.newHashMap();
            dictProductVsEmpowerIdMap = MapUtil.newHashMap();
            for (ProductEmpowerDTO productEmpowerDTO : productEmpowerDTOS) {
                // 拼接授权菜单编码
                if (!empowerModuleCodeMap.containsKey(productEmpowerDTO.getYyOrderProductId())) {
                    StringBuilder stringBuilder = new StringBuilder();
                    stringBuilder.append(productEmpowerDTO.getMsunHealthModuleCode()).append(StrUtil.COMMA);
                    empowerModuleCodeMap.put(productEmpowerDTO.getYyOrderProductId(), stringBuilder);
                } else {
                    empowerModuleCodeMap.get(productEmpowerDTO.getYyOrderProductId()).append(productEmpowerDTO.getMsunHealthModuleCode()).append(StrUtil.COMMA);
                }
                // 拼接授权字典id
                if (addRecord.getProductEmpowerScope() == ProductEmpowerScopeEnum.MODULE.getCode()) {
                    if (!dictProductVsEmpowerIdMap.containsKey(productEmpowerDTO.getYyOrderProductId())) {
                        StringBuilder stringBuilder = new StringBuilder();
                        List<Long> vsIds =
                                dictProductVsEmpowers.stream().filter(e -> e.getOrderProductId() == productEmpowerDTO.getYyOrderProductId().longValue() && e.getMsunHealthModuleCode().equals(productEmpowerDTO.getMsunHealthModuleCode())).map(DictProductVsEmpower::getProductVsEmpowerId).collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(vsIds)) {
                            for (Long vsId : vsIds) {
                                stringBuilder.append(StrUtil.toString(vsId)).append(StrUtil.COMMA);
                            }
                            dictProductVsEmpowerIdMap.put(productEmpowerDTO.getYyOrderProductId(), stringBuilder);
                        }
                    }
                }
            }
        }
        for (Long yyProductId : yyProductIds) {
            ProjProductEmpowerAddRecordDetail detail = new ProjProductEmpowerAddRecordDetail();
            detail.setProductEmpowerAddRecordId(addRecord.getProductEmpowerAddRecordId());
            detail.setProductEmpowerAddRecordDetailId(SnowFlakeUtil.getId());
            detail.setYyProductId(yyProductId);
            if (MapUtil.isNotEmpty(empowerModuleCodeMap)) {
                StringBuilder moduleBuilder = empowerModuleCodeMap.get(yyProductId);
                if (ObjectUtil.isNotEmpty(moduleBuilder)) {
                    detail.setProductModuleCodes(moduleBuilder.toString());
                }
            }
            if (MapUtil.isNotEmpty(dictProductVsEmpowerIdMap)) {
                StringBuilder idBuilder = dictProductVsEmpowerIdMap.get(yyProductId);
                if (ObjectUtil.isNotEmpty(idBuilder)) {
                    detail.setDictVsEmpowerIds(idBuilder.toString());
                }
            }
            int count = productEmpowerAddRecordDetailMapper.insert(detail);
            log.info("新增产品授权明细数据. count: {}", count);
        }
    }

    /**
     * 更新产品补录明细表数据
     *
     * @param openedHospitalInfos 已开通沂源
     * @param projectInfoId       项目id
     * @param addRecord           主表数据
     * @param yyProductIds        产品id
     */
    private void updateDetail(List<ProjHospitalInfo> openedHospitalInfos, Long projectInfoId,
                              ProjProductEmpowerAddRecord addRecord, List<Long> yyProductIds) {
        if (CollUtil.isEmpty(yyProductIds)) {
            log.warn("产品id集合为空, 不更新明产品补录明细.");
            return;
        }
        ProjProductEmpowerAddRecordDetail updateDetail = new ProjProductEmpowerAddRecordDetail();
        StringBuilder hospitalIds = new StringBuilder();
        for (ProjHospitalInfo openedHospitalInfo : openedHospitalInfos) {
            hospitalIds.append(openedHospitalInfo.getHospitalInfoId()).append(StrUtil.COMMA);
        }
        updateDetail.setHospitalIds(hospitalIds.toString());
        ProjProjectInfo projectInfo = projectInfoMapper.selectOne(new QueryWrapper<ProjProjectInfo>().eq(
                "project_info_id", projectInfoId));
        // 更新工单Id
        if (ObjectUtil.isNotEmpty(projectInfo)) {
            updateDetail.setOrderInfoId(projectInfo.getOrderInfoId());
        }
        int count = productEmpowerAddRecordDetailMapper.update(updateDetail,
                new QueryWrapper<ProjProductEmpowerAddRecordDetail>().in("product_empower_add_record_id",
                        addRecord.getProductEmpowerAddRecordId()).in("yy_product_id", yyProductIds));
        log.info("更新产品授权补录明细表数据. count: {}", count);
    }

}
