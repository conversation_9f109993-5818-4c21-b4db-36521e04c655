package com.msun.csm.service.config;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.msun.csm.common.model.Result;
import com.msun.csm.dao.mapper.config.ConfigAgentScenarioDictMapper;
import com.msun.csm.model.req.ConfigAgentScenarioDictReq;
import com.msun.csm.model.resp.DictAgentChatScenarioConfigResp;

/**
 * AI智能体场景配置服务实现类
 * <AUTHOR> @since 2025-07-07 02:48:56
 */
@Slf4j
@Service
public class ConfigAgentScenarioDictImpl implements ConfigAgentScenarioDict {

    @Resource
    private ConfigAgentScenarioDictMapper configAgentScenarioDictMapper;

    @Override
    public Result<List<DictAgentChatScenarioConfigResp>> getAgentScenarioConfig(ConfigAgentScenarioDictReq req) {
        try {
            log.info("开始查询AI智能体场景配置，查询条件：{}", req);

            List<DictAgentChatScenarioConfigResp> configList = configAgentScenarioDictMapper.getAgentScenarioConfig(req);

            if (CollectionUtil.isEmpty(configList)) {
                log.warn("未查询到AI智能体场景配置数据");
                return Result.success(null, "暂无配置数据");
            }

            log.info("成功查询到{}条AI智能体场景配置", configList.size());
            return Result.success(configList, "查询成功");

        } catch (Exception e) {
            log.error("查询AI智能体场景配置失败", e);
            return Result.fail("查询配置失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Boolean> deleteAgentScenarioConfig(Long agentScenarioConfigId) {
        try {
            log.info("删除AI智能体场景配置ID：{}", agentScenarioConfigId);

            if (agentScenarioConfigId == null) {
                log.warn("配置ID为空，删除失败");
                return Result.fail("配置ID不能为空");
            }

            int result = configAgentScenarioDictMapper.deleteAgentScenarioConfig(agentScenarioConfigId);

            if (result > 0) {
                log.info("成功删除AI智能体场景配置，配置ID：{}", agentScenarioConfigId);
                return Result.success(true, "删除成功");
            } else {
                log.warn("删除AI智能体场景配置失败，可能配置不存在或已被删除，配置ID：{}", agentScenarioConfigId);
                return Result.fail("删除失败，配置不存在或已被删除");
            }

        } catch (Exception e) {
            log.error("删除AI智能体场景配置失败，配置ID：{}", agentScenarioConfigId, e);
            return Result.fail("删除配置失败：" + e.getMessage());
        }
    }

}
