package com.msun.csm.service.config;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.dict.DictAgentScenarioConfig;
import com.msun.csm.dao.mapper.config.ConfigAgentScenarioDictMapper;
import com.msun.csm.model.req.ConfigAgentScenarioDictReq;
import com.msun.csm.model.req.ConfigAgentScenarioDictSaveReq;
import com.msun.csm.model.resp.DictAgentChatScenarioConfigResp;
import com.msun.csm.model.resp.DictAgentChatDropdownResp;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.util.SnowFlakeUtil;

/**
 * AI智能体场景配置服务实现类
 *
 * <AUTHOR> @since 2025-07-07 02:48:56
 */
@Slf4j
@Service
public class ConfigAgentScenarioDictImpl implements ConfigAgentScenarioDict {

    @Resource
    private ConfigAgentScenarioDictMapper configAgentScenarioDictMapper;

    @Resource
    private UserHelper userHelper;

    /**
     * 查询AI智能体场景配置
     *
     * @param req 查询条件（可选，为null时查询所有）
     * @return 配置列表
     */
    @Override
    public Result<List<DictAgentChatScenarioConfigResp>> getAgentScenarioConfig(ConfigAgentScenarioDictReq req) {
        try {
            log.info("开始查询AI智能体场景配置，查询条件：{}", req);
            List<DictAgentChatScenarioConfigResp> configList = configAgentScenarioDictMapper.getAgentScenarioConfig(req);
            if (CollectionUtil.isEmpty(configList)) {
                log.warn("未查询到AI智能体场景配置数据");
                return Result.success(null, "暂无配置数据");
            }
            log.info("成功查询到{}条AI智能体场景配置", configList.size());
            return Result.success(configList, "查询成功");

        } catch (Exception e) {
            log.error("查询AI智能体场景配置失败", e);
            return Result.fail("查询配置失败：" + e.getMessage());
        }
    }

    /**
     * 删除AI智能体场景配置（逻辑删除）
     *
     * @param agentScenarioConfigId 配置ID
     * @return 删除结果
     */
    @Override
    public Result<Boolean> deleteAgentScenarioConfig(Long agentScenarioConfigId) {
        try {
            log.info("删除AI智能体场景配置ID：{}", agentScenarioConfigId);
            if (agentScenarioConfigId == null) {
                log.warn("配置ID为空，删除失败");
                return Result.fail("配置ID不能为空");
            }
            int result = configAgentScenarioDictMapper.deleteAgentScenarioConfig(agentScenarioConfigId);
            if (result > 0) {
                log.info("成功删除AI智能体场景配置，配置ID：{}", agentScenarioConfigId);
                return Result.success(true, "删除成功");
            } else {
                log.warn("删除AI智能体场景配置失败，可能配置不存在或已被删除，配置ID：{}", agentScenarioConfigId);
                return Result.fail("删除失败，配置不存在或已被删除");
            }

        } catch (Exception e) {
            log.error("删除AI智能体场景配置失败，配置ID：{}", agentScenarioConfigId, e);
            return Result.fail("删除配置失败：" + e.getMessage());
        }
    }

    /**
     * 保存AI智能体场景配置（新增或修改）
     *
     * @param req 保存请求参数
     * @return
     */
    @Override
    public Result<Boolean> saveAgentScenarioConfig(ConfigAgentScenarioDictSaveReq req) {
        try {
            // 根据ID是否为空判断是新增还是修改
            if (req.getAgentScenarioConfigId() == null) {
                // 新增
                return insertAgentScenarioConfig(req);
            } else {
                // 修改
                return updateAgentScenarioConfig(req);
            }

        } catch (Exception e) {
            log.error("保存AI智能体场景配置失败，请求参数：{}", req, e);
            return Result.fail("保存配置失败：" + e.getMessage());
        }
    }

    /**
     * 新增AI智能体场景配置
     */
    private Result<Boolean> insertAgentScenarioConfig(ConfigAgentScenarioDictSaveReq req) {
        log.info("新增AI智能体场景配置，请求参数：{}", req);
        DictAgentScenarioConfig config = new DictAgentScenarioConfig();
        config.setAgentScenarioConfigId(SnowFlakeUtil.getId());
        config.setAgentCode(req.getAgentCode());
        config.setScenarioDesc(req.getScenarioDesc());
        config.setScenarioPrompt(req.getScenarioPrompt());
        config.setScenarioCode(req.getScenarioCode());
        config.setAgentCode(req.getAgentName());
        // 获取当前登录用户ID
        Long currentUserId = userHelper.getCurrentSysUserIdWithDefaultValue();
        config.setCreaterId(currentUserId);
        config.setUpdaterId(currentUserId);

        int result = configAgentScenarioDictMapper.insertAgentScenarioConfig(config);
        if (result > 0) {
            log.info("新增AI智能体场景配置成功，生成ID：{}", config.getAgentScenarioConfigId());
            return Result.success(true, "新增成功");
        } else {
            log.warn("新增AI智能体场景配置失败");
            return Result.fail("新增失败");
        }
    }

    /**
     * 修改AI智能体场景配置
     */
    private Result<Boolean> updateAgentScenarioConfig(ConfigAgentScenarioDictSaveReq req) {
        log.info("修改AI智能体场景配置，配置ID：{}，请求参数：{}", req.getAgentScenarioConfigId(), req);
        // 先检查配置是否存在
        int count = configAgentScenarioDictMapper.countById(req.getAgentScenarioConfigId());
        if (count == 0) {
            log.warn("要修改的配置不存在，配置ID：{}", req.getAgentScenarioConfigId());
            return Result.fail("要修改的配置不存在");
        }
        DictAgentScenarioConfig config = new DictAgentScenarioConfig();
        config.setAgentScenarioConfigId(req.getAgentScenarioConfigId());
        config.setAgentCode(req.getAgentCode());
        config.setScenarioCode(req.getScenarioCode());
        config.setScenarioDesc(req.getScenarioDesc());
        config.setScenarioPrompt(req.getScenarioPrompt());
        // 获取当前登录用户ID
        Long currentUserId = userHelper.getCurrentSysUserIdWithDefaultValue();
        config.setUpdaterId(currentUserId);

        int result = configAgentScenarioDictMapper.updateAgentScenarioConfig(config);
        if (result > 0) {
            log.info("修改AI智能体场景配置成功，配置ID：{}", req.getAgentScenarioConfigId());
            return Result.success(true, "修改成功");
        } else {
            log.warn("修改AI智能体场景配置失败，配置ID：{}", req.getAgentScenarioConfigId());
            return Result.fail("修改失败");
        }
    }

}
