package com.msun.csm.service.common;

import java.util.List;

import org.springframework.http.ResponseEntity;

import com.msun.core.component.implementation.api.deviceanaysis.dto.ResponseData;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.csm.LongUrlToShortUrlDTO;
import com.msun.csm.model.imsp.CustomerNotOnlineVO;
import com.msun.csm.model.imsp.HospitalNotOnlineVO;
import com.msun.csm.model.statis.ProjCsmHostReq;
import com.msun.csm.model.statis.ProjCustomReq;

/**
 * @Description:
 * @Author: zd
 * @Date: 2025、02、08
 */
public interface BaseLongUrlToShortUrlService {

    /**
     * 长链接转短链接
     * @param dto
     * @return
     */
    Result<String> longUrlToShortUrlFunction(LongUrlToShortUrlDTO dto);

    /**
     * 短链接转长链接
     * @param shortCode
     * @return
     */
    ResponseEntity<Void> getLongUrl(String shortCode);

    /**
     * 获取交付平台测试环境域名或正式环境域名
     * @param dto
     * @return
     */
    ResponseData getCsmHost(ProjCsmHostReq dto);

    /**
     * 查询未上线客户信息
     * @return
     */
    Result<List<CustomerNotOnlineVO>> getNotOnlineCustomer();

    /**
     * 根据客户id查询未上线医院信息
     * @param dto
     * @return
     */
    Result<List<HospitalNotOnlineVO>> getNotOnlineHospitalData(ProjCustomReq dto);
}
