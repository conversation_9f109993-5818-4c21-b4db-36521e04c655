package com.msun.csm.service.proj;

import java.util.List;

import com.msun.csm.dao.entity.proj.ProjMessageRecordPeople;
import com.msun.csm.model.dto.AddProjMessageRecordParamDTO;
import com.msun.csm.model.dto.SaveMessageRecordAndDetailParamDTO;
import com.msun.csm.model.dto.SaveMessageRecordParam;

/**
 * <AUTHOR>
 * @since 2024-05-07 11:50:23
 */

public interface ProjMessageRecordService {

    /**
     * 添加消息发送记录
     *
     * @param paramDTO 参数
     * @return true-添加成功；false-添加失败
     */
    boolean addProjMessageRecord(AddProjMessageRecordParamDTO paramDTO);


    boolean saveProjMessageRecordAndPeopleDetail(SaveMessageRecordAndDetailParamDTO paramDTO);


    boolean addProjMessageRecordPeople(SaveMessageRecordParam paramDTO, List<ProjMessageRecordPeople> recordPeople);

}
