package com.msun.csm.service.dict;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.dao.entity.dict.DictBusinessStatus;
import com.msun.csm.dao.mapper.dict.DictBusinessStatusMapper;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class DictBusinessStatusServiceImpl implements DictBusinessStatusService {

    @Resource
    private DictBusinessStatusMapper dictBusinessStatusMapper;

    @Override
    public String getSurveyPlanStatusDescriptionByStatusId(Integer statusId) {
        DictBusinessStatus dictBusinessStatus = dictBusinessStatusMapper.selectOne(
                new QueryWrapper<DictBusinessStatus>()
                        .eq("is_deleted", 0)
                        .eq("business_code", "surveyPlan")
                        .eq("status_id", statusId)
        );
        if (dictBusinessStatus == null) {
            throw new IllegalArgumentException("业务状态字典缺少对应的状态，business_code=surveyPlan，status_id=" + statusId);
        }
        return dictBusinessStatus.getStatusDescription();
    }
}
