package com.msun.csm.service.proj;

import java.util.Date;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.mapper.proj.ProjMilestoneInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.feign.client.oldimsp.OldImspFeignClient;
import com.msun.csm.feign.entity.oldimsp.req.ConfirmEntryReq;
import com.msun.csm.model.dto.ConfirmEntryDTO;
import com.msun.csm.model.dto.UpdateMilestoneDTO;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.util.CsmSignUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName: ConfirmEntryServiceImpl
 * @Description:
 * @Author: Yhongmin
 * @Date: 11:27 2024/5/31
 */
@Service
@Slf4j
public class ConfirmEntryServiceImpl implements ConfirmEntryService {
    @Resource
    private OldImspFeignClient oldImspFeignClient;
    @Resource
    private ProjMilestoneInfoService projMilestoneInfoService;
    @Resource
    private ProjMilestoneInfoMapper projMilestoneInfoMapper;
    @Resource
    private UserHelper userHelper;
    @Resource
    private ProjProjectInfoMapper projProjectInfoMapper;

    /**
     * 说明: 新项目调用老项目确认入驻用于处理运营平台和消息等
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/5/31 14:53
     * @remark: Copyright
     */
    @Override
    public Result putConfirmEntry(ConfirmEntryDTO dto) {
        SysUserVO sysUserVO = userHelper.getCurrentUser();
        ConfirmEntryReq confirmEntryReq = new ConfirmEntryReq();
        confirmEntryReq.setProjectId(dto.getProjectInfoId());
        confirmEntryReq.setUserYunyingId(sysUserVO.getUserYunyingId());
        log.info("调用老项目确认入驻,{}", confirmEntryReq);
        String auth = CsmSignUtil.getHeader();
        String resp = oldImspFeignClient.confirmEntry(confirmEntryReq, auth);
        log.info("调用老项目确认入驻返回,{}", resp);
        JSONObject respJson = JSONObject.parseObject(resp);
        if (!respJson.getBoolean("success")) {
            log.error("调用老项目确认入驻返回,{}", respJson.getString("msg"));
            throw new CustomException("调用老项目确认入驻异常返回:" + respJson.getString("msg"));
        }
        //回更节点状态
        return updateMillstone(dto);
    }

    public Result<String> updateMillstone(ConfirmEntryDTO dto) {
        ProjMilestoneInfo projMilestoneInfo = projMilestoneInfoMapper.selectById(dto.getMilestoneInfoId());
        if (projMilestoneInfo == null) {
            return Result.fail("根据节点Id查询不到节点信息");
        }
        UpdateMilestoneDTO milestoneDTO = new UpdateMilestoneDTO();
        milestoneDTO.setMilestoneInfoId(dto.getMilestoneInfoId());
        milestoneDTO.setMilestoneStatus(NumberEnum.NO_1.num());
        Result<Boolean> updateMilestone = projMilestoneInfoService.compMilestone(milestoneDTO);
        if (updateMilestone.isSuccess()) {
            ProjProjectInfo projProjectInfo = new ProjProjectInfo();
            projProjectInfo.setProjectInfoId(projMilestoneInfo.getProjectInfoId());
            projProjectInfo.setSettleInTime(new Date());
            projProjectInfo.setProjectDeliverStatus(3);
            projProjectInfoMapper.updateById(projProjectInfo);
            return Result.success();
        }
        log.error("节点更新失败");
        return Result.fail("节点更新失败");
    }
}
