package com.msun.csm.service.proj;

import java.util.List;

import com.msun.csm.common.enums.projsettlement.SettlementStatusEnum;
import com.msun.csm.dao.entity.proj.ProjProjectSettlement;
import com.msun.csm.dao.entity.proj.projsettlement.SurveyHardwareNewResultdataEntity;

/**
 * <AUTHOR>
 * @since 2024-06-17 05:11:00
 */

public interface ProjProjectSettlementService {

    /**
     * 更新申请单状态, 并保存日志, 保存最终结果表
     *
     * @param settlement 工单信息
     */
    void updateSettlementStatus(ProjProjectSettlement settlement, SettlementStatusEnum settlementStatusEnum);
    /**
     * 根据项目id查询申请单
     *
     * @param projectInfoId 项目id
     * @return ProjProjectSettlement
     */
    ProjProjectSettlement getProjectSettlement(Long projectInfoId);

    /**
     * 根据项目id查询小硬件清单文件
     *
     * @param projectInfoId 新系统项目id
     * @return List<SurveyHardwareNewResultdataEntity>
     */
    List<SurveyHardwareNewResultdataEntity> selectHardwareFile(Long projectInfoId);

    /**
     * 根据项目id查询申请单信息
     *
     * @param projectInfoId 项目id
     * @return ProjProjectSettlement
     */
    ProjProjectSettlement getSettlementByProjectInfoId(Long projectInfoId);

}
