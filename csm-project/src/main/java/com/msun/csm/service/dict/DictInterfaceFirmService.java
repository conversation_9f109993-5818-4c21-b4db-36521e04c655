package com.msun.csm.service.dict;

import java.util.List;

import com.msun.csm.dao.entity.dict.DictInterfaceFirm;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/9/12
 */

public interface DictInterfaceFirmService {

    int deleteByPrimaryKey(Long dictInterfaceFirmId);

    int insert(DictInterfaceFirm record);

    int insertOrUpdate(DictInterfaceFirm record);

    int insertOrUpdateSelective(DictInterfaceFirm record);

    int insertSelective(DictInterfaceFirm record);

    DictInterfaceFirm selectByPrimaryKey(Long dictInterfaceFirmId);

    int updateByPrimaryKeySelective(DictInterfaceFirm record);

    int updateByPrimaryKey(DictInterfaceFirm record);

    int updateBatch(List<DictInterfaceFirm> list);

    int updateBatchSelective(List<DictInterfaceFirm> list);

    int batchInsert(List<DictInterfaceFirm> list);

}
