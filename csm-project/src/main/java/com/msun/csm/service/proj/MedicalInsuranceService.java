package com.msun.csm.service.proj;

import java.io.UnsupportedEncodingException;
import java.util.List;

import com.msun.core.component.implementation.api.medinsur.dto.MedInsurInSettleResultDTO;
import com.msun.csm.model.param.OnlineValidateParam;

public interface MedicalInsuranceService {

    /**
     * 医院id 如果是编辑医院的代码，则传入实际的医院id，如果编辑的是地区的编码，传入-1
     *
     * @param customInfoId
     * @param projectInfoId
     * @param hospitalInfoId
     * @return
     */
    String jumpMedicalInsurance(Long customInfoId, Long projectInfoId, Long hospitalInfoId) throws UnsupportedEncodingException;

    String updateHospitalId(Long newHospitalId, Long oldHospitalId);

    void sendErrorMessage(String errorMessage, Long newHospitalId, Long oldHospitalId);

    boolean onlineVerification(OnlineValidateParam param);


    List<MedInsurInSettleResultDTO> onlineVerification2(Long hospitalInfoId);
}
