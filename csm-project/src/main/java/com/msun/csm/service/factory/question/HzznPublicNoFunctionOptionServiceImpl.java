package com.msun.csm.service.factory.question;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.msun.csm.common.enums.QuestionEnum;
import com.msun.csm.dao.entity.comm.HzznPublicNoFunction;
import com.msun.csm.dao.entity.tduck.QuestionOption;
import com.msun.csm.service.common.HzznDeliverService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class HzznPublicNoFunctionOptionServiceImpl implements QuestionOptionService {

    @Resource
    private HzznDeliverService hzznDeliverService;

    @Override
    public QuestionEnum questionInfo() {
        return QuestionEnum.HZZN_PUBLIC_NO_FUNCTION;
    }

    @Override
    public List<QuestionOption> getQuestionOption() {
        List<HzznPublicNoFunction> publicNoFunction = hzznDeliverService.getPublicNoFunction();
        List<QuestionOption> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(publicNoFunction)) {
            for (HzznPublicNoFunction hzznPublicNoFunction : publicNoFunction) {
                QuestionOption questionOption = new QuestionOption();
                questionOption.setValue(hzznPublicNoFunction.getCode());
                questionOption.setLabel(hzznPublicNoFunction.getName());
                questionOption.setSort(hzznPublicNoFunction.getSort());
                result.add(questionOption);
            }
        }
        return result;
    }

}
