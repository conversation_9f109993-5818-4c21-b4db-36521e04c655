package com.msun.csm.service.proj;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.extension.service.IService;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjEquipRecordVsHd;
import com.msun.csm.model.dto.HdEquipSelectDTO;
import com.msun.csm.model.dto.ProjEquipRecordVsHdDTO;
import com.msun.csm.model.dto.ProjEquipSendToCloudDTO;
import com.msun.csm.model.dto.ProjEquipVsProductFinishDTO;
import com.msun.csm.model.vo.ProjEquipRecordHdResultVO;
import com.msun.csm.model.vo.ProjEquipRecordVsHdVO;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/10
 */

public interface ProjEquipRecordVsHdService extends IService<ProjEquipRecordVsHd> {

    /**
     * 保存血透设备信息
     *
     * @param dto
     * @return
     */
    Result saveOrUpdateEquipToHd(ProjEquipRecordVsHdDTO dto);

    /**
     * 查询单个血透设备数据
     *
     * @param equipRecordVsHdId
     * @return
     */
    Result<ProjEquipRecordVsHdVO> getEquipRecordVsHd(Long equipRecordVsHdId, Integer isMobile);

    /**
     * 删除血透设备数据
     *
     * @param equipRecordVsHdId
     * @return
     */
    Result deleteEquipRecordVsHd(Long equipRecordVsHdId);

    /**
     * 查询血透设备列表数据
     *
     * @param dto
     * @return
     */
    Result<ProjEquipRecordHdResultVO> selectHdEquipData(HdEquipSelectDTO dto);

    /**
     * 设备信息发送到LIS解析平台
     *
     * @param equipRecordVsHdIds
     * @return
     */
    Result equipSendToLis(List<Long> equipRecordVsHdIds);

    /**
     * 撤销LIS接口申请
     *
     * @param equipRecordVsHdId
     * @return
     */
    Result equipRevokeToHd(Long equipRecordVsHdId);

    /**
     * 提交完成
     *
     * @return
     */
    Result commitFinish(ProjEquipVsProductFinishDTO dto);

    /**
     * 发送到云健康
     *
     * @param dto
     * @return
     */
    Result sendToMsunVsHd(ProjEquipSendToCloudDTO dto);

    /**
     * 一键检测
     *
     * @param dto
     * @return
     */
    Result checkSendToMsunVsHd(ProjEquipSendToCloudDTO dto);

    /**
     * 血透下载模版
     *
     * @param response
     */
    void downloadTemplateVsHD(HttpServletResponse response, Long projectInfoId);

    /**
     * 血透导入模版数据
     *
     * @param multipartFile
     * @param customInfoId
     * @param projectInfoId
     * @return
     */
    Result importExcelDataVsHD(MultipartFile multipartFile, Long customInfoId, Long projectInfoId);

    /**
     * 血透设备导出数据
     * @param dto
     * @param response
     */
    void exportExcelDataVsHD(HdEquipSelectDTO dto, HttpServletResponse response);

    /**
     * 老系统血透设备迁移到新系统
     *
     * @param projectInfoId
     * @param oldEquipId
     * @return
     */
    Result sendEquipToOldHD(Long projectInfoId, Long oldEquipId);
}
