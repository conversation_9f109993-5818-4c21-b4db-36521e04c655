package com.msun.csm.service.oldimsp;

import java.util.List;

import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.oldimsp.ImspProject;
import com.msun.csm.dao.entity.proj.extend.ProjProjectInfoExtend;
import com.msun.csm.feign.entity.oldimsp.req.ProjectManagerConfirmDto;
import com.msun.csm.model.req.project.TransOldProjectReq;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/6/18
 */
public interface OldImspService {

    /**
     * 导数据接口
     *
     * @return
     */
    Result transDatas(TransOldProjectReq req);

    /**
     * 确认项目状态
     *
     * @param oldProject
     * @param projectInfoExtend
     * @return
     */
    ProjectManagerConfirmDto getProjectManagerConfirmDto(ImspProject oldProject,
                                                         ProjProjectInfoExtend projectInfoExtend);

    /**
     * 根据运营平台人员id查询老系统人员id
     *
     * @param userYunyingId
     * @return
     */
    Long getImspUserId(Long userYunyingId);

    /**
     * 合同客户接口
     *
     * @return
     */
    Result dealContractCustomer();

    /**
     * 处理沂源地理区划信息
     *
     * @return
     */
    Result dealHospitalGeographic();

    /**
     * 处理合同对用的方案分公司数据
     *
     * @return
     */
    Result dealContractPreSaleId();

    /**
     * 处理老平台医院数据
     *
     * @return
     */
    Result dealCommCustomerInfo();

    /**
     * 处理里程碑时间
     *
     * @return
     */
    Result fixMilestoneTime(List<Long> ids);

    /**
     * 处理新增字典数据
     *
     * @param ids
     * @return
     */
    Result fixAddDictData(List<Long> ids);
}
