package com.msun.csm.service.proj;

import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.dto.TaskAllocationDTO;
import com.msun.csm.model.req.ProjTaskAllocationReq;

/**
 * @description:
 * @fileName:
 * @author:<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @updateBy:
 * @Date:Created in 11:21 2024/5/14
 * @remark:
 */
public interface TaskAllocationService {

    /**
     * 通过projectInfoId查询该项目的节点信息，用于[准备阶段任务分配] 数据内容 只查询准备阶段和测试阶段的数据
     *
     * @param dto
     * @return
     */
    Result<ProjTaskAllocationReq> getTaskAllocation(SelectHospitalDTO dto);


    /**
     * 通过返回的数据，存储数据，更新数据
     *
     * @param dto
     * @return
     */
    Result<String> updateTaskAllocation(TaskAllocationDTO dto);
    /**
     * 说明: 根据projectInfoId 更新产品交付记录表的数据
     * @param projectInfoId
     * @return:void
     * @author: Yhongmin
     * @createAt: 2024/5/28 9:52
     * @remark: Copyright
      */
    void updateProductDeliverRecord(Long projectInfoId);

}
