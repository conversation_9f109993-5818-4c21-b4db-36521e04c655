package com.msun.csm.service.proj;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.ProjEquipRecordVsEcgDTO;
import com.msun.csm.model.dto.ProjEquipRecordVsEcgSelectDTO;
import com.msun.csm.model.dto.ProjEquipVsProductFinishDTO;
import com.msun.csm.model.vo.ProjEquipRecordEcgResultVO;
import com.msun.csm.model.vo.ProjEquipRecordVsEcgVO;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/12/17/14:28
 */
public interface ProjEquipRecordVsEcgService {

    /**
     * 查询列表数据
     *
     * @param dto
     * @return
     */
    Result<ProjEquipRecordEcgResultVO> selectEcgEquipData(ProjEquipRecordVsEcgSelectDTO dto);

    /**
     * 根据id查询单个心电设备数据
     *
     * @param dto
     * @return
     */
    Result<ProjEquipRecordVsEcgVO> viewEquipToEcg(ProjEquipRecordVsEcgSelectDTO dto);

    /**
     * 删除心电设备数据
     *
     * @param dto
     * @return
     */
    Result deleteEquipToEcg(ProjEquipRecordVsEcgSelectDTO dto);

    /**
     * 新增或修改心电设备数据
     *
     * @param dto
     * @return
     */
    Result saveOrUpdateEquipToEcg(ProjEquipRecordVsEcgDTO dto);

    /**
     * 发送到云健康
     *
     * @param dto
     * @return
     */
    Result<String> ecgEquipSendCloudEquip(ProjEquipRecordVsEcgSelectDTO dto);

    /**
     * 一键检测
     *
     * @param dto
     * @return
     */
    Result<String> ecgEquipCheckForCloud(ProjEquipRecordVsEcgSelectDTO dto);

    /**
     * 心电设备下载模版
     *
     * @param response
     * @param projectInfoId
     */
    void downloadTemplateForEcg(HttpServletResponse response, Long projectInfoId);

    /**
     * 导入模版数据
     *
     * @param multipartFile
     * @param customInfoId
     * @param projectInfoId
     * @return
     */
    Result importExcelDatas(MultipartFile multipartFile, Long customInfoId, Long projectInfoId);

    /**
     * 导出数据
     *
     * @param dto
     * @param response
     */
    void exportExcelDatas(ProjEquipRecordVsEcgSelectDTO dto, HttpServletResponse response);

    /**
     * 提交完成
     *
     * @param dto
     * @return
     */
    Result commitFinish(ProjEquipVsProductFinishDTO dto);
}
