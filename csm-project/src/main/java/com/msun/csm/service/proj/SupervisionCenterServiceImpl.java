package com.msun.csm.service.proj;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.enums.BackendTeamTypeEnum;
import com.msun.csm.common.enums.issue.IssueOperEnums;
import com.msun.csm.common.enums.projprojectinfo.ProjectDeliverStatusEnums;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysDept;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.config.ConfigIssueClassification;
import com.msun.csm.dao.entity.dict.DictAcceptanceClassification;
import com.msun.csm.dao.entity.dict.DictDeductionType;
import com.msun.csm.dao.entity.dict.DictServerType;
import com.msun.csm.dao.entity.proj.*;
import com.msun.csm.dao.entity.proj.projreport.ConfigCustomBackendDetailLimit;
import com.msun.csm.dao.mapper.config.ConfigIssueClassificationMapper;
import com.msun.csm.dao.mapper.dict.DictAcceptanceClassificationMapper;
import com.msun.csm.dao.mapper.dict.DictDeductionTypeMapper;
import com.msun.csm.dao.mapper.dict.DictProjectAcceptRuleMapper;
import com.msun.csm.dao.mapper.dict.DictServerTypeMapper;
import com.msun.csm.dao.mapper.proj.*;
import com.msun.csm.dao.mapper.report.ConfigCustomBackendDetailLimitMapper;
import com.msun.csm.dao.mapper.sysdept.SysDeptMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.model.DictDeductionTypeVO;
import com.msun.csm.model.param.*;
import com.msun.csm.model.req.issue.QueryIssueReq;
import com.msun.csm.model.req.issue.SaveDeductionClassificationDictType;
import com.msun.csm.model.req.issue.SaveIssueReq;
import com.msun.csm.model.resp.issue.IssueDataResp;
import com.msun.csm.model.resp.issue.IssueDataResp2;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.common.CommonService;
import com.msun.csm.service.yunying.YunYingService;
import com.msun.csm.util.SnowFlakeUtil;

@Slf4j
@Service
public class SupervisionCenterServiceImpl implements SupervisionCenterService {

    //未分配状态
    private static final Long UN_ASSIGNED = 1L;

    //已分配状态
    private static final Long ASSIGNED = 2L;

    @Resource
    private DictAcceptanceClassificationMapper dictAcceptanceClassificationMapper;

    @Resource
    private ConfigIssueClassificationMapper configIssueClassificationMapper;

    @Resource
    private ProjIssueInfoMapper projIssueInfoMapper;

    @Resource
    private DictDeductionTypeMapper dictDeductionTypeMapper;

    @Resource
    private DictServerTypeMapper dictServerTypeMapper;

    @Resource
    private ProjIssueInfoService issueInfoService;

    @Resource
    private UserHelper userHelper;

    @Resource
    private ImplementApplicationService implementApplicationService;

    @Resource
    private ProjProjectDeductionDetailMapper projProjectDeductionDetailMapper;

    @Resource
    private ProjDeductionDetailCommonMapper projDeductionDetailCommonMapper;

    @Resource
    private ProjIssueOperLogMapper issueOperLogMapper;

    @Resource
    private ProjProjectInfoMapper projProjectInfoMapper;

    @Resource
    private ProjProjectMemberMapper projProjectMemberMapper;

    @Resource
    private SysDeptMapper sysDeptMapper;

    @Resource
    private ProjDeductionDetailInfoMapper projDeductionDetailInfoMapper;

    @Resource
    private ConfigCustomBackendDetailLimitMapper configCustomBackendDetailLimitMapper;

    @Resource
    private ServerTeamDeductionRecordMapper serverTeamDeductionRecordMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private ServerTeamScoreLogService serverTeamScoreLogService;

    @Resource
    private ProjProjectClassificationScoreMapper projProjectClassificationScoreMapper;

    @Resource
    private DictProjectAcceptRuleMapper dictProjectAcceptRuleMapper;

    @Resource
    private ProjProjectInfoService projectInfoService;

    @Resource
    private CommonService commonService;

    @Resource
    private YunYingService yunYingService;

    @Override
    public List<BaseCodeNameResp> getAcceptanceClassification() {
        List<DictAcceptanceClassification> dictAcceptanceClassifications = dictAcceptanceClassificationMapper.selectList(
                new QueryWrapper<DictAcceptanceClassification>()
                        .eq("is_deleted", 0)
                        .orderByAsc("sort_no")
        );
        return dictAcceptanceClassifications.stream().map(item -> new BaseCodeNameResp(item.getAcceptanceClassificationCode(), item.getAcceptanceClassificationName())).collect(Collectors.toList());
    }

    @Override
    public List<BaseCodeNameResp> getIssueClassification(GetIssueClassificationParam param) {
        ConfigIssueClassification configIssueClassification = new ConfigIssueClassification();
        configIssueClassification.setPlanItemCode(param.getPlanItemCode());
        configIssueClassification.setAcceptanceClassificationCode(param.getAcceptanceClassificationCode());
        List<ConfigIssueClassification> classificationConfigList = configIssueClassificationMapper.getConfigIssueClassification(configIssueClassification);
        List<BaseCodeNameResp> classificationRespList = classificationConfigList.stream().map(item -> {
            BaseCodeNameResp baseIdNameResp = new BaseCodeNameResp();
            baseIdNameResp.setId(String.valueOf(item.getId()));
            baseIdNameResp.setName(item.getName());
            return baseIdNameResp;
        }).collect(Collectors.toList());
        // 项目计划编码为空时，还走原来的逻辑
        if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getPlanItemCode())) {
            return classificationRespList;
        } else {
            //查询项目自定义的分类
            List<String> classificationNameList = projIssueInfoMapper.getClassificationList(param.getProjectInfoId());
            if (!CollectionUtils.isEmpty(classificationNameList)) {
                List<Long> collect = classificationRespList.stream().map(item -> Long.valueOf(item.getId())).collect(Collectors.toList());
                long lastId = collect.stream().max(Long::compareTo).orElse(1L);
                for (int i = 0; i < classificationNameList.size(); i++) {
                    BaseCodeNameResp baseIdNameResp = new BaseCodeNameResp();
                    baseIdNameResp.setId(String.valueOf(lastId + i + 1));
                    baseIdNameResp.setName(classificationNameList.get(i));
                    classificationRespList.add(baseIdNameResp);
                }
            }
            return classificationRespList;
        }
    }

    @Override
    public List<DeductionClassificationVO> getDeductionClassification(GetDeductionClassificationParam param) {
        List<DictDeductionType> deductionTypeByIssueClassification = dictDeductionTypeMapper.getDeductionTypeByIssueClassification(param.getIssueClassificationId());
        return deductionTypeByIssueClassification.stream().map(item -> new DeductionClassificationVO(item.getCode(), item.getName(), item.getDefaultScore().toPlainString())).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public boolean saveIssue(SaveIssueReq req) {
        //  通过项目计划添加问题时传的是问题分类的描述（classification），监管中心添加问题时传的是问题分类的ID（issueClassificationId），此时问题分类的描述为空，需要根据问题分类的ID查询问题分类的描述
        if (req.getIssueClassificationId() != null && StringUtils.isBlank(req.getClassification())) {
            ConfigIssueClassification configIssueClassification = configIssueClassificationMapper.selectByPrimaryKey(req.getIssueClassificationId());
            if (configIssueClassification != null) {
                req.setClassification(configIssueClassification.getName());
            }
        }
        // 监管中心质管人员添加问题
        if ("supervisionCenter".equals(req.getOperationSource())) {
            // 扣分分值不为0
            ProjIssueInfo saveDeductionScoreResult = null;
            if (StringUtils.isNotBlank(req.getDeductionScore())) {
                if (req.getId() != null) {
                    ProjIssueInfo currentIssueInfo = projIssueInfoMapper.selectByPrimaryKey(req.getId());
                    // 修改问题时先同步删除原来的扣分记录
                    projDeductionDetailCommonMapper.updateStatusToDeletedById(currentIssueInfo.getBusinessId());
                }
                saveDeductionScoreResult = saveDeductionScore(req);
            }
            SysUserVO user = userHelper.getCurrentUser();
            Date now = new Date();
            ProjIssueInfo issueInfo = new ProjIssueInfo();
            BeanUtil.copyProperties(req, issueInfo);
            issueInfo.setUpdateTime(now);
            issueInfo.setUpdaterId(user.getSysUserId());
            if (req.getClassification() != null) {
                issueInfo.setClassification(req.getClassification().replace(StrUtil.SPACE, StrUtil.EMPTY));
            }
            if (saveDeductionScoreResult != null) {
                issueInfo.setBusinessId(saveDeductionScoreResult.getBusinessId());
            }
            int operType = -1;
            if (req.getId() != null) {
                log.info("质管中心修改问题");
                ProjIssueInfo currentIssueInfo = projIssueInfoMapper.selectByPrimaryKey(req.getId());
                if (issueInfo.getChargePerson() != null) {
                    if (currentIssueInfo.getChargePerson() == null || !issueInfo.getChargePerson().equals(currentIssueInfo.getChargePerson())) {
                        operType = IssueOperEnums.ASSIGN_CHARGE_PERSON.getCode();
                    }
                } else if (issueInfo.getDescription() != null) {
                    if (currentIssueInfo.getDescription() == null || !issueInfo.getDescription().equals(currentIssueInfo.getDescription())) {
                        operType = IssueOperEnums.MODIFY_ISSUE_DESC.getCode();
                    }
                } else if (issueInfo.getStatus() != null) {
                    if (currentIssueInfo.getStatus() == null || !issueInfo.getStatus().equals(currentIssueInfo.getStatus())) {
                        operType = IssueOperEnums.MODIFY_ISSUE_STATUS.getCode();
                    }
                }
                //分配责任人，状态自动变化
                if (operType == IssueOperEnums.ASSIGN_CHARGE_PERSON.getCode()) {
                    issueInfo.setStatus(ASSIGNED);
                }
                projIssueInfoMapper.updateByPrimaryKeySelective(issueInfo);
            } else {
                log.info("质管中心新增问题");
                if (StringUtils.isBlank(req.getDescription())) {
                    throw new IllegalArgumentException("没有填写问题描述");
                }
                //状态，如果分配了责任人，状态为已分配，否则为未分配
                if (req.getChargePerson() != null) {
                    issueInfo.setStatus(ASSIGNED);
                } else {
                    issueInfo.setStatus(UN_ASSIGNED);
                }
                issueInfo.setCreaterId(user.getSysUserId());
                issueInfo.setCreateTime(now);
                issueInfo.setIsDeleted(0);
                issueInfo.setId(SnowFlakeUtil.getId());
                projIssueInfoMapper.insert(issueInfo);
                operType = IssueOperEnums.ADD.getCode();
            }
            //保存操作日志-需要判断更新字段，所以在数据修改前记录日志
            if (operType != -1) {
                saveLog(Collections.singletonList(issueInfo), operType);
            }
            return true;
        }
        // 不是监管中心的操作，调用原来的保存方法
        issueInfoService.saveIssue(req);
        return true;
    }

    private ProjIssueInfo saveDeductionScore(SaveIssueReq req) {
        ProjProjectInfo projProjectInfo = projProjectInfoMapper.selectByPrimaryKey(req.getProjectInfoId());
        Integer currentAcceptanceTimes = ProjectDeliverStatusEnums.FIRST_ACCEPTED.getCode().equals(projProjectInfo.getProjectDeliverStatus()) ? 2 : 1;

        String source;
        if (req.getOnlyOneCheckFlag()) {
            source = "final";
        } else if (Integer.valueOf(2).equals(currentAcceptanceTimes)) {
            source = "final";
        } else {
            source = "first";
        }

        // 线下培训、设备接口、数据统计
        if ("offline".equals(req.getAcceptanceClassificationCode()) || "equip".equals(req.getAcceptanceClassificationCode()) || "dataStatistics".equals(req.getAcceptanceClassificationCode())) {
            String menuCode;
            // 线下培训
            if ("offline".equals(req.getAcceptanceClassificationCode())) {
                menuCode = "RapidImplementationApplication";
            } else if ("equip".equals(req.getAcceptanceClassificationCode())) {
                // 设备接口对接
                menuCode = "EquipmentInterface";
            } else {
                // 数据统计
                menuCode = "DataStatistics";
            }
            SaveCommonDeductionParam saveCommonDeductionParam = new SaveCommonDeductionParam();
            saveCommonDeductionParam.setPracticalDeduction(req.getDeductionScore());
            saveCommonDeductionParam.setRemark(req.getDescription());
            saveCommonDeductionParam.setDeductionType(req.getDeductionTypeCode());
            // 通过添加问题没有附件
            saveCommonDeductionParam.setAttachmentInfoList(new ArrayList<>());
            saveCommonDeductionParam.setProjectInfoId(projProjectInfo.getProjectInfoId());
            saveCommonDeductionParam.setMenuCode(menuCode);
            saveCommonDeductionParam.setOnlyOneCheckFlag(req.getOnlyOneCheckFlag());
            // 如果项目状态是一次验收通过，再添加问题时认为是第二次验收的问题，其他情况认为是一次验收的问题
            saveCommonDeductionParam.setCurrentAcceptanceTimes(currentAcceptanceTimes);

            ProjDeductionDetailCommon projDeductionDetailCommon = implementApplicationService.saveCommonDeduction(saveCommonDeductionParam);
            ProjIssueInfo projIssueInfo = new ProjIssueInfo();
            projIssueInfo.setBusinessId(projDeductionDetailCommon.getProjDeductionDetailInfoId());
            return projIssueInfo;
        }
        // 项目过程
        if ("process".equals(req.getAcceptanceClassificationCode())) {
            SaveProcessDeductionParam saveProcessDeductionParam = new SaveProcessDeductionParam();
            saveProcessDeductionParam.setProjectInfoId(projProjectInfo.getProjectInfoId());
            saveProcessDeductionParam.setMenuCode("RapidImplementationApplication");
            saveProcessDeductionParam.setPracticalDeduction(req.getDeductionScore());
            saveProcessDeductionParam.setRemark(req.getDescription());
            saveProcessDeductionParam.setOnlyOneCheckFlag(req.getOnlyOneCheckFlag());
            saveProcessDeductionParam.setCurrentAcceptanceTimes(currentAcceptanceTimes);
            saveProcessDeductionParam.setYyProductId(req.getProductId());
            saveProcessDeductionParam.setDeductionType(req.getDeductionTypeCode());
            saveProcessDeductionParam.setAttachmentInfoList(new ArrayList<>());
            ProjDeductionDetailProcess projDeductionDetailProcess = implementApplicationService.saveProcessDeduction(saveProcessDeductionParam);
            ProjIssueInfo projIssueInfo = new ProjIssueInfo();
            projIssueInfo.setBusinessId(projDeductionDetailProcess.getProjDeductionDetailInfoId());
            return projIssueInfo;
        }
        // 项目文档扣分
        if ("document".equals(req.getAcceptanceClassificationCode())) {
            SaveDocumentDeductionParam saveDocumentDeductionParam = new SaveDocumentDeductionParam();
            saveDocumentDeductionParam.setProjectInfoId(projProjectInfo.getProjectInfoId());
            saveDocumentDeductionParam.setMenuCode("RapidImplementationApplication");
            saveDocumentDeductionParam.setPracticalDeduction(new BigDecimal(req.getDeductionScore()));
            saveDocumentDeductionParam.setRemark(req.getDescription());
            // FIXME 项目阶段获取不到
            saveDocumentDeductionParam.setProjectStageCode("none");
            // FIXME 里程碑节点获取不到
            saveDocumentDeductionParam.setMilestoneNodeCode("none");
            saveDocumentDeductionParam.setProjectFileId(null);
            saveDocumentDeductionParam.setOnlyOneCheckFlag(req.getOnlyOneCheckFlag());
            saveDocumentDeductionParam.setCurrentAcceptanceTimes(currentAcceptanceTimes);
            saveDocumentDeductionParam.setDeductionType(req.getDeductionTypeCode());
            ProjDeductionDetailDocument projDeductionDetailDocument = implementApplicationService.saveDocumentDeduction(saveDocumentDeductionParam);
            ProjIssueInfo projIssueInfo = new ProjIssueInfo();
            projIssueInfo.setBusinessId(projDeductionDetailDocument.getProjDeductionDetailInfoId());
            return projIssueInfo;
        }

        Date now = new Date();
        ProjProjectDeductionDetail projProjectDeductionDetail = new ProjProjectDeductionDetail();
        projProjectDeductionDetail.setProjDeductionDetailInfoId(SnowFlakeUtil.getId());
        projProjectDeductionDetail.setIsDeleted(0);
        projProjectDeductionDetail.setCreaterId(userHelper.getCurrentUser().getSysUserId());
        projProjectDeductionDetail.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
        projProjectDeductionDetail.setCreateTime(now);
        projProjectDeductionDetail.setUpdateTime(now);
//        projProjectDeductionDetail.setCustomInfoId(projProjectInfo.getCustomInfoId());
        projProjectDeductionDetail.setProjectInfoId(projProjectInfo.getProjectInfoId());
        projProjectDeductionDetail.setYyProductId(req.getProductId());
        // 获取不到
        projProjectDeductionDetail.setFunctionCode("none");
        // 实际扣分
        projProjectDeductionDetail.setPracticalDeduction(new BigDecimal(req.getDeductionScore()));
        projProjectDeductionDetail.setRemark(req.getDescription());
        projProjectDeductionDetail.setClassificationCode("ProductFunctionApplication");
        // 预估扣分
        projProjectDeductionDetail.setEstimatedDeduction(BigDecimal.ZERO);
        projProjectDeductionDetail.setSource(source);
        projProjectDeductionDetail.setUseCount(0);
        projProjectDeductionDetail.setDeductionType(req.getDeductionTypeCode());
        projProjectDeductionDetail.setOperationType("function");
        int insert = projProjectDeductionDetailMapper.insert(projProjectDeductionDetail);
        ProjIssueInfo projIssueInfo = new ProjIssueInfo();
        projIssueInfo.setBusinessId(projProjectDeductionDetail.getProjDeductionDetailInfoId());
        return projIssueInfo;
    }

    /**
     * @param issueInfoList
     * @Description: 保存操作日志
     */
    private void saveLog(List<ProjIssueInfo> issueInfoList, int operType) {
        //判断是新增、更新还是删除
        if (issueInfoList != null && !issueInfoList.isEmpty()) {
            SysUserVO user = userHelper.getCurrentUser();
            Date now = new Date();
            List<ProjIssueOperLog> logList = issueInfoList.stream()
                    .map(item -> getProjIssueOperLog(item, user, now, operType))
                    .collect(Collectors.toList());
            issueOperLogMapper.batchInsert(logList);
        }
    }

    @NotNull
    private static ProjIssueOperLog getProjIssueOperLog(ProjIssueInfo item, SysUserVO user, Date now, int operType) {
        // 1. 创建问题 2. 分配责任人 3. 修改问题描述 4.问题状态变更 5 删除问题数据
        ProjIssueOperLog log = new ProjIssueOperLog();
        log.setId(SnowFlakeUtil.getId());
        log.setIssueInfoId(item.getId());
        log.setOperType(operType);
        log.setModifyData(JSON.toJSONString(item));
        log.setCreaterId(user.getSysUserId());
        log.setUpdaterId(user.getSysUserId());
        log.setCreateTime(now);
        log.setUpdateTime(now);
        log.setIsDeleted(0);
        return log;
    }

    @Override
    public Result<List<IssueDataResp2>> queryIssue(QueryIssueReq queryIssueReq) {
        queryIssueReq.setOperationSource("supervisionCenter");
        Result<List<IssueDataResp>> listResult = issueInfoService.queryData(queryIssueReq);
        List<IssueDataResp2> issueDataResp2s = new ArrayList<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(listResult.getData())) {
            for (IssueDataResp issueDataResp : listResult.getData()) {
                IssueDataResp2 issueDataResp2 = new IssueDataResp2();
                BeanUtils.copyProperties(issueDataResp, issueDataResp2);
                issueDataResp2.setIssueClassificationId(String.valueOf(issueDataResp.getIssueClassificationId()));
                issueDataResp2s.add(issueDataResp2);
            }
        }
        return Result.success(issueDataResp2s);
    }

    @Override
    @Transactional
    public boolean saveDeductionClassificationDict(SaveDeductionClassificationDictType req) {
        Date now = new Date();
        // 新增
        if (req.getId() == null) {
            DictDeductionType dictDeductionType = new DictDeductionType();
            dictDeductionType.setId(SnowFlakeUtil.getId());
            dictDeductionType.setIsDeleted(0);
            dictDeductionType.setCreaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
            dictDeductionType.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
            dictDeductionType.setCreateTime(now);
            dictDeductionType.setUpdateTime(now);
            dictDeductionType.setName(req.getName());
            dictDeductionType.setCode(UUID.randomUUID().toString().replace("-", ""));
            dictDeductionType.setApplicableScene("none");
            dictDeductionType.setSortNo(99);
            dictDeductionType.setProjectStageCode(null);
            dictDeductionType.setIssueClassificationId(req.getIssueClassificationId());
            dictDeductionType.setServerType(req.getServerType());
            dictDeductionType.setDefaultScore(new BigDecimal(req.getDefaultScore()));
            dictDeductionTypeMapper.insert(dictDeductionType);
            return true;
        }

        DictDeductionType dictDeductionType = new DictDeductionType();
        dictDeductionType.setId(req.getId());
        dictDeductionType.setIssueClassificationId(req.getIssueClassificationId());
        dictDeductionType.setName(req.getName());
        dictDeductionType.setServerType(req.getServerType());
        if (StringUtils.isBlank(req.getDefaultScore())) {
            dictDeductionType.setDefaultScore(BigDecimal.ZERO);
        } else {
            dictDeductionType.setDefaultScore(new BigDecimal(req.getDefaultScore()));
        }
        dictDeductionType.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
        dictDeductionType.setUpdateTime(now);
        dictDeductionTypeMapper.updateById(dictDeductionType);
        return true;
    }

    @Override
    @Transactional
    public boolean deleteDeductionClassificationDict(Long id) {
        return 1 == dictDeductionTypeMapper.invalidateById(id);
    }

    @Override
    public List<DictDeductionTypeVO> queryDeductionClassificationDict(QueryDeductionClassificationDictParam req) {
        return dictDeductionTypeMapper.queryDeductionClassificationDict(req);
    }

    @Override
    public List<BaseCodeNameResp> queryServerTypeDict() {
        List<DictServerType> allServerType = dictServerTypeMapper.getAllServerType();
        if (CollectionUtils.isEmpty(allServerType)) {
            return Collections.emptyList();
        }
        return allServerType.stream().map(item -> new BaseCodeNameResp(item.getServerTypeCode(), item.getServerTypeName())).collect(Collectors.toList());
    }


    @Override
    @Transactional
    public List<BackendTeamDeductionRecordVO> queryServerTeamDeductionRecordForBackendTeam(QueryServerTeamDeductionRecordParam param) {
        // 数据来源，判断查询首验还是终验的数据
        String source;
        if (param.getOnlyOneCheckFlag()) {
            source = "final";
        } else if (Integer.valueOf(2).equals(param.getCurrentAcceptanceTimes())) {
            source = "final";
        } else {
            source = "first";
        }
        List<BackendTeamDeductionRecordVO> list = new ArrayList<>();

        ConfigCustomBackendDetailLimit businessSwitch = configCustomBackendDetailLimitMapper.getCustomBackendDetailLimit(param.getProjectInfoId(), 13);
        if (businessSwitch != null && Integer.valueOf(1).equals(businessSwitch.getOpenFlag())) {
            BackendTeamDeductionRecordVO businessRecord = serverTeamDeductionRecordMapper.getServerTeamDeductionRecordByProjectInfoIdAndServerType(param.getProjectInfoId(), BackendTeamTypeEnum.BUSINESS_TEAM.getCode());
            List<ProjProjectMember> projProjectMembers = projProjectMemberMapper.selectByProjectIdAndRole(param.getProjectInfoId(), 3L);
            if (CollectionUtils.isEmpty(projProjectMembers)) {
                throw new IllegalArgumentException("当前项目没有配置业务负责人");
            }
            SysUser sysUser = sysUserMapper.selectOne(new QueryWrapper<SysUser>().eq("sys_user_id", projProjectMembers.get(0).getProjectMemberId()));
            if (businessRecord == null) {
                ServerTeamDeductionRecord serverTeamDeductionRecord = new ServerTeamDeductionRecord();
                serverTeamDeductionRecord.setServerTeamDeductionRecordId(SnowFlakeUtil.getId());
                serverTeamDeductionRecord.setIsDeleted(0);
                serverTeamDeductionRecord.setCreaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                serverTeamDeductionRecord.setCreateTime(new Date());
                serverTeamDeductionRecord.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                serverTeamDeductionRecord.setUpdateTime(new Date());
                serverTeamDeductionRecord.setSource(source);
                serverTeamDeductionRecord.setServerTypeCode(BackendTeamTypeEnum.BUSINESS_TEAM.getCode());
                serverTeamDeductionRecord.setServerTeamYyId(sysUser.getDeptId());
                serverTeamDeductionRecord.setRecordStatus(1);
                serverTeamDeductionRecord.setProjectInfoId(param.getProjectInfoId());
                int i = serverTeamDeductionRecordMapper.saveServerTeamDeductionRecord(serverTeamDeductionRecord);
                businessRecord = serverTeamDeductionRecordMapper.getServerTeamDeductionRecordByProjectInfoIdAndServerType(param.getProjectInfoId(), BackendTeamTypeEnum.BUSINESS_TEAM.getCode());
            }
            businessRecord.setTotalScore(this.getTotalScore(param.getProjectInfoId(), source));
            businessRecord.setDeductionScore(this.getDeductionScore(param.getProjectInfoId(), BackendTeamTypeEnum.BUSINESS_TEAM, source));
            businessRecord.setToBeConfirmedCount(this.getToBeConfirmedCount(param.getProjectInfoId(), BackendTeamTypeEnum.BUSINESS_TEAM, source));
            list.add(businessRecord);
        }
        ConfigCustomBackendDetailLimit dataSwitch = configCustomBackendDetailLimitMapper.getCustomBackendDetailLimit(param.getProjectInfoId(), 14);
        if (dataSwitch != null && Integer.valueOf(1).equals(dataSwitch.getOpenFlag())) {
            BackendTeamDeductionRecordVO dataRecord = serverTeamDeductionRecordMapper.getServerTeamDeductionRecordByProjectInfoIdAndServerType(param.getProjectInfoId(), BackendTeamTypeEnum.DATA_TEAM.getCode());
            List<ProjProjectMember> projProjectMembers = projProjectMemberMapper.selectByProjectIdAndRole(param.getProjectInfoId(), 5L);
            if (CollectionUtils.isEmpty(projProjectMembers)) {
                throw new IllegalArgumentException("当前项目没有配置数据服务负责人");
            }
            SysUser sysUser = sysUserMapper.selectOne(new QueryWrapper<SysUser>().eq("sys_user_id", projProjectMembers.get(0).getProjectMemberId()));
            if (dataRecord == null) {
                ServerTeamDeductionRecord serverTeamDeductionRecord = new ServerTeamDeductionRecord();
                serverTeamDeductionRecord.setServerTeamDeductionRecordId(SnowFlakeUtil.getId());
                serverTeamDeductionRecord.setIsDeleted(0);
                serverTeamDeductionRecord.setCreaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                serverTeamDeductionRecord.setCreateTime(new Date());
                serverTeamDeductionRecord.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                serverTeamDeductionRecord.setUpdateTime(new Date());
                serverTeamDeductionRecord.setSource(source);
                serverTeamDeductionRecord.setServerTypeCode(BackendTeamTypeEnum.DATA_TEAM.getCode());
                serverTeamDeductionRecord.setServerTeamYyId(sysUser.getDeptId());
                serverTeamDeductionRecord.setRecordStatus(1);
                serverTeamDeductionRecord.setProjectInfoId(param.getProjectInfoId());
                int i = serverTeamDeductionRecordMapper.saveServerTeamDeductionRecord(serverTeamDeductionRecord);
                dataRecord = serverTeamDeductionRecordMapper.getServerTeamDeductionRecordByProjectInfoIdAndServerType(param.getProjectInfoId(), BackendTeamTypeEnum.DATA_TEAM.getCode());
            }
            dataRecord.setTotalScore(this.getTotalScore(param.getProjectInfoId(), source));
            dataRecord.setDeductionScore(this.getDeductionScore(param.getProjectInfoId(), BackendTeamTypeEnum.DATA_TEAM, source));
            dataRecord.setToBeConfirmedCount(this.getToBeConfirmedCount(param.getProjectInfoId(), BackendTeamTypeEnum.DATA_TEAM, source));
            list.add(dataRecord);
        }

        ConfigCustomBackendDetailLimit interfaceSwitch = configCustomBackendDetailLimitMapper.getCustomBackendDetailLimit(param.getProjectInfoId(), 15);
        if (interfaceSwitch != null && Integer.valueOf(1).equals(interfaceSwitch.getOpenFlag())) {
            BackendTeamDeductionRecordVO interfaceRecord = serverTeamDeductionRecordMapper.getServerTeamDeductionRecordByProjectInfoIdAndServerType(param.getProjectInfoId(), BackendTeamTypeEnum.INTERFACE_TEAM.getCode());
            List<ProjProjectMember> projProjectMembers = projProjectMemberMapper.selectByProjectIdAndRole(param.getProjectInfoId(), 7L);
            if (CollectionUtils.isEmpty(projProjectMembers)) {
                throw new IllegalArgumentException("当前项目没有配置业务负责人");
            }
            SysUser sysUser = sysUserMapper.selectOne(new QueryWrapper<SysUser>().eq("sys_user_id", projProjectMembers.get(0).getProjectMemberId()));

            if (interfaceRecord == null) {
                ServerTeamDeductionRecord serverTeamDeductionRecord = new ServerTeamDeductionRecord();
                serverTeamDeductionRecord.setServerTeamDeductionRecordId(SnowFlakeUtil.getId());
                serverTeamDeductionRecord.setIsDeleted(0);
                serverTeamDeductionRecord.setCreaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                serverTeamDeductionRecord.setCreateTime(new Date());
                serverTeamDeductionRecord.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                serverTeamDeductionRecord.setUpdateTime(new Date());
                serverTeamDeductionRecord.setSource(source);
                serverTeamDeductionRecord.setServerTypeCode(BackendTeamTypeEnum.INTERFACE_TEAM.getCode());
                serverTeamDeductionRecord.setServerTeamYyId(sysUser.getDeptId());
                serverTeamDeductionRecord.setRecordStatus(1);
                serverTeamDeductionRecord.setProjectInfoId(param.getProjectInfoId());
                int i = serverTeamDeductionRecordMapper.saveServerTeamDeductionRecord(serverTeamDeductionRecord);
                interfaceRecord = serverTeamDeductionRecordMapper.getServerTeamDeductionRecordByProjectInfoIdAndServerType(param.getProjectInfoId(), BackendTeamTypeEnum.INTERFACE_TEAM.getCode());
            }
            interfaceRecord.setTotalScore(this.getTotalScore(param.getProjectInfoId(), source));
            interfaceRecord.setDeductionScore(this.getDeductionScore(param.getProjectInfoId(), BackendTeamTypeEnum.INTERFACE_TEAM, source));
            interfaceRecord.setToBeConfirmedCount(this.getToBeConfirmedCount(param.getProjectInfoId(), BackendTeamTypeEnum.INTERFACE_TEAM, source));
            list.add(interfaceRecord);
        }
        return list;
    }

    private BigDecimal getTotalScore(Long projectInfoId, String source) {
        // 两次验收的第一次验收的评分记录
        List<ProjProjectClassificationScorePO> firstRecord = projProjectClassificationScoreMapper.getProjectClassificationScoreInfo(projectInfoId, source);
//        List<ProjProjectClassificationScorePO> firstRecord = projProjectClassificationScoreMapper.getProjectClassificationScoreInfo(projectInfoId, "first");

        // 只有一次验收或者两次验收的第二次验收的评分记录
//        List<ProjProjectClassificationScorePO> finalRecord = projProjectClassificationScoreMapper.getProjectClassificationScoreInfo(projectInfoId, "final");

        // 快速实施应用
        ProjProjectClassificationScorePO firstItem1 = firstRecord.stream()
                .filter(score1 -> "RapidImplementationApplication".equals(score1.getClassificationCode())).findFirst()
                .orElse(dictProjectAcceptRuleMapper.getProjectClassificationScore("RapidImplementationApplication"));

        // 云健康应用-产品应用功能点
        ProjProjectClassificationScorePO firstItem2 = firstRecord.stream()
                .filter(score1 -> "ProductFunctionApplication".equals(score1.getClassificationCode())).findFirst()
                .orElse(dictProjectAcceptRuleMapper.getProjectClassificationScore("ProductFunctionApplication"));

        // 云健康应用-设备接口
        ProjProjectClassificationScorePO firstItem3 = firstRecord.stream()
                .filter(score1 -> "EquipmentInterface".equals(score1.getClassificationCode())).findFirst()
                .orElse(dictProjectAcceptRuleMapper.getProjectClassificationScore("EquipmentInterface"));

        // 云健康应用-数据统计
        ProjProjectClassificationScorePO firstItem4 = firstRecord.stream()
                .filter(score1 -> "DataStatistics".equals(score1.getClassificationCode())).findFirst()
                .orElse(dictProjectAcceptRuleMapper.getProjectClassificationScore("DataStatistics"));

        // 满意度调查
        ProjProjectClassificationScorePO firstItem5 = firstRecord.stream()
                .filter(score1 -> "SatisfactionSurvey".equals(score1.getClassificationCode())).findFirst()
                .orElse(dictProjectAcceptRuleMapper.getProjectClassificationScore("SatisfactionSurvey"));

//        // 快速实施应用
//        ProjProjectClassificationScorePO finalItem1 = finalRecord.stream()
//                .filter(score1 -> "RapidImplementationApplication".equals(score1.getClassificationCode())).findFirst()
//                .orElse(dictProjectAcceptRuleMapper.getProjectClassificationScore("RapidImplementationApplication"));
//
//        // 云健康应用-产品应用功能点
//        ProjProjectClassificationScorePO finalItem2 = finalRecord.stream()
//                .filter(score1 -> "ProductFunctionApplication".equals(score1.getClassificationCode())).findFirst()
//                .orElse(dictProjectAcceptRuleMapper.getProjectClassificationScore("ProductFunctionApplication"));
//
//        // 云健康应用-设备接口
//        ProjProjectClassificationScorePO finalItem3 = finalRecord.stream()
//                .filter(score1 -> "EquipmentInterface".equals(score1.getClassificationCode())).findFirst()
//                .orElse(dictProjectAcceptRuleMapper.getProjectClassificationScore("EquipmentInterface"));
//
//        // 云健康应用-数据统计
//        ProjProjectClassificationScorePO finalItem4 = finalRecord.stream()
//                .filter(score1 -> "DataStatistics".equals(score1.getClassificationCode())).findFirst()
//                .orElse(dictProjectAcceptRuleMapper.getProjectClassificationScore("DataStatistics"));
//
//        // 满意度调查
//        ProjProjectClassificationScorePO finalItem5 = finalRecord.stream()
//                .filter(score1 -> "SatisfactionSurvey".equals(score1.getClassificationCode())).findFirst()
//                .orElse(dictProjectAcceptRuleMapper.getProjectClassificationScore("SatisfactionSurvey"));

//        // 只需要一次验收
//        if ("final".equals(source)) {
//            // fixme 只需要一次验收时的总得分
//            BigDecimal onlyOneTotalScore = finalItem1.getScore().add(finalItem2.getScore()).add(finalItem3.getScore()).add(finalItem4.getScore()).add(finalItem5.getScore());
//            return onlyOneTotalScore.stripTrailingZeros();
//        }
        // 首验总得分
        BigDecimal firstTotalScore = firstItem1.getScore().add(firstItem2.getScore()).add(firstItem3.getScore()).add(firstItem4.getScore()).add(firstItem5.getScore());
        return firstTotalScore.stripTrailingZeros();

//        // 验收总得分
//        BigDecimal totalScore = firstItem1.getScore().add(firstItem2.getScore()).add(firstItem3.getScore()).add(firstItem4.getScore()).add(firstItem5.getScore()).add(finalItem1.getScore()).add(finalItem2.getScore()).add(finalItem3.getScore()).add(finalItem4.getScore()).add(finalItem5.getScore());
//        return totalScore.stripTrailingZeros();
    }

    private BigDecimal getDeductionScore(Long projectInfoId, BackendTeamTypeEnum serverType, String source) {
        ArrayList<String> stageCodeList = new ArrayList<>();
        stageCodeList.add(source);
        BackendTeamDeductionDetailVO2 backendTeamDeductionDetailVO2 = new BackendTeamDeductionDetailVO2();
        backendTeamDeductionDetailVO2.setProjectInfoId(projectInfoId);
        backendTeamDeductionDetailVO2.setServerType(serverType.getCode());
        backendTeamDeductionDetailVO2.setStageCode(stageCodeList);

        List<BackendTeamDeductionDetailInfoVO> list = projDeductionDetailInfoMapper.getBackendTeamDeductionDetailInfo(backendTeamDeductionDetailVO2);

        return list.stream().map(BackendTeamDeductionDetailInfoVO::getDeductionAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private Integer getToBeConfirmedCount(Long projectInfoId, BackendTeamTypeEnum serverType, String source) {
        ArrayList<Integer> detailStatusList = new ArrayList<>();
        detailStatusList.add(3);

        ArrayList<String> stageCodeList = new ArrayList<>();
        stageCodeList.add(source);
        BackendTeamDeductionDetailVO2 backendTeamDeductionDetailVO2 = new BackendTeamDeductionDetailVO2();
        backendTeamDeductionDetailVO2.setProjectInfoId(projectInfoId);
        backendTeamDeductionDetailVO2.setServerType(serverType.getCode());
        backendTeamDeductionDetailVO2.setStageCode(stageCodeList);
        backendTeamDeductionDetailVO2.setDetailRecordStatus(detailStatusList);

        List<BackendTeamDeductionDetailInfoVO> list = projDeductionDetailInfoMapper.getBackendTeamDeductionDetailInfo(backendTeamDeductionDetailVO2);
        return list.size();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean sendConfirmation(SendConfirmationParam param) {
        if (CollectionUtils.isEmpty(param.getBackendTeamDeductionRecordIdList())) {
            throw new IllegalArgumentException("发送验收确认单，参数 backendTeamDeductionRecordIdList 不可为空");
        }
        Date now = new Date();
        for (Long recordId : param.getBackendTeamDeductionRecordIdList()) {
            ServerTeamDeductionRecord serverTeamDeductionRecord = serverTeamDeductionRecordMapper.selectById(recordId);
            if (serverTeamDeductionRecord == null) {
                throw new IllegalArgumentException("发送验收确认单，参数 backendTeamDeductionRecordIdList 存在无效的记录");
            }
            if (serverTeamDeductionRecord.getRecordStatus() == 1 || serverTeamDeductionRecord.getRecordStatus() == 3) {
                UpdateServerTeamDeductionRecordParam updateParam = new UpdateServerTeamDeductionRecordParam();
                updateParam.setServerTeamDeductionRecordId(recordId);
                updateParam.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                updateParam.setRecordStatus(2);
                updateParam.setQualityRemark(param.getQualityRemark());
                updateParam.setApplyUserId(userHelper.getCurrentSysUserIdWithDefaultValue());
                updateParam.setApplyTime(now);
                serverTeamDeductionRecordMapper.updateServerTeamDeductionRecord(updateParam);

                SysDept sysDept = sysDeptMapper.selectYunYingId(serverTeamDeductionRecord.getServerTeamYyId());
                SysUser sysUser = sysUserMapper.selectUserIdByYungyingId(sysDept.getDeptLeaderYunyingId());

                serverTeamScoreLogService.saveServerTeamScoreLog(recordId, 1, String.format("已发送后端确认单，待【%s】%s进行确认", sysDept.getDeptName(), sysUser.getUserName()), param.getQualityRemark());
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean revertConfirmation(RevertConfirmationParam param) {
        ServerTeamDeductionRecord serverTeamDeductionRecord = serverTeamDeductionRecordMapper.selectById(param.getBackendTeamDeductionRecordId());
        if (serverTeamDeductionRecord == null) {
            throw new IllegalArgumentException("撤回验收确认单，参数 backendTeamDeductionRecordId 无效");
        }
        if (serverTeamDeductionRecord.getRecordStatus() != 2) {
            throw new IllegalArgumentException("撤回验收确认单，仅允许撤回【待后端确认】的记录");
        }
        UpdateServerTeamDeductionRecordParam updateParam = new UpdateServerTeamDeductionRecordParam();
        updateParam.setServerTeamDeductionRecordId(param.getBackendTeamDeductionRecordId());
        updateParam.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
        updateParam.setRecordStatus(1);
        serverTeamDeductionRecordMapper.updateServerTeamDeductionRecord(updateParam);

        serverTeamDeductionRecordMapper.clearUserAndTime(param.getBackendTeamDeductionRecordId(), "apply");

        serverTeamScoreLogService.saveServerTeamScoreLog(param.getBackendTeamDeductionRecordId(), 2, "质管撤回验收确认单", "");
        return true;
    }

    @Override
    @Transactional
    public BackendTeamDeductionDetailVO queryBackendTeamDeductionDetail(QueryBackendDeductionRecordParam param) {
        ServerTeamDeductionRecord serverTeamDeductionRecord = serverTeamDeductionRecordMapper.selectById(param.getBackendTeamDeductionRecordId());
        if (serverTeamDeductionRecord == null) {
            throw new IllegalArgumentException("参数 backendTeamDeductionRecordId 无效");
        }

        BackendTeamDeductionDetailVO backendTeamDeductionDetailVO = new BackendTeamDeductionDetailVO();
        backendTeamDeductionDetailVO.setBackendTeamDeductionRecordId(serverTeamDeductionRecord.getServerTeamDeductionRecordId());
        backendTeamDeductionDetailVO.setQualityRemark(serverTeamDeductionRecord.getQualityRemark());
        backendTeamDeductionDetailVO.setBackendRemark(serverTeamDeductionRecord.getBackendRemark());
        backendTeamDeductionDetailVO.setRecordStatus(serverTeamDeductionRecord.getRecordStatus());
        backendTeamDeductionDetailVO.setRecordStatusName(serverTeamDeductionRecord.getRecordStatusName());
        backendTeamDeductionDetailVO.setTotalDeductionAmount(this.getDeductionScore(serverTeamDeductionRecord.getProjectInfoId(), BackendTeamTypeEnum.getBackendTeamTypeEnumByCode(serverTeamDeductionRecord.getServerTypeCode()), serverTeamDeductionRecord.getSource()));
        backendTeamDeductionDetailVO.setLogInfoList(serverTeamScoreLogService.queryOperationLogByServerTeamDeductionRecordId(param.getBackendTeamDeductionRecordId()));

        BackendTeamTypeEnum backendTeamTypeEnumByCode = BackendTeamTypeEnum.getBackendTeamTypeEnumByCode(serverTeamDeductionRecord.getServerTypeCode());
        long roleId;
        if (BackendTeamTypeEnum.BUSINESS_TEAM.equals(backendTeamTypeEnumByCode)) {
            roleId = 3L;
        } else if (BackendTeamTypeEnum.DATA_TEAM.equals(backendTeamTypeEnumByCode)) {
            roleId = 5L;
        } else {
            roleId = 7L;
        }
        List<ProjProjectMember> projProjectMembers = projProjectMemberMapper.selectByProjectIdAndRole(serverTeamDeductionRecord.getProjectInfoId(), roleId);
        if (CollectionUtils.isEmpty(projProjectMembers)) {
            throw new IllegalArgumentException("当前项目没有配置业务负责人");
        }


        SysDept sysDept = sysDeptMapper.selectYunYingId(serverTeamDeductionRecord.getServerTeamYyId());
        SysUser sysUser = sysUserMapper.selectOne(new QueryWrapper<SysUser>().eq("sys_user_id", projProjectMembers.get(0).getProjectMemberId()));

        Long currentSysUserId = userHelper.getCurrentSysUserIdWithDefaultValue();
        SysUser currentSysUser = sysUserMapper.selectBySysUserId(currentSysUserId);
        backendTeamDeductionDetailVO.setCanAudit(currentSysUser.getUserYunyingId().equals(sysDept.getDeptLeaderYunyingId()));

        BackendTeamDeductionDetailVO2 backendTeamDeductionDetailVO2 = new BackendTeamDeductionDetailVO2();
        backendTeamDeductionDetailVO2.setProjectInfoId(serverTeamDeductionRecord.getProjectInfoId());
        backendTeamDeductionDetailVO2.setServerType(serverTeamDeductionRecord.getServerTypeCode());
        backendTeamDeductionDetailVO2.setStageCode(param.getProjectStageCode());
        backendTeamDeductionDetailVO2.setDetailRecordStatus(param.getRecordStatus());

        List<BackendTeamDeductionDetailInfoVO> list = projDeductionDetailInfoMapper.getBackendTeamDeductionDetailInfo(backendTeamDeductionDetailVO2);
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(item -> {
                item.setServerTeamYyId(serverTeamDeductionRecord.getServerTeamYyId());
                item.setServerTeamName(sysDept.getDeptName());
                item.setServerTeamLeaderYyId(sysDept.getDeptLeaderYunyingId());
                item.setServerTeamLeaderName(sysUser.getUserName());
            });
        }

        backendTeamDeductionDetailVO.setDetailInfoList(list);
        return backendTeamDeductionDetailVO;
    }


    @Override
    @Transactional
    public List<ServerTeamDeductionRecordForBackendTeamVO> queryServerTeamDeductionRecordForBackendTeam(QueryServerTeamDeductionRecordForBackendTeamParam param) {
        List<ServerTeamDeductionRecordForBackendTeamVO> serverTeamDeductionRecordForBackendTeamVOS = serverTeamDeductionRecordMapper.queryServerTeamDeductionRecordForBackendTeam(param);
        Long currentSysUserId = userHelper.getCurrentSysUserIdWithDefaultValue();
        SysUser sysUser = sysUserMapper.selectBySysUserId(currentSysUserId);
        if (!CollectionUtils.isEmpty(serverTeamDeductionRecordForBackendTeamVOS)) {
            serverTeamDeductionRecordForBackendTeamVOS.forEach(item -> {
                item.setTotalScore(this.getTotalScore(item.getProjectInfoId(), item.getSource()));
                item.setDeductionScore(this.getDeductionScore(item.getProjectInfoId(), BackendTeamTypeEnum.getBackendTeamTypeEnumByCode(item.getServerTypeCode()), item.getSource()));
                item.setToBeConfirmedCount(this.getToBeConfirmedCount(item.getProjectInfoId(), BackendTeamTypeEnum.getBackendTeamTypeEnumByCode(item.getServerTypeCode()), item.getSource()));
                item.setCanAudit(sysUser.getUserYunyingId().equals(String.valueOf(item.getServerTeamLeaderYyId())));
            });
        }
        return serverTeamDeductionRecordForBackendTeamVOS;
    }

    @Override
    @Transactional
    public boolean invalidDeduction(DeductionDetailIdParam param) {
        ProjDeductionDetailInfo projDeductionDetailInfo = new ProjDeductionDetailInfo();
        projDeductionDetailInfo.setProjDeductionDetailInfoId(param.getBackendTeamDeductionDetailId());
        projDeductionDetailInfo.setIsDeleted(1);
        return 1 == projDeductionDetailInfoMapper.updateByPrimaryKeySelective(projDeductionDetailInfo);
    }

    @Override
    @Transactional
    public boolean submitConfirm(DeductionDetailIdParam param) {
        ProjDeductionDetailInfo projDeductionDetailInfo = new ProjDeductionDetailInfo();
        projDeductionDetailInfo.setProjDeductionDetailInfoId(param.getBackendTeamDeductionDetailId());
        projDeductionDetailInfo.setDetailStatus(1);
        return 1 == projDeductionDetailInfoMapper.updateByPrimaryKeySelective(projDeductionDetailInfo);
    }

    @Override
    @Transactional
    public boolean changeDeduction(ChangeDeductionParam param) {
        ProjDeductionDetailInfo projDeductionDetailInfo = new ProjDeductionDetailInfo();
        projDeductionDetailInfo.setProjDeductionDetailInfoId(param.getBackendTeamDeductionDetailId());
        projDeductionDetailInfo.setPracticalDeduction(param.getDeductionAmount());
        return 1 == projDeductionDetailInfoMapper.updateByPrimaryKeySelective(projDeductionDetailInfo);
    }

    @Override
    @Transactional
    public boolean backendOperation(BackendOperationParam param) {
        if ("confirm".equals(param.getOperationType())) {
            for (Long id : param.getIdList()) {
                ProjDeductionDetailInfo projDeductionDetailInfo = new ProjDeductionDetailInfo();
                projDeductionDetailInfo.setProjDeductionDetailInfoId(id);
                projDeductionDetailInfo.setDetailStatus(2);
                projDeductionDetailInfoMapper.updateByPrimaryKeySelective(projDeductionDetailInfo);
            }
            return true;
        } else if ("reject".equals(param.getOperationType())) {
            for (Long id : param.getIdList()) {
                ProjDeductionDetailInfo projDeductionDetailInfo = new ProjDeductionDetailInfo();
                projDeductionDetailInfo.setProjDeductionDetailInfoId(id);
                projDeductionDetailInfo.setDetailStatus(3);
                projDeductionDetailInfo.setBackendRemark(param.getRejectReason());
                projDeductionDetailInfoMapper.updateByPrimaryKeySelective(projDeductionDetailInfo);
            }
            return true;
        } else if ("revert".equals(param.getOperationType())) {
            for (Long id : param.getIdList()) {
                ProjDeductionDetailInfo projDeductionDetailInfo = new ProjDeductionDetailInfo();
                projDeductionDetailInfo.setProjDeductionDetailInfoId(id);
                projDeductionDetailInfo.setDetailStatus(1);
                projDeductionDetailInfoMapper.updateByPrimaryKeySelective(projDeductionDetailInfo);
            }
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public boolean backendConfirmOrReject(BackendOperationParam2 param) {
        ServerTeamDeductionRecord serverTeamDeductionRecord = serverTeamDeductionRecordMapper.selectById(param.getRecordId());
        if (serverTeamDeductionRecord == null) {
            throw new IllegalArgumentException("发送验收确认单，参数 backendTeamDeductionRecordIdList 存在无效的记录");
        }

        BackendTeamDeductionDetailVO2 backendTeamDeductionDetailVO2 = new BackendTeamDeductionDetailVO2();
        backendTeamDeductionDetailVO2.setProjectInfoId(serverTeamDeductionRecord.getProjectInfoId());
        backendTeamDeductionDetailVO2.setServerType(serverTeamDeductionRecord.getServerTypeCode());

        List<BackendTeamDeductionDetailInfoVO> detailList = projDeductionDetailInfoMapper.getBackendTeamDeductionDetailInfo(backendTeamDeductionDetailVO2);
        if (!CollectionUtils.isEmpty(detailList)) {
            List<Integer> detailStatusList = detailList.stream().map(BackendTeamDeductionDetailInfoVO::getDetailStatus).collect(Collectors.toList());
            // 存在未确认的明细项
            if (detailStatusList.contains(1)) {
                throw new IllegalArgumentException("请确认完所有的扣分明细项之后再提交验收确认单");
            }
            // 存在驳回状态的扣分明细项，只能进行驳回操作
            if (detailStatusList.contains(3)) {
                param.setOperationType("reject");
            }
        }

        Date now = new Date();
        // 后端确认
        if ("confirm".equals(param.getOperationType())) {
            if (serverTeamDeductionRecord.getRecordStatus() == 2) {
                UpdateServerTeamDeductionRecordParam updateParam = new UpdateServerTeamDeductionRecordParam();
                updateParam.setServerTeamDeductionRecordId(param.getRecordId());
                updateParam.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                updateParam.setRecordStatus(4);
                updateParam.setBackendRemark(param.getRejectReason());
                updateParam.setConfirmUserId(userHelper.getCurrentSysUserIdWithDefaultValue());
                updateParam.setConfirmTime(now);
                serverTeamDeductionRecordMapper.updateServerTeamDeductionRecord(updateParam);

                serverTeamScoreLogService.saveServerTeamScoreLog(param.getRecordId(), 4, "后端已确认", param.getRejectReason());
                return true;
            }
        } else if ("reject".equals(param.getOperationType())) {
            if (serverTeamDeductionRecord.getRecordStatus() == 2) {
                UpdateServerTeamDeductionRecordParam updateParam = new UpdateServerTeamDeductionRecordParam();
                updateParam.setServerTeamDeductionRecordId(param.getRecordId());
                updateParam.setUpdaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
                updateParam.setRecordStatus(3);
                updateParam.setBackendRemark(param.getRejectReason());
                serverTeamDeductionRecordMapper.updateServerTeamDeductionRecord(updateParam);

                SysUser sysUser = sysUserMapper.selectBySysUserId(serverTeamDeductionRecord.getApplyUserId());
                serverTeamScoreLogService.saveServerTeamScoreLog(param.getRecordId(), 3, String.format("后端已驳回，待【%s】进行确认", sysUser.getUserName()), param.getRejectReason());
                return true;
            }
        }
        return false;
    }
}
