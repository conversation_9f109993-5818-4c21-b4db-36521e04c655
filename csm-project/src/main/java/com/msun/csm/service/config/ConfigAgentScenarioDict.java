package com.msun.csm.service.config;

import java.util.List;

import com.msun.csm.common.model.Result;
import com.msun.csm.model.req.ConfigAgentScenarioDictReq;
import com.msun.csm.model.req.ConfigAgentScenarioDictSaveReq;
import com.msun.csm.model.resp.DictAgentChatDropdownResp;
import com.msun.csm.model.resp.DictAgentChatScenarioConfigResp;

/**
 * AI智能体场景配置服务接口
 * <AUTHOR> @since 2025-07-07 02:48:56
 */
public interface ConfigAgentScenarioDict {

    /**
     * 查询AI检测配置
     * @param req 查询条件（可选，为null时查询所有）
     * @return 返回AI检测配置列表
     */
    Result<List<DictAgentChatScenarioConfigResp>> getAgentScenarioConfig(ConfigAgentScenarioDictReq req);

    /**
     * 删除AI智能体场景配置（逻辑删除）
     * @param agentScenarioConfigId 配置ID
     * @return 删除结果
     */
    Result<Boolean> deleteAgentScenarioConfig(Long agentScenarioConfigId);

    /**
     * 保存AI智能体场景配置（新增或修改）
     * @param req 保存请求参数
     * @return 保存结果
     */
    Result<Boolean> saveAgentScenarioConfig(ConfigAgentScenarioDictSaveReq req);

}
