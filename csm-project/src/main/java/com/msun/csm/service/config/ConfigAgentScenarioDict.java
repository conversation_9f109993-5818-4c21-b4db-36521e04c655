package com.msun.csm.service.config;

import java.util.List;

import com.msun.csm.common.model.Result;
import com.msun.csm.model.req.ConfigAgentScenarioDictReq;
import com.msun.csm.model.resp.DictAgentChatScenarioConfigResp;

/**
 * AI智能体场景配置服务接口
 * <AUTHOR> @since 2025-07-07 02:48:56
 */
public interface ConfigAgentScenarioDict {

    /**
     * 查询所有AI图片检测配置
     * @return 返回AI图片检测配置列表
     */
    Result<List<DictAgentChatScenarioConfigResp>> getAgentScenarioConfig();

    /**
     * 根据条件查询AI图片检测配置
     * @param req 查询条件
     * @return 返回AI图片检测配置列表
     */
    Result<List<DictAgentChatScenarioConfigResp>> getAgentScenarioConfigByCondition(ConfigAgentScenarioDictReq req);

}
