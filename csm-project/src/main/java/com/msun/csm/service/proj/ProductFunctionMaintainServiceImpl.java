package com.msun.csm.service.proj;

import lombok.extern.slf4j.Slf4j;

import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.msun.csm.common.enums.HospitalTypeEnum;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.CsmPageResult;
import com.msun.csm.common.model.dto.ExcelHeaderRowDTO;
import com.msun.csm.common.model.dto.ExcelSheetInfoDTO;
import com.msun.csm.dao.entity.dict.DictHospitalType;
import com.msun.csm.dao.entity.dict.DictProductFunction;
import com.msun.csm.dao.entity.dict.DictProductFunctionOperationLog;
import com.msun.csm.dao.entity.proj.ExportProductFunctionVO;
import com.msun.csm.dao.entity.proj.ProductFunctionOperationLogVO;
import com.msun.csm.dao.entity.proj.ProductFunctionVO;
import com.msun.csm.dao.mapper.dict.DictHospitalTypeMapper;
import com.msun.csm.dao.mapper.dict.DictProductFunctionMapper;
import com.msun.csm.dao.mapper.dict.DictProductFunctionOperationLogMapper;
import com.msun.csm.model.param.QueryProductFunctionListParam;
import com.msun.csm.model.param.QueryProductFunctionListParam2;
import com.msun.csm.model.param.QueryProductFunctionOperationLogParam;
import com.msun.csm.model.param.SaveProductFunctionParam;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.util.ExcelUtil;
import com.msun.csm.util.PageHelperUtil;
import com.msun.csm.util.Sm4Util;
import com.msun.csm.util.SnowFlakeUtil;

@Slf4j
@Service
public class ProductFunctionMaintainServiceImpl implements ProductFunctionMaintainService {

    @Resource
    private DictHospitalTypeMapper dictHospitalTypeMapper;

    @Resource
    private DictProductFunctionMapper dictProductFunctionMapper;

    @Resource
    private DictProductFunctionOperationLogMapper dictProductFunctionOperationLogMapper;

    @Resource
    private UserHelper userHelper;

    @Override
    public List<BaseCodeNameResp> queryHospitalType() {
        List<DictHospitalType> hospitalTypeList = dictHospitalTypeMapper.queryAllHospitalType();
        if (CollectionUtils.isEmpty(hospitalTypeList)) {
            return Collections.emptyList();
        }

        return hospitalTypeList.stream().map(item -> new BaseCodeNameResp(item.getHospitalTypeCode(), item.getHospitalTypeName())).collect(Collectors.toList());
    }

    private DictProductFunction getDictProductFunctionBySaveParam(SaveProductFunctionParam param) {
        DictProductFunction dictProductFunction = new DictProductFunction();
        if (param.getId() == null) {
            dictProductFunction.setId(SnowFlakeUtil.getId());
            dictProductFunction.setCreaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
            dictProductFunction.setCreateTime(new Date());
            dictProductFunction.setFunctionCode(UUID.randomUUID().toString());
        } else {
            dictProductFunction.setId(param.getId());
        }
        dictProductFunction.setYyProductId(param.getYyProductId());
        dictProductFunction.setFunctionName(param.getFunctionName());
        dictProductFunction.setFunctionDesc(param.getFunctionDesc());
        dictProductFunction.setMarkingStandard(param.getMarkingStandard());
        dictProductFunction.setIsDelete(param.getIsDelete());
        if (param.getApplicableRange().contains(HospitalTypeEnum.PEOPLES_HOSPITAL.getHospitalTypeCode())) {
            dictProductFunction.setPeoplesHospitalFlag(1);
        } else {
            dictProductFunction.setPeoplesHospitalFlag(0);
        }
        if (param.getApplicableRange().contains(HospitalTypeEnum.CHINESE_HOSPITAL.getHospitalTypeCode())) {
            dictProductFunction.setChineseHospitalFlag(1);
        } else {
            dictProductFunction.setChineseHospitalFlag(0);
        }
        if (param.getApplicableRange().contains(HospitalTypeEnum.MATERNAL_CHILD_HOSPITAL.getHospitalTypeCode())) {
            dictProductFunction.setMaternalChildHospitalFlag(1);
        } else {
            dictProductFunction.setMaternalChildHospitalFlag(0);
        }
        if (param.getApplicableRange().contains(HospitalTypeEnum.TUMOR_HOSPITAL.getHospitalTypeCode())) {
            dictProductFunction.setTumorHospitalFlag(1);
        } else {
            dictProductFunction.setTumorHospitalFlag(0);
        }

        if (param.getApplicableRange().contains(HospitalTypeEnum.STOMATOLOGY_HOSPITAL.getHospitalTypeCode())) {
            dictProductFunction.setStomatologyHospitalFlag(1);
        } else {
            dictProductFunction.setStomatologyHospitalFlag(0);
        }
        if (param.getApplicableRange().contains(HospitalTypeEnum.EYE_HOSPITAL.getHospitalTypeCode())) {
            dictProductFunction.setEyeHospitalFlag(1);
        } else {
            dictProductFunction.setEyeHospitalFlag(0);
        }
        if (param.getCheckSql() != null) {
            dictProductFunction.setCheckSql(Sm4Util.decrypt(param.getCheckSql()));
            dictProductFunction.setCheckSqlMaintainerUserId(userHelper.getCurrentSysUserIdWithDefaultValue());
            dictProductFunction.setCheckSqlMaintenanceTime(new Date());
        }

        return dictProductFunction;
    }

    @Override
    @Transactional
    public boolean saveProductFunction(SaveProductFunctionParam param) {
        DictProductFunction dictProductFunction = getDictProductFunctionBySaveParam(param);

        if (param.getId() == null) {
            //新增
            dictProductFunctionMapper.insert(dictProductFunction);

            DictProductFunctionOperationLog dictProductFunctionOperationLog = new DictProductFunctionOperationLog();
            BeanUtils.copyProperties(dictProductFunction, dictProductFunctionOperationLog);
            dictProductFunctionOperationLog.setLogId(SnowFlakeUtil.getId());
            dictProductFunctionOperationLog.setDictProductFunctionId(dictProductFunction.getId());
            dictProductFunctionOperationLog.setCreateTime(new Date());
            dictProductFunctionOperationLog.setCreaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
            dictProductFunctionOperationLogMapper.insert(dictProductFunctionOperationLog);
            return true;
        }
        dictProductFunctionMapper.updateProductFunctionById(dictProductFunction);
        DictProductFunction dictProductFunction1 = dictProductFunctionMapper.selectById(param.getId());
        DictProductFunctionOperationLog dictProductFunctionOperationLog = new DictProductFunctionOperationLog();
        BeanUtils.copyProperties(dictProductFunction1, dictProductFunctionOperationLog);
        dictProductFunctionOperationLog.setLogId(SnowFlakeUtil.getId());
        dictProductFunctionOperationLog.setDictProductFunctionId(dictProductFunction.getId());
        dictProductFunctionOperationLog.setCreateTime(new Date());
        dictProductFunctionOperationLog.setCreaterId(userHelper.getCurrentSysUserIdWithDefaultValue());
        dictProductFunctionOperationLogMapper.insert(dictProductFunctionOperationLog);
        return true;
    }

    @Override
    public CsmPageResult<ProductFunctionVO> queryProductFunctionList(QueryProductFunctionListParam param) {
        QueryProductFunctionListParam2 queryProductFunctionListParam2 = new QueryProductFunctionListParam2();
        queryProductFunctionListParam2.setYyProductId(param.getYyProductId());
        queryProductFunctionListParam2.setEnabledFlag(param.getEnabledFlag());
        queryProductFunctionListParam2.setNoCheckSql(param.getNoCheckSql());
        queryProductFunctionListParam2.setHospitalType(param.getHospitalType());
        int dataCount = dictProductFunctionMapper.queryProductFunctionCount(queryProductFunctionListParam2);

        List<ProductFunctionVO> list = PageHelperUtil.queryPage(param.getPageNum(), param.getPageSize(), page -> dictProductFunctionMapper.queryProductFunctionList(queryProductFunctionListParam2));

        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(item -> {
                item.setApplicableRange(item.convertApplicableRange(item));
            });
        }
        CsmPageResult<ProductFunctionVO> pageList = new CsmPageResult<>();
        pageList.setPageNum(param.getPageNum());
        pageList.setPageSize(param.getPageSize());
        pageList.setTotal(dataCount);
        pageList.setPageResultList(list);
        return pageList;
    }

    @Override
    public void exportProductFunctionExcel(QueryProductFunctionListParam param, OutputStream outputStream) {
        QueryProductFunctionListParam2 queryProductFunctionListParam2 = new QueryProductFunctionListParam2();
        queryProductFunctionListParam2.setYyProductId(param.getYyProductId());
        queryProductFunctionListParam2.setEnabledFlag(param.getEnabledFlag());
        queryProductFunctionListParam2.setNoCheckSql(param.getNoCheckSql());
        queryProductFunctionListParam2.setHospitalType(param.getHospitalType());

        List<ExcelSheetInfoDTO> list = new ArrayList<>();
        List<ProductFunctionVO> hisData = dictProductFunctionMapper.queryProductFunctionList(queryProductFunctionListParam2);

        List<ExportProductFunctionVO> collect = hisData.stream().map(item -> {
            ExportProductFunctionVO exportProductFunctionVO = new ExportProductFunctionVO();
            exportProductFunctionVO.setYyProductName(item.getYyProductName());
            exportProductFunctionVO.setFunctionName(item.getFunctionName());
            exportProductFunctionVO.setFunctionDesc(item.getFunctionDesc());
            exportProductFunctionVO.setMarkingStandard(item.getMarkingStandard() == null ? "" : item.getMarkingStandard().toPlainString());
            exportProductFunctionVO.setCheckSql(item.getCheckSql());
            exportProductFunctionVO.setIsDelete(Integer.valueOf(0).equals(item.getIsDelete()) ? "启用" : "禁用");
            exportProductFunctionVO.setPeoplesHospitalFlag(Integer.valueOf(1).equals(item.getPeoplesHospitalFlag()) ? "是" : "否");
            exportProductFunctionVO.setChineseHospitalFlag(Integer.valueOf(1).equals(item.getChineseHospitalFlag()) ? "是" : "否");
            exportProductFunctionVO.setMaternalChildHospitalFlag(Integer.valueOf(1).equals(item.getMaternalChildHospitalFlag()) ? "是" : "否");
            exportProductFunctionVO.setTumorHospitalFlag(Integer.valueOf(1).equals(item.getTumorHospitalFlag()) ? "是" : "否");
            exportProductFunctionVO.setStomatologyHospitalFlag(Integer.valueOf(1).equals(item.getStomatologyHospitalFlag()) ? "是" : "否");
            exportProductFunctionVO.setEyeHospitalFlag(Integer.valueOf(1).equals(item.getEyeHospitalFlag()) ? "是" : "否");
            exportProductFunctionVO.setFunctionMaintainer(item.getFunctionMaintainer());
            exportProductFunctionVO.setFunctionMaintenanceTime(item.getFunctionMaintenanceTime());
            exportProductFunctionVO.setCheckSqlMaintainer(item.getCheckSqlMaintainer());
            exportProductFunctionVO.setCheckSqlMaintenanceTime(item.getCheckSqlMaintenanceTime());

            return exportProductFunctionVO;
        }).collect(Collectors.toList());
        ExcelSheetInfoDTO<ExportProductFunctionVO> hisDataSheet = new ExcelSheetInfoDTO<>();
        hisDataSheet.setSheetName("产品应用功能点");
        hisDataSheet.setSheetData(collect);
        ExcelHeaderRowDTO[] headerRow1 = {
                new ExcelHeaderRowDTO("产品名称", "yyProductName"),
                new ExcelHeaderRowDTO("功能检测点", "functionName"),
                new ExcelHeaderRowDTO("功能检测说明", "functionDesc"),
                new ExcelHeaderRowDTO("扣分分值", "markingStandard"),
                new ExcelHeaderRowDTO("功能检测脚本", "checkSql"),
                new ExcelHeaderRowDTO("是否启用", "isDelete"),
                new ExcelHeaderRowDTO("人民医院是否考核", "peoplesHospitalFlag"),
                new ExcelHeaderRowDTO("中医院是否考核", "chineseHospitalFlag"),
                new ExcelHeaderRowDTO("妇幼保健院是否考核", "maternalChildHospitalFlag"),
                new ExcelHeaderRowDTO("肿瘤医院是否考核", "tumorHospitalFlag"),
                new ExcelHeaderRowDTO("口腔医院是否考核", "stomatologyHospitalFlag"),
                new ExcelHeaderRowDTO("眼科医院是否考核", "eyeHospitalFlag"),
                new ExcelHeaderRowDTO("功能点维护人", "functionMaintainer"),
                new ExcelHeaderRowDTO("功能点维护时间", "functionMaintenanceTime"),
                new ExcelHeaderRowDTO("脚本维护人", "checkSqlMaintainer"),
                new ExcelHeaderRowDTO("脚本维护时间", "checkSqlMaintenanceTime"),
        };
        hisDataSheet.setHeaderRowArray(headerRow1);
        list.add(hisDataSheet);

        ExcelUtil.exportExcelBySheets(list, outputStream);
    }

    @Override
    public CsmPageResult<ProductFunctionOperationLogVO> queryProductFunctionOperationLog(QueryProductFunctionOperationLogParam param) {
        int dataCount = this.dictProductFunctionOperationLogMapper.queryProductFunctionOperationLogCount(param.getYyProductId(), param.getSearchKeyword());

        List<ProductFunctionOperationLogVO> list = PageHelperUtil.queryPage(param.getPageNum(), param.getPageSize(), page -> dictProductFunctionOperationLogMapper.queryProductFunctionOperationLogList(param.getYyProductId(), param.getSearchKeyword()));

        CsmPageResult<ProductFunctionOperationLogVO> pageList = new CsmPageResult<>();
        pageList.setPageNum(param.getPageNum());
        pageList.setPageSize(param.getPageSize());
        pageList.setTotal(dataCount);
        pageList.setPageResultList(list);
        return pageList;
    }
}
