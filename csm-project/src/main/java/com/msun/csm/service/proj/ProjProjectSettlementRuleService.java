package com.msun.csm.service.proj;

import java.util.List;

import com.msun.csm.common.enums.projsettlement.CheckResultEnum;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectSettlementCheck;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementCheckUploadDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementRuleDelFileDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementRuleQueryDTO;
import com.msun.csm.model.dto.projsetttlement.ProjProjectSettlementRuleSaveDTO;
import com.msun.csm.model.dto.projsetttlement.SettlementRuleParam;
import com.msun.csm.model.dto.projsetttlement.SettlementRuleTransport;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementRuleHistoryVO;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementRuleVO;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementSubmitEntryVO;
import com.msun.csm.model.vo.projsettlement.ShowFile;
import com.msun.csm.model.vo.user.SysUserVO;

/**
 * <AUTHOR>
 * @since 2024-06-17 10:49:37
 */

public interface ProjProjectSettlementRuleService {

    /**
     * 确认入场条件查询
     * 会在查询时创建规则并返回创建的规则
     *
     * @param projectSettlementRuleQueryDTO 请求参数
     * @return Result<ProjProjectSettlementSubmitEntryVO>
     */
    Result<ProjProjectSettlementSubmitEntryVO> findProjProjectSettlementRuleList(ProjProjectSettlementRuleQueryDTO projectSettlementRuleQueryDTO);

    Result<Boolean> saveSettlement(ProjProjectSettlementRuleSaveDTO settlementRuleSaveDTO);

    Result<Boolean> saveSettlement(ProjProjectSettlementRuleSaveDTO settlementRuleSaveDTO, SysUserVO sysUserVO);

    Result<Boolean> delSettlementFile(ProjProjectSettlementRuleDelFileDTO settlementRuleDelFileDTO);


    /**
     * 创建运营部审核规则
     * settlementRuleParam 规则参数
     * checkResultEnum 审核结果
     */
    void createRistRule(SettlementRuleTransport settlementRuleParam, CheckResultEnum checkResultEnum);

    /**
     * 创建规则和审核节点
     * 若 createRule = true, 则只创建规则. create = false, 只创建审核节点。 或者 createRule, createCheck均为true, 这种情况只在销售提交申请动态生成时使用
     * checkController.savesettlement
     *
     * @param settlementRuleParam 请求参数
     */
    void setRulesAndCheckBySence(SettlementRuleParam settlementRuleParam);

    /**
     * 查询审核节点有哪些
     *
     * @param projectInfoId 项目id
     * @return List<ProjProjectSettlementCheck>
     */
    List<ProjProjectSettlementCheck> findSettlementCheck(Long projectInfoId);

    /**
     * 获取首付款用的传输对象
     *
     * @param projectInfoId 项目id
     * @return SettlementRuleTransport 传输对象
     */
    SettlementRuleTransport getTransportForPayedSignage(Long projectInfoId);

    /**
     * 需要缴纳首付款
     *
     * @param transport 传输对象
     * @return boolean true: 需要缴纳, false: 不需要缴纳
     */
    boolean needPaySignage(SettlementRuleTransport transport);

    /**
     * 需要缴纳首付款
     *
     * @param projectInfoId 项目id
     * @return boolean true: 需要缴纳, false: 不需要缴纳
     */
    boolean needPaySignage(Long projectInfoId);

    /**
     * 创建分公司经理规则
     *
     * @param transport       规则生成参数
     * @param checkResultEnum 审核节点结果。null, 不做处理, 置空; CheckResultEnum.AUDIT_PASS, 通过; CheckResultEnum.AUDIT_FAIL, 驳回;
     */
    void createBranchMgrRule(SettlementRuleTransport transport, CheckResultEnum checkResultEnum);

    List<ProjProjectSettlementRuleHistoryVO> transformHistoryVO(List<ProjProjectSettlementRuleVO> voList,
                                                                String projectInfoId, int settlementStatus);

    List<ShowFile> getShowFileFileByProjectInfoId(Long projectFileId);

    List<ShowFile> getShowFileFileByProjectInfoId(List<ProjProjectFile> projProjectFiles);

    Result<Boolean> uploadFile(ProjProjectSettlementCheckUploadDTO settlementCheckUploadDTO);

    /**
     * 拆分项目使用, 会复制入驻规则及状态数据, 会向下一个审核节点发送消息通知
     * <p>
     * 新的项目需要重新走入驻流程, 会给销售发申请. 此接口只能在拆分项目中使用
     * </p>
     *
     * @param splitProject 拆分出的项目
     */
    void splitProject(ProjProjectInfo splitProject);

    /**
     * 拆分项目使用, 推送消息给销售
     *
     * @param splitProject 拆分的项目
     */
    void splitProjectSendMessage(ProjProjectInfo splitProject);
}
