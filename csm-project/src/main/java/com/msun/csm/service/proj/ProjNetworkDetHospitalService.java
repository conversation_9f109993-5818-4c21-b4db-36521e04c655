package com.msun.csm.service.proj;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjNetworkDetHospital;
import com.msun.csm.dao.entity.proj.ProjNetworkDetHospitalRelative;
import com.msun.csm.model.dto.networkdetect.DomainHolderParamDTO;
import com.msun.csm.model.dto.networkdetect.ProjNetworkDetDomainDTO;
import com.msun.csm.model.dto.networkdetect.ProjNetworkDetHospitalDTO;
import com.msun.csm.model.dto.networkdetect.ProjNetworkDetHospitalToolParamDTO;
import com.msun.csm.model.dto.networkdetect.ProjNetworkDetLogFileDTO;
import com.msun.csm.model.vo.networkdetected.ProjNetworkDetDomainVO;
import com.msun.csm.model.vo.networkdetected.ProjNetworkDetHospitalRelativeVO;
import com.msun.csm.model.vo.networkdetected.ProjNetworkDetHospitalStatusVO;

/**
 * <AUTHOR>
 * @since 2024-05-16 09:02:04
 */

public interface ProjNetworkDetHospitalService {


    /**
     * 获取测试域名
     * @return
     */
    String getTestDomain();
    /**
     * 列表查询
     *
     * @param projNetworkDetHospitalDTO
     * @return
     */
    Result<PageInfo<ProjNetworkDetHospitalRelativeVO>> findNetworkDetHospitalInfoList(
            ProjNetworkDetHospitalDTO projNetworkDetHospitalDTO);

    /**
     * 获取医院
     *
     * @param projNetworkDetHospitalDTO
     * @return
     */
    Result<ProjNetworkDetHospitalStatusVO> getNetworkDetHospitalStatusInfo(
            ProjNetworkDetHospitalDTO projNetworkDetHospitalDTO);

    /**
     * 根据条件查询详情（不分页）
     *
     * @param dto
     * @return
     */
    List<ProjNetworkDetHospitalRelative> findNetworkDetHospitalInfoListImpl(ProjNetworkDetHospitalDTO dto);

    void downloadNetworkDetectTool(ProjNetworkDetHospitalToolParamDTO toolParamDTO, HttpServletResponse response);

    /**
     * 新增和修改前置机数据
     *
     * @param projNetworkDetHospitalDTO
     * @return
     */
    int updateFrontMachineWithLinux(ProjNetworkDetHospitalDTO projNetworkDetHospitalDTO);

    void downloadNetworkLogFile(ProjNetworkDetLogFileDTO dto, HttpServletResponse response);
    //    Result<String> startDetectionWithLinux(ProjNetworkDetHospitalDTO hospitalDTO);

    int deleteFrontMachine(ProjNetworkDetHospitalDTO projNetworkDetHospitalDTO);

    ProjNetworkDetHospital findNetworkDetHospitalInfoByHospitalId(String logId);

    int updateDetHospitalByHospitalInfoId(ProjNetworkDetHospital detHospital);

    void downloadNetworkDetDescTxt(HttpServletResponse response);

    Result getFrontMachineById(ProjNetworkDetHospitalDTO projNetworkDetHospitalDTO);

    List<String> findDistinctDomainByProjectInfoId(String projectInfoId);

    ProjNetworkDetDomainVO getDomainAddressList(ProjNetworkDetDomainDTO domainDTO);

    /**
     * 根据测试域名配置key获取domainHolder所需参数
     *
     * @return DomainHolderParamDTO
     */
    DomainHolderParamDTO getDomainHolderParamDtoByTestDomainConfig();
}
