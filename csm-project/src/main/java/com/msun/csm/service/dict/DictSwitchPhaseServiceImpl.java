package com.msun.csm.service.dict;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.msun.csm.dao.entity.dict.DictSwitchPhase;
import com.msun.csm.dao.mapper.dict.DictSwitchPhaseMapper;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/9/23
 */

@Service
public class DictSwitchPhaseServiceImpl implements DictSwitchPhaseService {

    @Autowired
    private DictSwitchPhaseMapper dictSwitchPhaseMapper;

    @Override
    public int deleteByPrimaryKey(Long dictSwitchPhaseId) {
        return dictSwitchPhaseMapper.deleteByPrimaryKey(dictSwitchPhaseId);
    }

    @Override
    public int insert(DictSwitchPhase record) {
        return dictSwitchPhaseMapper.insert(record);
    }

    @Override
    public int insertOrUpdate(DictSwitchPhase record) {
        return dictSwitchPhaseMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(DictSwitchPhase record) {
        return dictSwitchPhaseMapper.insertOrUpdateSelective(record);
    }

    @Override
    public int insertSelective(DictSwitchPhase record) {
        return dictSwitchPhaseMapper.insertSelective(record);
    }

    @Override
    public DictSwitchPhase selectByPrimaryKey(Long dictSwitchPhaseId) {
        return dictSwitchPhaseMapper.selectByPrimaryKey(dictSwitchPhaseId);
    }

    @Override
    public int updateByPrimaryKeySelective(DictSwitchPhase record) {
        return dictSwitchPhaseMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(DictSwitchPhase record) {
        return dictSwitchPhaseMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<DictSwitchPhase> list) {
        return dictSwitchPhaseMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<DictSwitchPhase> list) {
        return dictSwitchPhaseMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<DictSwitchPhase> list) {
        return dictSwitchPhaseMapper.batchInsert(list);
    }

}
