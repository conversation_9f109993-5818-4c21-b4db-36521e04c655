package com.msun.csm.service.proj.producttask;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.msun.core.component.implementation.api.csm.dto.CsmDTO;
import com.msun.core.component.implementation.api.deviceanaysis.dto.ResponseData;
import com.msun.core.component.implementation.api.report.entity.dto.ReportRenderingsDTO;
import com.msun.core.component.implementation.api.report.entity.dto.ReportRenderingsListDTO;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.producttask.ProjProductTask;
import com.msun.csm.model.csm.CsmParamerDTO;
import com.msun.csm.model.csm.HisLoginCsmVo;
import com.msun.csm.model.param.ProjProductTaskParam;
import com.msun.csm.model.param.ProjProductTaskRecordParam;
import com.msun.csm.model.resp.producttask.ProjProductTaskResp;
import com.msun.csm.model.vo.OneCheckResultVO;


/**
 * <AUTHOR>
 * @description 针对表【proj_product_task(产品待处理任务表)】的数据库操作Service
 * @createDate 2024-07-25 18:14:54
 */
public interface ProjProductTaskService extends IService<ProjProductTask> {
    /**
     * 查询产品待办任务列表
     *
     * @param dto
     * @return
     */
    Result<List<ProjProductTaskResp>> selectProductTaskList(ProjProductTaskParam dto);

    /**
     * 确认完成状态
     *
     * @param dto
     * @return
     */
    Boolean updatePorductTaskData(ProjProductTaskParam dto);

    /**
     * 获取登录云健康链接
     *
     * @param dto
     * @return
     */
    Object hisLogin(ProjProductTaskParam dto);

    /**
     * 一键检测
     *
     * @param dto
     * @return
     */
    Result<OneCheckResultVO> oneCheck(ProjProductTaskParam dto, String message);

    /**
     * 待办进度
     *
     * @param dto
     * @return
     */
    Object selectProductTaskProgress(ProjProductTaskParam dto);

    String updateReviewRecordDataByParamer(ProjProductTaskRecordParam dto);

    /**
     * 查询数据
     *
     * @param dto
     * @return
     */
    ProjProductTaskResp selectTaskDataByTaskId(ProjProductTaskRecordParam dto);

    /**
     * 验证登录前跳转云健康
     *
     * @param param
     * @return
     */
    Result<HisLoginCsmVo> verifyLoginBeforeCloudHealthRedirect(CsmParamerDTO param);

    /**
     * 补偿接口
     * @return
     */
    Result compensationInterface();

    /**
     * 获取产品待办，待办内容取填鸭字典数据
     *
     * @param param 参数
     * @return 产品待办
     */
    List<ProjProductTaskResp> getProductTaskWithDictInfoList(ProjProductTaskParam param);


    /**
     * 获取产品待办，待办内容取填鸭字典数据
     *
     * @param param 参数
     * @return 产品待办
     */
    List<ProjProductTask> getProductTaskWithDictInfo(ProjProductTaskParam param);

    /**
     * 打印平台查询交付平台医院下报表打印节点数据
     * @param csmDTO
     * @return
     */
    ResponseData getPrintCodeResultListByParamer(CsmDTO csmDTO);

    /**
     * 给负责人发送消息
     * @param csmDTO
     * @return
     */
    ResponseData sendReportPrintMsg(CsmDTO csmDTO);

    /**
     * 查询负责人手机号
     * @param csmDTO
     * @return
     */
    ResponseData getPersonByParamer(CsmDTO csmDTO);

    /**
     * 上传单据打印效果
     *
     * @param reportRenderingsDTO
     * @return
     */
    ResponseData saveMedicalPrinterConfigRenderings(ReportRenderingsDTO reportRenderingsDTO);

    /**
     * 上传协助记录附件
     * @param reportRenderingsDTO
     * @return
     */
    ResponseData uploadAssistAttachment(ReportRenderingsListDTO reportRenderingsDTO);

    /**
     * 急诊挂单
     * @param dto
     * @return
     */
    Result sheetPendingOrder(ProjProductTaskResp dto);
}
