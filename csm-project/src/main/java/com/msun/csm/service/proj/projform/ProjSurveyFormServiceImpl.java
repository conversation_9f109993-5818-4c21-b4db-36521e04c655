package com.msun.csm.service.proj.projform;


import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.util.StrUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.msun.core.component.implementation.filter.ImplHospitalDomainHolder;
import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.enums.MilestoneNodeEnum;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.projform.FormSourceType;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.dict.DictProduct;
import com.msun.csm.dao.entity.dict.DictProductVsModules;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjProductBacklog;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjSurveyPlan;
import com.msun.csm.dao.entity.proj.projform.DictFormType;
import com.msun.csm.dao.entity.proj.projform.ProjFormExamineLog;
import com.msun.csm.dao.entity.proj.projform.ProjSurveyForm;
import com.msun.csm.dao.entity.proj.projreport.ConfigCustomBackendDetailLimit;
import com.msun.csm.dao.entity.proj.projreport.ProjBusinessExamineLog;
import com.msun.csm.dao.mapper.comm.ProjBusinessExamineLogMapper;
import com.msun.csm.dao.mapper.dict.DictProductVsModulesMapper;
import com.msun.csm.dao.mapper.formlibrary.LibProductFormMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProductBacklogMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectFileMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyPlanMapper;
import com.msun.csm.dao.mapper.projform.DictFormTypeMapper;
import com.msun.csm.dao.mapper.projform.ProjFormExamineLogMapper;
import com.msun.csm.dao.mapper.projform.ProjSurveyFormMapper;
import com.msun.csm.model.param.ProjProductTaskParam;
import com.msun.csm.model.req.formlibrary.LibFormReq;
import com.msun.csm.model.req.projform.ProjSurveyFormAddReq;
import com.msun.csm.model.req.projform.ProjSurveyFormReq;
import com.msun.csm.model.req.projform.ProjSurveyFormResponsibilitiesReq;
import com.msun.csm.model.req.projform.ProjSurveyFormUpdateReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportReviewExamineReq;
import com.msun.csm.model.req.todotask.SaveOrUpdateTodoTaskParam;
import com.msun.csm.model.resp.projform.ProjSurveyFormMenuResp;
import com.msun.csm.model.resp.projform.ProjSurveyFormParamerResp;
import com.msun.csm.model.resp.projform.ProjSurveyFormResp;
import com.msun.csm.model.resp.projform.ProjSurveyReprotFormCount;
import com.msun.csm.model.resp.projform.ProjSurveyReprotFormPageResp;
import com.msun.csm.model.resp.projreport.ProjBusinessExamineLogResp;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.common.BaseQueryService;
import com.msun.csm.service.proj.ProjProjectFileService;
import com.msun.csm.service.proj.ProjProjectInfoService;
import com.msun.csm.service.proj.ProjProjectPlanService;
import com.msun.csm.service.proj.ProjTodoTaskService;
import com.msun.csm.service.proj.producttask.ProjProductTaskService;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.StringUtils;
import com.msun.csm.util.obs.OBSClientUtils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description 针对表【proj_survey_form(表单主表)】的数据库操作Service实现
 * @createDate 2024-09-14 15:15:49
 */
@Service
@Slf4j
public class ProjSurveyFormServiceImpl extends ServiceImpl<ProjSurveyFormMapper, ProjSurveyForm> implements ProjSurveyFormService {

    @Resource
    private ProjSurveyFormMapper projSurveyFormMapper;

    @Resource
    private DictFormTypeMapper dictFormTypeMapper;

    @Resource
    private ProjProjectFileService projProjectFileService;

    @Resource
    private ProjProjectFileMapper projProjectFileMapper;

    @Resource
    private ProjFormExamineLogMapper projFormExamineLogMapper;


    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;

    @Resource
    private ImplHospitalDomainHolder domainHolder;

    @Resource
    private ProjProductBacklogMapper productBacklogMapper;

    @Resource
    private DictProductVsModulesMapper productVsModulesMapper;

    @Lazy
    @Resource
    private ProjProductTaskService projProductTaskService;

    @Resource
    @Lazy
    private ProjProjectInfoService projProjectInfoService;

    @Lazy
    @Resource
    private ProjMilestoneInfoMapper projMilestoneInfoMapper;

    @Lazy
    @Resource
    private ProjSurveyPlanMapper projSurveyPlanMapper;

    @Value("${requestUrl}")
    private String requestUrl;

    @Resource
    private UserHelper userHelper;

    @Resource
    private LibProductFormMapper libProductFormMapper;

    @Resource
    private ProjProjectPlanService projProjectPlanService;

    @Resource
    private ProjTodoTaskService projTodoTaskService;

    @Resource
    @Lazy
    private ProjProjectInfoMapper projProjectInfoMapper;

    @Resource
    @Lazy
    private ProjBusinessExamineLogMapper projBusinessExamineLogMapper;

    @Resource
    @Lazy
    private BaseQueryService baseQueryService;

    /**
     * @param projSurveyFormReq
     * @return
     */
    @Override
    public Result<ProjSurveyReprotFormPageResp<ProjSurveyFormResp>> selectSurveyFormByPage(ProjSurveyFormReq projSurveyFormReq) {
        List<ProjSurveyReprotFormCount> projSurveyReprotFormPageResp = projSurveyFormMapper.selectSurveyFormCount(projSurveyFormReq);
        // 列表查询
        PageHelper.startPage(projSurveyFormReq.getPageNum(), projSurveyFormReq.getPageSize());
        List<ProjSurveyFormResp> projSurveyReportResps = projSurveyFormMapper.selectSurveyFormByPage(projSurveyFormReq);
        ProjSurveyReprotFormPageResp<ProjSurveyFormResp> returnPage = new ProjSurveyReprotFormPageResp<ProjSurveyFormResp>(projSurveyReportResps);
        ProjProjectInfo projectInfo = projProjectInfoService.selectByPrimaryKey(projSurveyFormReq.getProjectInfoId());
        // 查询该项目是否需要审核人，提交审核
        if (projectInfo != null && projectInfo.getProjectInfoId() != null) {
            ConfigCustomBackendDetailLimit limit = baseQueryService.isOpenAuditorFlag(projectInfo.getProjectInfoId(), 12);
            returnPage.setIsNeedAuditorFlag(limit != null && limit.getOpenFlag() != null && limit.getOpenFlag() != 0);
        }
        if (projSurveyFormReq.getPageSource() != null && ("backend_form_design".equals(projSurveyFormReq.getPageSource()) || "backend_form_audit".equals(projSurveyFormReq.getPageSource()))) {
            returnPage.setIsNeedAuditorFlag(true);
        }
        // 展示设计制作，验收后不展示此按钮
        Boolean isDesignMakeFlag = true;
        if (projectInfo != null && projectInfo.getAcceptTime() != null) {
            isDesignMakeFlag = false;
        }
        if (projSurveyReportResps != null && projSurveyReportResps.size() > 0) {
            Set<Long> projectInfoIds = projSurveyReportResps.stream().map(ProjSurveyFormResp::getProjectInfoId).collect(Collectors.toSet());
            List<ProjProjectInfo> projList = projProjectInfoMapper.selectList(new QueryWrapper<ProjProjectInfo>().in("project_info_id", projectInfoIds));
            Map<Long, ProjProjectInfo> projectInfoMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(projList)) {
                projectInfoMap = projList.stream().collect(Collectors.toMap(ProjProjectInfo::getProjectInfoId, Function.identity()));
            }
            for (ProjSurveyFormResp projSurveyReportResp : projSurveyReportResps) {
                ProjProjectInfo projectInfoss = projectInfoMap.get(projSurveyReportResp.getProjectInfoId());
                if (userHelper != null && userHelper.getCurrentUser() != null && userHelper.getCurrentUser().getSysUserId() != null && projectInfoss != null && projectInfoss.getProjectInfoId() != null) {
                    projSurveyReportResp.setIsLeaderOrProjectManagerFlag(true);
                } else {
                    projSurveyReportResp.setIsLeaderOrProjectManagerFlag(false);
                }
                if (projSurveyFormReq.getPageSource() != null && ("backend_form_design".equals(projSurveyFormReq.getPageSource()) || "backend_form_audit".equals(projSurveyFormReq.getPageSource()))) {
                    isDesignMakeFlag = true;
                    projSurveyReportResp.setIsLeaderOrProjectManagerFlag(true);
                }
                projSurveyReportResp.setFormSourceStr(FormSourceType.getDescByCode(projSurveyReportResp.getFormSource()));
                // 获取文件信息
                String[] surveyImgs = null;
                if (StringUtils.isNotEmpty(projSurveyReportResp.getSurveyImgs())) {
                    surveyImgs = projSurveyReportResp.getSurveyImgs().split(",");
                }
                String[] finishImgs = null;
                if (StringUtils.isNotEmpty(projSurveyReportResp.getFinishImgs())) {
                    finishImgs = projSurveyReportResp.getFinishImgs().split(",");
                }
                List<ProjProjectFile> surveyImgsFileList = new ArrayList<>();
                if (surveyImgs != null && surveyImgs.length > 0) {
                    List<Long> surveyImgsList = new ArrayList<>();
                    for (String surveyImg : surveyImgs) {
                        try {
                            surveyImgsList.add(Long.valueOf(surveyImg));
                        } catch (Exception e) {
                            log.error("转换失败", e);
                        }
                    }
                    if (surveyImgsList != null && surveyImgsList.size() > 0) {
                        surveyImgsFileList = projProjectFileMapper.selectList(new QueryWrapper<ProjProjectFile>().eq("project_info_id", projSurveyReportResp.getProjectInfoId()).in("project_file_id", surveyImgsList));
                        surveyImgsFileList.stream().filter(item -> !item.getFilePath().startsWith("http")).forEach(item -> item.setFilePath(OBSClientUtils.getTemporaryUrl(item.getFilePath(), 3600)));
                    }
                }
                List<ProjProjectFile> finishImgsFileList = new ArrayList<>();
                if (finishImgs != null && finishImgs.length > 0) {
                    List<Long> finishImgsList = new ArrayList<>();
                    for (String finishImg : finishImgs) {
                        try {
                            finishImgsList.add(Long.valueOf(finishImg));
                        } catch (Exception e) {
                            log.error("转换失败", e);
                        }
                    }
                    if (finishImgsList != null && finishImgsList.size() > 0) {
                        finishImgsFileList = projProjectFileMapper.selectList(new QueryWrapper<ProjProjectFile>().eq("project_info_id", projSurveyReportResp.getProjectInfoId()).in("project_file_id", finishImgsList));
                        finishImgsFileList.stream().filter(item -> !item.getFilePath().startsWith("http")).forEach(item -> item.setFilePath(OBSClientUtils.getTemporaryUrl(item.getFilePath(), 3600)));
                    }
                }
                projSurveyReportResp.setSurveyImgsList(surveyImgsFileList);
                projSurveyReportResp.setFinishImgsList(finishImgsFileList);
                // url 与产品不存在时不展示设计按钮
                if (isDesignMakeFlag) {
                    isDesignMakeFlag = projSurveyReportResp.getFormPageUrl() != null && !"".equals(projSurveyReportResp.getFormPageUrl()) && projSurveyReportResp.getCloudProductCode() != null && !"".equals(projSurveyReportResp.getCloudProductCode());
                }
                projSurveyReportResp.setIsDesignMakeFlag(isDesignMakeFlag);
                List<Integer> integers = Arrays.asList(4, 5, 6);
                if (integers.contains(projSurveyReportResp.getFinishStatus()) && !returnPage.getIsNeedAuditorFlag()) {
                    projSurveyReportResp.setFinishStatus(0);
                }
            }
        }
        if (projSurveyReprotFormPageResp != null && projSurveyReprotFormPageResp.size() > 0) {
            returnPage.setTotalCount(projSurveyReprotFormPageResp.get(0).getTotalCount());
            returnPage.setPreLaunchCompletionCount(projSurveyReprotFormPageResp.get(0).getPreLaunchCompletionCount());
            returnPage.setIncompleteCount(projSurveyReprotFormPageResp.get(0).getIncompleteCount());
            returnPage.setRejectedCount(projSurveyReprotFormPageResp.get(0).getRejectedCount());
        }
        return Result.success(returnPage);
    }

    /**
     * 删除表单数据
     *
     * @param projSurveyFormReq
     * @return
     */
    @Override
    public Result deleteSurveyForm(ProjSurveyFormUpdateReq projSurveyFormReq) {
        ProjSurveyForm form = this.getById(projSurveyFormReq.getSurveyFormId());
        form.setIsDeleted(1);
        this.updateById(form);
        projProjectPlanService.addTotalCountByProjectInfoIdAndItemCode(form.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_FORM, -1);
        projTodoTaskService.todoTaskTotalCountSync(form.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_FORM.getPlanItemCode());
        projTodoTaskService.todoTaskTotalCountSync(form.getProjectInfoId(), DictProjectPlanItemEnum.PREPARAT_FORM.getPlanItemCode());
        SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
        param.setProjectInfoId(form.getProjectInfoId());
        param.setHospitalInfoId(form.getHospitalInfoId());
        param.setUserId(form.getMakeUserId());
        param.setCode(DictProjectPlanItemEnum.PREPARAT_FORM.getPlanItemCode());
        try {
            projTodoTaskService.projectTodoTaskInit(param);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("projectTodoTaskInit调用失败");
        }
        return Result.success("删除成功");
    }

    /**
     * 设计制作
     *
     * @param projSurveyFormReq
     * @return
     */
    @Override
    public Result formMark(ProjSurveyFormUpdateReq projSurveyFormReq) {
        try {
            ProjSurveyForm form = this.getById(projSurveyFormReq.getSurveyFormId());
            if (!(form.getFormPageUrl() != null && !"".equals(form.getFormPageUrl()))) {
                return Result.fail("未能找到与表单对应的节点，请前往相应系统进行制作");
            }
            ProjProductTaskParam dto = new ProjProductTaskParam();
            dto.setHospitalInfoId(form.getHospitalInfoId());
            dto.setCloudProductCode(form.getCloudProductCode());
            dto.setTaskPageUrl(form.getFormPageUrl());
            Map<String, Object> map = new HashMap<>();
            // TODO 参数拼装
            dto.setData(JSON.toJSONString(map));
            Object str = projProductTaskService.hisLogin(dto);
            SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
            param.setProjectInfoId(form.getProjectInfoId());
            param.setHospitalInfoId(form.getHospitalInfoId());
            param.setUserId(form.getMakeUserId());
            param.setCode(DictProjectPlanItemEnum.PREPARAT_FORM.getPlanItemCode());
            projTodoTaskService.updateProjectTodoTaskStatus(param);
            if (str != null && !"".equals(str.toString())) {
                return Result.success(str);
            } else {
                throw new CustomException("未检测到医院信息，请联系管理员");
            }
        } catch (Exception e) {
            throw new CustomException("未检测到医院信息，请联系管理员");
        }
    }

    /**
     * 表单审核驳回
     *
     * @param projSurveyFormReq
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result updateExamineFormStatus(ProjSurveyFormUpdateReq projSurveyFormReq) {
        ProjSurveyForm form = this.getById(projSurveyFormReq.getSurveyFormId());
        form.setExamineOpinion(projSurveyFormReq.getExamineOpinion());
        form.setFinishStatus(projSurveyFormReq.getFinishStatus());
        this.updateById(form);
        //  插入审核日志
        ProjFormExamineLog log = new ProjFormExamineLog();
        log.setFormExamineLogId(form.getSurveyFormId());
        log.setExamineStatus(form.getFinishStatus());
        log.setFormExamineLogId(SnowFlakeUtil.getId());
        log.setExamineOpinion(form.getExamineOpinion());
        projFormExamineLogMapper.insert(log);
        SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
        param.setProjectInfoId(form.getProjectInfoId());
        param.setHospitalInfoId(form.getHospitalInfoId());
        param.setUserId(form.getMakeUserId());
        param.setCode(DictProjectPlanItemEnum.PREPARAT_FORM.getPlanItemCode());
        projTodoTaskService.updateProjectTodoTaskStatus(param);
        return Result.success("驳回");
    }

    /**
     * 更新表单责任人
     *
     * @param projSurveyFormReq
     * @return
     */
    @Override
    public Result updateFormResponsibilities(ProjSurveyFormResponsibilitiesReq projSurveyFormReq) {
        if (projSurveyFormReq.getIds() != null && projSurveyFormReq.getIds().size() > 0) {
            for (Long surveyFormId : projSurveyFormReq.getIds()) {
                ProjSurveyForm form = this.getById(surveyFormId);
                if (projSurveyFormReq.getMakeUserId() != null && !"0".equals(projSurveyFormReq.getMakeUserId())) {
                    form.setMakeUserId(projSurveyFormReq.getMakeUserId());
                }
                if (projSurveyFormReq.getOnlineEssential() != null) {
                    form.setOnlineEssential(projSurveyFormReq.getOnlineEssential());
                }
                this.updateById(form);
                SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
                param.setProjectInfoId(form.getProjectInfoId());
                param.setHospitalInfoId(form.getHospitalInfoId());
                param.setUserId(projSurveyFormReq.getMakeUserId());
                param.setCode(DictProjectPlanItemEnum.PREPARAT_FORM.getPlanItemCode());
                projTodoTaskService.projectTodoTaskInit(param);
            }
        } else {
            return Result.fail("请选择要更新的表单");
        }
        return Result.success("更新成功");
    }

    /**
     * @param projSurveyFormReq
     * @return
     */
    @Override
    public Result<List<ProjSurveyFormMenuResp>> getFormMenuByProjectId(ProjSurveyFormUpdateReq projSurveyFormReq) {
        List<ProjSurveyFormMenuResp> list = new ArrayList<>();
        ProjProjectInfo projectInfo = projProjectInfoService.selectByPrimaryKey(projSurveyFormReq.getProjectInfoId());
        // 查询项目下调研阶段报表是否使用的新流程
        List<ProjMilestoneInfo> milestoneInfoList = projMilestoneInfoMapper.selectList(new QueryWrapper<ProjMilestoneInfo>().eq("project_info_id", projSurveyFormReq.getProjectInfoId()).in("milestone_node_code", MilestoneNodeEnum.SURVEY_FORM.getCode()).eq("invalid_flag", NumberEnum.NO_0.num()));
        Boolean isNewFlow = milestoneInfoList != null && milestoneInfoList.size() > 0 && milestoneInfoList.get(NumberEnum.NO_0.num()).getIsComponent() != null && !"".equals(milestoneInfoList.get(NumberEnum.NO_0.num()).getIsComponent());
        // 单体
        if (projectInfo.getProjectType() != null && NumberEnum.NO_1.num().equals(projectInfo.getProjectType())) {
            if (isNewFlow) {
                list.add(new ProjSurveyFormMenuResp("FormSurvey", "表单任务分配及制作"));
            } else {
                list.add(new ProjSurveyFormMenuResp("TaskAllocAndMake", "表单任务分配及制作"));
            }
        } else {
            list.add(new ProjSurveyFormMenuResp("AreaFormInit", "区域表单初始化"));
            if (isNewFlow) {
                list.add(new ProjSurveyFormMenuResp("FormSurvey", "表单任务分配及制作"));
            } else {
                list.add(new ProjSurveyFormMenuResp("TaskAllocAndMake", "表单任务分配及制作"));
            }
        }
        LibFormReq libFormReq = new LibFormReq();
        libFormReq.setProjectInfoId(projSurveyFormReq.getProjectInfoId());
        List<DictProduct> listData = libProductFormMapper.selectProductListData(libFormReq);
        if (listData != null && listData.size() > 0) {
            list.add(new ProjSurveyFormMenuResp("FormResourceLibrary", "护理表单资源库引用"));
        }
//        list.add(new ProjSurveyFormMenuResp("NursingDocument", "护理文书模板引用"));
        List<DictProduct> aimsListData = libProductFormMapper.selectAimsProductListData(libFormReq);
        if (aimsListData != null && aimsListData.size() > 0) {
            list.add(new ProjSurveyFormMenuResp("SmFormResourceLibrary", "手麻表单资源库引用"));
        }
        return Result.success(list);
    }

    /**
     * 查询所需参数
     *
     * @param projSurveyFormReq
     * @return
     */
    @Override
    public Result<ProjSurveyFormParamerResp> getFormParamer(ProjSurveyFormUpdateReq projSurveyFormReq) {
        ProjSurveyFormParamerResp resp = new ProjSurveyFormParamerResp();
        List<BaseCodeNameResp> sourceList = new ArrayList<>();
        for (FormSourceType item : FormSourceType.values()) {
            sourceList.add(new BaseCodeNameResp(item.getCode(), item.getName()));
        }
        List<DictFormType> formTypeList = dictFormTypeMapper.selectList(new QueryWrapper<DictFormType>().eq("is_deleted", NumberEnum.NO_0.num()).eq(ObjectUtil.isNotEmpty(projSurveyFormReq.getYyProductId()), "yy_product_id", projSurveyFormReq.getYyProductId()).eq(ObjectUtil.isEmpty(projSurveyFormReq.getYyProductId()), "yy_product_id", -1));
        List<BaseCodeNameResp> formTypeListResp = formTypeList.stream().map(item -> new BaseCodeNameResp(item.getTypeCode(), item.getTypeName())).collect(Collectors.toList());
        resp.setSourceList(sourceList);
        resp.setFormTypeList(formTypeListResp);
        return Result.success(resp);
    }

    /**
     * 批量保存
     *
     * @param projSurveyFormAddReqs
     * @return
     */
    @Override
    public Result batchFormSave(List<ProjSurveyFormAddReq> projSurveyFormAddReqs) {
        if (CollectionUtil.isEmpty(projSurveyFormAddReqs)) {
            return Result.success("无需保存");
        }
        // 调研负责人
        Long surveyUserId = null;
        List<ProjSurveyPlan> surveyPlan = projSurveyPlanMapper.selectList(new QueryWrapper<ProjSurveyPlan>().eq("project_info_id", projSurveyFormAddReqs.get(NumberEnum.NO_0.num()).getProjectInfoId()).eq("yy_product_id", projSurveyFormAddReqs.get(NumberEnum.NO_0.num()).getYyProductId()));
        if (surveyPlan != null && !surveyPlan.isEmpty()) {
            surveyUserId = surveyPlan.get(NumberEnum.NO_0.num()).getSurveyUserId();
        }
        List<DictFormType> typeList = dictFormTypeMapper.selectList(new QueryWrapper<DictFormType>().eq("is_deleted", NumberEnum.NO_0.num()));
        for (ProjSurveyFormAddReq req : projSurveyFormAddReqs) {

            List<ProjSurveyForm> list = this.list(
                    new QueryWrapper<ProjSurveyForm>()
                            .eq("is_deleted", 0)
                            .eq("form_name", req.getFormName())
                            .eq(ObjectUtil.isNotEmpty(req.getProjectInfoId()), "project_info_id", req.getProjectInfoId())
                            .eq(ObjectUtil.isNotEmpty(req.getYyProductId()), "yy_product_id", req.getYyProductId())
                            .eq(ObjectUtil.isNotEmpty(req.getYyModuleId()), "yy_module_id", req.getYyProductId())
            );

            if (list != null && !list.isEmpty()) {
                ProjSurveyForm updateProjForm = list.get(0);
                // 尺寸信息不变以及图片信息不变时。不进行修改
                boolean skipCreate = this.isSkipCreate(updateProjForm, req);
                if (skipCreate) {
                    continue;
                }

                // 保存图片
                List<Long> listLong = addSurveyImgData(req);
                String surveyImgs = "";
                if (listLong != null) {
                    for (Long id : listLong) {
                        surveyImgs += id + ",";
                        try {
                            ProjProjectFile file = projProjectFileMapper.selectById(id);
                            file.setProjectInfoId(req.getProjectInfoId());
                            projProjectFileMapper.updateByPrimaryKey(file);
                        } catch (Exception e) {
                            log.error(e.getMessage());
                        }
                    }
                    if (!"".equals(surveyImgs)) {
                        surveyImgs = surveyImgs.substring(0, surveyImgs.length() - 1);
                        updateProjForm.setSurveyImgs(surveyImgs);
                    }
                }
                updateProjForm.setFormName(req.getFormName());
                updateProjForm.setSurveyUserId(surveyUserId);
                updateProjForm.setFormPageUrl(req.getFormPageUrl());
                updateProjForm.setCloudProductCode(req.getCloudProductCode());
                this.updateById(updateProjForm);
            } else {
                ProjSurveyForm projSurveyform = new ProjSurveyForm();
                BeanUtil.copyProperties(req, projSurveyform);

                if (typeList != null && !typeList.isEmpty()) {
                    for (DictFormType item : typeList) {
                        if (item.getTypeCode().equals(req.getTypeCode())) {
                            projSurveyform.setFormPageUrl(item.getFormPageUrl());
                            projSurveyform.setCloudProductCode(item.getCloudProductCode());
                        }
                    }
                }

                // 保存图片
                List<Long> listLong = addSurveyImgData(req);
                String surveyImgs = "";
                if (listLong != null) {
                    for (Long id : listLong) {
                        surveyImgs += id + ",";
                        try {
                            ProjProjectFile file = projProjectFileMapper.selectById(id);
                            file.setProjectInfoId(projSurveyform.getProjectInfoId());
                            projProjectFileMapper.updateByPrimaryKey(file);
                        } catch (Exception e) {
                            log.error(e.getMessage());
                        }
                    }
                    if (!"".equals(surveyImgs)) {
                        surveyImgs = surveyImgs.substring(0, surveyImgs.length() - 1);
                        projSurveyform.setSurveyImgs(surveyImgs);
                    }
                }
                projSurveyform.setSurveyFormId(SnowFlakeUtil.getId());
                this.save(projSurveyform);
            }
        }
        return Result.success();
    }

    private boolean isSkipCreate(ProjSurveyForm dataSource, ProjSurveyFormAddReq req) {
        // 没有调研图片，正常进行生成
        if (org.apache.commons.lang3.StringUtils.isBlank(req.getSurveyImgs())) {
            return false;
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(dataSource.getSurveyImgs())) {
            return false;
        }
        List<String> surveyImagePathList = Arrays.stream(req.getSurveyImgs().split(",")).collect(Collectors.toList());
        List<Long> surveyImageFileId = Arrays.stream(dataSource.getSurveyImgs().split(",")).map(Long::valueOf).collect(Collectors.toList());
        List<ProjProjectFile> projProjectFiles = projProjectFileMapper.selectList(new QueryWrapper<ProjProjectFile>().eq("is_deleted", 0).in("project_file_id", surveyImageFileId));
        List<String> dataSourceSurveyImage = projProjectFiles.stream().map(ProjProjectFile::getFilePath).collect(Collectors.toList());


        // 排序两个列表并逐个比较元素
        Collections.sort(surveyImagePathList);
        Collections.sort(dataSourceSurveyImage);
        if (surveyImagePathList.size() != dataSourceSurveyImage.size()) {
            return false;
        }
        return surveyImagePathList.equals(dataSourceSurveyImage);
    }

    /**
     * 新增OR修改
     *
     * @param projSurveyFormReq
     * @return
     */
    @Override
    public Result updateOrAdd(ProjSurveyFormAddReq projSurveyFormReq) {
        if ((projSurveyFormReq.getCustomerInfoId() == null || projSurveyFormReq.getProjectInfoId() == null) && projSurveyFormReq.getHospitalInfoId() != null) {
            List<ProjProjectInfo> ppiList = projProjectInfoMapper.selectProjectListByHospitalId(projSurveyFormReq.getHospitalInfoId());
            if (ppiList != null && ppiList.size() > 0) {
                List<ProjProjectInfo> hisPpiList = ppiList.stream().filter(ppi -> ppi.getHisFlag() == 1).collect(Collectors.toList());
                if (hisPpiList != null && hisPpiList.size() > 0) {
                    projSurveyFormReq.setProjectInfoId(hisPpiList.get(0).getProjectInfoId());
                    projSurveyFormReq.setCustomerInfoId(hisPpiList.get(0).getCustomInfoId());
                } else {
                    projSurveyFormReq.setProjectInfoId(ppiList.get(0).getProjectInfoId());
                    projSurveyFormReq.setCustomerInfoId(ppiList.get(0).getCustomInfoId());
                }
            }
        }
        Long surveyUserId = null;
        List<ProjSurveyPlan> surveyPlan = projSurveyPlanMapper.selectList(new QueryWrapper<ProjSurveyPlan>().eq("project_info_id", projSurveyFormReq.getProjectInfoId()).eq("yy_product_id", projSurveyFormReq.getYyProductId()));
        if (surveyPlan != null && surveyPlan.size() > 0) {
            surveyUserId = surveyPlan.get(NumberEnum.NO_0.num()).getSurveyUserId();
        }
        String surveyImgs = "";
        if (projSurveyFormReq.getSurveyImgsList() != null && projSurveyFormReq.getSurveyImgsList().size() > 0) {
            for (ProjProjectFile projProjectFile : projSurveyFormReq.getSurveyImgsList()) {
                if (projProjectFile.getProjectFileId() != null) {
                    surveyImgs += projProjectFile.getProjectFileId() + ",";
                    try {
                        ProjProjectFile file = projProjectFileMapper.selectById(projProjectFile.getProjectFileId());
                        file.setProjectInfoId(projSurveyFormReq.getProjectInfoId());
                        projProjectFileMapper.updateByPrimaryKey(file);
                    } catch (Exception e) {
                        log.error(e.getMessage());
                    }
                }
            }
            if (surveyImgs.length() > 0) {
                surveyImgs = surveyImgs.substring(0, surveyImgs.length() - 1);
            }
        }
        projSurveyFormReq.setSurveyImgs(surveyImgs);
        String finishImgs = "";
        if (projSurveyFormReq.getFinishImgsList() != null && projSurveyFormReq.getFinishImgsList().size() > 0) {
            for (ProjProjectFile projProjectFile : projSurveyFormReq.getFinishImgsList()) {
                if (projProjectFile.getProjectFileId() != null) {
                    finishImgs += projProjectFile.getProjectFileId() + ",";
                    try {
                        ProjProjectFile file = projProjectFileMapper.selectById(projProjectFile.getProjectFileId());
                        file.setProjectInfoId(projSurveyFormReq.getProjectInfoId());
                        projProjectFileMapper.updateByPrimaryKey(file);
                    } catch (Exception e) {
                        log.error(e.getMessage());
                    }
                }
            }
            if (finishImgs.length() > 0) {
                finishImgs = finishImgs.substring(0, finishImgs.length() - 1);
            }
        }
        projSurveyFormReq.setFinishImgs(finishImgs);
        // 分类
        if (projSurveyFormReq.getTypeCode() != null && !"".equals(projSurveyFormReq.getTypeCode())) {
            List<DictFormType> formTypeList = dictFormTypeMapper.selectList(new QueryWrapper<DictFormType>().eq("is_deleted", NumberEnum.NO_0.num()).eq("type_code", projSurveyFormReq.getTypeCode()));
            if (formTypeList != null && formTypeList.size() > 0) {
                projSurveyFormReq.setFormPageUrl(formTypeList.get(0).getFormPageUrl());
                projSurveyFormReq.setCloudProductCode(formTypeList.get(0).getCloudProductCode());
            }
        }
        ProjProjectInfo projectInfo = projProjectInfoService.selectByPrimaryKey(projSurveyFormReq.getProjectInfoId());
        if (projectInfo != null) {
            projSurveyFormReq.setCustomerInfoId(projectInfo.getCustomInfoId());
        }
        if (projSurveyFormReq != null && projSurveyFormReq.getSurveyFormId() != null && !"".equals(projSurveyFormReq.getSurveyFormId())) {
            // 修改
            ProjSurveyForm projSurveyform = this.getById(projSurveyFormReq.getSurveyFormId());
            projSurveyform.setFormName(projSurveyFormReq.getFormName());
            projSurveyform.setFormSource(projSurveyFormReq.getFormSource());
            projSurveyform.setOnlineEssential(projSurveyFormReq.getOnlineEssential());
            projSurveyform.setCloudProductCode(projSurveyFormReq.getCloudProductCode());
            projSurveyform.setFormPageUrl(projSurveyFormReq.getFormPageUrl());
            projSurveyform.setCloudProductCode(projSurveyFormReq.getCloudProductCode());
            projSurveyform.setYyProductId(projSurveyFormReq.getYyProductId());
            projSurveyform.setMakeUserId(projSurveyFormReq.getMakeUserId());
            projSurveyform.setSurveyImgs(projSurveyFormReq.getSurveyImgs());
            projSurveyform.setFinishImgs(projSurveyFormReq.getFinishImgs());
            if (projSurveyform.getFinishImgs() != null && !"".equals(projSurveyform.getFinishImgs())) {
                // 查看该项目是否开启审核，开启审核的需要审核完成后进行提交
                // 查询该项目是否需要审核人，提交审核
                if (projectInfo != null && projectInfo.getProjectInfoId() != null) {
                    ConfigCustomBackendDetailLimit limit = baseQueryService.isOpenAuditorFlag(projectInfo.getProjectInfoId(), 12);
                    if (limit != null && limit.getOpenFlag() != null && limit.getOpenFlag() != 0) {
                        // 查询该表单是否审核通过
                        List<ProjBusinessExamineLog> logList = projBusinessExamineLogMapper.selectList(new QueryWrapper<ProjBusinessExamineLog>().eq("business_id", projSurveyform.getSurveyFormId()).eq("business_type", "form").eq("examine_status", 1));
                        if (logList != null && logList.size() > 0) {
                            projSurveyform.setFinishStatus(1);
                            projSurveyform.setCommitFinishTime(new Timestamp(System.currentTimeMillis()));
                            projSurveyform.setUpdateTime(new Timestamp(System.currentTimeMillis()));
                        }
                    } else {
                        projSurveyform.setFinishStatus(1);
                        projSurveyform.setCommitFinishTime(new Timestamp(System.currentTimeMillis()));
                        projSurveyform.setUpdateTime(new Timestamp(System.currentTimeMillis()));
                    }
                }
            }
            projSurveyform.setTypeCode(projSurveyFormReq.getTypeCode());
            projSurveyform.setHospitalInfoId(projSurveyFormReq.getHospitalInfoId());
            this.updateById(projSurveyform);
            SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
            param.setProjectInfoId(projSurveyFormReq.getProjectInfoId());
            param.setHospitalInfoId(projSurveyFormReq.getHospitalInfoId());
            param.setUserId(projSurveyFormReq.getMakeUserId());
            param.setCode(DictProjectPlanItemEnum.PREPARAT_FORM.getPlanItemCode());
            projTodoTaskService.updateProjectTodoTaskStatus(param);
        } else {
            // 新增
            ProjSurveyForm projSurveyform = new ProjSurveyForm();
            BeanUtil.copyProperties(projSurveyFormReq, projSurveyform);
            projSurveyform.setSurveyFormId(SnowFlakeUtil.getId());
            this.save(projSurveyform);
            SysUserVO currentUser = userHelper.getCurrentUser();
            SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
            param.setProjectInfoId(projSurveyFormReq.getProjectInfoId());
            param.setHospitalInfoId(projSurveyFormReq.getHospitalInfoId());
            param.setCode(DictProjectPlanItemEnum.SURVEY_FORM.getPlanItemCode());
            param.setUserId(currentUser.getSysUserId());
            projTodoTaskService.updateProjectTodoTaskStatus(param);
        }
        return Result.success("操作成功");
    }

    /**
     * 修改表单完成状态
     *
     * @param projSurveyFormUpdateReq
     * @return
     */
    @Override
    public Result updateFormFinishStatus(ProjSurveyFormUpdateReq projSurveyFormUpdateReq) {
        ProjSurveyForm projSurveyForm = this.getById(projSurveyFormUpdateReq.getSurveyFormId());
        ProjProjectInfo projectInfo = projProjectInfoService.selectByPrimaryKey(projSurveyForm.getProjectInfoId());
        // 查看该项目是否开启审核，开启审核的需要审核完成后进行提交
        // 查询该项目是否需要审核人，提交审核
        if (projectInfo != null && projectInfo.getProjectInfoId() != null) {
            ConfigCustomBackendDetailLimit limit = baseQueryService.isOpenAuditorFlag(projectInfo.getProjectInfoId(), 12);
            if (limit != null && limit.getOpenFlag() != null && limit.getOpenFlag() != 0) {
                // 查询该表单是否审核通过
                List<ProjBusinessExamineLog> reportList = projBusinessExamineLogMapper.selectListByIds(Collections.singletonList(projSurveyForm.getSurveyFormId()));
                if (!(reportList != null && reportList.size() > 0)) {
                    log.error(StrUtil.format("当前表单尚未通过后端人员审核，请等待审核通过后再提交完成。params: {}\nresult: {}", projSurveyForm.getSurveyFormId(), JSON.toJSONString(reportList)));
                    return Result.fail(422, "当前表单尚未通过后端人员审核，请等待审核通过后再提交完成。");
                }
            }
        }
        if (projSurveyForm != null && projSurveyForm.getFinishImgs() != null && !"".equals(projSurveyForm.getFinishImgs())) {
            projSurveyForm.setFinishStatus(projSurveyFormUpdateReq.getFinishStatus());
            projSurveyForm.setCommitFinishTime(new Timestamp(System.currentTimeMillis()));
            projSurveyForm.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            projSurveyForm.setExamineOpinion("");
            this.updateById(projSurveyForm);
            //  插入审核日志
            ProjFormExamineLog log = new ProjFormExamineLog();
            log.setFormExamineLogId(projSurveyForm.getSurveyFormId());
            log.setExamineStatus(projSurveyForm.getFinishStatus());
            log.setFormExamineLogId(SnowFlakeUtil.getId());
            log.setExamineOpinion("提交审核");
            projFormExamineLogMapper.insert(log);
            List<ProjSurveyForm> listFinish = this.list(new QueryWrapper<ProjSurveyForm>().eq("yy_product_id", projSurveyForm.getYyProductId()).eq("online_essential", projSurveyForm.getOnlineEssential()).eq("yy_module_id", projSurveyForm.getYyModuleId()).notIn("finish_status", new ArrayList<Integer>(Arrays.asList(1, 3))));
            if (!(listFinish != null && listFinish.size() > 0)) {
                // 不存在未完成的，更新产品报表的状态
                // 处理产品明细节点状态
                ProjProductBacklog projProductBacklog = new ProjProductBacklog();
                projProductBacklog.setReportDataStatus(1);
                QueryWrapper<ProjProductBacklog> wrapper = new QueryWrapper<>();
                wrapper.eq("project_info_id", projSurveyForm.getProjectInfoId());
                // 判断传入的产品是否不是模块。当为模块时 赋值模块id
                DictProductVsModules modules = productVsModulesMapper.selectOne(new QueryWrapper<DictProductVsModules>().eq("yy_module_id", projSurveyForm.getYyProductId()));
                if (ObjectUtil.isNotEmpty(modules)) {
                    wrapper.eq("yy_product_module_id", projSurveyForm.getYyProductId());
                } else {
                    wrapper.eq("yy_product_id", projSurveyForm.getYyProductId());
                }
                wrapper.eq("hospital_info_id", projSurveyForm.getHospitalInfoId());
                productBacklogMapper.update(projProductBacklog, wrapper);
            }
            SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
            param.setProjectInfoId(projSurveyForm.getProjectInfoId());
            param.setHospitalInfoId(projSurveyForm.getHospitalInfoId());
            param.setUserId(projSurveyForm.getMakeUserId());
            param.setCode(DictProjectPlanItemEnum.PREPARAT_FORM.getPlanItemCode());
            projTodoTaskService.updateProjectTodoTaskStatus(param);
            return Result.success("操作成功");
        } else {
            return Result.fail(422, "请上传制作完成图片");
        }
    }

    /**
     * 批量分配审核人
     *
     * @param projSurveyFormReq
     * @return
     */
    @Override
    public Result updateReportReviewer(ProjSurveyFormResponsibilitiesReq projSurveyFormReq) {
        if (projSurveyFormReq.getIds() != null && projSurveyFormReq.getIds().size() > 0) {
            for (Long surveyFormId : projSurveyFormReq.getIds()) {
                ProjSurveyForm projSurveyForm = this.getById(surveyFormId);
                if (projSurveyFormReq.getReviewerUserId() != null && !"0".equals(projSurveyFormReq.getReviewerUserId())) {
                    log.info("修改表单审核人：{}", projSurveyFormReq.getReviewerUserId());
                    projSurveyForm.setReviewerUserId(projSurveyFormReq.getReviewerUserId());
                }
                this.updateById(projSurveyForm);
            }
        } else {
            return Result.fail("请选择要更新的报表");
        }
        return Result.success("更新成功");
    }

    /**
     * 批量审核
     *
     * @param projSurveyFormReq
     * @return
     */
    @Override
    public Result updateReportExamine(ProjSurveyReportReviewExamineReq projSurveyFormReq) {
        int count = 0;
        if (projSurveyFormReq.getIds() != null && projSurveyFormReq.getIds().size() > 0) {
            for (Long surveyFormId : projSurveyFormReq.getIds()) {
                ProjSurveyForm projSurveyForm = this.getById(surveyFormId);
                ProjProjectInfo projectInfo = projProjectInfoService.selectByPrimaryKey(projSurveyForm.getProjectInfoId());
                // 查看该项目是否开启审核，开启审核的需要审核完成后进行提交
                // 查询该项目是否需要审核人，提交审核
                ConfigCustomBackendDetailLimit limit = baseQueryService.isOpenAuditorFlag(projectInfo.getProjectInfoId(), 12);
                // 开启表单审核的且提交审核的才需要审核
                List<ProjBusinessExamineLog> reportList = projBusinessExamineLogMapper.selectListByParamer(Collections.singletonList(projSurveyForm.getSurveyFormId()), Arrays.asList(0, 1, 2));
                if ((limit != null && limit.getOpenFlag() != null && limit.getOpenFlag() != 0) && (reportList != null && reportList.size() > 0 && reportList.get(0).getExamineStatus() == 0)) {
                    if (projSurveyFormReq.getExamineStatus() == 1) {
                        // 审批通过
                        projSurveyForm.setFinishStatus(5);
                        this.updateById(projSurveyForm);
                    } else {
                        projSurveyFormReq.setExamineStatus(2);
                        // 审批驳回
                        projSurveyForm.setFinishStatus(6);
                        this.updateById(projSurveyForm);
                    }
                    //  审核  1是通过  2驳回
                    insertIntoLog(surveyFormId, "form", projSurveyFormReq.getExamineStatus(), projSurveyFormReq.getExamineOpinion());
                    // 更新项目代办及明细完成数
                    projTodoTaskService.todoTaskTotalCountSync(projSurveyForm.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_FORM.getPlanItemCode());
                } else {
                    count++;
                }
            }
        } else {
            return Result.fail("请选择要审核的表单");
        }
        if (count > 0) {
            return Result.success(null, "审核成功" + (projSurveyFormReq.getIds().size() - count) + "条数据; 无需审核的数据有" + count + "条数据");
        }
        return Result.success("更新成功");
    }

    /**
     * 提交运维审核
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    @Override
    public Result updateReportExamineStatus(ProjSurveyReportReviewExamineReq projSurveyReportExamineReq) {
        int count = 0;
        if (projSurveyReportExamineReq.getIds() != null && projSurveyReportExamineReq.getIds().size() > 0) {
            for (Long surveyFormId : projSurveyReportExamineReq.getIds()) {
                ProjSurveyForm projSurveyForm = this.getById(surveyFormId);
                ProjProjectInfo projectInfo = projProjectInfoService.selectByPrimaryKey(projSurveyForm.getProjectInfoId());
                // 查看该项目是否开启审核，开启审核的需要审核完成后进行提交
                // 查询该项目是否需要审核人，提交审核
                ConfigCustomBackendDetailLimit limit = baseQueryService.isOpenAuditorFlag(projectInfo.getProjectInfoId(), 12);
                // 开启表单审核的且提交审核的才需要审核
                List<ProjBusinessExamineLog> reportList = projBusinessExamineLogMapper.selectListByParamer(Collections.singletonList(projSurveyForm.getSurveyFormId()), Arrays.asList(0, 1));
                if ((limit == null || (limit != null && limit.getOpenFlag() != null && limit.getOpenFlag() == 0)) && (reportList != null && reportList.size() > 0)) {
                    count++;
                } else {
                    if (projSurveyForm != null) {
                        // 已提后端运维
                        projSurveyForm.setFinishStatus(4);
                        this.updateById(projSurveyForm);
                    }
                    //  提交审核  0是待审核
                    insertIntoLog(surveyFormId, "form", 0, null);
                }
            }
        } else {
            return Result.fail("请选择要更新的报表");
        }
        if (count > 0) {
            return Result.success(null, "提交成功" + (projSurveyReportExamineReq.getIds().size() - count) + "条数据; 无需提交的数据有" + count + "条数据");
        }
        return Result.success("更新成功");
    }

    /**
     * 查询审核日志
     *
     * @param id
     * @return
     */
    @Override
    public Result<List<ProjBusinessExamineLogResp>> selectLogById(Long id) {
        if (id != null) {
            List<ProjBusinessExamineLogResp> list = projBusinessExamineLogMapper.selectLogById(id);
            return Result.success(list);
        }
        return Result.fail("id不能为null");
    }

    /**
     * 插入审核日志
     *
     * @param businessId
     * @param businessType
     * @param examineStatus
     * @param examineOpinion
     */
    private void insertIntoLog(Long businessId, String businessType, Integer examineStatus, String examineOpinion) {
        ProjBusinessExamineLog log = new ProjBusinessExamineLog();
        log.setBusinessId(businessId);
        log.setBusinessType(businessType);
        log.setExamineStatus(examineStatus);
        log.setExamineOpinion(examineOpinion);
        log.setCreateTime(new Date());
        log.setBusinessExamineLogId(SnowFlakeUtil.getId());
        projBusinessExamineLogMapper.insert(log);
    }

    /**
     * 新增文件数据
     *
     * @param req
     * @return
     */
    public List<Long> addSurveyImgData(ProjSurveyFormAddReq req) {
        List<Long> imgIds = new ArrayList<>();
        try {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(req.getSurveyImgs())) {
                String surveyValue = req.getSurveyImgs();
                String[] imgList = surveyValue.split(",");
                if (imgList != null) {
                    for (String img : imgList) {
                        Long id = SnowFlakeUtil.getId();
                        String fileName = "-";
                        String url = String.valueOf(img);
                        String fileUrl = url;
                        ProjProjectFile file = new ProjProjectFile();
                        file.setProjectInfoId(req.getProjectInfoId());
                        file.setProjectFileId(id);
                        file.setProjectStageCode("stage_survey");
                        file.setMilestoneNodeCode("submit_survey_form");
                        file.setFileName(fileName);
                        file.setFilePath(fileUrl);
                        file.setFileDesc("-");
                        file.setCreaterId(req.getCreaterId());
                        file.setUpdaterId(req.getUpdaterId());
                        projProjectFileMapper.insert(file);
                        imgIds.add(id);
                    }
                }
            }
        } catch (Exception e) {
            log.error("保存图片失败", e);
        }
        return imgIds;
    }
}
