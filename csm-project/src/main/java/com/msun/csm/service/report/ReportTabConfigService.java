package com.msun.csm.service.report;

import java.util.List;

import com.msun.csm.common.model.BaseUrlResp;
import com.msun.csm.dao.entity.report.ReportTabConfig;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/11/7
 */

public interface ReportTabConfigService {

    int deleteByPrimaryKey(Long reportTabConfigId);

    int insert(ReportTabConfig record);

    int insertOrUpdate(ReportTabConfig record);

    int insertOrUpdateSelective(ReportTabConfig record);

    int insertSelective(ReportTabConfig record);

    ReportTabConfig selectByPrimaryKey(Long reportTabConfigId);

    int updateByPrimaryKeySelective(ReportTabConfig record);

    int updateByPrimaryKey(ReportTabConfig record);

    int updateBatch(List<ReportTabConfig> list);

    int updateBatchSelective(List<ReportTabConfig> list);

    int batchInsert(List<ReportTabConfig> list);

    /**
     * 根据路径获取配置信息
     *
     * @param path
     * @return
     */
    List<BaseUrlResp> getReportTabConfigByPath(String path);
}
