package com.msun.csm.service.proj.applyorder;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.enums.config.SerialNumType;
import com.msun.csm.common.enums.message.DictMessageTypeEnum;
import com.msun.csm.common.enums.projapplyorder.ArrageApplyStatusEnum;
import com.msun.csm.common.enums.projapplyorder.ProductOpenStatusEnum;
import com.msun.csm.common.enums.projapplyorder.ProjApplyOrderResultTypeEnum;
import com.msun.csm.common.enums.projapplyorder.ProjApplyTypeEnum;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.proj.ProductInfo;
import com.msun.csm.dao.entity.proj.ProjApplyOrder;
import com.msun.csm.dao.entity.proj.ProjApplyOrderProductRecordRelative;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjOrderProduct;
import com.msun.csm.dao.entity.tmp.TmpProjectNewVsOld;
import com.msun.csm.dao.mapper.oldimsp.OldProductRelationMapper;
import com.msun.csm.dao.mapper.proj.ProjApplyOrderMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderProductMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.dao.mapper.tmp.TmpProjectNewVsOldMapper;
import com.msun.csm.model.dto.ApplyOrderNodeRecordParamDTO;
import com.msun.csm.model.dto.productauth.ApplyOpenProductDTO;
import com.msun.csm.model.imsp.HospitalUpdateStatusAndProductDeployDTO;
import com.msun.csm.service.common.CommonService;
import com.msun.csm.service.config.ConfigSerialNumberService;
import com.msun.csm.service.proj.ProjOrderProductService;
import com.msun.csm.service.proj.ProjOrderProductServiceImpl;
import com.msun.csm.service.proj.ProjProjectSettlementCheckMainService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 申请工单 产品服务
 */
@Slf4j
@Service
public class ProjApplyOrderProductServiceImpl implements ProjApplyOrderProductService {

    @Resource
    private ProjApplyOrderService applyOrderService;

    @Resource
    private ProjOrderProductServiceImpl orderProductService;

    @Resource
    private ConfigSerialNumberService configSerialNumberService;

    @Resource
    private ProjApplyOrderProductRecordService applyOrderProductRecordService;

    @Resource
    private ProjOrderProductService projOrderProductService;

    @Resource
    private ProjOrderProductMapper orderProductMapper;

    @Resource
    private OldProductRelationMapper oldProductRelationMapper;

    @Resource
    private ProjApplyOrderHospitalService applyOrderHospitalService;

    @Resource
    private TmpProjectNewVsOldMapper tmpProjectNewVsOldMapper;

    @Resource
    private ProjApplyOrderProductService applyOrderProductService;

    @Resource
    private ProjApplyOrderEnvService applyOrderEnvService;

    @Resource
    private ProjProjectSettlementCheckMainService mainService;

    @Resource
    private ProjApplyOrderHospitalYunweiService applyOrderHospitalYunweiService;

    @Resource
    private ProjApplyOrderMapper applyOrderMapper;

    @Resource
    private CommonService commonService;

    @Resource
    @Lazy
    private SysUserMapper sysUserMapper;

    @Override
    public Result<String> applyOpenProduct(ApplyOpenProductDTO applyOpenProductDTO) {
        // 判断是否首期
        Result<Boolean> result = canOpenProduct(applyOpenProductDTO.getProjectId());
        if (!result.getData()) {
            return Result.fail(result.getMsg());
        }
        // 处理非首期状态的产品开通逻辑
        return orderProductService.applyOpenProductImpl(applyOpenProductDTO, null,
                configSerialNumberService.createNum(SerialNumType.APPLY_ORDER));
    }

    @Override
    public Result<String> applyOpenProductBatch(ApplyOpenProductDTO applyOpenProductDTO) {
        // 判断是否首期
        Result<Boolean> result = canOpenProduct(applyOpenProductDTO.getProjectId());
        if (!result.getData()) {
            return Result.fail(result.getMsg());
        }
        return orderProductService.applyOpenProductBatchImpl(applyOpenProductDTO,
                configSerialNumberService.createNum(SerialNumType.APPLY_ORDER));
    }

    @Override
    public Result<Boolean> canOpenHospital(Long projectInfoId) {
        // 查询是否有部署环境
        boolean hasDeployedEnv = hasDeployedEnv(projectInfoId);
        if (!hasDeployedEnv) {
            log.warn("不能开通医院, 没有查询到部署环境. projectInfoId: {}", projectInfoId);
            return Result.success(false, "未检测到部署环境, 请先部署云资源.");
        }
        // 查询是否有未开通的医院
        List<ProjHospitalInfo> notOpenHospitalInfos =
                applyOrderHospitalYunweiService.findNotOpenHospitalInfo(projectInfoId);
        if (CollUtil.isEmpty(notOpenHospitalInfos)) {
            log.warn("未查询到需要开通的医院. projectInfoId: {}", projectInfoId);
            return Result.success(false, "未检测到待开通的医院.");
        }
        return Result.success(true);
    }

    @Override
    public Result<Boolean> canOpenProduct(Long projectInfoId) {
        return canOpenProduct(projectInfoId, true, true);
    }

    @Override
    public Result<Boolean> canOpenProduct(Long projectInfoId, boolean checkProduct, boolean checkHospital) {
        // 查询是否有部署环境
        boolean hasDeployedEnv = hasDeployedEnv(projectInfoId);
        if (!hasDeployedEnv) {
            log.warn("不能开通产品, 没有查询到部署环境或没有查询到产品信息. projectInfoId: {}", projectInfoId);
            return Result.success(false, "不能开通产品, 没有查询到部署环境.");
        }
        // 检测产品
        if (checkProduct) {
            List<ProductInfo> productInfos = orderProductService.getUnOpenProducts(projectInfoId);
            if (CollUtil.isEmpty(productInfos)) {
                log.warn("未查询到要开通的产品. projectInfoId: {}", projectInfoId);
                return Result.success(false, "未查询到要开通的产品.");
            }
        }
        // 检测医院
        if (checkHospital) {
            List<ProjHospitalInfo> openedHospitalInfos =
                    applyOrderHospitalYunweiService.findOpenedHospitalInfo(projectInfoId);
            if (CollUtil.isEmpty(openedHospitalInfos)) {
                log.warn("未查询到已开通的医院. projectInfoId: {}", projectInfoId);
                return Result.success(false, "未查询到已开通的医院.");
            }
        }
        return Result.success(true);
    }

    /**
     * 判断是否可开通新环境
     *
     * @param projectInfoId 项目id
     * @return Result<Boolean>
     */
    @Override
    public Result<Boolean> canOpenEnv(Long projectInfoId) {
        if (!mainService.isFirstProject(projectInfoId) || hasDeployedEnv(projectInfoId)) {
            log.warn("非首期项目无法开通新环境. projectInfoId: {}", projectInfoId);
            return Result.success(false, "非首期项目无法开通新环境, 或已存在开通的医院,不能重复开通环境");
        }
        // 查询是否有处理中或是已开通的产品等
        List<ProjOrderProduct> orderProducts = orderProductMapper.selectList(new QueryWrapper<ProjOrderProduct>().eq(
                "project_info_id", projectInfoId));
        if (CollUtil.isEmpty(orderProducts)) {
            List<ProjOrderProduct> openOrderProducts =
                    ProjApplyOrderHospitalYunweiService.orderProductOpen(orderProducts);
            if (CollUtil.isNotEmpty(orderProducts) || (openOrderProducts.size() != orderProducts.size())) {
                log.warn("没有查询到产品信息, 或已存在正在开通或已经开通的产品. projectInfoId: {}", projectInfoId);
                return Result.success(false, "未查询到产品信息.");
            }
        }
        // 判断所有需要开通的医院是否
        List<ProjHospitalInfo> notOpenHospitalInfos =
                applyOrderHospitalYunweiService.findNotOpenHospitalInfo(projectInfoId);
        if (CollUtil.isEmpty(notOpenHospitalInfos)) {
            log.warn("医院开通状态异常. projectInfoId: {}", projectInfoId);
            return Result.success(false, "医院开通状态异常.");
        }
        // 判断是否进行了环境申请
        List<ProjApplyOrder> applyOrders = applyOrderMapper.selectList(new QueryWrapper<ProjApplyOrder>().eq(
                        "project_info_id", projectInfoId)
                .eq("apply_type", ProjApplyTypeEnum.FIRST_EVN_APPLY.getCode()).orderByDesc("create_time"));
        if (CollUtil.isNotEmpty(applyOrders)) {
            if (applyOrders.get(0).getResultType() != ProjApplyOrderResultTypeEnum.REJECTED.getCode()) {
                return Result.success(false, "新环境已申请, 不能重复申请.");
            }
        }
        return Result.success(true);
    }

    /**
     * 是否有开通的医院
     *
     * @param projectInfoId 项目id
     * @return boolean
     */
    @Override
    public boolean hasDeployedEnv(Long projectInfoId) {
        List<ProjHospitalInfo> hospitalInfos = applyOrderHospitalYunweiService.findOpenedHospitalInfo(projectInfoId);
        return CollUtil.isNotEmpty(hospitalInfos);
    }

    /**
     * 产品审核
     *
     * @param dto 请求参数
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void auditHandler(HospitalUpdateStatusAndProductDeployDTO dto) {
        SysUser sysUser = extractedSysUser(dto);
        ProjApplyOrder applyOrder = applyOrderService.getProjApplyOrder(dto.getDeliverPlatformApplyId());
        ApplyOrderNodeRecordParamDTO applyOrderNodeRecordParamDTO = ApplyOrderNodeRecordParamDTO.builder()
                .refusedReason(StrUtil.EMPTY)
                .rejectReason(StrUtil.EMPTY)
                .telephone(sysUser.getPhone())
                .operator(dto.getOperateName())
                .operateContent(dto.getOperateName() + Objects.requireNonNull(ProjApplyOrderResultTypeEnum.getEnumDes(dto.getOperateType())).getDesc())
                .build();
        applyOrderService.addApplyOrderNodeRecord(applyOrder, applyOrderNodeRecordParamDTO);
    }

    /**
     * 获取操作人信息
     * @param dto
     * @return
     */
    private SysUser extractedSysUser(HospitalUpdateStatusAndProductDeployDTO dto) {
        List<SysUser> voList = sysUserMapper.selectList(new QueryWrapper<SysUser>().eq("is_deleted", 0).eq("user_name", dto.getOperateName()));
        SysUser sysUser = new SysUser();
        if (voList != null && voList.size() > 0) {
            sysUser = voList.get(0);
        }
        return sysUser;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void rejectHandler(ProjApplyOrder applyOrder, HospitalUpdateStatusAndProductDeployDTO dto) {
        SysUser sysUser = extractedSysUser(dto);
        applyOrderService.updateApplyOrder(applyOrder, dto.getOperateType());
        // - 查询申请记录添加日志
        applyOrder = applyOrderService.getProjApplyOrder(dto.getDeliverPlatformApplyId());
        ApplyOrderNodeRecordParamDTO applyOrderNodeRecordParamDTO = ApplyOrderNodeRecordParamDTO.builder()
                .refusedReason(StrUtil.EMPTY)
                .rejectReason(dto.getRemark())
                .telephone(sysUser.getPhone())
                .operator(dto.getOperateName())
                .operateContent(dto.getOperateName() + Objects.requireNonNull(ProjApplyOrderResultTypeEnum.getEnumDes(dto.getOperateType())).getDesc())
                .build();
        applyOrderService.addApplyOrderNodeRecord(applyOrder, applyOrderNodeRecordParamDTO);
        // 更新医院产品-未开通状态
        List<Long> productIds = this.getProductIdListByApplyOrderId(applyOrder.getId());
        this.updateProductOpenStatus(ProductOpenStatusEnum.NOT_OPEN, productIds, applyOrder.getProjectInfoId());
        String rejectedContent = "产品申请被驳回, 请知晓, 驳回原因: " + dto.getRemark();
        // 给pmo发送消息
        commonService.sendToPmo(applyOrder.getProjectInfoId(), rejectedContent);
        // 给提交人发送驳回消息
        commonService.sendToSinglePerson(applyOrder.getProjectInfoId(), rejectedContent,
                DictMessageTypeEnum.DEPLOY_REJECT_KOWN.getId(), applyOrder.getCreaterId());
    }

    /**
     * 产品部署
     *
     * @param dto 请求参数
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void deployedHandler(ProjApplyOrder applyOrder, HospitalUpdateStatusAndProductDeployDTO dto) {
        SysUser sysUser = extractedSysUser(dto);
        applyOrderService.updateApplyOrder(applyOrder, dto.getOperateType());
        ApplyOrderNodeRecordParamDTO applyOrderNodeRecordParamDTO = ApplyOrderNodeRecordParamDTO.builder()
                .refusedReason(StrUtil.EMPTY)
                .rejectReason(StrUtil.EMPTY)
                .telephone(sysUser.getPhone())
                .operator(dto.getOperateName())
                .operateContent(dto.getOperateName() + Objects.requireNonNull(ProjApplyOrderResultTypeEnum.getEnumDes(dto.getOperateType())).getDesc())
                .build();
        applyOrderService.addApplyOrderNodeRecord(applyOrder, applyOrderNodeRecordParamDTO);
    }

    /**
     * 产品交付
     *
     * @param dto 请求参数
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void deliveredHandler(ProjApplyOrder applyOrder, HospitalUpdateStatusAndProductDeployDTO dto) {
        SysUser sysUser = extractedSysUser(dto);
        List<ProjHospitalInfo> hospitals =
                applyOrderHospitalService.findOpenedHospitalInfos(applyOrder.getProjectInfoId());
        // 查询已开通的所有医院
        ApplyOrderNodeRecordParamDTO applyOrderNodeRecordParamDTO = ApplyOrderNodeRecordParamDTO.builder()
                .refusedReason(StrUtil.EMPTY)
                .rejectReason(StrUtil.EMPTY)
                .telephone(sysUser.getPhone())
                .operator(dto.getOperateName())
                .operateContent(dto.getOperateName() + Objects.requireNonNull(ProjApplyOrderResultTypeEnum.getEnumDes(dto.getOperateType())).getDesc())
                .build();
        applyOrderService.addApplyOrderNodeRecord(applyOrder, applyOrderNodeRecordParamDTO);
        List<Long> productIds = getProductIdListByApplyOrderId(applyOrder.getId());
        projOrderProductService.applyEmpowers(applyOrder.getProjectInfoId(), hospitals, productIds);
    }


    /**
     * 处理产品业务
     */

    @Override
    public Result<String> productHandler(ProjApplyOrder applyOrder, HospitalUpdateStatusAndProductDeployDTO dto) {
        // 更新状态
        applyOrderService.updateApplyOrder(applyOrder, dto.getOperateType());
        if (dto.getOperateType() == ProjApplyOrderResultTypeEnum.AUDITED.getCode()) {
            applyOrderProductService.auditHandler(dto);
            return Result.success();
        }
        if (dto.getOperateType() == ProjApplyOrderResultTypeEnum.REJECTED.getCode()) {
            applyOrderProductService.rejectHandler(applyOrder, dto);
            return Result.success();
        }
        // 已部署完成处理
        if (dto.getOperateType() == ProjApplyOrderResultTypeEnum.ENV_DEPLOYED.getCode()) {
            applyOrderProductService.deployedHandler(applyOrder, dto);
            return Result.success();
        }
        // 已交付
        if (dto.getOperateType() == ProjApplyOrderResultTypeEnum.DELIVERED.getCode()) {
            try {
                applyOrderProductService.deliveredHandler(applyOrder, dto);
                // 发送给提交人交付成功通知
                commonService.sendToSinglePerson(applyOrder.getProjectInfoId(), "产品已完成交付, 请知晓!",
                        DictMessageTypeEnum.DEPLOY_DEPLOYED_KOWN.getId(),
                        applyOrder.getCreaterId());
                return Result.success();
            } catch (Throwable e) {
                log.error("交付产品失败. 部署申请工单号: {}, 异常: {}, e=", applyOrder.getApplyNum(), e.getMessage(), e);
                // 发送消息给系统管理员交付失败的消息
                applyOrderEnvService.sendToSystemManager(applyOrder);
            }

        }
        return Result.fail();
    }

    @Override
    public List<Long> getProductIdListByApplyOrderId(Long applyOrderId) {
        List<ProjApplyOrderProductRecordRelative> applyOrderProductRecords
                = applyOrderProductRecordService.findByApplyOrderId(applyOrderId);
        return applyOrderProductRecords.stream().map(ProjApplyOrderProductRecordRelative::getYyOrderProductId)
                .collect(Collectors.toList());
    }

    @Override
    public void updateProductOpenStatus(ProductOpenStatusEnum productOpenStatusEnum, List<Long> yyProductIds,
                                        Long projectInfoId) {
        int count = updateOrderProductOpenStatus(productOpenStatusEnum, yyProductIds, projectInfoId);
        log.info("更新工单产品开通状态. count: {}", count);
        orderProductService.updateEspectialOrderProductStatus(projectInfoId, yyProductIds,
                ArrageApplyStatusEnum.OPENING.getCode(), productOpenStatusEnum.getCode());
        List<TmpProjectNewVsOld> tmpProjectNewVsOlds =
                tmpProjectNewVsOldMapper.selectByNewProjectIds(CollUtil.newArrayList(projectInfoId));
        if (CollUtil.isNotEmpty(tmpProjectNewVsOlds)) {
            TmpProjectNewVsOld tmpProjectNewVsOld = tmpProjectNewVsOlds.get(0);
            count = oldProductRelationMapper.updateBatchByYyProductidAndProjectInfoId(productOpenStatusEnum.getOldCode(), tmpProjectNewVsOld.getOldProjectInfoId(), yyProductIds);
            log.info("更新老产品开通状态. count: {}", count);
        }
    }

    @Override
    public int updateOrderProductOpenStatus(ProductOpenStatusEnum productOpenStatusEnum, List<Long> productIds,
                                            Long projectInfoId) {
        ProjOrderProduct orderProduct = new ProjOrderProduct();
        orderProduct.setProductOpenStatus(productOpenStatusEnum.getCode());
        return orderProductMapper.update(orderProduct, new QueryWrapper<ProjOrderProduct>().eq("project_info_id",
                projectInfoId).in("yy_order_product_id", productIds));
    }
}
