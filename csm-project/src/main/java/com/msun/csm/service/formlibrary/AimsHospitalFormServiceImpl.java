package com.msun.csm.service.formlibrary;


import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import com.msun.core.commons.api.ResponseResult;
import com.msun.core.component.implementation.api.aims.AimsOasProcessConfigApi;
import com.msun.core.component.implementation.api.aims.dto.CustomFormDTO;
import com.msun.core.component.implementation.api.aims.dto.FindByJfDTO;
import com.msun.core.component.implementation.api.aims.dto.ImportByJfDTO;
import com.msun.core.component.implementation.api.aims.dto.ImportByJfItemDTO;
import com.msun.core.component.implementation.api.aims.vo.FindByJfVO;
import com.msun.core.component.implementation.filter.ImplHospitalDomainHolder;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.formlibrary.ProjSelectApplicationForm;
import com.msun.csm.dao.entity.formlibrary.ProjSelectApplicationFormDetail;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.mapper.formlibrary.ProjSelectApplicationFormDetailMapper;
import com.msun.csm.dao.mapper.formlibrary.ProjSelectApplicationFormMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.model.req.formlibrary.AimsHospitalFormImportParamerReq;
import com.msun.csm.model.req.formlibrary.AimsHospitalFormLookParamerReq;
import com.msun.csm.model.req.formlibrary.AimsHospitalFormParamerReq;
import com.msun.csm.model.req.formlibrary.AimsHospitalFormSelectParamerReq;
import com.msun.csm.model.req.formlibrary.ProjSelectApplicationFormPageReq;
import com.msun.csm.model.req.formlibrary.ProjSelectApplicationFormUpdateReq;
import com.msun.csm.model.resp.formlibrary.AimsHospitalFormImportResultResp;
import com.msun.csm.model.resp.formlibrary.AimsHospitalFormLookResp;
import com.msun.csm.model.resp.formlibrary.AimsHospitalFormResp;
import com.msun.csm.model.resp.formlibrary.ProjSelectApplicationFormResp;
import com.msun.csm.util.DomainMapUtil;
import com.msun.csm.util.HttpsClientRequestFactory;
import com.msun.csm.util.PageHelperUtil;
import com.msun.csm.util.SnowFlakeUtil;

/**
 * <AUTHOR>
 * @description 针对表【proj_survey_form(表单主表)】的数据库操作Service实现
 * @createDate 2024-09-14 15:15:49
 */
@Service
@Slf4j
public class AimsHospitalFormServiceImpl implements AimsHospitalFormService {


    private final RestTemplate restTemplate = new RestTemplate();
    @Resource
    private AimsOasProcessConfigApi aimsOasProcessConfigApi;
    @Lazy
    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;
    @Resource
    private ImplHospitalDomainHolder domainHolder;
    @Resource
    private ProjSelectApplicationFormMapper projSelectApplicationFormMapper;
    @Resource
    private ProjSelectApplicationFormDetailMapper projSelectApplicationFormDetailMapper;

    @Value("${emredit.url}")
    private String reportUrl;

    /**
     * @param aimsHospitalFormParamerReq
     * @return
     */
    @Override
    public Result<PageInfo<AimsHospitalFormResp>> selectAimsHospitalData(AimsHospitalFormParamerReq aimsHospitalFormParamerReq) {
        if (aimsHospitalFormParamerReq.getHospitalInfoId() == null) {
            return Result.fail("医院ID必须传入");
        }
        List<String> codeList = new ArrayList<>();
        List<ProjSelectApplicationForm> listdb = projSelectApplicationFormMapper.selectList(new QueryWrapper<ProjSelectApplicationForm>().eq("project_info_id", aimsHospitalFormParamerReq.getProjectInfoId()));
        if (!CollectionUtils.isEmpty(listdb)) {
            codeList = listdb.stream().map(ProjSelectApplicationForm::getFormCategoryCode).collect(Collectors.toList());
        }
        ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectById(aimsHospitalFormParamerReq.getHospitalInfoId());
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        FindByJfDTO dto = new FindByJfDTO();
        dto.setHospitalId(hospitalInfo.getCloudHospitalId());
        dto.setHisOrgId(hospitalInfo.getOrgId());
        dto.setHospitalIdList(Collections.singletonList(hospitalInfo.getCloudHospitalId()));
        ResponseResult<List<FindByJfVO>> result = aimsOasProcessConfigApi.findByJf(dto);
        List<AimsHospitalFormResp> resultList = new ArrayList<>();
        List<BaseCodeNameResp> listType = queryAimsLibFormType();
        if (result.isSuccess()) {
            List<FindByJfVO> list = result.getData();
            // 数据转换
            if (list != null && list.size() > 0) {
                for (FindByJfVO resp : list) {
                    AimsHospitalFormResp aimsHospitalFormResp = new AimsHospitalFormResp();
                    aimsHospitalFormResp = getAimsHospitalFormResp(resp, aimsHospitalFormResp, listType);
                    aimsHospitalFormResp.setHospitalName(hospitalInfo.getHospitalName());
                    if (codeList.contains(aimsHospitalFormResp.getFormCategoryCode())) {
                        aimsHospitalFormResp.setIsProjectSelect(1);
                    } else {
                        aimsHospitalFormResp.setIsProjectSelect(0);
                    }
                    resultList.add(aimsHospitalFormResp);
                }
            }
        }
        PageInfo<AimsHospitalFormResp> pageInfo = new PageInfo<>(resultList);
        return Result.success(pageInfo);
    }

    /**
     * 导入数据
     *
     * @param resp
     * @return
     */
    @Override
    public Result<AimsHospitalFormImportResultResp> importAimsFormData(AimsHospitalFormImportParamerReq resp) {
        AimsHospitalFormImportResultResp aimsHospitalFormImportResultResp = new AimsHospitalFormImportResultResp();
        List<Long> selectApplicationFormIds = resp.getSelectApplicationFormIds();
        if (CollectionUtils.isEmpty(selectApplicationFormIds)) {
            return Result.fail("请选择需要导入的数据");
        }
        ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectById(resp.getHospitalInfoId());
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(hospitalInfo);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        ImportByJfDTO importByJfDTO = new ImportByJfDTO();
        importByJfDTO.setHospitalId(hospitalInfo.getCloudHospitalId());
        importByJfDTO.setHisOrgId(hospitalInfo.getOrgId());
        importByJfDTO.setCheckImportFlag(String.valueOf(resp.getCheckImportFlag()));
        List<ProjSelectApplicationForm> selectList = projSelectApplicationFormMapper.selectList(new QueryWrapper<ProjSelectApplicationForm>().lambda().in(ProjSelectApplicationForm::getSelectApplicationFormId, selectApplicationFormIds));
        List<ImportByJfItemDTO> customFormDTOListDto = new ArrayList<>();
        for (ProjSelectApplicationForm selectApplicationForm : selectList) {
            ImportByJfItemDTO customFormDTO = getCustomFormDTO(selectApplicationForm, hospitalInfo);
            customFormDTOListDto.add(customFormDTO);
        }
        importByJfDTO.setCustomFormDTOList(customFormDTOListDto);
        if (resp.getCheckImportFlag() != null && resp.getCheckImportFlag() == 1) {
            ResponseResult<?> result = aimsOasProcessConfigApi.importByJf(importByJfDTO);
            if (result.isSuccess()) {
                try {
                    List customFormDTOList = (List) result.getData();
                    if (customFormDTOList != null && customFormDTOList.size() > 0) {
                        String jsonString = "";
                        for (Object customFormDTO : customFormDTOList) {
                            // 创建 ObjectMapper 实例
                            ObjectMapper objectMapper = new ObjectMapper();
                            // 忽略未知字段
                            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                            CustomFormDTO customFormDTOs = objectMapper.readValue(customFormDTO.toString(), CustomFormDTO.class);
                            jsonString += "【" + customFormDTOs.getCustomFormName() + "】";
                        }
                        aimsHospitalFormImportResultResp.setDescription("检测到" + jsonString + "表单已导入，是否确认覆盖现有数据并继续导入？请谨慎操");
                        aimsHospitalFormImportResultResp.setResult(0);
                        return Result.success(aimsHospitalFormImportResultResp);
                    } else {
                        importByJfDTO.setCheckImportFlag("0");
                        return getAimsHospitalFormImportResultRespResult(importByJfDTO, aimsHospitalFormImportResultResp, selectList, hospitalInfo);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    aimsHospitalFormImportResultResp.setDescription("导入失败");
                    aimsHospitalFormImportResultResp.setResult(2);
                    return Result.success(aimsHospitalFormImportResultResp);
                }
            }
        } else {
            return getAimsHospitalFormImportResultRespResult(importByJfDTO, aimsHospitalFormImportResultResp, selectList, hospitalInfo);
        }
        return Result.fail("导入失败");
    }

    /**
     * 导入云健康
     *
     * @param importByJfDTO
     * @param aimsHospitalFormImportResultResp
     * @param selectList
     * @param hospitalInfo
     * @return
     */
    private Result<AimsHospitalFormImportResultResp> getAimsHospitalFormImportResultRespResult(ImportByJfDTO importByJfDTO, AimsHospitalFormImportResultResp aimsHospitalFormImportResultResp, List<ProjSelectApplicationForm> selectList, ProjHospitalInfo hospitalInfo) {
        try {
            ResponseResult<?> newImportResult = aimsOasProcessConfigApi.importByJf(importByJfDTO);
            if (newImportResult.isSuccess()) {
                aimsHospitalFormImportResultResp.setDescription("导入成功");
                aimsHospitalFormImportResultResp.setResult(1);
                // 记录日志：
                insertLogData(selectList, 1, hospitalInfo);
                return Result.success(aimsHospitalFormImportResultResp);
            } else {
                // 记录日志：
                insertLogData(selectList, 2, hospitalInfo);
                aimsHospitalFormImportResultResp.setDescription("导入失败");
                aimsHospitalFormImportResultResp.setResult(2);
                return Result.success(aimsHospitalFormImportResultResp);
            }
        } catch (Exception e) {
            e.printStackTrace();
            aimsHospitalFormImportResultResp.setDescription("导入失败，可能原因是表单类型不存在。请检查目标医院是否设置了该表单类型，若不存在，请先新建相应的表单类型，然后再次尝试导入。");
            aimsHospitalFormImportResultResp.setResult(2);
            return Result.success(aimsHospitalFormImportResultResp);
        }

    }

    /**
     * 记录日志
     *
     * @param selectList
     * @param operStatus
     * @param hospitalInfo
     */
    private void insertLogData(List<ProjSelectApplicationForm> selectList, Integer operStatus, ProjHospitalInfo hospitalInfo) {
        if (selectList != null && selectList.size() > 0) {
            for (ProjSelectApplicationForm form : selectList) {
                ProjSelectApplicationFormDetail projSelectApplicationFormLog = new ProjSelectApplicationFormDetail();
                projSelectApplicationFormLog.setSelectApplicationFormId(form.getSelectApplicationFormId());
                projSelectApplicationFormLog.setSelectApplicationFormDetailId(SnowFlakeUtil.getId());
                projSelectApplicationFormLog.setOperType(2);
                projSelectApplicationFormLog.setOperStatus(operStatus);
                projSelectApplicationFormLog.setHospitalName(hospitalInfo.getHospitalName());
                projSelectApplicationFormLog.setHospitalInfoId(hospitalInfo.getHospitalInfoId());
                projSelectApplicationFormLog.setOperResult("导入云健康");
                projSelectApplicationFormDetailMapper.insert(projSelectApplicationFormLog);
                form.setSendStatus(operStatus);
                projSelectApplicationFormMapper.updateById(form);
            }
        }

    }

    /**
     * 获取自定义表单数据
     *
     * @param selectApplicationForm
     * @param hospitalInfo
     * @return
     */
    private ImportByJfItemDTO getCustomFormDTO(ProjSelectApplicationForm selectApplicationForm, ProjHospitalInfo hospitalInfo) {
        // formType传0，parent_id不传，source传1 template_level不传， deptId不传  custom_form_code也不传
        ImportByJfItemDTO customFormDTO = new ImportByJfItemDTO();
//        customFormDTO.setCustomFormId(selectApplicationForm.getFormPatternId());
        customFormDTO.setCustomFormName(selectApplicationForm.getFormPatternName());
        customFormDTO.setDescription(selectApplicationForm.getDescription());
        customFormDTO.setSort(selectApplicationForm.getSort());
        customFormDTO.setFormContent(selectApplicationForm.getFormContentPc());
        customFormDTO.setFormConfiguration(selectApplicationForm.getFormConfigurationPc());
        customFormDTO.setProcessConfigCode(selectApplicationForm.getFormCategoryCode());
        customFormDTO.setFormContentPad(selectApplicationForm.getFormContentPad());
        customFormDTO.setFormLayoutPad(selectApplicationForm.getFormConfigurationPad());
        customFormDTO.setVersion(0L);
        customFormDTO.setHisCreaterId(-1L);
        customFormDTO.setHisCreaterName("交付导入");
        customFormDTO.setHisUpdaterId(-1L);
        customFormDTO.setFormContentWeb(selectApplicationForm.getFormContentWeb());
        customFormDTO.setFormLayoutWeb(selectApplicationForm.getFormConfigurationWeb());
        return customFormDTO;
    }

    /**
     * @param aimsHospitalFormParamerReq
     * @return
     */
    @Override
    public Result<PageInfo<ProjSelectApplicationFormResp>> selectAimsSelectFormHospitalData(ProjSelectApplicationFormPageReq aimsHospitalFormParamerReq) {
        return PageHelperUtil.queryPage(aimsHospitalFormParamerReq.getPageNum(), aimsHospitalFormParamerReq.getPageSize(), page -> {
            List<ProjSelectApplicationFormResp> projHospitalInfos = projSelectApplicationFormMapper.selectDataLogByPage(aimsHospitalFormParamerReq);
            PageInfo<ProjSelectApplicationFormResp> pageInfo = new PageInfo<>(projHospitalInfos);
            if (pageInfo.getList() != null && pageInfo.getList().size() > 0) {
                for (ProjSelectApplicationFormResp projSelectApplicationFormResp : pageInfo.getList()) {
                    List<ProjSelectApplicationFormDetail> logList = projSelectApplicationFormDetailMapper.selectDataListByParam(projSelectApplicationFormResp);
                    projSelectApplicationFormResp.setLogList(logList);
                }
            }
            return Result.success(pageInfo);
        });
    }

    /**
     * 移除数据
     *
     * @param aimsHospitalFormParamerReq
     * @return
     */
    @Override
    public Result removeAimsSelectFormHospitalData(ProjSelectApplicationFormUpdateReq aimsHospitalFormParamerReq) {
        if (aimsHospitalFormParamerReq.getSelectApplicationFormIds() != null && aimsHospitalFormParamerReq.getSelectApplicationFormIds().size() > 0) {
            List<Long> list = aimsHospitalFormParamerReq.getSelectApplicationFormIds();
            for (Long aLong : list) {
                projSelectApplicationFormMapper.deleteById(aLong);
            }
        } else {
            return Result.fail("请选择要移除的数据");
        }
        return Result.success("移除成功");
    }

    /**
     * @param aimsHospitalFormParamerReq
     * @return
     */
    @Override
    public Result seleltAimsFormListToCsmData(AimsHospitalFormSelectParamerReq aimsHospitalFormParamerReq) {
        List<AimsHospitalFormResp> list = aimsHospitalFormParamerReq.getImportList();
        if (CollectionUtils.isEmpty(list) || list.size() == 0) {
            return Result.fail("请选择要选用的数据");
        }
        if (aimsHospitalFormParamerReq.getProjectInfoId() == null) {
            return Result.fail("请选择项目");
        }
        List<Long> formPatternIds = list.stream().map(AimsHospitalFormResp::getFormPatternId).collect(Collectors.toList());
        List<ProjSelectApplicationForm> selectList = projSelectApplicationFormMapper.selectList(new QueryWrapper<ProjSelectApplicationForm>().lambda().in(ProjSelectApplicationForm::getFormPatternId, formPatternIds).eq(ProjSelectApplicationForm::getProjectInfoId, aimsHospitalFormParamerReq.getProjectInfoId()));
        if (CollectionUtils.isEmpty(selectList)) {
            for (AimsHospitalFormResp resp : list) {
                ProjSelectApplicationForm selectApplicationForm = new ProjSelectApplicationForm();
                BeanUtils.copyProperties(resp, selectApplicationForm);
                selectApplicationForm.setFormCategoryCode(resp.getFormCategoryCode());
                if (ObjectUtil.isEmpty(selectApplicationForm.getHisOrgId())) {
                    selectApplicationForm.setHisOrgId(-1L);
                }
                selectApplicationForm.setProjectInfoId(aimsHospitalFormParamerReq.getProjectInfoId());
                selectApplicationForm.setYyProductId(4050L);
                selectApplicationForm.setSelectApplicationFormId(SnowFlakeUtil.getId());
                projSelectApplicationFormMapper.insert(selectApplicationForm);
            }
        } else {
            List<Long> dbFormPatternIds = selectList.stream().map(ProjSelectApplicationForm::getFormPatternId).collect(Collectors.toList());
            for (AimsHospitalFormResp resp : list) {
                if (dbFormPatternIds.contains(resp.getFormPatternId())) {
                    continue;
                }
                ProjSelectApplicationForm selectApplicationForm = new ProjSelectApplicationForm();
                BeanUtils.copyProperties(resp, selectApplicationForm);
                selectApplicationForm.setFormCategoryCode(resp.getFormCategoryCode());
                selectApplicationForm.setProjectInfoId(aimsHospitalFormParamerReq.getProjectInfoId());
                selectApplicationForm.setYyProductId(4050L);
                selectApplicationForm.setSelectApplicationFormId(SnowFlakeUtil.getId());
                projSelectApplicationFormMapper.insert(selectApplicationForm);
            }
        }
        return Result.success("选用成功");
    }

    /**
     * 查询手麻资源库数据
     *
     * @param aimsHospitalFormParamerReq
     * @return
     */
    @Override
    public Result<PageInfo<AimsHospitalFormResp>> selectAimsHospitalFormLibData(AimsHospitalFormParamerReq aimsHospitalFormParamerReq) {
        return PageHelperUtil.queryPage(aimsHospitalFormParamerReq.getPageNum(), aimsHospitalFormParamerReq.getPageSize(), page -> {
            List<AimsHospitalFormResp> projHospitalInfos = projSelectApplicationFormMapper.selectAimsHospitalFormLibData(aimsHospitalFormParamerReq);
            PageInfo<AimsHospitalFormResp> pageInfo = new PageInfo<>(projHospitalInfos);
            List<String> codeList = new ArrayList<>();
            List<ProjSelectApplicationForm> listdb = projSelectApplicationFormMapper.selectList(new QueryWrapper<ProjSelectApplicationForm>().eq("project_info_id", aimsHospitalFormParamerReq.getProjectInfoId()));
            if (!CollectionUtils.isEmpty(listdb)) {
                codeList = listdb.stream().map(ProjSelectApplicationForm::getFormCategoryCode).collect(Collectors.toList());
            }
            if (pageInfo.getList() != null && pageInfo.getList().size() > 0) {
                for (AimsHospitalFormResp resp : pageInfo.getList()) {
                    if (codeList.contains(resp.getFormCategoryCode())) {
                        resp.setIsProjectSelect(1);
                    } else {
                        resp.setIsProjectSelect(0);
                    }
                }
            }
            return Result.success(pageInfo);
        });
    }

    /**
     * @return
     */
    @Override
    public List<BaseCodeNameResp> queryAimsLibFormType() {
        List<BaseCodeNameResp> baseCodeNameResps = projSelectApplicationFormMapper.queryAimsLibFormType();
        return baseCodeNameResps;
    }

    /**
     * @param aimsHospitalFormParamerReq
     * @return
     */
    @Override
    public Result<AimsHospitalFormLookResp> lookAimsForm(AimsHospitalFormLookParamerReq aimsHospitalFormParamerReq) {
        log.error("预览样式");
        AimsHospitalFormLookResp resp = new AimsHospitalFormLookResp();
        String reportUrls = reportUrl;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        // 请求
        Map<String, Object> param = new HashMap<>();
        param.put("rawData", aimsHospitalFormParamerReq.getRawData());
        param.put("config", aimsHospitalFormParamerReq.getConfig());
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(param, headers);
        // 使用RestTemplate请求
        RestTemplate restTemplateHttps;
        if (reportUrls.contains("https:")) {
            restTemplateHttps = new RestTemplate(new HttpsClientRequestFactory());
        } else {
            restTemplateHttps = new RestTemplate();
        }
        SimpleClientHttpRequestFactory rf = ((SimpleClientHttpRequestFactory) restTemplateHttps.getRequestFactory());
        rf.setConnectTimeout(5000);
        rf.setReadTimeout(5000);
        try {
            log.error("请求成功前convertBase64");
            ResponseEntity<JSONObject> responseBody = restTemplateHttps.postForEntity(reportUrls, request, JSONObject.class);
            log.error("请求成功convertBase64", responseBody);
            if (responseBody.getStatusCodeValue() == 200) {
                ObjectMapper objectMapper = new ObjectMapper();
                // 忽略未知字段
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                AimsHospitalFormLookResp customFormDTOs = objectMapper.readValue(responseBody.getBody().toString(), AimsHospitalFormLookResp.class);
                resp = customFormDTOs;

            } else {
                log.error("请求失败convertBase64", responseBody);
                resp.setMsg("失败");
                resp.setBase64("");
            }
        } catch (Exception e) {
            log.error("失败catch");
            e.printStackTrace();
            resp.setMsg("失败");
            resp.setBase64("");
        }
        return Result.success(resp);
    }

    /**
     * 数据转化
     *
     * @param resp
     * @param aimsHospitalFormResp
     * @param listType
     * @return
     */
    private AimsHospitalFormResp getAimsHospitalFormResp(FindByJfVO resp, AimsHospitalFormResp aimsHospitalFormResp, List<BaseCodeNameResp> listType) {
        BeanUtils.copyProperties(resp, aimsHospitalFormResp);
        aimsHospitalFormResp.setFormPatternId(resp.getCustomFormId());
        aimsHospitalFormResp.setFormPatternName(resp.getCustomFormName());
        aimsHospitalFormResp.setFormContentPc(resp.getFormContent());
        aimsHospitalFormResp.setFormConfigurationPc(resp.getFormConfiguration());
        aimsHospitalFormResp.setFormCategoryCode(resp.getProcessConfigCode());
        aimsHospitalFormResp.setFormConfigurationPad(resp.getFormLayoutPad());
        aimsHospitalFormResp.setFormContentWeb(resp.getFormContentWeb());
        aimsHospitalFormResp.setFormConfigurationWeb(resp.getFormLayoutWeb());
        if (listType != null && listType.size() > 0) {
            for (BaseCodeNameResp baseCodeNameResp : listType) {
                if (baseCodeNameResp.getId().equals(resp.getProcessConfigCode())) {
                    aimsHospitalFormResp.setFormCategoryName(baseCodeNameResp.getName());
                }
            }
        }
        return aimsHospitalFormResp;
    }
}
