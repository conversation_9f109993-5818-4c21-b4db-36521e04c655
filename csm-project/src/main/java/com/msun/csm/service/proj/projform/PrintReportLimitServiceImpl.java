package com.msun.csm.service.proj.projform;


import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.core.component.implementation.api.report.ReportApi;
import com.msun.core.component.implementation.api.report.entity.dto.BaseReportConfigDTO;
import com.msun.core.component.implementation.filter.ImplHospitalDomainHolder;
import com.msun.csm.common.enums.projapplyorder.ProjApplyTypeEnum;
import com.msun.csm.common.enums.projform.ReprtLimitType;
import com.msun.csm.common.model.BaseIdCodeNameResp;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.proj.ProjApplyOrder;
import com.msun.csm.dao.entity.proj.ProjApplyOrderHospitalRecord;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectMember;
import com.msun.csm.dao.entity.report.TmpCustomPrintReport;
import com.msun.csm.dao.entity.report.TmpHospitalPrintReport;
import com.msun.csm.dao.entity.tmp.TmpHdywTeam;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.dao.mapper.proj.ProjApplyOrderHospitalRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectMemberMapper;
import com.msun.csm.dao.mapper.report.TmpCustomPrintReportMapper;
import com.msun.csm.dao.mapper.report.TmpHospitalPrintReportMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.dao.mapper.tmp.TmpHdywPermissionMapper;
import com.msun.csm.dao.mapper.tmp.TmpHdywTeamMapper;
import com.msun.csm.model.dto.DirUserDTO;
import com.msun.csm.model.dto.ProjProjectMemberDTO;
import com.msun.csm.model.req.projform.PrintReportLimitDataReq;
import com.msun.csm.model.vo.DirPersonVO;
import com.msun.csm.model.vo.ProjProjectMemberVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.proj.SurveyPlanAuditService;
import com.msun.csm.util.DomainMapUtil;
import com.msun.csm.util.RedisUtil;
import com.msun.csm.util.SnowFlakeUtil;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description
 * @createDate 2024-09-14 15:15:50
 */
@Service
@Slf4j
public class PrintReportLimitServiceImpl implements PrintReportLimitService {

    @Resource
    private ProjProjectInfoMapper projProjectInfoMapper;

    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;

    @Resource
    private TmpHospitalPrintReportMapper tmpHospitalPrintReportMapper;

    @Resource
    private TmpCustomPrintReportMapper tmpCustomPrintReportMapper;

    @Resource
    private ReportApi reportApi;
    @Resource
    private ImplHospitalDomainHolder domainHolder;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private ProjApplyOrderHospitalRecordMapper projApplyOrderHospitalRecordMapper;

    @Resource
    private ProjProjectMemberMapper projProjectMemberMapper;

    @Resource
    private TmpHdywTeamMapper tmpHdywTeamMapper;

    @Resource
    private TmpHdywPermissionMapper tmpHdywPermissionMapper;

    @Resource
    private UserHelper userHelper;

    @Resource
    private SysUserMapper userMapper;

    @Resource
    private DictProductMapper dictProductMapper;

    @Lazy
    @Resource
    private SurveyPlanAuditService surveyPlanAuditService;

    /**
     * 更新客户
     *
     * @param printReportLimitDataReq
     * @return
     */
    @Override
    public Result printReportCustomLimitDataUpdate(PrintReportLimitDataReq printReportLimitDataReq) {
        /**
         * 1.针对首次进行云资源部署申请的客户，按“实施地客户+单体/区域”记录该客户强制启用众阳设计器【首次云健康申请/交付完成的时候写都行】【主表配置-客户级别】
         * 云资源部署交付以后，调研数据中台接口传递本次申请的所有医院强制启用众阳设计器，交付平台记录2本次申请的医院启用众阳设计器(调用出错时，不要影响云资源部署交付状态更新，交付平台记录执行成功/失败状态即可)【明细表配置-记录医院级别】
         * 3新增开通医院时，按“实施地客户+单体/区域"查询该客户是否强制启用众阳设计器，强制启用的，开通 @医院时，调研同第2步的接口，更新状态;若未查询到记录/该客户未启用，则不调用接口，不限制必须使用众阳设计器【新增明细表配置-根据客户级别配置设置医院级别配置】
         * 若有临时需要调整医院配置的，手动调用数据中台接口
         */
        Integer reportLimitFlag = ReprtLimitType.ZHONGYANG.getNumber();
        if (printReportLimitDataReq.getReportLimitFlag() != null) {
            reportLimitFlag = printReportLimitDataReq.getReportLimitFlag();
        }
        List<TmpCustomPrintReport> cusList = tmpCustomPrintReportMapper.selectList(new QueryWrapper<TmpCustomPrintReport>().eq("custom_info_id", printReportLimitDataReq.getCustomerInfoId()).eq("project_type", printReportLimitDataReq.getProjectType()));
        if (cusList != null && cusList.size() > 0) {
            for (TmpCustomPrintReport item : cusList) {
                item.setReportLimitFlag(reportLimitFlag);
                item.setUpdateTime(new Date());
                tmpCustomPrintReportMapper.updateById(item);
            }
        } else {
            TmpCustomPrintReport tmpCustomPrintReport = new TmpCustomPrintReport();
            tmpCustomPrintReport.setCustomInfoId(printReportLimitDataReq.getCustomerInfoId());
            tmpCustomPrintReport.setProjectType(printReportLimitDataReq.getProjectType());
            tmpCustomPrintReport.setReportLimitFlag(reportLimitFlag);
            tmpCustomPrintReport.setCustomPrintReportId(SnowFlakeUtil.getId());
            tmpCustomPrintReport.setUpdateTime(new Date());
            tmpCustomPrintReport.setCreateTime(new Date());
            tmpCustomPrintReportMapper.insert(tmpCustomPrintReport);

        }
        return Result.success("更新成功");
    }

    /**
     * 更新医院
     *
     * @param printReportLimitDataReq
     * @return
     */
    @Override
    public Result printReportHospitalLimitDataUpdate(PrintReportLimitDataReq printReportLimitDataReq) {
        List<TmpCustomPrintReport> cusList = tmpCustomPrintReportMapper.selectList(new QueryWrapper<TmpCustomPrintReport>().eq("custom_info_id", printReportLimitDataReq.getCustomerInfoId()).eq("project_type", printReportLimitDataReq.getProjectType()));
        List<ProjHospitalInfo> hospitalList = projHospitalInfoMapper.selectList(new QueryWrapper<ProjHospitalInfo>().in("hospital_info_id", printReportLimitDataReq.getHospitalInfoIds()));
        if (!(hospitalList != null && hospitalList.size() > 0)) {
            hospitalList = projHospitalInfoMapper.selectList(new QueryWrapper<ProjHospitalInfo>().eq("custom_info_id", printReportLimitDataReq.getCustomerInfoId()));
        }
        if (cusList != null && cusList.size() > 0) {
            TmpCustomPrintReport tmpCustomPrintReport = cusList.get(0);
            List<TmpHospitalPrintReport> tmpHospitalPrintReports = tmpHospitalPrintReportMapper.selectList(new QueryWrapper<TmpHospitalPrintReport>().eq("custom_print_report_id", tmpCustomPrintReport.getCustomPrintReportId()));
            if (tmpHospitalPrintReports != null && tmpHospitalPrintReports.size() > 0) {
                // 过滤掉hospitalList中医院id在tmpHospitalPrintReports的已发送的数据
                List<Long> hospitalIds = tmpHospitalPrintReports.stream().map(TmpHospitalPrintReport::getHospitalInfoId).collect(Collectors.toList());
                List<ProjHospitalInfo> newHospitalList = hospitalList.stream().filter(item -> !hospitalIds.contains(item.getHospitalInfoId())).collect(Collectors.toList());
                for (ProjHospitalInfo item : newHospitalList) {
                    TmpHospitalPrintReport tmpHospitalPrintReport = new TmpHospitalPrintReport();
                    tmpHospitalPrintReport.setCustomPrintReportId(tmpCustomPrintReport.getCustomPrintReportId());
                    tmpHospitalPrintReport.setPrintReportId(SnowFlakeUtil.getId());
                    tmpHospitalPrintReport.setReportLimitFlag(tmpCustomPrintReport.getReportLimitFlag());
                    tmpHospitalPrintReport.setHospitalInfoId(item.getHospitalInfoId());
                    tmpHospitalPrintReport.setCloudHospitalId(item.getCloudHospitalId());
                    tmpHospitalPrintReport.setUpdateTime(new Date());
                    tmpHospitalPrintReport.setCreateTime(new Date());
                    updateHospital(item, tmpHospitalPrintReport.getReportLimitFlag());
                    tmpHospitalPrintReportMapper.insert(tmpHospitalPrintReport);
                }
            } else {
                for (ProjHospitalInfo item : hospitalList) {
                    TmpHospitalPrintReport tmpHospitalPrintReport = new TmpHospitalPrintReport();
                    tmpHospitalPrintReport.setCustomPrintReportId(tmpCustomPrintReport.getCustomPrintReportId());
                    tmpHospitalPrintReport.setPrintReportId(SnowFlakeUtil.getId());
                    if (printReportLimitDataReq.getReportLimitFlag() != null) {
                        tmpHospitalPrintReport.setReportLimitFlag(printReportLimitDataReq.getReportLimitFlag());
                    } else {
                        tmpHospitalPrintReport.setReportLimitFlag(ReprtLimitType.ZHONGYANG.getNumber());
                    }
                    tmpHospitalPrintReport.setHospitalInfoId(item.getHospitalInfoId());
                    tmpHospitalPrintReport.setCloudHospitalId(item.getCloudHospitalId());
                    tmpHospitalPrintReport.setUpdateTime(new Date());
                    tmpHospitalPrintReport.setCreateTime(new Date());
                    updateHospital(item, tmpHospitalPrintReport.getReportLimitFlag());
                    tmpHospitalPrintReportMapper.insert(tmpHospitalPrintReport);
                }
            }

        } else {
            return Result.fail("未查询到维护的客户信息");
        }
        return null;
    }

    /**
     * 更新报表限制
     *
     * @param projApplyOrder
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Result printReportLimitDataUpdate(ProjApplyOrder projApplyOrder) {
        try {
            ProjProjectInfo projectInfo = projProjectInfoMapper.selectByPrimaryKey(projApplyOrder.getProjectInfoId());
            PrintReportLimitDataReq printReportLimitDataReq = new PrintReportLimitDataReq();
            printReportLimitDataReq.setProjectInfoId(projApplyOrder.getProjectInfoId());
            printReportLimitDataReq.setCustomerInfoId(projApplyOrder.getCustomInfoId());
            printReportLimitDataReq.setProjectType(projectInfo.getProjectType());
            if (projApplyOrder.getApplyType() == ProjApplyTypeEnum.FIRST_EVN_APPLY.getCode()) {
                // 更新客户报表
                printReportCustomLimitDataUpdate(printReportLimitDataReq);
                // 查询医院信息
                List<ProjApplyOrderHospitalRecord> hospitalRecords = projApplyOrderHospitalRecordMapper.selectList(new QueryWrapper<ProjApplyOrderHospitalRecord>().eq("apply_order_id", projApplyOrder.getId()));
                if (hospitalRecords != null) {
                    List<Long> hospitalInfoIds = hospitalRecords.stream().map(ProjApplyOrderHospitalRecord::getHospitalInfoId).collect(Collectors.toList());
                    printReportLimitDataReq.setHospitalInfoIds(hospitalInfoIds);
                }
                // 更新数据
                printReportHospitalLimitDataUpdate(printReportLimitDataReq);
            } else if (projApplyOrder.getApplyType() == ProjApplyTypeEnum.HOSPITAL_APPLY.getCode()) {
                // 查询医院信息
                List<ProjApplyOrderHospitalRecord> hospitalRecords = projApplyOrderHospitalRecordMapper.selectList(new QueryWrapper<ProjApplyOrderHospitalRecord>().eq("apply_order_id", projApplyOrder.getId()));
                if (hospitalRecords != null) {
                    List<Long> hospitalInfoIds = hospitalRecords.stream().map(ProjApplyOrderHospitalRecord::getHospitalInfoId).collect(Collectors.toList());
                    printReportLimitDataReq.setHospitalInfoIds(hospitalInfoIds);
                }
                // 更新数据
                printReportHospitalLimitDataUpdate(printReportLimitDataReq);
            }
        } catch (Exception e) {
            log.error("打印报表异常", e);
        }
        return Result.success();
    }

    /**
     * 报表修改责任人按钮校验
     *
     * @param dto
     * @return
     */
    @Override
    public Result reportUpdateDirUserCheck(DirUserDTO dto) {
        if (dto.getPageSource() != null && "print_report_design".equals(dto.getPageSource())) {
            return Result.success(true);
        }
        // 校验当前登录人是否有 修改责任人功能
        // 1、校验是否是本项目的项目经理
        Long sysUserId = userHelper.getCurrentUser().getSysUserId();
        ProjProjectInfo projProjectInfo = projProjectInfoMapper.selectById(dto.getProjectInfoId());
        // 后端项目经理
        List<ProjProjectMember> existBackProjectManager = projProjectMemberMapper.selectList(new QueryWrapper<ProjProjectMember>().eq("project_member_role_id", 3).eq("project_info_id", dto.getProjectInfoId()));
        if (existBackProjectManager != null && existBackProjectManager.size() > 0) {
            for (ProjProjectMember pro : existBackProjectManager) {
                if (pro.getProjectMemberId().equals(sysUserId)) {
                    return Result.success(true);
                }
            }
        }
        // 2、校验是否为运维团队负责人
        List<TmpHdywTeam> tmpHdywTeams = tmpHdywTeamMapper.selectList(new QueryWrapper<TmpHdywTeam>().eq("team_people", Convert.toLong(userHelper.getCurrentUser().getUserYunyingId())).eq("business_line", dto.getBusinessLine()).last("limit 1"));
        if ((CollectionUtil.isNotEmpty(tmpHdywTeams) && tmpHdywTeams.get(0).getIsHead() == 1) || (sysUserId.equals(projProjectInfo.getProjectLeaderId()))) {
            return Result.success(true);
        } else {
            return Result.success(false);
        }
    }

    /**
     * 查询报表责任人下拉数据
     *
     * @param dto
     * @return
     */
    @Override
    public Result<DirPersonVO> getDirUserList(DirUserDTO dto) {
        DirPersonVO dirPersonVO = new DirPersonVO();
        List<BaseIdNameResp> dirUserList = new ArrayList<>();
        if (dto.getProjectInfoId() != null) {
            ProjProjectMemberDTO dto1 = new ProjProjectMemberDTO();
            dto1.setProjectInfoId(dto.getProjectInfoId());
            List<ProjProjectMemberVO> projProjectMemberVOS = projProjectMemberMapper.selectMemberVO(dto1);
            for (ProjProjectMemberVO vo : projProjectMemberVOS) {
                BaseIdNameResp baseIdNameResp = new BaseIdNameResp();
                baseIdNameResp.setId(vo.getProjectMemberId());
                baseIdNameResp.setName(vo.getProjectMemberName());
                dirUserList.add(baseIdNameResp);
            }
            dirPersonVO.setDirPersonList(dirUserList);
        } else {
            List<BaseIdCodeNameResp> canSurveyProduct = surveyPlanAuditService.getAuditor(null);
            if (canSurveyProduct != null  && canSurveyProduct.size() > 0) {
                for (BaseIdCodeNameResp resp : canSurveyProduct) {
                    BaseIdNameResp baseIdNameResp = new BaseIdNameResp();
                    baseIdNameResp.setId(Long.valueOf(resp.getId()));
                    baseIdNameResp.setName(resp.getName());
                    dirUserList.add(baseIdNameResp);
                }
            }
            dirPersonVO.setDirPersonList(dirUserList);
        }
        return Result.success(dirPersonVO);
    }

    /**
     * 查询审批人下拉数据
     *
     * @param dto
     * @return
     */
    @Override
    public Result<List<BaseIdNameResp>> getExamineUserList(DirUserDTO dto) {
        List<BaseIdNameResp> dirUserList = new ArrayList<>();
        // 判断是否只能是运维团队人员的责任人下拉
        List<TmpHdywTeam> tmpHdywTeams = tmpHdywTeamMapper.selectList(new QueryWrapper<TmpHdywTeam>().eq("business_line", dto.getBusinessLine()));
        if (ObjectUtil.isNotEmpty(tmpHdywTeams)) {
            // 查询对应人员信息
            List<SysUser> userList = userMapper.selectList(new QueryWrapper<SysUser>().in("user_yunying_id", tmpHdywTeams.stream().map(TmpHdywTeam::getTeamPeople).map(String::valueOf).collect(Collectors.toList())));
            for (SysUser user : userList) {
                BaseIdNameResp baseIdNameResp = new BaseIdNameResp();
                baseIdNameResp.setId(user.getSysUserId());
                baseIdNameResp.setName(user.getUserName());
                dirUserList.add(baseIdNameResp);
            }
        }
        return Result.success(dirUserList);
    }


    /**
     * 处理医院数据
     *
     * @param info
     * @param reportLimitFlag
     */
    private void updateHospital(ProjHospitalInfo info, Integer reportLimitFlag) {
        Map<String, String> domainMap = DomainMapUtil.getDomainMap(info);
        log.info("设定医院信息:{}", domainMap);
        domainHolder.refresh(domainMap);
        domainMap.clear();
        BaseReportConfigDTO batchInsertDTO = new BaseReportConfigDTO();
        batchInsertDTO.setHospitalId(info.getCloudHospitalId());
        batchInsertDTO.setHisOrgId(info.getOrgId());
        batchInsertDTO.setCategory("基础设置");
        batchInsertDTO.setConfig("设计器配置");
        batchInsertDTO.setValue(ReprtLimitType.getCodeByNumber(reportLimitFlag));
        reportApi.editDataIpDict(batchInsertDTO);
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        Future<?> future = executorService.submit(() -> {
            reportApi.editDataIpDict(batchInsertDTO);
        });
        try {
            // 等待2秒
            future.get(2, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("线程被中断", e);
            // 重新设置中断状态
            Thread.currentThread().interrupt();
        } catch (ExecutionException e) {
            log.error("编辑数据字典失败", e.getCause());
        } catch (TimeoutException e) {
            log.error("编辑数据字典超时", e);
            // 取消任务
            future.cancel(true);
        }
    }

}
