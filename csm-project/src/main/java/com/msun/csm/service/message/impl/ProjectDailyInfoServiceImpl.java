package com.msun.csm.service.message.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.enums.WorkTypeEnum;
import com.msun.csm.common.enums.message.MsgToCategory;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.staticvariable.StaticPara;
import com.msun.csm.dao.entity.dailyreport.resp.RiskProjectInfo;
import com.msun.csm.dao.entity.dailyreport.resp.RiskProjectResp;
import com.msun.csm.dao.entity.oldimsp.EquipProgressInfo;
import com.msun.csm.dao.entity.oldimsp.ThirdInterfaceProgress;
import com.msun.csm.dao.entity.proj.ProductInfo;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjThirdInterface;
import com.msun.csm.dao.entity.proj.ProjectDailyInfo;
import com.msun.csm.dao.entity.tmp.TmpProjectNewVsOld;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjEquipRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyReportMapper;
import com.msun.csm.dao.mapper.proj.ProjThirdInterfaceMapper;
import com.msun.csm.dao.mapper.projform.ProjSurveyFormMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.dao.mapper.tmp.TmpProjectNewVsOldMapper;
import com.msun.csm.feign.client.oldimsp.OldImspFeignClient;
import com.msun.csm.model.dto.ProductInfoDTO;
import com.msun.csm.model.dto.ProjProductBacklogDTO;
import com.msun.csm.model.dto.applyorder.ProjApplyOrderMainInfoDTO;
import com.msun.csm.model.param.MessageParam;
import com.msun.csm.model.req.projform.ProjSurveyFormReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportReq;
import com.msun.csm.model.resp.projform.ProjSurveyReprotFormCount;
import com.msun.csm.model.vo.ProjCustomInfoVO;
import com.msun.csm.model.vo.ProjMilestoneInfoVO;
import com.msun.csm.model.vo.ProjProductBacklogDataVO;
import com.msun.csm.model.vo.ProjProductBacklogVO;
import com.msun.csm.model.vo.applyorder.HospitalMainInfoVO;
import com.msun.csm.model.vo.surveyplan.ProjectDailyProductVO;
import com.msun.csm.model.vo.surveyplan.SurveyPlanInitVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.config.ProjMessageInfoService;
import com.msun.csm.service.message.ProjectDailyInfoService;
import com.msun.csm.service.message.SendMessageService;
import com.msun.csm.service.proj.ProjOrderProductService;
import com.msun.csm.service.proj.ProjProductBacklogService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderDescService;
import com.msun.csm.util.RedisUtil;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class ProjectDailyInfoServiceImpl implements ProjectDailyInfoService {

    @Resource
    private ProjEquipRecordMapper projEquipRecordMapper;

    @Resource
    private ProjThirdInterfaceMapper projThirdInterfaceMapper;

    @Resource
    private ProjProjectInfoMapper projectInfoMapper;

    @Resource
    private ProjCustomInfoMapper projCustomInfoMapper;

    @Resource
    private ProjOrderProductService orderProductService;

    @Resource
    private ProjMilestoneInfoMapper projMilestoneInfoMapper;

    @Resource
    private TmpProjectNewVsOldMapper tmpProjectNewVsOldMapper;

    @Resource
    private ProjSurveyReportMapper projSurveyReportMapper;

    @Resource
    private ProjSurveyFormMapper projSurveyFormMapper;

    @Resource
    private OldImspFeignClient oldImspFeignClient;

    @Resource
    private SendMessageService messageService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private ProjProductBacklogService productBacklogService;

    @Resource
    private ProjApplyOrderDescService applyOrderDescService;

    @Resource
    private SysUserMapper sysUserMapper;

    @Value("${project.current.url}")
    private String currentUrl;

    @Value("${project.current.qyWeChat-Auth-url}")
    private String weChatAuthUrl;

    @Resource
    private ProjMessageInfoService messageInfoService;

    private static final String UNDERWAY_KEY = "project_daily_message_task";

    private static final String FINISHED_KEY = "project_daily_message_finished";

    @Resource
    private UserHelper userHelper;

    @Override
    public void sendProjectDailyMessage() {
        List<Integer> statusList = new ArrayList<>();
        statusList.add(3);
        statusList.add(4);
        List<ProjProjectInfo> projProjectInfos = projectInfoMapper.selectList(
                new QueryWrapper<ProjProjectInfo>()
                        // 未作废的
                        .eq("is_deleted", 0)
                        // 首期
                        .eq("his_flag", 1)
                        .in("project_deliver_status", statusList)
        );
        log.info("查询需要发送进度日报的项目信息={}", JSON.toJSONString(projProjectInfos));
        if (CollectionUtil.isEmpty(projProjectInfos)) {
            throw new IllegalArgumentException("没有需要发送进度日报的项目信息");
        }
        // 所有从老项目迁移的数据
        List<TmpProjectNewVsOld> allOldData = tmpProjectNewVsOldMapper.getAllOldData();
        if (!CollectionUtil.isEmpty(allOldData)) {
            // 从老项目迁移过来的数据在新系统中的项目ID
            List<Long> projectInfoIdListFromOldSystem = allOldData.stream().map(TmpProjectNewVsOld::getNewProjectInfoId)
                    .collect(Collectors.toList());
            // 过滤掉从老项目迁移过来的数据
            projProjectInfos = projProjectInfos.stream()
                    .filter(item -> !projectInfoIdListFromOldSystem.contains(item.getProjectInfoId()))
                    .collect(Collectors.toList());
        }
        log.info("过滤掉从老项目迁移过来的数据后需要发送进度日报的项目信息={}", JSON.toJSONString(projProjectInfos));
        if (CollectionUtil.isEmpty(projProjectInfos)) {
            throw new IllegalArgumentException("过滤掉从老项目迁移过来的数据后没有需要发送进度日报的项目信息");
        }
        Object underwayFlag = redisUtil.get(UNDERWAY_KEY);
        if (underwayFlag != null) {
            throw new IllegalArgumentException("项目进度日报消息发送正在处理中，请勿重复操作");
        }
        redisUtil.set(UNDERWAY_KEY, true, 60, TimeUnit.MINUTES);
        try {
            for (ProjProjectInfo item : projProjectInfos) {
                try {
                    Object finishedFlag = redisUtil.get(
                            FINISHED_KEY + DateUtil.formatDate(new Date()) + item.getProjectInfoId());
                    if (finishedFlag != null) {
                        log.info("当前项目今日已发送项目进度日报，不再重复发送，项目ID={}", item.getProjectInfoId());
                        continue;
                    }

                    Long sysUserId = userHelper.getCurrentSysUserIdWithDefaultValue();
                    String domain = currentUrl.endsWith("/") ? currentUrl : currentUrl + "/";
                    // 业务链接地址
                    String businessUrl = domain + "projectDaily?projectInfoId=" + item.getProjectInfoId();
                    // 记录实际业务跳转路径
                    Long msgInfoId = messageInfoService.insert(businessUrl, sysUserId);

                    // 发送给分公司经理、部门经理、客服中心总、PMO
                    MessageParam messageParam = new MessageParam();
                    messageParam.setProjectInfoId(item.getProjectInfoId());
                    messageParam.setContent(
                            item.getProjectName() + DateUtil.formatDate(new Date()) + "项目进度日报，请您查阅");
                    String url = weChatAuthUrl + "?state=" + msgInfoId;
                    messageParam.setUrl(url);
                    messageParam.setMessageTypeId(8001L);
                    messageParam.setMessageToCategory(MsgToCategory.TO_ROLE.getCode());
                    messageService.sendMessage(messageParam);
                    // 发送给项目经理
                    MessageParam messageParam2 = new MessageParam();
                    messageParam2.setProjectInfoId(messageParam.getProjectInfoId());
                    messageParam2.setContent(messageParam.getContent());
                    messageParam2.setUrl(messageParam.getUrl());
                    messageParam2.setMessageTypeId(messageParam.getMessageTypeId());
                    messageParam2.setMessageToCategory(MsgToCategory.TO_PRO_MGR.getCode());
                    messageService.sendMessage(messageParam2);
                    // 发送完成放到缓存里，每天只发送一次
                    long between = DateUtil.endOfDay(new Date()).between(new Date(), DateUnit.MINUTE);
                    redisUtil.set(FINISHED_KEY + DateUtil.formatDate(new Date()) + item.getProjectInfoId(), true,
                            between, TimeUnit.MINUTES);
                } catch (Exception e) {
                    log.error(
                            "发送项目进度日报，循环内部异常，发生异常，projectInfoId={}，projectName={}，errMsg={}，stackInfo=",
                            item.getProjectInfoId(), item.getProjectName(), e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            log.error("发送项目进度日报，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        } finally {
            // 删除处理中key
            redisUtil.del(UNDERWAY_KEY);
        }
    }

    @Override
    public ProjectDailyInfo getProjectDailyInfo(Long projectInfoId) {
        // 项目信息
        ProjProjectInfo projProjectInfo = projectInfoMapper.selectOne(
                new QueryWrapper<ProjProjectInfo>()
                        .eq("is_deleted", 0)
                        .eq("project_info_id", projectInfoId)
        );
        if (projProjectInfo == null) {
            throw new IllegalArgumentException(
                    String.format("参数信息非法，未能根据项目ID获取到项目信息。项目ID=%s", projectInfoId));
        }
        String settleInTime = projProjectInfo.getSettleInTime() == null ? null
                : DateUtil.formatDateTime(projProjectInfo.getSettleInTime());
        // 累计工期
        String cumulativeDuration = projProjectInfo.getSettleInTime() == null ? "0"
                : String.valueOf(DateUtil.betweenDay(projProjectInfo.getSettleInTime(), new Date(), true) + 1);
        ProjApplyOrderMainInfoDTO projApplyOrderMainInfoDTO = new ProjApplyOrderMainInfoDTO();
        projApplyOrderMainInfoDTO.setProjectInfoId(String.valueOf(projectInfoId));
        HospitalMainInfoVO hospitalMainInfoVO = null;
        try {
            hospitalMainInfoVO = applyOrderDescService.getHospitalMainInfo(projApplyOrderMainInfoDTO);
        } catch (Exception e) {
            log.error("项目日报获取年收入，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        String customAnnualIncome;
        if (hospitalMainInfoVO != null && StringUtils.isNotBlank(hospitalMainInfoVO.getAnnualIncome())) {
            customAnnualIncome = hospitalMainInfoVO.getAnnualIncome();
        } else {
            customAnnualIncome = "0";
            log.info("项目日报获取年收入，没有获取到hospitalMainInfoVO信息，projectInfoId={}", projectInfoId);
        }
        // 客户信息
        List<ProjCustomInfoVO> customInfoVOS = projCustomInfoMapper.selectVOById(projProjectInfo.getCustomInfoId());
        String customName;
        if (!CollectionUtils.isEmpty(customInfoVOS) && customInfoVOS.get(0) != null) {
            customName = customInfoVOS.get(0).getCustomName();
        } else {
            customName = "项目信息表中的客户ID非法。项目ID=" + projectInfoId + "客户ID="
                    + projProjectInfo.getCustomInfoId();
        }
        // 工单产品
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        productInfoDTO.setProjectInfoId(projectInfoId);
        productInfoDTO.setProductWorkType(WorkTypeEnum.ORDER_WORK.getCode());
        List<ProductInfo> productInfos = orderProductService.getProductListImpl(productInfoDTO);
        String orderProductCount;
        if (!CollectionUtil.isEmpty(productInfos)) {
            orderProductCount = String.valueOf(productInfos.size());
        } else {
            orderProductCount = "0";
        }
        // 云资源部署
        ProjMilestoneInfo cloudResourceMilestoneParam = new ProjMilestoneInfo();
        cloudResourceMilestoneParam.setProjectInfoId(projectInfoId);
        cloudResourceMilestoneParam.setMilestoneNodeCode("cloud_resource");
        ProjMilestoneInfoVO cloudResourceMilestone = projMilestoneInfoMapper.getMilestoneInfoByParams(
                cloudResourceMilestoneParam);
        short cloudResourceStatus;
        if (cloudResourceMilestone != null && cloudResourceMilestone.getMilestoneStatus() != null) {
            cloudResourceStatus = cloudResourceMilestone.getMilestoneStatus();
        } else {
            cloudResourceStatus = 0;
        }
        // 导数据进度
        ProjMilestoneInfo dataImportMilestoneParam = new ProjMilestoneInfo();
        dataImportMilestoneParam.setProjectInfoId(projectInfoId);
        dataImportMilestoneParam.setMilestoneNodeCode("data_import");
        ProjMilestoneInfoVO dataImportMilestone = projMilestoneInfoMapper.getMilestoneInfoByParams(
                dataImportMilestoneParam);
        short dataImportStatus;
        if (dataImportMilestone != null && dataImportMilestone.getMilestoneStatus() != null) {
            dataImportStatus = dataImportMilestone.getMilestoneStatus();
        } else {
            dataImportStatus = 0;
        }
        // 新老项目对照关系
        List<TmpProjectNewVsOld> tmpProjectNewVsOldList = tmpProjectNewVsOldMapper.selectList(
                new QueryWrapper<TmpProjectNewVsOld>()
                        .eq("new_project_info_id", projectInfoId)
        );
        TmpProjectNewVsOld tmpProjectNewVsOld = tmpProjectNewVsOldList.get(0);
        // 三方接口信息，只查询上线前必备的
        List<ProjThirdInterface> requiredInterface = projThirdInterfaceMapper.getRequiredInterface(projectInfoId);
        // 三方接口中上线前必须完成的接口的总数量
        long thirdInterfaceCount = 0;
        // 三方接口中上线前必须完成的接口的测试通过的数量
        long thirdInterfaceTested = 0;
        // 三方接口中上线前必须完成的接口的未完成的数量
        long thirdInterfaceUnderway = 0;
        if (!CollectionUtils.isEmpty(requiredInterface)) {
            try {
                thirdInterfaceCount = requiredInterface.size();
            } catch (Exception e) {
                log.error("获取项目日报内容的三方接口上线前必须完成的总数量，发生异常，信息={}，errMsg={}，stackInfo=",
                        JSON.toJSONString(requiredInterface), e.getMessage(), e);
            }
            try {
                thirdInterfaceTested = requiredInterface.stream()
                        .filter(item -> Integer.valueOf(32).compareTo(item.getStatus()) <= 0).count();
            } catch (Exception e) {
                log.error("获取项目日报内容的三方接口测试通过数量，发生异常，信息={}，errMsg={}，stackInfo=",
                        JSON.toJSONString(requiredInterface), e.getMessage(), e);
            }
            try {
                thirdInterfaceUnderway = thirdInterfaceCount - thirdInterfaceTested;
            } catch (Exception e) {
                log.error("获取项目日报内容的三方接口进行中数量，发生异常，信息={}，errMsg={}，stackInfo=",
                        JSON.toJSONString(requiredInterface), e.getMessage(), e);
            }
        }
        // 三方接口整体完成进度：0-未完成、1-已完成
        Integer thirdInterfaceStatus = thirdInterfaceUnderway == 0 ? 1 : 0;
        // 报表信息
        ProjSurveyReportReq projSurveyReportReq = new ProjSurveyReportReq();
        projSurveyReportReq.setProjectInfoId(projectInfoId);
        List<ProjSurveyReprotFormCount> projSurveyReprotFormPageResp = projSurveyReportMapper.selectSurveyReportCount(
                projSurveyReportReq);
        // 报表中上线必须完成的报表总数
        int reportCount = 0;
        // 报表中上线必须完成的报表中已制作完成数量
        int reportFinished = 0;
        // 报表中上线必须完成的报表中未制作完成数量
        int reportUnFinished = 0;
        if (!CollectionUtils.isEmpty(projSurveyReprotFormPageResp)) {
            try {
                reportCount = projSurveyReprotFormPageResp.get(0).getPreLaunchCompletionCount();
            } catch (Exception e) {
                log.error("获取项目日报内容的报表上线前必须完成数量，发生异常，信息={}，errMsg={}，stackInfo=",
                        JSON.toJSONString(projSurveyReprotFormPageResp.get(0)), e.getMessage(), e);
            }
            try {
                reportFinished = projSurveyReprotFormPageResp.get(0).getPreLaunchCompletionCount()
                        - projSurveyReprotFormPageResp.get(0).getIncompleteCount() - projSurveyReprotFormPageResp.get(0)
                        .getRejectedCount();
            } catch (Exception e) {
                log.error("获取项目日报内容的报表已制作完成数量，发生异常，信息={}，errMsg={}，stackInfo=",
                        JSON.toJSONString(projSurveyReprotFormPageResp.get(0)), e.getMessage(), e);
            }
            try {
                reportUnFinished = projSurveyReprotFormPageResp.get(0).getIncompleteCount();
            } catch (Exception e) {
                log.error("获取项目日报内容的报表未制作完成数量，发生异常，信息={}，errMsg={}，stackInfo=",
                        JSON.toJSONString(projSurveyReprotFormPageResp.get(0)), e.getMessage(), e);
            }
        }
        // 报表整体完成进度：0-未完成、1-已完成
        Integer reportStatus = reportCount == reportFinished ? 1 : 0;
        // 表单信息
        ProjSurveyFormReq projSurveyFormReq = new ProjSurveyFormReq();
        projSurveyFormReq.setProjectInfoId(projectInfoId);
        List<ProjSurveyReprotFormCount> projSurveyReprotFormCountList = projSurveyFormMapper.selectSurveyFormCount(
                projSurveyFormReq);
        // 上线前必须完成的表单的总数量
        int formCount = 0;
        // 上线前必须完成的表单的已制作完成数量
        int formFinished = 0;
        // 上线前必须完成的表单的未制作完成数量
        int formUnFinished = 0;
        if (!CollectionUtils.isEmpty(projSurveyReprotFormCountList)) {
            try {
                formCount = projSurveyReprotFormCountList.get(0).getPreLaunchCompletionCount();
            } catch (Exception e) {
                log.error("获取项目日报内容的上线前必须完成的表单的总数量，信息={}，errMsg={}，stackInfo=",
                        JSON.toJSONString(projSurveyReprotFormCountList.get(0)), e.getMessage(), e);
            }
            try {
                formFinished = projSurveyReprotFormCountList.get(0).getPreLaunchCompletionCount()
                        - projSurveyReprotFormCountList.get(0).getIncompleteCount() - projSurveyReprotFormCountList.get(
                        0).getRejectedCount();
            } catch (Exception e) {
                log.error("获取项目日报内容的表单已制作完成数量，信息={}，errMsg={}，stackInfo=",
                        JSON.toJSONString(projSurveyReprotFormCountList.get(0)), e.getMessage(), e);
            }
            try {
                formUnFinished = projSurveyReprotFormCountList.get(0).getIncompleteCount();
            } catch (Exception e) {
                log.error("获取项目日报内容的表单未制作完成数量，信息={}，errMsg={}，stackInfo=",
                        JSON.toJSONString(projSurveyReprotFormCountList.get(0)), e.getMessage(), e);
            }
        }
        // 表单整体完成进度：0-未完成、1-已完成
        Integer formStatus = formCount == formFinished ? 1 : 0;
        // 设备信息
        List<EquipProgressInfo> equipProgressInfoList = new ArrayList<>();
        try {
            equipProgressInfoList = projEquipRecordMapper.getEquipProgressInfo(projectInfoId);
        } catch (Exception e) {
            log.error("获取项目日报内容的设备对接信息，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        // 设备整体完成进度：0-未完成、1-已完成
        int equipProgressStatus;
        if (CollectionUtils.isEmpty(equipProgressInfoList)) {
            equipProgressStatus = 1;
        } else {
            List<String> collect = equipProgressInfoList.stream().map(item -> {
                if (item.getEquipCount().equals(item.getEquipFinishedCount())) {
                    return "1";
                }
                return "0";
            }).collect(Collectors.toList());
            if (collect.contains("0")) {
                equipProgressStatus = 0;
            } else {
                equipProgressStatus = 1;
            }
        }
        // 各产品准备工作里程碑
        ProjMilestoneInfo preparatProductMilestoneParam = new ProjMilestoneInfo();
        preparatProductMilestoneParam.setProjectInfoId(projectInfoId);
        preparatProductMilestoneParam.setMilestoneNodeCode("preparat_product");
        ProjMilestoneInfoVO preparatProductMilestone = projMilestoneInfoMapper.getMilestoneInfoByParams(
                preparatProductMilestoneParam);
        Result<SurveyPlanInitVO> surveyPlanInitResult = null;
        // 走到了准备阶段
        if (preparatProductMilestone != null) {
            ProjProductBacklogDTO projProductBacklogDTO = new ProjProductBacklogDTO();
            projProductBacklogDTO.setProjectInfoId(projectInfoId);
            projProductBacklogDTO.setMilestoneInfoId(preparatProductMilestone.getMilestoneInfoId());
            surveyPlanInitResult = productBacklogService.selectProductPrepareHospital(projProductBacklogDTO);
        }
        List<ProjectDailyProductVO> projectDailyProductList = new ArrayList<>();
        if (surveyPlanInitResult != null && surveyPlanInitResult.getData() != null && !CollectionUtils.isEmpty(
                surveyPlanInitResult.getData().getHospitalProductVOList())) {
            surveyPlanInitResult.getData().getHospitalProductVOList().forEach(item -> {
                // 复制到项目日报实体中
                ProjectDailyProductVO projectDailyProductVO = new ProjectDailyProductVO();
                projectDailyProductVO.setHospitalInfoId(String.valueOf(item.getHospitalInfoId()));
                projectDailyProductVO.setHospitalName(item.getHospitalName());
                projectDailyProductVO.setProductNum(
                        item.getProductNum() == null ? "0" : String.valueOf(item.getProductNum()));
                projectDailyProductVO.setCompleteProductNum(
                        item.getCompleteProductNum() == null ? "0" : String.valueOf(item.getCompleteProductNum()));
                projectDailyProductVO.setProductUnFinished(
                        String.valueOf(item.getProductNum() - item.getCompleteProductNum()));
                projectDailyProductList.add(projectDailyProductVO);
            });
        }
        // 产品准备工作整体完成进度：0-未完成、1-已完成
        int projectDailyProductStatus;
        // 设备整体完成进度：0-未完成、1-已完成
        if (CollectionUtils.isEmpty(projectDailyProductList)) {
            projectDailyProductStatus = 1;
        } else {
            List<String> collect = projectDailyProductList.stream().map(item -> {
                if (item.getProductNum().equals(item.getCompleteProductNum())) {
                    return "1";
                }
                return "0";
            }).collect(Collectors.toList());
            if (collect.contains("0")) {
                projectDailyProductStatus = 0;
            } else {
                projectDailyProductStatus = 1;
            }
        }
        return ProjectDailyInfo
                .builder()
                .customName(customName)
                .customAnnualIncome(customAnnualIncome + "（亿元）")
                .orderProductCount(orderProductCount)
                .settleInTime(settleInTime)
                .cumulativeDuration(cumulativeDuration + "天")
                .cloudResourceStatus(cloudResourceStatus)
                .dataImportStatus(dataImportStatus)
                .thirdInterfaceCount(String.valueOf(thirdInterfaceCount))
                .thirdInterfaceTested(String.valueOf(thirdInterfaceTested))
                .thirdInterfaceUnderway(String.valueOf(thirdInterfaceUnderway))
                .thirdInterfaceStatus(thirdInterfaceStatus)
                .reportCount(String.valueOf(reportCount))
                .reportFinished(String.valueOf(reportFinished))
                .reportUnFinished(String.valueOf(reportUnFinished))
                .reportStatus(reportStatus)
                .formCount(String.valueOf(formCount))
                .formFinished(String.valueOf(formFinished))
                .formUnFinished(String.valueOf(formUnFinished))
                .formStatus(formStatus)
                .equipProgressInfoList(equipProgressInfoList)
                .equipProgressStatus(equipProgressStatus)
                .projectDailyProductList(projectDailyProductList)
                .projectDailyProductStatus(projectDailyProductStatus)
                .build();
    }

    @Override
    public boolean clearFinishedFlag(Long projectInfoId) {
        try {
            redisUtil.del(FINISHED_KEY + DateUtil.formatDate(new Date()) + projectInfoId);
        } catch (Exception e) {
            log.error("删除项目日报消息发送成功标记，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return false;
        }
        return true;
    }

    @Override
    public List<ThirdInterfaceProgress> getThirdInterfaceProgress(Long projectInfoId) {
        // 项目信息
        ProjProjectInfo projProjectInfo = projectInfoMapper.selectOne(
                new QueryWrapper<ProjProjectInfo>()
                        .eq("is_deleted", 0)
                        .eq("project_info_id", projectInfoId)
        );
        if (projProjectInfo == null) {
            throw new IllegalArgumentException(
                    String.format("参数信息非法，未能根据项目ID获取到项目信息。项目ID=%s", projectInfoId));
        }
        // 三方接口信息，只查询上线前必备的
        List<ThirdInterfaceProgress> result = new ArrayList<>();
        List<ProjThirdInterface> requiredInterface = projThirdInterfaceMapper.getRequiredInterface(projectInfoId);
        if (CollectionUtils.isEmpty(requiredInterface)) {
            return new ArrayList<>();
        }
        List<ProjThirdInterface> unfinished = requiredInterface.stream()
                .filter(item -> Integer.valueOf(32).compareTo(item.getStatus()) > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(unfinished)) {
            return new ArrayList<>();
        }
        for (ProjThirdInterface projThirdInterface : unfinished) {
            ThirdInterfaceProgress thirdInterfaceProgress = new ThirdInterfaceProgress();
            thirdInterfaceProgress.setThirdName(projThirdInterface.getDictInterfaceName());
            thirdInterfaceProgress.setStatus(this.convertStatus(projThirdInterface.getStatus()));
            result.add(thirdInterfaceProgress);
        }
        return result;
    }

    private String convertStatus(Integer status) {
        if (Integer.valueOf(0).equals(status)) {
            return "未申请";
        }
        if (Integer.valueOf(1).equals(status)) {
            return "提交裁定";
        }
        if (Integer.valueOf(11).equals(status)) {
            return "裁定驳回";
        }
        if (Integer.valueOf(12).equals(status)) {
            return "待评审";
        }
        if (Integer.valueOf(13).equals(status)) {
            return "裁定通过";
        }
        if (Integer.valueOf(14).equals(status)) {
            return "接口分公司驳回";
        }
        if (Integer.valueOf(21).equals(status)) {
            return "测试环境申请授权";
        }
        if (Integer.valueOf(22).equals(status)) {
            return "测试环境授权通过";
        }
        if (Integer.valueOf(23).equals(status)) {
            return "研发中";
        }
        if (Integer.valueOf(24).equals(status)) {
            return "测试环境测试完成";
        }
        if (Integer.valueOf(31).equals(status)) {
            return "申请正式环境授权";
        }
        if (Integer.valueOf(32).equals(status)) {
            return "正式环境授权通过";
        }
        if (Integer.valueOf(33).equals(status)) {
            return "研发完成";
        }
        if (Integer.valueOf(50).equals(status)) {
            return "接口完成";
        }
        return "未完成";
    }

    @Override
    public List<ProjProductBacklogVO> getProductBacklogProgress(Long projectInfoId, Long hospitalInfoId) {
        ProjProductBacklogDTO projProductBacklogDTO = new ProjProductBacklogDTO();
        projProductBacklogDTO.setProjectInfoId(projectInfoId);
        projProductBacklogDTO.setHospitalInfoId(hospitalInfoId);
        ProjProductBacklogDataVO projProductBacklogDataVO = productBacklogService.selectProductBacklog(
                projProductBacklogDTO);
        if (projProductBacklogDataVO != null) {
            List<ProjProductBacklogVO> backlogList = projProductBacklogDataVO.getBacklogList();
            if (CollectionUtils.isEmpty(backlogList)) {
                return new ArrayList<>();
            }
            return backlogList.stream().filter(item -> Integer.valueOf(0).equals(item.getCompleteStatus()))
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 获取风险项目列表
     *
     * @return
     */
    @Override
    public RiskProjectResp getRiskProjects() {
        //获取入驻风险项目列表
        log.info("获取上线风险项目列表");
        List<ProjProjectInfo> onlineRiskProjects = projectInfoMapper.queryOnlineRiskProjects();
        //获取验收风险项目列表
        log.info("获取验收风险项目列表");
        List<ProjProjectInfo> acceptRisks = projectInfoMapper.queryAcceptRiskProjects();
        if (CollectionUtils.isEmpty(onlineRiskProjects) && CollectionUtils.isEmpty(acceptRisks)) {
            return new RiskProjectResp();
        }
        List<Long> customInfoIds = onlineRiskProjects.stream().map(ProjProjectInfo::getCustomInfoId)
                .collect(Collectors.toList());
        customInfoIds.addAll(acceptRisks.stream().map(ProjProjectInfo::getCustomInfoId).collect(Collectors.toList()));
        List<ProjCustomInfo> customInfos = projCustomInfoMapper.selectByCustomInfoIds(customInfoIds);
        //转换成RiskProjectInfo
        List<RiskProjectInfo> onlineRiskProjectInfos = onlineRiskProjects.stream()
                .map(item -> getRiskProjectInfo(item, item.getSettleInTime(), customInfos))
                .collect(Collectors.toList());
        List<RiskProjectInfo> acceptRiskProjectInfos = acceptRisks.stream()
                .map(item -> getRiskProjectInfo(item, item.getOnlineTime(), customInfos)).collect(Collectors.toList());
        RiskProjectResp resp = new RiskProjectResp();
        resp.setOnlineProjects(onlineRiskProjectInfos);
        resp.setAcceptProjects(acceptRiskProjectInfos);
        return resp;
    }

    @NotNull
    private RiskProjectInfo getRiskProjectInfo(ProjProjectInfo projectInfo, Date keyTime,
                                               List<ProjCustomInfo> customInfos) {
        RiskProjectInfo riskProjectInfo = new RiskProjectInfo();
        riskProjectInfo.setProjectInfoId(projectInfo.getProjectInfoId());
        ProjCustomInfo customInfo = customInfos.stream()
                .filter(item -> item.getCustomInfoId().equals(projectInfo.getCustomInfoId())).findFirst().orElse(null);
        String projectName;
        if (customInfo == null) {
            String projectTypeName = projectInfo.getProjectType() == 1 ? "单体" : "区域";
            projectName = projectInfo.getProjectName() + "-" + projectTypeName;
        } else {
            boolean isSale = customInfo.getTelesalesFlag() == 1;
            String projectTypeName = isSale ? "电销" : (projectInfo.getProjectType() == 1 ? "单体" : "区域");
            projectName = customInfo.getCustomName() + "-" + projectTypeName;
        }
        riskProjectInfo.setProjectInfoName(projectName);
        riskProjectInfo.setKeyTime(keyTime);
        riskProjectInfo.setDuration(DateUtil.betweenDay(projectInfo.getSettleInTime(), new Date(), true) + 1 + "天");
        return riskProjectInfo;
    }


    /**
     * 发送风险项目信息消息
     */
    @Override
    public void sendRiskProjectInfosMessageTask() {
        String rote = "projectRiskAlert";
        // 发送给部门经理、客服中心总、PMO、系统管理员
        MessageParam messageParam = new MessageParam();
        messageParam.setProjectInfoId(null);
        messageParam.setTitle("风险项目提醒");
        messageParam.setContent(DateUtil.formatDate(new Date()) + "-风险项目提醒");
        String businessUrl = currentUrl.endsWith("/") ? currentUrl + rote : currentUrl + "/" + rote;
        //记录实际业务跳转路径-定时任务没有当前用户，用户id默认-1
        Long msgInfoId = messageInfoService.insert(businessUrl, StaticPara.DEFAULT_LONG);
        String url = weChatAuthUrl + "?state=" + msgInfoId;
        messageParam.setUrl(url);
        messageParam.setMessageTypeId(8002L);
        messageParam.setMessageToCategory(MsgToCategory.SYS.getCode());
        List<Long> roleIds = new ArrayList<>();
        roleIds.add(447447825206853633L);
        roleIds.add(447447825206853635L);
        roleIds.add(447447825206853637L);
        roleIds.add(447447825206853639L);
        List<Long> sysUserIds = sysUserMapper.selectSysUsersByRoleIds(roleIds);
        messageParam.setSysUserIds(sysUserIds);
        messageService.sendMessage(messageParam);
    }
}
