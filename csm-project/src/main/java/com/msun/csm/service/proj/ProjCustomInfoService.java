package com.msun.csm.service.proj;

import java.util.List;

import org.springframework.web.bind.annotation.RequestBody;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.feign.entity.yunying.resp.YunYingCustomersResp;
import com.msun.csm.model.dto.ProjCustomInfoDTO;
import com.msun.csm.model.vo.ProjCustomInfoVO;
import com.msun.csm.model.vo.ProjectToolsOptionsForCustomerInfoTopVO;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/24
 */

public interface ProjCustomInfoService {

    Result<PageInfo<ProjCustomInfoVO>> selectCustomInfoList(ProjCustomInfoDTO projCustomInfoDTO);

    int deleteByPrimaryKey(Long customInfoId);

    int insert(ProjCustomInfo record);

    int insertOrUpdate(ProjCustomInfo record);

    int insertOrUpdateSelective(ProjCustomInfo record);

    int insertSelective(ProjCustomInfo record);

    ProjCustomInfo selectByPrimaryKey(Long customInfoId);

    int updateByPrimaryKeySelective(ProjCustomInfo record);

    int updateByPrimaryKey(ProjCustomInfo record);

    int updateBatch(List<ProjCustomInfo> list);

    int updateBatchSelective(List<ProjCustomInfo> list);

    int batchInsert(List<ProjCustomInfo> list);


    ProjCustomInfo getHospitalInfoByKey(Long customerInfoId);

    /**
     * 保存ip和端口
     *
     * @param projCustomInfoDTO
     * @return
     */
    Result saveIpAndPort(@RequestBody ProjCustomInfoDTO projCustomInfoDTO);

    /**
     * 说明: 定时或手动保存客户基础信息
     *
     * @param customersResps
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/6/23 12:54
     * @remark: Copyright
     */
    Result saveCustomInfoAndCustomInfoDetail(List<YunYingCustomersResp> customersResps);

    /**
     * 查询客户信息, 根据当前登录人的id进行角色过滤
     *
     * @return Result<List < ProjCustomInfoVO>>
     */
    Result<List<ProjCustomInfoVO>> findCustomInfoListByQuery();

    /**
     * 说明: 定时或手动保存客户基础信息
     *
     * @param customersResps
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/6/23 12:54
     * @remark: Copyright
     */
    Result updateCustomInfoAndCustomInfoDetail(List<YunYingCustomersResp> customersResps);

    /**
     * 查询客户信息，===== 项目工具
     *
     * @return
     */
    Result<ProjectToolsOptionsForCustomerInfoTopVO> projectToolsSelectCustomInfoList();


    List<ProjCustomInfo> selectCustomInfoListImpl(Long currentUserId, ProjCustomInfoDTO projCustomInfoDTO);
}
