package com.msun.csm.service.config;

import java.util.List;

import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.ConfigOnlineStepDTO;
import com.msun.csm.model.dto.ConfigOnlineStepSelectDTO;
import com.msun.csm.model.vo.ConfigOnlineStepVO;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/12/10/9:17
 */
public interface ConfigOnlineStepService {


    /**
     * 查询上线步骤配置列表
     *
     * @param dto
     * @return
     */
    Result<List<ConfigOnlineStepVO>> selectConfigOnlineStepList(ConfigOnlineStepSelectDTO dto);

    /**
     * 修改上线步骤配置信息
     *
     * @param dto
     * @return
     */
    Result updateConfigOnlineStep(ConfigOnlineStepDTO dto);

    /**
     * 删除上线步骤配置信息
     *
     * @param configOnlineStepId
     * @return
     */
    Result deleteConfigOnlineStep(Long configOnlineStepId);

    /**
     * 查询上线步骤字典数据
     *
     * @return
     */
    Result<List<BaseIdNameResp>> selectDictOnlineStepData();

}
