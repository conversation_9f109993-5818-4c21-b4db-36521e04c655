package com.msun.csm.service.common;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.enums.projform.CustomTypeEnum;
import com.msun.csm.common.enums.projform.LibFormTypeEnum;
import com.msun.csm.common.enums.projprojectinfo.ProjectDeliverStatusEnums;
import com.msun.csm.common.enums.projprojectinfo.ProjectTypeEnums;
import com.msun.csm.common.enums.projprojectinfo.ProjectUpgradationTypeEnums;
import com.msun.csm.common.model.*;
import com.msun.csm.dao.entity.dict.DictBusinessStatus;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.projreport.ConfigCustomBackendDetailLimit;
import com.msun.csm.dao.mapper.conf.ConfigProductJobMenuDetailMapper;
import com.msun.csm.dao.mapper.dict.DictBusinessStatusMapper;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.dao.mapper.dict.DictProjectPlanStageMapper;
import com.msun.csm.dao.mapper.proj.*;
import com.msun.csm.dao.mapper.report.ConfigCustomBackendDetailLimitMapper;
import com.msun.csm.dao.mapper.sysdept.SysDeptMapper;
import com.msun.csm.dao.mapper.sysrole.SysRoleMapper;
import com.msun.csm.model.dto.ProjProjectMemberDTO;
import com.msun.csm.model.dto.role.SysRoleDTO;
import com.msun.csm.model.vo.ProjProjectMemberVO;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.proj.ProjOrderProductService;
import com.msun.csm.service.proj.ProjProjectInfoService;
import com.msun.csm.util.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/10
 */
@Service
public class BaseQueryServiceImpl implements BaseQueryService {

    @Resource
    private ProjContractCustomInfoMapper contractCustomInfoMapper;
    @Resource
    private ProjCustomInfoMapper customInfoMapper;
    @Resource
    private ProjProjectInfoMapper projectInfoMapper;
    @Resource
    private DictProductMapper dictProductMapper;
    @Resource
    private SysDeptMapper deptMapper;
    @Resource
    private ProjHospitalInfoMapper hospitalInfoMapper;

    @Lazy
    @Resource
    private ProjMilestoneInfoMapper milestoneInfoMapper;

    @Lazy
    @Resource
    private ProjOnlineStepMapper projOnlineStepMapper;

    @Resource
    private ProjOrderProductService orderProductService;

    @Resource
    private UserHelper userHelper;

    @Resource
    private ProjProjectMemberMapper projMemberMapper;

    @Resource
    private SysRoleMapper sysRoleMapper;

    @Lazy
    @Resource
    private ConfigProductJobMenuDetailMapper configProductJobMenuDetailMapper;

    @Lazy
    @Resource
    private ConfigCustomBackendDetailLimitMapper configCustomBackendDetailLimitMapper;

    @Resource
    private DictProjectPlanStageMapper dictProjectPlanStageMapper;


    /**
     * 查询合同客户
     *
     * @param keyword
     * @return
     */
    @Override
    public List<BaseIdNameResp> queryAllContractCustomer(String keyword) {
        if (StringUtils.isNotBlank(keyword)) {
            keyword = keyword.trim();
        }
        return contractCustomInfoMapper.selectByKeyword(keyword);
    }

    /**
     * 基础查询-查询所有客户
     *
     * @param keyword
     * @return
     */
    @Override
    public List<BaseIdNameResp> queryAllCustomer(String keyword) {
        if (StringUtils.isNotBlank(keyword)) {
            keyword = keyword.trim();
        }
        return customInfoMapper.selectByKeyword(keyword);
    }

    /**
     * 查询所有项目
     *
     * @param keyword
     * @param customInfoId
     * @return
     */
    @Override
    public List<BaseIdNameResp> queryProject(String keyword, Long customInfoId) {
        ProjProjectInfo queryParam = new ProjProjectInfo();
        queryParam.setCustomInfoId(customInfoId);
        if (StringUtils.isNotBlank(keyword)) {
            keyword = keyword.trim();
        }
        queryParam.setProjectName(keyword);
        List<ProjProjectInfo> projectInfos = projectInfoMapper.selectByParam(queryParam);
        List<BaseIdNameResp> result = projectInfos.stream().map(item -> new BaseIdNameResp(item.getProjectInfoId(), item.getProjectName() + "-" + item.getProjectNumber() + "(" + (item.getHisFlag() == 1 ? "首期" : "非首期") + ")")).collect(Collectors.toList());
        return result;
    }

    /**
     * @param keyword
     * @param isOnlyDomain
     * @param customInfoId
     * @return
     */
    @Override
    public List<BaseHospitalNameResp> queryHospital(String keyword, Boolean isOnlyDomain, Long customInfoId, Long projectInfoId) {
        List<BaseHospitalNameResp> result = hospitalInfoMapper.selectHospitalInfoListByParamer(isOnlyDomain, customInfoId, projectInfoId);
        return result;
    }

    /**
     * 查询枚举表数据
     *
     * @return
     */
    @Override
    public List<BaseCodeNameResp> queryLibFormType() {
        List<BaseCodeNameResp> list = new ArrayList<>();
        for (LibFormTypeEnum e : LibFormTypeEnum.values()) {
            list.add(new BaseCodeNameResp(e.getCode(), e.getMessage()));
        }
        return list;
    }

    /**
     * 查询产品
     *
     * @param keyword
     * @return
     */
    @Override
    public List<BaseIdNameResp> queryProduct(String keyword) {
        if (StringUtils.isNotBlank(keyword)) {
            keyword = keyword.trim();
        }
        return dictProductMapper.selectByKeyword(keyword);
    }

    /**
     * 查询部门
     *
     * @param keyword
     * @return
     */
    @Override
    public List<BaseIdNameResp> queryDept(String keyword) {
        if (StringUtils.isNotBlank(keyword)) {
            keyword = keyword.trim();
        }
        return deptMapper.selectByKeyword(keyword);
    }

    /**
     * 查询项目类型-单体/区域
     *
     * @return
     */
    @Override
    public List<BaseIdNameResp> getProjectType() {
        List<BaseIdNameResp> projectTypeList = ProjectTypeEnums.getBaseDataList();
        //医共体需要删除
        projectTypeList.removeIf(item -> item.getId().equals(3L));
        projectTypeList.removeIf(item -> item.getId().equals(4L));
        return projectTypeList;
    }

    /**
     * 查询项目升级方式-老换新/新客户
     *
     * @return
     */
    @Override
    public List<BaseIdNameResp> getProjectUpgradation() {
        List<BaseIdNameResp> upgradationTypeList = ProjectUpgradationTypeEnums.getBaseDataList();
        return upgradationTypeList;
    }

    /**
     * 查询项目验收超时天数
     *
     * @param projectInfoId
     * @param hospitalInfoId
     * @param limitType
     * @return
     */
    @Override
    public Integer queryAcceptanceExceedTimeLimit(Long projectInfoId, Long hospitalInfoId, Integer limitType) {
        // 默认不限制 0 / 1 满足限制
        Integer result = 0;
        // 默认查询三方接口限制
        if (limitType == null || limitType == 0) {
            result = projectInfoMapper.queryAcceptanceExceedTimeLimit(projectInfoId, hospitalInfoId, "interfacelimit");
        } else if (limitType == 2) {
            result = projectInfoMapper.queryAcceptanceExceedTimeLimit(projectInfoId, hospitalInfoId, "reportlimit");
        }
        return result;
    }

    /**
     * 查询里程碑数据
     *
     * @param projectInfoId
     * @return
     */
    @Override
    public List<BaseIdNameExtendResp> queryProjectMilestone(Long projectInfoId) {
        return milestoneInfoMapper.queryProjectMilestone(projectInfoId);
    }

    /**
     * @param projectInfoId
     * @return
     */
    @Override
    public List<BaseIdNameExtendResp> queryProjectOnlineStep(Long projectInfoId) {
        return projOnlineStepMapper.queryProjectOnlineStep(projectInfoId);
    }

    /**
     * 根据项目id查询项目信息
     *
     * @param projectInfoId
     * @return
     */
    @Override
    public ProjProjectInfo getProjectByProjectInfoId(Long projectInfoId) {
        return projectInfoMapper.selectById(projectInfoId);
    }

    /**
     * 查询首期项目
     *
     * @param customInfoId
     * @return
     */
    @Override
    public List<BaseIdNameResp> queryFirstProject(Long customInfoId) {
        ProjProjectInfo queryParam = new ProjProjectInfo();
        queryParam.setCustomInfoId(customInfoId);
        queryParam.setHisFlag(1);
        List<ProjProjectInfo> projectInfos = projectInfoMapper.selectByParam(queryParam);
        List<BaseIdNameResp> result = new ArrayList<>();
        if (projectInfos != null && projectInfos.size() > 0) {
            for (ProjProjectInfo e : projectInfos) {
                if (e.getHisFlag() == 1) {
                    String projectNumber = e.getProjectType() == 2 ? "[区域]" : "[单体]";
                    String projectName = e.getProjectName() + "-" + projectNumber + "(" + (e.getHisFlag() == 1 ? "首期" : "非首期") + ")";
                    result.add(new BaseIdNameResp(e.getProjectInfoId(), projectName));
                }
            }
        }
        return result;
    }


    /**
     * @return
     */
    @Override
    public List<BaseIdNameResp> queryCustomType() {
        List<BaseIdNameResp> result = new ArrayList<>();
        for (CustomTypeEnum e : CustomTypeEnum.values()) {
            result.add(new BaseIdNameResp(e.getCode(), e.getMessage()));
        }
        return result;
    }

    /**
     * @return
     */
    @Override
    public List<BaseIdNameResp> queryProjectProgress() {
        List<BaseIdNameResp> result = new ArrayList<>();
        for (ProjectDeliverStatusEnums e : ProjectDeliverStatusEnums.values()) {
            result.add(new BaseIdNameResp(Long.valueOf(e.getCode()), e.getName()));
        }
        return result;
    }

    /**
     * @param deptType
     * @return
     */
    @Override
    public List<BaseIdNameResp> queryDeptByParamer(Integer deptType) {
        if (deptType == null || deptType == 0) {
            deptType = 1;
        }
        return deptMapper.queryDeptByParamer(deptType);
    }

    @Override
    public List<BaseProjectInfoResp> queryBackendProjectInfo(Long customInfoId) {
        ProjProjectInfo queryParam = new ProjProjectInfo();
        queryParam.setCustomInfoId(customInfoId);
        List<ProjProjectInfo> projectInfos = projectInfoMapper.selectByParam(queryParam);
        return projectInfos.stream().map(item -> {
            List<String> productNames = orderProductService.getProductName(item.getProjectInfoId());
            return new BaseProjectInfoResp(String.valueOf(item.getProjectInfoId()), item.getProjectNumber() + "(" + (item.getHisFlag() == 1 ? "首期" : "非首期") + ")", StringUtils.join(productNames, ","));
        }).collect(Collectors.toList());
    }

    /**
     * 查询当前账号的角色信息--前后端人员
     *
     * @param projectInfoId
     * @return
     */
    @Override
    public Integer getUserRoleType(Long projectInfoId) {
        SysUserVO currentUser = userHelper.getCurrentUser();
        ProjProjectMemberDTO memberDTO = new ProjProjectMemberDTO();
        memberDTO.setProjectInfoId(projectInfoId);
        memberDTO.setProjectMemberId(currentUser.getSysUserId());
        List<ProjProjectMemberVO> members = projMemberMapper.selectMemberVO(memberDTO);
        //如果当前账号在项目组中，则返回在项目组中的角色
        if (ObjectUtil.isNotEmpty(members)) {
            return members.get(0).getRoleType();
        }
        // 不在项目组中，查询是否有系统管理员的角色
        List<SysRoleDTO> currentUserRoleList = sysRoleMapper.selectRoleByUserId(currentUser.getSysUserId());
        // 当前登录用户的角色编码
        List<String> currentUserRoleCodeSet = currentUserRoleList.stream().map(SysRoleDTO::getRoleCode)
                .collect(Collectors.toList());
        //不在项目组中且不是系统管理员，则默认是前端人员
        if (!currentUserRoleCodeSet.contains("1")) {
            return 1;
        }
        return 0;
    }

    /**
     * 查询项目待办类型
     *
     * @param projectInfoId
     * @return
     */
    @Override
    public List<BaseIdNameResp> queryProjectTodoTypeData(Long projectInfoId) {
        List<BaseIdNameResp> list = new ArrayList<>();
        list.add(new BaseIdNameResp(0L, "产品总体状态"));
        list.addAll(configProductJobMenuDetailMapper.selectProductMenuDetail(projectInfoId));

        // 自定义顺序
        Map<String, Integer> sortOrder = new HashMap<>();
        sortOrder.put("产品总体状态", 0);
        sortOrder.put("基础数据", 1);
        sortOrder.put("配置", 2);
        sortOrder.put("待处理任务", 3);
        sortOrder.put("打印报表", 4);
        sortOrder.put("表单", 5);

        list.sort(Comparator.comparingInt(item -> sortOrder.getOrDefault(item.getName(), Integer.MAX_VALUE)));

        return list;
    }

    /**
     * * 查询项目是业务线否开启审核
     * 限制类型 1开启医护打印验证流程、2打印报表、3云护理表单、4手麻表单、5重症表单、6急诊表单、7三方接口、8医保接口、9统计报表/10 产品业务调研  11 打印报表 12 表单
     *
     * @param projectInfoId
     * @param openType
     * @return
     */
    @Override
    public ConfigCustomBackendDetailLimit isOpenAuditorFlag(Long projectInfoId, Integer openType) {
        ConfigCustomBackendDetailLimit configCustomBackendDetailLimit = new ConfigCustomBackendDetailLimit();
        List<ConfigCustomBackendDetailLimit> list = configCustomBackendDetailLimitMapper.selectList(new QueryWrapper<ConfigCustomBackendDetailLimit>().eq("is_deleted", 0).eq("project_info_id", projectInfoId).eq("open_type", openType));
        if (list != null && list.size() > 0) {
            configCustomBackendDetailLimit = list.get(0);
        } else {
            configCustomBackendDetailLimit.setOpenFlag(0);
        }
        return configCustomBackendDetailLimit;
    }

    @Resource
    private DictBusinessStatusMapper dictBusinessStatusMapper;

    @Resource
    private ProjProjectInfoService projectInfoService;

    @Override
    public List<BaseIdCodeNameResp> getBusinessStatusList(String businessCode, Long projectInfoId) {
        List<DictBusinessStatus> dictBusinessStatuses = dictBusinessStatusMapper.selectList(
                new QueryWrapper<DictBusinessStatus>()
                        .eq("is_deleted", 0)
                        .eq("business_code", businessCode)
                        .orderByAsc("sort_no")
        );
        // 产品业务调研
        if ("surveyPlan".equals(businessCode)) {
            boolean openSurveyAudit = projectInfoService.isOpenSurveyAudit(projectInfoId);
            if (openSurveyAudit) {
                // 开启后端审核，返回全部状态
                return dictBusinessStatuses.stream().map(item -> new BaseIdCodeNameResp(String.valueOf(item.getStatusId()), item.getStatusCode(), item.getStatusDescription())).collect(Collectors.toList());
            }
            List<Integer> common = Arrays.asList(0, 1, 2, 3);
            return dictBusinessStatuses.stream().filter(item -> common.contains(item.getStatusId())).map(item -> new BaseIdCodeNameResp(String.valueOf(item.getStatusId()), item.getStatusCode(), item.getStatusDescription())).collect(Collectors.toList());
        }
        return dictBusinessStatuses.stream().map(item -> new BaseIdCodeNameResp(String.valueOf(item.getStatusId()), item.getStatusCode(), item.getStatusDescription())).collect(Collectors.toList());
    }


    /**
     * 查询项目计划阶段字典
     * @return
     */
    @Override
    public List<BaseCodeNameResp> getAllPlanStage() {
        return dictProjectPlanStageMapper.getAllPlanStage();
    }
}
