package com.msun.csm.service.report;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.msun.csm.util.PageHelperUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.msun.csm.common.model.BaseCodeNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.projreport.ProjHospitalTerminalConfig;
import com.msun.csm.dao.mapper.report.ProjHospitalTerminalConfigMapper;
import com.msun.csm.model.req.projreport.ProjHospitalTerminalConfigExportReq;
import com.msun.csm.model.req.projreport.ProjHospitalTerminalConfigPageReq;
import com.msun.csm.model.req.projreport.ProjHospitalTerminalConfigReq;
import com.msun.csm.model.req.projreport.ProjHospitalTerminalConfigSaveReq;
import com.msun.csm.model.resp.projreport.ProjHospitalTerminalConfigExportResp;
import com.msun.csm.model.resp.projreport.ProjHospitalTerminalConfigPageResp;
import com.msun.csm.model.resp.projreport.ProjHospitalTerminalConfigResp;
import com.msun.csm.util.SnowFlakeUtil;

import cn.hutool.core.bean.BeanUtil;

/**
 * @Description:
 * @Author: zd
 * @Date: 2024/11/7
 */

@Service
public class ProjHospitalTerminalConfigServiceImpl extends ServiceImpl<ProjHospitalTerminalConfigMapper, ProjHospitalTerminalConfig> implements ProjHospitalTerminalConfigService {

    @Resource
    private ProjHospitalTerminalConfigMapper projHospitalTerminalConfigMapper;

    /**
     * 保存医院电脑配置数据
     *
     * @param saveData
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result saveHospitalTerminalConfigData(ProjHospitalTerminalConfigSaveReq saveData) {
        List<ProjHospitalTerminalConfig> list = new ArrayList<>(10);
        if (saveData.getSaveData() == null) {
            return Result.fail("拉取医院电脑配置为空");
        }
        if (saveData.getCustomInfoId() == null) {
            return Result.fail("客户id为空");
        }
        if (saveData.getProjectInfoId() == null) {
            return Result.fail("项目id为空");
        }
        projHospitalTerminalConfigMapper.delete(new LambdaQueryWrapper<ProjHospitalTerminalConfig>().eq(ProjHospitalTerminalConfig::getProjectInfoId, saveData.getProjectInfoId()));
        for (ProjHospitalTerminalConfigReq req : saveData.getSaveData()) {
            ProjHospitalTerminalConfig config = new ProjHospitalTerminalConfig();
            BeanUtil.copyProperties(req, config);
            config.setProjectInfoId(saveData.getProjectInfoId());
            config.setCustomInfoId(saveData.getCustomInfoId());
            config.setHospitalTerminalConfigId(SnowFlakeUtil.getId());
            list.add(config);
        }
        this.saveBatch(list);
        return Result.success();
    }

    /**
     * @param dto
     * @return
     */
    @Override
    public Result<ProjHospitalTerminalConfigPageResp<ProjHospitalTerminalConfigResp>> findDataInfoPage(ProjHospitalTerminalConfigPageReq dto) {
        List<ProjHospitalTerminalConfigResp> list = PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> projHospitalTerminalConfigMapper.findDataInfoPage(dto));
        ProjHospitalTerminalConfigPageResp<ProjHospitalTerminalConfigResp> pageResp = new ProjHospitalTerminalConfigPageResp<>(list);
        List<ProjHospitalTerminalConfig> valList = projHospitalTerminalConfigMapper.selectList(new LambdaQueryWrapper<ProjHospitalTerminalConfig>().eq(ProjHospitalTerminalConfig::getProjectInfoId, dto.getProjectInfoId()));
        if (valList != null && valList.size() > 0) {
            Set<String> hospitalNameSet = valList.stream().map(ProjHospitalTerminalConfig::getHospitalName).collect(Collectors.toSet());
            List<BaseCodeNameResp> hospitalList = new ArrayList<>(hospitalNameSet.size());
            hospitalNameSet.stream().forEach(item -> {
                BaseCodeNameResp hospital = new BaseCodeNameResp();
                hospital.setId(item);
                hospital.setName(item);
                hospitalList.add(hospital);
            });
            pageResp.setHospitalList(hospitalList);
            List<BaseCodeNameResp> coperatSystemList = new ArrayList<>(10);
            Set<String> coperatSystemSet = valList.stream().map(ProjHospitalTerminalConfig::getOperatSystem).collect(Collectors.toSet());
            coperatSystemSet.stream().forEach(item -> {
                BaseCodeNameResp coperatSystem = new BaseCodeNameResp();
                coperatSystem.setId(item);
                coperatSystem.setName(item);
                coperatSystemList.add(coperatSystem);
            });
            pageResp.setCoperatSystemList(coperatSystemList);
            List<BaseCodeNameResp> computerNumberList = new ArrayList<>(10);
            Set<String> computerNumberSet = valList.stream().map(ProjHospitalTerminalConfig::getComputerNumber).collect(Collectors.toSet());
            computerNumberSet.stream().forEach(item -> {
                BaseCodeNameResp computerNumber = new BaseCodeNameResp();
                computerNumber.setId(item);
                computerNumber.setName(item);
                computerNumberList.add(computerNumber);
            });
            pageResp.setComputerNumberList(computerNumberList);
            List<BaseCodeNameResp> configMeetList = new ArrayList<>(10);
            Set<String> configMeetSet = valList.stream().map(ProjHospitalTerminalConfig::getConfigMeetFlag).collect(Collectors.toSet());
            configMeetSet.stream().forEach(item -> {
                BaseCodeNameResp configMeet = new BaseCodeNameResp();
                configMeet.setId(item);
                configMeet.setName(item);
                configMeetList.add(configMeet);
            });
            pageResp.setConfigMeetList(configMeetList);
        }
        return Result.success(pageResp);
    }

    /**
     * 导出数据
     *
     * @param unCheckDTO
     * @param response
     */
    @Override
    public void exportHospitalFile(ProjHospitalTerminalConfigExportReq unCheckDTO, HttpServletResponse response) {
        ProjHospitalTerminalConfigPageReq dto = new ProjHospitalTerminalConfigPageReq();
        dto.setProjectInfoId(unCheckDTO.getProjectInfoId());
        List<ProjHospitalTerminalConfigResp> list = projHospitalTerminalConfigMapper.findDataInfoPage(dto);
        String fileName = "医院电脑检测数据导出";
        // 查询项目名称
        try {
            List<ProjHospitalTerminalConfigExportResp> liste = new ArrayList<>();
            if (list != null && !list.isEmpty()) {
                for (ProjHospitalTerminalConfigResp item : list) {
                    ProjHospitalTerminalConfigExportResp exportResp = new ProjHospitalTerminalConfigExportResp();
                    BeanUtil.copyProperties(item, exportResp);
                    liste.add(exportResp);
                }
            }
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            // 这里 URLEncoder.encode 防止中文乱码
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=" + encodedFileName + ".xlsx");

            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream(),
                    ProjHospitalTerminalConfigExportResp.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet("Sheet1").build();
            excelWriter.write(liste, writeSheet);
            excelWriter.finish();
        } catch (Exception e) {
            log.error("医院电脑检测数据导出失败", e);
        }
    }
}
