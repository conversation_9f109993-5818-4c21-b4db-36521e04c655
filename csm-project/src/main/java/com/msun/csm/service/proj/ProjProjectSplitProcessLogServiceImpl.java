package com.msun.csm.service.proj;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.msun.csm.dao.entity.proj.ProjProjectSplitProcessLog;
import com.msun.csm.dao.mapper.proj.ProjProjectSplitProcessLogMapper;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/5/23
 */

@Service
public class ProjProjectSplitProcessLogServiceImpl implements ProjProjectSplitProcessLogService {

    @Autowired
    private ProjProjectSplitProcessLogMapper projProjectSplitProcessLogMapper;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return projProjectSplitProcessLogMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(ProjProjectSplitProcessLog record) {
        return projProjectSplitProcessLogMapper.insert(record);
    }

    @Override
    public int insertOrUpdate(ProjProjectSplitProcessLog record) {
        return projProjectSplitProcessLogMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(ProjProjectSplitProcessLog record) {
        return projProjectSplitProcessLogMapper.insertOrUpdateSelective(record);
    }

    @Override
    public int insertSelective(ProjProjectSplitProcessLog record) {
        return projProjectSplitProcessLogMapper.insertSelective(record);
    }

    @Override
    public ProjProjectSplitProcessLog selectByPrimaryKey(Long id) {
        return projProjectSplitProcessLogMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(ProjProjectSplitProcessLog record) {
        return projProjectSplitProcessLogMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ProjProjectSplitProcessLog record) {
        return projProjectSplitProcessLogMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<ProjProjectSplitProcessLog> list) {
        return projProjectSplitProcessLogMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<ProjProjectSplitProcessLog> list) {
        return projProjectSplitProcessLogMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<ProjProjectSplitProcessLog> list) {
        return projProjectSplitProcessLogMapper.batchInsert(list);
    }
}
