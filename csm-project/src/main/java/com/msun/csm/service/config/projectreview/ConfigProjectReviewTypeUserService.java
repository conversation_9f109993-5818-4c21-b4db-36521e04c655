package com.msun.csm.service.config.projectreview;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.projectreview.ConfigProjectReviewTypeUser;
import com.msun.csm.model.req.projectreview.ConfigProjectReviewTypeUserReq;
import com.msun.csm.model.req.projectreview.ConfigProjectReviewTypeUserSaveReq;
import com.msun.csm.model.req.projectreview.QueryInfoReq;
import com.msun.csm.model.resp.projectreview.ConfigProjectReviewTypeUserResp;
import com.msun.csm.model.resp.projectreview.UserModelAllResp;

/**
 * <AUTHOR>
 * @description 针对表【config_project_review_type_user(项目审核类型对应人员配置表)】的数据库操作Service
 * @createDate 2025-06-18 08:30:31
 */
public interface ConfigProjectReviewTypeUserService extends IService<ConfigProjectReviewTypeUser> {

    /**
     * 查询项目审核类型对应人员配置表分页数据
     *
     * @param dto
     * @return
     */
    Result<PageInfo<ConfigProjectReviewTypeUserResp>> findDataPage(ConfigProjectReviewTypeUserReq dto);

    /**
     * 项目审核人员配置表删除
     *
     * @param dto
     * @return
     */
    Result<String> delData(ConfigProjectReviewTypeUserReq dto);

    /**
     * 项目审核人员配置表保存修改
     *
     * @param dto
     * @return
     */
    Result<String> saveData(ConfigProjectReviewTypeUserSaveReq dto);

    /**
     * 查询审核人集合
     *
     * @param dto
     * @return
     */
    UserModelAllResp findUserModel(QueryInfoReq dto);

}
