package com.msun.csm.service.dict;

import java.util.List;

import com.msun.csm.dao.entity.dict.DictCustomerClass;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/25
 */

public interface DictCustomerClassService {

    int deleteByPrimaryKey(Long id);

    int insert(DictCustomerClass record);

    int insertOrUpdate(DictCustomerClass record);

    int insertOrUpdateSelective(DictCustomerClass record);

    int insertSelective(DictCustomerClass record);

    DictCustomerClass selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DictCustomerClass record);

    int updateByPrimaryKey(DictCustomerClass record);

    int updateBatch(List<DictCustomerClass> list);

    int updateBatchSelective(List<DictCustomerClass> list);

    int batchInsert(List<DictCustomerClass> list);
}
