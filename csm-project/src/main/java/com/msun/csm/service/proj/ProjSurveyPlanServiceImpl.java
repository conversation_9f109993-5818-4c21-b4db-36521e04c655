package com.msun.csm.service.proj;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.OutputStream;
import java.lang.reflect.Type;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.msun.core.commons.id.IdGenerator;
import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.enums.MilestoneNodeEnum;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.ProductEquipSurveyMenuEnum;
import com.msun.csm.common.enums.ProjectPlanStatusEnum;
import com.msun.csm.common.enums.ResultEnum;
import com.msun.csm.common.enums.WorkTypeEnum;
import com.msun.csm.common.enums.message.MsgToCategory;
import com.msun.csm.common.enums.projform.FormSourceType;
import com.msun.csm.common.enums.projresearch.ResearchStatus;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.SimpleId;
import com.msun.csm.dao.entity.SysConfig;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.comm.HzznDeliver;
import com.msun.csm.dao.entity.config.ConfigProductJobMenuDetail;
import com.msun.csm.dao.entity.dict.DictProduct;
import com.msun.csm.dao.entity.dict.DictProductExtend;
import com.msun.csm.dao.entity.dict.DictProductVsModules;
import com.msun.csm.dao.entity.proj.ProductInfo;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalOnlineDetail;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjMilestoneTask;
import com.msun.csm.dao.entity.proj.ProjMilestoneTaskDetail;
import com.msun.csm.dao.entity.proj.ProjProductBacklog;
import com.msun.csm.dao.entity.proj.ProjProductConfig;
import com.msun.csm.dao.entity.proj.ProjProductConfigLog;
import com.msun.csm.dao.entity.proj.ProjProductDeliverRecord;
import com.msun.csm.dao.entity.proj.ProjProjectFile;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectMember;
import com.msun.csm.dao.entity.proj.ProjProjectPlan;
import com.msun.csm.dao.entity.proj.ProjProjectReviewRecord;
import com.msun.csm.dao.entity.proj.ProjSurveyPlan;
import com.msun.csm.dao.entity.proj.ProjTipRecord;
import com.msun.csm.dao.entity.proj.extend.ProjProductConfigExtend;
import com.msun.csm.dao.entity.proj.extend.ProjProductTaskExtend;
import com.msun.csm.dao.entity.proj.extend.ProjSurveyPlanExtend;
import com.msun.csm.dao.entity.proj.producttask.ProjProductTask;
import com.msun.csm.dao.entity.proj.producttask.ProjProductTaskFileRelation;
import com.msun.csm.dao.entity.proj.projform.ProjSurveyForm;
import com.msun.csm.dao.entity.proj.projreport.ConfigCustomBackendDetailLimit;
import com.msun.csm.dao.entity.proj.projreport.ProjBusinessExamineLog;
import com.msun.csm.dao.entity.proj.projreport.ProjSurveyReport;
import com.msun.csm.dao.entity.tduck.FmUserForm;
import com.msun.csm.dao.entity.tduck.FmUserFormData;
import com.msun.csm.dao.entity.tduck.FmUserFormDataVO;
import com.msun.csm.dao.entity.tmp.TmpProjectNewVsOld;
import com.msun.csm.dao.mapper.comm.ProjBusinessExamineLogMapper;
import com.msun.csm.dao.mapper.conf.ConfigProductJobMenuDetailMapper;
import com.msun.csm.dao.mapper.config.SysConfigMapper;
import com.msun.csm.dao.mapper.dict.DictProductExtendMapper;
import com.msun.csm.dao.mapper.dict.DictProductMapper;
import com.msun.csm.dao.mapper.dict.DictProductVsModulesMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalOnlineDetailMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneTaskDetailMapper;
import com.msun.csm.dao.mapper.proj.ProjMilestoneTaskMapper;
import com.msun.csm.dao.mapper.proj.ProjProductBacklogMapper;
import com.msun.csm.dao.mapper.proj.ProjProductConfigLogMapper;
import com.msun.csm.dao.mapper.proj.ProjProductConfigMapper;
import com.msun.csm.dao.mapper.proj.ProjProductDeliverRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjProductTaskFileRelationMapper;
import com.msun.csm.dao.mapper.proj.ProjProductTaskMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectFileMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectReviewRecordMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyPlanMapper;
import com.msun.csm.dao.mapper.proj.ProjSurveyReportMapper;
import com.msun.csm.dao.mapper.proj.ProjTipRecordMapper;
import com.msun.csm.dao.mapper.projform.ProjSurveyFormMapper;
import com.msun.csm.dao.mapper.report.ConfigCustomBackendDetailLimitMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.dao.mapper.tduck.FmUserFormDataMapper;
import com.msun.csm.dao.mapper.tduck.FmUserFormMapper;
import com.msun.csm.dao.mapper.tmp.TmpProjectNewVsOldMapper;
import com.msun.csm.model.dto.FormDataDTO;
import com.msun.csm.model.dto.GetProductJobMenuDetailParam;
import com.msun.csm.model.dto.HospitalInfoDTO;
import com.msun.csm.model.dto.MilestoneInfoDTO;
import com.msun.csm.model.dto.ProductInfoDTO;
import com.msun.csm.model.dto.ProjProductBacklogDTO;
import com.msun.csm.model.dto.ProjSurveyPlanDTO;
import com.msun.csm.model.dto.ResearchPlanDTO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.dto.SurveyPlanAuditLog;
import com.msun.csm.model.dto.UpdateMilestoneDTO;
import com.msun.csm.model.param.ConfirmDataParam;
import com.msun.csm.model.param.ConfirmFinalDataParam;
import com.msun.csm.model.param.CopySurveyResultParam;
import com.msun.csm.model.param.CreateTduckFormParam;
import com.msun.csm.model.param.DifferenceProductParam;
import com.msun.csm.model.param.MessageParam;
import com.msun.csm.model.param.ProductIdAndDeptNameParam;
import com.msun.csm.model.param.SendMessageParam;
import com.msun.csm.model.param.SurveyPlanResultParam;
import com.msun.csm.model.param.TduckCopySurveyResultParam;
import com.msun.csm.model.req.projform.ProjSurveyFormAddReq;
import com.msun.csm.model.req.projform.ProjSurveyFormUpdateReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportDeleteReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportUpdateReq;
import com.msun.csm.model.req.surveyplan.AddSurveyPlanReq;
import com.msun.csm.model.req.surveyplan.UpdateSurveyPlanReq;
import com.msun.csm.model.req.todotask.SaveOrUpdateTodoTaskParam;
import com.msun.csm.model.resp.projreport.ProjBusinessExamineLogResp;
import com.msun.csm.model.resp.surveyplan.DifferenceProductVO;
import com.msun.csm.model.resp.surveyplan.SurveyPlanResult;
import com.msun.csm.model.resp.surveyplan.SurveyPlanTaskResp;
import com.msun.csm.model.tduck.req.SaveBackLogAndDetailReq;
import com.msun.csm.model.tduck.req.TDuckConfig;
import com.msun.csm.model.tduck.req.TDuckForm;
import com.msun.csm.model.tduck.req.TDuckReport;
import com.msun.csm.model.tduck.req.TDuckTask;
import com.msun.csm.model.tduck.req.UpdateSurveyPlanStatusReq;
import com.msun.csm.model.vo.ConfigProductJobMenuDetailVO;
import com.msun.csm.model.vo.ProductBacklogParam;
import com.msun.csm.model.vo.ProductBacklogUrlVO;
import com.msun.csm.model.vo.ProductSurveyPlanMessageVO;
import com.msun.csm.model.vo.ProjProductBacklogVO;
import com.msun.csm.model.vo.surveyplan.SurveyPlanHospitalProductVO;
import com.msun.csm.model.vo.surveyplan.SurveyPlanInitVO;
import com.msun.csm.model.vo.surveyplan.SurveyPlanProductVO;
import com.msun.csm.model.vo.surveyplan.SurveyPlanTaskRespVO;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.common.HzzDeliverPublicService;
import com.msun.csm.service.common.HzznDeliverService;
import com.msun.csm.service.config.ProjMessageInfoService;
import com.msun.csm.service.dict.DictBusinessStatusService;
import com.msun.csm.service.dict.DictProductService;
import com.msun.csm.service.dict.DictProductVsDeliverService;
import com.msun.csm.service.message.SendMessageService;
import com.msun.csm.service.proj.projform.ProjSurveyFormService;
import com.msun.csm.service.tduck.FmUserFormService;
import com.msun.csm.service.tduck.TduckService;
import com.msun.csm.util.ListUtils;
import com.msun.csm.util.RedisUtil;
import com.msun.csm.util.SignUtil;
import com.msun.csm.util.SnowFlakeUtil;

@Slf4j
@Service
public class ProjSurveyPlanServiceImpl implements ProjSurveyPlanService {
    /**
     * 各产品准备工作
     */
    private static final String PREPARAT_PRODUCT = "preparat_product";
    @Resource
    private ProjSurveyPlanMapper projSurveyPlanMapper;
    @Resource
    private ProjProjectInfoMapper projectInfoMapper;
    @Resource
    private ProjProductDeliverRecordMapper deliverRecordMapper;
    @Resource
    private DictProductMapper dictProductMapper;
    @Resource
    private UserHelper userHelper;
    @Resource
    private FmUserFormMapper fmUserFormMapper;
    @Resource
    private TduckService tduckService;
    @Value("${tduck.domain}")
    private String tduckDomain;
    @Resource
    private ProjProductBacklogMapper productBacklogMapper;
    @Resource
    private ProjProductConfigMapper productConfigMapper;
    @Resource
    private ProjProductConfigLogMapper productConfigLogMapper;
    @Resource
    private ProjProductTaskMapper productTaskMapper;

    @Resource
    private DictProductVsModulesMapper productVsModulesMapper;
    @Resource
    private ConfigProductJobMenuDetailMapper configProductJobMenuDetailMapper;
    @Resource
    private ProjMilestoneTaskMapper projMilestoneTaskMapper;
    @Resource
    private ProjMilestoneTaskDetailMapper projMilestoneTaskDetailMapper;
    @Resource
    private ProjMilestoneInfoMapper projMilestoneInfoMapper;
    @Resource
    @Lazy
    private ProjMilestoneInfoService milestoneInfoService;
    @Resource
    private ProjProductDeliverRecordMapper projProductDeliverRecordMapper;
    @Resource
    private SysUserMapper sysUserMapper;
    @Lazy
    @Resource
    private ProjProjectInfoService projectInfoService;
    @Resource
    private FmUserFormDataMapper fmUserFormDataMapper;
    @Resource
    private ProjProductBacklogService productBacklogService;
    @Resource
    private DictProductExtendMapper dictProductExtendMapper;
    @Resource
    private HzznDeliverService hzznDeliverService;
    @Resource
    private TmpProjectNewVsOldMapper tmpProjectNewVsOldMapper;
    @Resource
    private ProjHospitalInfoMapper projHospitalInfoMapper;
    @Resource
    private SendMessageService sendMessageService;
    @Resource
    private ProjProductTaskFileRelationMapper projProductTaskFileRelationMapper;
    @Resource
    private ProjProjectFileMapper projProjectFileMapper;
    @Value("${project.current.url}")
    private String platformUrl;
    @Lazy
    @Resource
    private ProjSurveyReportService projSurveyReportService;
    @Resource
    private FmUserFormService fmUserFormService;
    @Resource
    private DictProductService dictProductService;
    @Resource
    private DictProductVsModulesMapper dictProductVsModulesMapper;

    @Resource
    private ProjSurveyFormService projSurveyFormService;

    @Resource
    private HzzDeliverPublicService hzzDeliverPublicService;

    @Resource
    private ProjSurveyReportMapper projSurveyReportMapper;

    @Resource
    private DictProductVsDeliverService dictProductVsDeliverService;

    @Resource
    private ProjSurveyFormMapper projSurveyFormMapper;

    @Resource
    private ProjBusinessExamineLogMapper projBusinessExamineLogMapper;

    @Resource
    private ProjBusinessExamineLogService projBusinessExamineLogService;

    @Lazy
    @Resource
    private RedisUtil redisUtil;

    @Resource
    private ProjTipRecordMapper tipRecordMapper;

    @Lazy
    @Resource
    private ProjOrderProductService orderProductService;

    @Lazy
    @Resource
    private ProjHospitalOnlineDetailMapper projHospitalOnlineDetailMapper;

    @Value("${project.current.qyWeChat-Auth-url}")
    private String weChatAuthUrl;

    @Lazy
    @Resource
    private ProjMessageInfoService messageInfoService;
    @Resource
    private ProjTodoTaskService todoTaskService;

    @Resource
    private ProjProjectPlanService projProjectPlanService;

    @Lazy
    @Resource
    private ProjProjectReviewRecordMapper projProjectReviewRecordMapper;

    @Resource
    private DictBusinessStatusService dictBusinessStatusService;

    @Resource
    private ProjProjectConfigService projectConfigService;

    //处理数据
    private void constructData(SurveyPlanTaskResp task, SurveyPlanTaskResp taskResp, SysUserVO currentUser, boolean isProjectLeader, List<Long> notSurveyProductIds) {
        task.setCompleteStatus(taskResp.getCompleteStatus());
        task.setCompletionProgress(dictBusinessStatusService.getSurveyPlanStatusDescriptionByStatusId(taskResp.getCompleteStatus()));
        // 判断父级的产品是否需要分配任务。部分产品不需要进行调研
        // 查询哪些产品不需要调研 ， 在下方需要把不需要调研的产品过滤出来展示在前端
        if (notSurveyProductIds.contains(task.getYyProductId())) {
            task.setSurveyFlag(0);
            task.setCompleteStatus(ResearchStatus.FINISHED.getCode());
            task.setCompletionProgress(dictBusinessStatusService.getSurveyPlanStatusDescriptionByStatusId(ResearchStatus.FINISHED.getCode()));
        } else {
            task.setSurveyFlag(1);
        }
        task.setHospitalInfoName(taskResp.getHospitalInfoName());
        task.setHospitalInfoId(taskResp.getHospitalInfoId());
        task.setSurveyUserName(taskResp.getSurveyUserName());
        task.setDeptName(taskResp.getDeptName());
        task.setSurveyPlanId(taskResp.getSurveyPlanId());
        task.setSurveyUserId(taskResp.getSurveyUserId());
        task.setPlanCompleteTime(taskResp.getPlanCompleteTime());
        task.setActualCompTime(taskResp.getActualCompTime());
        task.setCanEdit(isProjectLeader);
        task.setCanSurvey(currentUser.getSysUserId().equals(task.getSurveyUserId()));
        task.setProjectInfoId(taskResp.getProjectInfoId());
        task.setOrderNo(taskResp.getOrderNo());
        task.setAuditSysUserId(taskResp.getAuditSysUserId());
        task.setPlanAuditTime(taskResp.getPlanAuditTime());
        task.setActualAuditTime(taskResp.getActualAuditTime());
        task.setAuditUserName(taskResp.getAuditUserName());
        task.setRejectReason(taskResp.getRejectReason());
        task.setPlanAuditTimeStr(taskResp.getPlanAuditTimeStr());
        task.setPlanId(taskResp.getPlanId());

    }

    @Override
    public int deleteByPrimaryKey(Long surveyPlanId) {
        return projSurveyPlanMapper.deleteByPrimaryKey(surveyPlanId);
    }

    @Override
    public int insert(ProjSurveyPlan record) {
        return projSurveyPlanMapper.insert(record);
    }

    @Override
    public int insertOrUpdate(ProjSurveyPlan record) {
        return projSurveyPlanMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(ProjSurveyPlan record) {
        return projSurveyPlanMapper.insertOrUpdateSelective(record);
    }

    @Override
    public int insertSelective(ProjSurveyPlan record) {
        return projSurveyPlanMapper.insertSelective(record);
    }

    @Override
    public ProjSurveyPlan selectByPrimaryKey(Long surveyPlanId) {
        return projSurveyPlanMapper.selectByPrimaryKey(surveyPlanId);
    }

    @Override
    public int updateByPrimaryKeySelective(ProjSurveyPlan record) {
        return projSurveyPlanMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ProjSurveyPlan record) {
        return projSurveyPlanMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<ProjSurveyPlan> list) {
        return projSurveyPlanMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<ProjSurveyPlan> list) {
        return projSurveyPlanMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<ProjSurveyPlan> list) {
        return projSurveyPlanMapper.batchInsert(list);
    }

    /**
     * 查询调研计划关联信息
     *
     * @param dto
     * @return
     */
    @Override
    public Result<SurveyPlanTaskRespVO> findSurveyPlanTask(ProjSurveyPlanDTO dto) {
        SurveyPlanTaskRespVO surveyPlanTaskRespVO = new SurveyPlanTaskRespVO();
        boolean openSurveyAudit = projectInfoService.isOpenSurveyAudit(dto.getProjectInfoId());
        surveyPlanTaskRespVO.setOpenSurveyAudit(openSurveyAudit);
        //判断里程碑节点有没有完成
        surveyPlanTaskRespVO.setConfirmComplete(!milestoneInfoService.verifyMilestoneStatus(dto.getMilestoneInfoId()));
        // 查询调研计划
        if (Integer.valueOf(2).equals(dto.getUserType())) {
            dto.setAuditSysUserId(dto.getSurveyUserId());
            dto.setSurveyUserId(null);
        }
        List<SurveyPlanTaskResp> surveyPlanList = projSurveyPlanMapper.findSurveyPlan(dto);

        this.dealSurveyPlanTask(dto, surveyPlanList);
        // 查询项目信息
        ProjProjectInfo projectInfo = projectInfoMapper.selectById(dto.getProjectInfoId());
        // 查询项目下所有实施产品
        List<BaseIdNameResp> productInfoList = dictProductMapper.findByProjectInfoId(dto.getProjectInfoId(), dto.getYyProductId());
        // 有查询条件时 判断surveyPlan中是否 存在数据，当不存在时按照正常逻辑走 。存在时 只根据surveyPlan中数据进行数据返回
        if (dto.hasFilterConditions()) {
            // 没有调研计划，直接返回
            if (CollectionUtil.isEmpty(surveyPlanList)) {
                surveyPlanTaskRespVO.setSurveyPlanTaskResp(new ArrayList<>());
                return Result.success(surveyPlanTaskRespVO);
            } else if (CollectionUtil.isNotEmpty(surveyPlanList)) {
                // 存在调研计划时查询调研计划中的产品
                Set<Long> yyProductIds = surveyPlanList.stream().map(ProjSurveyPlan::getYyProductId).collect(Collectors.toSet());
                productInfoList = dictProductMapper.findByProductIds(yyProductIds);
            }
        }
        // 当前登录人
        SysUserVO currentUser = userHelper.getCurrentUser();
        // true-当前登录人是前端项目经理
        boolean isProjectLeader = currentUser.getSysUserId().equals(projectInfo.getProjectLeaderId());
        // 通过实施产品组装的调研计划列表
        List<SurveyPlanTaskResp> respList = productInfoList.stream().filter(a -> !a.getName().contains("升级云健康-") && !a.getName().contains("分院模式")).map(product -> {
            SurveyPlanTaskResp resp = new SurveyPlanTaskResp();
            resp.setYyProductId(product.getId());
            resp.setProductInfoName(product.getName().endsWith(StrUtil.DASHED) ? product.getName().substring(0, product.getName().length() - 1) : product.getName());
            resp.setMobileProductName(product.getName().endsWith(StrUtil.DASHED) ? product.getName().substring(0, product.getName().length() - 1) : product.getName());
            resp.setPid(0L);
            resp.setCompleteStatus(ResearchStatus.NOT_STARTED.getCode());
            resp.setCompletionProgress(dictBusinessStatusService.getSurveyPlanStatusDescriptionByStatusId(ResearchStatus.NOT_STARTED.getCode()));
            resp.setCanEdit(isProjectLeader);
            resp.setCustomInfoId(projectInfo.getCustomInfoId());
            resp.setHasChildren(false);
            resp.setProjectInfoId(projectInfo.getProjectInfoId());
            resp.setShowType("main");
            return resp;
        }).collect(Collectors.toList());
        // 哪些产品不需要调研
        List<DictProductExtend> productExtendList = dictProductExtendMapper.selectList(
                new QueryWrapper<DictProductExtend>()
                        .eq("survey_flag", 0)
                        .eq("is_deleted", 0)
        );
        // 无需调研的产品ID
        List<Long> notSurveyProductIds = productExtendList.stream().map(DictProductExtend::getYyProductId).collect(Collectors.toList());
        // 设置是否需要调研标识
        steSurveyFlag(respList, notSurveyProductIds);
        // 当查询结果为空 并且 查询条件为空的时候 (除了项目id以外的字段没数据)
        if (surveyPlanList.isEmpty() && dto.getSurveyUserId() == null && dto.getCompleteStatus() == null && dto.getYyProductId() == null) {
            surveyPlanTaskRespVO.setSurveyPlanTaskResp(CollUtil.isEmpty(respList) ? new ArrayList<>() : respList);
            return Result.success(surveyPlanTaskRespVO);
        }
        Map<Long, List<SurveyPlanTaskResp>> productIdSurveyPlanMap = surveyPlanList.stream().collect(Collectors.groupingBy(ProjSurveyPlan::getYyProductId));
        List<SurveyPlanTaskResp> childrenResp = new ArrayList<>();
        // 父级科室信息是 子集中的科室信息综合信息  拼装数据 ********* 拆分方法
        infoDataForChild(respList, productIdSurveyPlanMap, childrenResp, currentUser, isProjectLeader, notSurveyProductIds);
        respList.addAll(childrenResp);
        if (StringUtils.isNotBlank(dto.getShowType())) {
            respList = respList.stream().filter(item -> dto.getShowType().equals(item.getShowType())).collect(Collectors.toList());
        }
        try {
            List<DictProduct> products = dictProductMapper.selectList(new QueryWrapper<DictProduct>().isNotNull("order_no"));
            respList.forEach(surveyPlanTaskResp -> {
                Long orderNo = null;
                if (ObjectUtil.isNotEmpty(products)) {
                    for (DictProduct d : products) {
                        if (ObjectUtil.isNotEmpty(surveyPlanTaskResp.getYyProductId()) && surveyPlanTaskResp.getYyProductId().equals(d.getYyProductId())) {
                            orderNo = d.getOrderNo();
                        }
                    }
                    if (ObjectUtil.isEmpty(orderNo)) {
                        surveyPlanTaskResp.setOrderNo(surveyPlanTaskResp.getYyProductId());
                    }
                }
            });
            // 排序
            List<SurveyPlanTaskResp> sortedItems = respList.stream().sorted(Comparator.comparing(SurveyPlanTaskResp::getOrderNo, Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
            surveyPlanTaskRespVO.setSurveyPlanTaskResp(CollUtil.isEmpty(sortedItems) ? new ArrayList<>() : respList);
            return Result.success(surveyPlanTaskRespVO);
        } catch (Exception e) {
            log.error("查询调研计划，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("查询失败," + e.getMessage());
        }
    }


    private void dealSurveyPlanTask(ProjSurveyPlanDTO dto, List<SurveyPlanTaskResp> surveyPlanTaskFormDatabase) {
        if (CollectionUtils.isEmpty(surveyPlanTaskFormDatabase)) {
            return;
        }
        // 查询所有的答题结果
        List<FmUserFormData> fmUserFormDataList = fmUserFormDataMapper.selectList(
                new QueryWrapper<FmUserFormData>()
                        .eq("project_info_id", dto.getProjectInfoId())
                        .eq("save_type", "permanent")
                        .eq("source", "config")
        );

        for (SurveyPlanTaskResp item : surveyPlanTaskFormDatabase) {
            item.setPlanId(item.getSurveyPlanId());
            if (dto.getHospitalInfoId() != null) {
                item.setHospitalInfoId(dto.getHospitalInfoId());
            }

            // 审批日志
            if (Integer.valueOf(4).equals(item.getCompleteStatus()) || Integer.valueOf(5).equals(item.getCompleteStatus())) {
                List<ProjBusinessExamineLog> projBusinessExamineLogs = projBusinessExamineLogMapper.selectList(
                        new QueryWrapper<ProjBusinessExamineLog>()
                                .eq("is_deleted", 0)
                                .eq("business_type", "survey")
                                .eq("examine_status", 2)
                                .eq("business_id", item.getSurveyPlanId())
                );
                if (!CollectionUtils.isEmpty(projBusinessExamineLogs)) {
                    ProjBusinessExamineLog projBusinessExamineLog = projBusinessExamineLogs.stream().max(Comparator.comparing(ProjBusinessExamineLog::getCreateTime)).get();
                    item.setRejectReason(projBusinessExamineLog.getExamineOpinion());
                }
            }
            if (dto.getHospitalInfoId() != null) {
                String formKey = null;
                try {
                    if (item.getYyProductId() != null) {
                        formKey = fmUserFormService.getProjectFormKeyByProjectInfoIdAndYyProductId(dto.getProjectInfoId(), String.valueOf(item.getYyProductId()));
                    }
                } catch (Exception e) {
                    log.error("获取表单key，发生异常，errMsg={}", e.getMessage());
                }
                String finalFormKey = formKey;
                FmUserFormData fmUserFormData = null;
                if (StringUtils.isNotBlank(finalFormKey)) {
                    fmUserFormData = fmUserFormDataList.stream()
                            .filter(userFormData -> finalFormKey.equals(userFormData.getFormKey()) && "config".equals(userFormData.getSource()) && userFormData.getHospitalInfoId().equals(dto.getHospitalInfoId()))
                            .findFirst().orElse(null);
                }
                if (fmUserFormData != null) {
                    item.setFinalResultUserId(fmUserFormData.getSysUserId());
                    item.setFinalResultDataId(fmUserFormData.getId());
                }
            }
        }
    }

    @Override
    public Boolean cancelFinalResult(Long finalResultDataId) {
        int id = fmUserFormDataMapper.delete(new QueryWrapper<FmUserFormData>().eq("id", finalResultDataId));
        return 1 == id;
    }

    /**
     * 说明:获取项目调研计划任务-医院列表
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result<java.util.List<com.msun.csm.model.vo.surveyplan.SurveyPlanHospitalProductVO>>
     * @author: Yhongmin
     * @createAt: 2024/8/29 11:15
     * @remark: Copyright
     */
    @Override
    public Result<SurveyPlanInitVO> findSurveyPlanHospitalProduct(ProjSurveyPlanDTO dto) {
        SurveyPlanInitVO surveyPlanInitVO = new SurveyPlanInitVO();
        //左上角产品调研数据详情
        SurveyPlanProductVO surveyPlanProductVO = new SurveyPlanProductVO();
        //查询所有实施产品
        List<ProjProductDeliverRecord> productNum = projProductDeliverRecordMapper.selectList(
                new QueryWrapper<ProjProductDeliverRecord>()
                        .eq("project_info_id", dto.getProjectInfoId())
                        .eq("is_deleted", NumberEnum.NO_0.num())
        );
        // 首期项目已上线，且当前项目仅派工“分院模式”产品时
        Boolean isBranchOnlineFlag = this.isBranchOnlineFlag(dto.getProjectInfoId());
        List<Long> yyProductIds = productNum.stream().map(ProjProductDeliverRecord::getProductDeliverId).distinct().collect(Collectors.toList());
        Long productDeliverRecordNum = Long.valueOf(yyProductIds.size());
        surveyPlanProductVO.setProductDeliverRecordNum(productDeliverRecordNum);
        //查询所有调研已调研的产品
        List<Long> surveyPlanList = projSurveyPlanMapper.findYyProductIdByProjectInfoId(dto.getProjectInfoId());
        if (isBranchOnlineFlag) {
            surveyPlanList = projSurveyPlanMapper.findIsBranchOnlineYyProductIdByProjectInfoId(dto.getProjectInfoId());
        }
        //已分配调研产品数量
        Long allotProductNum = Long.valueOf(surveyPlanList.size());
        surveyPlanProductVO.setAllotProductNum(Long.valueOf(surveyPlanList.size()));
        //需要调研产品数量
        Result<List<BaseIdNameResp>> listResult = productBacklogService.selectYyProductAndModule(dto.getProjectInfoId());
        if (listResult.isSuccess() && listResult.getData() != null) {
            surveyPlanProductVO.setSurveyPlanProductNum(Long.valueOf(listResult.getData().size()));
            //未分配的数据
            Long noAllotProductNum = surveyPlanProductVO.getSurveyPlanProductNum() - allotProductNum;
            surveyPlanProductVO.setNoAllotProductNum(noAllotProductNum > 0L ? noAllotProductNum : 0L);
            if (surveyPlanProductVO.getNoAllotProductNum() > 0L) {
                //处理未分配调研的产品数据
                List<Long> finalSurveyPlanList = surveyPlanList;
                Set<Long> noAllotProductId = yyProductIds.stream().filter(item -> !finalSurveyPlanList.contains(item))
                        .collect(Collectors.toSet());
                List<BaseIdNameResp> noAllotProducts = dictProductMapper.findByProductIds(noAllotProductId);
                noAllotProducts.forEach(noAllotProduct -> {
                    String name = noAllotProduct.getName();
                    noAllotProduct.setName(name.endsWith("-") ? name.substring(0, name.length() - 1) : name);
                });
                surveyPlanProductVO.setNoAllotProductList(noAllotProducts);
            } else {
                surveyPlanProductVO.setNoAllotProductList(new ArrayList<>());
            }
        }
        surveyPlanInitVO.setSurveyPlanProductVO(surveyPlanProductVO);
        //判断里程碑节点有没有完成
        surveyPlanInitVO.setConfirmComplete(!milestoneInfoService.verifyMilestoneStatus(dto.getMilestoneInfoId()));
        List<SurveyPlanHospitalProductVO> surveyPlanHospitalProductVOS = projSurveyPlanMapper.findSurveyPlanHospitalProduct(dto);
        if (CollUtil.isNotEmpty(surveyPlanHospitalProductVOS)) {
            surveyPlanHospitalProductVOS.forEach(surveyPlanHospitalProductVO -> {
                //主院
                surveyPlanHospitalProductVO.setIsOnlineFlag(false);
                if (NumberEnum.NO_1.num().equals(surveyPlanHospitalProductVO.getHealthBureauFlag())) {
                    surveyPlanHospitalProductVO.setIsCompleteProduct(surveyPlanHospitalProductVO.getCompleteProductNum().equals(surveyPlanProductVO.getSurveyPlanProductNum()));
                } else {
                    surveyPlanHospitalProductVO.setIsCompleteProduct(surveyPlanHospitalProductVO.getCompleteProductNum().equals(surveyPlanHospitalProductVO.getProductNum())
                            && 0L != surveyPlanHospitalProductVO.getProductNum());
                }
            });
        }
        if (!CollectionUtils.isEmpty(surveyPlanHospitalProductVOS)) {
            try {
                SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
                selectHospitalDTO.setProjectInfoId(dto.getProjectInfoId());
                List<ProjHospitalInfo> projHospitalInfoList = projHospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);
                // 创建一个映射B集合的hospitalId到其索引
                Map<Long, Integer> orderMap = new HashMap<>();
                for (int i = 0; i < projHospitalInfoList.size(); i++) {
                    orderMap.put(projHospitalInfoList.get(i).getHospitalInfoId(), i);
                }
                // 按照B集合的顺序对A集合进行排序
                surveyPlanHospitalProductVOS.sort(Comparator.comparingInt(item -> orderMap.getOrDefault(item.getHospitalInfoId(), Integer.MAX_VALUE)));
                surveyPlanInitVO.setDefaultHospitalInfoId(surveyPlanHospitalProductVOS.get(0).getHospitalInfoId());
            } catch (Exception e) {
                log.error("调研计划页面对医院排序，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            }
        }
        // 分院模式下，上线的医院不可进行调研分配
        if (isBranchOnlineFlag) {
            this.setHospitalProductVOList(surveyPlanInitVO, surveyPlanHospitalProductVOS, dto.getProjectInfoId());
        }
        surveyPlanInitVO.setHospitalProductVOList(ObjectUtils.isEmpty(surveyPlanHospitalProductVOS) ? new ArrayList<>() : surveyPlanHospitalProductVOS);
        return Result.success(surveyPlanInitVO);
    }

    /**
     * 分院模式下，上线的医院不可进行调研分配
     *
     * @param surveyPlanInitVO
     * @param surveyPlanHospitalProductVOS
     * @param projectInfoId
     */
    @Override
    public void setHospitalProductVOList(SurveyPlanInitVO surveyPlanInitVO,
                                         List<SurveyPlanHospitalProductVO> surveyPlanHospitalProductVOS, Long projectInfoId) {
        try {
            List<ProjHospitalOnlineDetail> detailList =
                    projHospitalOnlineDetailMapper.selectList(new QueryWrapper<ProjHospitalOnlineDetail>().in("hospital_info_id", surveyPlanHospitalProductVOS.stream().map(SurveyPlanHospitalProductVO::getHospitalInfoId).collect(Collectors.toList()))
                            .eq("is_deleted", 0));
            detailList = detailList.stream().filter(item -> item.getOnlineStatus().equals(NumberEnum.NO_1.num()) && !item.getProjectInfoId().equals(projectInfoId)).collect(Collectors.toList());
            List<Long> onlineHos = detailList.stream().map(ProjHospitalOnlineDetail::getHospitalInfoId).collect(Collectors.toList());
            for (SurveyPlanHospitalProductVO surveyPlanHospitalProductVO : surveyPlanHospitalProductVOS) {
                if (onlineHos.contains(surveyPlanHospitalProductVO.getHospitalInfoId())) {
                    surveyPlanHospitalProductVO.setIsOnlineFlag(true);
                } else {
                    surveyPlanHospitalProductVO.setIsOnlineFlag(false);
                }
            }
            List<SurveyPlanHospitalProductVO> survList = surveyPlanHospitalProductVOS.stream().filter(item -> !item.getIsOnlineFlag()).collect(Collectors.toList());
            if (survList != null && survList.size() > 0) {
                surveyPlanInitVO.setDefaultHospitalInfoId(survList.get(0).getHospitalInfoId());
            } else {
                surveyPlanInitVO.setDefaultHospitalInfoId(null);
            }
        } catch (Exception e) {
            log.error("分院模式下，上线的医院不可进行调研分配，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
    }

    /**
     * 判断分院模式且首期项目已上线，且派工产品只有一个
     *
     * @param projectInfoId
     * @return
     */
    @Override
    public Boolean isBranchOnlineFlag(Long projectInfoId) {
        Boolean b = false;
        try {
            b = projectInfoService.isBranchHospital(projectInfoId);
            if (b) {
                // 工单产品
                ProductInfoDTO productInfoDTO = new ProductInfoDTO();
                productInfoDTO.setProjectInfoId(projectInfoId);
                productInfoDTO.setProductWorkType(WorkTypeEnum.ORDER_WORK.getCode());
                List<ProductInfo> productInfos = orderProductService.getProductListImpl(productInfoDTO);
                if (productInfos != null && productInfos.size() == 1) {
                    b = true;
                } else {
                    b = false;
                }
            }
        } catch (Exception e) {
            log.error("判断分院模式且首期项目已上线，且派工产品只有一个，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        return b;
    }

    private void steSurveyFlag(List<SurveyPlanTaskResp> respList, List<Long> notSurveyProductIds) {
        for (SurveyPlanTaskResp v : respList) {
            // 赋值是否可调研
            // 判断父级的产品是否需要分配任务。部分产品不需要进行调研
            // 查询哪些产品不需要调研 ， 在下方需要把不需要调研的产品过滤出来展示在前端
            if (notSurveyProductIds.contains(v.getYyProductId())) {
                v.setSurveyFlag(0);
            } else {
                v.setSurveyFlag(1);
            }
        }
    }

    /**
     * 产品业务调研，数据拼装
     */
    void infoDataForChild(List<SurveyPlanTaskResp> respList, Map<Long, List<SurveyPlanTaskResp>> productIdSurveyPlanMap,
                          List<SurveyPlanTaskResp> childrenResp, SysUserVO currentUser, boolean isProjectLeader,
                          List<Long> notSurveyProductIds) {
        List<Integer> audited = Arrays.asList(1, 3, 4, 5);
        List<Integer> notAudited = Arrays.asList(0, 2, 6);
        for (SurveyPlanTaskResp task : respList) {
            List<SurveyPlanTaskResp> surveyPlans = productIdSurveyPlanMap.get(task.getYyProductId());
            if (surveyPlans != null && surveyPlans.size() == 1) {
                //只有一条调研计划任务，直接显示为父类
                SurveyPlanTaskResp taskResp = surveyPlans.get(0);
                constructData(task, taskResp, currentUser, isProjectLeader, notSurveyProductIds);
            } else if (surveyPlans != null && surveyPlans.size() > 1) {
                //多条调研计划任务，显示为子类
                steSurveyFlag(surveyPlans, notSurveyProductIds);
                surveyPlans.forEach(children -> {
                    children.setPid(task.getYyProductId());
                    children.setShowType("detail");
                    if (children.getSurveyFlag() == 0) {
                        children.setCompletionProgress(dictBusinessStatusService.getSurveyPlanStatusDescriptionByStatusId(ResearchStatus.FINISHED.getCode()));
                        children.setCompleteStatus(ResearchStatus.FINISHED.getCode());
                    } else {
                        children.setCompletionProgress(dictBusinessStatusService.getSurveyPlanStatusDescriptionByStatusId(children.getCompleteStatus()));
                    }
                    children.setCanSurvey(currentUser.getSysUserId().equals(children.getSurveyUserId()));
                    children.setCanEdit(isProjectLeader);
                    children.setHospitalInfoName(children.getHospitalInfoName());
                    children.setDeptName(children.getDeptName());
                    //因为pid父级id使用的yyProductId，所以这里需要置空，不然父项子项id相同，前端渲染死循环
                    children.setYyProductId(null);
                    children.setMobileProductName(task.getMobileProductName());
                    children.setProductInfoName(task.getProductInfoName());

                });
                childrenResp.addAll(surveyPlans);
                // 医院名称 ： 整理子集的《医院名称》 为 集合， 按照逗号分割成一个参数 加入到父级中 **** 后期大概率删除、现在父级展示了一堆数据，看着巨乱
                Set<String> hospitalNameForChildList =
                        surveyPlans.stream().map(SurveyPlanTaskResp::getHospitalInfoName).collect(Collectors.toSet());
                StringJoiner hospitalNameForChildStr = new StringJoiner(",");
                for (String item : hospitalNameForChildList) {
                    hospitalNameForChildStr.add(item);
                }
                task.setHospitalInfoName(hospitalNameForChildStr.toString());
                // 科室名称 ： 整理子集的《医院名称-科室名称》 为 集合， 按照逗号分割成一个参数 加入到父级中
                Set<String> deptNameForChildList =
                        surveyPlans.stream().map(ProjSurveyPlan::getDeptName).collect(Collectors.toSet());
                StringJoiner deptNameForChildStr = new StringJoiner(",");
                for (String item : deptNameForChildList) {
                    deptNameForChildStr.add(item);
                }
                task.setDeptName(deptNameForChildStr.toString());
                //  调研人 ： 同 科室名称
                Set<String> surveyUserNameForChildList =
                        surveyPlans.stream().map(SurveyPlanTaskResp::getSurveyUserName).collect(Collectors.toSet());
                StringJoiner surveyUserNameForChildStr = new StringJoiner(",");
                for (String item : surveyUserNameForChildList) {
                    surveyUserNameForChildStr.add(item);
                }
                task.setSurveyUserName(surveyUserNameForChildStr.toString());
                // 计划时间：  取子集中最晚时间
                List<SurveyPlanTaskResp> collect = surveyPlans.stream().filter(vo -> vo.getPlanCompleteTime() != null)
                        .collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(collect)) {
                    task.setPlanCompleteTime(collect.stream()
                            .map(ProjSurveyPlan::getPlanCompleteTime)
                            .max(Comparator.comparing(Date::getTime)).get());
                } else {
                    task.setPlanCompleteTime(null);
                }
                List<SurveyPlanTaskResp> collect1 = surveyPlans.stream().filter(vo -> vo.getActualCompTime() != null)
                        .collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(collect1)) {
                    task.setActualCompTime(collect1.stream()
                            .map(ProjSurveyPlan::getActualCompTime)
                            .max(Comparator.comparing(Date::getTime)).get());
                } else {
                    task.setActualCompTime(null);
                }
                // 父节点的唯一主键设置为产品id，前端展开时需要唯一值。单独设置
                task.setSurveyPlanId(task.getYyProductId());
                //处理父级节点完成状态
                boolean isAllComplete = surveyPlans.stream()
                        .allMatch(e -> e.getCompleteStatus() == ResearchStatus.FINISHED.getCode());
                boolean isAllNotStart = surveyPlans.stream()
                        .allMatch(e -> e.getCompleteStatus() == ResearchStatus.NOT_STARTED.getCode());
                // surveyPlans 比值 ：已完成的数量 / 任务总数  父级节点效果 ： 未完成(0/N)
                int finishedCount = (int) surveyPlans.stream().filter(e -> !Integer.valueOf(0).equals(e.getCompleteStatus())).count();
                if (isAllComplete) {
                    task.setCompleteStatus(ResearchStatus.FINISHED.getCode());
                    task.setCompletionProgress(
                            dictBusinessStatusService.getSurveyPlanStatusDescriptionByStatusId(ResearchStatus.FINISHED.getCode()) + "(" + finishedCount + "/" + surveyPlans.size() + ")");
                } else if (isAllNotStart) {
                    task.setCompleteStatus(ResearchStatus.NOT_STARTED.getCode());
                    task.setCompletionProgress(
                            dictBusinessStatusService.getSurveyPlanStatusDescriptionByStatusId(ResearchStatus.NOT_STARTED.getCode()) + "(" + finishedCount + "/" + surveyPlans.size()
                                    + ")");
                } else {
                    task.setCompleteStatus(ResearchStatus.DOING.getCode());
                    task.setCompletionProgress(
                            dictBusinessStatusService.getSurveyPlanStatusDescriptionByStatusId(ResearchStatus.DOING.getCode()) + "(" + finishedCount + "/" + surveyPlans.size() + ")");
                }
                if (task.getSurveyFlag() == 0) {
                    task.setCompletionProgress(dictBusinessStatusService.getSurveyPlanStatusDescriptionByStatusId(ResearchStatus.FINISHED.getCode()));
                    task.setCompleteStatus(ResearchStatus.FINISHED.getCode());
                }
                boolean douShiWeiTiShen = surveyPlans.stream().allMatch(item -> notAudited.contains(item.getCompleteStatus()));
                // 都未提交审核
                if (douShiWeiTiShen) {
                    String auditor = surveyPlans.stream().map(SurveyPlanTaskResp::getAuditUserName).filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
                    // 拼接审核人名称
                    task.setAuditUserName(auditor);
                    // 审核意见为空
                    task.setRejectReason(null);
                    if (isAllNotStart) {
                        task.setCompleteStatus(ResearchStatus.NOT_STARTED.getCode());
                        task.setCompletionProgress(dictBusinessStatusService.getSurveyPlanStatusDescriptionByStatusId(ResearchStatus.NOT_STARTED.getCode()) + "(" + 0 + "/" + surveyPlans.size() + ")");
                    } else {
                        task.setCompleteStatus(ResearchStatus.DOING.getCode());
                        task.setCompletionProgress(dictBusinessStatusService.getSurveyPlanStatusDescriptionByStatusId(ResearchStatus.DOING.getCode()) + "(" + finishedCount + "/" + surveyPlans.size() + ")");
                    }
                } else {
                    SurveyPlanTaskResp surveyPlanTaskResp = surveyPlans.stream().filter(item -> audited.contains(item.getCompleteStatus())).max(Comparator.comparing(SurveyPlanTaskResp::getUpdateTime)).orElse(null);

                    if (surveyPlanTaskResp != null) {
                        task.setPlanId(surveyPlanTaskResp.getSurveyPlanId());
                        task.setSurveyPlanId(surveyPlanTaskResp.getSurveyPlanId());
                        task.setAuditUserName(surveyPlanTaskResp.getAuditUserName());
                        task.setRejectReason(surveyPlanTaskResp.getRejectReason());
                        task.setActualCompTime(surveyPlanTaskResp.getActualCompTime());
                        task.setCompleteStatus(surveyPlanTaskResp.getCompleteStatus());
                        task.setCompletionProgress(dictBusinessStatusService.getSurveyPlanStatusDescriptionByStatusId(surveyPlanTaskResp.getCompleteStatus()) + "(" + finishedCount + "/" + surveyPlans.size() + ")");
                    }
                }
                task.setHasChildren(true);
            } else {
                // 判断父级的产品是否需要分配任务。部分产品不需要进行调研
                // 查询哪些产品不需要调研 ， 在下方需要把不需要调研的产品过滤出来展示在前端
                if (notSurveyProductIds.contains(task.getYyProductId())) {
                    task.setSurveyFlag(0);
                } else {
                    task.setSurveyFlag(1);
                }
            }
        }
    }

    /**
     * 添加调研计划任务
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result addSurveyPlanTask(AddSurveyPlanReq addSurveyPlanReq) {
        SysUserVO currentUser = userHelper.getCurrentUser();
        List<ProjSurveyPlan> projSurveyPlans = projSurveyPlanMapper.selectList(
                new QueryWrapper<ProjSurveyPlan>()
                        .eq("is_deleted", 0)
                        .eq("project_info_id", addSurveyPlanReq.getProjectInfoId())
                        .eq("hospital_info_id", addSurveyPlanReq.getHospitalInfoId())
                        .eq("yy_product_id", addSurveyPlanReq.getYyProductId())
                        .eq("dept_name", addSurveyPlanReq.getDeptName())
        );
        if (!CollectionUtils.isEmpty(projSurveyPlans)) {
            throw new IllegalArgumentException(String.format("【%s】科室已分配调研人，请确认后重新分配", addSurveyPlanReq.getDeptName()));
        }
        ProjSurveyPlanExtend surveyPlan = new ProjSurveyPlanExtend(addSurveyPlanReq, currentUser);
        surveyPlan.setSurveyPlanId(SnowFlakeUtil.getId());
        List<DictProductExtend> productExtendList = dictProductExtendMapper.selectList(new QueryWrapper<DictProductExtend>().eq("survey_flag", 0).eq("is_deleted", 0));
        if (!CollectionUtils.isEmpty(productExtendList)) {
            int con = (int) productExtendList.stream().filter(a -> a.getYyProductId().equals(addSurveyPlanReq.getYyProductId())).count();
            if (con > 0) {
                surveyPlan.setCompleteStatus(ResearchStatus.FINISHED.getCode());
            }
        }
        projSurveyPlanMapper.insert(surveyPlan);
        // 添加设备调研任务
        UpdateSurveyPlanReq updateSurveyPlanReq = new UpdateSurveyPlanReq();
        updateSurveyPlanReq.setProjectInfoId(addSurveyPlanReq.getProjectInfoId());
        updateSurveyPlanReq.setYyProductId(addSurveyPlanReq.getYyProductId());
        updateSurveyPlanReq.setPlanCompleteTime(addSurveyPlanReq.getPlanCompleteTime());
        updateSurveyPlanReq.setSurveyUserId(addSurveyPlanReq.getSurveyUserId());
        ProjSurveyPlan projSurveyPlan = projSurveyPlanMapper.selectOne(
                new QueryWrapper<ProjSurveyPlan>().eq("survey_plan_id", surveyPlan.getSurveyPlanId())
        );

        // 如果分配的是无需调研的产品，项目计划完成数进行更新
        projProjectPlanService.updatePlanTotalAndCompleteCountByProjectAndItemCode(addSurveyPlanReq.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_PRODUCT);
        addEquipSurveyTask(updateSurveyPlanReq, projSurveyPlan);
        SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
        param.setProjectInfoId(addSurveyPlanReq.getProjectInfoId());
        param.setHospitalInfoId(addSurveyPlanReq.getHospitalInfoId());
        param.setYyProductId(addSurveyPlanReq.getYyProductId());
        param.setPlanTime(DateUtil.parse(addSurveyPlanReq.getPlanCompleteTime()));
        param.setUserId(addSurveyPlanReq.getSurveyUserId());
        param.setCode(DictProjectPlanItemEnum.SURVEY_PRODUCT.getPlanItemCode());
        param.setTotalCount(this.getSurveyPlanTotal(addSurveyPlanReq.getProjectInfoId(), addSurveyPlanReq.getHospitalInfoId(), addSurveyPlanReq.getYyProductId(), addSurveyPlanReq.getSurveyUserId()));
        todoTaskService.projectTodoTaskInit(param);
        return Result.success();
    }

    private int getSurveyPlanTotal(Long projectInfoId, Long hospitalInfoId, Long yyProductId, Long sysUserId) {
        ProjSurveyPlanDTO projSurveyPlanDTO = new ProjSurveyPlanDTO();
        projSurveyPlanDTO.setProjectInfoId(projectInfoId);
        projSurveyPlanDTO.setHospitalInfoId(hospitalInfoId);
        projSurveyPlanDTO.setYyProductId(yyProductId);
        projSurveyPlanDTO.setSurveyUserId(sysUserId);
        List<SurveyPlanTaskResp> surveyPlan = projSurveyPlanMapper.findSurveyPlan(projSurveyPlanDTO);
        return surveyPlan.size();
    }


    private int getSurveyPlanComplete(Long projectInfoId, Long hospitalInfoId, Long yyProductId, Long sysUserId) {
        ProjSurveyPlanDTO projSurveyPlanDTO = new ProjSurveyPlanDTO();
        projSurveyPlanDTO.setProjectInfoId(projectInfoId);
        projSurveyPlanDTO.setHospitalInfoId(hospitalInfoId);
        projSurveyPlanDTO.setYyProductId(yyProductId);
        projSurveyPlanDTO.setSurveyUserId(sysUserId);
        List<SurveyPlanTaskResp> surveyPlan = projSurveyPlanMapper.findSurveyPlan(projSurveyPlanDTO);
        if (CollectionUtils.isEmpty(surveyPlan)) {
            return 0;
        }
        boolean openSurveyAudit = projectInfoService.isOpenSurveyAudit(projectInfoId);
        // 开启后端审核，只有审核通过的认为是已完成
        if (openSurveyAudit) {
            surveyPlan = surveyPlan.stream().filter(item -> {
                if (Integer.valueOf(1).equals(item.getCompleteStatus())) {
                    return true;
                }
                return Integer.valueOf(3).equals(item.getCompleteStatus());
            }).collect(Collectors.toList());
            return surveyPlan.size();
        }
        // 不开后端审核保留原来逻辑
        surveyPlan = surveyPlan.stream().filter(item -> !Integer.valueOf(0).equals(item.getCompleteStatus())).collect(Collectors.toList());
        return surveyPlan.size();
    }

    /**
     * 删除调研计划任务
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result deleteSurveyPlanTask(ProjSurveyPlanDTO dto) {
        List<ProjSurveyPlan> projSurveyPlans = projSurveyPlanMapper.selectList(new QueryWrapper<ProjSurveyPlan>().in("survey_plan_id", dto.getSurveyPlanIds()));
        projSurveyPlanMapper.deleteBatchIds(dto.getSurveyPlanIds());
        if (!CollectionUtils.isEmpty(projSurveyPlans)) {
            projSurveyPlans.forEach(item -> {
                // 删除调研问卷
                String formKey = fmUserFormService.getProjectFormKeyByProjectInfoIdAndYyProductIdNoException(item.getProjectInfoId(), String.valueOf(item.getYyProductId()));
                if (StringUtils.isNotBlank(formKey)) {
                    int delete = fmUserFormDataMapper.delete(
                            new QueryWrapper<FmUserFormData>()
                                    .eq("form_key", formKey)
                                    .eq("hospital_info_id", item.getHospitalInfoId())
                                    .eq("sys_user_id", item.getSurveyUserId())
                                    .eq("dept_name", item.getDeptName())
                    );
                }

                // 修改我的待办数据
                SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
                param.setProjectInfoId(item.getProjectInfoId());
                param.setHospitalInfoId(item.getHospitalInfoId());
                param.setYyProductId(item.getYyProductId());
                param.setPlanTime(null);
                param.setUserId(item.getSurveyUserId());
                param.setCode(DictProjectPlanItemEnum.SURVEY_PRODUCT.getPlanItemCode());
                param.setTotalCount(this.getSurveyPlanTotal(item.getProjectInfoId(), item.getHospitalInfoId(), item.getYyProductId(), item.getSurveyUserId()));
                param.setCompleteCount(this.getSurveyPlanComplete(item.getProjectInfoId(), item.getHospitalInfoId(), item.getYyProductId(), item.getSurveyUserId()));
                todoTaskService.projectTodoTaskInit(param);

                // 删除待处理任务
                ProjProductTask projProductTask = new ProjProductTask();
                projProductTask.setIsDeleted(1);
                int update1 = productTaskMapper.update(projProductTask, new QueryWrapper<ProjProductTask>().eq("project_info_id", item.getProjectInfoId()).eq("hospital_info_id", item.getHospitalInfoId()).eq("yy_product_id", item.getYyProductId()));
                log.info("删除调研计划时删除待处理任务，删除结果={}", update1);

                ProjMilestoneTask milestoneTask1 = projMilestoneTaskMapper.selectOne(new QueryWrapper<ProjMilestoneTask>()
                        .eq("is_deleted", 0)
                        .eq("project_info_id", item.getProjectInfoId())
                        .eq("hospital_info_id", item.getHospitalInfoId())
                        .eq("milestone_node_code", DictProjectPlanItemEnum.PREPARAT_PRODUCT.getPlanItemCode()));
                if (milestoneTask1 != null) {
                    ProjMilestoneTaskDetail projMilestoneTaskDetail = new ProjMilestoneTaskDetail();
                    projMilestoneTaskDetail.setIsDeleted(1);
                    int update = projMilestoneTaskDetailMapper.update(projMilestoneTaskDetail, new QueryWrapper<ProjMilestoneTaskDetail>()
                            .eq("milestone_task_id", milestoneTask1.getMilestoneTaskId())
                            .eq("product_deliver_id", item.getYyProductId()));
                    log.info("删除调研计划时删除里程碑任务，删除结果={}", update);
                }

            });
            List<BaseIdNameResp> yyProductIdList = projSurveyPlans.stream().map(item -> new BaseIdNameResp(item.getYyProductId(), "")).collect(Collectors.toList());

            // 删除配置
            int i = productConfigMapper.deleteByParam(projSurveyPlans.get(0).getProjectInfoId(), projSurveyPlans.get(0).getHospitalInfoId(), yyProductIdList);
            log.info("删除调研计划时删除配置，删除结果={}", i);

            // 删除待处理任务明细
            int i1 = productBacklogMapper.deleteByParam(projSurveyPlans.get(0).getProjectInfoId(), projSurveyPlans.get(0).getHospitalInfoId(), yyProductIdList);
            log.info("删除调研计划时删除待处理任务明细，删除结果={}", i1);

            List<Long> collect = projSurveyPlans.stream().map(ProjSurveyPlan::getYyProductId).collect(Collectors.toList());

            // 删除报表
            List<ProjSurveyReport> projSurveyReports = projSurveyReportMapper.selectSurveyReport(projSurveyPlans.get(0).getProjectInfoId(), projSurveyPlans.get(0).getHospitalInfoId(), collect);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(projSurveyReports)) {
                List<Long> collect1 = projSurveyReports.stream().map(ProjSurveyReport::getSurveyReportId).collect(Collectors.toList());
                ProjSurveyReportDeleteReq req = new ProjSurveyReportDeleteReq(collect1);
                Result<Void> voidResult = projSurveyReportService.deleteReport(req);
                log.info("删除调研计划时删除报表，删除结果={}", JSON.toJSONString(voidResult));
            }

            // 删除表单
            List<ProjSurveyForm> projSurveyForms = projSurveyFormMapper.selectSurveyForm(projSurveyPlans.get(0).getProjectInfoId(), projSurveyPlans.get(0).getHospitalInfoId(), collect);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(projSurveyForms)) {
                for (ProjSurveyForm form : projSurveyForms) {
                    ProjSurveyFormUpdateReq req = new ProjSurveyFormUpdateReq();
                    req.setSurveyFormId(form.getSurveyFormId());
                    Result result = projSurveyFormService.deleteSurveyForm(req);
                    log.info("删除调研计划时删除表单，删除结果={}", JSON.toJSONString(result));
                }
            }

        }
        return Result.success();
    }

    /**
     * 批量新增调研计划任务
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result saveSurveyPlanTaskList(AddSurveyPlanReq req) {
        // service中存在新增功能，数据量不会很大 可以循环调用
        try {
            for (Long yyProductId : req.getYyProductIdList()) {
                req.setYyProductId(yyProductId);
                this.addSurveyPlanTask(req);
            }
        } catch (Exception e) {
            log.error("批量分配调研计划，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
        return Result.success("批量新增成功");
    }

    /**
     * 批量修改调研计划任务
     */
    @Override
    public Result updateSurveyPlanTaskList(UpdateSurveyPlanReq req) {
        // service中存在修改功能，数据量不会很大 可以循环调用
        try {
            for (Long surveyPlanId : req.getSurveyPlanIdList()) {
                req.setSurveyPlanId(surveyPlanId);
                this.updateSurveyPlanTask(req);
            }
        } catch (Exception e) {
            log.error("批量修改调研计划，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
        return Result.success("批量修改功");
    }

    /**
     * 更新调研计划任务
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result updateSurveyPlanTask(UpdateSurveyPlanReq updateSurveyPlanReq) {
        // 记录更新之前的人员信息和科室信息
        ProjSurveyPlan projSurveyPlan = projSurveyPlanMapper.selectOne(
                new QueryWrapper<ProjSurveyPlan>().eq("survey_plan_id", updateSurveyPlanReq.getSurveyPlanId())
        );
        List<ProjSurveyPlan> projSurveyPlans = projSurveyPlanMapper.selectList(
                new QueryWrapper<ProjSurveyPlan>()
                        .eq("is_deleted", 0)
                        .eq("project_info_id", projSurveyPlan.getProjectInfoId())
                        .eq("hospital_info_id", projSurveyPlan.getHospitalInfoId())
                        .eq("yy_product_id", projSurveyPlan.getYyProductId())
                        .eq("dept_name", updateSurveyPlanReq.getDeptName())
        );
        if (!CollectionUtils.isEmpty(projSurveyPlans)) {
            projSurveyPlans = projSurveyPlans.stream().filter(item -> !projSurveyPlan.getSurveyPlanId().equals(item.getSurveyPlanId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(projSurveyPlans)) {
                throw new IllegalArgumentException(String.format("【%s】科室已分配调研人，请确认后重新分配", updateSurveyPlanReq.getDeptName()));
            }
        }
        SysUserVO currentUser = userHelper.getCurrentUser();
        ProjSurveyPlanExtend surveyPlan = new ProjSurveyPlanExtend(updateSurveyPlanReq, currentUser);
        surveyPlan.setCreaterId(null);
        surveyPlan.setCreateTime(null);
        // 修改调研计划不更新完成状态
        surveyPlan.setCompleteStatus(null);
        projSurveyPlanMapper.updateByPrimaryKeySelective(surveyPlan);
        // 调研任务计划阶段，修改调研人员或者修改调研科室时，同步修改调研平台中调研结果的人员信息
        if (updateSurveyPlanReq.getSurveyUserId() != null || StringUtils.isNotBlank(updateSurveyPlanReq.getDeptName())) {
            // 查询项目信息
            ProjProjectInfo projectInfo = projectInfoService.selectByPrimaryKey(projSurveyPlan.getProjectInfoId());
            Long userId = updateSurveyPlanReq.getSurveyUserId();
            String productId = String.valueOf(projSurveyPlan.getYyProductId());
            // 同步更新TDUCK 负责人
            // 先查询当前表单信息
            String formKey = this.fmUserFormService.getProjectFormKeyByProjectInfoIdAndYyProductIdNoException(projectInfo.getProjectInfoId(), productId);
            if (StringUtils.isNotBlank(formKey)) {
                List<FmUserFormData> fmUserFormDataList = fmUserFormDataMapper.selectList(
                        new QueryWrapper<FmUserFormData>()
                                .eq("form_key", formKey)
                                .eq("source", "survey")
                                .eq("hospital_info_id", projSurveyPlan.getHospitalInfoId())
                                .eq("sys_user_id", projSurveyPlan.getSurveyUserId())
                                .eq("dept_name", projSurveyPlan.getDeptName())
                );
                if (!CollectionUtils.isEmpty(fmUserFormDataList)) {
                    fmUserFormDataList.forEach(fmUserFormData -> {
                        FmUserFormData fmUserFormDataUpdate = new FmUserFormData();
                        fmUserFormDataUpdate.setId(fmUserFormData.getId());
                        if (updateSurveyPlanReq.getSurveyUserId() != null) {
                            fmUserFormDataUpdate.setSysUserId(userId);
                        }
                        if (StringUtils.isNotBlank(updateSurveyPlanReq.getDeptName())) {
                            fmUserFormDataUpdate.setDeptName(updateSurveyPlanReq.getDeptName());
                        }
                        fmUserFormDataMapper.updateById(fmUserFormDataUpdate);
                    });
                }
            }
            addEquipSurveyTask(updateSurveyPlanReq, projSurveyPlan);
        }

        if (updateSurveyPlanReq.getSurveyUserId() != null) {
            projProjectPlanService.updatePlanTotalAndCompleteCountByProjectAndItemCode(projSurveyPlan.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_PRODUCT);
            SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
            param.setProjectInfoId(projSurveyPlan.getProjectInfoId());
            param.setHospitalInfoId(projSurveyPlan.getHospitalInfoId());
            param.setYyProductId(projSurveyPlan.getYyProductId());
            param.setUserId(updateSurveyPlanReq.getSurveyUserId());
            param.setCode(DictProjectPlanItemEnum.SURVEY_PRODUCT.getPlanItemCode());
            todoTaskService.projectTodoTaskInit(param);

        }

        return Result.success();
    }

    /**
     * 生成设备里程碑节点任务
     *
     * @param updateSurveyPlanReq
     * @param projSurveyPlan
     */
    void addEquipSurveyTask(UpdateSurveyPlanReq updateSurveyPlanReq, ProjSurveyPlan projSurveyPlan) {
        // 当责任人不为空时候 说明指定了负责人。同步设置 设备调研负责人信息， milestoneTaks表创建 设备调研任务
        if (updateSurveyPlanReq.getSurveyUserId() != null) {
            // 查询当前产品是否包含设备。当不包含时 跳过
            ConfigProductJobMenuDetail configProductJobMenuDetail = configProductJobMenuDetailMapper.selectOne(
                    new QueryWrapper<ConfigProductJobMenuDetail>()
                            .eq("product_job_menu_id", 5)
                            .eq("yy_product_id", projSurveyPlan.getYyProductId())
            );
            if (ObjectUtil.isNotEmpty(configProductJobMenuDetail)) {
                HospitalInfoDTO hospitalInfoDTO = new HospitalInfoDTO();
                hospitalInfoDTO.setCustomInfoId(projSurveyPlan.getCustomInfoId());
                hospitalInfoDTO.setProjectInfoId(projSurveyPlan.getProjectInfoId());
                hospitalInfoDTO.setHospitalInfoId(projSurveyPlan.getHospitalInfoId());
                // 查询设备调研里程碑节点信息
                ProjMilestoneInfo milestoneInfo = milestoneInfoService.getMilestoneInfo(projSurveyPlan.getProjectInfoId(),
                        MilestoneNodeEnum.SURVEY_DEVICE.getCode());
                MilestoneInfoDTO milestoneInfoDTO = new MilestoneInfoDTO();
                milestoneInfoDTO.setMilestoneNodeCode(MilestoneNodeEnum.SURVEY_DEVICE.getCode());
                milestoneInfoDTO.setMilestoneInfoId(milestoneInfo.getMilestoneInfoId());
                milestoneInfoDTO.setProjectStageId(milestoneInfo.getProjectStageId());
                milestoneInfoDTO.setProjectStageCode(milestoneInfo.getProjectStageCode());
                // 组装任务数据
                ResearchPlanDTO researchPlanDTO = getResearchPlanDTO(hospitalInfoDTO, milestoneInfoDTO, null, new Date());
                // 检查是否已经存在任务，当不存在时候 进行创建
                ProjMilestoneTask milestoneTask = projMilestoneTaskMapper.selectOne(new QueryWrapper<ProjMilestoneTask>()
                        .eq("milestone_node_code", MilestoneNodeEnum.SURVEY_DEVICE.getCode())
                        .eq("customer_info_id", projSurveyPlan.getCustomInfoId())
                        .eq("project_info_id", projSurveyPlan.getProjectInfoId())
                        .eq("hospital_info_id", projSurveyPlan.getHospitalInfoId())
                );
                if (ObjectUtil.isEmpty(milestoneTask)) {
                    log.info("生成设备调研的里程碑节点任务数据 , 数据参数信息 , {}", JSONUtil.toJsonStr(researchPlanDTO));
                    projMilestoneTaskMapper.insertResearchPlans(Arrays.asList(researchPlanDTO));
                }
                ProjMilestoneTask milestoneTask2 =
                        projMilestoneTaskMapper.selectOne(new QueryWrapper<ProjMilestoneTask>()
                                .eq("milestone_node_code", MilestoneNodeEnum.SURVEY_DEVICE.getCode())
                                .eq("customer_info_id", projSurveyPlan.getCustomInfoId())
                                .eq("project_info_id", projSurveyPlan.getProjectInfoId())
                                .eq("hospital_info_id", projSurveyPlan.getHospitalInfoId())
                        );
                // 生成 设备调研里程碑节点明细任务数据
                ProjMilestoneTaskDetail projMilestoneTaskDetail = infoDataForTaskDetail(milestoneTask2,
                        projSurveyPlan, updateSurveyPlanReq.getSurveyUserId());
                // 检查是否已经生成了明细任务
                ProjMilestoneTaskDetail projMilestoneTaskDetail1 = projMilestoneTaskDetailMapper.selectOne(new QueryWrapper<ProjMilestoneTaskDetail>()
                        .eq("milestone_task_id", milestoneTask2.getMilestoneTaskId())
                        .eq("product_deliver_id", projSurveyPlan.getYyProductId())
                );
                if (ObjectUtil.isNotEmpty(projMilestoneTaskDetail1)) {
                    projMilestoneTaskDetail.setMilestoneTaskDetailId(projMilestoneTaskDetail1.getMilestoneTaskDetailId());
                    log.info("更新设备调研的里程碑节点明细任务 , 数据参数信息 , {}", JSONUtil.toJsonStr(projMilestoneTaskDetail));
                    projMilestoneTaskDetailMapper.updateById(projMilestoneTaskDetail);
                } else {
                    log.info("生成设备调研的里程碑节点明细任务 , 数据参数信息 , {}", JSONUtil.toJsonStr(projMilestoneTaskDetail));
                    projMilestoneTaskDetailMapper.insert(projMilestoneTaskDetail);
                }
                SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
                param.setProjectInfoId(updateSurveyPlanReq.getProjectInfoId());
                param.setHospitalInfoId(projSurveyPlan.getHospitalInfoId());
                param.setYyProductId(updateSurveyPlanReq.getYyProductId());
                param.setPlanTime(DateUtil.parse(updateSurveyPlanReq.getPlanCompleteTime()));
                param.setUserId(updateSurveyPlanReq.getSurveyUserId());
                //todo 硬编码需要处理
                param.setCode("survey_device");
                todoTaskService.saveOrUpdateProjTodoTask(param);
            }

        }
    }

    private ResearchPlanDTO getResearchPlanDTO(HospitalInfoDTO v, MilestoneInfoDTO infoDTO, String secondLeaders,
                                               Date createTime) {
        ResearchPlanDTO planDto = new ResearchPlanDTO();
        planDto.setProjResearchPlanId(SnowFlakeUtil.getId());
        planDto.setHospitalId(v.getHospitalInfoId() == null ? 0 : v.getHospitalInfoId());
        planDto.setProjectInfoId(v.getProjectInfoId());
        planDto.setCustomerInfoId(v.getCustomInfoId());
        planDto.setResearchCode(infoDTO.getMilestoneNodeCode() == null ? "" : infoDTO.getMilestoneNodeCode());
        planDto.setCreaterId(userHelper.getCurrentUser().getSysUserId());
        planDto.setCreateTime(createTime);
        planDto.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
        planDto.setUpdateTime(createTime);
        planDto.setPlanStartTime(ObjectUtil.isNotEmpty(infoDTO.getExpectStartTime())
                ? DateUtil.beginOfDay(infoDTO.getExpectStartTime()) : null);
        planDto.setPlanEndTime(ObjectUtil.isNotEmpty(infoDTO.getExpectCompTime())
                ? DateUtil.endOfDay(infoDTO.getExpectCompTime()) : null);
        planDto.setLeaderId(v.getLeaderId() == null ? 0 : v.getLeaderId());
        planDto.setSecondLeaderId(secondLeaders == null ? "" : secondLeaders);
        planDto.setResultSourceId(40004L);
        planDto.setIsDeleted(0);
        planDto.setMilestoneInfoId(infoDTO.getMilestoneInfoId());
        planDto.setProjectStageId(infoDTO.getProjectStageId());
        planDto.setProjectStageCode(infoDTO.getProjectStageCode());
        return planDto;
    }

    private ProjMilestoneTaskDetail infoDataForTaskDetail(ProjMilestoneTask milestoneTask,
                                                          ProjSurveyPlan projSurveyPlan, Long surveyUserId) {
        ProjMilestoneTaskDetail detail = new ProjMilestoneTaskDetail();
        detail.setMilestoneTaskDetailId(SnowFlakeUtil.getId());
        detail.setLeaderId(surveyUserId);
        detail.setProductDeliverId(projSurveyPlan.getYyProductId());
        detail.setMilestoneTaskId(milestoneTask.getMilestoneTaskId());
        detail.setProductDeliverRecordId(projSurveyPlan.getYyProductId());
        detail.setCompleteStatus(0);
        return detail;
    }


    private String createUrl(String formKey, Long surveyUserId, String sign, String source, Long hospitalInfoId,
                             String deptName) {
        String url = tduckDomain + "/" + formKey + "?sysUserId=" + surveyUserId + "&sign=" + sign + "&source=" + source;
        if (hospitalInfoId != null) {
            url = url + "&hospitalInfoId=" + hospitalInfoId;
        }
        if (StringUtils.isNotBlank(deptName)) {
            url = url + "&deptName=" + deptName;
        }
        return url;
    }

    @Override
    public Result<List<SurveyPlanResult>> surveyPlanResult(SurveyPlanResultParam param) {
        List<DictProduct> products = dictProductMapper.selectList(
                new QueryWrapper<DictProduct>().isNotNull("order_no"));
        // 项目信息
        ProjProjectInfo projectInfo = projectInfoMapper.selectById(param.getProjectInfoId());
        if (projectInfo == null) {
            return Result.fail(ResultEnum.NO_DATA);
        }
        // 查询哪些产品不需要调研 ， 在下方需要把不需要调研的产品过滤出来展示在前端
        List<DictProductExtend> productExtendList = dictProductExtendMapper.selectList(new QueryWrapper<DictProductExtend>().eq("survey_flag", 0).eq("is_deleted", 0));
        List<Long> notSurveyProductIdList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(productExtendList)) {
            notSurveyProductIdList = productExtendList.stream().map(vo -> vo.getYyProductId()).collect(Collectors.toList());
        }
        // 项目实施产品
        List<ProjProductDeliverRecord> deliverRecordList = deliverRecordMapper.findProductDeliverRecordByProjectInfoId(param.getProjectInfoId());
        // 交付工单产品
        Set<Long> productIds = deliverRecordList.stream().map(e -> e.getProductDeliverId()).collect(Collectors.toSet());
        List<BaseIdNameResp> productInfoList = dictProductMapper.findByProductIds(productIds);
        // 产品名称信息
        List<SurveyPlanTaskResp> respList = productInfoList.stream()
                .filter(baseIdNameResp -> !baseIdNameResp.getName().contains("升级云健康-"))
                .map(e -> {
                    SurveyPlanTaskResp resp = new SurveyPlanTaskResp();
                    resp.setYyProductId(e.getId());
                    resp.setProductInfoName(
                            e.getName().endsWith(StrUtil.DASHED) ? e.getName().substring(0, e.getName().length() - 1)
                                    : e.getName());
                    resp.setPid(0L);
                    resp.setCompletionProgress(dictBusinessStatusService.getSurveyPlanStatusDescriptionByStatusId(ResearchStatus.NOT_STARTED.getCode()));
                    resp.setCustomInfoId(projectInfo.getCustomInfoId());
                    resp.setHasChildren(false);
                    return resp;
                }).sorted(Comparator.comparing(
                                (SurveyPlanTaskResp item) -> item.getProductInfoName().contains(StrUtil.DASHED) ? 0 : 1)
                        .thenComparing(SurveyPlanTaskResp::getProductInfoName)).collect(Collectors.toList());
        // 查询调研计划任务
        ProjSurveyPlanDTO projSurveyPlanDTO = new ProjSurveyPlanDTO();
        projSurveyPlanDTO.setProjectInfoId(param.getProjectInfoId());
        projSurveyPlanDTO.setHospitalInfoId(param.getHospitalInfoId());
        List<SurveyPlanTaskResp> surveyPlanList = projSurveyPlanMapper.findSurveyPlan(projSurveyPlanDTO);
        // 没有查询到调研计划任务
        if (CollectionUtils.isEmpty(surveyPlanList)) {
            List<SurveyPlanResult> results = new ArrayList<>();
            for (SurveyPlanTaskResp surveyPlanTaskResp : respList) {
                SurveyPlanResult surveyPlanResult = new SurveyPlanResult();
                surveyPlanResult.setYyProductId(surveyPlanTaskResp.getYyProductId());
                surveyPlanResult.setProductInfoName(surveyPlanTaskResp.getProductInfoName());
                surveyPlanResult.setFormKey(null);
                surveyPlanResult.setStatus(dictBusinessStatusService.getSurveyPlanStatusDescriptionByStatusId(ResearchStatus.NOT_STARTED.getCode()));
                surveyPlanResult.setCompletedNum(0);
                surveyPlanResult.setTotal(0);
                surveyPlanResult.setDetailUrl("");
                surveyPlanResult.setResultUrl("");
                surveyPlanResult.setNeedConfirm(false);
                results.add(surveyPlanResult);
            }
            // 产品排序
            try {
                results.forEach(surveyPlanResult -> {
                    Long orderNo = null;
                    if (ObjectUtil.isNotEmpty(products)) {
                        for (DictProduct d : products) {
                            if (ObjectUtil.isNotEmpty(surveyPlanResult.getYyProductId())
                                    && surveyPlanResult.getYyProductId().equals(d.getYyProductId())) {
                                orderNo = d.getOrderNo();
                            }
                        }
                        if (ObjectUtil.isNotEmpty(orderNo)) {
                            surveyPlanResult.setOrderNo(orderNo);
                        }
                        if (ObjectUtil.isEmpty(orderNo) && surveyPlanResult.getProductInfoName()
                                .contains(StrUtil.DASHED)) {
                            surveyPlanResult.setOrderNo(surveyPlanResult.getYyProductId());
                        }
                    }
                });
                // 排序
                results = results.stream()
                        .sorted(Comparator.comparing(SurveyPlanResult::getOrderNo)
                                .thenComparing(SurveyPlanResult::getYyProductId)
                        )
                        .collect(Collectors.toList());
            } catch (Exception e) {
                log.error("排序异常", e);
            }
            return Result.success(results);
        }
        // 调研计划任务根据产品分组
        Map<Long, List<SurveyPlanTaskResp>> productIdSurveyPlanMap = surveyPlanList.stream()
                .collect(Collectors.groupingBy(ProjSurveyPlan::getYyProductId));
        // 当前项目所有的表单
        List<FmUserForm> userFormList = fmUserFormMapper.selectList(
                new QueryWrapper<FmUserForm>()
                        .eq("project_info_id", param.getProjectInfoId())
                        .ne("is_deleted", 1)
        );
        List<SurveyPlanResult> results = new ArrayList<>();
        // 根据产品遍历
        for (SurveyPlanTaskResp task : respList) {
            SurveyPlanResult surveyPlanResult = new SurveyPlanResult();
            surveyPlanResult.setYyProductId(task.getYyProductId());
            surveyPlanResult.setProductInfoName(task.getProductInfoName());
            // 获取当前产品的表单信息
            FmUserForm fmUserForm = userFormList.stream()
                    .filter(item -> String.valueOf(task.getYyProductId()).equals(item.getYunYingProductId()))
                    .findFirst().orElse(null);
            String formKey;
            if (fmUserForm == null) {
                formKey = null;
            } else {
                formKey = fmUserForm.getFormKey();
            }
            surveyPlanResult.setFormKey(formKey);
            // 当前产品的调研计划
            List<SurveyPlanTaskResp> surveyPlans = productIdSurveyPlanMap.get(task.getYyProductId());
            if (!CollectionUtils.isEmpty(surveyPlans) && !notSurveyProductIdList.contains(task.getYyProductId())) {
                // 总数量
                int total = surveyPlans.size();
                surveyPlanResult.setTotal(total);
                // 已完成的数量
                List<SurveyPlanTaskResp> finishedTask = surveyPlans.stream()
                        .filter(item -> Integer.valueOf(ResearchStatus.FINISHED.getCode())
                                .equals(item.getCompleteStatus())).collect(Collectors.toList());
                surveyPlanResult.setCompletedNum(finishedTask.size());
                // 没有已完成的
                if (CollectionUtils.isEmpty(finishedTask)) {
                    surveyPlanResult.setStatus(dictBusinessStatusService.getSurveyPlanStatusDescriptionByStatusId(ResearchStatus.NOT_STARTED.getCode()));
                } else if (total == finishedTask.size()) {
                    // 都完成了
                    surveyPlanResult.setStatus(dictBusinessStatusService.getSurveyPlanStatusDescriptionByStatusId(ResearchStatus.FINISHED.getCode()));
                } else {
                    surveyPlanResult.setStatus(dictBusinessStatusService.getSurveyPlanStatusDescriptionByStatusId(ResearchStatus.DOING.getCode()));
                }
                // 只有一份调研结果
                if (surveyPlans.size() == 1) {
                    // 表单信息不存在
                    if (StringUtils.isBlank(formKey)) {
                        surveyPlanResult.setDetailUrl("");
                        surveyPlanResult.setResultUrl("");
                    } else {
                        // 表单信息存在
                        String sign = SignUtil.encrypt(formKey + surveyPlans.get(0).getSurveyUserId() + "survey",
                                CharsetUtil.UTF_8);
                        List<FormDataDTO> dataByFormKey = tduckService.getDataByFormKey(formKey,
                                param.getHospitalInfoId());
                        FormDataDTO formDataDTO = dataByFormKey.stream()
                                .filter(item -> "survey".equals(item.getSource()) && "permanent".equals(
                                        item.getSaveType())).findFirst().orElse(null);
                        String url = this.createUrl(formKey, surveyPlans.get(0).getSurveyUserId(), sign, "survey",
                                surveyPlans.get(0).getHospitalInfoId(), surveyPlans.get(0).getDeptName());
                        surveyPlanResult.setDetailUrl(url);
                        surveyPlanResult.setDataId(formDataDTO != null ? formDataDTO.getId() : null);
                        // 最终结果
                        FormDataDTO resultData = dataByFormKey.stream()
                                .filter(item -> "config".equals(item.getSource()) && "permanent".equals(
                                        item.getSaveType())).findFirst().orElse(null);
                        // 表单信息存在
                        String resultSign = resultData != null
                                ? SignUtil.encrypt(formKey + resultData.getSysUserId() + "config", CharsetUtil.UTF_8)
                                : null;
                        String resultUrl = resultData != null
                                ? this.createUrl(formKey, Long.valueOf(resultData.getSysUserId()), resultSign, "config",
                                Long.valueOf(resultData.getHospitalInfoId()), resultData.getDeptName()) : "";
                        surveyPlanResult.setResultUrl(resultUrl);
                    }
                    surveyPlanResult.setNeedConfirm(false);
                } else {
                    // 表单信息不存在
                    if (StringUtils.isBlank(formKey)) {
                        surveyPlanResult.setDetailUrl("");
                        surveyPlanResult.setResultUrl("");
                    } else {
                        List<FormDataDTO> dataByFormKey = tduckService.getDataByFormKey(formKey,
                                param.getHospitalInfoId());
                        FormDataDTO resultData = dataByFormKey.stream()
                                .filter(item -> "config".equals(item.getSource())).findFirst().orElse(null);
                        if (resultData != null) {
                            // 表单信息存在
                            String sign = SignUtil.encrypt(formKey + resultData.getSysUserId() + "config",
                                    CharsetUtil.UTF_8);
                            String url = this.createUrl(formKey, Long.valueOf(resultData.getSysUserId()), sign,
                                    "config", Long.valueOf(resultData.getHospitalInfoId()), resultData.getDeptName());
                            surveyPlanResult.setDetailUrl("");
                            surveyPlanResult.setResultUrl(url);
                        } else {
                            surveyPlanResult.setDetailUrl("");
                            surveyPlanResult.setResultUrl("");
                        }
                    }
                    // 有多份调研结果
                    surveyPlanResult.setNeedConfirm(true);
                }
            } else if (!notSurveyProductIdList.contains(task.getYyProductId())) {
                // 当前产品没有调研计划
                surveyPlanResult.setStatus(dictBusinessStatusService.getSurveyPlanStatusDescriptionByStatusId(ResearchStatus.NOT_STARTED.getCode()));
                surveyPlanResult.setTotal(0);
                surveyPlanResult.setCompletedNum(0);
                surveyPlanResult.setDetailUrl("");
                surveyPlanResult.setResultUrl("");
                surveyPlanResult.setNeedConfirm(false);
            }
            // 二次数据处理 **********************************************************************************************
            // 判断 resultUrl 是否存在数据 并且 是否需要调研。重新赋值数据
            // 不属于这两种情况时 赋值为0
            if (!notSurveyProductIdList.contains(task.getYyProductId())) {
                surveyPlanResult.setCompleteCode(ObjectUtil.isNotEmpty(surveyPlanResult.getResultUrl()));
                surveyPlanResult.setSurveyFlag(1);
            } else if (notSurveyProductIdList.contains(task.getYyProductId())) {
                surveyPlanResult.setCompleteCode(true);
                surveyPlanResult.setSurveyFlag(0);
                surveyPlanResult.setStatus("无需调研");
            }
            results.add(surveyPlanResult);
        }
        // 产品排序
        try {
            results.forEach(surveyPlanResult -> {
                Long orderNo = null;
                for (DictProduct d : products) {
                    if (ObjectUtil.isNotEmpty(surveyPlanResult.getYyProductId())
                            && surveyPlanResult.getYyProductId().equals(d.getYyProductId())) {
                        orderNo = d.getOrderNo();
                    }
                }
                if (ObjectUtil.isNotEmpty(orderNo)) {
                    surveyPlanResult.setOrderNo(orderNo);
                }
                if (ObjectUtil.isEmpty(orderNo) && surveyPlanResult.getProductInfoName().contains(StrUtil.DASHED)) {
                    surveyPlanResult.setOrderNo(surveyPlanResult.getYyProductId());
                }
            });
            // 排序
            results = results.stream()
                    .sorted(Comparator.comparing(SurveyPlanResult::getOrderNo)
                            .thenComparing(SurveyPlanResult::getYyProductId)
                    )
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("排序异常", e);
        }
        return Result.success(results);
    }

    @Resource
    private SysConfigMapper sysConfigMapper;

    @Resource
    private ProjSurveyPlanService surveyPlanService;

    /**
     * 更新调研计划状态
     */
    @Override
    public Result updateSurveyPlanStatus(UpdateSurveyPlanStatusReq updateSurveyPlanStatus) {
        boolean openSurveyAudit = projectInfoService.isOpenSurveyAudit(updateSurveyPlanStatus.getProjectInfoId());
        int count;
        try {
            // 开启后端审核，提交时不能变成待确认
            if (openSurveyAudit && Integer.valueOf(3).equals(updateSurveyPlanStatus.getCompleteStatus())) {
                SysConfig sysConfig = sysConfigMapper.selectConfigByName("AUTO_SUBMIT");
                boolean onlyOneSurveyFlag = surveyPlanService.getOnlyOneSurveyFlag(updateSurveyPlanStatus.getProjectInfoId(), updateSurveyPlanStatus.getHospitalInfoId(), updateSurveyPlanStatus.getYyProductId());

                ProjSurveyPlan projSurveyPlan = new ProjSurveyPlan();
                // 自动提交
                if (sysConfig != null && "open".equals(sysConfig.getConfigValue())) {
                    // 一条调研计划自动提交
                    if (onlyOneSurveyFlag) {
                        // TODO 发消息
                        projSurveyPlan.setCompleteStatus(4);
                    } else {
                        // 多份调研计划改该为已保存，手动选择提交哪一份
                        projSurveyPlan.setCompleteStatus(6);
                    }
                } else {
                    projSurveyPlan.setCompleteStatus(6);
                }
                projSurveyPlan.setUpdaterId(updateSurveyPlanStatus.getSurveyUserId());
                projSurveyPlan.setUpdateTime(new Date());

                count = projSurveyPlanMapper.update(projSurveyPlan, new QueryWrapper<ProjSurveyPlan>()
                                .eq("is_deleted", 0)
                                .eq("project_info_id", updateSurveyPlanStatus.getProjectInfoId())
                                .eq("hospital_info_id", updateSurveyPlanStatus.getHospitalInfoId())
                                .eq("yy_product_id", updateSurveyPlanStatus.getYyProductId())
//                        .eq("survey_user_id", updateSurveyPlanStatus.getSurveyUserId())
                );
                log.info("更新调研计划状态，更新数量：{}", count);
            } else {
                count = projSurveyPlanMapper.updateSurveyPlanStatus(updateSurveyPlanStatus);
                log.info("更新调研计划状态，更新数量：{}", count);
            }

            // 更新项目计划为进行中
            projProjectPlanService.updatePlanAndTodoTaskStatusByProjectAndItemCode(updateSurveyPlanStatus.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_PRODUCT, ProjectPlanStatusEnum.UNDERWAY);
            // 更新我的待办完成数
            SaveOrUpdateTodoTaskParam param = new SaveOrUpdateTodoTaskParam();
            param.setProjectInfoId(updateSurveyPlanStatus.getProjectInfoId());
            param.setHospitalInfoId(updateSurveyPlanStatus.getHospitalInfoId());
            param.setYyProductId(updateSurveyPlanStatus.getYyProductId());
            param.setUserId(updateSurveyPlanStatus.getSurveyUserId());
            param.setCode(DictProjectPlanItemEnum.SURVEY_PRODUCT.getPlanItemCode());
            param.setTotalCount(this.getSurveyPlanTotal(updateSurveyPlanStatus.getProjectInfoId(), updateSurveyPlanStatus.getHospitalInfoId(), updateSurveyPlanStatus.getYyProductId(), updateSurveyPlanStatus.getSurveyUserId()));
            param.setCompleteCount(this.getSurveyPlanComplete(updateSurveyPlanStatus.getProjectInfoId(), updateSurveyPlanStatus.getHospitalInfoId(), updateSurveyPlanStatus.getYyProductId(), updateSurveyPlanStatus.getSurveyUserId()));
            todoTaskService.projectTodoTaskInit(param);

            // 调研完成后 ，检测该项目的调研计划是否全部完成。当全部完成后，更新里程碑节点状态  查询当前项目不是已完成状态的调研计划
            List<ProjSurveyPlan> projSurveyPlans = projSurveyPlanMapper.selectList(new QueryWrapper<ProjSurveyPlan>()
                    .eq("project_info_id", updateSurveyPlanStatus.getProjectInfoId())
                    .ne("complete_status", 1)
            );
            if (projSurveyPlans.isEmpty()) {
                ProjMilestoneInfo milestoneInfo = milestoneInfoService.getMilestoneInfo(
                        updateSurveyPlanStatus.getProjectInfoId(),
                        MilestoneNodeEnum.SURVEY_PRODUCT.getCode());
                // 调研计划全部完成，更新里程碑节点状态
                log.info("调研计划全部完成，更新里程碑节点状态");
                UpdateMilestoneDTO updateMilestoneDTO = new UpdateMilestoneDTO();
                updateMilestoneDTO.setMilestoneStatus(1);
                updateMilestoneDTO.setMilestoneInfoId(milestoneInfo.getMilestoneInfoId());
                milestoneInfoService.updateMilestone(updateMilestoneDTO);
            }
            return Result.success();
        } catch (Exception e) {
            log.error("，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("更新调研计划失败 , " + e.getMessage());
        }
    }

    /**
     * 保存待办任务总数据和详情
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result saveBackLogAndDetail(SaveBackLogAndDetailReq req) {
        log.info("保存待办任务总数据和详情，入参：{}", JSON.toJSONString(req));
        if (CollectionUtil.isEmpty(req.getTaskList()) && CollectionUtil.isEmpty(req.getReportList()) && CollectionUtil.isEmpty(req.getConfigList()) && CollectionUtil.isEmpty(req.getFormList())) {
            return Result.fail("任务列表、报表列表、配置列表、表单列表不能同时为空");
        }

        // 填鸭表单传过来的产品ID集合
        Set<Long> tduckYyProductIdSet = req.getYyProductIdSet();
        SimpleId simpleId = new SimpleId();
        simpleId.setId(req.getProjectInfoId());
        Result<List<BaseIdNameResp>> productResult = this.getProductByProjectInfoId(simpleId);
        // 所有的产品ID及其产品名称
        List<BaseIdNameResp> productByProjectInfoId = productResult.getData();
        // 筛选出填鸭表单传过来的产品ID对应的产品
        List<BaseIdNameResp> yyProductIdList = productByProjectInfoId.stream().filter(item -> tduckYyProductIdSet.contains(item.getId())).collect(Collectors.toList());
        req.setYyProductList(yyProductIdList);
        Long hospitalInfoId = req.getHospitalInfoId();
        ProjHospitalInfo hospitalInfo = projHospitalInfoMapper.selectById(hospitalInfoId);
        boolean mainFlag = hospitalInfo.getHealthBureauFlag() == 1;
        Long projectInfoId = req.getProjectInfoId();
        ProjProjectInfo projectInfo = projectInfoMapper.selectByPrimaryKey(projectInfoId);
        SysUser projectLeader = sysUserMapper.selectById(projectInfo.getProjectLeaderId());
        if (ObjectUtil.isEmpty(projectInfo)) {
            return Result.fail("项目信息不存在");
        }
        Date now = new Date();
        //0. 参数预处理
        //配置、待处理任务、报表、表单
        Map<Long, List<TDuckConfig>> configMap = req.getConfigList().stream().collect(Collectors.groupingBy(TDuckConfig::getYyProductId));
        Map<Long, List<TDuckTask>> taskMap = req.getTaskList().stream().collect(Collectors.groupingBy(TDuckTask::getYyProductId));
        Map<Long, List<TDuckReport>> reportMap = req.getReportList().stream().collect(Collectors.groupingBy(TDuckReport::getProductId));
        Map<Integer, List<TDuckForm>> formMap = req.getFormList().stream().collect(Collectors.groupingBy(TDuckForm::getProductId));
        //2. 配置数据-proj_product_config，proj_product_config_log
        //先删除后存储
        log.info("保存待办任务总数据和详情，处理配置");
        List<ProjProductConfig> configList = req.getConfigList().stream().map(item -> new ProjProductConfigExtend(projectInfo, hospitalInfoId, item, now)).collect(Collectors.toList());
        productConfigMapper.deleteByParam(projectInfoId, hospitalInfoId, yyProductIdList);
        Boolean branchHospital = projectInfoService.isBranchHospital(projectInfoId);
        log.info("生成配置及待办时判断是否为分院，projectInfoId={}，结果={}", projectInfoId, branchHospital);
        if (branchHospital) {
            // 没有按照医院进行配置隔离的产品信息
            List<DictProductExtend> dictProductExtends = dictProductExtendMapper.selectList(new QueryWrapper<DictProductExtend>().eq("is_deleted", 0).eq("config_isolation_by_hospital_flag", 1));
            log.info("生成配置及待办时判断是否为分院，没有按照医院进行配置隔离的产品信息={}", JSON.toJSONString(dictProductExtends));
            if (!CollectionUtils.isEmpty(dictProductExtends)) {
                Set<Long> yyProductIdSet = dictProductExtends.stream().map(DictProductExtend::getYyProductId).collect(Collectors.toSet());
                log.info("保存待办任务总数据和详情，没有按照医院进行配置隔离的产品id={}", yyProductIdSet);
                configList = configList.stream().filter(item -> !yyProductIdSet.contains(item.getYyProductId())).collect(Collectors.toList());
            }
        }
        // 处理患者智能服务产品的交付平台内部配置
        if (!CollectionUtils.isEmpty(configList)) {
            ProjProductConfig projProductConfig = configList.stream().filter(item -> "jfpt".equals(item.getConfigType()) && "hzznPublicNoFunction".equals(item.getConfigCode())).findFirst().orElse(null);
            if (projProductConfig != null) {
                saveOrUpdateInitFunction(projectInfoId, projProductConfig.getConfigValue());
                // 写入患者智能服务功能配置之后，将此条数据删除
                configList.removeIf(item -> "jfpt".equals(item.getConfigType()) && "hzznPublicNoFunction".equals(item.getConfigCode()));
            }
        }
        log.info("生成待办，保存产品配置，configList长度={}", configList.size());
        if (CollectionUtil.isNotEmpty(configList)) {
            productConfigMapper.insertBatch(configList);
        }
        if (CollectionUtil.isNotEmpty(req.getConfigList())) {
            // 查询当前项目、当前医院、当前产品的全部配置数据，code设置成list
            List<ProjProductConfig> configs = productConfigMapper.selectList(new QueryWrapper<ProjProductConfig>().eq("project_info_id", projectInfoId).eq("hospital_info_id", hospitalInfoId).eq("yy_product_id", req.getConfigList().get(0).getYyProductId()));
            List<String> configCodeList = configs.stream().map(ProjProductConfig::getConfigCode).collect(Collectors.toList());
            //存储日志信息
            List<ProjProductConfigLog> configLogList = new ArrayList<>();
            for (ProjProductConfig item : configList) {
                ProjProductConfigLog configLog = new ProjProductConfigLog();
                ProjProductConfigLog productConfigLog = productConfigLogMapper.selectOne(new QueryWrapper<ProjProductConfigLog>().eq("project_info_id", projectInfoId).eq("hospital_info_id", hospitalInfoId).eq("yy_product_id", item.getYyProductId()).eq("config_code", item.getConfigCode()).orderByDesc("update_time").last("limit 1"));
                // 判断是否已经生成过，当生成过后，进行修改不再新增
                if (configCodeList.contains(item.getConfigCode()) && ObjectUtil.isNotEmpty(productConfigLog)) {
                    BeanUtil.copyProperties(item, configLog);
                    configLog.setProductConfigLogId(SnowFlakeUtil.getId());
                    configLog.setUpdateTime(new Date());
                    configLog.setConfigValueOld(productConfigLog.getConfigValueNew());
                    configLog.setConfigValueNew(item.getConfigValue());
                    configLog.setConfigStatus(2);
                    configLogList.add(configLog);
                } else {
                    configLog.setProductConfigLogId(SnowFlakeUtil.getId());
                    BeanUtil.copyProperties(item, configLog);
                    configLog.setConfigValueNew(item.getConfigValue());
                    configLogList.add(configLog);
                }
            }
            if (CollectionUtil.isNotEmpty(configLogList)) {
                productConfigLogMapper.insertBatch(configLogList);
            }
        }
        //3. 待处理任务-proj_product_task
        log.info("保存待办任务总数据和详情，处理待处理任务");
        //不能删除，因为有关联主键的子表数据
        //根据project_info_id,hospital_info_id,yy_product_id 过滤 更新还是新增
        // 产品多次提交调研结果，该产品在已存储数据库的数据比本次生成的待办要多，这种能删除吗？ 删除时是否还要操作其他的表？
        //  如:LIS产品中【是否有报告自助机？？】， 有则生成待办，无则不生成。
        //第一次提交调研结果，选择有。生成了待办。第二次提交调研结果，选择无。 则不生成待办。第一次选择有后生成的待办是 可以删除。
        if (!CollectionUtils.isEmpty(req.getTaskList())) {
            int i = productTaskMapper.deleteDataByParam(projectInfoId, hospitalInfoId, req.getTaskList());
            log.info("删除待办任务明细，删除结果={}", i);
        }
        //定义项目任务文件列表
        List<ProjProjectFile> projectFiles = new ArrayList<>();
        List<ProjProductTaskFileRelation> projectFileRelations = new ArrayList<>();
        //处理项目任务
        req.getTaskList().forEach(item -> {
            ProjProductTask task = new ProjProductTaskExtend(projectInfo, hospitalInfoId, item, now);
            task.setTaskExplainLink(item.getTaskExplainLink());
            log.info("保存待办任务总数据和详情，处理待处理任务，待办数据={}", JSON.toJSONString(task));
            int i = productTaskMapper.insertOrUpdate(task);
            log.info("保存待办任务总数据和详情，处理待处理任务，待办数据={}，结果={}", JSON.toJSONString(task), i);
            // 患者智能服务，添加调研时数据 【数据写死。数据来自调研平台 注释不写，后期干什么的功能都不一定记得】
            // 谢大哥提醒： 此处是患者智能服务生成待办时用于APP审核数据。 此处1是给王海华、刘忠志发送消息。
            // 2是给患者智能服务存储必要的数据写入患者智能服务（ hzzn_deliver）  hzzn_task_16是开通微信商户APP支付  hzzn_task_25 不开通微信商户APP支付
            String taskCode = "hzzn_task_16";
            String taskCodeTw = "hzzn_task_25";
            if (taskCode.equals(task.getTaskCode()) || taskCodeTw.equals(task.getTaskCode())) {
                List<ProjProductTask> projProductTasks = productTaskMapper.selectList(new QueryWrapper<ProjProductTask>().eq("project_info_id", task.getProjectInfoId()).eq("hospital_info_id", task.getHospitalInfoId()).eq("yy_product_id", task.getYyProductId()).eq("task_code", task.getTaskCode()));
                if (projProductTasks != null && !projProductTasks.isEmpty()) {
                    task.setProductTaskId(projProductTasks.get(0).getProductTaskId());
                }
                this.addSurveyImgData(task);
                this.handleHzznTaskData(task);
            }
            // 将项目任务关联文件加入集合（桂杰）
            List<JSONObject> fileList = item.getProjectFileList();
            if (CollUtil.isNotEmpty(fileList)) {
                for (JSONObject jsonObject : fileList) {
                    ProjProjectFile file = jsonObject.toJavaObject(ProjProjectFile.class);
                    file.setProjectFileId(IdGenerator.ins().generator());
                    file.setProjectInfoId(req.getProjectInfoId());
                    file.setMilestoneNodeCode("survey_product");
                    file.setProjectStageCode("stage_survey");
                    projectFiles.add(file);
                    ProjProductTaskFileRelation rel = new ProjProductTaskFileRelation();
                    rel.setTaskFileRelationId(IdGenerator.ins().generator());
                    rel.setProjectFileId(file.getProjectFileId());
                    rel.setProductTaskId(task.getProductTaskId());
                    projectFileRelations.add(rel);
                }
            }
        });
        // 批量执行项目任务关联文件插入操作
        ListUtils.chunkedWithSubIndex(projectFileRelations, 100, subList -> projProductTaskFileRelationMapper.insertBatch(subList));
        ListUtils.chunkedWithSubIndex(projectFiles, 100, subList -> projProjectFileMapper.insertBatch(subList));
        //4. 报表数据-老平台接口-surveyReport/saveOrUpdateReportList
        log.info("保存待办任务总数据和详情，处理报表");
        //判断backLog总表是否需要设置报表
        boolean noReportFlag = false;
        if (CollectionUtil.isNotEmpty(req.getReportList())) {
            // 查询项目下调研阶段报表是否使用的新流程
            List<ProjMilestoneInfo> milestoneInfoReportList = projMilestoneInfoMapper.selectList(new QueryWrapper<ProjMilestoneInfo>().eq("project_info_id", projectInfo.getProjectInfoId()).in("milestone_node_code", MilestoneNodeEnum.SURVEY_REPORT.getCode()).eq("invalid_flag", NumberEnum.NO_0.num()));
            // 打印类报表
            List<ProjSurveyReportUpdateReq> printReportReqList = new ArrayList<>();
            boolean reportNew = milestoneInfoReportList != null && !milestoneInfoReportList.isEmpty() && milestoneInfoReportList.get(0).getIsComponent() != null && !"".equals(milestoneInfoReportList.get(0).getIsComponent());
            if (reportNew) {
                if (mainFlag) {
                    printReportReqList = req.getReportList().stream().filter(item -> "print".equals(item.getPrintType())).map(item -> getImspSavePrintSurveyReportReq(item, hospitalInfoId, projectInfo, now, projectLeader)).collect(Collectors.toList());
                } else {
                    printReportReqList = req.getReportList().stream().filter(item -> item.getDefaultFlag() == 0 && "print".equals(item.getPrintType())).map(item -> getImspSavePrintSurveyReportReq(item, hospitalInfoId, projectInfo, now, projectLeader)).collect(Collectors.toList());
                }
            }

            if (CollectionUtil.isNotEmpty(printReportReqList)) {
                log.info("保存打印报表调用新平台接口，接口入参={}", JSON.toJSONString(printReportReqList));
                Result reportResult = projSurveyReportService.batchReprotSave(printReportReqList);
                log.info("保存打印报表调用新平台接口，接口出参={}", JSON.toJSONString(reportResult));
                if (!reportResult.isSuccess()) {
                    throw new CustomException("打印报表数据保存新平台失败");
                }
            } else {
                noReportFlag = true;
                log.info("保存待办任务总数据和详情，处理报表，分院默认设置无需保存");
            }

            todoTaskService.todoTaskTotalCountSync(projectInfo.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_REPORT.getPlanItemCode());
            todoTaskService.todoTaskTotalCountSync(projectInfo.getProjectInfoId(), DictProjectPlanItemEnum.PREPARAT_REPORT.getPlanItemCode());
        }
        //5. 表单数据-老平台接口-surveyForm/saveOrUpdateFormList
        log.info("保存待办任务总数据和详情，处理表单");
        //判断backLog总表是否需要设置表单
        boolean noFrmFlag = false;
        if (CollectionUtil.isNotEmpty(req.getFormList())) {
            // 查询项目下调研阶段报表是否使用的新流程
            List<ProjMilestoneInfo> milestoneInfoFormList = projMilestoneInfoMapper.selectList(new QueryWrapper<ProjMilestoneInfo>().eq("project_info_id", projectInfo.getProjectInfoId()).in("milestone_node_code", MilestoneNodeEnum.SURVEY_FORM.getCode()).eq("invalid_flag", NumberEnum.NO_0.num()));
            boolean reportNew = milestoneInfoFormList != null && !milestoneInfoFormList.isEmpty() && milestoneInfoFormList.get(0).getIsComponent() != null && !"".equals(milestoneInfoFormList.get(0).getIsComponent());
            List<ProjSurveyFormAddReq> formReqNewList = new ArrayList<>();
            if (reportNew) {
                if (mainFlag) {
                    formReqNewList = req.getFormList().stream().map(item -> getImspSaveSurveyFormReqNew(item, hospitalInfoId, projectInfo, now, projectLeader)).collect(Collectors.toList());
                } else {
                    formReqNewList = req.getFormList().stream().filter(item -> item.getDefaultFlag() == 0).map(item -> getImspSaveSurveyFormReqNew(item, hospitalInfoId, projectInfo, now, projectLeader)).collect(Collectors.toList());
                }
            }
            if (CollectionUtil.isNotEmpty(formReqNewList)) {
                log.info("保存表单调用新平台接口，接口入参={}", JSON.toJSONString(formReqNewList));
                Result formResult = projSurveyFormService.batchFormSave(formReqNewList);
                if (!formResult.isSuccess()) {
                    throw new CustomException("表单数据保存新平台失败");
                }
            } else {
                noFrmFlag = true;
                log.info("保存待办任务总数据和详情，处理表单，分院默认设置无需保存");
            }

            todoTaskService.todoTaskTotalCountSync(projectInfo.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_FORM.getPlanItemCode());
            todoTaskService.todoTaskTotalCountSync(projectInfo.getProjectInfoId(), DictProjectPlanItemEnum.PREPARAT_FORM.getPlanItemCode());
        }
        //1. 待办数据总数据表-proj_product_backlog
        //先删除后存储
        log.info("保存待办任务总数据和详情，处理待办");
        //实际上没有配置、待处理任务、报表、表单的，对应的状态要修改为0 注意：前提要过滤掉jfpt的
        Set<Long> collect = configList.stream().map(ProjProductConfig::getYyProductId).collect(Collectors.toSet());
        List<ProjProductBacklog> backlogList = new ArrayList<>();
        for (BaseIdNameResp baseIdNameResp : yyProductIdList) {
            ProjProductBacklog backlog = getProjProductBacklog(baseIdNameResp, hospitalInfoId, projectInfo, now);
            //实际上没有配置、待处理任务、报表、表单的，对应的状态要修改为0 注意：前提要过滤掉jfpt的
            if (!collect.contains(baseIdNameResp.getId())) {
                backlog.setConfigDataStatus(0);
            } else {
                backlog.setConfigDataStatus(1);
            }
            if (!taskMap.containsKey(baseIdNameResp.getId())) {
                backlog.setTodoTaskStatus(0);
            } else {
                backlog.setTodoTaskStatus(1);
            }
            if ((backlog.getReportDataStatus() == 1) && (reportMap.containsKey(baseIdNameResp.getId()) && !noReportFlag)) {
                backlog.setReportDataStatus(1);
            } else {
                List<ProjSurveyReport> projSurveyReports = new LambdaQueryChainWrapper<>(projSurveyReportMapper)
                        .eq(ProjSurveyReport::getIsDeleted, 0)
                        .eq(ProjSurveyReport::getProjectInfoId, projectInfo.getProjectInfoId())
                        .eq(ProjSurveyReport::getHospitalInfoId, backlog.getHospitalInfoId())
                        .in(ProjSurveyReport::getYyProductId, Arrays.asList(backlog.getYyProductId(), backlog.getYyProductModuleId()))
                        .list();
                // 查询是否已经存在对应的报表数据
                if (CollectionUtil.isNotEmpty(projSurveyReports)) {
                    backlog.setReportDataStatus(1);
                } else {
                    backlog.setReportDataStatus(0);
                }
            }
            if ((backlog.getFormDataStatus() == 1) && (formMap.containsKey(baseIdNameResp.getId().intValue()) && !noFrmFlag)) {
                backlog.setFormDataStatus(1);
            } else {
                // 查询是否存在对应表单数据
                List<ProjSurveyForm> projSurveyForms = new LambdaQueryChainWrapper<>(projSurveyFormMapper)
                        .eq(ProjSurveyForm::getIsDeleted, 0)
                        .eq(ProjSurveyForm::getProjectInfoId, backlog.getProjectInfoId())
                        .eq(ProjSurveyForm::getHospitalInfoId, backlog.getHospitalInfoId())
                        .in(ProjSurveyForm::getYyProductId, Arrays.asList(backlog.getYyProductId(), backlog.getYyProductModuleId()))
                        .list();
                if (CollectionUtil.isNotEmpty(projSurveyForms)) {
                    backlog.setFormDataStatus(1);
                } else {
                    backlog.setFormDataStatus(0);
                }
            }
            ProjProductBacklog apply = backlog;
            backlogList.add(apply);
        }
        log.info("生成待办任务，backlogList长度={}", backlogList.size());
        if (CollectionUtil.isNotEmpty(backlogList)) {
            productBacklogMapper.deleteByParam(projectInfoId, hospitalInfoId, yyProductIdList);
            log.info("生成待办任务，backlogList={}", JSON.toJSONString(backlogList));
            productBacklogMapper.insertBatch(backlogList);
        }
        // 6.生成待办的接口要往milestone_task、milestone_task_detail表写数据； 使用到其他表csm.proj_product_deliver_record
        //0是项目经理确认，1是最终版本提交 ,最终版本提交不需要重新生成任务,但是需要修改task_detail的完成状态
        if (req.getCompleteFlag() == 0) {
            try {
                this.saveMilestoneTaskAndDetail(req, hospitalInfoId, projectInfo, now);
            } catch (Exception e) {
                log.error("生成待办任务失败", e);
                throw new CustomException("里程碑任务生成失败");
            }
        } else if (req.getCompleteFlag() == 1) {
            List<ProjMilestoneTask> milestoneTasks = projMilestoneTaskMapper.selectList(new QueryWrapper<ProjMilestoneTask>().eq("project_info_id", projectInfoId).eq("milestone_node_code", PREPARAT_PRODUCT));
            if (CollectionUtil.isNotEmpty(milestoneTasks)) {
                List<Long> productIds = req.getYyProductList().stream().map(BaseIdNameResp::getId).collect(Collectors.toList());
                projMilestoneTaskDetailMapper.updateCompleteStatus(productIds, milestoneTasks.stream().map(ProjMilestoneTask::getMilestoneTaskId).collect(Collectors.toList()));
            }
        }
        boolean planModel = projectConfigService.isPlanModel(req.getProjectInfoId());
        if (planModel) {
            projProjectPlanService.projectPlanTotalCountSync(req.getProjectInfoId(), DictProjectPlanItemEnum.PREPARAT_PRODUCT.getPlanItemCode());
        }
        return Result.success();
    }

    /**
     * 组装数据
     *
     * @param item
     * @param hospitalInfoId
     * @param projectInfo
     * @param now
     * @param projectLeader
     * @return
     */
    private ProjSurveyFormAddReq getImspSaveSurveyFormReqNew(TDuckForm item, Long hospitalInfoId, ProjProjectInfo projectInfo, Date now, SysUser projectLeader) {
        ProjSurveyFormAddReq req = new ProjSurveyFormAddReq();
        BeanUtil.copyProperties(item, req);
        //全部使用新平台id，老平台单独处理id
        req.setCustomerInfoId(projectInfo.getCustomInfoId());
        req.setCreaterId(Long.valueOf(projectLeader.getUserYunyingId()));
        req.setUpdaterId(Long.valueOf(projectLeader.getUserYunyingId()));
        req.setCreateTime(now);
        req.setUpdateTime(now);
        req.setSurveyFinishTime(new Timestamp(now.getTime()));
        req.setSurveyImgs(item.getImglist());
        req.setOnlineEssential(Integer.valueOf(item.getOnlineEssential()));
        req.setHospitalInfoId(hospitalInfoId);
        req.setProjectInfoId(projectInfo.getProjectInfoId());
        if (ObjectUtil.isNotEmpty(item.getProductId())) {
            req.setYyProductId(Long.valueOf(item.getProductId()));
        }
        req.setCloudProductCode(item.getAppCode());
        req.setFormPageUrl(item.getFormUrl());
        req.setFormSource(FormSourceType.PRODUCTSURVEY.getCode());
        return req;
    }

    /**
     * 新平台打印类报表保存
     */
    private ProjSurveyReportUpdateReq getImspSavePrintSurveyReportReq(TDuckReport item, Long hospitalInfoId, ProjProjectInfo projectInfo, Date now, SysUser projectLeader) {
        ProjSurveyReportUpdateReq report = new ProjSurveyReportUpdateReq();
        BeanUtil.copyProperties(item, report);
        //全部使用新平台id，老平台单独处理id
        report.setCustomerInfoId(projectInfo.getCustomInfoId());
        report.setCreaterId(Long.valueOf(projectLeader.getUserYunyingId()));
        report.setUpdaterId(Long.valueOf(projectLeader.getUserYunyingId()));
        report.setCreateTime(now);
        report.setUpdateTime(now);
        report.setSurveyImgs(item.getImglist());
        report.setOnlineEssential(Integer.valueOf(item.getOnlineEssential()));
        report.setHospitalInfoId(hospitalInfoId);
        report.setProjectInfoId(projectInfo.getProjectInfoId());
        report.setYyProductId(item.getProductId());
        report.setPrintDataCode(item.getPrintDataCode());
        report.setReportFileTag(item.getReportFileTag());
        return report;
    }

    /**
     * 保存图片
     */
    @Override
    public void addSurveyImgData(ProjProductTask task) {
        try {
            projProductTaskFileRelationMapper.delete(new QueryWrapper<ProjProductTaskFileRelation>().eq("product_task_id", task.getProductTaskId()));
            if (StringUtils.isNotBlank(task.getSurveyValue())) {
                String surveyValue = task.getSurveyValue();
                Gson gson = new Gson();
                Type listType = new TypeToken<List<Map<String, String>>>() {
                }.getType();
                List<Map<String, String>> list = gson.fromJson(surveyValue, listType);
                if (CollectionUtil.isNotEmpty(list)) {
                    for (Map<String, String> map : list) {
                        String fileName = String.valueOf(map.get("name"));
                        String url = String.valueOf(map.get("url"));
                        String fileUrl = url;
                        ProjProjectFile file = new ProjProjectFile();
                        file.setProjectInfoId(task.getProjectInfoId());
                        file.setProjectFileId(SnowFlakeUtil.getId());
                        file.setProjectStageCode("stage_survey");
                        file.setMilestoneNodeCode("submit_survey_report");
                        file.setFileName(fileName);
                        file.setFilePath(fileUrl);
                        file.setIsDeleted(0);
                        file.setFileDesc("-");
                        projProjectFileMapper.insert(file);
                        ProjProductTaskFileRelation projProductTaskFileRelation = new ProjProductTaskFileRelation();
                        projProductTaskFileRelation.setProductTaskId(task.getProductTaskId());
                        projProductTaskFileRelation.setProjectFileId(file.getProjectFileId());
                        projProductTaskFileRelation.setTaskFileRelationId(SnowFlakeUtil.getId());
                        projProductTaskFileRelationMapper.insert(projProductTaskFileRelation);
                    }
                }
            }
        } catch (Exception e) {
            log.error("保存图片失败", e);
        }
    }

    private void saveOrUpdateInitFunction(Long projectInfoId, String initFunction) {
        try {
            // 替换传入的 实施地客户id 和项目id 。 该方法只适用于患者智能服务
            TmpProjectNewVsOld tmpProjectNewVsOld = tmpProjectNewVsOldMapper.selectOne(new QueryWrapper<TmpProjectNewVsOld>().eq("new_project_info_id", projectInfoId));
            ProjProjectInfo projectInfo = projectInfoService.selectByPrimaryKey(projectInfoId);
            String deliverId = "";
            if (tmpProjectNewVsOld != null) {
                List<HzznDeliver> hzznDeliverList = hzznDeliverService.getHzznDeliver(tmpProjectNewVsOld.getOldCustomId().toString(), tmpProjectNewVsOld.getOldProjectInfoId().toString());
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(hzznDeliverList)) {
                    deliverId = hzznDeliverList.get(0).getId();
                    hzzDeliverPublicService.saveOrUpdateInitFunction(deliverId, initFunction);
                }
            }
            if (StringUtils.isEmpty(deliverId)) {
                deliverId = String.valueOf(SnowFlakeUtil.getId());
                HzznDeliver hzznDeliver = new HzznDeliver();
                hzznDeliver.setId(deliverId);
                hzznDeliver.setProjectId(String.valueOf(tmpProjectNewVsOld.getOldProjectInfoId()));
                hzznDeliver.setCustomerId(String.valueOf(tmpProjectNewVsOld.getOldCustomId()));
                hzznDeliver.setState(0);
                hzznDeliver.setOperateType(projectInfo.getUpgradationType() == 1 ? 1 : 0);
                hzznDeliver.setCreateTime(new Date());
                hzznDeliver.setUpdateTime(new Date());
                hzznDeliverService.save(hzznDeliver);
                hzzDeliverPublicService.saveOrUpdateInitFunction(deliverId, initFunction);
            }
        } catch (Exception e) {
            log.error("向hzz_deliver表中写入数据，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
    }

    /**
     * 处理患者智能服务数据
     *
     * @param task
     */
    private void handleHzznTaskData(ProjProductTask task) {
        try {
            // 替换传入的 实施地客户id 和项目id 。 该方法只适用于患者智能服务
            TmpProjectNewVsOld tmpProjectNewVsOld = tmpProjectNewVsOldMapper.selectOne(new QueryWrapper<TmpProjectNewVsOld>().eq("new_project_info_id", task.getProjectInfoId()));
            ProjProjectInfo projectInfo = projectInfoMapper.selectByPrimaryKey(task.getProjectInfoId());
            String deliverId = "";
            if (tmpProjectNewVsOld != null) {
                List<HzznDeliver> hzznDeliverList = hzznDeliverService.getHzznDeliver(tmpProjectNewVsOld.getOldCustomId().toString(), tmpProjectNewVsOld.getOldProjectInfoId().toString());
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(hzznDeliverList)) {
                    deliverId = hzznDeliverList.get(0).getId();
                }
            }
            if (StringUtils.isEmpty(deliverId)) {
                deliverId = String.valueOf(SnowFlakeUtil.getId());
                HzznDeliver hzznDeliver = new HzznDeliver();
                hzznDeliver.setId(deliverId);
                hzznDeliver.setProjectId(String.valueOf(tmpProjectNewVsOld.getOldProjectInfoId()));
                hzznDeliver.setCustomerId(String.valueOf(tmpProjectNewVsOld.getOldCustomId()));
                hzznDeliver.setState(0);
                hzznDeliver.setOperateType(projectInfo.getUpgradationType() == 1 ? 1 : 0);
                hzznDeliver.setCreateTime(new Date());
                hzznDeliver.setUpdateTime(new Date());
                hzznDeliverService.save(hzznDeliver);
            }

            // 发送APP消息，判断是否可发送，避免无效发送
            Boolean sendFlag = false;
            try {
                List<ProjProjectReviewRecord> listCodes = projProjectReviewRecordMapper.selectList(
                        new QueryWrapper<ProjProjectReviewRecord>().eq("project_info_id", tmpProjectNewVsOld.getNewProjectInfoId())
                                .eq("item_code", "entry_payment_instrument"));
                if (listCodes != null && listCodes.size() > 0) {
                    sendFlag = true;
                }
            } catch (Exception e) {
                log.error("查询报错", e.getMessage());
            }

            String key = "hzzn" + task.getProductTaskId();
            Object value = redisUtil.get(key);
            if (ObjectUtils.isEmpty(value) && sendFlag) {
                SysUser sysUser = sysUserMapper.selectById(projectInfo.getProjectLeaderId());
                String userName = sysUser != null ? sysUser.getUserName() : "";
                // 发送企业微信消息给刘忠智， 王海华
                String content = "【" + projectInfo.getProjectName() + "】" + "APP支付开通截图已上传，请您尽快审核！项目经理【" + userName + "】";
                MessageParam messageParam = new MessageParam();
                messageParam.setProjectInfoId(task.getProjectInfoId());
                messageParam.setContent(content);
                messageParam.setTitle("【" + projectInfo.getProjectName() + "】" + "APP支付开通审核提醒");
                // 发送消息的人 刘忠志， 王海华
                List<Long> sysUserIds = sysUserMapper.selectUserIdByAccount(null);
                String userIds = sysUserIds.stream().map(String::valueOf).collect(Collectors.joining(","));
                // TODO: 发消息的地址===https://imsp.msunhis.com/csm-front-mobile/appAudit?productTaskId=490628096959807488&sysUserId=464993504059150337
                String businessUrl = platformUrl + "appAudit?projectInfoId=" + task.getProjectInfoId() + "&productTaskId="
                        + task.getProductTaskId();
                Long msgInfoId = messageInfoService.insert(businessUrl, sysUser.getSysUserId());
                String url = weChatAuthUrl + "?state=" + msgInfoId;
                messageParam.setUrl(url);
                messageParam.setMessageTypeId(7003L);
                messageParam.setMessageToCategory(MsgToCategory.SINGLE_PERSON.getCode());
                messageParam.setSysUserIds(sysUserIds);
                sendMessageService.sendMessage(messageParam);
                redisUtil.set(key, "1", 60 * 60 * 24);
            }
        } catch (Exception e) {
            log.error("处理患者智能服务数据失败", e);
        }
    }

    // 构造待办数据
    private @NotNull
    ProjProductBacklog getProjProductBacklog(BaseIdNameResp item, Long hospitalInfoId,
                                             ProjProjectInfo projectInfo, Date now) {
        ProjProductBacklog backlog = new ProjProductBacklog();
        backlog.setProductBacklogId(SnowFlakeUtil.getId());
        backlog.setProductName(item.getName());
        backlog.setHospitalInfoId(hospitalInfoId);
        backlog.setProjectInfoId(projectInfo.getProjectInfoId());
        backlog.setCreaterId(projectInfo.getProjectLeaderId());
        backlog.setUpdaterId(projectInfo.getProjectLeaderId());
        backlog.setCreateTime(now);
        backlog.setUpdateTime(now);
        backlog.setIsDeleted(0);
        //产品名称带有‘-’ 标识是模块
        if (item.getName().contains(StrUtil.DASHED)) {
            // 模块
            // 当产品数据为空时  表明是模块产品
            DictProductVsModules modules = productVsModulesMapper.selectOne(new QueryWrapper<DictProductVsModules>().eq("yy_module_id", item.getId()));
            if (ObjectUtil.isEmpty(modules)) {
                log.error("生成产品待办-模块产品数据不存在,模块id-{}", item.getId());
                throw new CustomException("生成产品待办-模块产品数据不存在");
            }
            backlog.setYyProductModuleId(item.getId());
            backlog.setYyProductId(modules.getYyProductId());
        } else {
            // 产品
            backlog.setYyProductId(item.getId());
            backlog.setYyProductModuleId(-1L);
        }
        //2	baseData 基础数据
        //4	setting 配置
        //8	other 待处理任务
        //5	equip 设备
        //6	report 报表
        //7	form 表单
        //0 不包含  1 未完成  2 已完成
        GetProductJobMenuDetailParam dto = new GetProductJobMenuDetailParam();
        dto.setUpgradationType(dictProductVsDeliverService.getUpgradationTypeByDeliverProductId(item.getId(), projectInfo.getProjectInfoId()));
        dto.setYyProductId(item.getId());
        dto.setPrepareUseFlag(1);
        List<ConfigProductJobMenuDetailVO> menuDetailList = configProductJobMenuDetailMapper.getProductJobMenuDetail(dto);
        List<Long> jobMenuIdList = menuDetailList.stream().map(ConfigProductJobMenuDetailVO::getProductJobMenuId)
                .collect(Collectors.toList());
        backlog.setBaseDataStatus(jobMenuIdList.contains(2L) ? 1 : 0);
        backlog.setConfigDataStatus(jobMenuIdList.contains(4L) ? 1 : 0);
        backlog.setTodoTaskStatus(jobMenuIdList.contains(8L) ? 1 : 0);
        //查询是否已有backLog数据
        ProjProductBacklogDTO param = new ProjProductBacklogDTO();
        param.setProjectInfoId(projectInfo.getProjectInfoId());
        param.setYyProductId(item.getId());
        param.setHospitalInfoId(hospitalInfoId);
        //报表和表单 使用原数据
        ProjProductBacklog oldBacklog = productBacklogMapper.selectByParam(param);
        if (ObjectUtil.isNotEmpty(oldBacklog)) {
            backlog.setFormDataStatus(oldBacklog.getFormDataStatus());
            backlog.setReportDataStatus(oldBacklog.getReportDataStatus());
        } else {
            backlog.setFormDataStatus(jobMenuIdList.contains(7L) ? 1 : 0);
            backlog.setReportDataStatus(jobMenuIdList.contains(6L) ? 1 : 0);
        }
        return backlog;
    }

    @Override
    public Result<List<BaseIdNameResp>> getProductByProjectInfoId(SimpleId param) {
        // 项目信息
        ProjProjectInfo projectInfo = projectInfoMapper.selectById(param.getId());
        if (projectInfo == null) {
            return Result.fail(ResultEnum.NO_DATA);
        }
        // 项目实施产品
        List<ProjProductDeliverRecord> deliverRecordList = deliverRecordMapper.findProductDeliverRecordByProjectInfoId(param.getId());
        // 交付工单产品
        Set<Long> productIds = deliverRecordList.stream().map(e -> e.getProductDeliverId()).collect(Collectors.toSet());
        List<BaseIdNameResp> productInfoList = dictProductMapper.findByProductIds(productIds);
        // 产品名称信息
        List<SurveyPlanTaskResp> respList = productInfoList.stream().filter(baseIdNameResp -> !baseIdNameResp.getName().contains("升级云健康-")).map(e -> {
            SurveyPlanTaskResp resp = new SurveyPlanTaskResp();
            resp.setYyProductId(e.getId());
            resp.setProductInfoName(e.getName().endsWith(StrUtil.DASHED) ? e.getName().substring(0, e.getName().length() - 1) : e.getName());
            resp.setPid(0L);
            resp.setCompletionProgress(dictBusinessStatusService.getSurveyPlanStatusDescriptionByStatusId(ResearchStatus.NOT_STARTED.getCode()));
            resp.setCustomInfoId(projectInfo.getCustomInfoId());
            resp.setHasChildren(false);
            return resp;
        }).sorted(Comparator.comparing((SurveyPlanTaskResp item) -> item.getProductInfoName().contains(StrUtil.DASHED) ? 0 : 1).thenComparing(SurveyPlanTaskResp::getProductInfoName)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(respList)) {
            return Result.success(new ArrayList<>(), "没有获取到数据");
        }
        List<BaseIdNameResp> collect = respList.stream().filter(item -> item.getPid() == 0).map(item -> {
            BaseIdNameResp baseIdNameResp = new BaseIdNameResp();
            baseIdNameResp.setId(item.getYyProductId());
            baseIdNameResp.setName(item.getProductInfoName());
            return baseIdNameResp;
        }).collect(Collectors.toList());
        return Result.success(collect);
    }

    /**
     * 保存里程碑任务和明细
     */
    @Override
    public void saveMilestoneTaskAndDetail(SaveBackLogAndDetailReq saveBackLogAndDetailReq, Long hospitalInfoId, ProjProjectInfo projectInfo, Date now) {
        // 里程碑任务-list只有一条数据
        List<ProjMilestoneTask> list = getProjMilestoneTaskList(hospitalInfoId, projectInfo, now);
        List<ProjMilestoneTask> listOld = null;
        // 查看是否有项目数据任务
        if (CollectionUtil.isNotEmpty(list)) {
            listOld = projMilestoneTaskMapper.selectList(new QueryWrapper<ProjMilestoneTask>().eq("hospital_info_id", list.get(0).getHospitalInfoId()).eq("project_info_id", list.get(0).getProjectInfoId()).eq("milestone_node_code", list.get(0).getMilestoneNodeCode()));
            if (CollectionUtil.isEmpty(listOld)) {
                projMilestoneTaskMapper.insertBatch(list);
            }
        }
        if (CollectionUtil.isNotEmpty(listOld)) {
            list = listOld;
        }
        List<ProjMilestoneTaskDetail> listDetail = getProjMilestoneTaskDetailList(saveBackLogAndDetailReq, hospitalInfoId, projectInfo, now, list);
        List<ProjMilestoneTaskDetail> listDetailOld = projMilestoneTaskDetailMapper.selectList(new QueryWrapper<ProjMilestoneTaskDetail>().eq("is_deleted", 0).eq("milestone_task_id", list.get(0).getMilestoneTaskId()));
        //过滤掉已存在的老数据
        Set<Long> oldProductIdSet = listDetailOld.stream().map(old -> old.getProductDeliverId()).collect(Collectors.toSet());
        listDetail.removeIf(e -> oldProductIdSet.contains(e.getProductDeliverId()));
        if (CollectionUtil.isNotEmpty(listDetail)) {
            projMilestoneTaskDetailMapper.insertBatch(listDetail);
        }
    }

    /**
     * 生成里程碑待办任务明细
     */
    private List<ProjMilestoneTaskDetail> getProjMilestoneTaskDetailList(SaveBackLogAndDetailReq saveBackLogAndDetailReq, Long hospitalInfoId, ProjProjectInfo projectInfo, Date now, List<ProjMilestoneTask> listMain) {
        List<BaseIdNameResp> yyProductIdList = saveBackLogAndDetailReq.getYyProductList();
        List<ProjMilestoneInfo> projMilestoneInfoList = projMilestoneInfoMapper.selectList(new QueryWrapper<ProjMilestoneInfo>().eq("project_info_id", projectInfo.getProjectInfoId()).eq("invalid_flag", 0).eq("milestone_node_code", PREPARAT_PRODUCT));
        if (projMilestoneInfoList.isEmpty()) {
            log.error("里程碑任务详情不存在{}", PREPARAT_PRODUCT);
            throw new CustomException("里程碑任务详情不存在");
        }
        List<ProjMilestoneTaskDetail> list = new ArrayList<>();
        for (BaseIdNameResp yyProductId : yyProductIdList) {
            List<ProjProductDeliverRecord> projProductDeliverRecordList = projProductDeliverRecordMapper.selectList(new QueryWrapper<ProjProductDeliverRecord>().eq("product_deliver_id", yyProductId.getId()).eq("project_info_id", projectInfo.getProjectInfoId()).eq("is_deleted", 0));
            if (projProductDeliverRecordList.isEmpty()) {
                log.error("项目实施产品记录表数据不存在{}");
                throw new CustomException("项目实施产品记录表数据不存在");
            }
            ProjMilestoneTaskDetail projMilestoneTaskDetail = new ProjMilestoneTaskDetail();
            projMilestoneTaskDetail.setMilestoneTaskDetailId(SnowFlakeUtil.getId());
            projMilestoneTaskDetail.setMilestoneTaskId(listMain.get(0).getMilestoneTaskId());
            projMilestoneTaskDetail.setProductDeliverId(yyProductId.getId());
            projMilestoneTaskDetail.setProductDeliverRecordId(projProductDeliverRecordList.get(0).getProductDeliverId());
            projMilestoneTaskDetail.setCreaterId(hospitalInfoId);
            projMilestoneTaskDetail.setUpdaterId(hospitalInfoId);
            projMilestoneTaskDetail.setIsDeleted(0);
            projMilestoneTaskDetail.setCreateTime(now);
            projMilestoneTaskDetail.setUpdateTime(now);
            projMilestoneTaskDetail.setCompleteStatus(0);
            list.add(projMilestoneTaskDetail);
        }
        return list;
    }

    /**
     * 生成里程碑待办任务
     *
     * @param hospitalInfoId
     * @param projectInfo
     * @param now
     * @return
     */
    private List<ProjMilestoneTask> getProjMilestoneTaskList(Long hospitalInfoId, ProjProjectInfo projectInfo, Date now) {
        List<ProjMilestoneInfo> projMilestoneInfoList = projMilestoneInfoMapper.selectList(new QueryWrapper<ProjMilestoneInfo>().eq("project_info_id", projectInfo.getProjectInfoId()).eq("invalid_flag", 0).eq("milestone_node_code", PREPARAT_PRODUCT));
        if (projMilestoneInfoList.isEmpty()) {
            log.error("里程碑不存在{}", PREPARAT_PRODUCT);
            throw new CustomException("生成产品待办-模块产品数据不存在");
        }
        List<ProjMilestoneTask> list = new ArrayList<>();
        ProjMilestoneTask milestoneTask = new ProjMilestoneTask();
        milestoneTask.setMilestoneTaskId(SnowFlakeUtil.getId());
        milestoneTask.setProjectInfoId(projectInfo.getProjectInfoId());
        milestoneTask.setHospitalInfoId(hospitalInfoId);
        milestoneTask.setCustomerInfoId(projectInfo.getCustomInfoId());
        // 里程碑节点
        milestoneTask.setMilestoneInfoId(projMilestoneInfoList.get(0).getMilestoneInfoId());
        milestoneTask.setMilestoneNodeCode(PREPARAT_PRODUCT);
        milestoneTask.setProjectStageId(projMilestoneInfoList.get(0).getProjectStageId());
        milestoneTask.setResultSourceId(0L);
        milestoneTask.setIsDeleted(0);
        milestoneTask.setCompleteStatus(0);
        milestoneTask.setCreaterId(projectInfo.getProjectLeaderId());
        milestoneTask.setUpdaterId(projectInfo.getProjectLeaderId());
        milestoneTask.setCreateTime(now);
        milestoneTask.setUpdateTime(now);
        milestoneTask.setProjectStageCode(projMilestoneInfoList.get(0).getProjectStageCode());
        list.add(milestoneTask);
        return list;
    }

    @Override
    public Boolean isConfirmData(Long projectInfoId) {
        Boolean flag = projectInfoService.isBranchHospital(projectInfoId);
        if (flag) {
            Long count = projSurveyPlanMapper.selectCount(new QueryWrapper<ProjSurveyPlan>().eq("project_info_id", projectInfoId).eq("complete_status", NumberEnum.NO_0.num()).eq("is_deleted", NumberEnum.NO_0.num()));
            return count > 0;
        }
        return false;
    }

    /**
     * 引用调研结果
     *
     * @param param
     * @return
     * @throws Exception
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> copySurveyResult(CopySurveyResultParam param) throws Exception {
        // 查询需要复制的医院的调研计划
        List<ProjSurveyPlan> sourceSurveyPlans = projSurveyPlanMapper.selectList(new QueryWrapper<ProjSurveyPlan>()
                .eq("project_info_id", param.getProjectInfoId())
                .eq("hospital_info_id", param.getSourceHospitalInfoId())
                .eq("is_deleted", 0)
                .in("yy_product_id", param.getYyProductIds()));
        if (CollectionUtils.isEmpty(sourceSurveyPlans)) {
            Result<Boolean> result = new Result<>();
            result.setCode(ResultEnum.FAIL.getIndex());
            result.setSuccess(false);
            result.setData(false);
            result.setMsg("被引用调研结果的医院不存在调研计划");
            return result;
        }
        List<Long> notSurveyYyProductId = dictProductService.getNotSurveyYyProductId();
        // 把不需要调研的从调研计划中过滤掉
        if (!CollectionUtils.isEmpty(notSurveyYyProductId)) {
            sourceSurveyPlans = sourceSurveyPlans.stream().filter(item -> !notSurveyYyProductId.contains(item.getYyProductId())).collect(Collectors.toList());
        }
        // 未完成调研的调研计划对应的产品信息ID
        List<Long> noComplateYyProductIdSet = sourceSurveyPlans.stream().filter(projSurveyPlan -> Integer.valueOf(0).equals(projSurveyPlan.getCompleteStatus())).map(ProjSurveyPlan::getYyProductId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(noComplateYyProductIdSet)) {
            SimpleId simpleId = new SimpleId();
            simpleId.setId(param.getProjectInfoId());
            // 当前项目下所有产品
            Result<List<BaseIdNameResp>> productResult = this.getProductByProjectInfoId(simpleId);
            if (productResult != null && !CollectionUtils.isEmpty(productResult.getData())) {
                // 未完成产品的名称
                List<String> productNameList = productResult.getData().stream().filter(item -> noComplateYyProductIdSet.contains(item.getId())).map(BaseIdNameResp::getName).collect(Collectors.toList());
                Result<Boolean> result = new Result<>();
                result.setCode(ResultEnum.FAIL.getIndex());
                result.setSuccess(false);
                result.setData(false);
                result.setMsg("被引用调研结果的医院存在未调研完成的产品。产品名称：" + String.join("，", productNameList));
                return result;
            }
            Result<Boolean> result = new Result<>();
            result.setCode(ResultEnum.FAIL.getIndex());
            result.setSuccess(false);
            result.setData(false);
            result.setMsg("被引用调研结果的医院存在未调研完成的产品。产品ID：" + noComplateYyProductIdSet.stream().map(String::valueOf).collect(Collectors.joining("，")));
            return result;
        }
        // 组装需要复制的数据
        List<ProjSurveyPlan> needAdd = new ArrayList<>();
        List<ProjSurveyPlan> needUpdate = new ArrayList<>();
        // 当前登录用户
        SysUserVO currentUser = userHelper.getCurrentUser();
        Long sysUserId = currentUser.getSysUserId();
        // 组装需要更新及修改的数据
        for (Long targetHospitalInfoId : param.getTargetHospitalInfoIds()) {
            List<ProjSurveyPlan> targetSurveyPlans = projSurveyPlanMapper
                    .selectList(new QueryWrapper<ProjSurveyPlan>()
                            .eq("project_info_id", param.getProjectInfoId())
                            .eq("hospital_info_id", targetHospitalInfoId)
                            .eq("is_deleted", 0)
                            .in("yy_product_id", param.getYyProductIds()));
            // 目标医院没有调研计划，完全按照来源医院的调研计划复制即可
            if (CollectionUtils.isEmpty(targetSurveyPlans)) {
                for (ProjSurveyPlan sourceSurveyPlan : sourceSurveyPlans) {
                    ProjSurveyPlan copySurveyPlan = new ProjSurveyPlan();
                    BeanUtils.copyProperties(sourceSurveyPlan, copySurveyPlan);
                    copySurveyPlan.setSurveyPlanId(SnowFlakeUtil.getId());
                    copySurveyPlan.setHospitalInfoId(targetHospitalInfoId);
                    copySurveyPlan.setSurveyUserId(sysUserId);
                    copySurveyPlan.setCompleteStatus(3);
                    copySurveyPlan.setActualCompTime(new Date());
                    copySurveyPlan.setIsDeleted(0);
                    copySurveyPlan.setCreaterId(sysUserId);
                    copySurveyPlan.setCreateTime(new Date());
                    copySurveyPlan.setUpdaterId(sysUserId);
                    copySurveyPlan.setUpdateTime(new Date());
                    needAdd.add(copySurveyPlan);
                }
            } else {
                for (ProjSurveyPlan sourceSurveyPlan : sourceSurveyPlans) {
                    // 去目标医院的调研任务计划里找与来源医院同一个产品同一个科室的调研计划，如果能找到，更新目标医院的调研计划，否则新增
                    ProjSurveyPlan targetHospitalSurveyPlan = targetSurveyPlans.stream().filter(targetSurveyPlan -> targetSurveyPlan.getYyProductId().equals(sourceSurveyPlan.getYyProductId()) && targetSurveyPlan.getDeptName().equals(sourceSurveyPlan.getDeptName())).findFirst().orElse(null);
                    // 新增
                    if (targetHospitalSurveyPlan == null) {
                        ProjSurveyPlan copySurveyPlan = new ProjSurveyPlan();
                        BeanUtils.copyProperties(sourceSurveyPlan, copySurveyPlan);
                        copySurveyPlan.setSurveyPlanId(SnowFlakeUtil.getId());
                        copySurveyPlan.setHospitalInfoId(targetHospitalInfoId);
                        copySurveyPlan.setSurveyUserId(sysUserId);
                        copySurveyPlan.setCompleteStatus(3);
                        copySurveyPlan.setActualCompTime(new Date());
                        copySurveyPlan.setIsDeleted(0);
                        copySurveyPlan.setCreaterId(sysUserId);
                        copySurveyPlan.setCreateTime(new Date());
                        copySurveyPlan.setUpdaterId(sysUserId);
                        copySurveyPlan.setUpdateTime(new Date());
                        needAdd.add(copySurveyPlan);
                    } else {
                        targetHospitalSurveyPlan.setSurveyUserId(sysUserId);
                        targetHospitalSurveyPlan.setCompleteStatus(3);
                        targetHospitalSurveyPlan.setActualCompTime(new Date());
                        targetHospitalSurveyPlan.setIsDeleted(0);
                        targetHospitalSurveyPlan.setUpdaterId(sysUserId);
                        targetHospitalSurveyPlan.setUpdateTime(new Date());
                        needUpdate.add(targetHospitalSurveyPlan);
                    }
                }
            }
        }
        List<ProjSurveyPlan> allData = new ArrayList<>();
        if (!CollectionUtils.isEmpty(needAdd)) {
            this.projSurveyPlanMapper.batchInsert(needAdd);
            allData.addAll(needAdd);
        }
        if (!CollectionUtils.isEmpty(needUpdate)) {
            for (ProjSurveyPlan updateInfo : needUpdate) {
                this.projSurveyPlanMapper.updateById(updateInfo);
            }
            allData.addAll(needUpdate);
        }
        List<ProductIdAndDeptNameParam> collect = allData.stream().map(item -> {
            ProductIdAndDeptNameParam copySurveyResultProductParam = new ProductIdAndDeptNameParam();
            copySurveyResultProductParam.setDeptName(item.getDeptName());
            copySurveyResultProductParam.setYyProductId(String.valueOf(item.getYyProductId()));
            return copySurveyResultProductParam;
        }).collect(Collectors.toList());
        TduckCopySurveyResultParam tduckCopySurveyResultParam = new TduckCopySurveyResultParam();
        tduckCopySurveyResultParam.setSysUserId(sysUserId);
        tduckCopySurveyResultParam.setProjectInfoId(param.getProjectInfoId());
        tduckCopySurveyResultParam.setSourceHospitalInfoId(param.getSourceHospitalInfoId());
        tduckCopySurveyResultParam.setTargetHospitalInfoIds(param.getTargetHospitalInfoIds());
        tduckCopySurveyResultParam.setProductAndDept(collect);
        Result<Boolean> result = tduckService.copySurveyResult(tduckCopySurveyResultParam);
        if (Boolean.TRUE.equals(result.getData())) {
            List<ProjHospitalInfo> targetHosList = new LambdaQueryChainWrapper<>(projHospitalInfoMapper)
                    .in(ProjHospitalInfo::getHospitalInfoId, param.getTargetHospitalInfoIds())
                    .eq(ProjHospitalInfo::getIsDeleted, 0)
                    .list();
            for (ProjHospitalInfo targetHos : targetHosList) {
                for (ProjSurveyPlan plan : allData) {
                    ConfirmDataParam confirmDataParam = new ConfirmDataParam();
                    String formKey = fmUserFormService.getProjectFormKeyByProjectInfoIdAndYyProductId(param.getProjectInfoId(), String.valueOf(plan.getYyProductId()));
                    // 查询调研阶段的数据
                    List<FmUserFormData> fmUserFormDataList = fmUserFormDataMapper.selectList(
                            new QueryWrapper<FmUserFormData>()
                                    .eq("form_key", formKey)
                                    .eq("source", "survey")
                                    .eq("save_type", "permanent")
                                    .eq("project_info_id", param.getProjectInfoId())
                                    .eq("hospital_info_id", targetHos.getHospitalInfoId())
                    );
                    if (CollectionUtils.isEmpty(fmUserFormDataList)) {
                        throw new CustomException(StrUtil.format("没有调研结果，请先完成调研。formKey={}，hospitalInfoId={}", formKey, targetHos.getHospitalInfoId()));
                    }
                    confirmDataParam.setId(Collections.singletonList(CollUtil.getFirst(fmUserFormDataList).getId()));
                    confirmDataParam.setSysUserId(CollUtil.getFirst(sourceSurveyPlans).getSurveyUserId());
                    confirmDataParam.setProjectInfoId(param.getProjectInfoId());
                    confirmDataParam.setHospitalInfoId(targetHos.getHospitalInfoId());
                    tduckService.confirmData(confirmDataParam);
                }
            }
            return result;
        } else {
            throw new Exception("调研平台复制调研结果失败，回滚已引用的调研计划");
        }
    }

    /**
     * 确认完成展示左侧列表
     *
     * @param param
     * @return
     */
    @Override
    public List<DifferenceProductVO> showDifferenceProduct(DifferenceProductParam param) {
        log.info("展示差异页面左侧列表信息，入参={}", JSON.toJSONString(param));
        List<FmUserFormData> fmUserFormDataList = fmUserFormDataMapper.selectList(
                new QueryWrapper<FmUserFormData>()
                        .eq("project_info_id", param.getProjectInfoId())
                        .eq("save_type", "permanent")
        );
        if (CollectionUtils.isEmpty(fmUserFormDataList)) {
            return new ArrayList<>();
        }
        // 先根据表单key分组，相当于根据产品分组
        Map<String, List<FmUserFormData>> groupByProduct = fmUserFormDataList.stream().collect(Collectors.groupingBy(FmUserFormData::getFormKey));
        Set<String> formKeySet = groupByProduct.keySet();
        List<DifferenceProductVO> result = new ArrayList<>();
        for (String formKey : formKeySet) {
            List<FmUserFormData> formDataListGroupByProduct = groupByProduct.get(formKey);
            Map<Long, List<FmUserFormData>> groupByHospital = formDataListGroupByProduct.stream().collect(Collectors.groupingBy(FmUserFormData::getHospitalInfoId));
            Set<Long> hospitalInfoIdSet = groupByHospital.keySet();
            for (Long hospitalInfoId : hospitalInfoIdSet) {
                List<FmUserFormData> formDataListByHospitalInfoId = groupByHospital.get(hospitalInfoId);
                if (formDataListByHospitalInfoId.size() > 1) {
                    Set<String> collect = formDataListByHospitalInfoId.stream().map(FmUserFormData::getSource).collect(Collectors.toSet());
                    if (!collect.contains("config")) {
                        ProjHospitalInfo projHospitalInfo = projHospitalInfoMapper.selectOne(new QueryWrapper<ProjHospitalInfo>().eq("hospital_info_id", hospitalInfoId));
                        if (projHospitalInfo == null) {
                            log.info("项目经理确认页面展示左侧产品和医院信息列表，没有根据hospitalInfoId获取到医院信息，hospitalInfoId={}", hospitalInfoId);
                            continue;
                        }
                        String[] split = formKey.split("_");
                        long yyProductId = Long.parseLong(split[2]);
                        DictProduct dictProduct = dictProductMapper.selectOne(new QueryWrapper<DictProduct>().eq("yy_product_id", yyProductId));
                        String productName;
                        if (dictProduct == null) {
                            log.info("医院信息为null，dictProduct={}", dictProduct);
                            DictProductVsModules moduleInfo = dictProductVsModulesMapper.selectOne(new QueryWrapper<DictProductVsModules>().eq("yy_module_id", yyProductId));
                            productName = moduleInfo.getYyModuleName();
                        } else {
                            productName = dictProduct.getProductName();
                        }
                        DifferenceProductVO differenceProductVO = DifferenceProductVO
                                .builder()
                                .formKey(formKey)
                                .hospitalInfoId(hospitalInfoId)
                                .hospitalName(projHospitalInfo.getHospitalName())
                                .yyProductId(yyProductId)
                                .productName(productName)
                                .build();
                        result.add(differenceProductVO);
                    }
                }
            }
        }
        if (null != param.getHospitalInfoId()) {
            result = result.stream().filter(item -> param.getHospitalInfoId().equals(item.getHospitalInfoId())).collect(Collectors.toList());
        }
        if (null != param.getYyProductId()) {
            result = result.stream().filter(item -> param.getYyProductId().equals(item.getYyProductId())).collect(Collectors.toList());
        }
        return result.stream().sorted(Comparator.comparing(DifferenceProductVO::getYyProductId)).collect(Collectors.toList());
    }

    @Override
    public Result<List<ProductBacklogUrlVO>> selectSurveyProductJobMenuDetail(ProjProductBacklogDTO dto) {
        List<ProductBacklogUrlVO> productBacklogUrlVOList = new ArrayList<>();
        // 查询项目下调研阶段表单是否使用的新流程
        List<ProjMilestoneInfo> milestoneInfoFormList = projMilestoneInfoMapper.selectList(new QueryWrapper<ProjMilestoneInfo>()
                .eq("project_info_id", dto.getProjectInfoId())
                .in("milestone_node_code", MilestoneNodeEnum.SURVEY_FORM.getCode())
                .eq("invalid_flag", NumberEnum.NO_0.num())
        );
        // 查询项目下调研阶段报表是否使用的新流程
        List<ProjMilestoneInfo> milestoneInfoList = projMilestoneInfoMapper.selectList(new QueryWrapper<ProjMilestoneInfo>()
                .eq("project_info_id", dto.getProjectInfoId())
                .in("milestone_node_code", MilestoneNodeEnum.SURVEY_REPORT.getCode())
                .eq("invalid_flag", NumberEnum.NO_0.num())
        );
        dto.setSurveyUseFlag(1);
        List<ConfigProductJobMenuDetailVO> menuDetailVOList = configProductJobMenuDetailMapper.selectProductJobMenuDetail(dto);
        for (ConfigProductJobMenuDetailVO vo : menuDetailVOList) {
            ProductBacklogUrlVO productBacklogUrlVO = new ProductBacklogUrlVO();
            switch (Convert.toInt(vo.getProductJobMenuId())) {
                case 2:
                    // 组装基础数据的链接地址（老系统程序链接）
                    productBacklogService.infoDataBaseUrl(vo, dto);
                    productBacklogUrlVO.setMenuUrl(vo.getMenuUrl());
                    productBacklogUrlVO.setMenuName(vo.getMenuName());
                    productBacklogUrlVO.setMenuCode("BasicData");
                    break;
                case 6:
                    // 报表处理
                    // 设置报表连接
                    productBacklogService.infoDataBaseUrl(vo, dto);
                    productBacklogUrlVO.setMenuName(vo.getMenuName());
                    productBacklogUrlVO.setMenuCode("ReportSurvey");
                    productBacklogUrlVO.setMenuUrl(vo.getMenuUrl());
                    if (milestoneInfoList != null && milestoneInfoList.size() > 0) {
                        Boolean isShowNew = milestoneInfoList.get(0).getIsComponent() != null && !"".equals(
                                milestoneInfoList.get(0).getIsComponent());
                        if (isShowNew) {
                            productBacklogUrlVO.setMenuCode("ReportSurvey");
                        }
                    }
                    break;
                case 7:
                    // 处理表单链接
                    // 设置表单链接
                    productBacklogService.infoDataBaseUrl(vo, dto);
                    productBacklogUrlVO.setMenuName(vo.getMenuName());
                    productBacklogUrlVO.setMenuCode("FormSurvey");
                    productBacklogUrlVO.setMenuUrl(vo.getMenuUrl());
                    if (milestoneInfoFormList != null && milestoneInfoFormList.size() > 0) {
                        Boolean isShowFormNew = milestoneInfoFormList.get(0).getIsComponent() != null && !"".equals(milestoneInfoFormList.get(0).getIsComponent());
                        if (isShowFormNew) {
                            productBacklogUrlVO.setMenuCode("FormSurvey");
                        }
                    }
                    break;
                case 21:
                    CreateTduckFormParam param = new CreateTduckFormParam();
                    param.setProjectInfoId(dto.getProjectInfoId());
                    param.setProductId(dto.getYyProductId().toString());
                    param.setHospitalInfoId(dto.getHospitalInfoId());
                    param.setOperationSource("survey");
                    param.setDeptName(dto.getDeptName());
                    param.setSysUserId(dto.getSysUserId());
                    param.setSource("survey");

                    String tduckForm = tduckService.createTduckForm(param);
                    productBacklogUrlVO.setMenuName(vo.getMenuName());
                    productBacklogUrlVO.setMenuCode("ProductSurvey");
                    productBacklogUrlVO.setMenuUrl(tduckForm);

                    boolean openSurveyAudit = projectInfoService.isOpenSurveyAudit(dto.getProjectInfoId());
                    if (openSurveyAudit) {
                        ProjSurveyPlan projSurveyPlan = projSurveyPlanMapper.selectOne(
                                new QueryWrapper<ProjSurveyPlan>()
                                        .eq("is_deleted", 0)
                                        .eq("project_info_id", dto.getProjectInfoId())
                                        .eq("hospital_info_id", dto.getHospitalInfoId())
                                        .eq("yy_product_id", dto.getYyProductId())
                                        .eq("survey_user_id", dto.getSysUserId())
                                        .eq("dept_name", dto.getDeptName())

                        );
                        List<ProjBusinessExamineLogResp> businessExamineLogResps = projBusinessExamineLogService.selectLogById(projSurveyPlan.getSurveyPlanId());
                        if (!CollectionUtils.isEmpty(businessExamineLogResps)) {
                            List<SurveyPlanAuditLog> collect = businessExamineLogResps.stream().map(item -> new SurveyPlanAuditLog(item.getBusinessExamineLogId(), item.getExamineStatus(), item.getLogTitle(), item.getOperateContent(), item.getOperatorSysUserId(), item.getOperateUserName(), item.getOperateTime())).collect(Collectors.toList());
                            productBacklogUrlVO.setSurveyAuditLog(collect);
                        }
                    }

                    break;
                case 15:
                    // 小硬件调研
                    productBacklogService.infoDataBaseUrl(vo, dto);
                    productBacklogUrlVO.setMenuName(vo.getMenuName());
                    productBacklogUrlVO.setMenuCode("SmallHardwareSurvey");
                    productBacklogUrlVO.setMenuUrl(vo.getMenuUrl());
                    break;
                case 5:
                    // 设备
//                    productBacklogService.infoDataBaseUrl(vo, dto);
                    // 判断当前产品是否存在枚举中。当存在时  添加设备调研菜单
                    List<Long> menuProductIds =
                            Arrays.stream(ProductEquipSurveyMenuEnum.values()).map(v -> v.getYyProductId()).collect(Collectors.toList());
                    if (menuProductIds.contains(dto.getYyProductId())) {
                        productBacklogUrlVO.setMenuName(vo.getMenuName());
                        productBacklogUrlVO.setMenuCode(ProductEquipSurveyMenuEnum.getByYyProductId(dto.getYyProductId()).getCode());
                        productBacklogUrlVO.setMenuUrl("");
                    }
                    break;
                default:
                    break;
            }
            productBacklogUrlVOList.add(productBacklogUrlVO);
        }
        return Result.success(productBacklogUrlVOList);
    }

    /**
     * 产品业务调研消息提醒
     *
     * @param dto
     * @return
     */
    @Override
    public Result<ProductSurveyPlanMessageVO> productSurveyPlanMessage(ProjSurveyPlanDTO dto) {

        // 首期项目已上线，且当前项目仅派工“分院模式”产品时
        Boolean isBranchOnlineFlag = this.isBranchOnlineFlag(dto.getProjectInfoId());

        ProductSurveyPlanMessageVO productSurveyPlanMessageVO = new ProductSurveyPlanMessageVO();
        // 查询产品业务调研的节点消息是否已读，当已读后 才会进行本消息提醒。否则不提醒
        List<ProjTipRecord> projTipRecords = tipRecordMapper.selectList(new QueryWrapper<ProjTipRecord>()
                .eq("project_info_id", dto.getProjectInfoId())
                .eq("config_tip_id", 3)
                .orderByDesc("create_time")
                .last("limit 1")
        );
        if (CollectionUtil.isEmpty(projTipRecords) && projTipRecords.size() != 1) {
            productSurveyPlanMessageVO.setIsMessage(false);
            return Result.success(productSurveyPlanMessageVO);
        }
        ProjTipRecord projTipRecord = projTipRecords.get(0);
        if (projTipRecord.getReadFlag() == 0) {
            productSurveyPlanMessageVO.setIsMessage(false);
            return Result.success(productSurveyPlanMessageVO);
        }
        // 当前项目共有xxx个产品未分配调研任务，xxx个医院未进行产品调研任务分配，将会影响产品准备工作的完成，无法正常上线，请及时分配相应的产品调研任务
        // 查询产品侧消息提醒数据
        //查询所有调研已调研的产品
        List<Long> surveyPlanList = projSurveyPlanMapper.findYyProductIdByProjectInfoId(dto.getProjectInfoId());
        if (isBranchOnlineFlag) {
            surveyPlanList = projSurveyPlanMapper.findIsBranchOnlineYyProductIdByProjectInfoId(dto.getProjectInfoId());
        }
        //已分配调研产品数量
        Long allotProductNum = Long.valueOf(surveyPlanList.size());
        //需要调研产品数量
        Result<List<BaseIdNameResp>> listResult = productBacklogService.selectYyProductAndModule(dto.getProjectInfoId());
        //已分配调研产品数量
        Long aLong = Long.valueOf(listResult.getData().size());
        //未分配的数据
        Long noAllotProductNum = listResult.getData().size() - allotProductNum;
        productSurveyPlanMessageVO.setNoAllotProductNum(noAllotProductNum > 0L ? noAllotProductNum : 0L);
        // 查询医院侧消息提醒数据
        // 1、 查询当前项目一共多少个医院
        SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
        selectHospitalDTO.setProjectInfoId(dto.getProjectInfoId());
        // 查询当前项目要上线医院
        int size = 0;
        List<ProjHospitalInfo> hosList = projHospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);
        if (isBranchOnlineFlag) {
            List<ProjHospitalOnlineDetail> detailList =
                    projHospitalOnlineDetailMapper.selectList(new QueryWrapper<ProjHospitalOnlineDetail>().in("hospital_info_id", hosList.stream().map(ProjHospitalInfo::getHospitalInfoId).collect(Collectors.toList()))
                            .eq("is_deleted", 0));
            detailList = detailList.stream().filter(item -> item.getOnlineStatus().equals(NumberEnum.NO_1.num()) && !item.getProjectInfoId().equals(dto.getProjectInfoId())).collect(Collectors.toList());
            List<Long> onlineHos = detailList.stream().map(ProjHospitalOnlineDetail::getHospitalInfoId).collect(Collectors.toList());
            hosList = hosList.stream().filter(item -> !onlineHos.contains(item.getHospitalInfoId())).collect(Collectors.toList());
            size = hosList.size();
        } else {
            size = hosList.size();
        }

        // 2、 查询多少个医院分配了调研计划
        Long hospitalPlanCount = projSurveyPlanMapper.selectHospitalPlanCount(dto.getProjectInfoId());
        Long noAllotHospitalNum = Long.valueOf(size) - hospitalPlanCount > 0 ? Long.valueOf(size) - hospitalPlanCount : 0;
        productSurveyPlanMessageVO.setNoAllotHospitalNum(noAllotHospitalNum);
        productSurveyPlanMessageVO.setIsMessage(noAllotProductNum != 0 || noAllotHospitalNum != 0);
        return Result.success(productSurveyPlanMessageVO);
    }

    @Resource
    private ProjProductDeliverRecordService projProductDeliverRecordService;

    @Override
    public int getFinishedProductCount(Long projectInfoId) {
        // 根据项目ID查询所有医院
        SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
        selectHospitalDTO.setProjectInfoId(projectInfoId);
        List<ProjHospitalInfo> hospitalInfoList = projHospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);

        // 将医院ID去重并且排序
        List<Long> hospitalInfoIdList = hospitalInfoList.stream().map(ProjHospitalInfo::getHospitalInfoId).distinct().sorted().collect(Collectors.toList());

        // 需要调研的实施产品信息
        List<ProjProductDeliverRecord> needSurveyProduct = projProductDeliverRecordService.getSurveyProductDeliverRecord(projectInfoId, true);
        // 需要调研的实施产品的ID集合
        List<Long> needSurveyProductId = needSurveyProduct.stream().map(ProjProductDeliverRecord::getProductDeliverId).sorted().collect(Collectors.toList());

        // 当前项目所有产品所有医院的最终调研结果
        List<FmUserFormDataVO> fmUserFormData = fmUserFormDataMapper.getFmUserFormDataVO(projectInfoId, "permanent", "config");

        // 将最终调研结果根据产品分组
        Map<String, List<FmUserFormDataVO>> groupByHospital = fmUserFormData.stream().collect(Collectors.groupingBy(FmUserFormDataVO::getYyProductId));

        // 完成调研的产品数量
        int completeCount = 0;
        for (Long yyProductId : needSurveyProductId) {
            // 根据产品ID获取对应产品的最终调研结果集合
            List<FmUserFormDataVO> fmUserFormDataVOS = groupByHospital.get(String.valueOf(yyProductId));
            if (!CollectionUtils.isEmpty(fmUserFormDataVOS)) {
                // 获取所有最终调研结果的医院ID集合
                List<Long> surveyHospitalInfoIdSort = fmUserFormDataVOS.stream().map(FmUserFormDataVO::getHospitalInfoId).sorted().collect(Collectors.toList());
                // 最终调研结果的医院ID集合包含当前项目下任意一个医院ID，认为调研完成
                if (surveyHospitalInfoIdSort.stream().anyMatch(hospitalInfoIdList::contains)) {
                    completeCount = completeCount + 1;
                }
            }
        }

        return completeCount;
    }

    @Override
    public int getNotNeedSurveyProductCount(Long projectInfoId) {
        // 根据项目ID查询所有医院
        SelectHospitalDTO selectHospitalDTO = new SelectHospitalDTO();
        selectHospitalDTO.setProjectInfoId(projectInfoId);
        List<ProjHospitalInfo> hospitalInfoList = projHospitalInfoMapper.getHospitalInfoByProjectId(selectHospitalDTO);

        // 将医院ID去重并且排序
        List<Long> hospitalInfoIdList = hospitalInfoList.stream().map(ProjHospitalInfo::getHospitalInfoId).distinct().sorted().collect(Collectors.toList());

        // 无需调研的产品
        List<DictProductExtend> dictProductExtend = dictProductExtendMapper.selectList(new QueryWrapper<DictProductExtend>()
                .eq("survey_flag", 0)
                .eq("is_deleted", 0));

        // 没有无需调研的产品，直接返回
        if (CollectionUtils.isEmpty(dictProductExtend)) {
            return 0;
        }
        // 当前项目所有调研计划
        List<ProjSurveyPlan> projSurveyPlans = projSurveyPlanMapper.selectList(
                new QueryWrapper<ProjSurveyPlan>()
                        .eq("is_deleted", 0)
                        .eq("project_info_id", projectInfoId)
        );

        // 无需调研的产品ID集合
        List<Long> notNeedSurveyProductIdList = dictProductExtend.stream().map(DictProductExtend::getYyProductId).collect(Collectors.toList());

        // 分配了调研计划但是无需调研的产品调研计划
        List<ProjSurveyPlan> notNeedSurveyPlanList = projSurveyPlans.stream().filter(item -> notNeedSurveyProductIdList.contains(item.getYyProductId())).collect(Collectors.toList());

        // 分配了调研计划但是无需调研的产品调研计划根据产品分组
        Map<Long, List<ProjSurveyPlan>> groupByHospital = notNeedSurveyPlanList.stream().collect(Collectors.groupingBy(ProjSurveyPlan::getYyProductId));


        int completeCount = 0;
        for (Long yyProductId : groupByHospital.keySet()) {
            List<ProjSurveyPlan> projSurveyPlanList = groupByHospital.get(yyProductId);
            if (!CollectionUtils.isEmpty(projSurveyPlanList)) {

                List<Long> surveyHospitalInfoIdSort = projSurveyPlanList.stream().map(ProjSurveyPlan::getHospitalInfoId).distinct().sorted().collect(Collectors.toList());
                if (surveyHospitalInfoIdSort.stream().anyMatch(hospitalInfoIdList::contains)) {
                    completeCount = completeCount + 1;
                }
            }
        }

        return completeCount;
    }

    @Override
    @Transactional
    public boolean submitAudit(UpdateSurveyPlanReq req) {
        if (CollectionUtils.isEmpty(req.getSurveyPlanIdList())) {
            return false;
        }
        Date now = new Date();
        for (Long surveyPlanId : req.getSurveyPlanIdList()) {
            // 调研计划
            ProjSurveyPlan projSurveyPlan = projSurveyPlanMapper.selectByPrimaryKey(surveyPlanId);
            if (!Integer.valueOf(5).equals(projSurveyPlan.getCompleteStatus()) && !Integer.valueOf(6).equals(projSurveyPlan.getCompleteStatus())) {
                throw new IllegalArgumentException("仅已驳回和已提交状态的调研计划允许提交审核");
            }

            // 状态改为待审核
            ProjSurveyPlan updateParam = new ProjSurveyPlan();
            updateParam.setSurveyPlanId(surveyPlanId);
            updateParam.setCompleteStatus(4);
            updateParam.setUpdateTime(now);
            projSurveyPlanMapper.updateByPrimaryKeySelective(updateParam);

            projBusinessExamineLogService.saveOperationLog("survey", 0, "提交审核", surveyPlanId);

            // 提交审核时生成最终调研结果
            // 查询项目调研问卷的表单key
            String formKey = fmUserFormService.getProjectFormKeyByProjectInfoIdAndYyProductId(projSurveyPlan.getProjectInfoId(), String.valueOf(projSurveyPlan.getYyProductId()));
            // 查询调研阶段的数据
            List<FmUserFormData> fmUserFormDataList = fmUserFormDataMapper.selectList(
                    new QueryWrapper<FmUserFormData>()
                            .eq("form_key", formKey)
                            .eq("source", "survey")
                            .eq("save_type", "permanent")
                            .eq("project_info_id", projSurveyPlan.getProjectInfoId())
                            .eq("hospital_info_id", projSurveyPlan.getHospitalInfoId())
            );
            if (CollectionUtils.isEmpty(fmUserFormDataList)) {
                log.error("没有调研结果，请先完成调研。formKey={}，hospitalInfoId={}", formKey, projSurveyPlan.getHospitalInfoId());
                throw new IllegalArgumentException("没有调研结果，请先完成调研");
            }
            // 如果只有一份调研结果，直接确认最终调研结果
            List<Long> idList = new ArrayList<>();
            idList.add(fmUserFormDataList.get(0).getId());
            ConfirmDataParam confirmDataParam = new ConfirmDataParam();
            confirmDataParam.setId(idList);
            confirmDataParam.setSysUserId(projSurveyPlan.getSurveyUserId());
            confirmDataParam.setProjectInfoId(projSurveyPlan.getProjectInfoId());
            confirmDataParam.setHospitalInfoId(projSurveyPlan.getHospitalInfoId());
            tduckService.confirmData(confirmDataParam);

            // 项目计划
            ProjProjectPlan projectPlan = projProjectPlanService.getProjectPlanByProjectInfoIdAndItemCode(projSurveyPlan.getProjectInfoId(), DictProjectPlanItemEnum.SURVEY_PRODUCT);

            // 后端项目经理
            ProjProjectMember backLeader = projectInfoService.getBackLeader(projSurveyPlan.getProjectInfoId());
            if (backLeader == null) {
                throw new IllegalArgumentException("当前项目没有后端项目经理，请先维护后端项目经理");
            }
            Long projectMemberId = backLeader.getProjectMemberId();
            Long backendEngineerId = projectPlan.getBackendEngineerId();
            // 如果有产品没有分配审核人，给后端项目经理/专项后端负责人，发送消息，提示未分配，尽快安排审核人员审核；带上整个项目组考核的调研完成日期；
            if (projSurveyPlan.getAuditSysUserId() == null || projSurveyPlan.getAuditSysUserId() == -1) {
                List<Long> surveyUserId = new ArrayList<>();
                if (projectMemberId != null) {
                    surveyUserId.add(projectMemberId);
                }
                if (backendEngineerId != null) {
                    surveyUserId.add(backendEngineerId);
                }
                if (!CollectionUtils.isEmpty(surveyUserId)) {
                    SendMessageParam allocatingAuditorMessageParam = this.getAllocatingAuditorMessageParam(surveyPlanId);
                    allocatingAuditorMessageParam.setSysUserIds(surveyUserId);
                    sendMessageService.sendMessage2(allocatingAuditorMessageParam);
                } else {
                    log.info("分配调研审核责任人提醒，没有维护后端项目经理和后端专项负责人，导致没有可以接收消息的人");
                }
            } else {
                List<Long> surveyUserId = new ArrayList<>();
                surveyUserId.add(projSurveyPlan.getAuditSysUserId());
                if (projectMemberId != null) {
                    surveyUserId.add(projectMemberId);
                }
                if (backendEngineerId != null) {
                    surveyUserId.add(backendEngineerId);
                }
                SendMessageParam allocatingAuditorMessageParam = this.getAuditRemindMessageParam(surveyPlanId);
                allocatingAuditorMessageParam.setSysUserIds(surveyUserId);
                sendMessageService.sendMessage2(allocatingAuditorMessageParam);
            }
        }
        return true;
    }

    @Resource
    private ProjCustomInfoMapper customInfoMapper;

    @Resource
    private ConfigCustomBackendDetailLimitMapper configCustomBackendDetailLimitMapper;

    private SendMessageParam getAllocatingAuditorMessageParam(Long surveyPlanId) {
        // 调研计划
        ProjSurveyPlan projSurveyPlan = projSurveyPlanMapper.selectByPrimaryKey(surveyPlanId);
        // 项目信息
        ProjProjectInfo projProjectInfo = projectInfoService.selectByPrimaryKey(projSurveyPlan.getProjectInfoId());
        // 客户信息
        ProjCustomInfo projCustomInfo = customInfoMapper.selectByPrimaryKey(projProjectInfo.getCustomInfoId());

        // 消息内容替换的参数
        Map<String, String> messageContentParam = new HashMap<>();
        messageContentParam.put("customName", projCustomInfo.getCustomName());
        messageContentParam.put("projectNumber", projProjectInfo.getProjectNumber());
        if (Integer.valueOf(1).equals(projProjectInfo.getHisFlag())) {
            messageContentParam.put("hisFlag", "（首期）");
        } else {
            messageContentParam.put("hisFlag", "");
        }

        String productName = dictProductService.getProductNameByYyProductId(projSurveyPlan.getYyProductId());
        messageContentParam.put("productName", "（" + productName + "）");
        String surveyAssessmentTime = projectInfoService.getSurveyAssessmentTime(projProjectInfo);
        messageContentParam.put("time", surveyAssessmentTime);

        SendMessageParam messageParam = new SendMessageParam();
        messageParam.setMessageTypeId(100008L);
        messageParam.setProjectInfoId(projProjectInfo.getProjectInfoId());
        messageParam.setMessageContentParam(messageContentParam);
        return messageParam;
    }

    private SendMessageParam getAuditRemindMessageParam(Long surveyPlanId) {
        // 调研计划
        ProjSurveyPlan projSurveyPlan = projSurveyPlanMapper.selectByPrimaryKey(surveyPlanId);
        // 项目信息
        ProjProjectInfo projProjectInfo = projectInfoService.selectByPrimaryKey(projSurveyPlan.getProjectInfoId());
        // 客户信息
        ProjCustomInfo projCustomInfo = customInfoMapper.selectByPrimaryKey(projProjectInfo.getCustomInfoId());

        // 消息内容替换的参数
        Map<String, String> messageContentParam = new HashMap<>();
        messageContentParam.put("customName", projCustomInfo.getCustomName());
        messageContentParam.put("projectNumber", projProjectInfo.getProjectNumber());
        if (Integer.valueOf(1).equals(projProjectInfo.getHisFlag())) {
            messageContentParam.put("hisFlag", "（首期）");
        } else {
            messageContentParam.put("hisFlag", "");
        }

        String productName = dictProductService.getProductNameByYyProductId(projSurveyPlan.getYyProductId());
        messageContentParam.put("productName", "（" + productName + "）");
        String surveyAssessmentTime = projectInfoService.getSurveyAssessmentTime(projProjectInfo);

        ConfigCustomBackendDetailLimit customBackendDetailLimit = configCustomBackendDetailLimitMapper.getCustomBackendDetailLimit(projProjectInfo.getProjectInfoId(), 10);

        Date auditorTime = DateUtil.offsetHour(new Date(), customBackendDetailLimit.getLimitRatio());
        // 加了24小时之后的时间仍然早于调研考核时间
        if (auditorTime.before(DateUtil.parseDateTime(surveyAssessmentTime))) {
            messageContentParam.put("time", DateUtil.formatDateTime(auditorTime));
        } else {
            messageContentParam.put("time", surveyAssessmentTime);
        }

        SendMessageParam messageParam = new SendMessageParam();
        messageParam.setMessageTypeId(100009L);
        messageParam.setProjectInfoId(projProjectInfo.getProjectInfoId());
        messageParam.setMessageContentParam(messageContentParam);
        return messageParam;
    }

    @Override
    @Transactional
    public boolean revertSubmitAudit(Long surveyPlanId) {
        // 调研计划
        ProjSurveyPlan projSurveyPlan = projSurveyPlanMapper.selectByPrimaryKey(surveyPlanId);
        if (!Integer.valueOf(4).equals(projSurveyPlan.getCompleteStatus())) {
            throw new IllegalArgumentException("仅已提交审核且后端运维尚未进行审核的调研计划允许撤回");
        }

        // 状态改为待审核
        ProjSurveyPlan updateParam = new ProjSurveyPlan();
        updateParam.setSurveyPlanId(surveyPlanId);
        updateParam.setCompleteStatus(6);
        updateParam.setUpdateTime(new Date());
        projSurveyPlanMapper.updateByPrimaryKeySelective(updateParam);

        // 记录日志
        projBusinessExamineLogService.saveOperationLog("survey", 12, "撤销提交审核", surveyPlanId);

        String formKey = this.fmUserFormService.getProjectFormKeyByProjectInfoIdAndYyProductId(projSurveyPlan.getProjectInfoId(), String.valueOf(projSurveyPlan.getYyProductId()));

        int delete = fmUserFormDataMapper.delete(
                new QueryWrapper<FmUserFormData>()
                        .eq("form_key", formKey)
                        .eq("hospital_info_id", projSurveyPlan.getHospitalInfoId())
                        .eq("source", "config")
        );
        log.info("撤销审核时删除最终调研结果，删除成功数据条数={}", delete);

        // TODO 撤销时发送消息
        return true;
    }

    @Override
    public boolean revertConfirm(Long surveyPlanId) {
        // 调研计划
        ProjSurveyPlan projSurveyPlan = projSurveyPlanMapper.selectByPrimaryKey(surveyPlanId);
        if (!Integer.valueOf(1).equals(projSurveyPlan.getCompleteStatus())) {
            throw new IllegalArgumentException("仅项目经理或者前端专项负责人确认后的调研结果允许撤销确认。");
        }

        // 状态改为待审核
        ProjSurveyPlan updateParam = new ProjSurveyPlan();
        updateParam.setSurveyPlanId(surveyPlanId);
        updateParam.setCompleteStatus(6);
        updateParam.setUpdateTime(new Date());
        projSurveyPlanMapper.updateByPrimaryKeySelective(updateParam);

        // 记录日志
        projBusinessExamineLogService.saveOperationLog("survey", 11, "撤销确认结果", surveyPlanId);

        String formKey = this.fmUserFormService.getProjectFormKeyByProjectInfoIdAndYyProductId(projSurveyPlan.getProjectInfoId(), String.valueOf(projSurveyPlan.getYyProductId()));

        int delete = fmUserFormDataMapper.delete(
                new QueryWrapper<FmUserFormData>()
                        .eq("form_key", formKey)
                        .eq("hospital_info_id", projSurveyPlan.getHospitalInfoId())
                        .eq("source", "config")
        );
        log.info("撤销确认结果时删除最终调研结果，删除成功数据条数={}", delete);

        // TODO 撤销时发送消息

        return true;
    }

    @Override
    public List<ProjSurveyPlan> getSurveyPlan(Long projectInfoId, Long hospitalInfoId, Long yyProductId) {
        List<ProjSurveyPlan> projSurveyPlanList = projSurveyPlanMapper.selectList(
                new QueryWrapper<ProjSurveyPlan>()
                        .eq("is_deleted", 0)
                        .eq("project_info_id", projectInfoId)
                        .eq("hospital_info_id", hospitalInfoId)
                        .eq("yy_product_id", yyProductId)
        );
        log.info("获取调研计划，结果={}", JSON.toJSONString(projSurveyPlanList));
        return projSurveyPlanList;
    }

    @Override
    public boolean getOnlyOneSurveyFlag(Long projectInfoId, Long hospitalInfoId, Long yyProductId) {
        List<ProjSurveyPlan> surveyPlan = this.getSurveyPlan(projectInfoId, hospitalInfoId, yyProductId);
        return !CollectionUtils.isEmpty(surveyPlan) && surveyPlan.size() == 1;
    }

    private Long getYyProductId(ProjProductBacklogVO item) {
        if (item.getYyProductModuleId() == null) {
            return item.getYyProductId();
        }
        if (item.getYyProductModuleId() == -1) {
            return item.getYyProductId();
        }
        return item.getYyProductModuleId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result saveBackLogAndDetailTest(ConfirmFinalDataParam confirmFinalDataParam) {
        log.info("保存待办任务总数据和详情，入参：{}", JSON.toJSONString(confirmFinalDataParam));

        ProjProductBacklogDTO productBacklogDTO = new ProjProductBacklogDTO();
        productBacklogDTO.setProjectInfoId(confirmFinalDataParam.getProjectInfoId());
        // 生成的产品准备工作，已经生成了的不再重新生成
        List<ProjProductBacklogVO> backlogList = productBacklogService.findProductBacklogVOS(productBacklogDTO);
        List<ProductBacklogParam> finishedProduct = backlogList.stream().map(item -> {
            ProductBacklogParam productBacklogParam = new ProductBacklogParam();
            productBacklogParam.setProductBacklogId(item.getProductBacklogId());
            productBacklogParam.setCustomInfoId(item.getCustomInfoId());
            productBacklogParam.setProjectInfoId(item.getProjectInfoId());
            productBacklogParam.setYyProductId(this.getYyProductId(item));
            productBacklogParam.setProductName(item.getProductName());
            productBacklogParam.setMilestoneTaskDetailId(item.getMilestoneTaskDetailId());
            productBacklogParam.setHospitalInfoId(item.getHospitalInfoId());
            return productBacklogParam;
        }).collect(Collectors.toList());

        List<ProjProductDeliverRecord> surveyProductDeliverRecord = projProductDeliverRecordService.getSurveyProductDeliverRecord(confirmFinalDataParam.getProjectInfoId(), false);
        // 把不需要调研的从调研计划中过滤掉
        if (!CollectionUtils.isEmpty(surveyProductDeliverRecord)) {
            ProjProjectInfo projectInfo = projectInfoService.selectByPrimaryKey(confirmFinalDataParam.getProjectInfoId());
            List<ProjHospitalInfo> hospitalInfoList = projHospitalInfoMapper.findHospitalInfoByProjIdAndCustomerId(projectInfo.getCustomInfoId(), projectInfo.getProjectInfoId());
            for (ProjProductDeliverRecord yyProductId : surveyProductDeliverRecord) {
                for (ProjHospitalInfo hospitalInfo : hospitalInfoList) {
                    ProductBacklogParam productBacklogParam = new ProductBacklogParam();
                    productBacklogParam.setProjectInfoId(confirmFinalDataParam.getProjectInfoId());
                    productBacklogParam.setYyProductId(yyProductId.getProductDeliverId());
                    productBacklogParam.setHospitalInfoId(hospitalInfo.getHospitalInfoId());
                    finishedProduct.add(productBacklogParam);
                }
            }
        }
        confirmFinalDataParam.setFinishedProduct(finishedProduct);
        Result<List<SaveBackLogAndDetailReq>> createTaskParam = tduckService.getCreateTaskParam(confirmFinalDataParam);
        if (CollectionUtils.isEmpty(createTaskParam.getData())) {
            // 接口请求正常并且没有生成待办的参数，则不需要生成
            if (701 == createTaskParam.getCode()) {
                log.info("没有需要生成的待办和配置，projectInfoId={}，sysUserId={}", confirmFinalDataParam.getProjectInfoId(), confirmFinalDataParam.getSysUserId());
                return Result.success();
            } else {
                throw new IllegalArgumentException("批量生成多个医院得待办任务，参数为空");
            }
        }
        createTaskParam.getData().forEach(this::saveBackLogAndDetail);
        return Result.success();
    }

    /**
     * 导出调研计划数据
     *
     * @param response
     * @param dto
     */
    @Override
    public void surveyPlanExportExcel(HttpServletResponse response, ProjSurveyPlanDTO dto) {
        ProjProjectInfo projectInfo = projectInfoService.selectByPrimaryKey(dto.getProjectInfoId());
        String projectName = "";
        if (projectInfo != null) {
            projectName =projectInfo.getProjectName();
        }
        String fileName = projectName + "调研计划列表导出_" + System.currentTimeMillis();
        // 2查询导出的数据
        Result<SurveyPlanTaskRespVO> vo = this.findSurveyPlanTask(dto);
        List<SurveyPlanTaskResp> list = vo.getData().getSurveyPlanTaskResp();
        // 3. 执行导出
        // 导出数据
        try (ExcelWriter excelWriter = EasyExcelFactory.write(getOutputStream(fileName, response),
                SurveyPlanTaskResp.class).excludeColumnFieldNames(Collections.singleton("region")).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();
            // 导出文件
            excelWriter.write(list, writeSheet);
        } catch (Exception e) {
            log.error("发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }

    }

    /**
     * 构建输出流
     *
     * @param fileName：文件名称
     * @param response：
     * @return
     * @throws Exception
     */
    private OutputStream getOutputStream(String fileName, HttpServletResponse response) throws Exception {
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");
        // 告知浏览器下载，下载附件的形式
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
        return response.getOutputStream();
    }

}
