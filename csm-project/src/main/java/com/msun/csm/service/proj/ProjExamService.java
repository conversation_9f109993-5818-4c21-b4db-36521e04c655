package com.msun.csm.service.proj;

import java.util.List;

import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.model.dto.ExamImportStepDTO;
import com.msun.csm.model.dto.FindExamImportStepDTO;
import com.msun.csm.model.dto.ProjExamVsDeptDTO;
import com.msun.csm.model.dto.ProjHospitalInfoExamDTO;
import com.msun.csm.model.vo.ExamHospitalVO;
import com.msun.csm.model.vo.ExamOnlineDocVO;
import com.msun.csm.model.vo.ProjExamHospitalStepVO;
import com.msun.csm.model.vo.ProjExamVsDeptVO;

public interface ProjExamService {

    /**
     * 查询客户下是否有未开通培训环境的医院
     * @param dto
     * @return
     */
    Result<ExamHospitalVO> findApplyExamEnvironment(ProjHospitalInfoExamDTO dto);

    /**
     * 一键申请培训环境
     * @param dto
     * @return
     */
    Result applyExamEnvironment(ProjHospitalInfoExamDTO dto);

    /**
     * 医院开通时执行复制数据操作
     * @param customInfoId
     * @param hospitals
     */
    void handleExamEnviorment(Long customInfoId, List<ProjHospitalInfo> hospitals);

    /**
     * 获取在线文档路径
     * @return
     */
    Result<ExamOnlineDocVO> getOnlineDoc();

    /**
     * 查询医院导数据状态
     * @param dto
     * @return
     */
    Result<List<ProjExamHospitalStepVO>> findExamHospitalStepList(FindExamImportStepDTO dto);

    /**
     * 查询科室对照
     * @param examHospitalId
     * @return
     */
    Result<ProjExamVsDeptVO> findExamVsDept(Long examHospitalId);

    /**
     * 保存科室对照
     * @param dto
     * @return
     */
    Result saveExamVsDept(ProjExamVsDeptDTO  dto);

    /**
     * 执行SQL脚本
     * @param dto
     * @return
     */
    Result doSqltext(ExamImportStepDTO dto);

    /**
     * 执行SQL脚本
     * @param dto
     * @return
     */
    Result doTestSqltext(ExamImportStepDTO dto);

    /**
     * 删除数据
     * @param dto
     * @return
     */
    Result recycleExamData(ExamImportStepDTO dto);

    /**
     * 处理学习环境医院状态
     * @param projectInfoId
     */
    void handleExamHospital(Long projectInfoId);
}
