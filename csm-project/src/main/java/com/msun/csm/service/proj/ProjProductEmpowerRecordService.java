package com.msun.csm.service.proj;

import java.util.List;

import com.msun.csm.dao.entity.proj.ProjProductEmpowerRecord;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/24
 */

public interface ProjProductEmpowerRecordService {

    int deleteByPrimaryKey(Long productEmpowerRecordId);

    int insert(ProjProductEmpowerRecord record);

    int insertOrUpdate(ProjProductEmpowerRecord record);

    int insertOrUpdateSelective(ProjProductEmpowerRecord record);

    int insertSelective(ProjProductEmpowerRecord record);

    ProjProductEmpowerRecord selectByPrimaryKey(Long productEmpowerRecordId);

    int updateByPrimaryKeySelective(ProjProductEmpowerRecord record);

    int updateByPrimaryKey(ProjProductEmpowerRecord record);

    int updateBatch(List<ProjProductEmpowerRecord> list);

    int updateBatchSelective(List<ProjProductEmpowerRecord> list);

    int batchInsert(List<ProjProductEmpowerRecord> list);

    /**
     * 查询授权产品集合
     *
     * @param productId yy_order_product_id
     * @return List<ProjProductEmpowerRecord>
     */
    List<ProjProductEmpowerRecord> findEmpowerRecord(Long projectInfoId, List<Long> productId);

}
