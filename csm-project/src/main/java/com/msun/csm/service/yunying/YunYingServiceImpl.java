package com.msun.csm.service.yunying;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.api.yunying.CloudTimeNodeEnum;
import com.msun.csm.common.enums.api.yunying.OrderTypeEnums;
import com.msun.csm.common.enums.api.yunying.ProductSolutionEnum;
import com.msun.csm.common.enums.projprojectinfo.ProjectTypeEnums;
import com.msun.csm.common.enums.projsettlement.SettlementRuleCodeEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.dto.ProjectFileInfoDTO;
import com.msun.csm.common.model.yunying.YunyingResult;
import com.msun.csm.dao.entity.SysConfig;
import com.msun.csm.dao.entity.proj.ProjCustomCloudService;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjOrderInfo;
import com.msun.csm.dao.entity.proj.ProjOrderProduct;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectOrderRelation;
import com.msun.csm.dao.entity.proj.ProjProjectSettlementRule;
import com.msun.csm.dao.entity.proj.projsettlement.ProjSettlementOrderInfo;
import com.msun.csm.dao.mapper.config.SysConfigMapper;
import com.msun.csm.dao.mapper.proj.ProjCustomCloudServiceMapper;
import com.msun.csm.dao.mapper.proj.ProjHospitalVsProjectTypeMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderInfoMapper;
import com.msun.csm.dao.mapper.proj.ProjOrderProductMapper;
import com.msun.csm.dao.mapper.proj.ProjProjectSettlementRuleMapper;
import com.msun.csm.entity.ProjProjectSettlementMidOrder;
import com.msun.csm.feign.client.yunying.YunyingFeignClient;
import com.msun.csm.feign.entity.yunwei.req.SyncCloudTimeReq;
import com.msun.csm.feign.entity.yunying.req.FileReq;
import com.msun.csm.feign.entity.yunying.req.SyncIsMsunCloudDTO;
import com.msun.csm.feign.entity.yunying.req.YunOpenDTO;
import com.msun.csm.feign.entity.yunying.req.YunyingMidOrderDTO;
import com.msun.csm.feign.entity.yunying.resp.YunYingMsgObjResp;
import com.msun.csm.feign.entity.yunying.resp.YunYingMsgResp;
import com.msun.csm.model.dto.cloud.CloudValidateResultDTO;
import com.msun.csm.model.dto.yunweiplatform.cloud.SyncYunweiCloudTimeDTO;
import com.msun.csm.model.imsp.SyncYunInfoDTO;
import com.msun.csm.model.param.SendYunYingFineParam;
import com.msun.csm.model.param.SendYunYingMessageParam;
import com.msun.csm.service.api.ApiYunyingService;
import com.msun.csm.service.common.CommonService;
import com.msun.csm.service.common.ExceptionMessageService;
import com.msun.csm.service.operlog.SysOperLogService;
import com.msun.csm.service.proj.ProjCustomCloudServiceService;
import com.msun.csm.service.proj.ProjProjectSettlementCheckMainService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class YunYingServiceImpl implements YunYingService {

    public static final String TOKEN = "imsp";

    @Value("${env}")
    private String env;

    @Resource
    private YunyingFeignClient yunyingFeignClient;

    @Resource
    private ProjProjectSettlementCheckMainService mainService;

    @Resource
    private ProjProjectSettlementRuleMapper settlementRuleMapper;

    @Resource
    private SysOperLogService sysOperLogService;

    @Resource
    private ProjCustomCloudServiceMapper cloudServiceMapper;

    @Resource
    private ProjOrderInfoMapper orderInfoMapper;

    @Resource
    private ProjOrderProductMapper orderProductMapper;

    @Resource
    private YunYingCommonService yunYingCommonService;

    @Resource
    private ExceptionMessageService exceptionMessageService;

    @Resource
    private ProjCustomCloudServiceMapper customCloudServiceMapper;

    @Resource
    private CommonService commonService;

    @Resource
    private ProjCustomCloudServiceService customCloudServiceService;

    @Resource
    private ProjHospitalVsProjectTypeMapper hospitalVsProjectTypeMapper;

    @Resource
    private ApiYunyingService yunyingService;

    @Resource
    private YunYingService yunYingService;

    @Resource
    private SysConfigMapper sysConfigMapper;

    /**
     * 同步云云资源信息
     *
     * @param dto 请求参数
     * @return Result<String>
     */
    public Result<String> syncCloudInfo(SyncYunInfoDTO dto) {
        try {
            Map<FixCloudPhaseEnum, String> recordMap = MapUtil.newHashMap();
            // 云资源
            if (dto.getType() == NumberEnum.NO_9.num().intValue()) {
                Result<String> result = yunYingService.syncCloudInfoImpl(dto, recordMap);
                // 处理附加信息
                StringBuilder msg = new StringBuilder();
                recordMap.keySet().forEach(e -> {
                    msg.append(e.getDesc()).append(StrUtil.DASHED).append(recordMap.get(e)).append(StrUtil.COMMA);
                });
                result.setMsg(result.getMsg() + StrUtil.COMMA + msg);
                return result;
            }
        } catch (Throwable e) {
            log.error("修改云资源到期时间异常. message: {}, e=", e.getMessage(), e);
            exceptionMessageService.sendToSystemManager(-1L, "运营平台修改台账信息失败, param: " + dto + ". 异常: " + e.getMessage());
            return Result.fail(e.getMessage());
        }
        return Result.success();
    }

    /**
     * 阶段
     */
    @Getter
    public enum FixCloudPhaseEnum {
        CSM(0, "交付"),
        YUN_WEI(1, "运维平台"),
        YUN_YING(2, "运营平台");

        private final int code;
        private final String desc;

        FixCloudPhaseEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    public static void main(String[] args) {
//        DateUtil.to("2024-01-01");
//        DateUtil.paDateUtil.parseDate("2024-01-01");
    }

    @Transactional(rollbackFor = Throwable.class)
    public Result<String> syncCloudInfoImpl(SyncYunInfoDTO dto, Map<FixCloudPhaseEnum, String> recordMap) {
        // 验证参数
        CloudValidateResultDTO validateResultDTO = validateSyncYunInfoDto(dto);
        ProjCustomCloudService customCloudService = validateResultDTO.getCustomCloudService();
        // 修改交付系统云资源信息
        sysOperLogService.apiOperLogInsert(customCloudService, "运营修改云资源信息", "改前数据",
                Log.LogOperType.UPLOAD.getCode());
        // 拼接更新内容
        ProjCustomCloudService updateCloud = ProjCustomCloudService.builder()
                .customCloudServiceId(customCloudService.getCustomCloudServiceId())
                .planStartTime(DateUtil.parse(dto.getPlanStartTime()))
                .subscribeEndTime(DateUtil.parse(dto.getSubscribeEndTime()))
                .build();
        // 若期限为空则不更新
        if (ObjectUtil.isNotEmpty(dto.getSubscribeTerm())) {
            updateCloud.setServiceSubscribeTerm(dto.getSubscribeTerm());
        }
        int count = customCloudServiceMapper.updateById(updateCloud);
        ProjCustomCloudService cloudService =
                customCloudServiceMapper.selectOne(new QueryWrapper<ProjCustomCloudService>()
                        .eq("custom_cloud_service_id", customCloudService.getCustomCloudServiceId()));
        ProjCustomInfo customInfo = commonService.getCustomInfo(cloudService.getCustomInfoId());
        if (count > 0) {
            recordMap.put(FixCloudPhaseEnum.CSM,
                    "修改成功. 更新的实际数据为: 实施地客户名称:" + customInfo.getCustomName() + "(" + customInfo.getYyCustomerId()
                            + "), 解决方案(运营:" + cloudService.getSolutionType() + "), 签字时间:"
                            + cloudService.getPlanStartTime() + ", 到期时间:" + cloudService.getSubscribeEndTime()
                            + ", 实际开通时间:" + cloudService.getSubscribeStartTime() + ", 发函时间:"
                            + cloudService.getSendLetterDate());
        } else {
            recordMap.put(FixCloudPhaseEnum.CSM, "修改失败.");
            throw new CustomException(FixCloudPhaseEnum.CSM.getDesc() + ", 修改失败.");
        }
        log.info("更新云资源信息. count: {}", count);
        // 更新系统管理到期时间
        SyncYunweiCloudTimeDTO syncYunweiCloudTimeDTO = SyncYunweiCloudTimeDTO.builder()
                .customInfoId(customCloudService.getCustomInfoId())
                .sendLetterDate(StrUtil.isNotBlank(dto.getSendLetterTime()) ? dto.getSendLetterTime() + " 00:00:00"
                        : StrUtil.EMPTY)
                .subscribeEndTime(dto.getLatestExpDate() + " 00:00:00")
                .projectType(validateResultDTO.getProjectType())
                .build();
        // 同步系统管理时间
        Result<SyncCloudTimeReq> resultSendToYwei =
                customCloudServiceService.sendTimeToYunWeiImpl(syncYunweiCloudTimeDTO);
        if (ObjectUtil.isEmpty(resultSendToYwei) || !resultSendToYwei.isSuccess()) {
            String errMsg = (ObjectUtil.isNotEmpty(resultSendToYwei) ? resultSendToYwei.getMsg() : StrUtil.EMPTY);
            recordMap.put(FixCloudPhaseEnum.YUN_WEI, "修改失败." + errMsg);
            throw new CustomException(errMsg);
        } else {
            // 获取同步时使用的参数
            SyncCloudTimeReq syncCloudTimeReq = resultSendToYwei.getData();
            recordMap.put(FixCloudPhaseEnum.YUN_WEI, "修改成功." + "实施地客户名称:" + customInfo.getCustomName()
                    + "(" + customInfo.getYyCustomerId() + "), 项目类型(交付平台):"
                    + Objects.requireNonNull(ProjectTypeEnums.getEnum(validateResultDTO.getProjectType())).getName()
                    + ", 向系统管理同步时请求参数:" + syncCloudTimeReq);
        }
        return Result.success();

    }

    /**
     * 验证参数是否符合更新条件
     *
     * @param dto 请求参数
     * @return 验证通过后返回的云资源信息
     */
    private CloudValidateResultDTO validateSyncYunInfoDto(SyncYunInfoDTO dto) {
        if (dto.getYyWoId() == -1) {
            throw new CustomException("参数非法, 无法查询运营平台工单id是-1的数据.");
        }
        // 验证运营工单id是否符合更新条件
        ProjCustomCloudService customCloudService =
                customCloudServiceMapper.selectOne(new QueryWrapper<ProjCustomCloudService>().eq(
                        "yy_order_id",
                        dto.getYyWoId()));
        if (ObjectUtil.isEmpty(customCloudService)) {
            throw new CustomException("根据运营工单id未查询到云资源信息.");
        }
        // 验证关联查询, 运营工单id+ 解决方案
        customCloudService = customCloudServiceMapper.selectOne(new QueryWrapper<ProjCustomCloudService>().eq(
                "yy_order_id",
                dto.getYyWoId()).eq("solution_type", dto.getPemcusssolType()));
        if (ObjectUtil.isEmpty(customCloudService)) {
            throw new CustomException("根据运营工单id和解决方案类型未查询到云资源信息.");
        }
        // 验证实施地客户信息是否符合更新条件, custom_info_id + yy_order_id + solution_type
        ProjCustomInfo customInfo = commonService.getCustomInfoByYyCustomerId(dto.getCustomerId());
        if (ObjectUtil.isEmpty(customInfo)) {
            throw new CustomException("根据运营实施地客户id未查询到客户信息.");
        }
        customCloudService = customCloudServiceMapper.selectOne(new QueryWrapper<ProjCustomCloudService>()
                .eq("yy_order_id", dto.getYyWoId())
                .eq("solution_type", dto.getPemcusssolType())
                .eq("custom_info_id", customInfo.getCustomInfoId())
        );
        if (ObjectUtil.isEmpty(customCloudService)) {
            throw new CustomException("根据运营工单id, 解决方案类型, 客户信息未查询到云资源信息.");
        }
        // 验证解决方案类型在交付转换为单体或区域后是否存在对应的医院
        ProjOrderInfo orderInfo = commonService.getOrderInfoByYyOrderId(dto.getYyWoId());
        if (ObjectUtil.isEmpty(orderInfo)) {
            throw new CustomException("未查询到工单信息.");
        }
        // 获取项目类型
        Integer projectType = getCloudProjectType(dto.getPemcusssolType(), customInfo.getCustomInfoId());
        // 判断是否需要更新系统管理到期时间
        List<ProjCustomCloudService> customCloudServices =
                customCloudServiceMapper.selectList(new QueryWrapper<ProjCustomCloudService>().eq("custom_info_id",
                        customInfo.getCustomInfoId()));
        customCloudServices =
                customCloudServices.stream().filter(e -> projectType.intValue() == getCloudProjectType(e.getSolutionType(), customInfo.getCustomInfoId())).collect(Collectors.toList());
        boolean syncYunwei = false;
        List<ProjCustomCloudService> clouds =
                customCloudServices.stream().sorted(Comparator.comparing(ProjCustomCloudService::getCreateTime).reversed()).collect(Collectors.toList());
        return CloudValidateResultDTO.builder()
                .customCloudService(customCloudService)
                .projectType(projectType)
                .build();
    }

    /**
     * 根据解决方案和客户id查询云健康对应的项目类型
     *
     * @param solutionType 解决方案
     * @param customInfoId 客户id
     * @return 项目类型
     */
    public Integer getCloudProjectType(Integer solutionType, Long customInfoId) {
        if (ProductSolutionEnum.YUN_JIAN_KANG_SHENG_JI.getCode().intValue() == solutionType) {
            // 若是云健康升级, 人工处理
            throw new CustomException("根据云资源解决方案, 无法辨别正确的单体或者区域医院信息.");
        } else if (ProductSolutionEnum.DAN_TI.getCode().intValue() == solutionType
                || ProductSolutionEnum.JI_CENG_DAN_TI.getCode().intValue() == solutionType) {
            return ProjectTypeEnums.SINGLE.getCode();
        } else if (ProductSolutionEnum.QU_YU.getCode().intValue() == solutionType
                || ProductSolutionEnum.YI_GONG_TI.getCode().intValue() == solutionType
                || ProductSolutionEnum.QUAN_XIAN_YU.getCode().intValue() == solutionType) {
            return ProjectTypeEnums.REGION.getCode();
        }
        throw new CustomException("未查询到对应的项目类型.");
    }

    @Override
    public void sendYunYingMessageByAsync(SendYunYingMessageParam param) {
        log.info("调用运营平台发送企业微信消息，uuid={}，参数={}", param.getUuid(), JSON.toJSONString(param));
        String url = StringUtils.isBlank(param.getUrl()) ? "" : param.getUrl();
        String pictureUrl = StringUtils.isBlank(param.getPicurl()) ? "" : param.getPicurl();
        YunYingMsgResp yunYingMsgResp;
        if (!StrUtil.equals(env, "prod")) {
            yunYingMsgResp = this.sentMessage(param.getTitle(), param.getDescription(), url, pictureUrl, param.getToUser());
        } else {
            yunYingMsgResp = yunyingFeignClient.sentMessage(param.getTitle(), param.getDescription(), url, pictureUrl, param.getToUser());
        }
        log.info("调用运营平台发送企业微信消息，uuid={}，结果={}", param.getUuid(), JSON.toJSONString(yunYingMsgResp));
        if (yunYingMsgResp != null && !CollectionUtils.isEmpty(yunYingMsgResp.getObj())) {
            // code不是0则是发送失败
            List<YunYingMsgObjResp> errorList =
                    yunYingMsgResp.getObj().stream().filter(item -> item.getErrcode() != 0).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(errorList)) {
                log.info("调用运营平台发送企业微信消息，uuid={}，存在{}条发送失败的消息", param.getUuid(), errorList.size());
            }
        }
    }

    @Override
    public boolean sendEnterpriseWeChatMessageForOnePeople(SendYunYingMessageParam param) {
        if (param.getToUser().contains(",")) {
            throw new IllegalArgumentException("此方法不支持批量发送消息");
        }
        log.info("给单个人发送企业微信消息，uuid={}，参数={}", param.getUuid(), JSON.toJSONString(param));
        String url = StringUtils.isBlank(param.getUrl()) ? "" : param.getUrl();
        String pictureUrl = StringUtils.isBlank(param.getPicurl()) ? "" : param.getPicurl();
        // FIXME 发版前回退
        YunYingMsgResp yunYingMsgResp;
        if (!StrUtil.equals(env, "prod")) {
            yunYingMsgResp = this.sentMessage(param.getTitle(), param.getDescription(), url, pictureUrl, param.getToUser());
        } else {
            yunYingMsgResp = yunyingFeignClient.sentMessage(param.getTitle(), param.getDescription(), url, pictureUrl, param.getToUser());
        }
        log.info("给单个人发送企业微信消息，uuid={}，结果={}", param.getUuid(), JSON.toJSONString(yunYingMsgResp));
        if (yunYingMsgResp != null && !CollectionUtils.isEmpty(yunYingMsgResp.getObj())) {
            // 如果发送成功，所有的成功记录中errcode都是0
            Set<Integer> successSet = yunYingMsgResp.getObj().stream().map(YunYingMsgObjResp::getErrcode).collect(Collectors.toSet());
            return successSet.size() == 1 && successSet.contains(0);
        }
        return false;
    }

    private YunYingMsgResp sentMessage(String title, String description,
                                       String url, String picurl,
                                       String toUser) {
        SysConfig messageTestUser = sysConfigMapper.selectConfigByName("message_test_user");
        String[] split = messageTestUser.getConfigValue().split(",");
        List<String> testUser = Arrays.asList(split);
        if (testUser.contains(toUser)) {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("title", title);
            paramMap.put("description", description);
            paramMap.put("url", url);
            paramMap.put("picurl", picurl);
            paramMap.put("toUser", toUser);
            String messageUrl = "http://snat.msunhis.com/msunErp-web/external/message/sendNew?token=imsp";
            String post = HttpUtil.post(messageUrl, paramMap, 8000);
            log.info("aaaaaaaaaaaa={}", post);
            return JSON.parseObject(post, YunYingMsgResp.class);
        }
        YunYingMsgResp yunYingMsgResp = new YunYingMsgResp();
        yunYingMsgResp.setMsg(String.format("当前用户=%s，sys_config的message_test_user维护的测试用户=%s", toUser, testUser));
        return yunYingMsgResp;
    }

    public void sendYunyingCloudTimeSettlement(Long projectInfoId) {
        sendYunyingCloudTime(projectInfoId, CloudTimeNodeEnum.SETTLEMENT, null);
    }

    @Override
    public void sendMidOrder(ProjProjectInfo projectInfo) {
        Long projectInfoId = projectInfo.getProjectInfoId();
        // 判断是否有开通确认单上传
        YunyingMidOrderDTO midOrderDTO = new YunyingMidOrderDTO();
        midOrderDTO.setToken(YunYingServiceImpl.TOKEN);
        // 查询申请内容
        ProjProjectSettlementMidOrder midOrder = commonService.getSettlementMidOrder(projectInfoId);
        midOrderDTO.setRemark(midOrder.getRemark());
        // 查询运营工单id
        Long yyOrderId = commonService.getOrderInfo(projectInfo.getOrderInfoId()).getYyOrderId();
        midOrderDTO.setProjId(yyOrderId);
        // 设置证明文件
        ProjectFileInfoDTO projectFileInfoDTO =
                yunYingCommonService.getDownloadFileInfo(midOrder.getProjectFileId());
        midOrderDTO.setFileList(Collections.singletonList(new FileReq(projectFileInfoDTO.getFileName(),
                projectFileInfoDTO.getFileUrl())));
        String salePersonAccount = commonService.getContractCustomSalePerson(projectInfoId).getAccount();
        sysOperLogService.apiOperLogInsertObjAry("免中间件部署申请", "入参",
                Log.LogOperType.ADD.getCode(), salePersonAccount, midOrderDTO);
        try {
            log.info("免中间件部署申请, 入参=, salePersonAccount: {}, {}", salePersonAccount, JSON.toJSONString(midOrderDTO));
            Result<String> result = yunyingFeignClient.middlewareApply(salePersonAccount, midOrderDTO);
            log.info("免中间件部署申请, 出参=, {}", JSON.toJSONString(result));
            sysOperLogService.apiOperLogInsertObjAry("免中间件部署申请", "出参",
                    Log.LogOperType.ADD.getCode(), result);
            if (ObjectUtil.isEmpty(result) || !result.isSuccess()) {
                exceptionMessageService.sendToSystemManager(projectInfo.getProjectInfoId(), "运营接口调用异常-私有云免中间件部署申请, "
                        + "请及时处理");
            }
        } catch (Throwable e) {
            log.error("运营接口调用异常-私有云免中间件部署申请. {}", e.getMessage());
            sysOperLogService.apiOperLogInsert(e.getMessage(), "免中间件部署申请",
                    "出参", Log.LogOperType.ADD.getCode());
            String messageContent = projectInfo.getProjectName() + "项目-" + projectInfo.getProjectNumber()
                    + ":调用运营接口申请免中间件部署异常.";
            exceptionMessageService.sendToSystemManager(projectInfoId, messageContent);
        }
    }

    public void sendYunyingCloudTimeDeployed(Long projectInfoId, String signDate) {
        sendYunyingCloudTime(projectInfoId, CloudTimeNodeEnum.DEPLOYED, signDate);
    }

    /**
     * 同步运营平台云厂商等
     *
     * @param userLoginname 登录
     */
    public void syncIsMsunCloud(String userLoginname, SyncIsMsunCloudDTO syncIsMsunCloudDTO) throws Exception {
        sysOperLogService.apiOperLogInsertObjAry("同步运营平台云资源附加信息-入参", StrUtil.EMPTY,
                Log.LogOperType.ADD.getCode(), userLoginname, syncIsMsunCloudDTO);
        YunyingResult<String> result = yunyingFeignClient.syncIsMsunCloud(userLoginname, syncIsMsunCloudDTO);
        sysOperLogService.apiOperLogInsertObjAry("同步运营平台云资源附加信息-返回值", StrUtil.EMPTY,
                Log.LogOperType.ADD.getCode(), result);
        log.info("同步运营平台云资源附加信息-返回值: {}", result);
        if (ObjectUtil.isEmpty(result) || !result.isSuccess()) {
            throw new Exception("接口返回异常.");
        }

    }


    @Override
    public void sendYunyingCloudTime(Long projectInfoId, CloudTimeNodeEnum cloudTimeNodeEnum, String signDate) {
        // 判断是否有开通确认单上传
        YunOpenDTO yunOpenDTO = new YunOpenDTO();
        // 设置云资源工单
        try {
            List<ProjSettlementOrderInfo> settlementOrderInfos = mainService.findCloudServiceForm(projectInfoId);
            if (CollUtil.isEmpty(settlementOrderInfos)) {
                log.info("未查询到云资源工单.");
                return;
            }
            if (CollUtil.isNotEmpty(settlementOrderInfos)) {
                ProjSettlementOrderInfo settlementOrderInfo = settlementOrderInfos.get(0);
                yunOpenDTO.setCloudProjId(settlementOrderInfo.getYyOrderId());
                // 设置云资源解决方案
                List<ProjOrderProduct> orderProducts =
                        orderProductMapper.selectList(new QueryWrapper<ProjOrderProduct>().eq("order_info_id",
                                settlementOrderInfo.getOrderInfoId()));
                yunOpenDTO.setPemCusSolType(orderProducts.get(0).getProductResolveTypeId().intValue());
            }
        } catch (Throwable ignore) {
            log.warn("调运营平台同步时间异常. {}", ignore.getMessage());
        }
        // 设置入驻类型
        yunOpenDTO.setType(cloudTimeNodeEnum.getCode());
        if (CloudTimeNodeEnum.SETTLEMENT.getCode().equals(cloudTimeNodeEnum.getCode())) {
            // 设置附件地址
            List<ProjProjectSettlementRule> settlementRules =
                    settlementRuleMapper.selectList(new QueryWrapper<ProjProjectSettlementRule>()
                            .eq("project_info_id", projectInfoId).eq("project_rule_code",
                                    SettlementRuleCodeEnum.SETTLEMENT_CLOUD_CONFIRM_FORM.getCode()));
            if (CollUtil.isNotEmpty(settlementRules)) {
                ProjProjectSettlementRule rule = settlementRules.get(0);
                if (ObjectUtil.isNotEmpty(rule.getProjectFileId())) {
                    // 获取可以下载的文件信息
                    ProjectFileInfoDTO projectFileInfoDTO =
                            yunYingCommonService.getDownloadFileInfo(rule.getProjectFileId());
                    yunOpenDTO.setFileList(Collections.singletonList(new FileReq(projectFileInfoDTO.getFileName(),
                            projectFileInfoDTO.getFileUrl())));
                }
            }
            // 设置开通时间
            ProjProjectOrderRelation relation = mainService.getCloudResourceRelationBothType(projectInfoId);
            if (ObjectUtil.isNotEmpty(relation)) {
                ProjCustomCloudService cloudService = cloudServiceMapper.selectById(relation.getBussinessInfoId());
                yunOpenDTO.setOpenDate(DateUtil.formatDateTime(cloudService.getSubscribeStartTime()));
            }
        } else {
            yunOpenDTO.setSignDate(signDate);
        }
        yunOpenDTO.setToken(TOKEN);
        // 设置软件工单
        ProjProjectInfo projectInfo = mainService.getProjectInfo(projectInfoId);
        // 企业微信： 2025、04、09  @张迪  刚才跟庆魁沟通了一下，针对云资源工单解决方案是云健康升级的，销售在提交入驻的时候，不要取云资源工单的类型，直接取软件的单体/区域的属性，运营平台在这个阶段，同步确认云资源的单体/区域属性
        if (yunOpenDTO.getPemCusSolType() == 10) {
            yunOpenDTO.setPemCusSolType(projectInfo.getProjectType());
        }
        List<ProjOrderInfo> orderInfos = orderInfoMapper.selectList(new QueryWrapper<ProjOrderInfo>().eq(
                "order_info_id", projectInfo.getOrderInfoId()));
        yunOpenDTO.setSoftProjId(orderInfos.get(0).getYyOrderId());
        yunOpenDTO.setProductType(StrUtil.toString(OrderTypeEnums.CLOUD_RESOURCE.getCode()));
        String salePersonAccount = commonService.getContractCustomSalePerson(projectInfoId).getAccount();
        List<Object> param = new ArrayList<>();
        param.add(salePersonAccount);
        param.add(yunOpenDTO);
        sysOperLogService.apiOperLogInsert(param, "同步运营平台云资源" + cloudTimeNodeEnum.getDesc() + "时间", "入参",
                Log.LogOperType.ADD.getCode());
        try {
            log.info("调用运营平台同步时间, 入参={}", JSON.toJSONString(param));
            Result<String> result = yunyingFeignClient.syncYzyOpen(salePersonAccount, yunOpenDTO);
            log.info("调用运营平台同步时间, 结果={}", JSON.toJSONString(result));
            sysOperLogService.apiOperLogInsert(result, "同步运营平台云资源" + cloudTimeNodeEnum.getDesc() + "时间", "返回值",
                    Log.LogOperType.ADD.getCode());
            if (ObjectUtil.isEmpty(result) || !result.isSuccess()) {
                exceptionMessageService.sendToSystemManager(projectInfo.getProjectInfoId(), "通知运营平台审核云资源开通确认时间接口调用异常."
                        + "请及时处理");
            }
        } catch (Throwable e) {
            log.error("调用运营平台同步时间异常. {}", e.getMessage());
            sysOperLogService.apiOperLogInsert(e.getMessage(), "同步运营平台云资源异常." + cloudTimeNodeEnum.getDesc() + "时间",
                    "返回值", Log.LogOperType.ADD.getCode());
            String messageContent = projectInfo.getProjectName() + "项目-" + projectInfo.getProjectNumber()
                    + ":调用运营平台同步时间异常.";
            exceptionMessageService.sendToSystemManager(projectInfoId, messageContent);
        }

    }

    /**
     * 发送运营平台消息
     *
     * @param param 参数
     */
    @Override
    public Boolean sendYunYingMessageWithResult(SendYunYingMessageParam param) {
        log.info("调用运营平台发送企业微信消息，参数={}", JSON.toJSONString(param));
        String url = StringUtils.isBlank(param.getUrl()) ? "" : param.getUrl();
        String pictureUrl = StringUtils.isBlank(param.getPicurl()) ? "" : param.getPicurl();
        YunYingMsgResp yunYingMsgResp;
        if (!StrUtil.equals(env, "prod")) {
            yunYingMsgResp = this.sentMessage(param.getTitle(), param.getDescription(), url, pictureUrl, param.getToUser());
        } else {
            yunYingMsgResp = yunyingFeignClient.sentMessage(param.getTitle(), param.getDescription(), url, pictureUrl, param.getToUser());
        }
        log.info("调用运营平台发送企业微信消息，结果={}", JSON.toJSONString(yunYingMsgResp));
        if (ObjectUtil.isEmpty(yunYingMsgResp)) {
            log.info("调用运营平台发送企业微信消息失败，失败原因:无结果返回");
            return false;
        }
        if (!yunYingMsgResp.getSuccess() || yunYingMsgResp.getCode() != 200) {
            log.info("调用运营平台发送企业微信消息失败，失败原因:{}", yunYingMsgResp.getMsg());
            return false;
        }
        return true;
    }

    /**
     * 发送运营处罚消息
     *
     * @param param 参数
     */
    @Override
    public Boolean sendYunYingFineWithResult(SendYunYingFineParam param) {
        log.info("调用运营平台发送罚单/预警单，参数={}", JSON.toJSONString(param));
        String url = StringUtils.isBlank(param.getUrl()) ? "" : param.getUrl();
        String pictureUrl = StringUtils.isBlank(param.getPicurl()) ? "" : param.getPicurl();
        YunYingMsgResp yunYingMsgResp = yunyingFeignClient.sendFineMessage(param.getTitle(), param.getDescription(), url,
                pictureUrl, param.getToUser(), param.getBusinessNo(), param.getBusinessId(), param.getStartedDate(),
                param.getStandardEndDate(), param.getMessageType(), param.getRemarks(), param.getFunValue(),
                param.getMonitorPoint(), param.getSource());
        log.info("调用运营平台发送罚单/预警单，结果={}", JSON.toJSONString(yunYingMsgResp));
        if (ObjectUtil.isEmpty(yunYingMsgResp)) {
            log.info("调用运营平台发送罚单/预警单失败，失败原因:无结果返回");
            return false;
        }
        if (!yunYingMsgResp.getSuccess() || yunYingMsgResp.getCode() != 200) {
            log.info("调用运营平台发送罚单/预警单失败，失败原因:{}", yunYingMsgResp.getMsg());
            return false;
        }
        return true;
    }
}
