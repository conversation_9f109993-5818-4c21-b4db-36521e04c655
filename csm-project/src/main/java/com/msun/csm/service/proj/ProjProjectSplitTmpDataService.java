package com.msun.csm.service.proj;

import java.util.List;

import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjProjectSplitTmpData;
import com.msun.csm.model.req.project.ProjectSplitReq;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/5/23
 */

public interface ProjProjectSplitTmpDataService {

    int deleteByPrimaryKey(Long id);

    int insert(ProjProjectSplitTmpData record);

    int insertOrUpdate(ProjProjectSplitTmpData record);

    int insertOrUpdateSelective(ProjProjectSplitTmpData record);

    int insertSelective(ProjProjectSplitTmpData record);

    ProjProjectSplitTmpData selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProjProjectSplitTmpData record);

    int updateByPrimaryKey(ProjProjectSplitTmpData record);

    int updateBatch(List<ProjProjectSplitTmpData> list);

    int updateBatchSelective(List<ProjProjectSplitTmpData> list);

    int batchInsert(List<ProjProjectSplitTmpData> list);

    /**
     * 暂存拆分参数
     *
     * @param req
     * @return
     */
    Result tempStage(ProjectSplitReq req);
}
