package com.msun.csm.controller.api.csmapi;

import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import com.msun.csm.util.PageHelperUtil;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.msun.core.commons.api.ResponseResult;
import com.msun.csm.common.annotation.CsmSign;
import com.msun.csm.controller.csmapi.FormPatternApi;
import com.msun.csm.dao.entity.oas.FormPattern;
import com.msun.csm.dao.mapper.oas.FormPatternMapper;
import com.msun.csm.model.csm.OutFormPatternFindByPageDTO;
import com.msun.csm.model.csm.OutFormPatternVO;
import com.msun.csm.model.csm.form.OutFormPatternGetDTO;

import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description:
 * @Author: zhangdi
 * @Date: 2024/9/23
 */
@Slf4j
@RestController
public class FormPatternOutApiImpl implements FormPatternApi {

    @Resource
    private FormPatternMapper formPatternMapper;

    /**
     * 分页查询接口
     *
     * @param dto 参数
     * @return Result
     */
    @Override
    @CsmSign
    public ResponseResult<PageInfo<OutFormPatternVO>> formPatternFindByPage(OutFormPatternFindByPageDTO dto) {
        return PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> {
            List<OutFormPatternVO> list = formPatternMapper.selectByDiQu(dto);
            PageInfo<OutFormPatternVO> pageInfo = new PageInfo<>(list, dto.getPageSize());
            return ResponseResult.success(pageInfo);
        });

    }

    /**
     * 根据模板ID查询模板数据
     *
     * @param dto 参数
     * @return Result
     */
    @Override
    @CsmSign
    public ResponseResult<OutFormPatternVO> get(OutFormPatternGetDTO dto) {
        FormPattern formPattern = formPatternMapper.selectById(dto.getFormPatternId());
        if (Objects.isNull(formPattern)) {
            return null;
        }
        OutFormPatternVO outFormPatternVO = new OutFormPatternVO();
        BeanUtil.copyProperties(formPattern, outFormPatternVO);
        return ResponseResult.success(outFormPatternVO);
    }
}
