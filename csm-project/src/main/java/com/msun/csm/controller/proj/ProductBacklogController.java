package com.msun.csm.controller.proj;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSON;
import com.msun.csm.common.annotation.CsmSign;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.proj.ProjProductConfigLog;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.producttask.ProjProductTask;
import com.msun.csm.model.dto.ProjProductBacklogDTO;
import com.msun.csm.model.dto.ProjProductBacklogOneClickDetectDTO;
import com.msun.csm.model.dto.ProjProductBacklogOneClickImportDTO;
import com.msun.csm.model.param.ProjProductTaskParam;
import com.msun.csm.model.param.ProjProductTaskRecordParam;
import com.msun.csm.model.req.surveyplan.QuerySurveyPlanProductReq;
import com.msun.csm.model.resp.producttask.ProjProductTaskResp;
import com.msun.csm.model.tduck.req.SaveBackLogAndDetailReq;
import com.msun.csm.model.vo.ConfigImportCloudVO;
import com.msun.csm.model.vo.OneCheckResultVO;
import com.msun.csm.model.vo.OneClickDetectionVO;
import com.msun.csm.model.vo.ProductBacklogUrlVO;
import com.msun.csm.model.vo.ProjProductBacklogDataVO;
import com.msun.csm.model.vo.ProjProductConfigLogVO;
import com.msun.csm.model.vo.surveyplan.SurveyPlanInitVO;
import com.msun.csm.service.proj.ProjProductBacklogService;
import com.msun.csm.service.proj.ProjProjectInfoService;
import com.msun.csm.service.proj.ProjSurveyPlanService;
import com.msun.csm.service.proj.producttask.ProjProductTaskService;

import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/07/25/16:56
 */
@Api (tags = "产品待办任务")
@Slf4j
@Controller
@RequestMapping ("/productBacklog")
public class ProductBacklogController {

    @Resource
    private ProjProductBacklogService productBacklogService;
    @Resource
    private ProjProductTaskService projProductTaskService;

    @Resource
    private ProjSurveyPlanService projSurveyPlanService;
    @Resource
    private ProjProjectInfoService projProjectInfoService;

    /**
     * 查询产品待办任务列表
     *
     * @param dto 参数
     * @return 查询产品待办任务列表
     */
    @Log (operName = "查询产品待办任务列表", operDetail = "查询产品待办任务列表", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "查询产品待办任务列表")
    @ApiOperation ("查询产品待办任务列表")
    @ResponseBody
    @PostMapping (value = "/selectProductBacklog")
    Result<ProjProductBacklogDataVO> selectProductBacklog(@RequestBody ProjProductBacklogDTO dto) {
        try {
            // 参数处理 ，当选择具体产品时 忽略具体的产品菜单、菜单状态、责任人 【当不选择产品时候 再根据其他传】 【董博培，扎赉诺尔】
            if (ObjectUtil.isNotEmpty(dto.getYyProductId())) {
                dto.setProductJobMenuId(null);
                dto.setProductJobMenuStatus(null);
                dto.setUserId(null);
            }
            return Result.success(productBacklogService.selectProductBacklog(dto));
        } catch (Exception e) {
            log.error("查询产品待办任务列表失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("查询产品待办任务列表失败 , " + e.getMessage());
        }
    }

    @ApiOperation ("一键检测(所有医院代办项)")
    @Log (operName = "一键检测", operDetail = "一键检测", operLogType = Log.LogOperType.FIX, intLogType = Log.IntLogType.SELF_SYS, cnName = "一键检测")
    @ResponseBody
    @PostMapping (value = "/oneClickDetection")
    Result<OneClickDetectionVO> oneClickDetection(@Valid @RequestBody ProjProductBacklogOneClickDetectDTO oneClickDTO) {
        return productBacklogService.oneClickDetection(oneClickDTO);
    }

    /**
     * 产品准备节点左侧医院列表信息
     *
     * @param dto 参数
     * @return 查询产品待办任务列表
     */
    @Log (operName = "产品准备节点左侧医院列表信息", operDetail = "产品准备节点左侧医院列表信息", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "产品准备节点左侧医院列表信息")
    @ApiOperation ("产品准备节点左侧医院列表信息")
    @ResponseBody
    @PostMapping (value = "/selectProductPrepareHospital")
    Result<SurveyPlanInitVO> selectProductPrepareHospital(@RequestBody ProjProductBacklogDTO dto) {
        try {
            return productBacklogService.selectProductPrepareHospital(dto);
        } catch (Exception e) {
            log.error("查询产品待办任务列表失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("查询产品待办任务列表失败 , " + e.getMessage());
        }
    }

    /**
     * 查询运维平台产品 **下拉使用**
     *
     * @param projectInfoId 参数
     * @return 查询运维平台产品 **下拉使用**
     */
    @Log (operName = "查询运维平台产品 **下拉使用**", operDetail = "查询运维平台产品 **下拉使用**", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "查询运维平台产品 **下拉使用**")
    @ApiOperation ("查询运维平台产品 **下拉使用**")
    @ResponseBody
    @GetMapping (value = "/selectYyProductAndModule")
    Result selectYyProductAndModule(@RequestParam (required = false) Long projectInfoId) {
        try {
            return productBacklogService.selectYyProductAndModule(projectInfoId);
        } catch (Exception e) {
            log.error("查询产品待办任务列表失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("查询产品待办任务列表失败 , " + e.getMessage());
        }
    }

    /**
     * 分配调研计划查询产品
     *
     * @return 分配调研计划查询产品
     */
    @Log (operName = "分配调研计划查询产品", operDetail = "分配调研计划查询产品", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "分配调研计划查询产品")
    @ApiOperation ("分配调研计划查询产品")
    @ResponseBody
    @PostMapping (value = "/selectSurveyPlanProducts")
    Result selectSurveyPlanProducts(@RequestBody @Valid QuerySurveyPlanProductReq req) {
        try {
            return productBacklogService.selectSurveyPlanProducts(req);
        } catch (Exception e) {
            log.error("查询产品待办任务列表失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("查询产品待办任务列表失败 , " + e.getMessage());
        }
    }

    /**
     * 更新产品待办任务责任人
     *
     * @param dto 参数
     * @return 更新产品待办任务责任人
     */
    @Log (operName = "更新产品待办任务责任人", operDetail = "更新产品待办任务责任人", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "更新产品待办任务责任人")
    @ApiOperation ("更新产品待办任务责任人")
    @ResponseBody
    @PostMapping (value = "/updateProductBacklogLeader")
    Result updateProductBacklogLeader(@RequestBody ProjProductBacklogDTO dto) {
        try {
            return productBacklogService.updateProductBacklogLeader(dto);
        } catch (Exception e) {
            log.error("更新产品待办任务责任人失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("更新产品待办任务责任人失败 , " + e.getMessage());
        }
    }

    /**
     * 批量更新产品待办任务责任人
     *
     * @param dto 参数
     * @return 更新产品待办任务责任人
     */
    @Log (operName = "批量更新产品待办任务责任人", operDetail = "批量更新产品待办任务责任人", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "更新产品待办任务责任人")
    @ApiOperation ("批量更新产品待办任务责任人")
    @ResponseBody
    @PostMapping (value = "/updateProductBacklogLeaderList")
    Result updateProductBacklogLeaderList(@RequestBody ProjProductBacklogDTO dto) {
        try {
            return productBacklogService.batchUpdateProductBacklogLeaderList(dto);
        } catch (Exception e) {
            log.error("批量更新产品待办任务责任人失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("批量更新产品待办任务责任人失败 , " + e.getMessage());
        }
    }

    /**
     * 产品待办任务列表==查询明细页面动态菜单数据
     *
     * @param dto 参数
     * @return 查询明细页面动态菜单数据
     */
    @Log (operName = "查询明细页面动态菜单数据", operDetail = "查询明细页面动态菜单数据", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "查询明细页面动态菜单数据")
    @ApiOperation ("查询明细页面动态菜单数据")
    @ResponseBody
    @PostMapping (value = "/selectBacklogUrlList")
    Result<List<ProductBacklogUrlVO>> selectBacklogUrlList(@RequestBody ProjProductBacklogDTO dto) {
        try {
            return productBacklogService.selectBacklogUrlList(dto);
        } catch (Exception e) {
            log.error("查询明细页面动态菜单数据失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("操作失败。错误信息：" + e.getMessage());
        }
    }

    /**
     * 查询产品待办任务列表明细
     *
     * @param dto 参数
     * @return 查询产品待办任务列表明细
     */
    @Log (operName = "查询产品待办任务列表明细", operDetail = "查询产品待办任务列表明细", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "查询产品待办任务列表明细")
    @ApiOperation ("查询产品待办任务列表明细")
    @ResponseBody
    @PostMapping (value = "/selectProductTaskList")
    Result selectProductTaskList(@RequestBody ProjProductTaskParam dto) {
        try {
            return projProductTaskService.selectProductTaskList(dto);
        } catch (Exception e) {
            log.error("查询产品待办任务列表明细失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("查询产品待办任务列表明细失败 , " + e.getMessage());
        }
    }

    /**
     * 查询产品待办任务列表进度
     *
     * @param dto 参数
     * @return 查询产品待办任务列表进度
     */
    @Log (operName = "查询产品待办任务列表进度", operDetail = "查询产品待办任务列表进度", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "查询产品待办任务列表进度")
    @ApiOperation ("查询产品待办任务列表进度")
    @ResponseBody
    @PostMapping (value = "/selectProductTaskProgress")
    Result selectProductTaskProgress(@RequestBody ProjProductTaskParam dto) {
        try {
            return Result.success(projProductTaskService.selectProductTaskProgress(dto));
        } catch (Exception e) {
            log.error("查询产品待办任务列表进度，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("查询产品待办任务列表进度 , " + e.getMessage());
        }
    }

    /**
     * 确认完成状态
     *
     * @param dto 参数
     * @return 确认完成状态
     */
    @Log (operName = "确认完成状态", operDetail = "确认完成状态", operLogType = Log.LogOperType.FIX, intLogType = Log.IntLogType.SELF_SYS, cnName = "确认完成状态")
    @ApiOperation ("确认完成状态")
    @ResponseBody
    @PostMapping (value = "/updateProductTaskData")
    Result updateProductTaskData(@RequestBody ProjProductTaskParam dto) {
        try {
            return Result.success(projProductTaskService.updatePorductTaskData(dto));
        } catch (Exception e) {
            log.error("确认完成状态失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("确认完成状态失败 , " + e.getMessage());
        }
    }

    /**
     * 设置-HIS登录
     *
     * @param dto 参数
     * @return 查询产品待办任务列表明细
     */
    @Log (operName = "HIS登录", operDetail = "HIS登录", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "HIS登录")
    @ApiOperation ("HIS登录")
    @ResponseBody
    @PostMapping (value = "/hisLogin")
    Result hisLogin(@RequestBody ProjProductTaskParam dto) {
        try {
            return Result.success(projProductTaskService.hisLogin(dto));
        } catch (Exception e) {
            log.error("HIS登录链接获取失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("HIS登录链接获取失败 , " + e.getMessage());
        }
    }

    /**
     * 一键检测
     *
     * @param dto 参数
     * @return 一键检测
     */
    @Log (operName = "一键检测", operDetail = "一键检测", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "一键检测")
    @ApiOperation ("一键检测")
    @ResponseBody
    @PostMapping (value = "/oneCheck")
    Result<OneCheckResultVO> oneCheck(@RequestBody ProjProductTaskParam dto) {
        try {
            return projProductTaskService.oneCheck(dto, "下钻页面待处理任务一键检测");
        } catch (Exception e) {
            log.error("一键检测失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("一键检测失败 , " + e.getMessage());
        }
    }

    /**
     * 配置数据导入云健康
     *
     * @param dto 参数
     * @return 配置数据导入云健康
     */
    @Log (operName = "配置数据导入云健康", operDetail = "配置数据导入云健康", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "配置数据导入云健康")
    @ResponseBody
    @PostMapping (value = "/importHealthConfig")
    Result importHealthConfig(@RequestBody ProjProductBacklogDTO dto) {
        try {
            return productBacklogService.importHealthConfig(dto);
        } catch (Exception e) {
            log.error("配置导入云健康失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("配置导入云健康失败 , " + e.getMessage());
        }
    }

    @ApiOperation ("一键导入云健康(所有医院,所有产品)")
    @Log (operName = "一键导入云健康(所有医院,所有产品)", operDetail = "一键导入云健康", operLogType = Log.LogOperType.FIX, intLogType = Log.IntLogType.SELF_SYS, cnName = "一键导入云健康")
    @ResponseBody
    @PostMapping (value = "/oneClickImportCloudHealth")
    Result<List<String>> oneClickImportCloudHealth(@Valid @RequestBody ProjProductBacklogOneClickImportDTO importDTO) {
        return productBacklogService.oneClickImportCloudHealth(importDTO);
    }

    /**
     * 测试csm是否通云健康环境
     *
     * @param customInfoId 参数
     * @return 测试csm是否通云健康环境
     */
    @Log (operName = "测试csm是否通云健康环境", operDetail = "测试csm是否通云健康环境", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "测试csm是否通云健康环境")
    @ResponseBody
    @GetMapping (value = "/testCsmToApi")
    Result testCsmToApi(@RequestParam ("customInfoId") Long customInfoId) {
        try {
            return productBacklogService.testCsmToApi(customInfoId);
        } catch (Exception e) {
            log.error("测试csm是否通云健康环境失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("测试csm是否通云健康环境失败 , " + e.getMessage());
        }
    }

    /**
     * 检测交付平台访问云健康数据库
     *
     * @param customInfoId 参数
     * @return 测试csm是否通云健康环境
     */
    @Log (operName = "检测交付平台访问云健康数据库", operDetail = "检测交付平台访问云健康数据库", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "检测交付平台访问云健康数据库")
    @ResponseBody
    @GetMapping (value = "/checkNetwork")
    Result checkNetwork(@RequestParam ("customInfoId") Long customInfoId) {
        try {
            return productBacklogService.checkNetwork(customInfoId);
        } catch (Exception e) {
            log.error("检测交付平台访问云健康数据库失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("检测交付平台访问云健康数据库失败 , " + e.getMessage());
        }
    }

    /**
     * 查询云健康表操作权限
     *
     * @param customInfoId 参数
     * @return 查询云健康表操作权限
     */
    @Log (operName = "查询云健康表操作权限", operDetail = "查询云健康表操作权限", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "查询云健康表操作权限")
    @ResponseBody
    @GetMapping (value = "/chisDictTableOperationPermissions")
    Result chisDictTableOperationPermissions(@RequestParam ("customInfoId") Long customInfoId) {
        try {
            return productBacklogService.chisDictTableOperationPermissions(customInfoId);
        } catch (Exception e) {
            log.error("查询云健康表操作权限失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("查询云健康表操作权限失败 , " + e.getMessage());
        }
    }

    /**
     * 查询配置导入日志信息
     *
     * @param configLog 参数
     * @return 查询配置导入日志信息
     */
    @Log (operName = "查询配置导入日志信息", operDetail = "查询配置导入日志信息", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "查询配置导入日志信息")
    @ResponseBody
    @PostMapping (value = "/selectConfigLogList")
    Result<List<ProjProductConfigLogVO>> selectConfigLogList(@RequestBody ProjProductConfigLog configLog) {
        try {
            return productBacklogService.selectConfigLogList(configLog);
        } catch (Exception e) {
            log.error("查询配置导入日志信息失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("查询配置导入日志信息失败 , " + e.getMessage());
        }
    }

    /**
     * 产品待办任务确认完成
     *
     * @param dto 参数
     * @return 查询配置导入日志信息
     */
    @Log (operName = "产品待办任务确认完成", operDetail = "产品待办任务确认完成", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "产品待办任务确认完成")
    @ResponseBody
    @PostMapping (value = "/taskBacklogFinish")
    Result<List<ProjProductConfigLogVO>> taskBacklogFinish(@RequestBody ProjProductBacklogDTO dto) {
        try {
            return productBacklogService.taskBacklogFinish(dto, "产品确认完成");
        } catch (Exception e) {
            log.error("产品待办任务确认完成失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("产品待办任务确认完成失败 , " + e.getMessage());
        }
    }

    @ResponseBody
    @ApiOperation ("保存里程碑待办")
    @PostMapping (value = "/saveMilestoneTaskAndDetail")
    Result<List<Object>> saveMilestoneTaskAndDetail() {
        try {
            SaveBackLogAndDetailReq saveBackLogAndDetailReq = new SaveBackLogAndDetailReq();
            List<BaseIdNameResp> yyProductIdList = new ArrayList<>();
            BaseIdNameResp ns = new BaseIdNameResp();
            ns.setId(4073L);
            yyProductIdList.add(ns);
            saveBackLogAndDetailReq.setYyProductList(yyProductIdList);
            Long hospitalInfoId = 111L;
            ProjProjectInfo projectInfo = projProjectInfoService.selectByPrimaryKey(484488903154065408L);
            Date now = new Date();
            projSurveyPlanService.saveMilestoneTaskAndDetail(saveBackLogAndDetailReq, hospitalInfoId, projectInfo, now);
            return null;
        } catch (Exception e) {
            log.error("查询配置导入日志信息失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("查询配置导入日志信息失败 , " + e.getMessage());
        }
    }

    /**
     * 手机端审核所需待办数据信息
     *
     * @param dto 参数
     * @return 手机端审核所需待办数据信息
     */
    @Log (operName = "手机端审核所需待办数据信息", operDetail = "手机端审核所需待办数据信息", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "手机端审核所需待办数据信息")
    @ApiOperation ("手机端审核所需待办数据信息")
    @ResponseBody
    @PostMapping (value = "/selectTaskDataByTaskId")
    Result selectTaskDataByTaskId(@RequestBody ProjProductTaskRecordParam dto) {
        try {
            ProjProductTaskResp projProductTask = projProductTaskService.selectTaskDataByTaskId(dto);
            return Result.success(projProductTask);
        } catch (Exception e) {
            log.error("手机端审核所需待办数据信息，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("手机端审核所需待办数据信息失败 , " + e.getMessage());
        }
    }

    /**
     * APP开通审核结果
     *
     * @param dto 参数
     * @return APP 审核结果
     */
    @Log (operName = "APP开通审核结果", operDetail = "APP开通审核结果", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "APP开通审核结果")
    @ApiOperation ("APP开通审核结果")
    @ResponseBody
    @PostMapping (value = "/updateReviewRecordDataByParamer")
    Result updateReviewRecordDataByParamer(@RequestBody ProjProductTaskRecordParam dto) {
        try {
            return Result.success(projProductTaskService.updateReviewRecordDataByParamer(dto));
        } catch (Exception e) {
            log.error("APP开通审核结果，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("APP开通审核结果失败 , " + e.getMessage());
        }
    }

    /**
     * 保存task图片数据
     *
     * @param task 参数
     * @return APP 保存task图片数据
     */
    @Log (operName = "保存task图片数据", operDetail = "保存task图片数据", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "保存task图片数据")
    @ApiOperation ("保存task图片数据")
    @ResponseBody
    @PostMapping (value = "/saveImgData")
    Result updateReviewRecordDataByParamer(@RequestBody ProjProductTask task) {
        try {
            projSurveyPlanService.addSurveyImgData(task);
            return Result.success();
        } catch (Exception e) {
            log.error("保存task图片数据，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("保存task图片数据失败 , " + e.getMessage());
        }
    }

    /**
     * 配置是否可导入云健康
     *
     * @param dto 参数
     * @return
     */
    @Log (operName = "配置是否可导入云健康", operDetail = "配置是否可导入云健康", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "配置是否可导入云健康")
    @ResponseBody
    @PostMapping (value = "/configIsImportHealth")
    Result<ConfigImportCloudVO> configIsImportHealth(@RequestBody ProjProductBacklogDTO dto) {
        try {
            return productBacklogService.configIsImportHealth(dto);
        } catch (Exception e) {
            log.error("配置是否可导入云健康，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("配置是否可导入云健康 , " + e.getMessage());
        }
    }


    /**
     * 更新配置导入云健康状态为未导入
     * 注意：pc端不能直接调用-仅限tduck调用
     */
    @CsmSign
    @Log (operName = "更新配置导入云健康状态为未导入", operDetail = "更新配置导入云健康状态为未导入", operLogType = Log.LogOperType.FIX, intLogType = Log.IntLogType.SELF_SYS, cnName = "更新配置导入云健康状态为未导入")
    @ResponseBody
    @PostMapping (value = "/updateProductConfigStatus")
    Result<Integer> updateProductConfigStatus(@RequestBody ProjProductBacklogDTO dto) {
        log.info("更新配置导入云健康状态为未导入，参数={}", JSON.toJSONString(dto));
        try {
            return productBacklogService.updateProductConfigStatus(dto);
        } catch (Exception e) {
            log.error("更新配置导入云健康状态为未导入，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("更新配置导入云健康状态为未导入，错误信息：" + e.getMessage());
        }
    }

    /**
     * 修改产品准备节点里程碑完成状态
     */
    @Log (operName = "修改产品准备节点里程碑完成状态", operDetail = "修改产品准备节点里程碑完成状态", operLogType = Log.LogOperType.FIX, intLogType = Log.IntLogType.SELF_SYS, cnName = "修改产品准备节点里程碑完成状态")
    @ResponseBody
    @PostMapping (value = "/updateProductBacklogMilestoneInfo")
    Result<Integer> updateProductBacklogMilestoneInfo(@RequestBody ProjProductBacklogDTO dto) {
        try {
            return productBacklogService.updateProductBacklogMilestoneInfo(dto);
        } catch (Exception e) {
            log.error("修改产品准备节点里程碑完成状态，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("修改产品准备节点里程碑完成状态，错误信息：" + e.getMessage());
        }
    }

    @Log (operName = "急诊挂单", operDetail = "急诊挂单", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "急诊挂单")
    @ApiOperation ("急诊挂单")
    @ResponseBody
    @PostMapping (value = "/sheetPendingOrder")
    Result sheetPendingOrder(@RequestBody ProjProductTaskResp dto) {
        try {
            return projProductTaskService.sheetPendingOrder(dto);
        } catch (Exception e) {
            log.error("查询产品待办任务列表明细失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("急诊挂单处理失败 , " + e.getMessage());
        }
    }

    /**
     * 确认开始，设置当前时间为开始时间
     *
     * @param dto 参数
     * @return 操作结果
     */
    @Log (operName = "确认开始", operDetail = "设置当前时间为开始时间",
         operLogType = Log.LogOperType.ADD, intLogType = Log.IntLogType.SELF_SYS,
         cnName = "确认开始")
    @ApiOperation("确认开始，设置当前时间为开始时间")
    @ResponseBody
    @PostMapping(value = "/confirmStart")
    Result confirmStart(@RequestBody ProjProductBacklogDTO dto) {
        try {
            return productBacklogService.confirmStart(dto);
        } catch (Exception e) {
            log.error("确认开始失败，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("确认开始失败，错误信息：" + e.getMessage());
        }
    }
}
