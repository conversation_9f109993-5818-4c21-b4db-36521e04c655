package com.msun.csm.controller.proj;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.mapper.proj.ProjProjectInfoMapper;
import com.msun.csm.model.resp.issue.IssueExcelResp;
import com.msun.csm.util.EasyExcelUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.SimpleId;
import com.msun.csm.model.req.issue.BatchDelIssueReq;
import com.msun.csm.model.req.issue.BatchSaveIssueReq;
import com.msun.csm.model.req.issue.QueryIssueReq;
import com.msun.csm.model.req.issue.SaveIssueReq;
import com.msun.csm.model.resp.issue.IssueBaseQueryResp;
import com.msun.csm.model.resp.issue.IssueDataResp;
import com.msun.csm.service.proj.ProjIssueInfoService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/11/29
 */

@Slf4j
@Api(tags = "项目组问题跟进")
@RestController
@RequestMapping("/issue")
public class ProjIssueController {

    @Resource
    private ProjIssueInfoService issueInfoService;

    /**
     * @param simpleId
     * @return
     * @Description: 查询项目组问题下拉数据
     */
    @Log(operName = "查询", operDetail = "查询项目组问题下拉数据", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询项目组问题下拉数据")
    @ResponseBody
    @PostMapping(value = "/getBaseQueryData")
    public Result<IssueBaseQueryResp> getBaseQueryData(@RequestBody SimpleId simpleId) {
        if (simpleId.getId() == null) {
            return Result.fail("项目id不能为空");
        }
        return issueInfoService.getBaseQueryData(simpleId);
    }

    /**
     * @param req
     * @return
     * @Description: 新增项目组问题数据
     */
    @Log(operName = "新增项目组问题数据", operDetail = "新增项目组问题数据", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "新增项目组问题数据")
    @ResponseBody
    @PostMapping(value = "/saveIssue")
    public Result saveIssue(@RequestBody @Valid SaveIssueReq req) {
        return issueInfoService.saveIssue(req);
    }

    /**
     * @param req
     * @return
     * @Description: 批量新增项目组问题数据
     */
    @Log(operName = "批量新增项目组问题数据", operDetail = "批量新增项目组问题数据", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "批量新增项目组问题数据")
    @ResponseBody
    @PostMapping(value = "/batchSaveIssue")
    public Result batchSaveIssue(@RequestBody @Valid BatchSaveIssueReq req) {
        return issueInfoService.batchSaveIssue(req);
    }

    /**
     * @param req
     * @return
     * @Description: 删除项目组问题数据-支持批量删除
     */
    @Log(operName = "删除项目组问题数据", operDetail = "删除项目组问题数据", operLogType = Log.LogOperType.DEL,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "删除项目组问题数据")
    @ResponseBody
    @PostMapping(value = "/delIssue")
    public Result delIssue(@RequestBody @Valid BatchDelIssueReq req) {
        return issueInfoService.delIssueBatch(req);
    }


    /**
     * @param queryIssueReq
     * @return
     * @Description: 查询项目组问题数据
     */
    @Log(operName = "查询项目组问题数据", operDetail = "查询项目组问题数据", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询项目组问题数据")
    @ResponseBody
    @PostMapping(value = "/queryData")
    public Result<List<IssueDataResp>> queryDataPage(@RequestBody @Valid QueryIssueReq queryIssueReq) {
        return issueInfoService.queryData(queryIssueReq);
    }


    @Log(operName = "项目组问题数据导出为Excel", operDetail = "项目组问题数据导出为Excel", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "项目组问题数据导出为Excel")
    @ResponseBody
    @PostMapping(value = "/exportExcel")
    public void exportExcel(@RequestBody @Valid QueryIssueReq queryIssueReq) {
        Result<List<IssueDataResp>> result = issueInfoService.queryData(queryIssueReq);
        List<IssueDataResp> data = result.getData();
        List<IssueExcelResp> excelDataList = new ArrayList<>();
        for (IssueDataResp item : data) {
            IssueExcelResp excelData = new IssueExcelResp();
            BeanUtils.copyProperties(item, excelData);
            excelDataList.add(excelData);
        }
        int size = excelDataList.size();
        for (int i = 0; i < size; i++) {
            excelDataList.get(i).setIndexNo(StrUtil.format("{}", i + 1));
        }
        try {
            ProjProjectInfoMapper mapper = SpringUtil.getBean(ProjProjectInfoMapper.class);

            ProjProjectInfo project = new LambdaQueryChainWrapper<>(mapper)
                    .eq(ProjProjectInfo::getProjectInfoId, queryIssueReq.getProjectInfoId())
                    .one();
            RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
            assert requestAttributes != null;
            HttpServletResponse response = ((ServletRequestAttributes) requestAttributes).getResponse();
            String dataFormat = DateUtil.format(new Date(), "yyyyMMddHHmmss");
            //xlsx格式:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet  xls格式:application/vnd.ms-excelExport
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = StrUtil.format("{}-{}-问题清单-{}.xlsx", project.getProjectName(), project.getProjectNumber(), dataFormat);
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));

            EasyExcel.write(response.getOutputStream(), IssueExcelResp.class)
                    .registerWriteHandler(EasyExcelUtils.getStyle())//引用样式
                    .registerWriteHandler(new EasyExcelUtils.ColWidthConfig())//自适应列宽
                    .registerWriteHandler(new EasyExcelUtils.RowHeightConfig())//自适应行高
                    .sheet("问题清单")
                    .doWrite(excelDataList);
        } catch (Exception e) {
            throw new CustomException("导出问题清单失败：", e);
        }
    }

    /**
     * @param simpleId
     * @return
     * @Description: 查询项目问题数据
     */
    @Log(operName = "查询项目问题数据", operDetail = "查询项目问题数据", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询项目问题数据")
    @ResponseBody
    @PostMapping(value = "/queryCount")
    public Result queryCount(@RequestBody @Valid SimpleId simpleId) {
        if (simpleId.getId() == null) {
            return Result.fail("项目id不能为空");
        }
        return issueInfoService.queryCount(simpleId);
    }


    /**
     * @param simpleId
     * @return
     * @Description: 导出问题excel
     */
    @Log(operName = "导出问题excel", operDetail = "导出问题excel", operLogType = Log.LogOperType.EXP,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "导出问题excel")
    @ResponseBody
    @PostMapping(value = "/export")
    public Result export(@RequestBody @Valid SimpleId simpleId) {
        if (simpleId.getId() == null) {
            return Result.fail("项目id不能为空");
        }
        return issueInfoService.export(simpleId);
    }


}
