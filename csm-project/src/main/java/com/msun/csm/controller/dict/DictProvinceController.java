package com.msun.csm.controller.dict;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.vo.DictProvinceVO;
import com.msun.csm.service.dict.DictProvinceService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/28/9:40
 */
@Api (tags = "省市-Controller")
@RestController
@RequestMapping ("/dictProvince")
public class DictProvinceController {

    @Resource
    private DictProvinceService dictProvinceService;

    @ApiOperation ("省市字典-列表查询")
    @PostMapping (value = "/selectProvinceList")
    Result<List<DictProvinceVO>> selectProvinceList() {
        return dictProvinceService.selectProvinceList();
    }
    /**
     * 定时任务手动触发
     *
     * @return
     */
    @Log(operName = "同步行政区划信息", operDetail = "同步行政区划信息-定时任务手动触发",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "同步行政区划信息-定时任务手动触发")
    @ApiOperation ("同步行政区划信息--定时任务手动触发")
    @PostMapping (value = "/divisionProvinceTask")
    public Result dictProvinceTask() {
        return dictProvinceService.divisionProvinceTask();
    }
}
