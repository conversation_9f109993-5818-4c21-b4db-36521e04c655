package com.msun.csm.controller.dict;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.dict.DictAgentChat;
import com.msun.csm.model.req.DictAgentChatReq;
import com.msun.csm.model.resp.DictAgentChatScenarioConfigResp;
import com.msun.csm.model.vo.DictAgentChatVO;
import com.msun.csm.service.dict.DictAgentChatService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * AI智能体配置控制器
 * <AUTHOR>
 * @date 2024/10/10
 */
@Slf4j
@RestController
@RequestMapping("/dictAgentChat")
@Api(tags = "AI检测图片配置相关接口")
public class DictAgentChatController {

    @Resource
    private DictAgentChatService dictAgentChatService;

    /**
     * 查询所有AI检测图片配置
     * @return 配置列表
     */
    @Log(operName = "查询所有AI检测图片配置", operDetail = "查询所有AI检测图片配置",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询AI配置")
    @ApiOperation("查询所有AI检测图片配置")
    @GetMapping("/getAllConfigs")
    public Result<List<DictAgentChatVO>> getAllAiImageDetectionConfigs() {
        log.info("接收到查询所有AI检测图片配置请求");
        return dictAgentChatService.getAllAiImageDetectionConfigs();
    }

    /**
     * 根据场景编码查询配置
     * @param dictAgentChatReq 请求参数
     * @return 配置信息
     */
    @Log(operName = "根据场景编码查询AI配置", operDetail = "根据场景编码查询AI配置",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询场景配置")
    @ApiOperation("根据场景编码查询配置")
    @PostMapping("/getConfigByScenario")
    public Result<DictAgentChatScenarioConfigResp> getConfigByScenario(
            @ApiParam(value = "查询参数", required = true) 
            @Valid @RequestBody DictAgentChatReq dictAgentChatReq) {
        log.info("接收到根据场景编码查询配置请求，scenarioCode: {}", dictAgentChatReq.getScenarioCode());
        return dictAgentChatService.getConfigByScenario(dictAgentChatReq);
    }

    /**
     * 发送图片检测消息
     * @param dictAgentChatReq 请求参数
     * @return 检测结果
     */
    @Log(operName = "发送图片检测消息", operDetail = "发送图片检测消息",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "图片检测")
    @ApiOperation("发送图片检测消息")
    @PostMapping("/sendMessage")
    public Result sendChartMessage(
            @ApiParam(value = "检测参数", required = true) 
            @Valid @RequestBody DictAgentChatReq dictAgentChatReq) {
        log.info("接收到发送图片检测消息请求，scenarioCode: {}", dictAgentChatReq.getScenarioCode());
        return dictAgentChatService.sendChartMessage(dictAgentChatReq);
    }
}
