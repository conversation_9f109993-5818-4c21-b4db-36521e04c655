package com.msun.csm.controller.proj;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.model.BaseIdCodeNameResp;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.common.model.ProjectInfoId;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.dto.PageList;
import com.msun.csm.model.dto.AllocatingAuditorParam;
import com.msun.csm.model.dto.GetSurveyPlanAuditListParam;
import com.msun.csm.model.dto.RejectOrAcceptParam;
import com.msun.csm.model.dto.SurveyPlanAuditDetail;
import com.msun.csm.model.dto.SurveyPlanAuditInfo;
import com.msun.csm.service.proj.SurveyPlanAuditService;

import lombok.extern.slf4j.Slf4j;


@Slf4j
@RestController
@RequestMapping("/surveyPlanAudit")
public class SurveyPlanAuditController {

    @Resource
    private SurveyPlanAuditService surveyPlanAuditService;

    /**
     * 获取允许分配调研计划的产品列表
     *
     * @param param 参数
     * @return 允许分配调研计划的产品列表
     */
    @PostMapping("/getCanSurveyProduct")
    public Result<List<BaseIdNameResp>> getCanSurveyProduct(@RequestBody ProjectInfoId param) {
        try {
            List<BaseIdNameResp> canSurveyProduct = surveyPlanAuditService.getCanSurveyProduct(param.getProjectInfoId());
            return Result.success(canSurveyProduct, "成功");
        } catch (Exception e) {
            log.error("获取允许分配调研计划的产品列表，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("预期之外的错误：" + e.getMessage());
        }
    }

    /**
     * 获取审核人
     *
     * @param param 参数
     * @return 审核人
     */
    @PostMapping("/getAuditor")
    public Result<List<BaseIdCodeNameResp>> getAuditor(@RequestBody ProjectInfoId param) {
        try {
            List<BaseIdCodeNameResp> canSurveyProduct = surveyPlanAuditService.getAuditor(param.getProjectInfoId());
            return Result.success(canSurveyProduct, "成功");
        } catch (Exception e) {
            log.error("获取审核人，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("预期之外的错误：" + e.getMessage());
        }
    }

    /**
     * 获取产品业务调研审核列表
     *
     * @param param 参数
     * @return 产品业务调研审核列表
     */
    @PostMapping("/getSurveyPlanAuditList")
    public Result<PageList<SurveyPlanAuditInfo>> getSurveyPlanAuditList(@RequestBody GetSurveyPlanAuditListParam param) {
        try {
            return Result.success(surveyPlanAuditService.getSurveyPlanAuditList(param), "成功");
        } catch (Exception e) {
            log.error("获取产品业务调研审核列表，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("预期之外的错误：" + e.getMessage());
        }
    }

    /**
     * 分配后端审核人
     *
     * @param param 参数
     * @return 操作结果
     */
    @PostMapping("/allocatingAuditor")
    public Result<Void> allocatingAuditor(@RequestBody List<AllocatingAuditorParam> param) {
        try {
            boolean result = surveyPlanAuditService.allocatingAuditor(param);
            if (result) {
                return Result.success(null, "分配成功");
            }
            return Result.success(null, "分配失败");
        } catch (Exception e) {
            log.error("分配后端审核人，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("预期之外的错误：" + e.getMessage());
        }
    }

    /**
     * 获取对应的填鸭问卷地址以及审批日志
     *
     * @param param 参数
     * @return 对应的填鸭问卷地址以及审批日志
     */
    @PostMapping("/getSurveyPlanAuditDetail")
    public Result<SurveyPlanAuditDetail> getSurveyPlanAuditDetail(@RequestBody AllocatingAuditorParam param) {
        try {
            return Result.success(surveyPlanAuditService.getSurveyPlanAuditDetail(param), "成功");
        } catch (Exception e) {
            log.error("获取对应的填鸭问卷地址以及审批日志，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("预期之外的错误：" + e.getMessage());
        }
    }

    /**
     * 驳回/通过审核
     *
     * @param param 参数
     * @return 对应的填鸭问卷地址以及审批日志
     */
    @PostMapping("/rejectOrAccept")
    public Result<Void> rejectOrAccept(@RequestBody RejectOrAcceptParam param) {
        try {
            boolean result = surveyPlanAuditService.rejectOrAccept(param);
            if (result) {
                return Result.success(null, "操作成功");
            }
            return Result.success(null, "操作失败");
        } catch (Exception e) {
            log.error("获取对应的填鸭问卷地址以及审批日志，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("预期之外的错误：" + e.getMessage());
        }
    }

}
