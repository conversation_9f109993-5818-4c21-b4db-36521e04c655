package com.msun.csm.controller.proj;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.req.projform.ProjSurveyFormAddReq;
import com.msun.csm.model.req.projform.ProjSurveyFormReq;
import com.msun.csm.model.req.projform.ProjSurveyFormResponsibilitiesReq;
import com.msun.csm.model.req.projform.ProjSurveyFormUpdateReq;
import com.msun.csm.model.req.projreport.ProjSurveyReportReviewExamineReq;
import com.msun.csm.model.resp.projform.ProjSurveyFormMenuResp;
import com.msun.csm.model.resp.projform.ProjSurveyFormParamerResp;
import com.msun.csm.model.resp.projform.ProjSurveyFormResp;
import com.msun.csm.model.resp.projform.ProjSurveyReprotFormPageResp;
import com.msun.csm.model.resp.projreport.ProjBusinessExamineLogResp;
import com.msun.csm.service.proj.projform.ProjSurveyFormService;

/**
 * @Description:
 * @Author: zhangdi
 * @Date: 2024/9/4
 */
@Api(tags = "表单")
@RestController
@RequestMapping("/projSurveyForm")
@Slf4j
public class ProjSurveyFormController {

    @Resource
    private ProjSurveyFormService projSurveyFormService;


    /**
     * 分页查询报表信息
     *
     * @param projSurveyFormReq
     * @return
     */
    @Log(operName = "分页查询表单信息", operDetail = "分页查询表单信息", intLogType = Log.IntLogType.SELF_SYS, cnName = "分页查询表单信息")
    @ApiOperation("分页查询表单信息")
    @PostMapping(value = "/selectSurveyFormByPage")
    public Result<ProjSurveyReprotFormPageResp<ProjSurveyFormResp>> selectSurveyFormByPage(@RequestBody ProjSurveyFormReq projSurveyFormReq) {
        return projSurveyFormService.selectSurveyFormByPage(projSurveyFormReq);
    }

    /**
     * 删除表单
     *
     * @param projSurveyFormReq
     * @return
     */
    @Log(operName = "删除表单", operDetail = "删除表单", intLogType = Log.IntLogType.SELF_SYS, cnName = "删除表单")
    @ApiOperation("删除表单")
    @PostMapping(value = "/deleteSurveyForm")
    public Result updateSurveyForm(@RequestBody ProjSurveyFormUpdateReq projSurveyFormReq) {
        return projSurveyFormService.deleteSurveyForm(projSurveyFormReq);
    }


    /**
     * 设计制作
     *
     * @param projSurveyFormReq
     * @return
     */
    @Log(operName = "设计制作", operDetail = "设计制作", intLogType = Log.IntLogType.SELF_SYS, cnName = "设计制作")
    @ApiOperation("设计制作")
    @PostMapping(value = "/formMark")
    Result formMark(@RequestBody ProjSurveyFormUpdateReq projSurveyFormReq) {
        return projSurveyFormService.formMark(projSurveyFormReq);
    }

    /**
     * 表单审核驳回
     *
     * @param projSurveyFormReq
     * @return
     */
    @Log(operName = "表单审核驳回", operDetail = "表单审核驳回", intLogType = Log.IntLogType.SELF_SYS, cnName = "表单审核驳回")
    @ApiOperation("表单审核驳回")
    @PostMapping(value = "/updateExamineFormStatus")
    Result updateExamineFormStatus(@RequestBody ProjSurveyFormUpdateReq projSurveyFormReq) {
        projSurveyFormReq.setFinishStatus(2);
        return projSurveyFormService.updateExamineFormStatus(projSurveyFormReq);
    }


    /**
     * 批量分配责任人
     *
     * @param projSurveyFormReq
     * @return
     */
    @Log(operName = "批量分配责任人", operDetail = "批量分配责任人", intLogType = Log.IntLogType.SELF_SYS, cnName = "批量分配责任人")
    @ApiOperation("批量分配责任人")
    @PostMapping(value = "/updateFormResponsibilities")
    Result updateFormResponsibilities(@RequestBody ProjSurveyFormResponsibilitiesReq projSurveyFormReq) {
        return projSurveyFormService.updateFormResponsibilities(projSurveyFormReq);
    }

    /**
     * 查询项目下报表内容模块数据
     *
     * @param projSurveyFormReq
     * @return
     */
    @Log(operName = "查询项目下报表内容模块数据", operDetail = "查询项目下报表内容模块数据", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询项目下报表内容模块数据")
    @ApiOperation("查询项目下报表内容模块数据")
    @PostMapping(value = "/getFormMenuByProjectId")
    Result<List<ProjSurveyFormMenuResp>> getFormMenuByProjectId(@RequestBody ProjSurveyFormUpdateReq projSurveyFormReq) {
        return projSurveyFormService.getFormMenuByProjectId(projSurveyFormReq);
    }

    /**
     * 查询所需参数
     *
     * @param projSurveyFormReq
     * @return
     */
    @Log(operName = "查询所需参数", operDetail = "查询所需参数", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询所需参数")
    @ApiOperation("查询所需参数")
    @PostMapping(value = "/getFormParamer")
    Result<ProjSurveyFormParamerResp> getFormParamer(@RequestBody ProjSurveyFormUpdateReq projSurveyFormReq) {
        return projSurveyFormService.getFormParamer(projSurveyFormReq);
    }

    /**
     * 批量保存
     *
     * @param projSurveyFormAddReqs
     * @return
     */
    @Log(operName = "批量保存", operDetail = "批量保存", intLogType = Log.IntLogType.SELF_SYS, cnName = "批量保存")
    @ApiOperation("批量保存")
    @PostMapping(value = "/batchFormSave")
    Result batchFormSave(@RequestBody List<ProjSurveyFormAddReq> projSurveyFormAddReqs) {
        return projSurveyFormService.batchFormSave(projSurveyFormAddReqs);
    }

    /**
     * 新增OR修改
     *
     * @param projSurveyFormReq
     * @return
     */
    @Log(operName = "新增OR修改", operDetail = "新增OR修改", intLogType = Log.IntLogType.SELF_SYS, cnName = "新增OR修改")
    @ApiOperation("新增OR修改")
    @PostMapping(value = "/updateOrAdd")
    Result updateOrAdd(@RequestBody ProjSurveyFormAddReq projSurveyFormReq) {
        return projSurveyFormService.updateOrAdd(projSurveyFormReq);
    }

    /**
     * 单个表单提交完成
     *
     * @param projSurveyFormUpdateReq
     * @return
     */
    @Log(operName = "单个表单提交完成", operDetail = "单个表单提交完成",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "单个表单提交完成")
    @ApiOperation("单个表单提交完成")
    @PostMapping(value = "/updateFormFinishStatus")
    Result updateFormFinishStatus(@RequestBody ProjSurveyFormUpdateReq projSurveyFormUpdateReq) {
        projSurveyFormUpdateReq.setFinishStatus(1);
        return projSurveyFormService.updateFormFinishStatus(projSurveyFormUpdateReq);
    }


    /**
     * 批量分配审核人
     *
     * @param projSurveyFormReq
     * @return
     */
    @Log (operName = "批量分配审核人", operDetail = "批量分配审核人", intLogType = Log.IntLogType.SELF_SYS, cnName = "批量分配审核人")
    @ApiOperation ("批量分配审核人")
    @PostMapping (value = "/updateReportReviewer")
    Result updateReportReviewer(@RequestBody ProjSurveyFormResponsibilitiesReq projSurveyFormReq) {
        return projSurveyFormService.updateReportReviewer(projSurveyFormReq);
    }

    /**
     * 批量审核
     *
     * @param projSurveyFormReq
     * @return
     */
    @Log (operName = "批量审核", operDetail = "批量审核", intLogType = Log.IntLogType.SELF_SYS, cnName = "批量审核")
    @ApiOperation ("批量审核")
    @PostMapping (value = "/updateReportExamine")
    Result updateReportExeReport(@RequestBody ProjSurveyReportReviewExamineReq projSurveyFormReq) {
        return projSurveyFormService.updateReportExamine(projSurveyFormReq);
    }

    /**
     * 提交运维审核
     *
     * @param projSurveyReportExamineReq
     * @return
     */
    @Log (operName = "提交运维审核", operDetail = "提交运维审核",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "提交运维审核")
    @ApiOperation ("提交运维审核")
    @PostMapping (value = "/updateReportExamineStatus")
    Result updateReportExamineStatus(@RequestBody ProjSurveyReportReviewExamineReq projSurveyReportExamineReq) {
        return projSurveyFormService.updateReportExamineStatus(projSurveyReportExamineReq);
    }

    /**
     * 查询日志信息
     *
     * @param projSurveyReportReviewExamineReq
     * @return
     */
    @Log (operName = "查询日志信息", operDetail = "查询日志信息",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询日志信息")
    @ApiOperation ("查询日志信息")
    @PostMapping (value = "/selectLogById")
    Result<List<ProjBusinessExamineLogResp>> selectLogById(@RequestBody ProjSurveyReportReviewExamineReq projSurveyReportReviewExamineReq) {
        return projSurveyFormService.selectLogById(projSurveyReportReviewExamineReq.getId());
    }

    /**
     * 导出表单数据
     *
     * @param response
     * @param dto
     */
    @Log(operName = "导出表单数据", operDetail = "导出表单数据", operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS, cnName = "导出表单数据")
    @ApiOperation("导出表单数据")
    @PostMapping(value = "/reportFormExportExcel")
    void reportPrintExportExcel(HttpServletResponse response, @RequestBody ProjSurveyFormReq dto) {
        projSurveyFormService.reportPrintExportExcel(response, dto);
    }

    /**
     * 批量分配前端验证人
     *
     * @param param 参数
     */
    @Log (operName = "批量分配前端验证人", operDetail = "批量分配前端验证人", intLogType = Log.IntLogType.SELF_SYS, cnName = "批量分配前端验证人")
    @PostMapping (value = "/updateFormIdentifier")
    Result<Void> updateFormIdentifier(@RequestBody ProjSurveyFormResponsibilitiesReq param) {
        return projSurveyFormService.updateFormIdentifier(param);
    }

    /**
     * 前端项目成员验证打印报表通过
     *
     * @param param 参数
     */
    @Log(operName = "前端项目成员验证打印报表通过", operDetail = "前端项目成员验证打印报表通过", intLogType = Log.IntLogType.SELF_SYS, cnName = "前端项目成员验证打印报表通过")
    @PostMapping(value = "/verificationPassed")
    Result<Void> verificationPassed(@RequestBody ProjSurveyFormResponsibilitiesReq param) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            return Result.fail("请选择要验证通过的表单");
        }
        boolean result = projSurveyFormService.verificationPassed(param);
        if (result) {
            return Result.success(null, "表单验证通过");
        }
        return Result.fail("表单验证通过，操作失败");
    }

}
