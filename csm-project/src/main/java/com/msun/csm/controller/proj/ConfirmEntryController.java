package com.msun.csm.controller.proj;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.model.dto.ConfirmEntryDTO;
import com.msun.csm.service.proj.ConfirmEntryService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName: ConfirmEntryController
 * @Description:确认入驻相关接口
 * @Author: Yhongmin
 * @Date: 11:25 2024/5/31
 */
@Api(tags = "确认入驻相关接口")
@RestController
@RequestMapping("/projectGroup")
public class ConfirmEntryController {
    @Resource
    private ConfirmEntryService confirmEntryService;

    /**
     * 说明: 新项目调用老项目确认入驻用于处理运营平台和消息等
     * @param dto
     * @return:com.msun.csm.common.model.Result
     * @author: Yhong<PERSON>
     * @createAt: 2024/5/31 14:53
     * @remark: Copyright
     */
    @ApiOperation("新项目调用老项目确认入驻用于处理运营平台和消息等")
    @PostMapping("/projectEnter")
    public Object putConfirmEntry(@RequestBody ConfirmEntryDTO dto) {
        return confirmEntryService.putConfirmEntry(dto);
    }
}
