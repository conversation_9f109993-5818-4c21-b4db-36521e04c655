package com.msun.csm.controller.proj;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.model.SimpleId;
import com.msun.csm.model.req.project.ProjectMergeReq;
import com.msun.csm.model.req.project.ProjectSplitReq;
import com.msun.csm.model.resp.project.ProjectSplitResp;
import com.msun.csm.service.proj.ProjProjectInfoService;

/**
 * @Description: 项目拆分合并
 * @Author: MengChuAn
 * @Date: 2024/5/22
 */
@RestController
@RequestMapping ("/splitAndMerge")
public class SplitAndMergeController {

    @Resource
    private ProjProjectInfoService projectInfoService;

    /**
     * 查询项目工单产品信息
     *
     * @param req
     * @return
     */
    @Log (operName = "拆分项目获取项目工单产品详情", operDetail = "拆分项目获取项目工单产品详情",
            operLogType = Log.LogOperType.SEARCH, intLogType = Log.IntLogType.SELF_SYS,
            cnName = "拆分项目获取项目工单产品详情")
    @RequestMapping ("getProjectInfo")
    public Result<ProjectSplitResp> getProjectInfo(@RequestBody @Validated SimpleId req) {
        return projectInfoService.getProjectOrderProductInfo(req);
    }

    /**
     * 项目拆分
     *
     * @param req
     * @return
     */
    @Log (operName = "项目拆分", operDetail = "项目拆分", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "项目拆分")
    @RequestMapping ("splitProject")
    public Result splitProject(@RequestBody @Validated ProjectSplitReq req) {
        return projectInfoService.splitProject(req);
    }

    /**
     * 查询可以合并的项目
     *
     * @param req
     * @return
     */
    @Log (operName = "查询可以合并的项目", operDetail = "查询可以合并的项目", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询可以合并的项目")
    @RequestMapping ("findMergeProject")
    public Result<List<ProjectSplitResp>> findMergeProject(@RequestBody @Validated SimpleId req) {
        return projectInfoService.findMergeProject(req);
    }

    /**
     * 合并项目
     *
     * @param req
     * @return
     */
    @Log (operName = "合并项目", operDetail = "合并项目", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "合并项目")
    @RequestMapping ("mergeProject")
    public Result mergeProject(@RequestBody @Validated ProjectMergeReq req) {
        return projectInfoService.mergeProject(req);
    }
}
