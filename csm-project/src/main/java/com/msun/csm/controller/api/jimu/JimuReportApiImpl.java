package com.msun.csm.controller.api.jimu;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.enums.projform.ApiProjectViolationTypes;
import com.msun.csm.common.model.Result;
import com.msun.csm.controller.jimu.JimuReportApi;
import com.msun.csm.model.jimu.dto.JmReportCustomInfoDTO;
import com.msun.csm.model.jimu.dto.ProjectDataParamerDTO;
import com.msun.csm.model.jimu.dto.ProjectPeriodDTO;
import com.msun.csm.model.jimu.req.ProjectDurationStatisticsReq;
import com.msun.csm.model.jimu.req.QueryProductAuthDataReq;
import com.msun.csm.service.jimu.IJimuQueryService;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/8/23
 */
@Slf4j
@RestController
public class JimuReportApiImpl implements JimuReportApi {

    @Resource
    private IJimuQueryService jimuService;

    /**
     * 查询项目阶段时间数据
     *
     * @return
     */
    @Override
    public Result findProjectPeriod(Date onlinetimeBegin, Date onlinetimeEnd, Date worktimeBegin,
                                    Date worktimeEnd, String customName, String upgradationType,
                                    Integer customDeliverStatus) {
        ProjectPeriodDTO projectPeriodDTO = new ProjectPeriodDTO();
        projectPeriodDTO.setCustomName(customName);
        projectPeriodDTO.setOnlinetimeBegin(onlinetimeBegin);
        projectPeriodDTO.setOnlinetimeEnd(onlinetimeEnd);
        if (ObjectUtil.isNotNull(onlinetimeEnd)) {
            projectPeriodDTO.setOnlinetimeEnd(DateUtil.offsetDay(onlinetimeEnd, 1));
        }
        projectPeriodDTO.setWorktimeBegin(worktimeBegin);
        projectPeriodDTO.setWorktimeEnd(worktimeEnd);
        if (ObjectUtil.isNotNull(worktimeEnd)) {
            projectPeriodDTO.setWorktimeEnd(DateUtil.offsetDay(worktimeEnd, 1));
        }
        if (upgradationType != null && !"".equals(upgradationType)) {
            String[] str = upgradationType.split(",");
            List<Integer> upgradationTypeList = new ArrayList<>();
            if (str != null && str.length > 0) {
                for (int i = 0; i < str.length; i++) {
                    upgradationTypeList.add(Integer.parseInt(str[i]));
                }
            }
            projectPeriodDTO.setUpgradationTypeList(upgradationTypeList);
        }
        projectPeriodDTO.setCustomDeliverStatus(customDeliverStatus);
        return jimuService.findProjectPeriod(projectPeriodDTO);
    }

    /**
     * @param projectPeriodDTO
     * @return
     */
    @Override
    public Result findProjectPeriodPost(ProjectPeriodDTO projectPeriodDTO) {
        if (projectPeriodDTO.getUpgradationType() != null && !"".equals(projectPeriodDTO.getUpgradationType())) {
            String[] str = projectPeriodDTO.getUpgradationType().split(",");
            List<Integer> upgradationTypeList = new ArrayList<>();
            if (str != null && str.length > 0) {
                for (int i = 0; i < str.length; i++) {
                    upgradationTypeList.add(Integer.parseInt(str[i]));
                }
            }
            projectPeriodDTO.setUpgradationTypeList(upgradationTypeList);
        }
        return jimuService.findProjectPeriod(projectPeriodDTO);
    }

    /**
     * 查询项目阶段时间数据
     *
     * @return
     */
    @Override
    public Result findReportCustomInfo(JmReportCustomInfoDTO reportCustomInfoDTO) {
        return jimuService.findReportCustomInfo(reportCustomInfoDTO);
    }


    /**
     * 查询项目交付核查统计数据
     *
     * @return
     */
    @Override
    public Result findProjectData(JmReportCustomInfoDTO reportCustomInfoDTO) {
        return jimuService.findProjectData(reportCustomInfoDTO);
    }

    /**
     * 查询项目交付核查统计明细数据(调研后新增数量: 设备)
     *
     * @param reportCustomInfoDTO
     * @return
     */
    @Override
    public Result findProjectEquipDataDetail(ProjectDataParamerDTO reportCustomInfoDTO) {
        reportCustomInfoDTO.setType(ApiProjectViolationTypes.SURVEYEQUIPMENT.getCode());
        return jimuService.findProjectDataDetail(reportCustomInfoDTO);
    }

    /**
     * 查询项目交付核查统计明细数据(调研后新增数量: 接口)
     *
     * @param reportCustomInfoDTO
     * @return
     */
    @Override
    public Result findProjectThirdDataDetail(ProjectDataParamerDTO reportCustomInfoDTO) {
        reportCustomInfoDTO.setType(ApiProjectViolationTypes.SURVEYINTERFACE.getCode());
        return jimuService.findProjectDataDetail(reportCustomInfoDTO);
    }

    /**
     * 查询项目交付核查统计明细数据(调研后新增数量: 需求)
     *
     * @param reportCustomInfoDTO
     * @return
     */
    @Override
    public Result findProjectSpecialDataDetail(ProjectDataParamerDTO reportCustomInfoDTO) {
        reportCustomInfoDTO.setType(ApiProjectViolationTypes.SURVEYSPECIAL.getCode());
        return jimuService.findProjectDataDetail(reportCustomInfoDTO);
    }

    /**
     * 查询项目交付核查统计明细数据(调研后新增数量: 小硬件)
     *
     * @param reportCustomInfoDTO
     * @return
     */
    @Override
    public Result findProjectHardWareDetail(ProjectDataParamerDTO reportCustomInfoDTO) {
        reportCustomInfoDTO.setType(ApiProjectViolationTypes.SURVEYHARDWARE.getCode());
        return jimuService.findProjectDataDetail(reportCustomInfoDTO);
    }

    /**
     * 入驻后增加产品
     *
     * @param reportCustomInfoDTO
     * @return
     */
    @Override
    public Result findProjectProductDetail(ProjectDataParamerDTO reportCustomInfoDTO) {
        reportCustomInfoDTO.setType(ApiProjectViolationTypes.ENTERPRODUCT.getCode());
        return jimuService.findProjectDataDetail(reportCustomInfoDTO);
    }

    /**
     * 授权数量
     *
     * @param reportCustomInfoDTO
     * @return
     */
    @Override
    public Result findProjectAuthorizationDetail(ProjectDataParamerDTO reportCustomInfoDTO) {
        reportCustomInfoDTO.setType(ApiProjectViolationTypes.ENTERAUTHORIZATION.getCode());
        return jimuService.findProjectDataDetail(reportCustomInfoDTO);
    }

    /**
     * 待办
     *
     * @param reportCustomInfoDTO
     * @return
     */
    @Override
    public Result findProjectProductTaskDetail(ProjectDataParamerDTO reportCustomInfoDTO) {
        reportCustomInfoDTO.setType(ApiProjectViolationTypes.UNFINISHEDPRODUCT.getCode());
        return jimuService.findProjectDataDetail(reportCustomInfoDTO);
    }

    /**
     * 接口
     *
     * @param reportCustomInfoDTO
     * @return
     */
    @Override
    public Result findProjectThirdDetail(ProjectDataParamerDTO reportCustomInfoDTO) {
        reportCustomInfoDTO.setType(ApiProjectViolationTypes.UNFINISHEDINTERFACE.getCode());
        return jimuService.findProjectDataDetail(reportCustomInfoDTO);
    }

    /**
     * 设备
     *
     * @param reportCustomInfoDTO
     * @return
     */
    @Override
    public Result findProjectEquipDetail(ProjectDataParamerDTO reportCustomInfoDTO) {
        reportCustomInfoDTO.setType(ApiProjectViolationTypes.UNFINISHEDEQUIPMENT.getCode());
        return jimuService.findProjectDataDetail(reportCustomInfoDTO);
    }

    /**
     * 表单
     *
     * @param reportCustomInfoDTO
     * @return
     */
    @Override
    public Result findProjectFormDetail(ProjectDataParamerDTO reportCustomInfoDTO) {
        reportCustomInfoDTO.setType(ApiProjectViolationTypes.UNFINISHEDFORM.getCode());
        return jimuService.findProjectDataDetail(reportCustomInfoDTO);
    }

    /**
     * 报表
     *
     * @param reportCustomInfoDTO
     * @return
     */
    @Override
    public Result findProjectReportDetail(ProjectDataParamerDTO reportCustomInfoDTO) {
        reportCustomInfoDTO.setType(ApiProjectViolationTypes.UNFINISHEDREPORT.getCode());
        return jimuService.findProjectDataDetail(reportCustomInfoDTO);
    }

    /**
     * @param reportCustomInfoDTO
     * @return
     */
    @Override
    public Result findProjectBaseData(ProjectDataParamerDTO reportCustomInfoDTO) {
        return jimuService.findProjectBaseData(reportCustomInfoDTO);
    }

    /**
     * 查询云健康升级进度
     *
     * @return
     */
    @Override
    public Result findCloudUpgradeProgress(ProjectPeriodDTO paramer) {
        return jimuService.findCloudUpgradeProgress(paramer);
    }

    /**
     * 项目工期统计
     *
     * @return
     */
    @Override
    public Result projectDurationStatistics(ProjectDurationStatisticsReq req) {
        if (ObjectUtil.isNotNull(req.getSettleInTimeEnd())) {
            req.setSettleInTimeEnd(DateUtil.offsetDay(req.getSettleInTimeEnd(), 1));
        }
        if (ObjectUtil.isNotNull(req.getApplyAcceptTimeEnd())) {
            req.setApplyAcceptTimeEnd(DateUtil.offsetDay(req.getApplyAcceptTimeEnd(), 1));
        }
        if (ObjectUtil.isNotNull(req.getAcceptTimeEnd())) {
            req.setAcceptTimeEnd(DateUtil.offsetDay(req.getAcceptTimeEnd(), 1));
        }
        if (ObjectUtil.isNotNull(req.getOnlinetimeEnd())) {
            req.setOnlinetimeEnd(DateUtil.offsetDay(req.getOnlinetimeEnd(), 1));
        }
        if (ObjectUtil.isNotNull(req.getWorktimeEnd())) {
            req.setWorktimeEnd(DateUtil.offsetDay(req.getWorktimeEnd(), 1));
        }
        return jimuService.projectDurationStatistics(req);
    }

    /**
     * @param projectPeriodDTO
     * @return
     */
    @Override
    public Result findProjectDataPost(ProjectPeriodDTO projectPeriodDTO) {
        if (projectPeriodDTO.getUpgradationType() != null && !"".equals(projectPeriodDTO.getUpgradationType())) {
            String[] str = projectPeriodDTO.getUpgradationType().split(",");
            List<Integer> upgradationTypeList = new ArrayList<>();
            if (str != null && str.length > 0) {
                for (int i = 0; i < str.length; i++) {
                    upgradationTypeList.add(Integer.parseInt(str[i]));
                }
            }
            projectPeriodDTO.setUpgradationTypeList(upgradationTypeList);
        }
        return jimuService.findProjectPeriodByProjectInfo(projectPeriodDTO);
    }

    /**
     * 客户医院授权数据查询
     *
     * @param req
     * @return
     */
    @Override
    public Result queryProductAuthData(QueryProductAuthDataReq req) {
        return jimuService.queryProductAuthData(req);
    }
}
