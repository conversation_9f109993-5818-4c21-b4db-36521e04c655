package com.msun.csm.controller.proj;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.req.ConfigAgentScenarioDictReq;
import com.msun.csm.model.req.ConfigAgentScenarioDictSaveReq;
import com.msun.csm.model.resp.DictAgentChatDropdownResp;
import com.msun.csm.model.resp.DictAgentChatScenarioConfigResp;
import com.msun.csm.service.config.ConfigAgentScenarioDict;

/**
 * AI智能体场景检测配置管理
 *
 * @Description: AI智能体场景检测配置管理
 * @Author:
 * @Date: 2025/7/7
 */
@Slf4j
@RestController
@RequestMapping("/agentScenarioConfigDict")
@Api(tags = "AI智能体场景检测配置管理")
public class ConfigAgentScenarioDictController {

    @Resource
    private ConfigAgentScenarioDict configAgentScenarioDict;

    /**
     * 查询AI智能体场景检测配置
     *
     * @param req 查询条件
     * @return 配置列表
     */
    @Log(operName = "查询AI智能体场景检测配置", operDetail = "查询AI智能体场景检测配置", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询AI智能体场景检测配置")
    @ApiOperation("查询AI智能体场景检测配置")
    @PostMapping("/getDict")
    public Result<List<DictAgentChatScenarioConfigResp>> getAgentScenarioConfig(@Valid @RequestBody ConfigAgentScenarioDictReq req) {
        log.info("查询条件：{}", req);
        return configAgentScenarioDict.getAgentScenarioConfig(req);
    }

    /**
     * 删除AI智能体场景检测配置
     *
     * @param req 删除请求参数
     * @return 删除结果
     */
    @Log(operName = "删除AI智能体场景检测配置", operDetail = "删除AI智能体场景检测配置", intLogType = Log.IntLogType.SELF_SYS, cnName = "删除AI智能体场景检测配置")
    @ApiOperation("删除AI智能体场景检测配置")
    @PostMapping("/deleteDict")
    public Result<Boolean> deleteAgentScenarioConfig(@Valid @RequestBody ConfigAgentScenarioDictReq req) {
        log.info("删除AI智能体场景配置，配置ID：{}", req.getAgentScenarioConfigId());
        return configAgentScenarioDict.deleteAgentScenarioConfig(req.getAgentScenarioConfigId());
    }

    /**
     * 保存AI智能体场景检测配置（新增或修改）
     *
     * @param req 保存请求参数
     * @return 保存结果
     */
    @Log(operName = "保存AI智能体场景检测配置", operDetail = "保存AI智能体场景检测配置", intLogType = Log.IntLogType.SELF_SYS, cnName = "保存AI智能体场景检测配置")
    @ApiOperation("保存AI智能体场景检测配置（新增或修改）")
    @PostMapping("/saveDict")
    public Result<Boolean> saveAgentScenarioConfig(@Valid @RequestBody ConfigAgentScenarioDictSaveReq req) {
        log.info("保存AI智能体场景配置，请求参数：{}", req);
        return configAgentScenarioDict.saveAgentScenarioConfig(req);
    }

    /**
     * 查询智能体下拉列表
     *
     * @return 智能体下拉列表
     */
    @Log(operName = "查询智能体下拉列表", operDetail = "查询智能体下拉列表", intLogType = Log.IntLogType.SELF_SYS, cnName = "查询智能体下拉列表")
    @ApiOperation("查询智能体下拉列表")
    @GetMapping("/getAgentDropdownList")
    public Result<List<DictAgentChatDropdownResp>> getAgentChatDropdownList() {
        log.info("查询智能体下拉列表");
        return configAgentScenarioDict.getAgentChatDropdownList();
    }
}
