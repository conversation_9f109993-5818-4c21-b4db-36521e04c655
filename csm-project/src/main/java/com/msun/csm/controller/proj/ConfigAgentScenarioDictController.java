package com.msun.csm.controller.proj;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.req.ConfigAgentScenarioDictReq;
import com.msun.csm.model.resp.DictAgentChatScenarioConfigResp;
import com.msun.csm.service.config.ConfigAgentScenarioDict;

/**
 * AI图片检测配置控制器
 * @Description: AI智能体场景配置管理
 * @Author:
 * @Date: 2025/7/7
 */
@Slf4j
@RestController
@RequestMapping("/agentScenarioConfigDict")
@Api(tags = "AI智能体场景配置管理")
public class ConfigAgentScenarioDictController {

    @Resource
    private ConfigAgentScenarioDict configAgentScenarioDict;

    /**
     * 查询所有AI图片检测配置
     * @return 配置列表
     */
    @Log(operName = "查询AI图片检测配置", operDetail = "查询所有AI智能体场景检测配置",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询AI智能体场景检测配置")
    @ApiOperation("查询所有AI智能体场景检测配置")
    @PostMapping("/getConfigDict")
    public Result<List<DictAgentChatScenarioConfigResp>> getAgentScenarioConfig() {
        log.info("查询AI检测配置");
        return configAgentScenarioDict.getAgentScenarioConfig();
    }
}
