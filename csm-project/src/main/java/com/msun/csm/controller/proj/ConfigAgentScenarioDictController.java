package com.msun.csm.controller.proj;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.resp.custombackendlimit.QueryProjectDataResp;

/**
 * ai图片检测配置
 *
 * @Description:
 * @Author:
 * @Date: 2025/7/7
 */
@Slf4j
@RestController
@RequestMapping("/agentScenarioConfigDict")
public class ConfigAgentScenarioDictController {

    @Resource
    private ConfigAgentScenarioDictService configAgentScenarioDictService;

    /**
     * 查询ai图片检测配置
     *
     * @return
     */
    @PostMapping("/getDict")
    public Result<PageInfo<QueryProjectDataResp>> queryData() {
        log.info("查询ai图片检测配置");
        return configAgentScenarioDictService.gettable();
    }
}
