package com.msun.csm.controller.proj;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.enums.DictProjectPlanItemEnum;
import com.msun.csm.common.enums.ProjectPlanStatusEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.*;
import com.msun.csm.dao.entity.proj.ProjProjectPlan;
import com.msun.csm.dao.entity.proj.ProjTodoTask;
import com.msun.csm.model.req.projectplan.*;
import com.msun.csm.model.resp.projectplan.ProjectPlanResp;
import com.msun.csm.service.proj.ProjProjectConfigService;
import com.msun.csm.service.proj.ProjProjectPlanService;
import com.msun.csm.service.proj.ProjTodoTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/11/29
 */

@Slf4j
@Api(tags = "项目计划")
@RestController
@RequestMapping("/projectPlan")
public class ProjProjectPlanController {

    @Resource
    private ProjProjectPlanService projectPlanService;
    @Resource
    private ProjTodoTaskService todoTaskService;

    @Resource
    private ProjProjectConfigService projectConfigService;


    /**
     * 分页查询项目计划
     *
     * @param req
     * @return
     * @Description: 分页查询项目计划
     */
    @Log(operName = "查询项目计划", operDetail = "查询项目计划", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询项目计划")
    @ResponseBody
    @PostMapping(value = "/queryData")
    public Result<List<ProjectPlanResp>> queryData(@RequestBody @Valid QueryProjectPlanReq req) {
        return projectPlanService.queryData(req);
    }

    /**
     * 保存或更新数据
     *
     * @param req
     * @return
     * @Description: 保存或更新数据
     */
    @Log(operName = "保存或更新数据", operDetail = "保存或更新数据", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "保存或更新数据")
    @ResponseBody
    @PostMapping(value = "/saveProjectPlan")
    public Result saveProjectPlan(@RequestBody @Valid SaveProjectPlanReq req) {
        return projectPlanService.saveProjectPlan(req);
    }

    /**
     * 删除数据-项目计划、我的待办统一一个接口
     *
     * @param req
     * @return
     * @Description: 删除数据-项目计划、我的待办统一一个接口
     */
    @Log(operName = "删除数据", operDetail = "删除数据", operLogType = Log.LogOperType.DEL,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "删除数据")
    @ResponseBody
    @PostMapping(value = "/delData")
    public Result delData(@RequestBody @Valid DelDataReq req) {
        if (req.getType() == 1) {
            log.info("删除项目计划，主键id：{}", req.getId());
            projectPlanService.deleteByPrimaryKey(req.getId());
            return Result.success();
        }
        if (req.getType() == 2) {
            log.info("删除我的待办，主键id：{}", req.getId());
            todoTaskService.deleteByPrimaryKey(req.getId());
            return Result.success();
        }
        return Result.success();
    }

    /**
     * 确认完成、重点关注-项目计划、我的待办统一一个接口
     *
     * @param req
     * @return
     * @Description: 确认完成、重点关注-项目计划、我的待办统一一个接口
     */
    @Log(operName = "确认完成、重点关注", operDetail = "确认完成、重点关注", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "确认完成、重点关注")
    @ResponseBody
    @PostMapping(value = "/completeOrAttentionData")
    public Result completeOrAttentionData(@RequestBody @Valid CompleteOrAttentionDataReq req) {
        if (req.getStatus() == null && req.getAttentionFlag() == null) {
            throw new CustomException("更新数据参数有无，完成状态、关注状态不能同时为空");
        }
        if (req.getType() == 1) {
            log.info("确认完成、重点关注项目计划，主键id：{}", req.getId());
            projectPlanService.updateStatusByProjectPlanId(req.getId(), ProjectPlanStatusEnum.getPlanStatusEnumByCode(req.getStatus()));
            projectPlanService.followOrUnfollow(req.getId(), req.getAttentionFlag());
            return Result.success();
        }
        if (req.getType() == 2) {
            log.info("确认完成、重点关注我的待办，主键id：{}", req.getId());
            ProjTodoTask todoTask = new ProjTodoTask();
            todoTask.setTodoTaskId(req.getId());
            todoTask.setStatus(req.getStatus());
            todoTask.setAttentionFlag(req.getAttentionFlag());
            todoTaskService.updateByPrimaryKeySelective(todoTask);
            return Result.success();
        }
        return Result.success();
    }


    /**
     * 查询阶段信息
     *
     * @param req
     * @return
     * @Description: 查询阶段信息
     */
    @Log(operName = "查询阶段信息", operDetail = "查询阶段信息", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询阶段信息")
    @ResponseBody
    @PostMapping(value = "/queryStage")
    public Result queryStage(@RequestBody @Valid SimpleId req) {
        return projectPlanService.queryStage(req.getId());
    }

    /**
     * 根据项目ID查询任务下拉框数据
     *
     * @param simpleId 项目ID
     */
    @ResponseBody
    @PostMapping(value = "/queryPlanItem")
    public Result<List<BaseIdCodeNameResp>> queryPlanItem(@RequestBody @Valid SimpleId simpleId) {
        if (simpleId.getId() == null) {
            return Result.fail("项目id不能为空");
        }
        return Result.success(projectPlanService.queryPlanItem(simpleId));
    }

    /**
     * 根据项目计划ID查询项目计划信息
     */
    @Log(operName = "根据项目计划ID查询项目计划信息", operDetail = "根据项目计划ID查询项目计划信息", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.SELF_SYS, cnName = "根据项目计划ID查询项目计划信息")
    @ResponseBody
    @PostMapping(value = "/queryProjectPlanInfo")
    public Result<ProjectPlanResp> queryProjectPlanInfo(@RequestBody @Valid QueryProjectPlanInfoReq req) {
        return projectPlanService.queryProjectPlanInfo(req);
    }

    /**
     * 校验项目计划的前置节点是否都已完成
     *
     * @param planId 项目计划id
     */
    @Log(operName = "校验项目计划的前置节点是否都已完成", operDetail = "校验项目计划的前置节点是否都已完成", intLogType = Log.IntLogType.SELF_SYS, cnName = "校验项目计划的前置节点是否都已完成")
    @ApiOperation("校验项目计划的前置节点是否都已完成")
    @GetMapping("/verifyProjectPlanInfo")
    Result<String> verifyProjectPlanInfo(Long planId) {
        return projectPlanService.verifyProjectPlanInfo(planId);
    }

    @Log(operName = "同步待办任务的总数量", operDetail = "同步待办任务的总数量", intLogType = Log.IntLogType.SELF_SYS, cnName = "同步待办任务的总数量")
    @ApiOperation("同步待办任务的总数量")
    @GetMapping("/todoTaskTotalCountSync")
    Result<?> todoTaskTotalCountSync(Long planId) {
        return projectPlanService.todoTaskTotalCountSync(planId);
    }

    /**
     * 变更双击项目卡片后的模式
     *
     * @param projectInfoId 项目ID
     */
    @Log(operName = "变更双击项目卡片后的模式", operDetail = "变更双击项目卡片后的模式", intLogType = Log.IntLogType.SELF_SYS, cnName = "变更双击项目卡片后的模式")
    @PostMapping("/changeViewModel")
    Result<Void> changeViewModel(@RequestBody ProjectInfoId projectInfoId) {
        return projectConfigService.changeViewModel(projectInfoId.getProjectInfoId());
    }

    /**
     * 变更双击项目卡片后的模式
     *
     * @param projectInfoId 项目ID
     */
    @Log(operName = "初始化项目计划", operDetail = "初始化项目计划", intLogType = Log.IntLogType.SELF_SYS, cnName = "初始化项目计划")
    @PostMapping("/initProjectPlan")
    Result<Void> initProjectPlan(@RequestBody ProjectInfoId projectInfoId) {
        return projectPlanService.initProjectPlan(projectInfoId.getProjectInfoId());
    }

    /**
     * 获取项目计划节点信息
     *
     * @param projectInfoId 项目ID
     */
    @Log(operName = "获取项目计划节点信息", operDetail = "获取项目计划节点信息", intLogType = Log.IntLogType.SELF_SYS, cnName = "获取项目计划节点信息")
    @PostMapping("/getProjectPlanByProjectAndItemCode")
    Result<ProjProjectPlan> getProjectPlanByProjectAndItemCode(@RequestBody GetProjectPlanParam projectInfoId) {
        ProjProjectPlan projectPlan = projectPlanService.getProjectPlanByProjectInfoIdAndItemCode(projectInfoId.getProjectInfoId(), DictProjectPlanItemEnum.getPlanItemByCode(projectInfoId.getPlanItemCode()));
        return Result.success(projectPlan);
    }


}
