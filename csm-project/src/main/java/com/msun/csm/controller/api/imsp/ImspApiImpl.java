package com.msun.csm.controller.api.imsp;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.msun.csm.common.annotation.CsmSign;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.enums.YunweiDeplyTypeEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.ResponseData;
import com.msun.csm.common.model.Result;
import com.msun.csm.controller.imsp.ImspApi;
import com.msun.csm.model.dto.UpdateMilestoneDTO;
import com.msun.csm.model.imsp.CustomerParamerDTO;
import com.msun.csm.model.imsp.HospitalUpdateStatusAndProductDeployDTO;
import com.msun.csm.model.imsp.ProjCustomInfoResp;
import com.msun.csm.model.imsp.SyncAcceptResult;
import com.msun.csm.model.imsp.SyncYunRenewDTO;
import com.msun.csm.model.imsp.UpdateMilestoneReq;
import com.msun.csm.model.imsp.UpdateProjectUserRelationReq;
import com.msun.csm.service.api.ApiYunyingService;
import com.msun.csm.service.proj.ProjMilestoneInfoService;
import com.msun.csm.service.proj.ProjProjectAcceptanceService;
import com.msun.csm.service.proj.ProjProjectInfoService;
import com.msun.csm.service.proj.ProjProjectMemberService;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderService;
import com.msun.csm.service.proj.disastercloud.ProjDisasterRecoveryInfoService;
import com.msun.csm.util.RedisUtil;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/28/14:39
 */
@Slf4j
@RestController
public class ImspApiImpl implements ImspApi {

    @Resource
    @Lazy
    private ProjProjectAcceptanceService projProjectAcceptanceService;

    @Resource
    private ProjApplyOrderService applyOrderService;

    @Resource
    private ProjProjectMemberService projProjectMemberService;

    @Resource
    private ProjMilestoneInfoService milestoneInfoService;
    @Resource
    private ApiYunyingService apiYunyingService;

    @Resource
    private ProjDisasterRecoveryInfoService disasterRecoveryInfoService;

    @Resource
    private ProjProjectInfoService projProjectInfoService;

    @Resource
    private RedisUtil redisUtil;

    /**
     * 申请开通老系统回调修改状态与产品部署
     *
     * @param param
     * @return
     */
    @Log(operName = "运维平台回调", operDetail = "运维平台回调", intLogType = Log.IntLogType.SELF_SYS, cnName = "运维平台回调-运维平台触发",
            saveParam = true)
    @Override
    @CsmSign
    public Result<String> saveApplyHistoryLog(Map<String, Object> param) {
        log.info("param: {}", JSONObject.toJSONString(param));
        return saveApplyHistoryLogWrapper(param);
    }

    /**
     * 处理运维平台回调
     * <p>防止重复提交</p>
     *
     * @param param 请求参数, 含新环境、新医院、新产品、容灾
     * @return 成功失败反馈
     */
    public Result<String> saveApplyHistoryLogWrapper(Map<String, Object> param) {
        HospitalUpdateStatusAndProductDeployDTO dto = BeanUtil.mapToBean(param,
                HospitalUpdateStatusAndProductDeployDTO.class, true, null);
        String key = "apply:yunwei:" + dto.getDeliverPlatformApplyId();
        if (ObjectUtil.isNotEmpty(redisUtil.get(key))) {
            return Result.fail("请勿重复提交请求, 正在处理中, 稍后再试.");
        }
        redisUtil.set(key, dto.getDeliverPlatformApplyId(), 3L, TimeUnit.MINUTES);
        // 分流, 容灾与其他审核回调做区分(新环境、产品、医院)
        try {
            if (ObjectUtil.isNotEmpty(dto.getDeployResourceType()) && dto.getDeployResourceType() == YunweiDeplyTypeEnum.DISASTER_RECOVERY.getCode()) {
                return disasterRecoveryInfoService.saveApplyHistoryLog(dto);
            } else {
                return applyOrderService.saveApplyHistoryLog(dto);
            }
        } finally {
            redisUtil.del(key);
        }
    }

    @Log(operName = "运营平台回调项目验收", operDetail = "运营平台回调项目验收", intLogType = Log.IntLogType.OPER_SYSTEM, cnName =
            "运营平台回调-运营平台触发", saveParam = true)
    @Override
    @CsmSign
    public Result syncAcceptResult(SyncAcceptResult result) {
        return projProjectAcceptanceService.syncAcceptResult(result);
    }

    /**
     * 保存项目用户关系
     *
     * @param req
     * @return
     */
    @Log(operName = "老项目添加删除项目成员同步数据", operDetail = "老项目添加删除项目成员同步数据",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "老项目回调-老项目添加删除项目成员同步数据",
            saveParam = true)
    @Override
    @CsmSign
    public Result updateProjectUserRelation(@Valid UpdateProjectUserRelationReq req) {
        return projProjectMemberService.updateProjectUserRelation(req);
    }

    /**
     * 更新里程碑节点状态
     *
     * @param req
     * @return
     */
    @Log(operName = "更新里程碑节点状态", operDetail = "更新里程碑节点状态",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "老项目回调-更新里程碑节点状态",
            saveParam = true)
    @Override
    @CsmSign
    public Result updateMilestone(@Valid UpdateMilestoneReq req) {
        log.info("ImspApiImpl.updateMilestone更新里程碑节点状态，参数={}", JSON.toJSONString(req));
        UpdateMilestoneDTO updateMilestoneDTO = new UpdateMilestoneDTO();
        updateMilestoneDTO.setMilestoneInfoId(req.getMilestoneInfoId());
        updateMilestoneDTO.setMilestoneStatus(req.getMilestoneStatus());
        updateMilestoneDTO.setActualCompTime(req.getActualCompTime());
        updateMilestoneDTO.setNodeHeadId(req.getNodeHeadId());
        milestoneInfoService.updateMilestone(updateMilestoneDTO);
        return Result.success();
    }

    @Log(operName = "老项目转发-云资源续签", operDetail = "老项目转发-云资源续签",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "老项目转发-云资源续签",
            saveParam = true)
    @Override
    public ResponseData<String> syncYunRenew(SyncYunRenewDTO dto) {
        throw new CustomException("已停用转发-云资源续签接口");
    }

    /**
     * 查询数据
     *
     * @param dto
     * @return
     */
    @Log(operName = "查询数据", operDetail = "查询数据",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询数据",
            saveParam = true)
    @Override
    @CsmSign
    public List<ProjCustomInfoResp> getCustomerList(CustomerParamerDTO dto) {
        return apiYunyingService.getCustomerList(dto);
    }

    /**
     * 手动处理三方接口及报表限制
     *
     * @param param
     * @return
     */
    @Override
    @CsmSign
    public Result saveTmpHospitalLimit(Map<String, Object> param) {

        String projectInfoId = param.get("projectInfoId").toString();
        List<Long> projectInfoIds = new ArrayList<>();
        String[] ids = projectInfoId.split(",");
        projectInfoIds = Arrays.stream(ids).map(Long::valueOf).collect(Collectors.toList());
        if (projectInfoIds != null) {
            for (Long id : projectInfoIds) {
//                projProjectAcceptanceService.saveTmpHospitalLimit(id);
                projProjectAcceptanceService.saveConfigCustomFormLimit(projProjectInfoService.selectByPrimaryKey(id));
            }
        }


        return Result.success("成功");
    }
}
