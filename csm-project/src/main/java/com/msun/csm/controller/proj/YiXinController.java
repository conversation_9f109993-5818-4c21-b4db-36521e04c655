package com.msun.csm.controller.proj;


import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.model.Result;
import com.msun.csm.model.req.QueryYxConfigParam;
import com.msun.csm.model.req.SaveYxConfigParam;
import com.msun.csm.model.req.YxConfigInfo;
import com.msun.csm.service.proj.YiXinService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/yiXin")
public class YiXinController {

    @Resource
    private YiXinService yiXinService;

    /**
     * 迁移数据
     */
    @ResponseBody
    @PostMapping(value = "/qianYi")
    public Result<Void> queryData() {
        boolean result = yiXinService.qianYi();
        if (result) {
            return Result.success(null, "迁移老平台数据成功");
        }
        return Result.fail("迁移老平台数据失败");
    }

    /**
     * 查询数据
     */
    @ResponseBody
    @PostMapping(value = "/queryYxConfig")
    public Result<YxConfigInfo> queryYxConfig(@RequestBody @Valid QueryYxConfigParam req, HttpServletRequest httpServletRequest) {
        YxConfigInfo yxConfigInfo = yiXinService.queryYxConfig(req, httpServletRequest);
        return Result.success(yxConfigInfo, "成功");
    }

    /**
     * 保存数据
     */
    @ResponseBody
    @PostMapping(value = "/saveYxConfig")
    public Result<Void> saveYxConfig(@RequestBody @Valid SaveYxConfigParam req) {
        boolean yxConfigInfo = yiXinService.saveYxConfig(req);
        if (!yxConfigInfo) {
            return Result.fail("保存失败");
        }
        try {
            QueryYxConfigParam queryYxConfigParam = new QueryYxConfigParam();
            queryYxConfigParam.setHospitalInfoId(req.getHospitalInfoId());
            queryYxConfigParam.setYyProductId(req.getYyProductId());
            queryYxConfigParam.setProjectInfoId(req.getProjectInfoId());
            Result<Void> voidResult = yiXinService.syncConfigToBaiLing(queryYxConfigParam);
            if (voidResult.getCode() == 1) {
                return Result.success(null, "保存成功");
            }
            return Result.fail("保存成功！" + voidResult.getMsg());
        } catch (Exception e) {
            log.error("保存数据后将配置同步给百灵，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail("保存成功！同步配置到百灵发生异常：" + e.getMessage());
        }
    }

}
