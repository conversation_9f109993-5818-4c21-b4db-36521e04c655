package com.msun.csm.controller.dict;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.DictProductVsEmpowerDTO;
import com.msun.csm.model.vo.dict.DictProductVsEmpowerVO;
import com.msun.csm.service.dict.DictProductVsEmpowerService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "工单产品与授权对照")
@RestController
@RequestMapping("/dictProductVsEmpower")
public class DictProductVsEmpowerController {

    @Resource
    private DictProductVsEmpowerService dictProductVsEmpowerService;

    /**
     * 查询工单产品对照授权菜单列表
     * @param orderProductName
     * @param msunHealthModuleCode
     * @param msunHealthModule
     * @return
     */
    @Log(
            operName = "查询工单产品对照授权菜单列表", operDetail = "查询工单产品对照授权菜单列表",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询工单产品对照授权菜单列表"
    )
    @ApiOperation("查询工单产品对照授权菜单列表")
    @GetMapping(value = "/findDictProductVsEmpowerList")
    Result<List<DictProductVsEmpowerVO>> findDictProductVsEmpowerList(@RequestParam("orderProductName") String orderProductName,
                                                                      @RequestParam("msunHealthModuleCode") String msunHealthModuleCode,
                                                                      @RequestParam("msunHealthModule") String msunHealthModule) {
        return dictProductVsEmpowerService.findDictProductVsEmpowerList(orderProductName, msunHealthModuleCode, msunHealthModule);
    }

    /**
     * 新增工单产品对照授权菜单
     * @param dto
     * @return
     */
    @Log(
            operName = "新增工单产品对照授权菜单", operDetail = "新增工单产品对照授权菜单",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "新增工单产品对照授权菜单"
    )
    @ApiOperation("新增工单产品对照授权菜单")
    @PostMapping(value = "/saveDictProductVsEmpower")
    Result saveDictProductVsEmpower(@RequestBody DictProductVsEmpowerDTO dto) {
        return dictProductVsEmpowerService.saveDictProductVsEmpower(dto);
    }

    /**
     * 删除工单产品对照授权菜单
     * @param dto
     * @return
     */
    @Log(
            operName = "删除工单产品对照授权菜单", operDetail = "删除工单产品对照授权菜单",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "删除工单产品对照授权菜单"
    )
    @ApiOperation("删除工单产品对照授权菜单")
    @PostMapping(value = "/deleteDictProductVsEmpower")
    Result deleteDictProductVsEmpower(@RequestBody DictProductVsEmpowerDTO dto) {
        return dictProductVsEmpowerService.deleteDictProductVsEmpower(dto);
    }
}
