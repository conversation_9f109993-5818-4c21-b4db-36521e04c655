//package com.msun.csm.config;
//
//import static com.alibaba.fastjson.schema.JSONSchema.Type.Const;
//
//import org.springframework.data.redis.connection.Message;
//import org.springframework.data.redis.core.RedisTemplate;
//
///**
// * 外部接口消息监听类
// */
//@Slf4j
//@Component
//public static class OutApiMessageListener implements MessageListener {
//
//    @Resource
//    private RedisTemplate redisTemplate;
//
//    @Resource
//    private PatientService patientService;
//
//    @Override
//    public void onMessage(Message message, byte[] pattern) {
//        // 获取消息和频道
//        Object msg = parseMsgObj(message);
//        Object channel = parseChanel(message);
//        // 渠道名称转换
//        if(Const.OUT_API_TOPIC_UPDATE_FEE_STATUS.getTopic().equals(channel.toString())){
//            PatientFeeInfo patientFeeInfo = JSON.parseObject(msg.toString(), PatientFeeInfo.class);
//            patientService.updatePatientStatus(patientFeeInfo);
//        }
//    }
//
//    private Object parseChanel(Message message) {
//        // 获取监听的频道
//        byte[] channelByte = message.getChannel();
//        // 使用字符串序列化器转换
//        return redisTemplate.getStringSerializer().deserialize(channelByte);
//    }
//
//    private Object parseMsgObj(Message message) {
//        byte[] messageBody = message.getBody();
//        // 使用值序列化器转换
//        return redisTemplate.getValueSerializer().deserialize(messageBody);
//    }
//}
