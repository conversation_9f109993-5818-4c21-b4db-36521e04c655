package com.msun.csm.dao.entity.proj;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BackendTeamDeductionDetailVO2 {

    /**
     * 项目ID
     */
    private Long projectInfoId;

    /**
     * 服务类型
     */
    private String serverType;

    /**
     * 阶段编码
     */
    private List<String> stageCode;

    /**
     * 运维确认状态
     */
    private List<Integer> detailRecordStatus;

}
