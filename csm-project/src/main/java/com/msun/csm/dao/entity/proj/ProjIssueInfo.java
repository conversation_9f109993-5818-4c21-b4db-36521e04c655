package com.msun.csm.dao.entity.proj;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2025/1/13
 */

/**
 * 项目问题跟进表主数据表
 */
@ApiModel(description = "项目问题跟进表主数据表")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjIssueInfo {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private Long priority;

    /**
     * 问题分类
     */
    @ApiModelProperty(value = "问题分类")
    private String classification;

    /**
     * 问题描述-富文本
     */
    @ApiModelProperty(value = "问题描述-富文本")
    private String description;

    /**
     * 问题状态
     */
    @ApiModelProperty(value = "问题状态")
    private Long status;

    /**
     * 问题处理结果-富文本
     */
    @ApiModelProperty(value = "问题处理结果-富文本")
    private String result;

    /**
     * 问题负责人，id
     */
    @ApiModelProperty(value = "问题负责人，id")
    private Long chargePerson;

    /**
     * 问题所属科室，手填
     */
    @ApiModelProperty(value = "问题所属科室，手填")
    private String dept;

    /**
     * 问题所属运营平台产品id-如果不满足，-1 其他
     */
    @ApiModelProperty(value = "问题所属运营平台产品id-如果不满足，-1 其他")
    private Integer productId;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private Long createrId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新人id
     */
    @ApiModelProperty(value = "更新人id")
    private Long updaterId;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 逻辑删除【0：否；1：是】
     */
    @ApiModelProperty(value = "逻辑删除【0：否；1：是】")
    private Integer isDeleted;

    /**
     * 问题提出人
     */
    @ApiModelProperty(value = "问题提出人")
    private Long submitterId;

    /**
     * 项目计划表主键
     */
    @ApiModelProperty(value = "项目计划表主键")
    private Long projectPlanId;

    /**
     * 我的待办主键
     */
    @ApiModelProperty(value = "我的待办主键")
    private Long todoTaskId;

    /**
     * 操作来源：supervisionCenter-监管中心验收页面
     */
    private String operationSource;

    /**
     * 扣分记录业务表明细
     */
    private Long businessId;

}
