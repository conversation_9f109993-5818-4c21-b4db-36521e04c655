package com.msun.csm.dao.entity.knowledge;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;


@Data
@TableName(schema = "knowledge", value = "yjk_maintenance_team")
public class BackendTeamInfo {

    /**
     * 主键
     */
    private Long id;

    /**
     * 团队名称
     */
    private String name;

    /**
     * 团队长userId
     */
    private Long userIdLeader;

    /**
     * 团队长姓名
     */
    private String userNameLeader;

    /**
     * 作废标识（0未作废，1已作废）
     */
    private Integer invalidFlag;

    /**
     * 创建人id
     */
    private Long hisCreaterId;

    /**
     * 创建人姓名
     */
    private String hisCreaterName;

    /**
     * 创建时间
     */
    private Date hisCreateTime;

    /**
     * 更新人id
     */
    private Long hisUpdaterId;

    /**
     * 最后更新人姓名
     */
    private String hisUpdaterName;

    /**
     * 最后更新时间
     */
    private Date hisUpdateTime;

    /**
     * 团队类型编码
     */
    private String teamTypeCode;

    /**
     * 团队类型名称
     */
    private String teamTypeName;

    /**
     * 团队负责人的运营平台ID
     */
    private Long userYunyingId;

    /**
     * 团队负责人的运营平台账号
     */
    private String account;

    /**
     * 团队负责人所属部门的运营平台ID
     */
    private Long yyDeptId;

    /**
     * 团队负责人所属部门的运营平台名称
     */
    private String yyDeptName;

}
