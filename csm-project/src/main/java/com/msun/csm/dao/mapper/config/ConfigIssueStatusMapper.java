package com.msun.csm.dao.mapper.config;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.msun.csm.dao.entity.config.ConfigIssueStatus;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/11/29
 */

public interface ConfigIssueStatusMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(ConfigIssueStatus record);

    int insertOrUpdate(ConfigIssueStatus record);

    int insertOrUpdateSelective(ConfigIssueStatus record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(ConfigIssueStatus record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    ConfigIssueStatus selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(ConfigIssueStatus record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(ConfigIssueStatus record);

    int updateBatch(@Param("list") List<ConfigIssueStatus> list);

    int updateBatchSelective(@Param("list") List<ConfigIssueStatus> list);

    int batchInsert(@Param("list") List<ConfigIssueStatus> list);

    /**
     * 查询所有状态
     *
     * @return
     */
    List<ConfigIssueStatus> selectAll();
}
