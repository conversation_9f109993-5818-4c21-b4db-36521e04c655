package com.msun.csm.dao.entity.proj;

import lombok.Data;

import java.util.Date;


@Data
public class UpdateServerTeamDeductionRecordParam {

    /**
     * 主键
     */
    private Long serverTeamDeductionRecordId;

    /**
     * 修改人id
     */
    private Long updaterId;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 来源：首次验收(first)/最终验收(final)
     */
    private String source;

    /**
     * 服务类型
     */
    private String serverTypeCode;

    /**
     * 服务团队运营平台ID
     */
    private Long serverTeamYyId;

    /**
     * 确认单状态：1-未发送、2-待后端确认、3-后端已驳回、4-后端已确认
     */
    private Integer recordStatus;

    /**
     * 质管发送验收确认单时填写的备注
     */
    private String qualityRemark;

    /**
     * 后端运维服务团队填写的备注
     */
    private String backendRemark;

    /**
     * 申请人id
     */
    private Long applyUserId;

    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 确认人id
     */
    private Long confirmUserId;

    /**
     * 确认时间
     */
    private Date confirmTime;

}