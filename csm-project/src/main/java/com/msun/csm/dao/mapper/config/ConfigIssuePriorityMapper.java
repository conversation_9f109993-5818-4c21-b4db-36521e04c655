package com.msun.csm.dao.mapper.config;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.msun.csm.dao.entity.config.ConfigIssuePriority;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/11/29
 */

public interface ConfigIssuePriorityMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(ConfigIssuePriority record);

    int insertOrUpdate(ConfigIssuePriority record);

    int insertOrUpdateSelective(ConfigIssuePriority record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(ConfigIssuePriority record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    ConfigIssuePriority selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(ConfigIssuePriority record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(ConfigIssuePriority record);

    int updateBatch(@Param("list") List<ConfigIssuePriority> list);

    int updateBatchSelective(@Param("list") List<ConfigIssuePriority> list);

    int batchInsert(@Param("list") List<ConfigIssuePriority> list);

    /**
     * 查询所有
     *
     * @return
     */
    List<ConfigIssuePriority> selectAll();
}
