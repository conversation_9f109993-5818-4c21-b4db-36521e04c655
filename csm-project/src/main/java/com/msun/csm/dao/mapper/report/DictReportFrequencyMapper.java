package com.msun.csm.dao.mapper.report;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.dao.entity.report.statis.DictReportFrequencyEntity;

/**
 * 统计报表频率
 *
 * <AUTHOR>
 * @since 2024-09-27 15:27:17
 */
@Mapper
public interface DictReportFrequencyMapper extends BaseMapper<DictReportFrequencyEntity> {

    /**
     * 查询报表频率数据
     * @return
     */
    List<BaseIdNameResp> queryFrequencyData();

    /**
     * 查询报表状态数据
     * @return
     */
    List<BaseIdNameResp> queryStausData();
}
