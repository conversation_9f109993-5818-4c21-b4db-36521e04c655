package com.msun.csm.dao.entity.proj.flowable;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@ApiModel("审批记录")
@Data
@TableName("csm.proj_backend_approve_record")
public class ProjBackendApproveRecord {

    /**
     * 主键
     */
    @TableId
    private Long approveRecordId;

    /**
     * 项目ID
     */
    private Long projectInfoId; //项目ID

    /**
     * 项目名称
     */
    private String projectName; //项目名称

    /**
     * 审批类型ID
     */
    private Long approveTypeId; //审批类型ID

    /**
     * 小前端大后端实施模式、联合实施模式
     */
    private String approveTypeName; //小前端大后端实施模式、联合实施模式

    /**
     *审批描述信息
     */
    private String details; //审批描述信息

    /**
     * 上一个操作节点
     */
    private Long lastOpNodeId; //上一个操作节点

    /**
     * 1审批中 2审批通过 5审批拒绝 6撤销审批
     */
    private Integer completeStatus; //1审批中 2审批通过 5审批拒绝 6撤销审批

    /**
     * 只有走到最后一个节点并且通过或者审批状态为5、6的时候会改为true
     */
    private Boolean completed = false; //只有走到最后一个节点并且通过或者审批状态为5、6的时候会改为true

    /**
     * 逻辑删除【0：否；1：是】
     */
    @ApiModelProperty("逻辑删除【0：否；1：是】")
    @TableLogic
    @TableField(fill = FieldFill.UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    private Integer isDeleted = 0;

    /**
     * 创建人员id
     */
    @ApiModelProperty("创建人员id")
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    private Long createrId = -1L;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime = new Date();

    /**
     * 更新人员id
     */
    @ApiModelProperty("更新人员id")
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    private Long updaterId = -1L;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime = new Date();

    /**
     * 审批团队名称，如果是多个就逗号分割
     */
    private String deptNames; //审批团队名称，如果是多个就逗号分割

    /**
     * 计划上线时间
     */
    private Date planOnlineTime; //计划上线时间

    /**
     * 表单内容
     */
    private String formData; //表单内容

    /**
     * 超时时间
     */
    private Date timeoutAt; //超时时间

    /**
     * 项目单号
     */
    private String projectNumber; //项目单号

    /**
     * 实施类型编码：frontBackendImpl-前后端实施；unionImpl-联合实施
     */
    private String approveTypeCode;

    /**
     * 申请的后端服务团队类型：bustype-业务服务；datatype-数据服务；interfacetype-接口服务
     */
    private String serverType;

    /**
     * 后端服务团队对应的运营平台部门ID
     */
    private Long yyBackendTeamId;

    /**
     * 创建人姓名
     */
    @TableField(exist = false)
    private String createrName; //创建人姓名


    @TableField(exist = false)
    private List<ProjBackendApproveNode> nodes;

    /**
     * 对应的服务经理名称
     */
    @TableField(exist = false)
    private String leaderName;
}
