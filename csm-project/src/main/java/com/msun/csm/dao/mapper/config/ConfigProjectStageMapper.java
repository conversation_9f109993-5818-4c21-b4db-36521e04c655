package com.msun.csm.dao.mapper.config;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.msun.csm.dao.entity.config.ConfigProjectStage;

/**
 * 里程碑项目阶段配置表(ConfigProjectStage)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-09 16:00:50
 */
@Mapper
public interface ConfigProjectStageMapper {

    /**
     * 作废
     *
     * @param record 配置信息
     * @return
     */
    int updateMcInvalid(ConfigProjectStage record);
    /**
     * 新增
     *
     * @param record 配置信息
     * @return
     */
    int insert(ConfigProjectStage record);

    /**
     * 根据id查询
     *
     * @param id 配置信息
     * @return
     */
    ConfigProjectStage selectByPrimaryKey(Long id);
    /**
     * 说明: 根据项目类型：-1.通用；1.单体；2.区域和实施类型：-1.通用；1.老换新；2.新上线查询费作废状态下里程碑项目阶段配置表
     * @param configProjectStage
     * @return:com.msun.csm.dao.entity.config.ConfigProjectStage
     * @author: Yhongmin
     * @createAt: 2024/5/9 17:19
     * @remark: Copyright
      */
    List<ConfigProjectStage> getByUpgradationByParam(ConfigProjectStage configProjectStage);
    
    /**
     * 多条件查询
     *
     * @param record 配置信息
     * @return
     */
    List<ConfigProjectStage> findConfigProjectStage(ConfigProjectStage record);

    /**
     * 修改
     *
     * @param record 配置信息
     * @return
     */
    int updateByPrimaryKey(ConfigProjectStage record);
}
