package com.msun.csm.dao.mapper.proj;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.msun.csm.dao.entity.proj.ProjMessageInfo;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/12/12
 */

public interface ProjMessageInfoMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(ProjMessageInfo record);

    int insertOrUpdate(ProjMessageInfo record);

    int insertOrUpdateSelective(ProjMessageInfo record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(ProjMessageInfo record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    ProjMessageInfo selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(ProjMessageInfo record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(ProjMessageInfo record);

    int updateBatch(@Param("list") List<ProjMessageInfo> list);

    int updateBatchSelective(@Param("list") List<ProjMessageInfo> list);

    int batchInsert(@Param("list") List<ProjMessageInfo> list);
}
