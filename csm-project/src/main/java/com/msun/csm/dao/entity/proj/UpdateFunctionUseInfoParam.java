package com.msun.csm.dao.entity.proj;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateFunctionUseInfoParam {

    /**
     * 主键
     */
    private Long projProductFunctionUseInfoId;

    /**
     * 产品ID
     */
    private Long yyProductId;

    /**
     * 产品应用功能点的功能编码
     */
    private String functionCode;

    /**
     * 功能点使用次数
     */
    private Integer useCount;

}
