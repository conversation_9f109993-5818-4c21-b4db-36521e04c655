package com.msun.csm.dao.mapper.report;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.report.statis.ConfigProductionMethod;
import com.msun.csm.model.resp.statis.ConfigProductionMethodResp;

/**
 * 制作方式表
 *
 * <AUTHOR>
 * @since 2024-09-27 15:27:17
 */
@Mapper
public interface ConfigProductionMethodMapper extends BaseMapper<ConfigProductionMethod> {

    /**
     * 查询制作方式
     * @return
     */
    List<ConfigProductionMethodResp> selectListByProjectInfoId();
}
