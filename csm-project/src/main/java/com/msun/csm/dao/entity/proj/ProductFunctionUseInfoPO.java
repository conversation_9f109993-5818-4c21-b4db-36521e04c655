package com.msun.csm.dao.entity.proj;

import lombok.Data;

import com.msun.csm.common.enums.HospitalTypeEnum;

@Data
public class ProductFunctionUseInfoPO extends ProjProductFunctionUseInfo {

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 运营平台实施产品ID
     */
    private Long yyProductId;

    /**
     * 功能点
     */
    private String functionName;

    /**
     * 功能描述
     */
    private String functionDesc;

    /**
     * 三级医院是否必选，0-非必选，1-必选
     */
    private Integer tertiaryHospitalFlag;

    /**
     * 二级医院是否必选，0-非必选，1-必选
     */
    private Integer secondHospitalFlag;

    /**
     * 一级医院是否必选，0-非必选，1-必选
     */
    private Integer firstHospitalFlag;

    /**
     * 妇幼保健院是否必选，0-非必选，1-必选
     */
    private Integer maternalChildHospitalFlag;

    /**
     * 其他专科医院（眼科医院、口腔医院）是否必选，0-非必选，1-必选
     */
    private Integer otherHospitalFlag;

    /**
     * 是否常用必备业务，比如挂号、缴费等可设置为常用必备业务，0-非常用，1-常用
     */
    private Integer commonFunctionFlag;


    /**
     * 人民医院可用：0-不可用；1-可用
     */
    private Integer peoplesHospitalFlag;

    /**
     * 中医院可用：0-不可用；1-可用
     */
    private Integer chineseHospitalFlag;

    /**
     * 肿瘤医院可用：0-不可用；1-可用
     */
    private Integer tumorHospitalFlag;

    /**
     * 口腔医院可用：0-不可用；1-可用
     */
    private Integer stomatologyHospitalFlag;

    /**
     * 眼科医院可用：0-不可用；1-可用
     */
    private Integer eyeHospitalFlag;


    /**
     * 根据医院等级判断当前功能是否是必备功能
     *
     * @param hospitalLevel 医院等级
     * @return true-当前医院等级下的必备功能；false-当前医院等级下的可选功能
     */
    public boolean isRequiredFunction(String hospitalLevel) {

        final Integer one = Integer.valueOf("1");
        // 医院等级是否必选标记
        boolean hospitalLevelFlag;
        if (HospitalTypeEnum.PEOPLES_HOSPITAL.getHospitalTypeCode().equals(hospitalLevel)) {
            hospitalLevelFlag = one.equals(peoplesHospitalFlag);
        } else if (HospitalTypeEnum.CHINESE_HOSPITAL.getHospitalTypeCode().equals(hospitalLevel)) {
            hospitalLevelFlag = one.equals(chineseHospitalFlag);
        } else if (HospitalTypeEnum.MATERNAL_CHILD_HOSPITAL.getHospitalTypeCode().equals(hospitalLevel)) {
            hospitalLevelFlag = one.equals(maternalChildHospitalFlag);
        } else if (HospitalTypeEnum.TUMOR_HOSPITAL.getHospitalTypeCode().equals(hospitalLevel)) {
            hospitalLevelFlag = one.equals(tumorHospitalFlag);
        } else if (HospitalTypeEnum.STOMATOLOGY_HOSPITAL.getHospitalTypeCode().equals(hospitalLevel)) {
            hospitalLevelFlag = one.equals(stomatologyHospitalFlag);
        } else if (HospitalTypeEnum.EYE_HOSPITAL.getHospitalTypeCode().equals(hospitalLevel)) {
            hospitalLevelFlag = one.equals(eyeHospitalFlag);
        } else {
            hospitalLevelFlag = false;
        }
        return hospitalLevelFlag;
    }

}
