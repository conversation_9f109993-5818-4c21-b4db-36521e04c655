package com.msun.csm.dao.entity.proj;

import java.io.Serializable;

import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/24
 */

/**
 * 项目实施产品记录表
 */
@ApiModel(description = "项目实施产品记录表")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjProductDeliverRecord extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 项目实施产品记录表ID
     */
    @ApiModelProperty(value = "项目实施产品记录表ID")
    private Long productDeliverRecordId;
    /**
     * 实施产品字典ID（记录运营平台产品字典ID）
     */
    @ApiModelProperty(value = "实施产品字典ID（记录运营平台产品字典ID）对照后id")
    private Long productDeliverId;
    /**
     * 实施地客户信息ID
     */
    @ApiModelProperty(value = "实施地客户信息ID")
    private Long customInfoId;
    /**
     * 运营平台交付工单产品ID
     */
    @ApiModelProperty(value = "运营平台交付工单产品ID-对照之前id")
    private Long yyOrderProductId;
    /**
     * 项目信息ID
     */
    @ApiModelProperty(value = "项目信息ID")
    private Long projectInfoId;
}
