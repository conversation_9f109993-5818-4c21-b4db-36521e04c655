package com.msun.csm.dao.entity.proj;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 自动化测试平台信息表
 *
 * @TableName proj_autocheck_info
 */
@TableName (value = "proj_autocheck_info", schema = "csm")
@Data
public class ProjAutocheckInfo implements Serializable {
    /**
     * 主键id
     */
    @TableId (type = IdType.INPUT)
    private Long projAutocheckId;

    /**
     * 医院id
     */
    private Long hospitalInfoId;

    /**
     * 医院id
     */
    private Long cloudHospitalId;

    /**
     * 概要报表链接
     */
    private String reportLink;

    /**
     * 详细报表链接
     */
    private String reportLinkDetail;

    /**
     * 当前步数
     */
    private Integer stepNum;

    /**
     * 创建人
     */
    private Long createrId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 逻辑删除【0：否；1：是】
     */
    private Short isDeleted;

    /**
     * 更新人id
     */
    private Long updaterId;

    /**
     * 项目id
     */
    private Long projectInfoId;


    /**
     * 自动化测试平台返回的任务id
     */
    private Long taskId;

    @ApiModelProperty ("云健康组织机构id")
    private Long orgId;

    @ApiModelProperty ("医院名称")
    private String hospitalName;

    @ApiModelProperty ("基础数据任务id")
    private Long baseTaskId;

    @ApiModelProperty ("历史报告链接【仅保存上次的概要报告】")
    private String historyReportLink;

    @ApiModelProperty ("业务任务是否可点击【0：否；1：是】")
    private Integer businessTaskFlag;
}
