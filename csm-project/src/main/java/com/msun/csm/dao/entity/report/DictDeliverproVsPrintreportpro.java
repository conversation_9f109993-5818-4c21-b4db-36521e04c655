package com.msun.csm.dao.entity.report;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 实施产品与打印平台产品对照
 *
 * @TableName dict_deliverpro_vs_printreportpro
 */
@TableName(value = "dict_deliverpro_vs_printreportpro")
@Data
@Api
public class DictDeliverproVsPrintreportpro extends BasePO {
    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private Long deliverproVsPrintreportproId;

    /**
     * 实施产品id
     */
    private Long deliverProductId;

    /**
     * 打印平台产品名称
     */
    @ApiModelProperty("打印平台产品名称")
    private String printProductName;

}