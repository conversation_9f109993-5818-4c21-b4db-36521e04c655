package com.msun.csm.dao.entity.proj.projreport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

/**
 * @TableName proj_report_examine_log
 */
@ApiModel(description = "业务审核记录")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm", value = "proj_business_examine_log")
public class ProjBusinessExamineLog extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 报表审核记录ID
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.INPUT)
    private Long businessExamineLogId;

    /**
     * 对应的业务主键如：报表、表单、产品调研
     */
    @ApiModelProperty(value = "对应的业务主键如：报表、表单、产品调研")
    private Long businessId;

    @ApiModelProperty(value = "业务类型 survey产品业务调研 /printreport打印报表 /form表单")
    private String businessType;

    /**
     * 审核状态
     * <p>0-产品业务调研、表单、打印报表提交后端审核，待后端审核</p>
     * <p>1-产品业务调研、表单、打印报表后端审核通过</p>
     * <p>2-产品业务调研、表单、打印报表后端审核驳回</p>
     * <p>11-产品业务调研撤销确认最终结果</p>
     * <p>12-产品业务调研撤销提交后端审核</p>
     * <p>21-打印报表前端人员验证通过</p>
     * <p>22-打印报表前端人员验证驳回</p>
     */
    @ApiModelProperty(value = "审核状态")
    private Integer examineStatus;
    @ApiModelProperty(value = "审核意见")
    private String examineOpinion;
}
