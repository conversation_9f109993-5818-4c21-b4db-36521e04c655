package com.msun.csm.dao.entity.proj;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import lombok.Data;


@Data
@TableName(schema = "csm", value = "proj_prerelease_msg")
public class ProjPrereleaseMsg extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId("prerelease_msg_id")
    private Long prereleaseMsgId;

    /**
     * 项目计划编码
     */
    private String planItemCode;

    /**
     * 医院ID
     */
    private Long hospitalInfoId;

    /**
     * 项目ID
     */
    private Long projectInfoId;

    /**
     * 产品ID
     */
    private Long yyProductId;

    /**
     * 消息类别ID
     */
    private Long msgClassId;

    /**
     * plan_item_code对应的工作项的前置工作项的编码
     */
    private String prePlanItemCode;

}
