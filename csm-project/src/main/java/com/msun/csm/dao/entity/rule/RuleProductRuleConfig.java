package com.msun.csm.dao.entity.rule;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/5/28
 */

/**
 * 产品规则配置表
 */
@ApiModel (description = "产品规则配置表")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RuleProductRuleConfig {
    /**
     * 产品规则配置表
     */
    @ApiModelProperty (value = "产品规则配置表")
    private Long productRuleConfigId;

    /**
     * 产品字典ID，使用运营平台产品字典ID
     */
    @ApiModelProperty (value = "产品字典ID，使用运营平台产品字典ID")
    private Long yyProductId;

    /**
     * 标识是否云his产品，用于标记首期项目
     */
    @ApiModelProperty (value = "标识是否云his产品，用于标记首期项目")
    private Integer hisFlag;

    /**
     * 云健康点数【0个点，1个点，3个点】
     */
    @ApiModelProperty (value = "云健康点数【0个点，1个点，3个点】")
    private Integer msunHealthPoint;

    /**
     * 云健康升级标识，用于标识是否老换新升级
     */
    @ApiModelProperty (value = "云健康升级标识，用于标识是否老换新升级")
    private Integer msunUpdateFlag;

    /**
     * 标识是否患者智能服务产品
     */
    @ApiModelProperty (value = "标识是否患者智能服务产品")
    private Integer hzznFlag;

    @ApiModelProperty ("是否需要调研【0：否；1：是】")
    private Integer surveyFlag;

    @ApiModelProperty ("是否为分院实施产品【0：否；1：是】")
    private Integer branchHospitalProductFlag;
}
