package com.msun.csm.dao.entity.dict;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "产品字典日志")
@Data
@TableName(schema = "csm")
public class DictProductLog extends BasePO {

    /**
     * 日志ID
     */
    @ApiModelProperty("日志ID")
    @TableId(type = IdType.INPUT)
    private Long dictProductLogId;

    /**
     * 操作模块
     */
    @ApiModelProperty("操作模块")
    private String operateModule;

    /**
     * 操作模块名称
     */
    @ApiModelProperty("操作模块名称")
    private String operateModuleName;

    /**
     * 操作类型：1新增、2修改、3删除
     */
    @ApiModelProperty("操作类型")
    private Integer operateType;

    /**
     * 操作类型名称
     */
    @ApiModelProperty("操作类型名称")
    private String operateTypeName;

    /**
     * 操作明细
     */
    @ApiModelProperty("操作明细")
    private String operateContent;

    /**
     * 操作人姓名
     */
    @ApiModelProperty("操作人姓名")
    private String createName;
}
