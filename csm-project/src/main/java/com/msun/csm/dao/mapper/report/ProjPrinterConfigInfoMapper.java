package com.msun.csm.dao.mapper.report;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.proj.projreport.ProjPrinterConfigInfo;
import com.msun.csm.model.csm.PrinterCsmDTO;
import com.msun.csm.model.req.projreport.ProjSurveyReportPrintReq;

/**
 * 打印机参数
 *
 * <AUTHOR>
 * @since 2024-09-27 15:27:17
 */
@Mapper
public interface ProjPrinterConfigInfoMapper extends BaseMapper<ProjPrinterConfigInfo> {

    /**
     * 查询非重复的打印机参数
     * @param paramer
     * @return
     */
    List<ProjPrinterConfigInfo> selectNotrepeatList(PrinterCsmDTO paramer);

    /**
     * 分页查询信息
     * @param dto
     * @return
     */
    List<ProjPrinterConfigInfo> queryPrintInfoByJf(ProjSurveyReportPrintReq dto);
}
