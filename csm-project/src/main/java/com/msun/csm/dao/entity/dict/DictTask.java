package com.msun.csm.dao.entity.dict;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/25
 */

/**
 * 待办任务字典表
 */
@ApiModel (description = "待办任务字典表")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DictTask implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty (value = "主键")
    private Long id;
    /**
     * 待办任务名称
     */
    @ApiModelProperty (value = "待办任务名称")
    private String taskName;
    /**
     * 待办任务处理路径，点击跳转的页面路径
     */
    @ApiModelProperty (value = "待办任务处理路径，点击跳转的页面路径")
    private String taskUrl;
    /**
     * 排序号
     */
    @ApiModelProperty (value = "排序号")
    private Integer orderNo;
    /**
     * 作废标识：0.否；1.是
     */
    @ApiModelProperty (value = "作废标识：0.否；1.是")
    private String invalidFlag;
    /**
     * 创建人id
     */
    @ApiModelProperty (value = "创建人id")
    private Long createrId;
    /**
     * 创建时间
     */
    @ApiModelProperty (value = "创建时间")
    private Date createTime;
    /**
     * 修改人id
     */
    @ApiModelProperty (value = "修改人id")
    private Long updaterId;
    /**
     * 修改时间
     */
    @ApiModelProperty (value = "修改时间")
    private Date updateTime;
}
