package com.msun.csm.dao.entity.proj.projreport;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @TableName config_custom_backend_limit
 */
@ApiModel(description = "项目整体开启小前端大后端限制")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ConfigCustomBackendLimit extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.INPUT)
    private Long customBackendLimitId;

    @ApiModelProperty("客户id")
    private Long customInfoId;
    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

    @ApiModelProperty(value = "是否开启 0 关/1开")
    private Integer openFlag;

    @TableField(exist = false)
    @ApiModelProperty("限制比例 默认 80%")
    private Integer limitRatio;
}
