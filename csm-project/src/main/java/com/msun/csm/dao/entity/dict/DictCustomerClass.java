package com.msun.csm.dao.entity.dict;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/25
 */

/**
 * 客户分类字典表
 */
@ApiModel (description = "客户分类字典表")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DictCustomerClass implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty (value = "主键")
    private Long id;
    /**
     * 客户分类名称
     */
    @ApiModelProperty (value = "客户分类名称")
    private String customerClassName;
    /**
     * 作废标识：0.否；1.是
     */
    @ApiModelProperty (value = "作废标识：0.否；1.是")
    private String invalidFlag;
    /**
     * 创建人id
     */
    @ApiModelProperty (value = "创建人id")
    private Integer createrId;
    /**
     * 创建时间
     */
    @ApiModelProperty (value = "创建时间")
    private Date createTime;
    /**
     * 修改人id
     */
    @ApiModelProperty (value = "修改人id")
    private Integer updaterId;
    /**
     * 修改时间
     */
    @ApiModelProperty (value = "修改时间")
    private Date updateTime;
}
