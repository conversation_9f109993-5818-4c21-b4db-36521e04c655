package com.msun.csm.dao.entity.config.extend;

import java.util.Date;

import com.msun.core.component.implementation.api.port.dto.CustFormCtrlDTO;
import com.msun.csm.dao.entity.config.ConfigCustomFormLimit;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjProductEmpowerRecord;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.util.SnowFlakeUtil;

import lombok.Data;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/8/5
 */
@Data
public class ConfigCustomFormLimitExtend extends ConfigCustomFormLimit {

    // 初始化构造函数
    public ConfigCustomFormLimitExtend(ProjProjectInfo projectInfo, ProjHospitalInfo hospital,
                                       ProjProductEmpowerRecord empowerRecord, CustFormCtrlDTO custFormCtrlDTO) {
        Date currentTime = new Date();
        this.setCustomFormLimitId(SnowFlakeUtil.getId());
        this.setCustomInfoId(hospital.getCustomInfoId());
        this.setHospitalInfoId(hospital.getHospitalInfoId());
        this.setOrderProductId(empowerRecord.getYyOrderProductId());
        this.setMsunHealthModuleCode(empowerRecord.getMsunHealthModuleCode());
        this.setSwitchFlag(custFormCtrlDTO.getSwitchFlag());
        this.setDaysAfterCheck(custFormCtrlDTO.getDaysAfterCheckClose());
        this.setIsDeleted(0);
        this.setCreaterId(projectInfo.getProjectLeaderId());
        this.setUpdaterId(projectInfo.getProjectLeaderId());
        this.setCreateTime(currentTime);
        this.setUpdateTime(currentTime);
        //定时任务是否处理标识
        this.setHasDeal(0);
        //验收时间
        this.setAcceptTime(projectInfo.getAcceptTime());
    }
}
