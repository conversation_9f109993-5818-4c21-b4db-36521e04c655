package com.msun.csm.dao.entity.dict;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;


/**
 * 扣分明细表
 */
@Data
@TableName(schema = "csm", value = "dict_hospital_type")
public class DictHospitalType implements Serializable {

    /**
     * 主键
     */
    @TableId
    private Long dictHospitalTypeId;

    /**
     * 删除标记，0-正常，1-已删除
     */
    private Integer isDeleted;

    /**
     * 创建人
     */
    private Long createrId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private Long updaterId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 医院类型编码
     */
    private String hospitalTypeCode;

    /**
     * 医院类型名称
     */
    private String hospitalTypeName;


}
