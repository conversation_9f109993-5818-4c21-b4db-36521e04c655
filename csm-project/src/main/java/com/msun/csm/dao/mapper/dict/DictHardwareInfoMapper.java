package com.msun.csm.dao.mapper.dict;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.dao.entity.dict.DictHardwareInfo;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/11/14
 */

@Mapper
public interface DictHardwareInfoMapper extends BaseMapper<DictHardwareInfo> {

    /**
     * 获取小硬件字典列表
     * @param hardwareName
     * @return
     */
    List<BaseIdNameResp> selectHardwareInfo(@Param("hardwareName") String hardwareName);

    /**
     * delete by primary key
     *
     * @param hardwareInfoId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long hardwareInfoId);

    int insertOrUpdate(DictHardwareInfo record);

    int insertOrUpdateSelective(DictHardwareInfo record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(DictHardwareInfo record);

    /**
     * select by primary key
     *
     * @param hardwareInfoId primary key
     * @return object by primary key
     */
    DictHardwareInfo selectByPrimaryKey(Long hardwareInfoId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(DictHardwareInfo record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(DictHardwareInfo record);

    int updateBatch(@Param("list") List<DictHardwareInfo> list);

    int updateBatchSelective(@Param("list") List<DictHardwareInfo> list);

    int batchInsert(@Param("list") List<DictHardwareInfo> list);
}
