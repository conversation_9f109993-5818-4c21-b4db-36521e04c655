package com.msun.csm.dao.entity.proj;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/12/12
 */

/**
 * 移动端安全改造后保存实际业务跳转页面url的消息记录表
 */
@ApiModel(description = "移动端安全改造后保存实际业务跳转页面url的消息记录表")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjMessageInfo {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 消息跳转实际页面路径
     */
    @ApiModelProperty(value = "消息跳转实际页面路径")
    private String businessUrl;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private Long createrId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人id
     */
    @ApiModelProperty(value = "修改人id")
    private Long updaterId;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 逻辑删除【0：否；1：是】
     */
    @ApiModelProperty(value = "逻辑删除【0：否；1：是】")
    private Integer isDeleted;
}
