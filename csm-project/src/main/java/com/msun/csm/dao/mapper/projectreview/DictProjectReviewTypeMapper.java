package com.msun.csm.dao.mapper.projectreview;


import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.projectreview.DictProjectReviewType;
import com.msun.csm.model.req.projectreview.DictProjectReviewTypeParam;
import com.msun.csm.model.resp.projectreview.DictProjectReviewTypeResp;

/**
 * <AUTHOR>
 * @description 针对表【dict_project_review_type(项目审核类型字典表)】的数据库操作Mapper
 * @createDate 2025-06-18 08:30:31
 * @Entity jiaofuceshi.domain.DictProjectReviewType
 */
public interface DictProjectReviewTypeMapper extends BaseMapper<DictProjectReviewType> {

    /**
     * 分页查询
     * @param dto
     * @return
     */
    List<DictProjectReviewTypeResp> findDataPage(DictProjectReviewTypeParam dto);

    /**
     * 查询未使用的数量
     * @param dto
     * @return
     */
    Integer selectIfNotUseCount(DictProjectReviewTypeParam dto);
}




