package com.msun.csm.dao.entity.proj;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.model.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-07-17 09:02:33
 */

@Data
@TableName(schema = "csm")
public class ProjProjectReviewRecord extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @TableId("project_review_record_id")
    private Long projectReviewRecordId;

    /**
     * 项目审核信息表id
     */
    @Schema(description = "项目审核信息表id")
    private Long projectReviewInfoId;

    /**
     * 项目信息表主键id
     */
    @Schema(description = "项目信息表主键id")
    private Long projectInfoId;

    /**
     * 项目阶段id
     */
    @Schema(description = "项目阶段id")
    private Long projectStageId;

    /**
     * 项目阶段编码
     */
    @Schema(description = "项目阶段编码")
    private String projectStageCode;

    /**
     * 类型编码
     */
    @Schema(description = "类型编码")
    private String classCode;

    /**
     * 类型名称
     */
    @Schema(description = "类型名称")
    private String className;

    /**
     * 项目编码
     */
    @Schema(description = "项目编码")
    private String itemCode;

    /**
     * 项目名称
     */
    @Schema(description = "项目名称")
    private String itemName;

    /**
     * 审核要点编码
     */
    @Schema(description = "审核要点编码")
    private String projectRuleCode;

    /**
     * 审核要点内容
     */
    @Schema(description = "审核要点内容")
    private String projectRuleContent;

    /**
     * 是否公开查看：0.否；1.是
     */
    @Schema(description = "是否公开查看：0.否；1.是")
    private Integer isPublic;

    /**
     * 是否有模版文件
     */
    @Schema(description = "是否有模版文件")
    private Integer templateFlag;

    /**
     * 模版文件编码
     */
    @Schema(description = "模版文件编码")
    private String templateFileCode;

    /**
     * 上传的凭证文件id
     */
    @Schema(description = "上传的凭证文件id")
    private Long projectFileId;

    /**
     * 排序号
     */
    @Schema(description = "排序号")
    private Integer orderNo;

    /**
     * 自检结果：0.不满足；1.已满足；2.不需要
     */
    @Schema(description = "自检结果：0.不满足；1.已满足；2.不需要")
    private Integer selfReviewResult;

    /**
     * 自检结果备注
     */
    @Schema(description = "自检结果备注")
    private String selfReviewMemo;

    /**
     * 审核结果：0.不通过；1.通过
     */
    @Schema(description = "审核结果：0.不通过；1.通过")
    private Integer reviewResult;

    /**
     * 审核说明
     */
    @Schema(description = "审核说明")
    private String reviewMemo;

    /**
     * 是否关联里程碑节点
     */
    @Schema(description = "是否关联里程碑节点")
    private Integer milestoneNodeFlag;

    /**
     * 关联的里程碑节点编码
     */
    @Schema(description = "关联的里程碑节点编码")
    private String milestoneNodeCode;

    /**
     * 是否包含子项标识:0.否；1.是
     */
    @Schema(description = "是否包含子项标识:0.否；1.是")
    private Integer containChildrenFlag;

    /**
     * 场景编码
     */
    @Schema(description = "场景编码")
    private String sceneCode;

    /**
     * 是否必填(0 非必填, 1 必填)
     */
    @Schema(description = "是否必填(0 非必填, 1 必填)")
    private Integer requiredFlag;

    /**
     * 验证方式：no 不验证；upload必须上传文件；prompt仅提示必须满足
     */
    @Schema(description = "验证方式：no 不验证；upload必须上传文件；prompt仅提示必须满足")
    private String verityWay;

    /**
     * 展示类型：0-提交审核pmo审核都展示 1-仅提交审核展示 2-仅pmo审核展示
     */
    @Schema(description = "展示类型：0-提交审核pmo审核都展示 1-仅提交审核展示 2-仅pmo审核展示")
    private Integer displayType;

}
