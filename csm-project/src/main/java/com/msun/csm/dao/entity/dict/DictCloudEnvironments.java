package com.msun.csm.dao.entity.dict;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-06-19 10:56:57
 */

@Data
@TableName(schema = "csm")
public class DictCloudEnvironments extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId("envir_id")
    private Long envirId;

    /**
     * 环境编码
     */
    @Schema(description = "环境编码")
    private String envirCode;

    /**
     * 环境名称
     */
    @Schema(description = "环境名称")
    private String envirName;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String memo;

    /**
     * 显示顺序
     */
    @Schema(description = "显示顺序")
    private Short sortOrder;

    /**
     * 云环境类型：1.众阳云；2.非众阳云
     */
    @Schema(description = "云环境类型：1.众阳云；2.非众阳云")
    private Short msunCloudFlag;

}
