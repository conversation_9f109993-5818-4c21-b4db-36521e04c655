package com.msun.csm.dao.entity.proj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* @description:
* @fileName: ProjFeedbackInterface.java
* @author: lius3
* @createAt: 2024/10/24 17:32
* @updateBy: lius3
* @remark: Copyright
*/
@Data
@TableName(schema = "csm")
public class ProjFeedbackInterface extends BasePO {

    /**
     * 数据id
     */
    @ApiModelProperty("数据id")
    @TableId(type = IdType.INPUT)
    private Long feedbackInterfaceId;

    /**
     * 三方接口id
     */
    @ApiModelProperty("三方接口id")
    private Long interfaceId;

    /**
     * 运维平台反馈单id
     */
    @ApiModelProperty("运维平台反馈单id")
    private Long feedbackId;

    /**
     * 完成标识
     */
    @ApiModelProperty("完成标识")
    private Integer completeFlag;
}
