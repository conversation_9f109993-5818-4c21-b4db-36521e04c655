package com.msun.csm.dao.entity.dict;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/10
 */

/**
 * 设备类型字典
 */
@ApiModel (description = "设备类型字典")
@Data
@TableName (schema = "csm")
public class DictEquipType extends BasePO {
    /**
     * 设备类型字典id
     */
    @ApiModelProperty (value = "设备类型字典id")
    @TableId (type = IdType.INPUT)
    private Long equipTypeId;

    /**
     * 设备类型名称
     */
    @ApiModelProperty (value = "设备类型名称")
    private String equipTypeName;

    /**
     * 设备分类字典id
     */
    @ApiModelProperty (value = "设备分类字典id")
    private Long equipClassId;

    @ApiModelProperty ("产品id")
    private Long yyProductId;
}
