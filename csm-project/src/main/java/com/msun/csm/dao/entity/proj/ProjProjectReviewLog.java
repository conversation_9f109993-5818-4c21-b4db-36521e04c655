package com.msun.csm.dao.entity.proj;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

/**
 * <AUTHOR>
 * @since 2024-07-17 05:05:56
 */

@Data
@TableName(schema = "csm")
public class ProjProjectReviewLog extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @TableId("project_review_log_id")
    private Long projectReviewLogId;

    /**
     * 操作节点
     */
    @Schema(description = "操作节点")
    private String operateNode;


    /**
     * 操作内容
     */
    @Schema(description = "操作内容")
    private String operateContent;

    /**
     * 操作标题
     */
    @Schema(description = "操作标题")
    private String operateTitle;

    /**
     * 操作人id
     */
    @Schema(description = "操作人id")
    private Long operateUserId;

    /**
     * 操作时间
     */
    @Schema(description = "操作时间")
    private Date operateTime;

    /**
     * 场景编码
     */
    @Schema(description = "场景编码")
    private String sceneCode;

    /**
     * 项目id
     */
    @Schema(description = "项目id")
    private Long projectInfoId;

    /**
     * 项目阶段id
     */
    @Schema(description = "项目阶段id")
    private Long projectStageId;

}
