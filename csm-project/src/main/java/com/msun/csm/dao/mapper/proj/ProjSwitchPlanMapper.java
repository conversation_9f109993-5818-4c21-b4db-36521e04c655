package com.msun.csm.dao.mapper.proj;

import java.util.List;

import javax.validation.constraints.NotNull;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.msun.csm.dao.entity.proj.ProjSwitchPlan;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/9/24
 */
@Mapper
public interface ProjSwitchPlanMapper {
    /**
     * delete by primary key
     *
     * @param switchPlanId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long switchPlanId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(ProjSwitchPlan record);

    int insertOrUpdate(ProjSwitchPlan record);

    int insertOrUpdateSelective(ProjSwitchPlan record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(ProjSwitchPlan record);

    /**
     * select by primary key
     *
     * @param switchPlanId primary key
     * @return object by primary key
     */
    ProjSwitchPlan selectByPrimaryKey(Long switchPlanId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(ProjSwitchPlan record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(ProjSwitchPlan record);

    int updateBatch(List<ProjSwitchPlan> list);

    int updateBatchSelective(List<ProjSwitchPlan> list);

    int batchInsert(@Param("list") List<ProjSwitchPlan> list);

    /**
     * 根据项目信息id查询切换计划
     *
     * @param id
     * @return
     */
    ProjSwitchPlan selectByProjectInfoId(@NotNull Long id);
}
