package com.msun.csm.dao.entity.proj;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-05-10 11:17:54
 */

@Data
@TableName(schema = "csm")
public class ResearchPlanForTask extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId("proj_research_plan_id")
    private Long milestoneTaskId;

    /**
     * 医院id
     */
    @Schema(description = "医院id")
    private Long hospitalInfoId;

    /**
     * 辅助负责人id
     */
    @Schema(description = "辅助负责人id")
    private String secondLeaderId;

    /**
     * 负责人id
     */
    @Schema(description = "负责人id")
    private Long leaderId;

    /**
     * 调研内容编码
     */
    @ApiModelProperty(value = "调研内容编码")
    private String milestoneNodeCode;

    /**
     * 调研计划开始时间
     */
    @Schema(description = "调研计划开始时间")
    private Date expectStartTime;

    /**
     * 调研计划结束时间
     */
    @Schema(description = "调研计划结束时间")
    private Date expectCompTime;

    /**
     * 项目id
     */
    @Schema(description = "项目id")
    private Long projectInfoId;

    /**
     * 客户id
     */
    @Schema(description = "客户id")
    private Long customerInfoId;

    /**
     * 结果来源id(医院)，0：现场调研
     */
    @Schema(description = "结果来源id(医院)，0：现场调研")
    private Long resultSourceId;
}
