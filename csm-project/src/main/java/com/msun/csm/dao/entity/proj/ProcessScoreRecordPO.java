package com.msun.csm.dao.entity.proj;

import java.math.BigDecimal;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessScoreRecordPO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 产品ID
     */
    private Long yyProductId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 项目阶段编码
     */
    private String projectStageCode;

    /**
     * 项目阶段名称
     */
    private String projectStageName;

    /**
     * 扣分类型字典表编码
     */
    private String deductionType;

    /**
     * 扣分类型字典名称
     */
    private String deductionTypeName;

    /**
     * 扣分明细
     */
    private String itemDetail;

    /**
     * 调研完成时间
     */
    private Date surveyCompleteTime;

    /**
     * 入驻时间
     */
    private Date settleInTime;

    /**
     * 准备完成时间
     */
    private Date preCompleteTime;

    /**
     * 上线时间
     */
    private Date onlineTime;

    /**
     * 验收时间
     */
    private Date acceptTime;

    /**
     * 扣分明细项的新增时间
     */
    private Date itemAddTime;

    /**
     * 附件ID集合，多个以英文逗号分割
     */
    private String attachmentId;

    /**
     * 预估扣分
     */
    private BigDecimal estimatedDeduction;

    /**
     * 实际扣分
     */
    private BigDecimal practicalDeduction;

    /**
     * 备注
     */
    private String remark;

    /**
     * 项目ID
     */
    private Long projectInfoId;

    /**
     * 考核指标/分类编码（优先存储二级编码）
     */
    private String classificationCode;

    /**
     * 来源：首次验收(first)/最终验收(final)
     */
    private String source;


}
