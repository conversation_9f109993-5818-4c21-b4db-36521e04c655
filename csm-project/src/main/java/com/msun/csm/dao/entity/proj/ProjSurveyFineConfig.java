package com.msun.csm.dao.entity.proj;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ProjSurveyFineConfig extends BasePO {

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @TableId(type = IdType.INPUT)
    private Long projSurveyFineConfigId;

    /**
     * 是否开启处罚监控：0否1是
     */
    @ApiModelProperty("是否开启处罚监控：0否1是")
    private Integer openFlag;

    /**
     * 开始管控时间
     */
    @ApiModelProperty("开始管控时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginControlTime;

    /**
     * 调研完成期限（即调研几天未完成进行处罚）
     */
    @ApiModelProperty("调研完成期限（即调研几天未完成进行处罚）")
    private Integer completeDay;

    /**
     * 预警天数（即首期项目需多少天提交调研，到期发送预警单）
     */
    @ApiModelProperty("预警天数（即首期项目需多少天提交调研，到期发送预警单）")
    private Integer warningDay;

    /**
     * 罚款金额
     */
    @ApiModelProperty("罚款金额")
    private String fineMoney;

    /**
     * 是否重复处罚：0否1是
     */
    @ApiModelProperty("是否重复处罚：0否1是")
    private Integer repetFineFlag;

    /**
     * 重复处罚周期天数
     */
    @ApiModelProperty("重复处罚周期天数")
    private Integer repetFineCycle;

    /**
     * 预警单消息内容模板
     */
    @ApiModelProperty
    private String warningTemplate;

    /**
     * 处罚单消息内容模板
     */
    @ApiModelProperty("预警模版")
    private String fineTemplate;

    /**
     * 项目经理消息提醒模版
     */
    @ApiModelProperty("项目经理消息提醒模版")
    private String projManagerTemplate;

    /**
     * 重复处罚预警单消息内容模板
     */
    private String repeatWarningTemplate;
}
