package com.msun.csm.dao.mapper.dict;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.dict.DictSatisfactionSurveyLevel;

/**
 * @Entity
 */
@Mapper
public interface DictSatisfactionSurveyLevelMapper extends BaseMapper<DictSatisfactionSurveyLevel> {

    List<DictSatisfactionSurveyLevel> getAllEvaluationLevel();


    DictSatisfactionSurveyLevel getSatisfactionSurveyLevelByCode(@Param("levelCode") String levelCode);

}




