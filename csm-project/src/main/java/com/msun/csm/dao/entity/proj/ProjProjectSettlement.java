package com.msun.csm.dao.entity.proj;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.enums.projsettlement.SettlementStatusEnum;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-06-17 05:11:00
 */

@Data
@TableName(schema = "csm")
public class ProjProjectSettlement extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 入驻信息id，主键
     */
    @Schema(description = "入驻信息id，主键")
    @TableId("project_settlement_id")
    private Long projectSettlementId;

    /**
     * 运营平台工单id
     */
    @Schema(description = "运营平台工单id")
    private Long yyOrderId;

    /**
     * 项目id
     */
    @Schema(description = "项目id")
    private Long projectInfoId;

    /**
     * 入驻状态：0.提交入驻条件；1.申请入驻；11.方案分公司经理复核通过；12.方案分公司经理驳回；21.运营部审核通过；22.风控驳回；31.PMO审核通过；32.PMO驳回；41.确认入驻
     */
    @Schema(description = "入驻状态：0.提交入驻条件；1.申请入驻；11.方案分公司经理复核通过；12.方案分公司经理驳回；21.运营部审核通过；22.风控驳回；31.PMO审核通过；32.PMO驳回；41.确认入驻")
    private Integer settlementStatus;

    /**
     * 是否需要售前审核：0.否；1.是；售前审核
     */
    @Schema(description = "是否需要售前审核：0.否；1.是；售前审核")
    private Integer checkPresaleFlag;

    /**
     * 是否需要审核预付款：0.否；1.是；运营部审核
     */
    @Schema(description = "是否需要审核预付款：0.否；1.是；运营部审核")
    private Integer checkPrepayFlag;

    /**
     * 是否需要审核云资源：0.否；1.是
     */
    @Schema(description = "是否需要审核云资源：0.否；1.是")
    private Integer checkCloudResourceFlag;

    /**
     * 客服提交人id
     */
    @Schema(description = "客服提交人id")
    private Long projectUserId;

    /**
     * 客服提交时间
     */
    @Schema(description = "客服提交时间")
    private Date projectUserTime;

    /**
     * 销售代理商申请人id
     */
    @Schema(description = "销售代理商申请人id")
    private Long saleApplyUserId;

    /**
     * 销售代理商申请入驻时间
     */
    @Schema(description = "销售代理商申请入驻时间")
    private Date saleApplyTime;

    /**
     * 逻辑删除标识：0.否；1.是
     */
    @Schema(description = "逻辑删除标识：0.否；1.是")
    private Integer isDeleted;

    /**
     * 操作人id
     */
    @Schema(description = "操作人id")
    private Long createrId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 修改人id
     */
    @Schema(description = "修改人id")
    private Long updaterId;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private Date updateTime;

    public void render(boolean hasDeployedCloud, boolean isFirstDeploy) {
        this.setCheckCloudResourceFlag(hasDeployedCloud ? 0 : 1);
        this.setSettlementStatus(SettlementStatusEnum.COMMIT_SETTLEMENT.getCode());
//        this.setCheckPrepayFlag(isFirstDeploy ? CheckPrepayFlagEnum.NEED_CHECK_PREPAY.getCode() : CheckPrepayFlagEnum.NEED_NO_CHECK_PREPAY.getCode());
//        this.setCheckPresaleFlag(isFirstDeploy ? CheckPresaleFlagEnum.NEED_CHECK_PRE_SALE.getCode() : CheckPresaleFlagEnum.NEED_NO_CHECK_PRE_SALE.getCode());
    }
}
