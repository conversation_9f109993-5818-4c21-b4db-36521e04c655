package com.msun.csm.dao.mapper.proj;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.dao.entity.proj.ProjProductConfigLog;
import com.msun.csm.model.vo.ProjProductConfigLogVO;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/07/29/15:47
 */
@Mapper
public interface ProjProductConfigLogMapper extends BaseMapper<ProjProductConfigLog> {

    /**
     * 查询日志列表
     *
     * @param log
     * @return
     */
    List<ProjProductConfigLogVO> selectLogList(ProjProductConfigLog log);

    /**
     * 批量保存日志
     *
     * @param configLogList
     */
    void insertBatch(List<ProjProductConfigLog> configLogList);

    /**
     * 删除日志
     *
     * @param projectInfoId
     * @param hospitalInfoId
     * @param yyProductIdList
     */
    void deleteByParam(@Param("projectInfoId") Long projectInfoId, @Param("hospitalInfoId") Long hospitalInfoId,
                       @Param("yyProductIdList") List<BaseIdNameResp> yyProductIdList);

    /**
     * 项目拆分合并处理数据
     *
     * @param oldProjectId
     * @param newProjectId
     * @param changeProductList
     */
    int updateByProjectId(@Param("oldProjectId") Long oldProjectId, @Param("newProjectId") Long newProjectId,
                          @Param("changeProductList") List<Long> changeProductList);
}
