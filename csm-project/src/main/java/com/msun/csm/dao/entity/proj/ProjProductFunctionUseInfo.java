package com.msun.csm.dao.entity.proj;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;


/**
 * 扣分明细表
 */
@Data
@TableName(schema = "csm", value = "proj_product_function_use_info")
public class ProjProductFunctionUseInfo implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long projProductFunctionUseInfoId;

    /**
     * 删除标记，0-正常，1-已删除
     */
    private Integer isDeleted;

    /**
     * 创建人
     */
    private Long createrId;

    /**
     * 更新人
     */
    private Long updaterId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 项目ID
     */
    private Long projectInfoId;

    /**
     * 运营平台实施产品ID
     */
    private Long yyProductId;

    /**
     * 功能编码
     */
    private String functionCode;

    /**
     * 功能点使用次数
     */
    private Integer useCount;

}
