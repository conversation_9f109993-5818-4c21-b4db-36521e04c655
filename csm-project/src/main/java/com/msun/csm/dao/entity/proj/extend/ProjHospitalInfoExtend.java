package com.msun.csm.dao.entity.proj.extend;

import static com.msun.csm.common.staticvariable.StaticPara.DEFAULT_LONG;

import java.util.Date;

import com.msun.csm.common.enums.HospitalOpenStatusEnum;
import com.msun.csm.dao.entity.oldimsp.OldCustomerInfo;
import com.msun.csm.dao.entity.proj.ProjCustomDetailInfo;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.service.proj.applyorder.ProjApplyOrderDescServiceImpl;
import com.msun.csm.util.SnowFlakeUtil;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/28
 */
@Data
@AllArgsConstructor
public class ProjHospitalInfoExtend extends ProjHospitalInfo {

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customName;

    /**
     * 构造方法
     *
     * @param hospitalInfoId
     * @param customInfoId
     * @param finalCustomInfo
     * @param finalCustomDetailInfo
     * @param leaderId
     * @param now
     */
    public ProjHospitalInfoExtend(Long hospitalInfoId, Long customInfoId, ProjCustomInfo finalCustomInfo,
                                  ProjCustomDetailInfo finalCustomDetailInfo, Long leaderId, Date now) {
        this.setHospitalInfoId(hospitalInfoId);
        this.setCustomInfoId(customInfoId);
        this.setHospitalName(finalCustomInfo.getCustomName());
        this.setHospitalBedCount(finalCustomDetailInfo.getCustomBedCount());
        this.setHospitalAnnualIncome(ObjectUtil.isEmpty(finalCustomDetailInfo.getCustomAnnualIncome()) ? 0L
                : ProjApplyOrderDescServiceImpl.convertToTenThousand(finalCustomDetailInfo.getCustomAnnualIncome().longValue()));
        this.setHospitalOutPatientCount(finalCustomDetailInfo.getCustomOutPatientCount());
        this.setHospitalOpenStatus(HospitalOpenStatusEnum.NOT_OPEN.getCode());
        this.setProvinceId(finalCustomDetailInfo.getProvinceId());
        this.setCityId(finalCustomDetailInfo.getCityId());
        this.setTownId(finalCustomDetailInfo.getTownId());
        this.setDictHospitalOrgId(-1L);
        //推送过来的一定是主院、不是分院
        this.setHealthBureauFlag(1);
        //todo 缺少医院机构类型、医院类型、区域片区、终端数量、村医数量、村卫生室数量、人口数
        this.setCreaterId(leaderId);
        this.setUpdaterId(leaderId);
        this.setCreateTime(now);
        this.setUpdateTime(now);
        this.setIsDeleted(0);
    }

    /**
     * 数据迁移老项目医院转新项目医院
     *
     * @param hospital
     * @param newCustomInfoId
     * @param now
     */
    public ProjHospitalInfoExtend(OldCustomerInfo hospital, Long newCustomInfoId, Date now) {
        if (hospital.getCustomerName().endsWith("卫生健康局") || hospital.getCustomerName().endsWith("卫生健康委员会")
                || hospital.getCustomerName().endsWith("卫生健康委") || hospital.getCustomerName().endsWith("医疗集团")
                || hospital.getCustomerName().endsWith("卫生局") || hospital.getCustomerName().endsWith("健康医疗")) {
            //主院
            this.setHospitalInfoId(hospital.getId());
            this.setHealthBureauFlag(1);
        } else {
            this.setHospitalInfoId(SnowFlakeUtil.getId());
            this.setHealthBureauFlag(0);
        }
        this.setOrgId(hospital.getOrgId());
        this.setCloudHospitalId(hospital.getHospitalId());
        this.setCloudDomain(hospital.getProductNetwork());
        this.setEnvId(hospital.getEnvId());
        this.setEnvName(hospital.getEnvName());
        //老系统只有0和1，新系统只有0、11、21
        //0：未开通，1：开通
        //0：未开通，11：处理中，21：已开通
        this.setHospitalOpenStatus("0".equals(hospital.getEnvStatus()) ? 0 : 21);
        this.setCustomInfoId(newCustomInfoId);
        this.setHospitalName(hospital.getCustomerName());
        this.setHospitalBedCount(hospital.getBedCount());
        this.setHospitalAnnualIncome(hospital.getAnnualIncome() != null ? hospital.getAnnualIncome().longValue() : 0L);
        this.setHospitalOutPatientCount(hospital.getOutpatientCount() != null ? hospital.getOutpatientCount() : 0);
        this.setProvinceId(hospital.getProvinceId() != null ? hospital.getProvinceId().longValue() : DEFAULT_LONG);
        this.setCityId(hospital.getCityId() != null ? hospital.getCityId().longValue() : DEFAULT_LONG);
        this.setTownId(hospital.getTownId() != null ? hospital.getTownId().longValue() : DEFAULT_LONG);
        this.setDictHospitalOrgId(DEFAULT_LONG);
        //增加字段
        this.setDictHospitalLevelId(DEFAULT_LONG);
        this.setCreaterId(DEFAULT_LONG);
        this.setUpdaterId(DEFAULT_LONG);
        this.setCreateTime(now);
        //迁移的数据更新时间统一设置为时分秒为00:00:00
        String time = DateUtil.formatDate(now);
        this.setUpdateTime(DateUtil.parse(time + " 00:00:00"));
        this.setIsDeleted(0);
    }
}
