package com.msun.csm.dao.entity.proj;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 部署申请查询实体
 */
@ApiModel(description = "部署申请查询实体")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjApplyOrderProduct {
    private static final long serialVersionUID = 1L;

    /**
     * 授权菜单名称
     */
    @ApiModelProperty(value = "授权菜单名称")
    private String productName;

    /**
     * 运营平台产品编码
     */
    @ApiModelProperty(value = "运营平台产品编码")
    private String yyProductCode;

    /**
     * 运营平台交付工单产品id
     */
    @ApiModelProperty(value = "运营平台交付工单产品id")
    private Long yyOrderProductId;
    /**
     * 运营平台部署产品id
     */
    @ApiModelProperty(value = "运营平台部署产品id")
    private Long productArrangeId;


}
