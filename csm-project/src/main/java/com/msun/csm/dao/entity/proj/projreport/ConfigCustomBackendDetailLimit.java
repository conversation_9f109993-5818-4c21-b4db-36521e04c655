package com.msun.csm.dao.entity.proj.projreport;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @TableName config_custom_backend_detail_limit
 */
@ApiModel(description = "项目整体开启小前端大后端限制明细")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ConfigCustomBackendDetailLimit extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.INPUT)
    private Long customDetailLimitId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;
    @ApiModelProperty(value = "限制类型 1开启医护打印验证流程、2打印报表、3云护理表单、4手麻表单、5重症表单、6急诊表单、7三方接口、8医保接口/9统计报表/10 产品业务调研  11 打印报表 12 表单...")
    private Integer openType;
    @ApiModelProperty(value = "是否开启 0 关/1开")
    private Integer openFlag;

    @ApiModelProperty("限制比例 默认 80%")
    private Integer limitRatio;
}