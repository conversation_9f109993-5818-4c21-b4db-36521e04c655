package com.msun.csm.dao.entity.proj;

import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-05-14 09:10:11
 */

@Data
@TableName (schema = "csm")
public class ProjNetworkDetDomainRecord extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId ("id")
    private Long id;

    /**
     * 回传id, 也是平台定义id, 使用医院主键
     */
    @Schema (description = "回传id, 也是平台定义id, 使用医院主键")
    private Long logId;

    /**
     * 测试使用的域名
     */
    @Schema (description = "测试使用的域名")
    private String webUrl;

    /**
     * 网络协议
     */
    @ApiModelProperty(value = "网络协议")
    private String protocol;

    /**
     * 测试域名所解析出来的IP地址
     */
    @Schema (description = "测试域名所解析出来的IP地址")
    private String ipAddress;

    /**
     * 测试的端口
     */
    @Schema (description = "测试的端口")
    private Integer port;

    /**
     * 本机IP地址
     */
    @Schema (description = "本机IP地址")
    private String localIpAddress;

    /**
     * 测试数量
     */
    @Schema (description = "测试数量")
    private Long count;

    /**
     * 测试失败数量
     */
    @Schema (description = "测试失败数量")
    private Long failed;

    /**
     * 成功率
     */
    @Schema (description = "成功率")
    private String successRate;

    /**
     * 最小延时
     */
    @Schema (description = "最小延时")
    private String minimum;

    /**
     * 最大延时
     */
    @Schema (description = "最大延时")
    private String maximum;

    /**
     * 平均延时
     */
    @Schema (description = "平均延时")
    private String average;

    /**
     * 总解析个数,当前测试为第几个
     */
    @Schema (description = "总解析个数,当前测试为第几个")
    private String note;

    /**
     * 用于工具提交给交付平台结果标识，比如测试失败、解析失败等
     */
    @Schema (description = "用于工具提交给交付平台结果标识，比如测试失败、解析失败等")
    private String testResult;

    /**
     * 用于工具提交给交付平台结果标识，比如测试失败、解析失败等
     */
    @Schema (description = "测试成功或失败状态 0失败 1成功")
    private int detectStatus;

    /**
     * 域名解析的ip数量
     */
    @Schema (description = "域名解析的ip数量")
    private int ipCount;

    /**
     * 随机码
     */
    @Schema (description = "随机码 ( 区分每一台电脑不同轮次的测试 )")
    private Long randomCode;


    @Schema (description = "测试时间")
    @JsonFormat (pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    private Date detectDateTime;
}
