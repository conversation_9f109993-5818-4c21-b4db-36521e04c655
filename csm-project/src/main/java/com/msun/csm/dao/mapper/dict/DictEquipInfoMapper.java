package com.msun.csm.dao.mapper.dict;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.dict.DictEquipInfo;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/10
 */
@Mapper
public interface DictEquipInfoMapper extends BaseMapper<DictEquipInfo> {
    /**
     * delete by primary key
     *
     * @param equipInfoId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long equipInfoId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(DictEquipInfo record);

    int insertOrUpdate(DictEquipInfo record);

    int insertOrUpdateSelective(DictEquipInfo record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(DictEquipInfo record);

    /**
     * select by primary key
     *
     * @param equipInfoId primary key
     * @return object by primary key
     */
    DictEquipInfo selectByPrimaryKey(Long equipInfoId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(DictEquipInfo record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(DictEquipInfo record);

    int updateBatch(List<DictEquipInfo> list);

    int updateBatchSelective(List<DictEquipInfo> list);

    int batchInsert(@Param("list") List<DictEquipInfo> list);
}
