package com.msun.csm.dao.entity.report.statis;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 医院科室字典表(CONFIG_HOSPITAL_DEPT)
 *
 * <AUTHOR>
 * @version 1.0.0 2025-01-13
 */
@Data
@TableName(value = "config_hospital_dept", schema = "csm")
@ApiModel
public class ConfigHospitalDeptEntity extends BasePO {
    /**
     * 版本号
     */
    private static final long serialVersionUID = 8789076942895049510L;

    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    @ApiModelProperty(value = "主键")
    private Long hospitalDeptId;

    /**
     * 科室名称
     */
    private String hospitalDeptName;

    /**
     * 备注信息
     */
    private String memo;

    /**
     * 排序
     */
    private Integer orderNo;

    /**
     * 科室分类-预留字段
     */
    private Integer hospitalDeptClass;

    /**
     * 项目id
     */
    private Long projectInfoId;
}