package com.msun.csm.dao.mapper.report;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.report.statis.ProjReportOperationRecordEntity;
import com.msun.csm.model.req.projreport.statis.ProjReportOperationRecordSelectReq;

/**
 * 统计报表操作日志记录
 *
 * <AUTHOR>
 * @since 2024-09-27 15:27:17
 */
@Mapper
public interface ProjReportOperationRecordMapper extends BaseMapper<ProjReportOperationRecordEntity> {

    /**
     * 根据主表id查询
     *
     * @param statisticalReportMainId
     * @return
     */
    List<ProjReportOperationRecordSelectReq> selectListById(@Param("statisticalReportMainId") Long statisticalReportMainId, @Param("operStatus") Integer operStatus);
}
