package com.msun.csm.dao.entity.dict;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/9/12
 */

@ApiModel(description = "csm.dict_interface")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName (schema = "csm")
public class DictInterface {
    /**
     * 主键id
     */
    @ApiModelProperty (value = "主键id")
    @TableId (type = IdType.INPUT)
    private Long dictInterfaceId;

    /**
     * 接口名称
     */
    @ApiModelProperty(value = "接口名称")
    private String dictInterfaceName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
