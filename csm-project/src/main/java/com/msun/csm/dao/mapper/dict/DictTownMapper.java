package com.msun.csm.dao.mapper.dict;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.dict.DictTown;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/25
 */
@Mapper
public interface DictTownMapper extends BaseMapper<DictTown> {
    int deleteByPrimaryKey(Long dictTownId);

    int insert(DictTown record);

    int insertOrUpdate(DictTown record);

    int insertOrUpdateSelective(DictTown record);

    int insertSelective(DictTown record);
//    DictTown selectByPrimaryKey(Long dictTownId);

    int updateByPrimaryKeySelective(DictTown record);

    int updateByPrimaryKey(DictTown record);

    int updateBatch(List<DictTown> list);

    int updateBatchSelective(List<DictTown> list);

    int batchInsert(@Param("list") List<DictTown> list);
}
