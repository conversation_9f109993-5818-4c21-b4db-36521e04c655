package com.msun.csm.dao.entity.proj;

import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableId;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2025/1/13
 */

/**
 * 我的待办业务数据表
 */
@ApiModel(description = "我的待办业务数据表")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjTodoTask {
    /**
     * 主键
     */
    @TableId
    @ApiModelProperty(value = "主键")
    private Long todoTaskId;

    /**
     * 工作项业务主键-相当于父节点
     */
    @ApiModelProperty(value = "工作项业务主键-相当于父节点")
    private Long projectPlanId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private Long yyProductId;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 计划完成时间
     */
    @ApiModelProperty(value = "计划完成时间")
    @JSONField(format = "yyyy-MM-dd")
    private Date planTime;

    /**
     * 状态：0 未完成/1 已完成 2 进行中
     */
    @ApiModelProperty(value = "状态：0 未完成,1 已完成,2 进行中")
    private Integer status;

    /**
     * 实施工程师
     */
    @ApiModelProperty(value = "实施工程师")
    private Long implementationEngineerId;

    /**
     * 后端工程师
     */
    @ApiModelProperty(value = "后端工程师")
    private Long backendEngineerId;

    /**
     * 完成数量 ，包含三种情况
     * 1产品维度来展示
     * 2任务总数展示-共xxx
     * 3接口等按照实际调研完成标准
     */
    @ApiModelProperty(value = "完成数量 ，包含三种情况,1产品维度来展示,2任务总数展示-共xxx,3接口等按照实际调研完成标准")
    private Integer completeCount;

    /**
     * 待办总数量
     */
    @ApiModelProperty(value = "待办总数量")
    private Integer totalCount;

    /**
     * 跳转类型：页面/通用-自定义详情或预置数据详情；0-没有页面不需要跳转；1-跳转里程碑页面；2-有产品时跳转各产品的明细页面
     */
    @ApiModelProperty(value = "跳转类型：页面/通用-自定义详情或预置数据详情")
    private Integer jumpType;

    /**
     * 跳转地址
     */
    @ApiModelProperty(value = "跳转地址")
    private String jumpPath;

    /**
     * 重点关注标识 0 未重点关注 1 重点关注
     */
    @ApiModelProperty(value = "重点关注标识 0 未重点关注 1 重点关注")
    private Integer attentionFlag;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private Long createrId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人id
     */
    @ApiModelProperty(value = "修改人id")
    private Long updaterId;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 逻辑删除【0：否；1：是】
     */
    @ApiModelProperty(value = "逻辑删除【0：否；1：是】")
    private Integer isDeleted;

    /**
     * 是否需要配置前端负责人
     */
    @ApiModelProperty(value = "是否需要配置前端负责人")
    private Integer frontFlag;

    /**
     * 是否需要配置后端负责人
     */
    @ApiModelProperty(value = "是否需要配置后端负责人")
    private Integer backendFlag;

    /**
     * 医院信息id
     */
    @ApiModelProperty(value = "医院信息id")
    private Long hospitalInfoId;

    @ApiModelProperty(value = "完成人id")
    private Long completionUserId;
}
