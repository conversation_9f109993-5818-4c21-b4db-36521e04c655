package com.msun.csm.dao.entity.dict;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/10
 */

/**
 * 设备厂商字典
 */
@ApiModel (description = "设备厂商字典")
@Data
@TableName (schema = "csm")
public class DictEquipFactory extends BasePO {
    /**
     * 设备厂商字典主键id
     */
    @ApiModelProperty (value = "设备厂商字典主键id")
    @TableId (type = IdType.INPUT)
    private Long equipFactoryId;

    /**
     * 设备厂商名称
     */
    @ApiModelProperty (value = "设备厂商名称")
    private String equipFactoryName;

    /**
     * 设备分类id
     */
    @ApiModelProperty (value = "设备分类id")
    private Long equipClassId;

    /**
     * 厂商所属运营平台产品id
     */
    @ApiModelProperty (value = "厂商所属运营平台产品id")
    private Long yyProductId;

}
