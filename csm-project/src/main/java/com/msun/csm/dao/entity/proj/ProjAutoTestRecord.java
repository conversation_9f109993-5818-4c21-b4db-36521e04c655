package com.msun.csm.dao.entity.proj;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025-02-18 03:07:31
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjAutoTestRecord extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId("proj_auto_test_record_id")
    private Long projAutoTestRecordId;

    /**
     * 客户id
     */
    @Schema(description = "客户id")
    private Long customInfoId;

    /**
     * 项目类型
     */
    @ApiModelProperty(value = "项目类型")
    private Integer projectType;

    /**
     * 云健康医院id
     */
    @Schema(description = "云健康医院id")
    private Long cloudHospitalId;

    /**
     * 检测状态. 0未检测, 1.检测中 2.检测执行失败 3. 检测不通过 4.人工判定通过  5. 检测通过
     */
    @Schema(description = "检测状态. 0未检测, 1.检测中 2.检测执行失败 3. 检测不通过 4.人工判定通过  5. 检测通过")
    private Integer status;

    /**
     * 检测报告查询路径
     */
    @Schema(description = "检测报告查询路径")
    private String testReportUrl;

    /**
     * 检测报告详情查询路径
     */
    @Schema(description = "检测报告详情查询路径")
    private String testReportDetailUrl;

    /**
     * 报告时间
     */
    @Schema(description = "报告时间")
    private Date reportTime;

    /**
     * 执行人用户id
     */
    @Schema(description = "执行人用户id")
    private Long executorUserId;

    /**
     * 执行人用户名
     */
    @Schema(description = "执行人用户名")
    private String executorUserName;

    /**
     * 备注（如人工判定通过, 注明原因）
     */
    @Schema(description = "备注（如人工判定通过, 注明原因）")
    private String remark;

    /**
     * 0.基础数据测试, 1.业务数据测试
     */
    @Schema(description = "0.基础数据测试, 1.业务数据测试")
    private Integer sceneType;

    /**
     * 逻辑删除【0：否；1：是】
     */
    @Schema(description = "逻辑删除【0：否；1：是】")
    private Integer isDeleted;

    /**
     * 创建人id
     */
    @Schema(description = "创建人id")
    private Long createrId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新人id
     */
    @Schema(description = "更新人id")
    private Long updaterId;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 测试执行失败原因
     */
    @Schema(description = "测试执行失败原因")
    private String testFailReason;

    /**
     * 测试日志id
     */
    @Schema(description = "测试日志id")
    private Long testLogId;

    /**
     * 项目id
     */
    @Schema(description = "项目id")
    private Long projectInfoId;
}
