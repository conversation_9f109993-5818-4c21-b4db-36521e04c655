package com.msun.csm.dao.entity.proj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/07/29/10:51
 */
@Data
@TableName(schema = "csm")
public class ProjProductConfig extends BasePO {

    @ApiModelProperty("主键id")
    @TableId(type = IdType.INPUT)
    private Long productConfigId;

    @ApiModelProperty("项目id")
    private Long projectInfoId;

    @ApiModelProperty("医院id")
    private Long hospitalInfoId;

    @ApiModelProperty("运营平台产品id")
    private Long yyProductId;

    @ApiModelProperty("配置名称")
    private String configName;

    @ApiModelProperty("配置编码")
    private String configCode;

    @ApiModelProperty("配置值")
    private String configValue;

    @ApiModelProperty("配置类型 系统管理：sys ；其他为产品code")
    private String configType;

    @ApiModelProperty("1已导入，0未导入")
    private Integer configStatus;

    /**
     * 调研题目
     */
    private String surveyQuestion;

    /**
     * 调研结果
     */
    private String surveyAnswer;

}
