package com.msun.csm.dao.mapper.proj;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.proj.ProjHospitalOnlineDetail;

/**
 * 医院上线明细表(ProjHospitalOnlineDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-23 15:57:31
 */
@Mapper
public interface ProjHospitalOnlineDetailMapper extends BaseMapper<ProjHospitalOnlineDetail> {

    /**
     * 批量更新
     * @param list
     */
    void updateBatch(List<ProjHospitalOnlineDetail> list);

    /**
     * 说明: 根据客户id修改云资源台账
     *
     * @param oldCustomInfoId
     * @param newCustomInfoId
     * @param projectInfoIds
     * @return
     */
    int updateByCustomInfoId(@Param("oldCustomInfoId") Long oldCustomInfoId,
                             @Param("newCustomInfoId") Long newCustomInfoId,
                             @Param("projectInfoIds") List<Long> projectInfoIds);
}
