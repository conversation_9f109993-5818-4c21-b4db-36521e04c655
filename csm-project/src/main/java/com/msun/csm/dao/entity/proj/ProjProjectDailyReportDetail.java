package com.msun.csm.dao.entity.proj;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(schema = "csm", value = "proj_project_daily_report_detail")
public class ProjProjectDailyReportDetail extends BasePO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long projectDailyReportDetailId;

    /**
     * 项目日报记录表主键
     */
    private Long projectDailyReportRecordId;

    /**
     * 里程碑节点/项目计划节点编码
     */
    private String nodeCode;

    /**
     * 用户填写的明细内容
     */
    private String reportDetailContent;

}
