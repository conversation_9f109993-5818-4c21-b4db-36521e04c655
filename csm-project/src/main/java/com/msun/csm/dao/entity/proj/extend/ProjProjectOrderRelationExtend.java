package com.msun.csm.dao.entity.proj.extend;

import java.util.Date;

import com.msun.csm.dao.entity.proj.ProjOrderInfo;
import com.msun.csm.dao.entity.proj.ProjProjectOrderRelation;
import com.msun.csm.util.SnowFlakeUtil;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-06-19 05:38:43
 */

@Data
@AllArgsConstructor
public class ProjProjectOrderRelationExtend extends ProjProjectOrderRelation {

    /**
     * 构造函数
     *
     * @param projOrderInfo
     */
    public ProjProjectOrderRelationExtend(ProjOrderInfo projOrderInfo, Long projectInfoId, Long customerInfoId,
                                          Date createTime, Long userId) {
        this.setProjectVsOrderId(SnowFlakeUtil.getId());
        this.setProjectInfoId(projectInfoId);
        this.setYyOrderId(projOrderInfo.getYyOrderId());
        //特殊记录合同id
        this.setBussinessInfoId(projOrderInfo.getContractInfoId());
        this.setContractCustomInfoId(customerInfoId);
        this.setCustomInfoId(customerInfoId);
        this.setDeliveryOrderId(projOrderInfo.getDeliveryOrderType());
        this.setIsDeleted(0);
        this.setCreateTime(createTime);
        this.setUpdateTime(createTime);
        this.setCreaterId(userId);
        this.setUpdaterId(userId);
    }


}
