package com.msun.csm.dao.entity.oas;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 自定义表单信息表(CustomForm)实体类
 *
 * <AUTHOR>
 * @since 2021-04-22 14:14:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "form_pattern", schema = "form_library")
public class FormPattern extends BasePO {

    /**
     * 主键id
     */
    @TableId
    private Long formPatternId;

    /**
     * 表单名称
     */
    private String formPatternName;

    /**
     * 描述说明
     */
    private String description;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 电脑表单样式内容
     */
    private String formContentPc;

    /**
     * 平板表单样式内容
     */
    private String formContentPad;

    /**
     * 平板端表单样式格式
     */
    private String formConfigurationPad;

    /**
     * 表单页面配置
     */
    private String formConfigurationPc;

    /**
     * 流程节点code
     */
    private String formCategoryCode;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 作废标识
     */
    @TableLogic
    private String invalidFlag;

    /**
     * 运营平台产品Id 手麻4050
     */
    private Long yyProductId;

    /**
     * 平板表单样式内容
     */
    private String formContentWeb;

    /**
     * 平板端表单样式格式
     */
    private String formConfigurationWeb;

    private String enableFlag;
}
