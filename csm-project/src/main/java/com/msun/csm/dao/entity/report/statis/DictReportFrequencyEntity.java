package com.msun.csm.dao.entity.report.statis;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 报表使用频率字典表(DICT_REPORT_FREQUENCY)
 *
 * <AUTHOR>
 * @version 1.0.0 2025-01-13
 */

@Data
@TableName(value = "DICT_REPORT_FREQUENCY", schema = "csm")
@ApiModel
public class DictReportFrequencyEntity extends BasePO {
    /**
     * 版本号
     */
    private static final long serialVersionUID = -451241236366497641L;

    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    @ApiModelProperty(value = "主键")
    private Long reportFrequencyId;

    /**
     * 频率名称
     */
    private String reportFrequencyName;

    /**
     * 排序号
     */
    private Integer orderNo;
}