package com.msun.csm.dao.entity.dict;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/30/11:35
 */
@Data
@TableName (schema = "csm")
public class DictHospitalLevel {

    @ApiModelProperty ("主键id")
    @TableId (type = IdType.INPUT)
    private Long dictHospitalLevelId;

    @ApiModelProperty ("医院级别名称")
    private String hospitalLevelName;

    @ApiModelProperty ("医院级别code")
    private String hospitalLevelCode;
}
