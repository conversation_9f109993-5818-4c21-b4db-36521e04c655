package com.msun.csm.dao.mapper.proj;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.proj.ProductFunctionUseInfoPO;
import com.msun.csm.dao.entity.proj.ProjProductFunctionUseInfo;
import com.msun.csm.dao.entity.proj.UpdateFunctionUseInfoParam;


@Mapper
public interface ProjProductFunctionUseInfoMapper extends BaseMapper<ProjProductFunctionUseInfo> {

    /**
     * 更新使用次数信息
     *
     * @param param 参数
     * @return 更新成功数据条数
     */
    int updateUseInfoById(UpdateFunctionUseInfoParam param);

    /**
     * 根据项目ID、产品ID、功能点编码获取功能点使用情况
     *
     * @param projectInfoId 项目ID
     * @param yyProductId   产品ID
     * @param functionCode  功能点编码
     * @return 功能点使用情况
     */
    ProjProductFunctionUseInfo selectUseInfoById(@Param("projectInfoId") Long projectInfoId, @Param("yyProductId") Long yyProductId, @Param("functionCode") String functionCode);

    /**
     * 查询各项目的功能点使用信息
     *
     * @param projectInfoId 项目ID
     * @param yyProductId   产品ID
     * @param useStatus   使用情况：1-仅查看未使用；2-仅查看已使用
     * @return 功能点使用信息
     */
    List<ProductFunctionUseInfoPO> queryProductFunctionUseInfoByProject(@Param("projectInfoId") Long projectInfoId, @Param("yyProductId") Long yyProductId, @Param("useStatus") Integer useStatus);

}
