package com.msun.csm.dao.mapper.proj;


import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.proj.ProjDeductionDetailSatisfaction;
import com.msun.csm.dao.entity.proj.SatisfactionSurveyScoreRecordVO;


public interface ProjDeductionDetailSatisfactionMapper extends BaseMapper<ProjDeductionDetailSatisfaction> {

    List<SatisfactionSurveyScoreRecordVO> getSatisfactionScoreRecord(@Param("projectInfoId") Long projectInfoId);

    int updateSatisfactionSurveyScoreById(ProjDeductionDetailSatisfaction param);


    List<SatisfactionSurveyScoreRecordVO> getRevisitSatisfactionScoreRecord(@Param("projectInfoId") Long projectInfoId);


}
