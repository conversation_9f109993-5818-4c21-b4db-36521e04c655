package com.msun.csm.dao.entity.tmp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import lombok.Data;

/**
 * 里程碑-老换新版本检测-相关产品版本存储(TmpProjProductVersion)实体类
 *
 * <AUTHOR>
 * @since 2024-06-18 10:11:00
 */
@Data
@TableName(value = "tmp_proj_product_version", schema = "csm")
public class TmpProjProductVersion extends BasePO {
    /**
     * 产品版本存储id
     */
    @TableId(type = IdType.NONE)
    private Long projProductVersionId;
    /**
     * 项目id
     */
    private Long projectInfoId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 产品版本号
     */
    private String productVersion;
}
