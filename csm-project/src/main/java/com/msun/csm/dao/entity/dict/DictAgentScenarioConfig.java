package com.msun.csm.dao.entity.dict;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;
/**
 * @Description:
 * @Author:
 * @Date: 2025/07/07
 */

/**
 * AI智能体场景配置
 */
@ApiModel(description = "智能体场景配置")
@Data
@TableName(schema = "csm")
public class DictAgentScenarioConfig extends BasePO {

    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.INPUT)
    private Long agentScenarioConfigId;

    @ApiModelProperty(value = "智能体关联标记")
    private String agentCode;

    @ApiModelProperty(value = "应用场景编码")
    private String scenarioCode;

    @ApiModelProperty(value = "应用场景描述")
    private String scenarioDesc;

    @ApiModelProperty(value = "应用场景提示词")
    private String scenarioPrompt;

    @ApiModelProperty(value = "智能体名称")
    private String agentName;

    @ApiModelProperty(value = "测试环境访问地址")
    private String agentAddress;

    @ApiModelProperty(value = "生产环境访问地址")
    private String agentAddressProduce;

    @ApiModelProperty(value = "访问密钥")
    private String agentKey;

}
