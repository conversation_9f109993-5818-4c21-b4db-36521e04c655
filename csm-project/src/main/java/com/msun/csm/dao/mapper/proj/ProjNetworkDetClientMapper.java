package com.msun.csm.dao.mapper.proj;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.proj.ProjNetworkDetClient;
import com.msun.csm.dao.entity.proj.ProjNetworkDetClientRelative;
import com.msun.csm.model.param.ProjNetworkDetClientParam;

/**
 * <AUTHOR>
 * @since 2024-05-16 04:01:06
 */

@Mapper
public interface ProjNetworkDetClientMapper extends BaseMapper<ProjNetworkDetClient> {

    List<ProjNetworkDetClientRelative> findNetworkDetClientInfoList(ProjNetworkDetClientParam param);

    List<ProjNetworkDetClient> findCountNetworkDetClientInfo(@Param("hospitalInfoId") Long hospitalInfoId,
                                                             @Param("localIpAddress") String localIpAddress,
                                                             @Param("randomCode") Long randomCode
    );

    /**
     * 根据医院id查询终端集合
     *
     * @param hospitalInfoId
     * @return
     */
    List<ProjNetworkDetClient> findCountNetworkDetClientInfoByHospitalId(@Param("hospitalInfoId") Long hospitalInfoId, @Param("randomCode") Long randomCode);

    int deleteNotThieDet(@Param("hospitalId") Long hospitalId, @Param("localIpAddress") String localIpAddress, @Param("randomCode") Long randomCode);

    ProjNetworkDetClient getByRandomCode(@Param("randomCode") Long randomCode);

    List<ProjNetworkDetClient> findClient(ProjNetworkDetClient client);
}
