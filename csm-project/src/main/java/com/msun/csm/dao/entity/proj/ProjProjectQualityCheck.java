package com.msun.csm.dao.entity.proj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * PROJ_PROJECT_QUALITY_CHECK
 *
 * <AUTHOR>
 * @version 1.0.0 2025-01-06
 */
@ApiModel(description = "项目质量检测")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjProjectQualityCheck extends BasePO implements java.io.Serializable {
    /**
     * 版本号
     */
    private static final long serialVersionUID = 3831258668379136103L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.INPUT)
    private Long projectQualityCheckId;

    /**
     * 项目
     */
    @ApiModelProperty(value = "项目")
    private Long projectInfoId;

    /**
     * 检测类型 0 数据质量检测 1 字典对照
     */
    @ApiModelProperty(value = "检测类型 0 数据质量检测 1 字典对照")
    private Integer checkType;

    /**
     * 错误描述
     */
    @ApiModelProperty(value = "错误描述")
    private String errorDesc;

    /**
     * 错误数量
     */
    @ApiModelProperty(value = "错误数量")
    private Integer errorCount;

    /**
     * 字典名称
     */
    @ApiModelProperty(value = "字典名称")
    private String dictName;

    /**
     * 总记录条数
     */
    @ApiModelProperty(value = "总记录条数")
    private Integer recordCount;

    /**
     * 未对照记录条数
     */
    @ApiModelProperty(value = "未对照记录条数")
    private Integer unverifiedRecordCount;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

}