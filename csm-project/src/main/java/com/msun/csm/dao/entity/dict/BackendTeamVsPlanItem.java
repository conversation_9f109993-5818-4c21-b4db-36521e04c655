package com.msun.csm.dao.entity.dict;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;


@Data
@TableName(schema = "csm", value = "backend_team_vs_plan_item")
public class BackendTeamVsPlanItem {

    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private Long backendTeamVsPlanItemId;

    /**
     * 逻辑删除【0：否；1：是】
     */
    private Integer isDeleted;

    /**
     * 创建人id
     */
    private Long createrId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人id
     */
    private Long updaterId;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 后端服务团队类型编码：bustype-业务服务、datatype-数据服务、interfacetype-接口服务
     */
    private String teamTypeCode;

    /**
     * 项目计划节点编码
     */
    private String projectPlanItemCode;

    /**
     * 实施类型：-1.通用；1.老换新；2.新上线
     */
    private Integer upgradationType;
}
