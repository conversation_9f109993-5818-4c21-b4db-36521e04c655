package com.msun.csm.dao.entity.tmp;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/5/7
 */

/**
 * 临时表 用来记录新老系统项目信息对照
 */
@ApiModel (description = "临时表 用来记录新老系统项目信息对照")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName (schema = "csm", value = "tmp_project_new_vs_old")
public class TmpProjectNewVsOld {
    private static final long serialVersionUID = 1L;
    /**
     * 新老系统项目信息对照主键ID
     */
    @TableId
    @ApiModelProperty (value = "新老系统项目信息对照主键ID")
    private Long projectNewVsOldId;

    /**
     * 新系统项目信息ID
     */
    @ApiModelProperty (value = "新系统项目信息ID")
    private Long newProjectInfoId;

    /**
     * 新系统实施地客户信息ID
     */
    @ApiModelProperty (value = "新系统实施地客户信息ID")
    private Long newCustomInfoId;

    /**
     * 老系统项目信息ID
     */
    @ApiModelProperty (value = "老系统项目信息ID")
    private Long oldProjectInfoId;

    /**
     * 老系统实施地客户信息ID
     */
    @ApiModelProperty (value = "老系统实施地客户信息ID")
    private Long oldCustomInfoId;

    /**
     * 老系统客户id
     */
    @ApiModelProperty (value = "老系统客户id")
    private Long oldCustomId;

    /**
     * 新系统中项目来源1.派工，2 数据迁移
     */
    @ApiModelProperty(value = "新系统中项目来源1.派工，2 数据迁移")
    private Integer newProjectSource;
}
