package com.msun.csm.dao.entity.proj.projreport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@ApiModel(description = "报表数据")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjSurveyReport extends BasePO {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    @ApiModelProperty(value = "主键")
    private Long surveyReportId;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户信息ID")
    private Long customerInfoId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

    /**
     * 医院id
     */
    @ApiModelProperty(value = "医院id")
    private Long hospitalInfoId;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private Long yyProductId;

    /**
     * 模块id
     */
    @ApiModelProperty(value = "模块id")
    private Long yyModuleId;

    /**
     * 报表名称
     */
    @ApiModelProperty(value = "报表名称")
    private String reportName;

    /**
     * 上线必备 0： 否 1: 是
     */
    @ApiModelProperty(value = "上线必备 0： 否 1: 是")
    private Integer onlineEssential;

    /**
     * 完成状态：
     * <p>7：调研信息待补充（批量新增之后待完善调研信息）</p>
     * <p>0：未完成（已保存）</p>
     * <p>4：已提后端运维（提交调研审核）</p>
     * <p>6：后端运维调研审核驳回</p>
     * <p>5：后端运维调研审核通过</p>
     * <p>1：制作完成</p>
     * <p>2：制作完成已驳回（前端验证不通过，项目经理审核驳回）</p>
     * <p>8：制作完成前端验证通过（最终状态）</p>
     */
    @ApiModelProperty(value = "完成状态：")
    private Integer finishStatus;

    /**
     * 调研收集的打印样式路径，多个样式名称应使用英文逗号（,）分隔（proj_project_file表project_file_id集）
     */
    @ApiModelProperty(value = "调研收集的打印样式路径，多个样式名称应使用英文逗号（,）分隔")
    private String surveyImgs;

    /**
     * 补充图片
     */
    @ApiModelProperty(value = "补充图片")
    private String supplementImgs;

    /**
     * 完成结果图片
     */
    @ApiModelProperty(value = "完成结果图片")
    private String finishImgs;

    /**
     * 打印节点
     */
    @ApiModelProperty(value = "打印节点")
    private String printDataCode;

    /**
     * 报表标识用于报表平台中指定具体要处理的报表
     */
    @ApiModelProperty(value = "报表标识用于报表平台中指定具体要处理的报表")
    private String reportFileTag;

    /**
     * 报表制作平台支持预览和跳转功能，当前实现中包括锐浪和众阳两种报表类型
     */
    @ApiModelProperty(value = "报表制作平台支持预览和跳转功能，当前实现中包括锐浪和众阳两种报表类型")
    private String reportMakePlatform;

    /**
     * 审核意见
     */
    @ApiModelProperty(value = "审核意见")
    private String examineOpinion;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 调研负责人
     */
    @ApiModelProperty(value = "调研负责人")
    private Long surveyUserId;
    /**
     * 调研完成时间
     */
    @ApiModelProperty(value = "调研完成时间")
    private Timestamp surveyFinishTime;
    /**
     * 制作负责人
     */
    @ApiModelProperty(value = "制作负责人")
    private Long makeUserId;

    @ApiModelProperty(value = "分配审核人")
    private Long reviewerUserId;

    /**
     * 提交制作完成时间
     */
    @ApiModelProperty(value = "确认完成时间")
    private Timestamp commitFinishTime;
    /**
     * 确认完成时间
     */
    @ApiModelProperty(value = "确认完成时间")
    private Timestamp makeFinishTime;

    @ApiModelProperty(value = "打印纸张大小")
    private String reportPaperSize;

    /**
     * 是否是默认选项 1默认 0 非默认
     */
    @ApiModelProperty(value = "是否是默认选项 1默认 0 非默认")
    private Integer defaultFlag;

    @ApiModelProperty(value = "打印平台状态 0 默认 1 已下发")
    private Integer printStatus;

    @ApiModelProperty(value = "打印平台任务id")
    private String reportTaskId;

    @ApiModelProperty(value = "云健康医院id")
    @TableField(exist = false)
    private Long cloudHospitalId;

    /**
     * 前端验证负责人
     */
    private Long identifierUserId;

}
