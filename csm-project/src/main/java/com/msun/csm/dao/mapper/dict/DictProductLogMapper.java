package com.msun.csm.dao.mapper.dict;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.dict.DictProductLog;
import com.msun.csm.model.dto.DictProductLogPageDTO;
import com.msun.csm.model.vo.dict.DictProductLogVO;

@Mapper
public interface DictProductLogMapper extends BaseMapper<DictProductLog> {

    /**
     * 分页查询产品字典日志
     * @param pageDTO
     * @return
     */
    List<DictProductLogVO> selectDictProductLogByPage(DictProductLogPageDTO pageDTO);
}
