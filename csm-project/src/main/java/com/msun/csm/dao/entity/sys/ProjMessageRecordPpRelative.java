package com.msun.csm.dao.entity.sys;

import java.io.Serializable;
import java.util.Date;

import com.msun.csm.common.model.po.BasePO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-05-07 11:52:52
 */

@Data
public class ProjMessageRecordPpRelative extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema (description = "主键")
    private Long id;

    /**
     * 消息类别id
     */
    @Schema (description = "消息类别id")
    private Long messageTypeId;

    /**
     * 是否发送成功
     */
    @Schema (description = "是否发送成功")
    private int isSuccess;

    /**
     * 消息标题
     */
    @Schema (description = "消息标题")
    private String messageTitle;

    /**
     * 消息内容
     */
    @Schema (description = "消息内容")
    private String messageContent;

    /**
     * 消息url参数
     */
    @Schema (description = "消息url参数")
    private String messageContentParam;

    /**
     * 消息状态：1.未读；1.已读未处理；2.已读已处理
     */
    @Schema (description = "消息状态：1.未读；1.已读未处理；2.已读已处理")
    private int messageStatus;

    /**
     * 消息发送对象分类：-1.系统指定人员；1.到个人；2.到角色；3.到部门；4.到项目经理
     */
    @Schema (description = "消息发送对象分类：-1.系统指定人员；1.到个人；2.到角色；3.到部门；4.到项目经理")
    private int messageToCategory;

    /**
     * 消息发送对象id，如果到个人或系统指定人员或项目经理，则保存userid；如果到角色，则保存roleid；如果到部门，则保存deptid
     */
    @Schema (
            description = "消息发送对象id，如果到个人或系统指定人员或项目经理，则保存userid；如果到角色，则保存roleid；如果到部门，则保存deptid")
    private Long messageToId;

    /**
     * 消息读取人userid
     */
    @Schema (description = "消息读取人userid")
    private Long readUserId;

    /**
     * 消息读取时间
     */
    @Schema (description = "消息读取时间")
    private Date readTime;

    /**
     * 消息来源项目id
     */
    @Schema (description = "消息来源项目id")
    private Long sourceProjectInfoId;

    /**
     * 消息来源人userid
     */
    @Schema (description = "消息来源人userid")
    private Long sourceUserId;

    /**
     * 消息是否过期
     */
    @Schema (description = "消息是否过期")
    private int expiredFlag;

    /**
     * 消息有效期限
     */
    @Schema (description = "消息有效期限")
    private Date expiredTime;

    /**
     * 消息过期时间
     */
    @Schema (description = "消息过期时间")
    private Date invalidTime;

    /**
     * 消息过期方式，0未过期，1读取过期，2处理过期，3超过有效期限后自动过期，4需要后期处理的消息读取时过期
     */
    @Schema (
            description = "消息过期方式，0未过期，1读取过期，2处理过期，3超过有效期限后自动过期，4需要后期处理的消息读取时过期")
    private int expiredWay;

    /**
     * 消息通知次数
     */
    @Schema (description = "消息通知次数")
    private int tipTimes;

    /**
     * 消息最后一次通知时间
     */
    @Schema (description = "消息最后一次通知时间")
    private Date lastTipTime;

    /**
     * 创建人id
     */
    @Schema (description = "创建人id")
    private Long createrId;

    /**
     * 创建时间
     */
    @Schema (description = "创建时间")
    private Date createTime;

    /**
     * 修改人id
     */
    @Schema (description = "修改人id")
    private Long updaterId;

    /**
     * 修改时间
     */
    @Schema (description = "修改时间")
    private Date updateTime;
}
