package com.msun.csm.dao.entity.report.statis;

import javax.validation.constraints.NotNull;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 统计口径表(PROJ_STATISTICAL_CALIBRATION)
 *
 * <AUTHOR>
 * @version 1.0.0 2025-01-13
 */

@Data
@TableName(value = "PROJ_STATISTICAL_CALIBRATION", schema = "csm")
@ApiModel
public class ProjStatisticalCalibrationEntity extends BasePO {
    /**
     * 版本号
     */
    private static final long serialVersionUID = -1190859743752846169L;

    /**
     * statisticalCalibrationId
     */
    @TableId(type = IdType.INPUT)
    @ApiModelProperty(value = "主键")
    private Long statisticalCalibrationId;

    /**
     * statisticalReportMainId
     */
    private Long statisticalReportMainId;

    /**
     * statisticalCalibrationName
     */
    @ApiModelProperty(value = "统计口径名称")
    @NotNull(message = "统计口径名称不能为空")
    private String statisticalCalibrationName;

    /**
     * orderNo
     */
    @ApiModelProperty(value = "排序号")
    private Integer orderNo;


}