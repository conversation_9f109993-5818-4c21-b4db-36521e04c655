package com.msun.csm.dao.entity.sys;

import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 此对象组合查询部门与用户关联查询结果
 */
@Data
@TableName (schema = "csm")
public class DeptUserComp {

    /**
     * 父级id
     */
    private Long pid;

    /**
     * 用户id
     */
    private Long sysUserId;

    /**
     * 运营账号id
     */
    private String deptLeaderYunyingId;

    /**
     * 账户
     */
    private String account;
}
