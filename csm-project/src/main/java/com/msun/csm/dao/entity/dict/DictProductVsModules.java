package com.msun.csm.dao.entity.dict;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/05/28/15:00
 */
@Data
@TableName (schema = "csm")
public class DictProductVsModules {

    @ApiModelProperty ("主键id")
    @TableId(type = IdType.INPUT)
    private Long productVsModuleId;

    @ApiModelProperty ("运营平台产品id")
    private Long yyProductId;

    @ApiModelProperty ("运营平台模块id")
    private Long yyModuleId;

    @ApiModelProperty ("运营平台模块code")
    private String yyModuleCode;

    @ApiModelProperty ("运营平台模块名称")
    private String yyModuleName;

    @ApiModelProperty ("是否删除 【1：是 ； 0：否】")
    private Integer isDeleted;

    @ApiModelProperty ("是否需要调研 【1：是 ； 0：否】")
    private Integer needSurvey;
}
