package com.msun.csm.dao.entity.oldimsp;

import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * 实施平台产品表
 */
@Data
@TableName (value = "product", schema = "platform")
public class OldProduct implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId (value = "id", type = IdType.AUTO)
    private Long id;

    //同步运营平台ID值,用作版本数据校验使用
    @TableField ("product_yunying_id")
    private Long productYunyingId;

    //产品名称
    @TableField ("name")
    private String name;

    //产品编码
    @TableField (value = "code")
    private String code;

    //产品简称，
    @TableField (value = "subname")
    private String subname;

    //分组名称
    @TableField ("group_name")
    private String groupName;

    //产品经理
    @TableField ("product_manager")
    private String productManager;

    //产品经理id
    @TableField ("product_manager_id")
    private Long productManagerId;

    //所属分公司
    @TableField ("subsidiary_org")
    private String subsidiaryOrg;

    //备注
    @TableField ("remark")
    private String remark;

    //使用流程
    @TableField (value = "flow_type")
    private String flowType;

    //层级。产品：1；模块：2
    @TableField (value = "level")
    private String level;

    //模块所属产品id。产品：-1。
    @TableField (value = "pid")
    private int pid;

    @TableField (value = "create_id", fill = FieldFill.INSERT)
    private Long createId;

    @TableField (value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @TableField (value = "update_id", fill = FieldFill.UPDATE)
    private Long updateId;

    @TableField (value = "update_time", fill = FieldFill.UPDATE)
    @JsonFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @TableField (value = "sort")
    private Integer sort;

    @TableField ("is_cloud")
    private Integer isCloud;

    @TableField ("del_flag")
    private Integer delFlag;

    @TableField (exist = false)
    private String useType;

    /**
     * 简拼
     */
    @TableField ("input_code")
    private String inputCode;
    /**
     * 全拼
     */
    @TableField ("full_code")
    private String fullCode;

    /**
     * 产品类型：
     * 1、自研软件；2、硬件；3、耗材；4、接口；5、软件服务费；6、硬件服务费；7、容灾；8、外采软件；9、云资源
     */
    @TableField ("product_type")
    private Integer productType;


    /**
     * 父id的运营平台id
     */
    @TableField (exist = false)
    private Long pProductYunyingId;


    /**
     * 是否集成  1 是  0 否
     */
    @TableField ("integration_flag")
    private Integer integrationFlag;
}