package com.msun.csm.dao.entity.oas;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 流程节点配置表(OasProcessConfig)实体类
 *
 * <AUTHOR>
 * @since 2021-01-18 10:53:37
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "form_category", schema = "form_library")
public class FormCategory extends BasePO {


    /**
     * 主键id
     */
    @TableId
    private Long formCategoryId;

    /**
     * 流程节点名称
     */
    private String formCategoryName;

    /**
     * 流程节点code
     */
    private String formCategoryCode;

    /**
     * 流程节点图标
     */
    private String categoryIcon;

    /**
     * 打印平台对应nodeCode
     */
    private String nodeCode;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 流程节点对应path
     */
    private String processPath;

    /**
     * 流程节点所处状态类型(术前术中术后)
     */
    private String processType;

    /**
     * 流程节点输入简码
     */
    private String inputCode;

    /**
     * 流程节点全拼
     */
    private String inputFullCode;

    /**
     * 父流程节点code
     */
    private String parentCode;

    /**
     * 拓展字段
     */
    private String expend;

    /**
     * 是否为患者菜单
     */
    private String menuFlag;

    /**
     * 页面来源
     */
    private String pageSource;

    /**
     * 是否其他系统可修改
     */
    private String allowModifyFlag;

    /**
     * 流程节点对应主数据库表名
     */
    private String processTable;

    /**
     * 院区id
     */
    private Long hospitalId;

    /**
     * 允许存储数据的最大序号
     */
    private Integer formMaxNo;

    /**
     * 作废标识
     */
    @TableLogic
    private String invalidFlag;

    /**
     * 运营平台产品Id 手麻4050
     */
    private Long yyProductId;
}
