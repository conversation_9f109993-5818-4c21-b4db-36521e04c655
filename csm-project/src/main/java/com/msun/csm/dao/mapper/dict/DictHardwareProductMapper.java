package com.msun.csm.dao.mapper.dict;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.dict.DictHardwareProduct;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/07/04/14:43
 */
@Mapper
public interface DictHardwareProductMapper extends BaseMapper<DictHardwareProduct> {

    /**
     * 批量添加
     *
     * @param list
     * @return
     */
    int batchInsert(@Param ("list") List<DictHardwareProduct> list);
}
