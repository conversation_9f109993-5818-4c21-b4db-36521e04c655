package com.msun.csm.dao.entity.oas;

import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/20 16:27
 */
@Data
public class BasePO implements Serializable {

    public static final String ORG_ID = "his_org_id";
    public static final Long DEFAULT_ID = 0L;
    private static final String DEFAULT_USER_NAME = "管理员";
    private Long hisOrgId;
    private Long hisCreaterId;
    private String hisCreaterName;
    private Long hisUpdaterId;
    @ApiModelProperty("乐观锁标识(更新、逻辑删除时必传)")
    private Integer version;
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date hisCreateTime;
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date hisUpdateTime;
//    public void update() {
//        LoginUserInfo loginUser = this.getLoginUser();
//        this.hisUpdaterId = loginUser.getUserSysId();
//        this.hisUpdateTime = Calendar.getInstance().getTime();
//    }
//
//    protected LoginUserInfo getLoginUser() {
//        LoginUserInfo loginUserInfo = LoginUserContext.getLoginUserInfo();
//        if (loginUserInfo == null) {
//            loginUserInfo = new LoginUserInfo();
//            loginUserInfo.setUserId(DEFAULT_ID);
//            loginUserInfo.setUserName("管理员");
//            loginUserInfo.setUserSysId(DEFAULT_ID);
//            loginUserInfo.setHospitalId(DEFAULT_ID);
//            loginUserInfo.setOrgId(DEFAULT_ID);
//        }
//
//        return loginUserInfo;
//    }
//
//    public void init() {
//        LoginUserInfo loginUser = this.getLoginUser();
//        Long userId = loginUser.getUserSysId();
//        String userName = loginUser.getUserName();
//        this.hisCreateTime = Calendar.getInstance().getTime();
//        this.hisCreaterId = userId;
//        this.hisCreaterName = userName;
//        this.version = 0;
//        this.update();
//        this.autoSetOrgId();
//    }
}
