package com.msun.csm.dao.mapper.proj;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.common.model.BaseHospitalNameResp;
import com.msun.csm.dao.entity.proj.HospitalOnlineInfo;
import com.msun.csm.dao.entity.proj.ProjCustomVsProjectTypeEntity;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfoRelative;
import com.msun.csm.model.dto.HospitalInfoDTO;
import com.msun.csm.model.dto.ProjHospitalInfoDTO;
import com.msun.csm.model.dto.ProjHospitalInfoPageDTO;
import com.msun.csm.model.dto.ProjOnlineStepDetailDTO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.resp.applyorder.DepResourceApplyHospitalResult;
import com.msun.csm.model.vo.ProjHospitalInfoVO;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/24
 */
@Mapper
public interface ProjHospitalInfoMapper extends BaseMapper<ProjHospitalInfo> {


    /**
     * 批量查询医院信息
     *
     * @param dto
     * @return
     */
    List<ProjHospitalInfoVO> selectHospitalInfoList(ProjHospitalInfoPageDTO dto);

    /**
     * 修改医院信息
     *
     * @param dto
     */
    void updateHospitalInfo(ProjHospitalInfoDTO dto);

    /**
     * 批量更新医院状态
     *
     * @param hospitalIds
     * @return
     */
    int updateBatchStateByHospitalInfoList(@Param("hospitalOpenStatus") Integer hospitalOpenStatus, @Param("list") List<Long> hospitalIds);

    int updateBatchByHospitalInfoList(@Param("info") ProjHospitalInfo hospitalInfo, @Param("list") List<Long> hospitalIds);

    int updateBatchByHospitalInfoListByCloudHospitalId(@Param("info") ProjHospitalInfo hospitalInfo, @Param("list") List<Long> hospitalIds);

    /**
     * 更新域名
     *
     * @param cloudDomain
     * @param hospitalIds
     * @return
     */
    int updateBatchCloudDomainByHospitalInfoList(@Param("cloudDomain") String cloudDomain, @Param("list") List<Long> hospitalIds);

    /**
     * 根据项目id查询医院信息
     *
     * @param dto
     * @return
     */
    List<ProjHospitalInfo> getHospitalInfoByProjectId(SelectHospitalDTO dto);

    /**
     * 根据项目id查询医院和分配负责人的信息
     *
     * @param dto
     * @return
     */
    List<HospitalInfoDTO> getHospitalAndLeaderInfoByProjectId(@Param("dto") SelectHospitalDTO dto);

    /**
     * 根据客户id与项目id查询医院列表
     * 1. 用于下拉框展示使用
     *
     * @param customInfoId
     * @param projectId
     * @return
     */
    List<ProjHospitalInfo> findHospitalInfoByProjIdAndCustomerId(@Param("customInfoId") Long customInfoId, @Param("projectId") Long projectId);

    /**
     * 通过项目Id和实施地客户Id查询项目下卫生院上线状态
     *
     * @param customInfoId
     * @param projectId
     * @return
     */
    List<HospitalOnlineInfo> selectHospitalOnlineDetail(@Param("customInfoId") Long customInfoId, @Param("projectId") Long projectId);

    List<ProjHospitalInfoRelative> getHospitalInfoRelativeByHospitalDTO(SelectHospitalDTO dto);

    /**
     * 根据云健康机构id查询医院信息,
     *
     * @param cloudHospitalId 医院id
     * @return List<ProjHospitalInfo>
     */
    List<ProjHospitalInfoRelative> findHospitalByCloudHospitalId(@Param("cloudHospitalId") Long cloudHospitalId);

    /**
     * 根据客户id更新
     *
     * @param hospitalInfo
     * @return
     */
    int updateHospitalInfoByCustomInfoId(ProjHospitalInfo hospitalInfo);

    List<ProjHospitalInfo> findByCustomInfoId(@Param("customInfoId") Long customInfoId);

    int updateBatchCloudHospitalIdAndOrgIdByCustomInfoId(@Param("hospitalList") List<DepResourceApplyHospitalResult> deployResourceApplyHospitalVOList, @Param("customInfoId") Long customerId, @Param("hospitalOpenStatus") Integer hospitalOpenStatus);

    /**
     * 查询医院信息
     *
     * @param list
     * @return
     */
    List<ProjHospitalInfo> selectListByParamer(List<ProjOnlineStepDetailDTO> list);

    /**
     * 查询所有医院信息
     *
     * @return
     */
    List<ProjHospitalInfo> selectOnlineData();

    /**
     * 说明: 根据客户id修改客户id为新的客户id
     *
     * @param oldCustomInfoId
     * @param newCustomInfoId
     * @return
     */
    int updateByCustomInfoId(@Param("oldCustomInfoId") Long oldCustomInfoId, @Param("newCustomInfoId") Long newCustomInfoId);

    List<BaseHospitalNameResp> selectHospitalInfoListByParamer(@Param("isOnlyDomain") Boolean isOnlyDomain, @Param("customInfoId") Long customInfoId, @Param("projectInfoId") Long projectInfoId);

    /**
     * 查询医院信息
     * @param entity
     * @return
     */
    List<ProjHospitalInfo> selectListByEntity(ProjCustomVsProjectTypeEntity entity);

    ProjHospitalInfo getHospitalInfoById(@Param("hospitalInfoId") Long hospitalInfoId);

    /**
     * 根据项目id查询医院信息
     *
     * @param dto
     * @return
     */
    List<ProjHospitalInfo> findHospitalInfoByProjectId(SelectHospitalDTO dto);

}
