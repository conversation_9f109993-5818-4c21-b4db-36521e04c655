package com.msun.csm.dao.mapper.proj;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @version : V1.52.0
 * @ClassName: OldTbProjectReadyWorkMapper
 * @Description:
 * @Author: Yhongmin
 * @Date: 19:18 2024/7/2
 */
@Mapper
public interface OldTbProjectReadyWorkMapper {
    /**
     * 说明: 根据项目和节点id查询有没有PMO审核
     *
     * @param projectId
     * @param detailId
     * @return:int
     * @author: Yhongmin
     * @createAt: 2024/7/2 19:23
     * @remark: Copyright
     */
    int getCount(@Param("projectId") Long projectId,
                 @Param("detailId") Long detailId);
}
