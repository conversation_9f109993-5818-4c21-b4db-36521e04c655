package com.msun.csm.dao.entity.proj;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/5/23
 */

/**
 * 项目拆分流程日志记录表
 */
@ApiModel(description = "项目拆分流程日志记录表")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjProjectSplitProcessLog {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 流程表id
     */
    @ApiModelProperty(value = "流程表id")
    private Long processId;

    /**
     * 操作标题-流程状态1申请；2重新申请；3pmo审核通过；4pmo审核驳回5质管审核通过；6质管审核驳回
     */
    @ApiModelProperty(value = "操作标题-流程状态1申请；2重新申请；3pmo审核通过；4pmo审核驳回5质管审核通过；6质管审核驳回")
    private Integer status;

    /**
     * 申请原因/审核意见
     */
    @ApiModelProperty(value = "申请原因/审核意见")
    private String comments;

    /**
     * 操作日志  xxx提交了申请
     */
    @ApiModelProperty(value = "操作日志  xxx提交了申请")
    private String log;

    @ApiModelProperty("逻辑删除【0：否；1：是】")
    @TableLogic
    @TableField(fill = FieldFill.UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    private Integer isDeleted;

    @ApiModelProperty("创建人员id")
    private Long createrId;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("更新人员id")
    private Long updaterId;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
