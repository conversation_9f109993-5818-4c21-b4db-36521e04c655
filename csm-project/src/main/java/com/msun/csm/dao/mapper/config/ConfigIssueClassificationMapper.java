package com.msun.csm.dao.mapper.config;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.msun.csm.dao.entity.config.ConfigIssueClassification;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/11/29
 */

public interface ConfigIssueClassificationMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(ConfigIssueClassification record);

    int insertOrUpdate(ConfigIssueClassification record);

    int insertOrUpdateSelective(ConfigIssueClassification record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(ConfigIssueClassification record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    ConfigIssueClassification selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(ConfigIssueClassification record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(ConfigIssueClassification record);

    int updateBatch(@Param("list") List<ConfigIssueClassification> list);

    int updateBatchSelective(@Param("list") List<ConfigIssueClassification> list);

    int batchInsert(@Param("list") List<ConfigIssueClassification> list);

    /**
     * 查询所有
     *
     * @return
     */
    List<ConfigIssueClassification> selectAll();
}
