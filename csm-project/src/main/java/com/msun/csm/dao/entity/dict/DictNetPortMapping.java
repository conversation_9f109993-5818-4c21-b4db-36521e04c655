package com.msun.csm.dao.entity.dict;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/21
 */

/**
 * 端口映射字典表
 */
@ApiModel(description = "端口映射字典表")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DictNetPortMapping {
    /**
     * 端口映射字典表主键
     */
    @ApiModelProperty(value = "端口映射字典表主键")
    private Long portMappingId;

    /**
     * 端口映射中台名称
     */
    @ApiModelProperty(value = "端口映射中台名称")
    private String portMappingName;

    /**
     * 出口网关唯一标识
     */
    @ApiModelProperty(value = "出口网关唯一标识")
    private String exportGatewayCode;

    /**
     * 远方需要开通的端口
     */
    @ApiModelProperty(value = "远方需要开通的端口")
    private String mappingPort;

    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer orderNo;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private Long createrId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人id
     */
    @ApiModelProperty(value = "修改人id")
    private Long updaterId;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 逻辑删除【0：否；1：是】
     */
    @ApiModelProperty(value = "逻辑删除【0：否；1：是】")
    private Integer isDeleted;
}
