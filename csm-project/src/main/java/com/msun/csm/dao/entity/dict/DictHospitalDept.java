package com.msun.csm.dao.entity.dict;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/9/23
 */

/**
 * 医院科室字典表
 */
@ApiModel(description = "医院科室字典表")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DictHospitalDept {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long dictHospitalDeptId;

    /**
     * 科室名称
     */
    @ApiModelProperty(value = "科室名称")
    private String dictHospitalDeptName;

    /**
     * 备注信息
     */
    @ApiModelProperty(value = "备注信息")
    private String memo;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer orderNo;

    /**
     * 科室分类-预留字段
     */
    @ApiModelProperty(value = "科室分类-预留字段")
    private Integer hospitalDeptClass;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private Long createrId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人id
     */
    @ApiModelProperty(value = "修改人id")
    private Long updaterId;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 逻辑删除【0：否；1：是】
     */
    @ApiModelProperty(value = "逻辑删除【0：否；1：是】")
    private Integer isDeleted;
}
