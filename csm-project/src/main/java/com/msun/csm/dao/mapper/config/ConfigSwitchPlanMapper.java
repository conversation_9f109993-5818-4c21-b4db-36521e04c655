package com.msun.csm.dao.mapper.config;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.msun.csm.dao.entity.config.ConfigSwitchPlan;
import com.msun.csm.model.req.switchplan.SaveSwitchPlanReq;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/9/23
 */

public interface ConfigSwitchPlanMapper {
    /**
     * delete by primary key
     *
     * @param configSwitchPlanId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long configSwitchPlanId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(ConfigSwitchPlan record);

    int insertOrUpdate(ConfigSwitchPlan record);

    int insertOrUpdateSelective(ConfigSwitchPlan record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(ConfigSwitchPlan record);

    /**
     * select by primary key
     *
     * @param configSwitchPlanId primary key
     * @return object by primary key
     */
    ConfigSwitchPlan selectByPrimaryKey(Long configSwitchPlanId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(ConfigSwitchPlan record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(ConfigSwitchPlan record);

    int updateBatch(List<ConfigSwitchPlan> list);

    int updateBatchSelective(List<ConfigSwitchPlan> list);

    int batchInsert(@Param("list") List<ConfigSwitchPlan> list);

    /**
     * 根据属性查询
     *
     * @param req
     * @param hosDeptIds
     * @return
     */
    List<ConfigSwitchPlan> selectByAttrAndHosDepts(@Param("req") SaveSwitchPlanReq req,
                                                   @Param("hosDeptIds") List<Long> hosDeptIds);
}
