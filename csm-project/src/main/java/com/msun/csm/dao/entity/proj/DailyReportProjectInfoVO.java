package com.msun.csm.dao.entity.proj;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 项目信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DailyReportProjectInfoVO {

    /**
     * 项目信息ID
     */
    private Long projectInfoId;

    /**
     * 项目编号
     */
    private String projectNumber;

    /**
     * 实施地客户信息ID
     */
    private Long customInfoId;

    /**
     * 客户名称
     */
    private String customName;

    /**
     * 项目状态 1已派工、2已调研、3已入驻、4、准备完成、5已上线、6已验收、7已启动"
     */
    private Integer projectDeliverStatus;

    /**
     * 项目类型 【1单体  2区域】
     */
    private Integer projectType;

    /**
     * 上线时间
     */
    private Date onlineTime;

    /**
     * 项目经理ID
     */
    private Long projectLeaderId;

}
