package com.msun.csm.dao.entity.proj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.model.BaseEntity;

import lombok.Data;

/**
 * 项目表单资源配置表(ProjFormResourceConfig)实体类
 *
 * <AUTHOR>
 * @since 2024-05-20 13:44:38
 */
@Data
@TableName("proj_form_resource_config")
public class ProjFormResourceConfig extends BaseEntity {

    @TableId(type = IdType.NONE)
    private Long projFormId;

    private String formCode;
    /**
     * 表单名称
     */
    private String formName;
    /**
     * 可用标识：0.否；1.是
     */
    private Integer enabledFlag;
     /**
     * 项目信息id
     */
    private Long projectInfoId;
}
