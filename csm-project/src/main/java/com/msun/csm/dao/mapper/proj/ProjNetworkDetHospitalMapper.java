package com.msun.csm.dao.mapper.proj;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.proj.ProjNetworkDetHospital;
import com.msun.csm.dao.entity.proj.ProjNetworkDetHospitalRelative;
import com.msun.csm.model.param.ProjNetworkDetHospitalParam;

/**
 * <AUTHOR>
 * @since 2024-05-16 09:02:04
 */

@Mapper
public interface ProjNetworkDetHospitalMapper extends BaseMapper<ProjNetworkDetHospital> {

    /**
     * 查询医院网络检测情况
     *
     * @param param
     * @return
     */
    List<ProjNetworkDetHospitalRelative> findNetworkDetHospitalInfoList(ProjNetworkDetHospitalParam param);

    int updateByHospitalInfoId(ProjNetworkDetHospital detHospital);

    /**
     * 更新检测事件.
     *
     * @param eventCode 0 开始 1 结束
     * @param hospitalInfoId
     */
    int updateDetectEventById(@Param("eventCode") int eventCode, @Param("hospitalInfoId") Long hospitalInfoId);

    /**
     *
     * @param hospitalInfoId
     * @return
     */
    List<ProjNetworkDetHospital> findHospitalByHospitalId(@Param("hospitalInfoId") Long hospitalInfoId, @Param("sysType") Integer sysType);

//    ProjNetworkDetHospital getDetHospitalById(Long aLong);

    List<String> findDistinctDomainByHospitalInfoIds(@Param("list") List<Long> ids);

    int deleteById(Long id);
}
