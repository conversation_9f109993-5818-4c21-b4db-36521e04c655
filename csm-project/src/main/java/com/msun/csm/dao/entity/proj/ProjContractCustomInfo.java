package com.msun.csm.dao.entity.proj;

import java.io.Serializable;

import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/24
 */

/**
 * 合同客户信息
 */
@ApiModel(description = "合同客户信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjContractCustomInfo extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 合同客户信息id
     */
    @ApiModelProperty(value = "合同客户信息id")
    private Long contractCustomInfoId;
    /**
     * 运营平台主体客户甲方ID
     */
    @ApiModelProperty(value = "运营平台主体客户甲方ID")
    private Long yyPartaId;
    /**
     * 合同客户名称
     */
    @ApiModelProperty(value = "合同客户名称")
    private String contractCustomName;
    /**
     * 合同客户归属销售责任人ID
     */
    @ApiModelProperty(value = "合同客户归属销售责任人ID")
    private Long contractCustomSalepersonId;
    /**
     * 合同客户归属销售团队ID
     */
    @ApiModelProperty(value = "合同客户归属销售团队ID")
    private Long contractCustomSaleteamId;
    /**
     * 合同客户归属销售省区ID
     */
    @ApiModelProperty(value = "合同客户归属销售省区ID")
    private Long contractCustomSaleprovinceId;
    /**
     * 合同客户归属销售中心ID
     */
    @ApiModelProperty(value = "合同客户归属销售中心ID")
    private Long contractCustomSalecenterId;
}
