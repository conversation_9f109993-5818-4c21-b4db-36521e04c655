package com.msun.csm.dao.entity.dict;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/30/8:54
 */
@Data
@TableName (schema = "csm")
public class DictHospitalOrg {

    @ApiModelProperty ("主键id")
    @TableId (type = IdType.INPUT)
    private Long dictHospitalOrgId;

    @ApiModelProperty ("医院机构名称")
    private String hospitalOrgName;

    @ApiModelProperty ("医院机构code")
    private String hospitalOrgCode;
}
