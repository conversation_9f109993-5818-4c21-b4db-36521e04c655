package com.msun.csm.dao.entity.comm;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * 交付平台医院信息表
 */
@Data
@TableName (value = "hzz_deliver_hospital", schema = "comm")
public class HzzDeliverHospital {
    @TableField ("id")
    private String id;

    @TableField ("deliver_id")
    private String deliverId;
    /**
     * 是否自建平台:0-无;1-有
     */
    @TableField ("self_platform")
    private Integer selfPlatform;
    /**
     * 微信商户类型:0-普通;1-V3;2-特约
     */
    @TableField ("wx_merchant_type")
    private Integer wxMerchantType;
    /**
     * 微信商户号
     */
    @TableField ("wx_merchant_no")
    private String wxMerchantNo;
    /**
     * 微信appId
     */
    @TableField ("wx_app_id")
    private String wxAppId;
    /**
     * 微信appsecret
     */
    @TableField ("wx_app_secret")
    private String wxAppSecret;
    /**
     * 微信支付密钥
     */
    @TableField ("wx_pay_key")
    private String wxPayKey;
    /**
     * 证书
     */
    @TableField ("wx_certificate")
    private String wxCertificate;
    /**
     * v3密钥
     */
    @TableField ("wx_v3_secretkey")
    private String wxV3Secretkey;
    /**
     * v3证书
     */
    @TableField ("wx_v3_certificate")
    private String wxV3Certificate;
    /**
     * v3证书序列号
     */
    @TableField ("wx_v3_serial")
    private String wxV3Serial;
    /**
     * 支付宝商户名
     */
    @TableField ("ali_merchant")
    private String aliMerchant;
    /**
     * 支付宝公钥
     */
    @TableField ("ali_public_key")
    private String aliPublicKey;
    /**
     * 支付宝私钥
     */
    @TableField ("ali_private_key")
    private String aliPrivateKey;
    /**
     * 支付宝appId
     */
    @TableField ("ali_app_id")
    private String aliAppId;
    /**
     * 医院id
     */
    @TableField ("hospital_id")
    private String hospitalId;
    /**
     * 医院编号
     */
    @TableField ("hospital_code")
    private String hospitalCode;
    /**
     * 医院名称
     */
    @TableField ("hospital_name")
    private String hospitalName;
    /**
     * 医院等级
     */
    @TableField ("hospital_grade")
    private String hospitalGrade;
    /**
     * 医院级别
     */
    @TableField ("hospital_level")
    private String hospitalLevel;
    /**
     * 医院经纬度
     */
    @TableField ("lat_long")
    private String latLong;
    /**
     * 医院所在省
     */
    @TableField ("provice")
    private String provice;
    /**
     * 医院所在市
     */
    @TableField ("city")
    private String city;
    /**
     * 医院所在县
     */
    @TableField ("district")
    private String district;

    @TableField ("chinese_district")
    private String chineseDistrict;
    /**
     * 医院联系电话
     */
    @TableField ("hospital_phone")
    private String hospitalPhone;

    /**
     * 支付平台返回字段
     */
    @TableField ("partner_id")
    private String partnerId;


    @TableField (value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    @TableField (value = "update_time", fill = FieldFill.UPDATE)
    @JsonFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
