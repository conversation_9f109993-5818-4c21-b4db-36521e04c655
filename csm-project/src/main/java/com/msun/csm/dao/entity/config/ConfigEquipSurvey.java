package com.msun.csm.dao.entity.config;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-01-09 02:48:56
 */

@Data
@TableName(schema = "csm")
public class ConfigEquipSurvey extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @TableId("config_equip_survey_id")
    private Long configEquipSurveyId;

    /**
     * 产品id(运营)
     */
    @Schema(description = "产品id(运营)")
    private Long yyProductId;

    /**
     * 字段编码
     */
    @Schema(description = "字段编码")
    private String fieldCode;

    /**
     * 字段名称
     */
    @Schema(description = "字段名称")
    private String fieldName;

    /**
     * 展示场景（一个sence_code会有多个字段）,
     */
    @Schema(description = "展示场景（一个sence_code会有多个字段）,")
    private String senceCode;

    /**
     * 实施类型：-1.通用；1.老换新；2.新上线
     */
    @Schema(description = "实施类型：-1.通用；1.老换新；2.新上线")
    private Short upgradationType;

    /**
     * 单体是否可用：0.否；1.是
     */
    @Schema(description = "单体是否可用：0.否；1.是")
    private Short monomerFlag;

    /**
     * 区域是否可用：0.否；1.是
     */
    @Schema(description = "区域是否可用：0.否；1.是")
    private Short regionFlag;

    /**
     * 电销是否可用：0.否；1.是
     */
    @Schema(description = "电销是否可用：0.否；1.是")
    private Short telesalesFlag;
}
