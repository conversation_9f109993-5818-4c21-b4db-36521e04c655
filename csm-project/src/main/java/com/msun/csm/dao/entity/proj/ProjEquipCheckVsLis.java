package com.msun.csm.dao.entity.proj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/10/17/10:25
 */
@Data
@TableName (schema = "csm")
public class ProjEquipCheckVsLis extends BasePO {

    @ApiModelProperty ("主键id")
    @TableId (type = IdType.INPUT)
    private Long equipCheckVsLisId;

    @ApiModelProperty ("Lis设备id")
    private Long equipRecordVsLisId;

    @ApiModelProperty ("云健康医院id")
    private Long cloudHospitalId;

    @ApiModelProperty ("医院名称")
    private String hospitalName;

    @ApiModelProperty ("通道号")
    private String itemChannel;

    @ApiModelProperty ("项目编码")
    private String itemNo;

    @ApiModelProperty ("项目名称")
    private String itemName;

    @ApiModelProperty ("调试结果 1、调试通过   2 调试失败")
    private int state;


}
