package com.msun.csm.dao.entity.knowledge;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;


@Data
@TableName(schema = "knowledge", value = "yjk_maintenance_team")
public class YjkMaintenanceTeam {

    /**
     * 主键
     */
    private Long id;

    /**
     * 团队名称
     */
    private String name;

    /**
     * 团队长userId
     */
    private Long userIdLeader;

    /**
     * 作废标识（0未作废，1已作废）
     */
    private Integer invalidFlag;

    /**
     * 创建人id
     */
    private Long hisCreaterId;

    /**
     * 创建人姓名
     */
    private String hisCreaterName;

    /**
     * 创建时间
     */
    private Date hisCreateTime;

    /**
     * 更新人id
     */
    private Long hisUpdaterId;

    /**
     * 最后更新时间
     */
    private Date hisUpdateTime;

    /**
     * 团队长姓名
     */
    private String userNameLeader;

    /**
     * 最后更新人姓名
     */
    private String hisUpdaterName;

    /**
     * 团队类型编码
     */
    private String teamTypeCode;

    /**
     * 团队类型名称
     */
    private String teamTypeName;

}
