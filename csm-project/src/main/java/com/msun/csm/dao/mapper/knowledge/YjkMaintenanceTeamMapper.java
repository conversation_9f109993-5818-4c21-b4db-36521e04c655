package com.msun.csm.dao.mapper.knowledge;

import java.util.List;

import com.msun.csm.dao.entity.knowledge.BackendTeamInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.knowledge.YjkMaintenanceTeam;


@Mapper
public interface YjkMaintenanceTeamMapper extends BaseMapper<YjkMaintenanceTeam> {

    /**
     * 根据团队类型编码获取团队信息
     *
     * @param teamTypeCode 团队类型编码：bustype-业务服务团队；datatype-数据服务团队；interfacetype-接口服务团队
     * @return 运维平台的后端团队信息
     */
    List<BackendTeamInfo> getTeamByTypeCode(@Param("teamTypeCode") String teamTypeCode);

}
