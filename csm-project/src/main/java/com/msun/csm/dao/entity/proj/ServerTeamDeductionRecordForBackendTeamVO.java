package com.msun.csm.dao.entity.proj;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServerTeamDeductionRecordForBackendTeamVO {

    /**
     * 来源：首次验收(first)/最终验收(final)
     */
    private String source;

    /**
     * 项目ID
     */
    private Long projectInfoId;

    /**
     * 后端运维服务得分扣分记录确认单主键
     */
    private Long backendTeamDeductionRecordId;

    /**
     * 客户名称
     */
    private String customName;

    /**
     * 工单编号
     */
    private String projectNumber;

    /**
     * 服务类型编码
     */
    private String serverTypeCode;

    /**
     * 服务类型名称
     */
    private String serverTypeName;

    /**
     * 后端服务团队运营平台ID
     */
    private Long serverTeamYyId;

    /**
     * 后端服务团队名称
     */
    private String serverTeamName;

    /**
     * 服务团队负责人运营平台ID
     */
    private Long serverTeamLeaderYyId;

    /**
     * 服务团队负责人姓名
     */
    private String serverTeamLeaderName;

    /**
     * 记录状态：1-未发送、2-待后端确认、3-后端已驳回、4-后端已确认
     */
    private Integer recordStatus;

    /**
     * 总得分
     */
    private BigDecimal totalScore;//-逻辑获取

    /**
     * 扣分
     */
    private BigDecimal deductionScore;// 逻辑获取

    /**
     * 待确认项数量
     */
    private Integer toBeConfirmedCount;// 逻辑获取

    /**
     * 申请时间
     */
    private String applyDate;

    /**
     * 申请人ID
     */
    private Long applyUserId;

    /**
     * 申请人姓名
     */
    private String applyUserName;

    /**
     * 确认时间
     */
    private String confirmDate;

    /**
     * 确认人ID
     */
    private Long confirmUserId;

    /**
     * 确认人姓名
     */
    private String confirmUserName;

    /**
     * 是否可以审核，可以审核时为true；不可审核时为false
     */
    private Boolean canAudit;

}
