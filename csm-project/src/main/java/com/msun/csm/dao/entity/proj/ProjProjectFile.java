package com.msun.csm.dao.entity.proj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/5/23
 */

/**
 * 项目文件管理表
 */
@ApiModel (description = "项目文件管理表")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName (schema = "csm")
public class ProjProjectFile extends BasePO {
    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    @ApiModelProperty (value = "主键")
    private Long projectFileId;

    /**
     * 项目id
     */
    @ApiModelProperty (value = "项目id")
    private Long projectInfoId;

    /**
     * 项目阶段编码
     */
    @ApiModelProperty (value = "项目阶段编码")
    private String projectStageCode;

    /**
     * 项目节点编码
     */
    @ApiModelProperty (value = "项目节点编码")
    private String milestoneNodeCode;

    /**
     * 文件名称
     */
    @ApiModelProperty (value = "文件名称")
    private String fileName;

    /**
     * 文件OBS路径
     */
    @ApiModelProperty (value = "文件OBS路径")
    private String filePath;

    /**
     * 文件简要说明
     */
    @ApiModelProperty (value = "文件简要说明")
    private String fileDesc;
}
