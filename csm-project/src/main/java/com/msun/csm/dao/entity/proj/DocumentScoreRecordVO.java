package com.msun.csm.dao.entity.proj;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentScoreRecordVO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 项目阶段编码
     */
    private String projectStageCode;

    /**
     * 项目阶段名称
     */
    private String projectStageName;

    /**
     * 项目里程碑节点编码
     */
    private String milestoneNodeCode;

    /**
     * 项目里程碑节点名称
     */
    private String milestoneNodeName;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件地址
     */
    private String fileUrl;

    /**
     * 预估扣分
     */
    private String estimatedDeduction;

    /**
     * 实际扣分
     */
    private String practicalDeduction;

    /**
     * 备注
     */
    private String remark;

    //---------------------------

    /**
     * 项目ID
     */
    private Long projectInfoId;

    /**
     * 菜单编码
     */
    private String menuCode;

    /**
     * 是否只需要一次验收：true-只需要一次验收；false-需要两次验收
     */
    @NotNull(message = "参数【onlyOneCheckFlag】不可为null")
    private Boolean onlyOneCheckFlag;

    /**
     * 当前验收次数：1-第一次验收；2-第二次验收
     */
    @NotNull(message = "参数【currentAcceptanceTimes】不可为null")
    private Integer currentAcceptanceTimes;


}
