package com.msun.csm.dao.entity.dict;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;


/**
 * 产品应用调查功能字典表
 */
@Data
@TableName(schema = "csm", value = "dict_product_function")
public class DictProductFunction implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 删除标记，0-正常，1-已删除
     */
    private Integer isDelete;

    /**
     * 创建人
     */
    private Long createrId;

    /**
     * 更新人
     */
    private Long updaterId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 运营平台实施产品ID
     */
    private Long yyProductId;

    /**
     * 功能名称
     */
    private String functionName;

    /**
     * 功能编码
     */
    private String functionCode;

    /**
     * 功能说明
     */
    private String functionDesc;

    /**
     * 上级功能编码，没有上级功能时为null
     */
    private String parentCode;

    /**
     * 妇幼保健院是否必选，0-非必选，1-必选
     */
    private Integer maternalChildHospitalFlag;

    /**
     * 是否常用必备业务，比如挂号、缴费等可设置为常用必备业务，0-非常用，1-常用
     */
    private Integer commonFunctionFlag;

    /**
     * 功能点未完成时扣分标准
     */
    private BigDecimal markingStandard;

    /**
     * 统计使用次数的SQL
     */
    private String checkSql;

    /**
     * 人民医院可用：0-不可用；1-可用
     */
    private Integer peoplesHospitalFlag;

    /**
     * 中医院可用：0-不可用；1-可用
     */
    private Integer chineseHospitalFlag;

    /**
     * 肿瘤医院可用：0-不可用；1-可用
     */
    private Integer tumorHospitalFlag;

    /**
     * 口腔医院可用：0-不可用；1-可用
     */
    private Integer stomatologyHospitalFlag;

    /**
     * 眼科医院可用：0-不可用；1-可用
     */
    private Integer eyeHospitalFlag;

    /**
     * 脚本维护人ID
     */
    private Long checkSqlMaintainerUserId;

    /**
     * 脚本维护时间
     */
    private Date checkSqlMaintenanceTime;

    /**
     * add：新增
     * update：修改
     */
    @TableField(exist = false)
    private  String operationType;

}
