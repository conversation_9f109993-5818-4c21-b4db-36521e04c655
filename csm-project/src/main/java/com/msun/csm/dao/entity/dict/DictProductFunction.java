package com.msun.csm.dao.entity.dict;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;


/**
 * 产品应用调查功能字典表
 */
@Data
@TableName(schema = "csm", value = "dict_product_function")
public class DictProductFunction implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 删除标记，0-正常，1-已删除
     */
    private Integer isDelete;

    /**
     * 创建人
     */
    private Long createrId;

    /**
     * 更新人
     */
    private Long updaterId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 运营平台实施产品ID
     */
    private Long yyProductId;

    /**
     * 功能名称
     */
    private String functionName;

    /**
     * 功能编码
     */
    private String functionCode;

    /**
     * 功能说明
     */
    private String functionDesc;

    /**
     * 上级功能编码，没有上级功能时为null
     */
    private String parentCode;

    /**
     * 三级医院是否必选，0-非必选，1-必选
     */
    private Integer tertiaryHospitalFlag;

    /**
     * 二级医院是否必选，0-非必选，1-必选
     */
    private Integer secondHospitalFlag;

    /**
     * 一级医院是否必选，0-非必选，1-必选
     */
    private Integer firstHospitalFlag;

    /**
     * 妇幼保健院是否必选，0-非必选，1-必选
     */
    private Integer maternalChildHospitalFlag;

    /**
     * 其他专科医院（眼科医院、口腔医院）是否必选，0-非必选，1-必选
     */
    private Integer otherHospitalFlag;

    /**
     * 是否常用必备业务，比如挂号、缴费等可设置为常用必备业务，0-非常用，1-常用
     */
    private Integer commonFunctionFlag;

    /**
     * 功能点未完成时扣分标准
     */
    private Integer markingStandard;

    /**
     * 统计使用次数的SQL
     */
    private String checkSql;

    /**
     * 根据医院等级判断当前功能是否是必备功能
     *
     * @param hospitalLevel 医院等级
     * @return true-当前医院等级下的必备功能；false-当前医院等级下的可选功能
     */
    public boolean isRequiredFunction(Integer hospitalLevel) {
        // 医院等级是否必选标记
        boolean hospitalLevelFlag;
        if (Integer.valueOf("1").equals(hospitalLevel)) {
            hospitalLevelFlag = Integer.valueOf("1").equals(tertiaryHospitalFlag);
        } else if (Integer.valueOf("2").equals(hospitalLevel)) {
            hospitalLevelFlag = Integer.valueOf("1").equals(secondHospitalFlag);
        } else if (Integer.valueOf("3").equals(hospitalLevel)) {
            hospitalLevelFlag = Integer.valueOf("1").equals(firstHospitalFlag);
        } else if (Integer.valueOf("4").equals(hospitalLevel)) {
            hospitalLevelFlag = Integer.valueOf("1").equals(maternalChildHospitalFlag);
        } else {
            hospitalLevelFlag = Integer.valueOf("1").equals(otherHospitalFlag);
        }
        return hospitalLevelFlag;
    }

}
