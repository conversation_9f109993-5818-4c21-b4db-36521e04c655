package com.msun.csm.dao.mapper.report;

import java.util.List;

import com.msun.csm.common.config.mybatiscfg.RootMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.msun.csm.dao.entity.proj.GetBoardRecordParamPO;
import com.msun.csm.dao.entity.proj.projreport.ConfigCustomBackendLimit;
import com.msun.csm.model.dto.BackendOperationBoardInfo;
import com.msun.csm.model.param.custombackendlimit.QueryDataReq;
import com.msun.csm.model.resp.custombackendlimit.QueryProjectDataResp;

/**
 * 项目整体开启小前端大后端限制
 *
 * <AUTHOR>
 * @since 2024-09-27 15:27:17
 */
@Mapper
public interface ConfigCustomBackendLimitMapper extends RootMapper<ConfigCustomBackendLimit> {

    /**
     * 查询项目整体开启小前端大后端限制
     *
     * @param req
     * @return
     */
    List<QueryProjectDataResp> queryProjectData(QueryDataReq req);

    /**
     * 根据项目id修改项目整体开启小前端大后端限制
     *
     * @param projectInfoId
     * @param isOpen
     * @return
     */
    int updateByProjectId(@Param("projectInfoId") Long projectInfoId, @Param("isOpen") Integer isOpen);

    /**
     * 根据项目id查询项目整体开启小前端大后端限制
     *
     * @param projectIds
     * @return
     */
    List<QueryProjectDataResp> selectByProjectIds(@Param("projectIds") List<Long> projectIds);

    /**
     * 根据项目id查询项目整体开启小前端大后端限制
     *
     * @param projectInfoId 项目ID
     * @return
     */
    ConfigCustomBackendLimit selectByProjectId(@Param("projectInfoId") Long projectInfoId);

    List<BackendOperationBoardInfo> getBackendOperationBoardInfo(GetBoardRecordParamPO param);
}
