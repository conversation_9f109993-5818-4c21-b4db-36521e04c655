package com.msun.csm.dao.entity.dict;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;


/**
 * 项目验收菜单字典表
 */
@Data
@TableName(schema = "csm", value = "dict_satisfaction_survey_level")
public class DictSatisfactionSurveyLevel implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 删除标记，0-正常，1-已删除
     */
    private Integer isDelete;

    /**
     * 创建人
     */
    private Long createrId;

    /**
     * 更新人
     */
    private Long updaterId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 评价编码
     */
    private String levelCode;

    /**
     * 评价名称
     */
    private String levelName;

    /**
     * 评价得分
     */
    private Integer levelScore;

    /**
     * 评分标准
     */
    private String evaluationCriterion;

}
