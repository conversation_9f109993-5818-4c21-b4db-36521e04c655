package com.msun.csm.dao.entity.dict;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName(schema = "csm")
public class DictExamImportStep extends BasePO {

    /**
     * 数据ID
     */
    @ApiModelProperty("数据ID")
    @TableId(type = IdType.INPUT)
    private Long stepId;

    /**
     * 步骤业务说明
     */
    @ApiModelProperty("步骤业务说明")
    private String stepName;

    /**
     * 步骤执行顺序
     */
    @ApiModelProperty("步骤执行顺序")
    private Integer stepOrder;

    /**
     * 脚本信息
     */
    @ApiModelProperty("脚本信息")
    private String sqlText;

    /**
     * 脚本类型：1insert、2select
     */
    @ApiModelProperty("脚本类型：1insert、2select")
    private Integer sqlType;

    /**
     * 步骤内执行顺序
     */
    @ApiModelProperty("步骤内执行顺序")
    private Integer orderNo;

    /**
     * 步骤业务编码
     */
    @ApiModelProperty("步骤业务编码")
    private String stepCode;

    /**
     * 执行数据库名称
     */
    @ApiModelProperty("执行数据库名称")
    private String stepDatabaseName;

    /**
     * 执行数据库schema
     */
    @ApiModelProperty("执行数据库schema")
    private String stepSchema;

    /**
     * 执行数据库表名
     */
    @ApiModelProperty("执行数据库表名")
    private String stepTableName;
}
