package com.msun.csm.dao.entity.proj;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/24
 */

/**
 * 项目干系人信息
 */
@ApiModel (description = "项目干系人信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjProjectStakeholderInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 项目干系人信息ID
     */
    @ApiModelProperty (value = "项目干系人信息ID")
    private Long projectStakeholderInfoId;
    /**
     * 项目信息ID
     */
    @ApiModelProperty (value = "项目信息ID")
    private Long projectId;
    /**
     * 医院信息ID
     */
    @ApiModelProperty (value = "医院信息ID")
    private Long hospitalId;
    /**
     * 项目干系人ID
     */
    @ApiModelProperty (value = "项目干系人ID")
    private Long projectPersonId;
    /**
     * 项目干系人名称
     */
    @ApiModelProperty (value = "项目干系人名称")
    private String projectPersonName;
    /**
     * 项目干系人类型 1.院方信息员 2 公司内部
     */
    @ApiModelProperty (value = "项目干系人类型 1.院方信息员 2 公司内部")
    private Integer projectPersonType;
    /**
     * 项目干系人团队ID
     */
    @ApiModelProperty (value = "项目干系人团队ID")
    private Long projectTeamId;
    /**
     * 项目干系人团队名称
     */
    @ApiModelProperty (value = "项目干系人团队名称")
    private String projectTeamName;
    /**
     * 逻辑删除【0：否；1：是】
     */
    @ApiModelProperty (value = "逻辑删除【0：否；1：是】")
    private Integer isDeleted;
    /**
     * 创建人id
     */
    @ApiModelProperty (value = "创建人id")
    private Long createrId;
    /**
     * 创建时间
     */
    @ApiModelProperty (value = "创建时间")
    private Date createTime;
    /**
     * 更新人id
     */
    @ApiModelProperty (value = "更新人id")
    private Long updaterId;
    /**
     * 更新时间
     */
    @ApiModelProperty (value = "更新时间")
    private Date updateTime;
    /**
     * 项目干系人联系电话
     */
    @ApiModelProperty (value = "项目干系人联系电话")
    private String projectPersonPhone;
}
