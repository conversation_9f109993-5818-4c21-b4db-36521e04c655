package com.msun.csm.dao.mapper.conf;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.dao.entity.config.ConfigProductJobMenuDetail;
import com.msun.csm.model.dto.GetProductJobMenuDetailParam;
import com.msun.csm.model.dto.ProjProductBacklogDTO;
import com.msun.csm.model.vo.ConfigProductJobMenuDetailVO;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/07/26/15:31
 */
@Mapper
public interface ConfigProductJobMenuDetailMapper extends BaseMapper<ConfigProductJobMenuDetail> {

    /**
     * 查询产品菜单数据
     *
     * @param dto
     * @return
     */
    List<ConfigProductJobMenuDetailVO> selectProductJobMenuDetail(ProjProductBacklogDTO dto);

    /**
     * 删除数据
     * @param detailId
     * @return
     */
    Integer deleteById(Long detailId);

    /**
     * 查询产品菜单数据
     * @param projectInfoId
     * @return
     */
    List<BaseIdNameResp> selectProductMenuDetail(@Param("projectInfoId") Long projectInfoId);

    /**
     * 根据实施类型及项目阶段查询产品菜单数据
     *
     * @param param 参数
     * @return 产品菜单数据
     */
    List<ConfigProductJobMenuDetailVO> getProductJobMenuDetail(GetProductJobMenuDetailParam param);
}
