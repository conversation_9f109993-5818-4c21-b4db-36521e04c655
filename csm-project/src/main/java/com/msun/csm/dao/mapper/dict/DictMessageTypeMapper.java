package com.msun.csm.dao.mapper.dict;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.sys.DictMessageType;

/**
 * <AUTHOR>
 * @since 2024-05-07 05:41:10
 */

@Mapper
public interface DictMessageTypeMapper extends BaseMapper<DictMessageType> {

    DictMessageType getDictMessageTypeById(@Param ("id") long id);
}
