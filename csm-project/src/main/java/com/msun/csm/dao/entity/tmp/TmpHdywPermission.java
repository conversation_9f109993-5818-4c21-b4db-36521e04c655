package com.msun.csm.dao.entity.tmp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/11/15/10:03
 */
@Data
@TableName (schema = "csm")
public class TmpHdywPermission {

    @ApiModelProperty ("主键id")
    @TableId(type = IdType.INPUT)
    private Long hdywPermissionId;

    @ApiModelProperty ("客户id")
    private Long customInfoId;

    @ApiModelProperty ("客户类型【1：单体；2：区域】")
    private Integer customType;

    @ApiModelProperty ("业务线  1报表，2三方接口")
    private Integer businessLine;

    @ApiModelProperty ("运营平台产品id")
    private Long yyProductId;

    @ApiModelProperty ("是否限制只能后端运维团队操作")
    private Integer limitHdywFlag;

    @ApiModelProperty ("是否作废【0：否；1：是】")
    @TableLogic
    private Integer isDeleted;
}
