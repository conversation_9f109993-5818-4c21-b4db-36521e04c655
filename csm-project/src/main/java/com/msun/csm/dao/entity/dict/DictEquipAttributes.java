package com.msun.csm.dao.entity.dict;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/10
 */

/**
 * 设备属性字典表
 */
@ApiModel (description = "设备属性字典表")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DictEquipAttributes extends BasePO {
    /**
     * 设备属性字典id
     */
    @ApiModelProperty (value = "设备属性字典id")
    @TableId(type = IdType.INPUT)
    private Long equipAttributesId;

    /**
     * 设备属性code
     */
    @ApiModelProperty (value = "设备属性code")
    private String equipAttributesCode;

    /**
     * 设备分类id
     */
    @ApiModelProperty(value = "设备分类id")
    private Long equipClassId;

    /**
     * 设备属性Key
     */
    @ApiModelProperty(value = "设备属性Key")
    private String equipAttributesKey;

    /**
     * 设备属性Value
     */
    @ApiModelProperty(value = "设备属性Value")
    private String equipAttributesValue;


}
