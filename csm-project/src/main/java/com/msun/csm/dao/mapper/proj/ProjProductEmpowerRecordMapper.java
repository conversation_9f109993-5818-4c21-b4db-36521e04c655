package com.msun.csm.dao.mapper.proj;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.proj.ProjProductEmpowerRecord;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/5/28
 */

public interface ProjProductEmpowerRecordMapper extends BaseMapper<ProjProductEmpowerRecord> {
    int deleteByPrimaryKey(Long productEmpowerRecordId);

    int insert(ProjProductEmpowerRecord record);

    int insertOrUpdate(ProjProductEmpowerRecord record);

    int insertOrUpdateSelective(ProjProductEmpowerRecord record);

    int insertSelective(ProjProductEmpowerRecord record);

    ProjProductEmpowerRecord selectByPrimaryKey(Long productEmpowerRecordId);

    int updateByPrimaryKeySelective(ProjProductEmpowerRecord record);

    int updateByPrimaryKey(ProjProductEmpowerRecord record);

    int updateBatch(List<ProjProductEmpowerRecord> list);

    int updateBatchSelective(List<ProjProductEmpowerRecord> list);

    int batchInsert(@Param("list") List<ProjProductEmpowerRecord> list);

    List<ProjProductEmpowerRecord> findEmpowerRecord(@Param("projectInfoId") Long projectInfoId, @Param(
            "productIdList") List<Long> productIdList);

    /**
     * 查询分院模式下授权产品
     *
     * @param projectInfoId 项目id
     * @return List<ProjProductEmpowerRecord>
     */
    List<ProjProductEmpowerRecord> findBranchEmpowerProduct(@Param("projectInfoId") Long projectInfoId);


    /**
     * 拆分项目更新数据
     *
     * @param oldProjectId
     * @param newProjectId
     * @param splitYYPIdList
     */
    int updateByProjectId(@Param("oldProjectId") Long oldProjectId,
                          @Param("newProjectId") Long newProjectId,
                          @Param("splitYYPIdList") List<Long> splitYYPIdList);

    /**
     * 根据项目ID删除
     *
     * @param projectInfoId
     */
    void deleteByProjectInfoId(Long projectInfoId);

    /**
     * 查询项目empower记录
     *
     * @param projectInfoIds
     * @return
     */
    List<ProjProductEmpowerRecord> findEmpowerRecordByProjectIds(@Param("projectInfoIds") List<Long> projectInfoIds);

    /**
     * 查询项目empower记录
     *
     * @param productIdList
     * @return
     */
    List<ProjProductEmpowerRecord> findByProductIds(@Param("productIdList") List<Long> productIdList);


    /**
     * 说明: 根据客户id修改云资源台账
     *
     * @param oldCustomInfoId
     * @param newCustomInfoId
     * @param projectInfoIds
     * @return
     */
    int updateByCustomInfoId(@Param("oldCustomInfoId") Long oldCustomInfoId,
                             @Param("newCustomInfoId") Long newCustomInfoId,
                             @Param("projectInfoIds") List<Long> projectInfoIds);
}
