package com.msun.csm.dao.entity.proj;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 网络改造方案PACS前置机磁盘信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PacsPrefrontDisk {
    /**
     * 排序号
     */
    private Integer sortNo;

    /**
     * 用途
     */
    private String useScene;

    /**
     * 磁盘大小
     */
    private String diskSize;

    /**
     * 卷组名称
     */
    private String volumeGroupName;

    /**
     * 逻辑卷
     */
    private String logicalVolume;

    /**
     * 挂载点
     */
    private String mountPoint;

}
