package com.msun.csm.dao.mapper.comm;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.proj.projreport.ProjBusinessExamineLog;
import com.msun.csm.model.resp.projreport.ProjBusinessExamineLogResp;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: zhangdi
 * @Date: 2025/03/11/10:53
 */
@Mapper
public interface ProjBusinessExamineLogMapper extends BaseMapper<ProjBusinessExamineLog> {

    /**
     * 根据业务主键查询操作日志
     *
     * @param id 业务主键
     * @return 操作日志
     */
    List<ProjBusinessExamineLogResp> selectLogById(@Param("id") Long id, @Param("examineStatus") Integer examineStatus);

    /**
     * 根据业务id查询最后一笔是已完成的
     * @param businessIds
     * @return
     */
    List<ProjBusinessExamineLog> selectListByIds(@Param("businessIds") List<Long> businessIds);

    /**
     * 根据业务id和状态查询
     * @param businessIds
     * @param status
     * @return
     */
    List<ProjBusinessExamineLog> selectListByParamer(@Param("businessIds") List<Long> businessIds, @Param("status") List<Integer> status);
}
