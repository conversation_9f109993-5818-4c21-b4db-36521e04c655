package com.msun.csm.dao.entity.projectreview;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

/**
 * 项目审核模式配置表
 *
 * <AUTHOR>
 * @TableName config_project_review
 */
@TableName(value = "config_project_review")
@Data
public class ConfigProjectReview extends BasePO {
    /**
     * 主键
     */
    @TableId
    private Long projectReviewId;

    /**
     * 审核类型id 取字典表 dict_project_review_type
     */
    private Integer reviewTypeId;

    /**
     * 客户类型 -1 通用 1单体 2区域
     */
    private Integer customType;

    /**
     * 电销属性 -1 通用 1 电销 0非电销
     */
    private Integer telesalesFlag;

    /**
     * 交付模式 -1 通用 1 前后端模式  0非前后端模式
     */
    private Integer deliveryModel;

    /**
     * 项目类型 -1 通用 1 首期 2 非首期
     */
    private Integer projectType;

    /**
     * 审核方式id 取字典表dict_review_method_type
     */
    private Long reviewMethodId;

    /**
     * 审核时间
     */
    private String reviewTime;

    /**
     * 预警时间
     */
    private String warningTime;

    /**
     * 是否产生罚单 0.否 1.是
     */
    private Integer isFineFlag;

    /**
     * 罚款金额
     */
    private Integer fineMoney;
}