package com.msun.csm.dao.entity.dict;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DictPlanItemFile extends BasePO {

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @TableId(type = IdType.INPUT)
    private Long dictPlanItemFileId;

    /**
     * 项目阶段编码
     */
    @ApiModelProperty("项目阶段编码")
    private String planStageCode;

    /**
     * 项目节点编码
     */
    @ApiModelProperty("项目节点编码")
    private String planItemCode;

    /**
     * 需上传资料描述
     */
    @ApiModelProperty("需上传资料描述")
    private String fileDesc;

    /**
     * 是否必传：0否1是
     */
    @ApiModelProperty("是否必传：0否1是")
    private Short needFlag;

    /**
     * 单体是否可用：0.否；1.是
     */
    @ApiModelProperty("单体是否可用：0.否；1.是")
    private Short monomerFlag;

    /**
     * 区域是否可用：0.否；1.是
     */
    @ApiModelProperty("区域是否可用：0.否；1.是")
    private Short regionFlag;

    /**
     * 电销是否可用：0.否；1.是
     */
    @ApiModelProperty("电销是否可用：0.否；1.是")
    private Short telesalesFlag;

    /**
     * 首期项目是否可用：0否1是
     */
    @ApiModelProperty("首期项目是否可用：0否1是")
    private Short initialFlag;

    /**
     * 非首期项目是否可用：0否1是
     */
    @ApiModelProperty("非首期项目是否可用：0否1是")
    private Short uninitialFlag;
}
