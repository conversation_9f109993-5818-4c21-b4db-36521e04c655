package com.msun.csm.dao.mapper.proj;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.common.model.BaseIdNameExtendResp;
import com.msun.csm.dao.entity.proj.NeedDocMilestoneInfo;
import com.msun.csm.dao.entity.proj.ProjMilestoneInfo;
import com.msun.csm.dao.entity.proj.UrlParam;
import com.msun.csm.model.dto.MilestoneInfoDTO;
import com.msun.csm.model.dto.ProjMilestoneInfoDTO;
import com.msun.csm.model.dto.SelectHospitalDTO;
import com.msun.csm.model.dto.UpdateMilestoneDTO;
import com.msun.csm.model.req.projtool.ProjProjectInfoUpdateReq;
import com.msun.csm.model.vo.ProjMilestoneInfoVO;

/**
 * @Description:
 * @Author: zhouzhaoyu
 * @Date: 2024/5/06
 */
@Mapper
public interface ProjMilestoneInfoMapper extends BaseMapper<ProjMilestoneInfo> {

    /**
     * 根据milestoneInfoDTO查询项目里程碑信息
     *
     * @param milestoneInfoDTO
     * @return
     */
    List<ProjMilestoneInfo> getMilestoneInfoByProjectInfoId(ProjMilestoneInfoDTO milestoneInfoDTO);

    List<NeedDocMilestoneInfo> getNeedDocMilestoneInfo(ProjMilestoneInfoDTO milestoneInfoDTO);


    /**
     * 批量插入里程碑节点信息表数据
     *
     * @param list
     * @return
     */
    Integer batchInsert(@Param("list") List<ProjMilestoneInfo> list);

    /**
     * 更新里程碑节点信息
     *
     * @param dto
     * @return
     */
    Integer updateMilestone(@Param("dto") UpdateMilestoneDTO dto);


    /**
     * 说明: 根据id获取节点信息
     *
     * @param id
     * @return:com.msun.csm.model.vo.ProjMilestoneInfoVO
     * @author: Yhongmin
     * @createAt: 2024/5/10 9:29
     * @remark: Copyright
     */
    ProjMilestoneInfoVO getProjMilestoneInfoVOById(Long id);

    /**
     * 说明: 多条件查询节点信息单条
     *
     * @param params
     * @return:com.msun.csm.model.vo.ProjMilestoneInfoVO
     * @author: Yhongmin
     * @createAt: 2024/5/10 11:59
     * @remark: Copyright
     */
    ProjMilestoneInfoVO getMilestoneInfoByParams(ProjMilestoneInfo params);

    /**
     * 说明: 多条件查询节点信息数组
     *
     * @param params
     * @return:com.msun.csm.model.vo.ProjMilestoneInfoVO
     * @author: Yhongmin
     * @createAt: 2024/5/10 11:59
     * @remark: Copyright
     */
    List<ProjMilestoneInfoVO> fingMilestoneInfoByParams(ProjMilestoneInfo params);

    /**
     * 通过阶段Id和项目Id查询里程碑节点信息
     *
     * @param projectInfoId
     * @param projectStageId
     * @return
     */
    List<ProjMilestoneInfoVO> getMilestoneInfo(@Param("projectInfoId") Long projectInfoId, @Param("projectStageId") Long projectStageId);


    /**
     * 查询urlParam
     *
     * @param projectInfoId
     * @return
     */
    UrlParam getUrlParams(@Param("projectInfoId") Long projectInfoId);

    /**
     * 通过projectInfoId查询该项目的节点信息，用于[准备阶段任务分配] 数据内容
     *
     * @param dto
     * @return
     */
    List<ProjMilestoneInfoVO> getMilestoneInfoByProjectId(@Param("dto") SelectHospitalDTO dto);

    /**
     * 批量更新里程碑信息
     *
     * @param list
     * @return
     */
    Integer batchUpdateMilestoneInfo(List<MilestoneInfoDTO> list);

    /**
     * 根据projectInfoId查询需要检测的准备工作
     *
     * @param projectInfoId 项目信息ID
     * @return 需要检测的准备工作
     */
    List<ProjMilestoneInfo> getMilestoneInfoByProjectInfoIdOnlyNeedCheck(@Param("projectInfoId") Long projectInfoId);

    /**
     * 查询项目下所有报表是否完成
     *
     * @param projMilestoneInfo
     * @return
     */
    Integer getReportCount(Map<String, Object> projMilestoneInfo);

    /**
     * 根据id更新里程碑信息
     *
     * @param dto 参数
     * @return int
     */
    int updateMilestoneById(UpdateMilestoneDTO dto);

    /**
     * 查询项目下所有报表是否完成
     *
     * @param projMilestoneInfo
     * @return
     */
    Integer getOldReportCount(Map<String, Object> projMilestoneInfo);

    /**
     * 查询项目下所有报表是否完成
     *
     * @param mao
     * @return
     */
    Integer getFormCount(Map<String, Object> mao);


    /**
     * 根据项目id查询里程碑信息
     *
     * @param projectInfoId
     * @return
     */
    List<ProjMilestoneInfo> findByProjectInfoId(Long projectInfoId);

    /**
     * 更新时间
     *
     * @param projectInfoId
     * @param completeTime
     * @param milestoneNodeCode
     */
    int updateTime(@Param("projectInfoId") Long projectInfoId, @Param("completeTime") Date completeTime, @Param("milestoneNodeCode") String milestoneNodeCode);

    /**
     * 查询项目里程碑信息
     *
     * @param projectInfoId
     * @return
     */
    List<BaseIdNameExtendResp> queryProjectMilestone(@Param("projectInfoId") Long projectInfoId);

    /**
     * 根据项目id查询项目里程碑信息
     *
     * @param libFormReq
     * @return
     */
    ProjMilestoneInfo getProjMilestoneInfoByProjectId(ProjProjectInfoUpdateReq libFormReq);

    /**
     * 根据项目id和节点编码查询里程碑信息
     *
     * @param projectInfoId
     * @param code
     * @return
     */
    ProjMilestoneInfo selectByProjectAndCode(@Param("projectInfoId") Long projectInfoId, @Param("code") String code);

    /**
     * 查询项目下所有报表是否完成
     *
     * @param mao
     * @return
     */
    Integer getFormSurveyFinishCount(Map<String, Object> mao);

    /**
     * 根据项目计划id，项目id更新里程碑完成状态
     * @param planItemCode
     * @param projectPlanId
     * @param projectInfoId
     * @param statusCode
     */
    void updateMilestoneByProjectPlan(@Param("planItemCode") String planItemCode, @Param("projectPlanId") Long projectPlanId, @Param("projectInfoId") Long projectInfoId, @Param("statusCode") Integer statusCode);
}
