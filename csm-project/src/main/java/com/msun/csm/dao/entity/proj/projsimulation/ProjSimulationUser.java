package com.msun.csm.dao.entity.proj.projsimulation;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName(schema = "csm")
public class ProjSimulationUser extends BasePO {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @TableId(type = IdType.INPUT)
    private Long projSimulationUserId;

    /**
     * 客户ID
     */
    @ApiModelProperty(value = "客户ID")
    private Long customInfoId;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private Long projectInfoId;

    /**
     * 医院ID
     */
    @ApiModelProperty(value = "医院ID")
    private Long hospitalInfoId;

    /**
     * 模拟流程角色字典ID
     */
    @ApiModelProperty(value = "模拟流程角色字典ID")
    private Long dictRoleId;

    /**
     * 模拟科室ID
     */
    @ApiModelProperty(value = "模拟科室ID")
    private Long simulationDeptId;

    /**
     * 模拟科室名称
     */
    @ApiModelProperty(value = "模拟科室名称")
    private String simulationDeptName;

    /**
     * 模拟用户ID
     */
    @ApiModelProperty(value = "模拟用户ID")
    private Long simulationUserId;

    /**
     * 模拟用户姓名
     */
    @ApiModelProperty(value = "模拟用户姓名")
    private String simulationUserName;

    /**
     * 模拟用户账号
     */
    @ApiModelProperty(value = "模拟用户账号")
    private String simulationUserAccount;

    /**
     * 病区ID
     */
    @ApiModelProperty(value = "病区ID")
    private Long simulationWardId;
}
