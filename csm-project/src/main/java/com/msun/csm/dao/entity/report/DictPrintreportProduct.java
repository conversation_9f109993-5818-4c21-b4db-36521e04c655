package com.msun.csm.dao.entity.report;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import lombok.Data;

/**
 * 打印平台产品
 *
 * @TableName dict_printreport_product
 */
@TableName(value = "dict_printreport_product")
@Data
public class DictPrintreportProduct extends BasePO {
    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private Long printreportProductId;

    /**
     * 打印平台产品名称
     */
    private String printProductName;
}