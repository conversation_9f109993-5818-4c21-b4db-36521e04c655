package com.msun.csm.dao.entity.proj;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BackendTeamDeductionRecordVO {

    /**
     * 后端服务团队扣分记录主键
     */
    private Long backendTeamDeductionRecordId;

    /**
     * 项目阶段名称
     */
    private String projectStageName;

    /**
     * 服务团队类型编码
     */
    private String serverTypeCode;

    /**
     * 服务团队类型名称
     */
    private String serverTypeName;

    /**
     * 服务团队运营平台ID
     */
    private Long serverTeamYyId;

    /**
     * 服务团队名称
     */
    private String serverTeamName;

    /**
     * 服务团队负责人运营平台ID
     */
    private Long serverTeamLeaderYyId;

    /**
     * 服务团队负责人姓名
     */
    private String serverTeamLeaderName;

    /**
     * 记录状态：1-未发送、2-待后端确认、3-后端已驳回、4-后端已确认
     */
    private Integer recordStatus;

    /**
     * 总得分
     */
        private BigDecimal totalScore;

    /**
     * 扣分
     */
    private BigDecimal deductionScore;

    /**
     * 待确认项数量
     */
    private Integer toBeConfirmedCount;

}
