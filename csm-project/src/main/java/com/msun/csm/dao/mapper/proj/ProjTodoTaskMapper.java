package com.msun.csm.dao.mapper.proj;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jetbrains.annotations.NotNull;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.proj.ProjTodoTask;
import com.msun.csm.model.req.todotask.QueryTodoTaskReq;
import com.msun.csm.model.resp.todotask.TodoTaskResp;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2025/1/13
 */

@Mapper
public interface ProjTodoTaskMapper extends BaseMapper<ProjTodoTask> {
    /**
     * delete by primary key
     *
     * @param todoTaskId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long todoTaskId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int addTodoTask(ProjTodoTask record);


    /**
     * select by primary key
     *
     * @param todoTaskId primary key
     * @return object by primary key
     */
    ProjTodoTask selectByPrimaryKey(Long todoTaskId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(ProjTodoTask record);

    @Deprecated
    int updateBatch(@Param("list") List<ProjTodoTask> list);

    int batchInsert(@Param("list") List<ProjTodoTask> list);

    /**
     * 批量删除
     *
     * @param planIds
     * @return
     */
    int deleteBatchPIds(@NotNull List<Long> planIds);

    /**
     * 查询数据
     *
     * @param req
     * @return
     */
    List<TodoTaskResp> queryData(@Param("req") QueryTodoTaskReq req);

    /**
     * 根据计划id和产品id查询
     *
     * @param projectPlanId
     * @param yyProductId
     * @param userId
     * @return
     */
    List<ProjTodoTask> selectByPlanAndProduct(@Param("projectPlanId") Long projectPlanId,
                                              @Param("hospitalInfoId") Long hospitalInfoId,
                                              @Param("yyProductId") Long yyProductId,
                                              @Param("userId") Long userId);


    /**
     * @param projectInfoId
     * @return
     */
    Integer queryMaxSort(Long projectInfoId);


    /**
     * 根据项目ID和计划ID查询待办列表
     *
     * @param projectInfoId 项目ID
     * @param planId        计划ID
     */
    List<ProjTodoTask> getTodoTaskByProjectAndPlan(@Param("projectInfoId") Long projectInfoId, @Param("planId") Long planId);


    List<ProjTodoTask> selectTodoTaskByTaskIdList(@Param("todoTaskIds") List<Long> todoTaskIds);

}
