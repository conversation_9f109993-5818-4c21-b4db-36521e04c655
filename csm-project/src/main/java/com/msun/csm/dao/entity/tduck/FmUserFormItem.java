package com.msun.csm.dao.entity.tduck;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.type.EnumTypeHandler;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.msun.csm.common.config.BooleanTypeHandler;
import com.msun.csm.common.config.JacksonTypeHandler;
import com.msun.csm.common.enums.tduck.FormItemTypeEnum;
import com.msun.csm.util.HtmlUtils;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/5/7
 */

/**
 * 临时表 用来记录新老系统项目信息对照
 */
@ApiModel(description = "临时表 用来记录新老系统项目信息对照")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "tduckpro", value = "fm_user_form_item", autoResultMap = true)
public class FmUserFormItem {
    private static final long serialVersionUID = 1L;
    /**
     * 新老系统项目信息对照主键ID
     */
    @TableId
    private Long id;

    /**
     * 表单Id
     */
    private String formKey;
    /**
     * 表单项Id 类型  + 时间戳
     */
    private String formItemId;
    /**
     * 表单项类型
     */
    @TableField(typeHandler = EnumTypeHandler.class)
    private FormItemTypeEnum type;
    /**
     * 表单项标题
     */
    private String label;


    /**
     * 展示类型组件 只在表单填写页查询到
     */
    @TableField(value = "is_display_type", typeHandler = BooleanTypeHandler.class)
    private Boolean displayType;

    /**
     * 隐藏类型组件 在表单填写页面无法查看到
     */
    @TableField(value = "is_hide_type", typeHandler = BooleanTypeHandler.class)
    private Boolean hideType;

    /**
     * 需要在入库前特殊处理的组件 比如随机编码等 验重
     */
    @TableField(value = "is_special_type", typeHandler = BooleanTypeHandler.class)
    private Boolean specialType;
    /**
     * 是否显示标签
     */
    @TableField(typeHandler = BooleanTypeHandler.class)
    private Boolean showLabel;

    /**
     * 表单项默认值
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String defaultValue;


    /**
     * 是否必填
     */
    @TableField(typeHandler = BooleanTypeHandler.class)
    private Boolean required;
    /**
     * 输入型提示文字
     */
    private String placeholder;
    /**
     * 排序
     */
    private Long sort;

    /**
     * 栅格宽度
     */
    private int span;

    /**
     * 扩展字段 表单项独有字段
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> scheme;

    /**
     * 正则表达式
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Map<String, Object>> regList;


    /**
     * 去除html格式
     *
     * @return
     */
    public String getTextLabel() {
        return HtmlUtils.cleanHtmlTag(this.label);
    }


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private String surveyResearchTitleCode;
}
