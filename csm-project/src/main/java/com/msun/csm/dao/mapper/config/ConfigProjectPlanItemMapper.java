package com.msun.csm.dao.mapper.config;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.msun.csm.dao.entity.config.ConfigProjectPlanItem;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2025/1/13
 */

@Mapper
public interface ConfigProjectPlanItemMapper {
    /**
     * delete by primary key
     *
     * @param configProjectPlanItemId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long configProjectPlanItemId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(ConfigProjectPlanItem record);

    int insertOrUpdate(ConfigProjectPlanItem record);

    int insertOrUpdateSelective(ConfigProjectPlanItem record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(ConfigProjectPlanItem record);

    /**
     * select by primary key
     *
     * @param configProjectPlanItemId primary key
     * @return object by primary key
     */
    ConfigProjectPlanItem selectByPrimaryKey(Long configProjectPlanItemId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(ConfigProjectPlanItem record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(ConfigProjectPlanItem record);

    int updateBatch(@Param("list") List<ConfigProjectPlanItem> list);

    int updateBatchSelective(@Param("list") List<ConfigProjectPlanItem> list);

    int batchInsert(@Param("list") List<ConfigProjectPlanItem> list);
}
