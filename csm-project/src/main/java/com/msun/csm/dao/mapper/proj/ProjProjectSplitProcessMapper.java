package com.msun.csm.dao.mapper.proj;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.msun.csm.dao.entity.proj.ProjProjectSplitProcess;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/5/23
 */
@Mapper
public interface ProjProjectSplitProcessMapper {
    int insert(ProjProjectSplitProcess record);

    int deleteByPrimaryKey(Long id);

    int insertOrUpdate(ProjProjectSplitProcess record);

    int insertOrUpdateSelective(ProjProjectSplitProcess record);

    int insertSelective(ProjProjectSplitProcess record);

    ProjProjectSplitProcess selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProjProjectSplitProcess record);

    int updateByPrimaryKey(ProjProjectSplitProcess record);

    int updateBatch(List<ProjProjectSplitProcess> list);

    int updateBatchSelective(List<ProjProjectSplitProcess> list);

    int batchInsert(@Param("list") List<ProjProjectSplitProcess> list);

    /**
     * 查询最新的一条申请记录
     *
     * @param projectInfoId
     * @return
     */
    ProjProjectSplitProcess selectLastByProjectId(Long projectInfoId);
}
