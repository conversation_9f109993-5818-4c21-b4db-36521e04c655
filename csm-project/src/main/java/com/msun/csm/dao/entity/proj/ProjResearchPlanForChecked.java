package com.msun.csm.dao.entity.proj;

import java.io.Serializable;
import java.util.Date;

import com.msun.csm.common.model.po.BasePO;

import lombok.Data;


/**
 * <AUTHOR>
 * @since 2024-05-10 11:17:54
 */

@Data
public class ProjResearchPlanForChecked extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long projResearchPlanId;

    /**
     * 医院id
     */
    private Long hospitalInfoId;

    /**
     * 医院名称
     */
    private String hospitalName;

    /**
     * 客户id
     */
    private Long customerInfoId;

    /**
     * 项目id
     */
    private Long projectInfoId;

    /**
     * 调研内容编码
     */
    private String milestoneNodeCode;

    /**
     * 主负责人id
     */
    private Long leaderId;

    /**
     * 主负责人姓名
     */
    private String leaderName;

    /**
     * 辅助负责人id(逗号分割存储多个负责人id)
     */
    private String secondLeaderId;

    /**
     * 计划开始时间
     */
    private Date planStartTime;

    /**
     * 计划结束时间
     */
    private Date planEndTime;

    /**
     * 结果来源id(医院)，0：现场调研
     */
    private Long resultSourceId;

    /**
     * 完成状态 0未完成 1已完成
     */
    private int completeStatus;
}
