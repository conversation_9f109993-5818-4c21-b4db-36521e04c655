package com.msun.csm.dao.entity.dict;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/25
 */

/**
 * 项目阶段字典表
 */
@ApiModel (description = "项目阶段字典表")
@Data
@TableName (schema = "csm")
public class DictProjectStage implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty (value = "主键")
    @TableId (type = IdType.INPUT)
    private Long id;
    /**
     * 项目阶段名称
     */
    @ApiModelProperty (value = "项目阶段名称")
    private String projectStageName;
    /**
     * 项目阶段编码
     */
    @ApiModelProperty (value = "项目阶段编码")
    private String projectStageCode;
    /**
     * 作废标识：0.否；1.是
     */
    @ApiModelProperty (value = "作废标识：0.否；1.是")
    @TableLogic
    private String invalidFlag;
    /**
     * 创建人id
     */
    @ApiModelProperty (value = "创建人id")
    private Long createrId;
    /**
     * 创建时间
     */
    @ApiModelProperty (value = "创建时间")
    private Date createTime;
    /**
     * 修改人id
     */
    @ApiModelProperty (value = "修改人id")
    private Long updaterId;
    /**
     * 修改时间
     */
    @ApiModelProperty (value = "修改时间")
    private Date updateTime;
}
