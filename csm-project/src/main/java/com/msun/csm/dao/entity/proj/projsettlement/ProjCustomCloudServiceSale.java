package com.msun.csm.dao.entity.proj.projsettlement;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@Data
public class ProjCustomCloudServiceSale implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 客户云服务信息表id
     */
    @ApiModelProperty(value = "客户云服务信息表id")
    private String customCloudServiceId;
    /**
     * 交付工单id
     */
    @ApiModelProperty(value = "交付工单id")
    private String orderInfoId;

    /**
     * 合同客户id
     */
    @ApiModelProperty(value = "合同客户id")
    private String contractCustomInfoId;

    /**
     * 实施地客户id
     */
    @ApiModelProperty(value = "实施地客户id")
    private String customInfoId;

    /**
     * 实施地客户名称
     */
    @ApiModelProperty(value = "实施地客户名称")
    private String contractName;
    /**
     * 实施地客户id
     */
    @ApiModelProperty(value = "合同号")
    private String contractNo;
    /**
     * 交付工单号
     */
    @ApiModelProperty(value = "交付工单号")
    private String deliveryOrderNo;
    /**
     * 交付工单类型
     */
    @ApiModelProperty(value = "交付工单类型")
    private Integer deliveryOrderType;
    /**
     * 环境主键
     */
    @ApiModelProperty(value = "环境主键")
    private String envirId;
    /**
     * 环境名称
     */
    @ApiModelProperty(value = "环境名称")
    private String envirName;
    /**
     * 云环境类型（云环境类型：1.众阳云；2.非众阳云）
     */
    @ApiModelProperty(value = "云环境类型（云环境类型：1.众阳云；2.非众阳云）")
    private String msunCloudFlag;
    /**
     * 运营工单id
     */
    @ApiModelProperty(value = "运营工单id")
    private Long yyOrderId;
    /**
     * 云服务订阅开始时间
     */
    @ApiModelProperty(value = "云服务订阅开始时间")
    private Date subscribeStartTime;


}
