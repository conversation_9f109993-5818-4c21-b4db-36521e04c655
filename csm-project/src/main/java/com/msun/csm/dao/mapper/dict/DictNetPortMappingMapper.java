package com.msun.csm.dao.mapper.dict;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.msun.csm.dao.entity.dict.DictNetPortMapping;
import com.msun.csm.dao.entity.dict.extend.DictNetPortMappingExtend;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/21
 */

public interface DictNetPortMappingMapper {
    /**
     * delete by primary key
     *
     * @param portMappingId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long portMappingId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(DictNetPortMapping record);

    int insertOrUpdate(DictNetPortMapping record);

    int insertOrUpdateSelective(DictNetPortMapping record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(DictNetPortMapping record);

    /**
     * select by primary key
     *
     * @param portMappingId primary key
     * @return object by primary key
     */
    DictNetPortMapping selectByPrimaryKey(Long portMappingId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(DictNetPortMapping record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(DictNetPortMapping record);

    int updateBatch(@Param("list") List<DictNetPortMapping> list);

    int updateBatchSelective(@Param("list") List<DictNetPortMapping> list);

    int batchInsert(@Param("list") List<DictNetPortMapping> list);

    /**
     * 根据网调研结果查询端口映射信息
     *
     * @param netSurveyDetailCode
     * @return
     */
    DictNetPortMapping selectBySurveyDetailCode(String netSurveyDetailCode);

    /**
     * 根据YY产品ID列表查询端口映射信息
     *
     * @param yyProductIdList
     * @return
     */
    List<DictNetPortMappingExtend> selectByYYProductIdList(List<Long> yyProductIdList);
}
