package com.msun.csm.dao.mapper.config;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.msun.csm.model.req.ConfigAgentScenarioDictReq;
import com.msun.csm.model.resp.DictAgentChatScenarioConfigResp;

/**
 * AI智能体场景配置Mapper
 * <AUTHOR> @since 2025-07-07 02:48:56
 */
@Mapper
public interface ConfigAgentScenarioDictMapper {

    /**
     * 查询AI智能体场景配置
     * @param req 查询条件（可选，为null时查询所有）
     * @return 配置列表
     */
    List<DictAgentChatScenarioConfigResp> getAgentScenarioConfig(@Param("req") ConfigAgentScenarioDictReq req);

    /**
     * 删除AI智能体场景配置（逻辑删除）
     * @param agentScenarioConfigId 配置ID
     * @return 影响行数
     */
    int deleteAgentScenarioConfig(@Param("agentScenarioConfigId") Long agentScenarioConfigId);

}
