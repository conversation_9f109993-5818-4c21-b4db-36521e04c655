package com.msun.csm.dao.entity.proj;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;


@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm", value = "proj_deduction_detail_info")
public class ProjDeductionDetailProcess implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long projDeductionDetailInfoId;

    /**
     * 删除标记，0-正常，1-已删除
     */
    private Integer isDeleted;

    /**
     * 创建人
     */
    private Long createrId;

    /**
     * 更新人
     */
    private Long updaterId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 项目ID
     */
    private Long projectInfoId;

    /**
     * 考核指标/分类编码（优先存储二级编码）
     */
    private String classificationCode;

    /**
     * 预估扣分
     */
    private BigDecimal estimatedDeduction;

    /**
     * 实际扣分
     */
    private BigDecimal practicalDeduction;

    /**
     * 扣分备注
     */
    private String remark;

    /**
     * 来源：首次验收(first)/最终验收(final)
     */
    private String source;

    /**
     * 项目阶段编码
     */
    private String projectStageCode;

    /**
     * 产品ID
     */
    private Long yyProductId;

    /**
     * 扣分类型字典表编码
     */
    private String deductionType;

    /**
     * 扣分明细数据，例如扣分的设备的厂商、型号，小硬件的名称等
     */
    private String itemDetail;

    /**
     * 项目文件表主键
     */
    private Date itemAddTime;

    /**
     * 附件主键，来自sys_file或者project_file
     */
    private String attachmentId;

    /**
     * 操作类型：process-项目进程扣分、document-项目文档扣分、common-快速实施应用线下培训、设备接口、数据统计，再根据classification_code判断是线下培训还是设备接口还是数据统计
     */
    private String operationType;

}
