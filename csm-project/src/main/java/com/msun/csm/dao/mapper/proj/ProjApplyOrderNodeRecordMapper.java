package com.msun.csm.dao.mapper.proj;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.proj.ProjApplyOrderNodeRecord;

/**
 * <AUTHOR>
 * @since 2024-05-23 08:32:14
 */

@Mapper
public interface ProjApplyOrderNodeRecordMapper extends BaseMapper<ProjApplyOrderNodeRecord> {

    List<ProjApplyOrderNodeRecord> findByApplyOrderId(Long id);

    int insertOrderNodeRecord(ProjApplyOrderNodeRecord nodeRecord);
}
