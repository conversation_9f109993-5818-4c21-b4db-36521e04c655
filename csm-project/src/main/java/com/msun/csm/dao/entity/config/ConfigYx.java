package com.msun.csm.dao.entity.config;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;


@Data
@TableName(schema = "csm", value = "config_yx")
public class ConfigYx {

    /**
     * 主键
     */
    private Long id;

    /**
     * 说明
     */
    private String valueDescribe;

    /**
     * 调研值（来自suItem(value)或者输入值, 标识每一项调研的值）
     */
    private String itemValue;

    /**
     * 所属医院
     */
    private Long customerId;

    /**
     * 创建人
     */
    private Long createrId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private Long updaterId;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 配置code
     */
    private String itemCode;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 产品id， 区分哪个产品的配置
     */
    private Long productId;

    /**
     * 配置类型 0 公共配置（系统管理） 1 产品私有配置
     */
    private Integer configType;

    /**
     * 1已导入，2未导入，0初始
     */
    private Integer configStatus;

    /**
     * 作废标识：0-正常；1-作废
     */
    private Integer isDeleted;

    /**
     * csm项目ID
     */
    private Long projectInfoId;

    /**
     * 运营平台产品ID
     */
    private Long yyProductId;

    /**
     * 老系统platform模式下的主键
     */
    private Long platformId;

    /**
     * 医院ID
     */
    private Long hospitalInfoId;

}
