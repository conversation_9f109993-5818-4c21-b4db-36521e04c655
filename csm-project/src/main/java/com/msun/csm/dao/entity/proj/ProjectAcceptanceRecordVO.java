package com.msun.csm.dao.entity.proj;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectAcceptanceRecordVO {

    /**
     * 工单编号
     */
    private String projectNumber;

    /**
     * 项目ID
     */
    private String projectInfoId;

    /**
     * 客户名称
     */
    private String customName;

    /**
     * 客户ID
     */
    private String customInfoId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 验收状态：1-首验申请；2-首验接受；3-首验驳回；4-首验完成；5-终验申请；6-终验接收；7-终验驳回；8-终验通过
     */
    private Integer status;

    /**
     * 验收得分
     */
    private String acceptanceScore;

    /**
     * 首验得分
     */
    private String firstScore;

    /**
     * 终验得分
     */
    private String finalScore;

    /**
     * 工期
     */
    private String standardDuration;

    /**
     * 入驻时间
     */
    private String settleInTime;

    /**
     * 验收考核时间
     */
    private String acceptanceTestTime;

    /**
     * 工期减免
     */
    private String durationReduction;

    /**
     * 实施团队名称
     */
    private String projectTeamName;

    /**
     * 实施团队ID
     */
    private String projectTeamId;

    /**
     * 项目经理名称
     */
    private String projectLeaderName;

    /**
     * 项目经理ID
     */
    private String projectLeaderId;

    /**
     * 是否只需要一次验收：true-只需要一次验收；false-需要两次验收
     */
    private Boolean onlyOneCheckFlag;

    /**
     * 排序用的时间
     */
    private Date acceptanceTestTimeSort;

}
