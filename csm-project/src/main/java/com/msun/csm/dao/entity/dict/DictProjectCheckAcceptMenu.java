package com.msun.csm.dao.entity.dict;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;


/**
 * 项目验收菜单字典表
 */
@Data
@TableName(schema = "csm", value = "dict_project_check_accept_menu")
public class DictProjectCheckAcceptMenu implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 删除标记，0-正常，1-已删除
     */
    private Integer isDelete;

    /**
     * 创建人
     */
    private Long createrId;

    /**
     * 更新人
     */
    private Long updaterId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 菜单编码
     */
    private String menuCode;

    /**
     * 上级菜单编码
     */
    private String parentCode;

    /**
     * 两次验收的首次验收是否可用，0-不可用，1-可用
     */
    private Integer firstCheck;

    /**
     * 两次验收的最终验收是否可用，0-不可用，1-可用
     */
    private Integer finalCheck;

    /**
     * 只有一次验收的是否可用，1-可用，其他不可用
     */
    private String webComponent;

    /**
     * 排序号
     */
    private Integer sortNo;

    /**
     * 考核指标/分类编码
     */
    private String classificationCode;

    /**
     * 仅需一次验收时菜单是否可用，0-不可用，1-可用
     */
    private Integer onlyOneCheck;

}
