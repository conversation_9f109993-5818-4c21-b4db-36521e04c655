package com.msun.csm.dao.mapper.report;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.dao.entity.report.DictDeliverproVsPrintreportpro;
import com.msun.csm.model.req.projreport.DictDeliverVsReportProductReq;
import com.msun.csm.model.resp.projreport.DictDeliverproVsPrintreportproResp;

/**
 * <AUTHOR>
 * @description 针对表【dict_deliverpro_vs_printreportpro(实施产品与打印平台产品对照)】的数据库操作Mapper
 * @createDate 2025-05-22 08:31:55
 * @Entity generator.addpage.DictDeliverproVsPrintreportpro
 */
public interface DictDeliverproVsPrintreportproMapper extends BaseMapper<DictDeliverproVsPrintreportpro> {

    /**
     * 查询列表
     * @param dto
     * @return
     */
    List<DictDeliverproVsPrintreportproResp> selectListByDto(DictDeliverVsReportProductReq dto);

    /**
     * 查询所有实施产品
     * @return
     */
    List<BaseIdNameResp> findProductAllDeliver(@Param("id") Long id);

}




