package com.msun.csm.dao.mapper.proj;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.core.component.implementation.api.imsp.dto.ProductEquipmentDto;
import com.msun.csm.dao.entity.proj.ProjEquipRecordVsAims;
import com.msun.csm.model.dto.AimsEquipSelectDTO;
import com.msun.csm.model.vo.AimsEquipSendCloudVO;
import com.msun.csm.model.vo.ProjEquipRecordVsAimsVO;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/12/02/15:36
 */
@Mapper
public interface ProjEquipRecordVsAimsMapper extends BaseMapper<ProjEquipRecordVsAims> {

    /**
     * 查询手麻设备列表数据
     *
     * @param dto
     * @return
     */
    List<ProjEquipRecordVsAimsVO> selectEquipRecordVsAimsList(AimsEquipSelectDTO dto);

    /**
     * 进行一键检测时的手麻设备数据查询
     *
     * @param dto
     * @return
     */
    List<ProductEquipmentDto> findToMsunEquipRecord(AimsEquipSelectDTO dto);

    /**
     * 查询手麻设备需要发送云健康的数据
     *
     * @param dto
     * @return
     */
    List<AimsEquipSendCloudVO> selectAimsEquipSendCloudData(AimsEquipSelectDTO dto);

    /**
     * 查询Lis设备未申请数量
     *
     * @param statusList
     * @return
     */
    Integer getNotApplyRecordCount(@Param("statusList") List<Integer> statusList,
                                   @Param("projectInfoId") Long projectInfoId);

    /**
     * 更新设备
     *
     * @param projEquipRecordVsAims 设备信息
     * @return 更新数据个数
     */
    int updateAimsById(ProjEquipRecordVsAims projEquipRecordVsAims);
}
