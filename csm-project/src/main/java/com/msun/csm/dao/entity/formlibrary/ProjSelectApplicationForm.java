package com.msun.csm.dao.entity.formlibrary;

import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@ApiModel(description = "选择表单数据")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjSelectApplicationForm extends BasePO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(type = IdType.INPUT)
    private Long selectApplicationFormId;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "选择表单id")
    private Long formPatternId;
    /**
     * 表单名称
     */
    @ApiModelProperty(value = "表单名称")
    private String formPatternName;

    /**
     * 描述说明
     */
    @ApiModelProperty(value = "描述说明")
    private String description;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Long sort;

    /**
     * 表单内容
     */
    @ApiModelProperty(value = "表单内容")
    private String formContentPc;

    /**
     * 表单页面配置
     */
    @ApiModelProperty(value = "表单页面配置")
    private String formConfigurationPc;

    /**
     * 医院id
     */
    @ApiModelProperty(value = "医院id")
    private Long hospitalId;

    /**
     * 机构ID
     */
    @ApiModelProperty(value = "机构ID")
    private Long hisOrgId;

    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID")
    private Long hisCreaterId;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称")
    private String hisCreaterName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Timestamp hisCreateTime;

    /**
     * 更新用户ID
     */
    @ApiModelProperty(value = "更新用户ID")
    private Long hisUpdaterId;

    /**
     * 乐观锁标识
     */
    @ApiModelProperty(value = "乐观锁标识")
    private Long version;

    /**
     * 作废标识
     */
    @ApiModelProperty(value = "作废标识")
    private Integer invalidFlag;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Timestamp hisUpdateTime;

    /**
     * 流程节点code
     */
    @ApiModelProperty(value = "流程节点code")
    private String formCategoryCode;

    /**
     * 平板端样式内容
     */
    @ApiModelProperty(value = "平板端样式内容")
    private String formContentPad;

    /**
     * 平板端样式格式
     */
    @ApiModelProperty(value = "平板端样式格式")
    private String formConfigurationPad;

    /**
     * 应用产品id  手麻：4050
     */
    @ApiModelProperty(value = "应用产品id  手麻：4050")
    private Long yyProductId;

    @ApiModelProperty("项目id")
    private Long projectInfoId;

    @ApiModelProperty("医院id")
    private Long hospitalInfoId;

    @ApiModelProperty(value = "发送状态 0 未发送 1 发送成功  2  发送失败")
    private Integer sendStatus;

    /**
     * 表单内容
     */
    @ApiModelProperty(value = "表单内容web")
    private String formContentWeb;

    /**
     * 表单页面配置
     */
    @ApiModelProperty(value = "表单页面配置web")
    private String formConfigurationWeb;
}
