package com.msun.csm.dao.entity.proj;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/24
 */

/**
 * 交付工单信息
 */
@ApiModel(description = "交付工单信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm", value = "proj_order_info")
public class ProjOrderInfo extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 交付工单信息ID
     */
    @ApiModelProperty(value = "交付工单信息ID")
    @TableId
    private Long orderInfoId;
    /**
     * 合同ID
     */
    @ApiModelProperty(value = "合同ID")
    private Long contractInfoId;
    /**
     * 运营平台交付工单ID
     */
    @ApiModelProperty(value = "运营平台交付工单ID")
    private Long yyOrderId;
    /**
     * 交付工单编号
     */
    @ApiModelProperty(value = "交付工单编号")
    private String deliveryOrderNo;
    /**
     * 交付工单类型 1、自研软件；2、硬件；3、耗材；4、接口；5、软件服务费；6、硬件服务费；7、容灾；8、外采软件；9、云资源
     */
    @ApiModelProperty(value = "交付工单类型")
    private int deliveryOrderType;
    /**
     * 交付工单云资源类型. ps:与运营平台同步
     */
    @ApiModelProperty(value = "交付工单云资源类型. ps:与运营平台同步")
    private String deliveryOrderCloudType;

    /**
     * 运营平台唯一id
     */
    @ApiModelProperty(value = "运营平台唯一id")
    private Long yyProjectNumber;

    /**
     * 运营平台实施地id
     */
    @ApiModelProperty(value = "运营平台实施地id")
    private Long yyCustomerId;
}
