package com.msun.csm.dao.entity.proj.extend;

import static com.msun.csm.common.staticvariable.StaticPara.DEFAULT_LONG;

import java.util.Date;

import com.msun.csm.dao.entity.proj.ProjContractCustomInfo;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.model.yunying.SyncContractDTO;
import com.msun.csm.util.SnowFlakeUtil;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/28
 */
@Data
@AllArgsConstructor
public class ProjContractCustomInfoExtend extends ProjContractCustomInfo {

    /**
     * 构造方法
     *
     * @param leaderId
     * @param now
     * @param syncContractDTO
     */
    public ProjContractCustomInfoExtend(Long leaderId, Date now, SyncContractDTO syncContractDTO) {
        this.setYyPartaId(syncContractDTO.getPrincipalCustomer().getCustomerId().longValue());
        this.setContractCustomName(syncContractDTO.getPrincipalCustomer().getCustomerName());
        this.setContractCustomSalepersonId(syncContractDTO.getSaleUserId().longValue());
        this.setContractCustomSaleteamId(syncContractDTO.getSaleOrgId().longValue());
        this.setContractCustomSaleprovinceId(syncContractDTO.getPrincipalCustomer().getSaleProvince() == null
                ? -1L : syncContractDTO.getPrincipalCustomer().getSaleProvince().longValue());
        this.setContractCustomSalecenterId(syncContractDTO.getCustomerInfo().getSaleCenterId());
        this.setCreaterId(leaderId);
        this.setUpdaterId(leaderId);
        this.setCreateTime(now);
        this.setUpdateTime(now);
        this.setIsDeleted(0);
    }

    /**
     * 构造方法
     *
     * @param now
     * @param customInfo
     */
    public ProjContractCustomInfoExtend(Date now, ProjCustomInfo customInfo) {
        this.setContractCustomInfoId(SnowFlakeUtil.getId());
        this.setYyPartaId(customInfo.getYyCustomerId());
        this.setContractCustomName(customInfo.getCustomName());
        this.setContractCustomSaleprovinceId(DEFAULT_LONG);
        this.setContractCustomSalecenterId(DEFAULT_LONG);
        this.setCreaterId(DEFAULT_LONG);
        this.setUpdaterId(DEFAULT_LONG);
        this.setCreateTime(now);
        this.setUpdateTime(now);
        this.setIsDeleted(0);
    }
}
