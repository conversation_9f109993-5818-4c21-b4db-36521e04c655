package com.msun.csm.dao.entity.dict;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/11/14
 */

/**
 * 小硬件字典表
 */
@ApiModel(description = "小硬件字典表")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class DictHardwareInfo {
    /**
     * 小硬件字典ID
     */
    @ApiModelProperty(value = "小硬件字典ID")
    @TableId (type = IdType.INPUT)
    private Long hardwareInfoId;

    /**
     * 小硬件名称
     */
    @ApiModelProperty(value = "小硬件名称")
    private String hardwareName;

    /**
     * 配置要求
     */
    @ApiModelProperty(value = "配置要求")
    private String configRequire;

    /**
     * 是否通用：0否1是
     */
    @ApiModelProperty(value = "是否通用：0否1是")
    private Integer commonFlag;

    /**
     * 是否需要对接：0否1是
     */
    @ApiModelProperty(value = "是否需要对接：0否1是")
    private Integer jointFlag;

    /**
     * 使用科室范围
     */
    @ApiModelProperty(value = "使用科室范围")
    private String useDept;

    /**
     * 逻辑删除【0：否；1：是】
     */
    @ApiModelProperty(value = "逻辑删除【0：否；1：是】")
    private Integer isDeleted;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private Long createrId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新人id
     */
    @ApiModelProperty(value = "更新人id")
    private Long updaterId;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
