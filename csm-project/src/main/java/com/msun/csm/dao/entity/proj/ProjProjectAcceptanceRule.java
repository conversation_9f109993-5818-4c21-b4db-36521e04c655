package com.msun.csm.dao.entity.proj;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 项目验收规则记录表
 *
 * @TableName proj_project_acceptance_rule
 */
@TableName (value = "csm.proj_project_acceptance_rule")
@Data
public class ProjProjectAcceptanceRule extends BasePO implements Serializable {
    /**
     * 主键
     */
    @TableId
    @ApiModelProperty ("主键")
    private Long projectAcceptanceRuleId;

    /**
     * 项目信息id
     */
    @ApiModelProperty ("项目信息id")
    private Long projectInfoId;

    /**
     * 项目规则编码
     */
    @ApiModelProperty ("项目规则编码")
    private String projectRuleCode;

    /**
     * 项目规则内容
     */
    @ApiModelProperty ("项目规则内容")
    private String projectRuleContent;

    /**
     * 验证方式：no 不验证；upload必须上传文件；prompt仅提示必须满足
     */
    private String verityWay;

    /**
     * 是否必须此条件：0.否；1.是
     */
    @ApiModelProperty ("是否必须此条件")
    private Integer requiredFlag;

    /**
     * 是否有模版文件：0.否；1.是
     */
    @ApiModelProperty ("是否有模版文件")
    private Integer templateFlag;

    /**
     * 模版文件对应系统文件表文件编码
     */
    @ApiModelProperty ("模版文件对应系统文件表文件编码")
    private String templateFileCode;

    /**
     * 项目验收表主键
     */
    @ApiModelProperty ("项目验收表主键")
    private Long projectAcceptanceId;

    /**
     * 项目文件管理表主键
     */
    @ApiModelProperty ("项目文件管理表主键")
    private Long projectFileId;

    /**
     * 排序号
     */
    @ApiModelProperty ("排序号")
    private Integer orderNo;

}
