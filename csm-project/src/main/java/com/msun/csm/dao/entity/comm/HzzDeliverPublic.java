package com.msun.csm.dao.entity.comm;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * 交付平台公众号信息表
 */
@Data
@TableName (value = "hzz_deliver_public", schema = "comm")
public class HzzDeliverPublic {

    @TableField ("id")
    private String id;
    /**
     * 交付主键
     */
    @TableField ("deliver_id")
    private String deliverId;
    /**
     * 公众号appid
     */
    @TableField ("public_app_id")
    private String publicAppId;
    /**
     * 公众号secret
     */
    @TableField ("public_app_secret")
    private String publicAppSecret;
    /**
     * 公众号类型
     */
    @TableField ("public_type")
    private Integer publicType;
    /**
     * 公众号编码
     */
    @TableField ("public_code")
    private String publicCode;
    /**
     * 公众号名称
     */
    @TableField ("public_name")
    private String publicName;
    /**
     * 公众号菜单
     */
    @TableField ("public_menu")
    private String publicMenu;
    /**
     * 初始化功能编码,逗号分割
     */
    @TableField ("init_function")
    private String initFunction;

    @TableField (value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    @TableField (value = "update_time", fill = FieldFill.UPDATE)
    @JsonFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
