package com.msun.csm.dao.entity.proj;

import lombok.Data;

@Data
public class GetBoardRecordParam {

    /**
     * 客户名称/工单号
     */
    private String key;

    /**
     * 项目状态 1已派工、2已调研、3已入驻、4、准备完成、5已上线、6已验收、7已启动 、8 申请验收
     */
    private Integer projectDeliverStatus;

    /**
     * 计划上线时间查询的开始时间
     */
    private String startTime;

    /**
     * 计划上线时间查询的结束时间
     */
    private String endTime;

}
