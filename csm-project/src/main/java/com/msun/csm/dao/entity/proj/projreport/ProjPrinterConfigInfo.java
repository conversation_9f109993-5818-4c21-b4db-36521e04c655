package com.msun.csm.dao.entity.proj.projreport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * 打印机信息
 *
 * <AUTHOR>
 * @date 2025年05月09日 08:43
 */
@Data
@ApiModel(description = "打印机信息")
public class ProjPrinterConfigInfo {

    @ApiModelProperty(value = "打印机id")
    @TableId(type = IdType.INPUT)
    private Long id;

    /**
     * 医院id
     */
    @ApiModelProperty(value = "医院id")
    private Long hospitalId;
    /**
     * mac地址
     */
    @NotBlank(message = "mac地址不能为空")
    @ApiModelProperty(value = "mac地址")
    private String mac;
    /**
     * ip地址
     */
    @NotBlank(message = "ip地址不能为空")
    @ApiModelProperty(value = "ip地址")
    private String ipAddress;
    /**
     * 打印机名称
     */
    @NotBlank(message = "打印机名称不能为空")
    @ApiModelProperty(value = "打印机名称")
    private String printerName;
    /**
     * 打印机状态
     */
    @ApiModelProperty(value = "打印机状态")
    private String printerStatus;
    /**
     * 打印机驱动名称
     */
    @NotBlank(message = "打印机驱动名称不能为空")
    @ApiModelProperty(value = "打印机驱动名称")
    private String printerDriver;
    /**
     * 如果是共享打印机，则是来源的ip地址；如果不是共享打印机，则是空字符串
     */
    @ApiModelProperty(value = "如果是共享打印机，则是来源的ip地址；如果不是共享打印机，则是空字符串\n")
    private String sharePrinterInfo;
    /**
     * 是否是共享打印机
     */
    @ApiModelProperty(value = "是否是共享打印机")
    private String isShare;
    /**
     * 纸张类型名称
     */
    @ApiModelProperty(value = "纸张类型名称")
    private String paperSize;
    /**
     * 纸张宽高
     */
    @ApiModelProperty(value = "纸张宽高")
    private String paperWidth;
    /**
     * 纸张高
     */
    @ApiModelProperty(value = "纸张高")
    private String paperHeight;
    /**
     * 横纵向
     */
    @ApiModelProperty(value = "横纵向")
    private String orientation;
    /**
     * 打印机分辨率
     */
    @ApiModelProperty(value = "打印机分辨率")
    private String printQuality;
    /**
     * 打印机驱动力里的私有数据（十六进制字符串）（未成功破解）
     */
    @ApiModelProperty(value = "打印机驱动力里的私有数据（十六进制字符串）（未成功破解）")
    private String privateDataHex;
    /**
     * 可打印区域宽度
     */
    @ApiModelProperty(value = "可打印区域宽度")
    private String physicalWidth;
    /**
     * 可打印区域高度
     */
    @ApiModelProperty(value = "可打印区域高度")
    private String physicalHeight;
    /**
     * 物理X偏移
     */
    @ApiModelProperty(value = "物理X偏移")
    private String physicalOffsetX;
    /**
     * 物理Y偏移
     */
    @ApiModelProperty(value = "物理Y偏移")
    private String physicalOffsetY;
    /**
     * 物理X分辨率
     */
    @ApiModelProperty(value = "物理X分辨率")
    private String physicalDpiX;
    /**
     * 物理Y分辨率
     */
    @ApiModelProperty(value = "物理Y分辨率")
    private String physicalDpiY;
    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String deptName;
    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    private Long deptId;
}
