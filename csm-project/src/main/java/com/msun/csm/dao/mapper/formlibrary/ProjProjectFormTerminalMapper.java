package com.msun.csm.dao.mapper.formlibrary;


import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.proj.projreport.ProjProjectFormTerminal;
import com.msun.csm.model.req.projreport.ProjSurveyReportDetailReq;
import com.msun.csm.model.resp.projreport.ProjProjectFormTerminalResp;

/**
 * <AUTHOR>
 * @description 针对表【ProjProjectFormTerminal(打印报表查询明细)】的数据库操作Mapper
 * @createDate 2024-10-16 16:14:27
 * @Entity generator.domain.DictFormLibrary
 */
@Mapper
public interface ProjProjectFormTerminalMapper extends BaseMapper<ProjProjectFormTerminal> {

    /**
     * 查询数据明细
     * @param projSurveyReportReq
     * @return
     */
    List<ProjProjectFormTerminalResp> selectListByParamer(ProjSurveyReportDetailReq projSurveyReportReq);
}




