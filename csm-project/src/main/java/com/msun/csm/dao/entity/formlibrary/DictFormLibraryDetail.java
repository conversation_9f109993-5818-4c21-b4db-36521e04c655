package com.msun.csm.dao.entity.formlibrary;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 字典明细表
 *
 * @TableName dict_form_library_detail
 */
@TableName(value = "dict_form_library_detail")
@Data
public class DictFormLibraryDetail implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 字典明细表主键
     */
    @TableField(value = "form_library_detail_id")
    private Long formLibraryDetailId;
    /**
     * 字典主表（dict_form_library）主键
     */
    @TableField(value = "form_library_id")
    private Long formLibraryId;
    /**
     * 明细表数据项code
     */
    @TableField(value = "form_library_detail_code")
    private String formLibraryDetailCode;
    /**
     * 明细表数据项名称
     */
    @TableField(value = "form_library_detail_name")
    private String formLibraryDetailName;
    /**
     * 明细表数据项说明
     */
    @TableField(value = "remark")
    private String remark;
    /**
     * 数据项排序
     */
    @TableField(value = "order_no")
    private Integer orderNo;
    /**
     * 机构id
     */
    @TableField(value = "his_org_id")
    private Long hisOrgId;
    /**
     * 创建人id
     */
    @TableField(value = "his_creater_id")
    private Long hisCreaterId;
    /**
     * 创建人名称
     */
    @TableField(value = "his_creater_name")
    private String hisCreaterName;
    /**
     * 创建时间
     */
    @TableField(value = "his_create_time")
    private LocalDateTime hisCreateTime;
    /**
     * 修改人id
     */
    @TableField(value = "his_updater_id")
    private Long hisUpdaterId;
    /**
     * 修改人名称
     */
    @TableField(value = "his_updater_name")
    private String hisUpdaterName;
    /**
     * 修改时间
     */
    @TableField(value = "his_update_time")
    private LocalDateTime hisUpdateTime;
    /**
     * 乐观锁标志
     */
    @TableField(value = "version")
    private Integer version;

}