package com.msun.csm.dao.entity.proj;

import java.util.List;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessScoreRecordVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 产品ID
     */
    private Long yyProductId;

    /**
     * 产品名称
     */
    private String yyProductName;

    /**
     * 项目阶段编码
     */
    private String projectStageCode;

    /**
     * 项目阶段名称
     */
    private String projectStageName;

    /**
     * 扣分类型字典表编码
     */
    private String deductionType;

    /**
     * 扣分类型描述
     */
    private String deductionTypeDesc;

    /**
     * 扣分明细数据
     */
    private String itemDetail;

    /**
     * 项目阶段完成时间
     */
    private String projectStageCompleteTime;

    /**
     * 新增时间
     */
    private String itemAddTime;

    /**
     * 附件
     */
    private List<AttachmentInfoVO> attachmentInfoList;

    /**
     * 预估扣分
     */
    private String estimatedDeduction;

    /**
     * 实际扣分
     */
    private String practicalDeduction;

    /**
     * 备注
     */
    private String remark;

    //---------------------------

    /**
     * 客户ID
     */
    private Long customInfoId;

    /**
     * 项目ID
     */
    private Long projectInfoId;

    /**
     * 菜单编码
     */
    private String menuCode;

    /**
     * 是否只需要一次验收：true-只需要一次验收；false-需要两次验收
     */
    @NotNull(message = "参数【onlyOneCheckFlag】不可为null")
    private Boolean onlyOneCheckFlag;

    /**
     * 当前验收次数：1-第一次验收；2-第二次验收
     */
    @NotNull(message = "参数【currentAcceptanceTimes】不可为null")
    private Integer currentAcceptanceTimes;

    /**
     * 是否展示修改和删除按钮。true-展示；false-不展示
     */
    private Boolean showUpdateAndDelete;


}
