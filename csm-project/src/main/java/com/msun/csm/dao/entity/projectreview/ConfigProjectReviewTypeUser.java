package com.msun.csm.dao.entity.projectreview;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

/**
 * 项目审核类型对应人员配置表
 *
 * <AUTHOR>
 * @TableName config_project_review_type_user
 */
@TableName(value = "config_project_review_type_user")
@Data
public class ConfigProjectReviewTypeUser extends BasePO {
    /**
     * 主键
     */
    @TableId
    private Long projectReviewUserId;

    /**
     * 项目审核类型字典表dict_project_review_type主键
     */
    private Long projectReviewTypeId;

    /**
     * 审核方式id 取字典表
     */
    private Long reviewMethodId;

    /**
     * 服务团队/部门 部门id
     */
    private Long reviewDeptId;

    /**
     * 人员id多选，sys_user表sys_user_id
     */
    private String reviewUserId;
}