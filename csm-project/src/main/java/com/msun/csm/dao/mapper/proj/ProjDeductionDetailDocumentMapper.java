package com.msun.csm.dao.mapper.proj;


import java.math.BigDecimal;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.proj.DocumentScoreRecordPO;
import com.msun.csm.dao.entity.proj.ProjDeductionDetailDocument;

/**
 * 项目文档扣分
 */
public interface ProjDeductionDetailDocumentMapper extends BaseMapper<ProjDeductionDetailDocument> {

    List<DocumentScoreRecordPO> getDocumentScoreRecord(@Param("projectInfoId") Long projectInfoId);

    int updatePracticalDeductionOrRemarkById(@Param("id") Long id, @Param("practicalDeduction") BigDecimal practicalDeduction, @Param("remark") String remark, @Param("deductionType") String deductionType);

}
