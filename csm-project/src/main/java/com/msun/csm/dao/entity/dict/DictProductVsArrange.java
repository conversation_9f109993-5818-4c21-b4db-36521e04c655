package com.msun.csm.dao.entity.dict;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/24
 */

/**
 * 工单产品与部署产品对照信息
 */
@ApiModel (description = "工单产品与部署产品对照信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DictProductVsArrange implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 工单产品与部署产品对照关系ID
     */
    @ApiModelProperty (value = "工单产品与部署产品对照关系ID")
    @TableId(type = IdType.INPUT)
    private Long productVsArrangeId;
    /**
     * 工单产品字典ID
     */
    @ApiModelProperty (value = "工单产品字典ID")
    private Long orderProductId;
    /**
     * 部署产品字典ID
     */
    @ApiModelProperty (value = "部署产品字典ID")
    private Long arrangeProductId;
    /**
     * 逻辑删除【0：否；1：是】
     */
    @ApiModelProperty (value = "逻辑删除【0：否；1：是】")
    private Integer isDeleted;
    /**
     * 创建人id
     */
    @ApiModelProperty (value = "创建人id")
    private Long createrId;
    /**
     * 创建时间
     */
    @ApiModelProperty (value = "创建时间")
    private Date createTime;
    /**
     * 更新人id
     */
    @ApiModelProperty (value = "更新人id")
    private Long updaterId;
    /**
     * 更新时间
     */
    @ApiModelProperty (value = "更新时间")
    private Date updateTime;
}
