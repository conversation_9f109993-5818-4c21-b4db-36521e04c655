package com.msun.csm.dao.mapper.oas;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Collection;
import java.util.regex.Pattern;

import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.logging.Log;
import org.apache.ibatis.logging.LogFactory;
import org.springframework.aop.support.AopUtils;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.msun.csm.dao.entity.oas.BasePO;

/**
 *
 * @param <T>
 */
public interface MsunBaseMapper<T extends BasePO> extends BaseMapper<T> {

    Log LOG = LogFactory.getLog(MsunBaseMapper.class);
    Pattern PATTERN = Pattern.compile("[A-Z]");

    default int save(T entity) {
//        entity.init();
        return this.insert(entity);
    }

    @Transactional(rollbackFor = {Exception.class})
    default void save(Collection<T> entitys) {
        Assert.notEmpty(entitys, "error: entityList must not be empty");
//        entitys.forEach(BasePO::init);
        String sqlStatement = this.sqlStatement(SqlMethod.INSERT_ONE);
        SqlHelper.executeBatch(this.currentModelClass(), LOG, entitys, 50, (sqlSession, entity) -> {
            sqlSession.insert(sqlStatement, entity);
        });
    }

    @Transactional(rollbackFor = {Exception.class})
    default int update(Collection<T> entitys) {
        Assert.notEmpty(entitys, "error: entityList must not be empty");
//        entitys.forEach(BasePO::update);
        String sqlStatement = this.sqlStatement(SqlMethod.UPDATE_BY_ID);
        SqlHelper.executeBatch(this.currentModelClass(), LOG, entitys, 50, (sqlSession, entity) -> {
            MapperMethod.ParamMap<T> param = new MapperMethod.ParamMap();
            param.put("et", entity);
            sqlSession.update(sqlStatement, param);
        });
        return entitys.size();
    }
//    default int delete(List<T> entities) {
//        AtomicInteger result = new AtomicInteger();
//        entities.forEach((entity) -> {
//            result.addAndGet(this.delete(entity));
//        });
//        return result.get();
//    }

    default String sqlStatement(SqlMethod sqlMethod) {
        return SqlHelper.getSqlStatement(this.currentMapperClass(), sqlMethod);
    }

    default Class<T> currentMapperClass() {
        Class<?> clazz = AopUtils.getTargetClass(this);
        Type[] genericInterfaces = clazz.getGenericInterfaces();
        return (Class) genericInterfaces[0];
    }

    default Class<T> currentModelClass() {
        Type[] genericInterfaces = AopUtils.getTargetClass(this).getGenericInterfaces();
        Class mapperClass = (Class) genericInterfaces[0];
        return (Class) ((ParameterizedType) mapperClass.getGenericInterfaces()[0]).getActualTypeArguments()[0];
    }

    default T getById(Long id) {
        return this.selectById(id);
    }
//    default T getInstance(Long id) {
//        BasePO instance = null;
//
//        try {
//            Type[] genericInterfaces = AopUtils.getTargetClass(this).getGenericInterfaces();
//            Class mapperClass = (Class)genericInterfaces[0];
//            Class cls = (Class)((ParameterizedType)mapperClass.getGenericInterfaces()[0]).getActualTypeArguments()[0];
//            instance = (BasePO)cls.newInstance();
//            Field[] fields = cls.getDeclaredFields();
//            Field[] var7 = fields;
//            int var8 = fields.length;
//
//            for(int var9 = 0; var9 < var8; ++var9) {
//                Field field = var7[var9];
//                if (field.getAnnotation(TableId.class) != null) {
//                    field.setAccessible(true);
//                    field.set(instance, id);
//                }
//            }
//
//            return instance;
//        } catch (Exception var11) {
//            throw BusinessException.build(CommonDbErrorCode.INSTANCE_PO_ERROR, new String[]{var11.getMessage()});
//        }
//    }

    default int update(T entity) {
//        entity.update();
        return this.updateById(entity);
    }
//    default int delete(T entity) {
//        MsunBaseMapper.Pk pk = this.getPk(entity);
//        Map<String, Object> map = new HashMap(4);
//        map.put("his_org_id", entity.getOrgId());
//        map.put(pk.getName(), pk.getValue());
//        return this.deleteByMap(map);
//    }
//    default int delete(Long id) {
//        return this.delete(this.getInstance(id));
//    }
//
//    default Wrapper<T> getWrapper(T entity) {
//        QueryWrapper<T> wrapper = new QueryWrapper();
//        wrapper.eq("his_org_id", this.getOrgId(entity));
//        MsunBaseMapper.Pk pk = this.getPk(entity);
//        wrapper.eq(pk.getName(), pk.getValue());
//        return wrapper;
//    }
//
//    default Long getOrgId(T entity) {
//        if (entity.getHisOrgId() != null) {
//            return entity.getHisOrgId();
//        } else {
//            LoginUserInfo user = LoginUserContext.getLoginUserInfo();
//            return user != null && user.getOrgId() != null ? user.getOrgId() : BasePO.DEFAULT_ID;
//        }
//    }
//
//    default MsunBaseMapper.Pk getPk(T entity) {
//        MsunBaseMapper.Pk pk = null;
//        Class<? extends BasePO> cls = entity.getClass();
//        Field[] fields = cls.getDeclaredFields();
//        Field[] var5 = fields;
//        int var6 = fields.length;
//
//        for(int var7 = 0; var7 < var6; ++var7) {
//            Field field = var5[var7];
//            if (field.getAnnotation(TableId.class) != null) {
//                field.setAccessible(true);
//                pk = new MsunBaseMapper.Pk();
//                Matcher matcher = PATTERN.matcher(field.getName());
//                StringBuffer pkName = new StringBuffer();
//
//                while(matcher.find()) {
//                    matcher.appendReplacement(pkName, "_" + matcher.group(0).toLowerCase());
//                }
//
//                matcher.appendTail(pkName);
//                pk.setName(pkName.toString());
//
//                try {
//                    pk.setValue((Long)field.get(entity));
//                } catch (IllegalAccessException var12) {
//                    throw BusinessException.build(CommonDbErrorCode.GET_PK_ERROR, new String[]{var12.getMessage()});
//                }
//            }
//        }
//
//        if (pk == null) {
//            throw BusinessException.build(CommonDbErrorCode.PK_SET_ERROR, new String[]{cls.getName()});
//        } else {
//            return pk;
//        }
//    }
//
//    public static class Pk {
//        private String name;
//        private Long value;
//
//        public Pk() {
//        }
//
//        public void setName(final String name) {
//            this.name = name;
//        }
//
//        public void setValue(final Long value) {
//            this.value = value;
//        }
//
//        public String getName() {
//            return this.name;
//        }
//
//        public Long getValue() {
//            return this.value;
//        }
//    }
}
