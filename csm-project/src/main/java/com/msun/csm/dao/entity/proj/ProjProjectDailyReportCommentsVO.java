package com.msun.csm.dao.entity.proj;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjProjectDailyReportCommentsVO {

    /**
     * 主键
     */
    @TableId
    private Long projProjectDailyReportCommentsId;

    /**
     * 项目日报记录表主键
     */
    private Long projectDailyReportRecordId;

    /**
     * 用户填写的评论内容
     */
    private String reportCommentsContent;

    /**
     * 评论人姓名
     */
    private String userName;

    /**
     * 评论人账号
     */
    private String userAccount;

    /**
     * 评论时间
     */
    private Date createTime;

}
