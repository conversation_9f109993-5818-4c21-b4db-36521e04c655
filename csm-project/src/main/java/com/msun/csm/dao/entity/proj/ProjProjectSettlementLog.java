package com.msun.csm.dao.entity.proj;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-06-18 08:29:00
 */

@Data
@TableName(schema = "csm")
public class ProjProjectSettlementLog extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @TableId("project_settlement_log_id")
    private Long projectSettlementLogId;

    /**
     * 项目入驻表主键id
     */
    @Schema(description = "项目入驻表主键id")
    private Long projectSettlementId;

    /**
     * 项目id
     */
    @Schema(description = "项目id")
    private Long projectInfoId;

    /**
     * 操作节点：提交入驻条件；销售申请入驻；方案分公司经理复核；运营部审核；PMO审核；确认入驻
     */
    @Schema(description = "操作节点：提交入驻条件；销售申请入驻；方案分公司经理复核；运营部审核；PMO审核；确认入驻")
    private Integer operateNode;

    /**
     * 节点状态（包含同意、驳回等）
     */
    @Schema(description = "节点状态（包含同意、驳回等）")
    private Integer settlementStatus;

    /**
     * 操作内容
     */
    @Schema(description = "操作内容")
    private String operateContent;

    /**
     * 操作人id
     */
    @Schema(description = "操作人id")
    private Long operateUserId;

    /**
     * 操作人姓名
     */
    @Schema(description = "操作人姓名")
    private String operateUserName;

    /**
     * 操作时间
     */
    @Schema(description = "操作时间")
    private Date operateTime;

}
