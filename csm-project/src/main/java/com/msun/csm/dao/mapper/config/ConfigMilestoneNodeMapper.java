package com.msun.csm.dao.mapper.config;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.config.ConfigMilestoneNode;
import com.msun.csm.model.dto.ConfigMilestoneNodeSelectDTO;
import com.msun.csm.model.vo.ConfigMilestoneNodeVO;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/25
 */

@Mapper
public interface ConfigMilestoneNodeMapper extends BaseMapper<ConfigMilestoneNode> {

    /**
     * 查询里程碑节点列表数据
     * @param dto
     * @return
     */
    List<ConfigMilestoneNodeVO> selectMilestoneNodeList(ConfigMilestoneNodeSelectDTO dto);

}
