package com.msun.csm.dao.entity.dict;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;


/**
 * 项目验收评分规则字典表
 */
@Data
@TableName(schema = "csm", value = "dict_project_accept_rule")
public class DictProjectAcceptRule implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 删除标记，0-正常，1-已删除
     */
    private Integer isDelete;

    /**
     * 创建人
     */
    private Long createrId;

    /**
     * 更新人
     */
    private Long updaterId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 考核指标/分类名称（快速实施应用/云健康产品应用/满意度调查）
     */
    private String classificationName;

    /**
     * 考核指标/分类编码
     */
    private String classificationCode;

    /**
     * 上级考核指标/分类编码，没有上级考核指标时为null
     */
    private String parentCode;

    /**
     * 分数权重，一级指标时为总权重，下级指标时为分项权重，仅需一次验收时此权重即是验收分值
     */
    private Integer scoreWeight;

    /**
     * 首次验收分值
     */
    private Integer firstScore;

    /**
     * 最终验收分值
     */
    private Integer finalScore;

    /**
     * 评分标准
     */
    private String scoreStandard;

}
