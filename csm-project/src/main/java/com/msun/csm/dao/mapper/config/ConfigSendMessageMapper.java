package com.msun.csm.dao.mapper.config;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.sys.ConfigSendMessage;

/**
 * <AUTHOR>
 * @since 2024-05-07 04:52:00
 */

@Mapper
public interface ConfigSendMessageMapper extends BaseMapper<ConfigSendMessage> {

    ConfigSendMessage selectById(@Param ("id") long id);

    /**
     * 获取发送消息配置信息
     *
     * @param messageTypeId     消息类别ID
     * @param messageToCategory 消息发送对象分类：-1.系统指定人员；1.个人；2.角色；3.部门;4.项目经理
     * @return 发送消息配置信息
     */
    List<ConfigSendMessage> getByMessageTypeIdAndMessageToCategory(@Param("messageTypeId") long messageTypeId, @Param("messageToCategory") int messageToCategory);
}
