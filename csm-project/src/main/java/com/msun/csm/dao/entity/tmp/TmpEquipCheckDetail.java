package com.msun.csm.dao.entity.tmp;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/12/16/17:49
 */
@TableName(schema = "csm")
@Data
public class TmpEquipCheckDetail {

    @ApiModelProperty("主键id")
    @TableId(type = IdType.INPUT)
    private Long tmpEquipCheckVsLisId;

    @ApiModelProperty("检测进度")
    private String testProgress;

    @ApiModelProperty("检测明细")
    private String itemInfo;

    @ApiModelProperty("云健康设备id")
    private Long cloudEquipId;

    @ApiModelProperty("云健康医院id")
    private Long cloudHospitalId;

    @ApiModelProperty("运营产品id")
    private Long yyProductId;

    @ApiModelProperty("检测结果【1：检测通过；6:检测失败】")
    private Integer status;

    @ApiModelProperty("更新时间")
    private Date updateTime;
}
