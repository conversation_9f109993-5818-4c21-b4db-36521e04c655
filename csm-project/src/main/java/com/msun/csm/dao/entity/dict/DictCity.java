package com.msun.csm.dao.entity.dict;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/25
 */

/**
 * 城市字典
 */
@ApiModel (description = "城市字典")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName (schema = "csm")
public class DictCity implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty (value = "")
    @TableId (type = IdType.INPUT)
    private Long dictCityId;
    @ApiModelProperty (value = "")
    private String dictCityName;
    @ApiModelProperty (value = "")
    private String inputCode;
    @ApiModelProperty (value = "")
    private String fullCode;
    @ApiModelProperty (value = "")
    private Long orderNo;
    @ApiModelProperty (value = "")
    private String invalidFlag;
    @ApiModelProperty (value = "")
    private Long dictProvinceId;
    @ApiModelProperty (value = "")
    private String wbCode;
    @ApiModelProperty (value = "")
    private Long createrId;
    @ApiModelProperty (value = "")
    private Date createTime;
    @ApiModelProperty (value = "")
    private Long updaterId;
    @ApiModelProperty (value = "")
    private Date updateTime;
    @ApiModelProperty (value = "")
    private String cityCode;
}
