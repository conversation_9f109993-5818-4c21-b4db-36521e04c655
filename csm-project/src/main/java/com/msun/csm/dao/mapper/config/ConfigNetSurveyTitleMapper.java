package com.msun.csm.dao.mapper.config;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.msun.csm.dao.entity.config.ConfigNetSurveyTitle;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/23
 */

public interface ConfigNetSurveyTitleMapper {
    /**
     * delete by primary key
     *
     * @param netSurveyTitleId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long netSurveyTitleId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(ConfigNetSurveyTitle record);

    int insertOrUpdate(ConfigNetSurveyTitle record);

    int insertOrUpdateSelective(ConfigNetSurveyTitle record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(ConfigNetSurveyTitle record);

    /**
     * select by primary key
     *
     * @param netSurveyTitleId primary key
     * @return object by primary key
     */
    ConfigNetSurveyTitle selectByPrimaryKey(Long netSurveyTitleId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(ConfigNetSurveyTitle record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(ConfigNetSurveyTitle record);

    int updateBatch(@Param("list") List<ConfigNetSurveyTitle> list);

    int updateBatchSelective(@Param("list") List<ConfigNetSurveyTitle> list);

    int batchInsert(@Param("list") List<ConfigNetSurveyTitle> list);

    /**
     * 根据项目属性查询
     *
     * @return
     */
    List<ConfigNetSurveyTitle> selectByProject(@Param("projectInfo") ProjProjectInfo projectInfo,
                                               @Param("telesalesFlag") Integer telesalesFlag);
}
