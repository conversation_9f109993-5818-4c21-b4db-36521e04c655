package com.msun.csm.dao.entity.proj;

import java.util.Date;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetProjectDailyReportRecordParamPO {

    /**
     * 客户名称
     */
    private String customName;

    /**
     * 上线日期开始时间
     */
    private Date onlineTimeStart;

    /**
     * 上线日期结束时间
     */
    private Date onlineTimeEnd;

    /**
     * 日报发送时间开始时间
     */
    private Date reportSendTimeStart;

    /**
     * 日报发送时间结束时间
     */
    private Date reportSendTimeEnd;

    /**
     * 日报状态：0-未填写；1-草稿；2-已发送；3-已评论
     */
    private Integer reportStatus;

    /**
     * 项目阶段信息
     */
    private List<Integer> deliverStatusList;
}
