package com.msun.csm.dao.mapper.projectreview;


import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.dao.entity.projectreview.ConfigProjectReviewTypeUser;
import com.msun.csm.dao.entity.projectreview.DictReviewMethodType;
import com.msun.csm.model.dto.DirUserDTO;
import com.msun.csm.model.req.projectreview.ConfigProjectReviewTypeUserReq;
import com.msun.csm.model.req.projectreview.ConfigProjectReviewTypeUserSaveReq;
import com.msun.csm.model.req.projectreview.QueryInfoReq;
import com.msun.csm.model.resp.projectreview.ConfigProjectReviewTypeUserResp;
import com.msun.csm.model.resp.projectreview.UserModelResp;

/**
 * <AUTHOR>
 * @description 针对表【config_project_review_type_user(项目审核类型对应人员配置表)】的数据库操作Mapper
 * @createDate 2025-06-18 08:30:31
 * @Entity jiaofuceshi.domain.ConfigProjectReviewTypeUser
 */
public interface ConfigProjectReviewTypeUserMapper extends BaseMapper<ConfigProjectReviewTypeUser> {

    /**
     * 分页查询
     * @param dto
     * @return
     */
    List<ConfigProjectReviewTypeUserResp> findDataPage(ConfigProjectReviewTypeUserReq dto);

    /**
     * 查询是否有占用地方
     * @param dto
     * @return
     */
    Integer selectIfNotUseCount(ConfigProjectReviewTypeUserReq dto);

    /**
     *    新增 校验同一审核方式、类型下，是否已存在相同团队
     *    若为编辑操作，校验时需排除当前记录ID，避免误判重复。
     *
     * @param dto
     * @return
     */
    Integer selectListByParamer(ConfigProjectReviewTypeUserSaveReq dto);

    /**
     * 查询众阳下所有人员
     * @param dto
     * @return
     */
    List<BaseIdNameResp> getDirUserList(DirUserDTO dto);

    /**
     * 按照项目审核类型查询审核人集合
     * @param dto
     * @return
     */
    List<UserModelResp> findUserModel(QueryInfoReq dto);

    /**
     * 查询项目审核类型下，审核方式
     * @param dto
     * @return
     */
    List<DictReviewMethodType> selectListByDto(QueryInfoReq dto);
}




