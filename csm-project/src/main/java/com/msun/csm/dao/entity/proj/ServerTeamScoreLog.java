package com.msun.csm.dao.entity.proj;

import lombok.Data;

import java.time.Instant;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;


@Data
@TableName(schema = "csm", value = "server_team_score_log")
public class ServerTeamScoreLog {

    /**
     * 主键
     */
    @TableId
    private Long serverTeamScoreLogId;

    /**
     * 逻辑删除【0：否；1：是】
     */
    private Integer isDeleted;

    /**
     * 创建人id
     */
    private Long createrId;

    /**
     * 创建时间
     */
    private Instant createTime;

    /**
     * 修改人id
     */
    private Long updaterId;

    /**
     * 修改时间
     */
    private Instant updateTime;

    /**
     * 后端服务团队得分记录表主键
     */
    private Long serverTeamDeductionRecordId;

    /**
     * 审核状态:1-质管已发送，待后端确认、2-后端已确认、3-后端已驳回
     */
    private Integer operationStatus;

    /**
     * 操作内容
     */
    private String operationContent;

    /**
     * 操作备注
     */
    private String operationRemark;

}