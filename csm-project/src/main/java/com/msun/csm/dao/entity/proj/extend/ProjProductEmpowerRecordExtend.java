package com.msun.csm.dao.entity.proj.extend;

import java.util.Date;

import com.msun.csm.dao.entity.proj.ProjProductEmpowerRecord;
import com.msun.csm.model.dto.ProductIdContrastDTO;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/28
 */
@Data
@AllArgsConstructor
public class ProjProductEmpowerRecordExtend extends ProjProductEmpowerRecord {

    /**
     * 构造方法
     *
     * @param productIdContrast
     * @param deliverRecordId
     * @param customInfoId
     * @param projectInfoId
     * @param leaderId
     * @param now
     */
    public ProjProductEmpowerRecordExtend(ProductIdContrastDTO productIdContrast, Long deliverRecordId,
                                          Long customInfoId, Long projectInfoId, Long leaderId, Date now) {
        this.setProductEmpowerRecordId(deliverRecordId);
        this.setYyOrderProductId(productIdContrast.getOriginalProductId());
        this.setMsunHealthModule(productIdContrast.getMsunHealthModule());
        this.setMsunHealthModuleCode(productIdContrast.getMsunHealthModuleCode());
        this.setCustomInfoId(customInfoId);
        this.setProjectInfoId(projectInfoId);
        this.setCreaterId(leaderId);
        this.setUpdaterId(leaderId);
        this.setCreateTime(now);
        this.setUpdateTime(now);
        this.setIsDeleted(0);
    }
}
