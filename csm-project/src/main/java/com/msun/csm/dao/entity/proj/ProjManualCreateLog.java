package com.msun.csm.dao.entity.proj;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @classDesc: 功能描述:(项目手动创建操作日志实体类)
 * @author: 侯艳军
 * @date: 2024/10/29 13:35
 * @copyright 众阳健康
 */
@Data
public class ProjManualCreateLog {
    @ApiModelProperty("主键id")
    @TableId(type = IdType.INPUT)
    private Long projManualCreateLogId;

    /**
     * 合同客户Id
     */
    @ApiModelProperty("合同客户Id")
    private Long principalCustomerInfoId;

    /**
     * 合同客户名
     */
    @ApiModelProperty ("合同客户名")
    private String principalCustomerName;

    /**
     * 实时地客户Id
     */
    @ApiModelProperty("实时地客户Id")
    private Long customerInfoId;

    /**
     * 实时地客户名
     */
    @ApiModelProperty ("实时地客户名")
    private String customerName;

    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String projectName;

    /**
     * 项目类型
     */
    @ApiModelProperty("项目类型")
    private Integer projectType;

    /**
     * 项目类型名称
     */
    @ApiModelProperty("项目类型名称")
    private String projectTypeName;

    /**
     * 实施类型
     */
    @ApiModelProperty("实施类型")
    private Integer upgradationType;

    /**
     * 实施类型名称
     */
    @ApiModelProperty("实施类型名称")
    private String upgradationTypeName;

    /**
     * 实施团队Id
     */
    @ApiModelProperty("实施团队Id")
    private Long projectTeamId;

    /**
     * 实施团队名称
     */
    @ApiModelProperty("实施团队名称")
    private String projectTeamName;

    /**
     * 项目经理帐号
     */
    @ApiModelProperty("项目经理帐号")
    private String projectLeaderAccount;

    /**
     * 销售帐号
     */
    @ApiModelProperty("销售帐号")
    private String salesPersonAccount;

    /**
     * 工单产品Id集合(逗号拼接)
     */
    @ApiModelProperty("工单产品Id集合(逗号拼接)")
    private String orderProductIds;

    /**
     * 工单产品名称集合(逗号拼接)
     */
    @ApiModelProperty("工单产品名称集合(逗号拼接)")
    private String orderProductNames;

    /**
     * 调用老交付平台创建项目接口的请求参数
     */
    @ApiModelProperty("调用运营平台状态接口的请求参数")
    private String oldApiRequest;

    /**
     * 调用老交付平台创建项目接口的响应参数
     */
    @ApiModelProperty("调用运营平台状态接口的响应参数")
    private String oldApiResponse;

    /**
     * 操作人id
     */
    @ApiModelProperty("操作人id")
    private Long operaterUserId;

    /**
     * 操作人名称
     */
    @ApiModelProperty("操作人名称")
    private String operaterUserName;

    /**
     * 操作时间
     */
    @ApiModelProperty("操作时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operateTime;

    /**
     * 创建人员id
     */
    @ApiModelProperty("创建人员id")
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    private Long createrId;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人员id
     */
    @ApiModelProperty("更新人员id")
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    private Long updaterId;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
