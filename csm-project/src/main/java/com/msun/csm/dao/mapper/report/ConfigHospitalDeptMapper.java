package com.msun.csm.dao.mapper.report;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.dao.entity.report.statis.ConfigHospitalDeptEntity;

/**
 * 科室
 *
 * <AUTHOR>
 * @since 2024-09-27 15:27:17
 */
@Mapper
public interface ConfigHospitalDeptMapper extends BaseMapper<ConfigHospitalDeptEntity> {

    /**
     * 根据项目信息id查询
     * @param projectInfoId
     * @return
     */
    List<BaseIdNameResp> selectListByProjectInfoId(@Param("projectInfoId") Long projectInfoId);
}
