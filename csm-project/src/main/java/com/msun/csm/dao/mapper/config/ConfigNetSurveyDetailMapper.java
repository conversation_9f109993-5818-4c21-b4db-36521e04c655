package com.msun.csm.dao.mapper.config;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.msun.csm.dao.entity.config.ConfigNetSurveyDetail;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/21
 */

public interface ConfigNetSurveyDetailMapper {
    /**
     * delete by primary key
     *
     * @param netSurveyDetailId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long netSurveyDetailId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(ConfigNetSurveyDetail record);

    int insertOrUpdate(ConfigNetSurveyDetail record);

    int insertOrUpdateSelective(ConfigNetSurveyDetail record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(ConfigNetSurveyDetail record);

    /**
     * select by primary key
     *
     * @param netSurveyDetailId primary key
     * @return object by primary key
     */
    ConfigNetSurveyDetail selectByPrimaryKey(Long netSurveyDetailId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(ConfigNetSurveyDetail record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(ConfigNetSurveyDetail record);

    int updateBatch(@Param("list") List<ConfigNetSurveyDetail> list);

    int updateBatchSelective(@Param("list") List<ConfigNetSurveyDetail> list);

    int batchInsert(@Param("list") List<ConfigNetSurveyDetail> list);

    /**
     * 根据问卷标题编码查询问卷详情
     *
     * @param titleCodeList
     * @return
     */
    List<ConfigNetSurveyDetail> selectByTitleCode(@Param("titleCodeList") List<String> titleCodeList);
}
