package com.msun.csm.dao.entity.proj;

import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/26
 */


/**
 * 申请单
 *
 * <AUTHOR>
 */
@ApiModel(description = "申请单")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjApplyOrder extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 申请单号
     */
    @ApiModelProperty(value = "申请单号")
    private Long applyNum;
    /**
     * 申请单类型：1：首次环境申请、2：增加医院、3：增加产品
     */
    @ApiModelProperty(value = "申请单类型：1：首次环境申请、2：增加医院、3：增加产品")
    private Integer applyType;
    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;
    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    private String applicant;
    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;
    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private Long customInfoId;
    /**
     * 卫生院id
     */
    @ApiModelProperty(value = "卫生院id")
    private Long hospitalInfoId;
    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private String productIds;
    /**
     * 运维人员
     */
    @ApiModelProperty(value = "运维人员")
    private String operationPerson;
    /**
     * 验收人员
     */
    @ApiModelProperty(value = "验收人员")
    private String acceptancePerson;
    /**
     * 结果：  0：待审核、1：已审核、2：已驳回、3：环境部署中、4：环境部署完成、5：已交付、6：已撤销
     */
    @ApiModelProperty(value = "结果：  0：待审核、1：已审核、2：已驳回、3：环境部署中、4：环境部署完成、5：已交付、6：已撤销")
    private Integer resultType;
    /**
     * 预资源规划文件地址，仅限私有云
     */
    @ApiModelProperty(value = "预资源规划文件地址，仅限私有云")
    private String resourceFilePath;
    /**
     * 拒收原因
     */
    @ApiModelProperty(value = "拒收原因")
    private String refusedReason;
    /**
     * 驳回原因
     */
    @ApiModelProperty(value = "驳回原因")
    private String rejectReason;
    /**
     * 交付文件下载地址
     */
    @ApiModelProperty(value = "交付文件下载地址")
    private String resourceFileDeliveryPath;

    /**
     * 验收时间
     */
    @ApiModelProperty(value = "验收时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date acceptanceTime;

    /**
     * 客户云环境. 0 众阳云-需要有云资源派工单, 1 非众阳云
     */
    @ApiModelProperty(value = "客户云环境. 0 众阳云-需要有云资源派工单, 1 非众阳云")
    private Integer cloudEnv;

    /**
     * 申请的域名地址
     */
    @ApiModelProperty(value = "申请的域名地址")
    private String domainUrl;

    @ApiModelProperty(value = "首次部署节点: 1是 0否")
    private Integer firstDeployNode;

    @ApiModelProperty(value = "是否是本地机房 1是 0否")
    private Integer isLocalRoom;

    @ApiModelProperty("指定部署人")
    private String deployUserName;

    @ApiModelProperty("指定部署人id")
    @TableField(exist = false)
    private Long deployUserId;

    @ApiModelProperty("指定部署人电话")
    private String deployPhone;

    @ApiModelProperty("预警时间h")
    @TableField(exist = false)
    private Integer earlywarnTime;

    @ApiModelProperty("多长时间进行消息提醒")
    @TableField(exist = false)
    private Integer cron;
}
