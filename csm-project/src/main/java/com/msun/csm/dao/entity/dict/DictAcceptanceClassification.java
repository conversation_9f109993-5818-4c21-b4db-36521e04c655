package com.msun.csm.dao.entity.dict;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 运营平台产品字典
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(schema = "csm", value = "dict_acceptance_classification")
public class DictAcceptanceClassification extends BasePO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private Long dictAcceptanceClassificationId;

    /**
     * 排序号
     */
    private Integer sortNo;

    /**
     * 验收分类编码
     */
    private String acceptanceClassificationCode;

    /**
     * 验收分类名称
     */
    private String acceptanceClassificationName;

}
