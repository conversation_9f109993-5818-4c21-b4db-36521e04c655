package com.msun.csm.dao.entity.proj;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentScoreRecordPO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 项目阶段编码
     */
    private String projectStageCode;

    /**
     * 项目阶段名称
     */
    private String projectStageName;

    /**
     * 项目里程碑节点编码
     */
    private String milestoneNodeCode;

    /**
     * 项目里程碑节点名称
     */
    private String milestoneNodeName;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件地址
     */
    private String filePath;

    /**
     * 预估扣分
     */
    private Integer estimatedDeduction;

    /**
     * 实际扣分
     */
    private Integer practicalDeduction;

    /**
     * 备注
     */
    private String remark;

    //---------------------------

    /**
     * 项目ID
     */
    private Long projectInfoId;

    /**
     * 考核指标/分类编码（优先存储二级编码）
     */
    private String classificationCode;

    /**
     * 来源：首次验收(first)/最终验收(final)
     */
    private String source;


}
