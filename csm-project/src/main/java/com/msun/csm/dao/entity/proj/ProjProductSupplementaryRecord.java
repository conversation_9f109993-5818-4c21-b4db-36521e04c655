package com.msun.csm.dao.entity.proj;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 特批/工单产品补录记录表(ProjProductSupplementaryRecord)实体类
 *
 * <AUTHOR>
 * @since 2024-07-04 16:43:18
 */
@Data
@TableName(value = "proj_product_supplementary_record", schema = "csm")
public class ProjProductSupplementaryRecord {
    /**
     * 主键
     */
    @TableId(type = IdType.NONE)
    private Long productSupplementaryRecordId;
    /**
     * 创建人id
     */
    private Long createrId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新人id
     */
    private Long updaterId;
    /**
     * 创建时间
     */
    private Date updateTime;
    /**
     * 逻辑删除【0：否；1：是】
     */
    private Integer isDeleted;
    /**
     * 工单id
     */
    private Long orderInfoId;
    /**
     * 客户id
     */
    private Long customInfoId;
    /**
     * 特批类型：1:工单产品;2:特批产品;
     */
    private Integer specialApprovalType;
    /**
     * 购买模式：0:临时特批;1:买产品(永久);2:买服务(有时间限制);
     */
    private Integer purchaseMode;
    /**
     * 项目文件管理表id，多个文件以逗号分割
     */
    private String projectFileId;
    /**
     * 产品有效时间-开始时间
     */
    private Date effectiveStartTime;
    /**
     * 产品有效时间-结束时间
     */
    private Date effectiveCompTime;
    /**
     * 备注
     */
    private String remark;
}
