package com.msun.csm.dao.entity.proj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 客户与项目类型对照信息(PROJ_CUSTOM_VS_PROJECT_TYPE)
 *
 * <AUTHOR>
 * @version 1.0.0 2025-01-22
 */
@ApiModel(description = "实施地客户与项目类型对照信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm", value = "proj_custom_vs_project_type")
public class ProjCustomVsProjectTypeEntity extends BasePO implements java.io.Serializable {
    /**
     * 版本号
     */
    private static final long serialVersionUID = -7699113771850731260L;

    /**
     * 客户与项目类型对照信息ID
     */
    @ApiModelProperty(value = "客户与项目类型对照信息ID")
    @TableId(type = IdType.INPUT)
    private Long customVsProjectTypeId;

    /**
     * 项目类型【1 单体    2 区域】
     */
    @ApiModelProperty(value = "项目类型【1 单体    2 区域】")
    private Integer projectType;

    @ApiModelProperty(value = "研发平台标识）唯一id,域名占用id,首次提交可不传")
    private Long preSubmitId;

    /**
     * 实施地客户信息ID
     */
    @ApiModelProperty(value = "实施地客户信息ID")
    private Long customInfoId;

    /**
     * 云健康域名
     */
    @ApiModelProperty(value = "云健康域名")
    private String cloudDomain;

    /**
     * 分院卫生院数量
     */
    @ApiModelProperty(value = "分院卫生院数量")
    private Integer branchNum;

    /**
     * 监理标识
     */
    @ApiModelProperty(value = "监理标识")
    private Integer supervisorFlag;

    /**
     * 部署状态 0 待申请， 11域名已申请待部署，21 已部署
     */
    @ApiModelProperty(value = "部署状态 0 待申请， 11域名已申请待部署，21 已部署")
    private Integer deploymentStatus;

    /**
     * customLevel
     */
    @ApiModelProperty(value = "实施地客户等级  区县  市级  三甲  二甲ps:需要跟运营平台同步")
    private String customLevel;
}