package com.msun.csm.dao.mapper.report;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.proj.projreport.ConfigCustomBackendDetailLimit;

/**
 * 项目整体开启小前端大后端限制 明细
 *
 * <AUTHOR>
 * @since 2024-09-27 15:27:17
 */
@Mapper
public interface ConfigCustomBackendDetailLimitMapper extends BaseMapper<ConfigCustomBackendDetailLimit> {

    /**
     * 根据项目id修改
     *
     * @param projectInfoId
     * @param printOpenFlag
     * @return
     */
    int updateByProjectId(@Param("projectInfoId") Long projectInfoId, @Param("printOpenFlag") Integer printOpenFlag);

    /**
     * 获取小前端大后端限制明细
     *
     * @param projectInfoId 项目ID
     * @param openType      限制类型：
     *                      <p>1-医护打印验证流程</p>
     *                      <p>2-打印报表</p>
     *                      <p>3-云护理表单</p>
     *                      <p>4-手麻表单</p>
     *                      <p>5-重症表单</p>
     *                      <p>6-急诊表单</p>
     *                      <p>7-三方接口</p>
     *                      <p>8-医保接口</p>
     *                      <p>9-统计报表</p>
     *                      <p>10-开启产品业务调研后端审核</p>
     *                      <p>11-开启打印报表后端审核</p>
     *                      <p>12-开启表单后端审核</p>
     *                      <p>13-业务后端服务</p>
     *                      <p>14-数据后端服务</p>
     *                      <p>15-接口后端服务</p>
     * @return 小前端大后端限制明细
     */
    ConfigCustomBackendDetailLimit getCustomBackendDetailLimit(@Param("projectInfoId") Long projectInfoId, @Param("openType") Integer openType);
}
