package com.msun.csm.dao.entity.tmp;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/09/25/14:25
 */
@TableName (schema = "csm")
@Data
public class TmpOldInterfaceVsNew {

    @ApiModelProperty ("主键id")
    @TableId (type = IdType.INPUT)
    private Long tmpOldInterfaceVsNewId;

    @ApiModelProperty ("老系统的接口id")
    private Long oldInterfaceId;

    @ApiModelProperty ("新系统接口id")
    private Long newInterfaceId;

    @ApiModelProperty ("同步时间")
    private Date createTime;

    @ApiModelProperty ("文件是否已同步【0：否；1：是】")
    private Integer fileFlag;

    @ApiModelProperty ("更新时间")
    private Date updateTime;


}
