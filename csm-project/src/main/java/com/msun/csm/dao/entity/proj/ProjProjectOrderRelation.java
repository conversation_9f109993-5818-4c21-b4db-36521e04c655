package com.msun.csm.dao.entity.proj;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-06-19 05:38:43
 */

@Data
@TableName(schema = "csm")
@AllArgsConstructor
@NoArgsConstructor
public class ProjProjectOrderRelation extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @TableId("project_vs_order_id")
    private Long projectVsOrderId;

    /**
     * 项目信息id
     */
    @Schema(description = "项目信息id")
    private Long projectInfoId;

    /**
     * 运营工单id
     */
    @Schema(description = "运营工单id")
    private Long yyOrderId;

    /**
     * 交付工单类型：1、自研软件；2、硬件；3、耗材；4、接口；5、软件服务费；6、硬件服务费；7、容灾；8、外采软件；9、云资源
     */
    @Schema(description = "交付工单类型：1、自研软件；2、硬件；3、耗材；4、接口；5、软件服务费；6、硬件服务费；7、容灾；8、外采软件；9、云资源")
    private Integer deliveryOrderId;

    /**
     * 业务信息id，根据交付工单类型不同，关联不同的业务表主键id，如云资源工单对应客户云服务信息表主键
     */
    @Schema(description = "业务信息id，根据交付工单类型不同，关联不同的业务表主键id，如云资源工单对应客户云服务信息表主键")
    private Long bussinessInfoId;

    /**
     * 合同客户id
     */
    @Schema(description = "合同客户id")
    private Long contractCustomInfoId;

    /**
     * 实施地客户id
     */
    @Schema(description = "实施地客户id")
    private Long customInfoId;

}
