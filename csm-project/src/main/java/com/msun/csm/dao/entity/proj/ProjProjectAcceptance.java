package com.msun.csm.dao.entity.proj;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import lombok.Data;

/**
 * 项目验收表
 *
 * @TableName proj_project_acceptance
 */
@TableName(value = "csm.proj_project_acceptance")
@Data
public class ProjProjectAcceptance extends BasePO implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long projectAcceptanceId;

    /**
     * 运营平台交付工单id
     */
    private Long yyOrderId;

    /**
     * 项目id
     */
    private Long projectInfoId;

    /**
     * 期望验收时间
     */
    private Date expectedAcceptanceTime;

    /**
     * 外部验收类型：0.未签署；1.外部验收报告；2.上线确认单
     */
    private Integer externalAcceptanceType;

    /**
     * 外部验收时间
     */
    private Date externalAcceptanceTime;

    /**
     * 申请验收时间
     */
    private Date applyAcceptanceTime;

    /**
     * 申请状态：0.未申请；1.提交申请验收；5.申请驳回；11.申请已接收；21.验收不通过;25.验收通过
     */
    private Integer acceptanceStatus;

    /**
     * 验收人userid（运营平台id）
     */
    private Long accepterUserId;

    /**
     * 验收时间
     */
    private Date acceptanceTime;

    /**
     * 验收得分
     */
    private String acceptanceScore;

    /**
     * 备注
     */
    private String remark;

    /**
     * 验收次数
     */
    private Integer acceptanceTimes;

    /**
     * 验收次数 1 or 2
     */
    private Integer requiredAcceptanceTimes;

    /**
     * 当前是第几次 1 or 2
     */
    private Integer currentTimes;


}
