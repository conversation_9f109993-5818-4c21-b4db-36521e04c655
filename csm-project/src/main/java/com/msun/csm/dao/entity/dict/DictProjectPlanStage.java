package com.msun.csm.dao.entity.dict;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2025/1/3
 */

/**
 * 项目计划阶段字典表
 */
@ApiModel(description = "项目计划阶段字典表")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DictProjectPlanStage {
    /**
     * 主键-字典表
     */
    @ApiModelProperty(value = "主键-字典表")
    private Long projectPlanStageId;

    /**
     * 项目计划阶段code
     */
    @ApiModelProperty(value = "项目计划阶段code")
    private String projectPlanStageCode;

    /**
     * 项目计划阶段名称
     */
    @ApiModelProperty(value = "项目计划阶段名称")
    private String projectPlanStageName;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private Long createrId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人id
     */
    @ApiModelProperty(value = "修改人id")
    private Long updaterId;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 逻辑删除【0：否；1：是】
     */
    @ApiModelProperty(value = "逻辑删除【0：否；1：是】")
    private Integer isDeleted;
}
