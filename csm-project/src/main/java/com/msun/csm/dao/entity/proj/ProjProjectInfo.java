package com.msun.csm.dao.entity.proj;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/28
 */

/**
 * 项目信息
 */
@ApiModel(description = "项目信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
    public class ProjProjectInfo extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 项目信息ID
     */
    @ApiModelProperty(value = "项目信息ID")
    @TableId(type = IdType.INPUT)
    private Long projectInfoId;
    /**
     * 实施地客户信息ID
     */
    @ApiModelProperty(value = "实施地客户信息ID")
    private Long customInfoId;
    /**
     * 项目状态 1已派工、2已调研、3已入驻、4、准备完成、5已上线、6已验收、7已启动"
     */
    @ApiModelProperty(value = "项目状态 1已派工、2已调研、3已入驻、4、准备完成、5已上线、6已验收、7已启动")
    private Integer projectDeliverStatus;
    /**
     * 交付工单ID
     */
    @ApiModelProperty(value = "交付工单ID")
    private Long orderInfoId;
    /**
     * 项目经理ID
     */
    @ApiModelProperty(value = "项目经理ID")
    private Long projectLeaderId;
    /**
     * 项目实施团队ID
     */
    @ApiModelProperty(value = "项目实施团队ID")
    private Long projectTeamId;
    /**
     * 销售派工时间
     */
    @ApiModelProperty(value = "销售派工时间")
    private Date workTime;
    /**
     * 项目组接收时间
     */
    @ApiModelProperty(value = "项目组接收时间")
    private Date receiveTime;
    /**
     * 调研完成时间
     */
    @ApiModelProperty(value = "调研完成时间")
    private Date surveyCompleteTime;
    /**
     * 入驻时间
     */
    @ApiModelProperty(value = "入驻时间")
    private Date settleInTime;
    /**
     * 准备完成时间
     */
    @ApiModelProperty(value = "准备完成时间")
    private Date preCompleteTime;
    /**
     * 上线时间
     */
    @ApiModelProperty(value = "上线时间")
    private Date onlineTime;
    /**
     * 验收时间
     */
    @ApiModelProperty(value = "验收时间")
    private Date acceptTime;
    /**
     * 项目实施类型  1 老换新升级 2新客户上线
     */
    @ApiModelProperty(value = "项目实施类型  1 老换新升级 2 新客户上线")
    private Integer upgradationType;
    /**
     * 项目首期标识  产品包含云his  基层his,0-非首期项目，1-首期项目
     */
    @ApiModelProperty(value = "项目首期标识  产品包含云his  基层his")
    private Integer hisFlag;
    /**
     * 项目标准工期
     */
    @ApiModelProperty(value = "项目标准工期")
    private String standardDuration;
    /**
     * 工期减免天数
     */
    @ApiModelProperty(value = "工期减免天数")
    private String durationReduction;
    /**
     * 交付工单结算状态
     */
    @ApiModelProperty(value = "交付工单结算状态")
    private String settleStatus;
    /**
     * 交付工单结算比例
     */
    @ApiModelProperty(value = "交付工单结算比例")
    private String settleProportion;
    /**
     * 交付工单应结算总金额
     */
    @ApiModelProperty(value = "交付工单应结算总金额")
    private BigDecimal settleAmount;
    /**
     * 监理标识 0 -非监理项目 1-监理项目
     */
    @ApiModelProperty(value = "监理标识")
    private Integer supervisorFlag;
    /**
     * 项目初始化标识
     */
    @ApiModelProperty(value = "项目初始化标识")
    private Integer memberInitFlag;
    /**
     * 云健康点数
     */
    @ApiModelProperty(value = "云健康点数")
    private String msunHealthPoint;
    /**
     * 验收得分
     */
    @ApiModelProperty(value = "验收得分")
    private Integer acceptScore;
    /**
     * 项目类型 【1单体  2区域】
     */
    @ApiModelProperty(value = "项目类型 【1单体  2区域】")
    private Integer projectType;
    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    private String projectNumber;
    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 提交验收申请时间
     */
    @ApiModelProperty("提交验收申请时间")
    @DateTimeFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat (pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyAcceptTime;

    /**
     * 外部验收时间
     */
    @ApiModelProperty ("外部验收时间")
    @DateTimeFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat (pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date externalAcceptTime;

    /**
     * 上线步骤初始化标志【0：未进行初始化；1：已初始化完成】
     */
    @ApiModelProperty ("上线步骤初始化标志【0：未进行初始化；1：已初始化完成】")
    private Integer onlineStepFlag;

    /**
     * 运营平台提交验收申请时间
     */
    private Date yyApplyAcceptTime;

    /**
     * 项目计划初始化标志【0：未进行初始化；1：已初始化完成】
     */
    @ApiModelProperty ("项目计划初始化标志【0：未进行初始化；1：已初始化完成】")
    private Integer projectPlanFlag;

    /**
     * 开始管控时间
     */
    @ApiModelProperty("开始管控时间")
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date controlTime;

    /**
     * 计划上线时间
     */
    @ApiModelProperty(value = "计划上线时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date planOnlineTime;

    /**
     * 为啥不确定上线时间
     */
    @ApiModelProperty(value = "为啥不确定上线时间")
    private String whyUnknownOnlineTime;


}
