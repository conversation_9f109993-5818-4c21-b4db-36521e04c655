package com.msun.csm.dao.mapper.proj;

import java.util.List;

import javax.validation.constraints.NotNull;

import org.apache.ibatis.annotations.Param;

import com.msun.csm.dao.entity.proj.ProjNetSurveyResult;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/22
 */

public interface ProjNetSurveyResultMapper {
    /**
     * delete by primary key
     *
     * @param netSurveyResultId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long netSurveyResultId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(ProjNetSurveyResult record);

    int insertOrUpdate(ProjNetSurveyResult record);

    int insertOrUpdateSelective(ProjNetSurveyResult record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(ProjNetSurveyResult record);

    /**
     * select by primary key
     *
     * @param netSurveyResultId primary key
     * @return object by primary key
     */
    ProjNetSurveyResult selectByPrimaryKey(Long netSurveyResultId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(ProjNetSurveyResult record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(ProjNetSurveyResult record);

    int updateBatch(@Param("list") List<ProjNetSurveyResult> list);

    int updateBatchSelective(@Param("list") List<ProjNetSurveyResult> list);

    int batchInsert(@Param("list") List<ProjNetSurveyResult> list);

    /**
     * 根据项目id查询
     *
     * @param projectInfoId
     * @return
     */
    List<ProjNetSurveyResult> selectByProjectId(Long projectInfoId);

    /**
     * 根据项目id删除
     *
     * @param projectInfoId
     */
    int deleteByProjectId(@NotNull(message = "projectInfoId不能为空") Long projectInfoId);
}
