package com.msun.csm.dao.entity.proj;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @fileName:
 * @author:<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @updateBy:
 * @Date:Created in 9:39 2024/5/8
 * @remark:
 */

@ApiModel (description = "里程碑初始化组装参数")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UrlParam {

    /**
     * 项目Id
     */
    @ApiModelProperty ("项目Id")
    private Long projectId;
    /**
     * 实施地客户Id
     */
    @ApiModelProperty ("实施地客户Id")
    private Long customerId;
    /**
     * 实施地客户名称
     */
    @ApiModelProperty ("customerName")
    private String customerName;
    /**
     * 医院id
     */
    @ApiModelProperty ("医院id")
    private Long customerInfoId;
    /**
     * 登录人组织id
     */
    @ApiModelProperty ("登录人组织id")
    private Long orgId;
    /**
     * 当前登录人id
     */
    @ApiModelProperty ("当前登录人id")
    private Long userId;
    /**
     * 当前登录人姓名
     */
    @ApiModelProperty ("当前登录人姓名")
    private String userName;
    @ApiModelProperty ("产品网络")
    private String preProductNetwork;

    @ApiModelProperty ("申请表示")
    private Integer applyFlag;

    /**
     * 项目工单编号
     */
    private String projectNumber;
}
