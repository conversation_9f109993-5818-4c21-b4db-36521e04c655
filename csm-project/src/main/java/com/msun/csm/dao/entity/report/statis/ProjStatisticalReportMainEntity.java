package com.msun.csm.dao.entity.report.statis;

import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 统计报表主表(PROJ_STATISTICAL_REPORT_MAIN)
 *
 * <AUTHOR>
 * @version 1.0.0 2025-01-13
 */

@Data
@TableName(value = "PROJ_STATISTICAL_REPORT_MAIN", schema = "csm")
@ApiModel
public class ProjStatisticalReportMainEntity extends BasePO {
    /**
     * 版本号
     */
    private static final long serialVersionUID = -3970433806590806570L;

    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    @ApiModelProperty(value = "主键")
    private Long statisticalReportMainId;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private Long customInfoId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

    /**
     * 报表名称： 字符串
     */
    @ApiModelProperty(value = "报表名称")
    private String reportName;

    /**
     * "报表样式，存储到sys_file的主键，多个以','分割。
     * 通过file_path 获取到路径对应的图片"
     */
    @ApiModelProperty(value = "报表样式")
    private String reportStyle;

    /**
     * 制作方式： 下拉（当前4种）
     */
    @ApiModelProperty(value = "制作方式")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long productionMethodId;

    /**
     * "状态（枚举）：
     * 1待申请裁定、
     * 11裁定中、12裁定驳回、13裁定通过
     * 21制作中、
     * 22 制作完成
     * 31已下沉"
     * 41发布
     */
    @ApiModelProperty(value = "状态")
    private Integer reportStatus;

    /**
     * 使用科室 id： 关联 dict_hospital_dept 主键
     */
    @ApiModelProperty(value = "使用科室")
    private Long hospitalDeptId;

    /**
     * 使用科室名称 ：
     */
    @ApiModelProperty(value = "使用科室名称")
    private String hospitalDeptName;

    /**
     * 用途分类id 关联dict_report_purpose 主键
     */
    @ApiModelProperty(value = "用途分类id")
    private Long reportPurposeId;

    /**
     * 用途分类名称
     */
    @ApiModelProperty(value = "用途分类名称")
    private String reportPurposeName;

    /**
     * 使用频次id dict_report_frequency 主键
     */
    @ApiModelProperty(value = "使用频次id")
    private Long reportFrequencyId;

    /**
     * 使用频次名称
     */
    @ApiModelProperty(value = "使用频次名称")
    private String reportFrequencyName;

    /**
     * 上线必备（1是 0否） 默认1
     */
    @ApiModelProperty(value = "上线必备")
    private Integer onlineFlag;

    /**
     * 统计报表备注
     */
    @ApiModelProperty(value = "统计报表备注")
    private String remarks;

    /**
     * 调研人
     */
    @ApiModelProperty(value = "调研人")
    private Long surveyUserId;

    /**
     * 调研时间
     */
    @ApiModelProperty(value = "调研时间")
    private Timestamp surveyTime;

    /**
     * 裁定审核人
     */
    @ApiModelProperty(value = "裁定审核人")
    private Long auditUserId;

    /**
     * 裁定审核时间
     */
    @ApiModelProperty(value = "裁定审核时间")
    private Timestamp auditTime;

    /**
     * 分配负责人
     */
    @ApiModelProperty(value = "分配负责人")
    private Long allocateUserId;

    /**
     * 分配时间
     */
    @ApiModelProperty(value = "分配时间")
    private Timestamp allocateTime;

    /**
     * 计划完成时间
     */
    @ApiModelProperty(value = "计划完成时间")
    private Timestamp planFinishTime;

    /**
     * 下沉人（完成人）
     */
    @ApiModelProperty(value = "下沉人")
    private Long finishUserId;

    /**
     * 下沉时间（完成时间）
     */
    @ApiModelProperty(value = "下沉时间")
    private Timestamp finishTime;

    @ApiModelProperty(value = "统计报表主表id")
    private String reportMainId;
    @ApiModelProperty(value = "统计报表主表名称")
    private String reportMainName;

    @ApiModelProperty(value = "医院信息ID")
    private Long hospitalInfoId;

    @ApiModelProperty(value = "裁定指标集合name")
    private String reportTargets;


    @ApiModelProperty(value = "裁定指标集合code")
    private String reportTargetsCodes;

    @ApiModelProperty(value = "裁定指标备注")
    private String rulingMarks;

    @ApiModelProperty(value = "挂载路径")
    private String mountPath;

    @ApiModelProperty(value = "运维平台唯一标识")
    private String operationStatisticsReportId;

    @ApiModelProperty(value = "裁定产品名称")
    @TableField(exist = false)
    private String operationProductName;

    @ApiModelProperty(value = "裁定产品id")
    private Long operationProductId;
}