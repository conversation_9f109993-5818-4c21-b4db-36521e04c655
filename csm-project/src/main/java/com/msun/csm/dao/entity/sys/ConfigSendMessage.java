package com.msun.csm.dao.entity.sys;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-05-07 04:52:00
 */

@Data
@TableName(schema = "csm")
public class ConfigSendMessage extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId("id")
    private Long id;

    /**
     * 消息类别id
     */
    @Schema(description = "消息类别id")
    private Long messageTypeId;

    /**
     * 消息发送对象分类：-1.系统指定人员；1.个人；2.角色；3.部门;4.项目经理
     */
    @Schema(description = "消息发送对象分类：-1.系统指定人员；1.个人；2.角色；3.部门;4.项目经理")
    private int messageToCategory;

    /**
     * 消息发送对象id，如果按角色发送为角色id；如果按个人发送为userid；如果按部门发送为deptid
     */
    @Schema(description = "消息发送对象id，如果按角色发送为角色id；如果按个人发送为userid；如果按部门发送为deptid")
    private Long messageToId;

    /**
     * 发送给消息对象的数据范围：1.全部组织；2.发送对象人员对应组织的消息；3.发送对象人员对应组织及以下组织的消息
     */
    @Schema(
            description = "发送给消息对象的数据范围：1.全部组织；2.发送对象人员对应组织的消息；3.发送对象人员对应组织及以下组织的消息")
    private int messageToRange;


}
