package com.msun.csm.dao.mapper.proj;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.proj.productempower.ProjProductEmpowerAddRecord;

/**
 * <AUTHOR>
 * @since 2024-09-27 10:20:26
 */

@Mapper
public interface ProjProductEmpowerAddRecordMapper extends BaseMapper<ProjProductEmpowerAddRecord> {

    /**
     * 说明: 根据客户id修改客户信息id
     *
     * @param oldCustomInfoId
     * @param newCustomInfoId
     * @return
     */
    int updateByCustomInfoId(@Param("oldCustomInfoId") Long oldCustomInfoId,
                             @Param("newCustomInfoId") Long newCustomInfoId);

}
