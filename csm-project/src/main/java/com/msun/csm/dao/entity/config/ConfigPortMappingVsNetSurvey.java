package com.msun.csm.dao.entity.config;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/21
 */

/**
 * 网络端口映射配置表
 */
@ApiModel(description = "网络端口映射配置表 ")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConfigPortMappingVsNetSurvey {
    /**
     * 网络端口映射配置表
     */
    @ApiModelProperty(value = "网络端口映射配置表")
    private Long portMappingVsSurveyId;

    /**
     * 根据调研选项对应时,调研选项字典的编码
     */
    @ApiModelProperty(value = "根据调研选项对应时,调研选项字典的编码")
    private String netSurveyCode;

    /**
     * 根据部署产品对应时，部署产品的运营平台id
     */
    @ApiModelProperty(value = "根据部署产品对应时，部署产品的运营平台id")
    private Integer yyProductId;

    /**
     * 端口映射字典表主键id
     */
    @ApiModelProperty(value = "端口映射字典表主键id")
    private Long portMappingId;

    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer orderNo;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private Long createrId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改人id
     */
    @ApiModelProperty(value = "修改人id")
    private Long updaterId;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 逻辑删除【0：否；1：是】
     */
    @ApiModelProperty(value = "逻辑删除【0：否；1：是】")
    private Integer isDeleted;
}
