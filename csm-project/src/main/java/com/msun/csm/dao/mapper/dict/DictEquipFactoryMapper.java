package com.msun.csm.dao.mapper.dict;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.dict.DictEquipFactory;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/10
 */
@Mapper
public interface DictEquipFactoryMapper extends BaseMapper<DictEquipFactory> {
    /**
     * delete by primary key
     *
     * @param equipFactoryId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long equipFactoryId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(DictEquipFactory record);

    int insertOrUpdate(DictEquipFactory record);

    int insertOrUpdateSelective(DictEquipFactory record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(DictEquipFactory record);

    /**
     * select by primary key
     *
     * @param equipFactoryId primary key
     * @return object by primary key
     */
    DictEquipFactory selectByPrimaryKey(Long equipFactoryId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(DictEquipFactory record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(DictEquipFactory record);

    int updateBatch(List<DictEquipFactory> list);

    int updateBatchSelective(List<DictEquipFactory> list);

    int batchInsert(@Param("list") List<DictEquipFactory> list);
}
