package com.msun.csm.dao.entity.proj;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;


/**
 * 项目成员信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectMemberVO {
    /**
     * 项目成员信息ID
     */
    @TableId
    private Long projectMemberInfoId;
    /**
     * 项目信息ID
     */
    private Long projectInfoId;
    /**
     * 项目人员ID
     */
    private Long projectMemberId;
    /**
     * 项目人员名称
     */
    private String projectMemberName;
    /**
     * 项目人员团队ID
     */
    private Long projectTeamId;
    /**
     * 项目人员团队名称
     */
    private String projectTeamName;
    /**
     * 项目成员角色 不是sysRole
     */
    private Long projectMemberRoleId;

    /**
     * 逻辑删除【0：否；1：是】
     */
    private Integer isDeleted;

    /**
     * 创建人id
     */
    private Long createrId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人id
     */
    private Long updaterId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 项目成员角色code
     */
    private String projectRoleCode;

    /**
     * 项目成员角色名称
     */
    private String projectRoleName;

    /**
     * 角色类型：1-前端角色；2-后端角色
     */
    private Integer roleType;

    /**
     * 角色权限：normal-普通用户；frontManager-前端项目经理；backendManager-后端项目经理
     */
    private String rolePermission;
}
