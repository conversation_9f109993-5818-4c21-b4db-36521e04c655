package com.msun.csm.dao.mapper.dict;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.dict.DictInterface;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/9/12
 */

@Mapper
public interface DictInterfaceMapper extends BaseMapper<DictInterface> {
    /**
     * delete by primary key
     *
     * @param dictInterfaceId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long dictInterfaceId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(DictInterface record);

    int insertOrUpdate(DictInterface record);

    int insertOrUpdateSelective(DictInterface record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(DictInterface record);

    /**
     * select by primary key
     *
     * @param dictInterfaceId primary key
     * @return object by primary key
     */
    DictInterface selectByPrimaryKey(Long dictInterfaceId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(DictInterface record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(DictInterface record);

    int updateBatch(List<DictInterface> list);

    int updateBatchSelective(List<DictInterface> list);

    int batchInsert(@Param ("list") List<DictInterface> list);
}
