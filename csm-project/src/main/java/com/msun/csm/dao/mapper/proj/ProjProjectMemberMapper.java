package com.msun.csm.dao.mapper.proj;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.proj.ProjProjectMember;
import com.msun.csm.model.dto.ProjProjectMemberDTO;
import com.msun.csm.model.vo.ProjProjectMemberVO;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/26
 */
@Mapper
public interface ProjProjectMemberMapper extends BaseMapper<ProjProjectMember> {
    int deleteByPrimaryKey(Long projectMemberInfoId);

    int insert(ProjProjectMember record);

    int insertOrUpdate(ProjProjectMember record);

    int insertOrUpdateSelective(ProjProjectMember record);

    int insertSelective(ProjProjectMember record);

    ProjProjectMember selectByPrimaryKey(Long projectMemberInfoId);

    int updateByPrimaryKeySelective(ProjProjectMember record);

    int updateByPrimaryKey(ProjProjectMember record);

    int updateBatch(List<ProjProjectMember> list);

    int updateBatchSelective(List<ProjProjectMember> list);

    int batchInsert(@Param("list") List<ProjProjectMember> list);

    /**
     * 根据项目id查询项目成员信息
     *
     * @param projectIds 项目id
     * @return 项目成员信息列表
     * <AUTHOR>
     */
    List<ProjProjectMember> selectByProjectIds(@Param("projectIds") List<Long> projectIds);

    /**
     * 根据项目id查询项目成员信息
     *
     * @param dto
     * @return 项目成员信息列表
     * <AUTHOR>
     */
    List<ProjProjectMemberVO> selectMemberVO(ProjProjectMemberDTO dto);

    /**
     * 删除项目成员信息
     *
     * @param projectInfoId
     * @param userIds
     * @return
     */
    int deleteByParam(@Param("projectInfoId") Long projectInfoId, @Param("userIds") List<Long> userIds);

    void deleteByList(@Param("listMemberData") List<ProjProjectMember> listMemberData);

    /**
     * 根据项目id和角色id查询项目成员信息
     *
     * @param projectInfoId
     * @param roleId
     * @return
     */
    List<ProjProjectMember> selectByProjectIdAndRole(@Param("projectInfoId") Long projectInfoId, @Param("roleId") Long roleId);


    List<ProjProjectMember> queryBackendManager();

    ProjProjectMember selectByProjectIdAndMemberId(@Param("projectInfoId") Long projectInfoId, @Param("memberId") Long memberId);

    /**
     * 查询所有项目成员
     *
     * @param dto
     * @return
     */
    List<ProjProjectMemberVO> selectMemberVOAll(ProjProjectMemberDTO dto);

    /**
     * 根据项目id和用户id查询项目成员信息
     *
     * @param projectInfoId
     * @param sysUserId
     */
    void insertUserByYunwei(@Param("projectInfoId") Long projectInfoId, @Param("sysUserId") Long sysUserId);
}
