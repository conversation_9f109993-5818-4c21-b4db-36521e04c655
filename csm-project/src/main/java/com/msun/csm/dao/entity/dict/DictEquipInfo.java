package com.msun.csm.dao.entity.dict;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/10
 */

/**
 * 设备字典
 */
@ApiModel (description = "设备字典")
@Data
@TableName (schema = "csm")
public class DictEquipInfo extends BasePO {
    /**
     * 设备字典id
     */
    @ApiModelProperty (value = "设备字典id")
    @TableId (type = IdType.INPUT)
    private Long equipInfoId;

    /**
     * 设备型号名称
     */
    @ApiModelProperty (value = "设备型号名称")
    private String equipModelName;

    /**
     * 设备类型字典id
     */
    @ApiModelProperty (value = "设备类型字典id")
    private Long equipTypeId;

    /**
     * 设备分类字典id
     */
    @ApiModelProperty (value = "设备分类字典id")
    private Long equipClassId;

    /**
     * 设备厂商字典id
     */
    @ApiModelProperty (value = "设备厂商字典id")
    private Long equipFactoryId;

    /**
     * 是否需要申请制作标识【0：否；1：是】
     */
    @ApiModelProperty (value = "是否免调研【0：否；1：是】")
    private Integer noSurveyFlag;

    /**
     * 是否有仪器对接文档标识【0：否；1：是】
     */
    @ApiModelProperty (value = "是否有仪器对接文档标识【0：否；1：是】")
    private Integer equipDocFlag;

    /**
     * 设备文档id【sys_file表主键id】
     */
    @ApiModelProperty (value = "设备文档id【sys_file表主键id】")
    private Long equipDocId;

    @ApiModelProperty ("产品id")
    private Long yyProductId;
}
