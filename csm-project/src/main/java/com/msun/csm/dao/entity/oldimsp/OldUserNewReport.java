package com.msun.csm.dao.entity.oldimsp;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/07/09/10:38
 */
@Data
@TableName (schema = "platform", value = "sys_user_report")
public class OldUserNewReport {

    @ApiModelProperty ("人员id")
    private Long userId;

    @ApiModelProperty ("账号")
    private String account;

    @ApiModelProperty ("级别")
    private String level;

    @ApiModelProperty ("姓名")
    private String userName;

    private String yyId;
}
