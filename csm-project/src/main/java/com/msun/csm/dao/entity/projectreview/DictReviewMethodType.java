package com.msun.csm.dao.entity.projectreview;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

/**
 * 审核方式字典表
 *
 * <AUTHOR>
 * @TableName dict_review_method_type
 */
@TableName(value = "dict_review_method_type")
@Data
public class DictReviewMethodType extends BasePO {
    /**
     * 主键
     */
    @TableId
    private Long reviewMethodTypeId;

    /**
     * 审核模式编码
     */
    private String dictCode;

    /**
     * 审核模式名称
     */
    private String dictName;
}