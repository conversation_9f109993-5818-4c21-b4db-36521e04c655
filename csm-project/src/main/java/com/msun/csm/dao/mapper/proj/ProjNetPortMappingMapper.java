package com.msun.csm.dao.mapper.proj;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.msun.csm.dao.entity.proj.ProjNetPortMapping;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/21
 */

public interface ProjNetPortMappingMapper {
    /**
     * delete by primary key
     *
     * @param portMappingId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long portMappingId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(ProjNetPortMapping record);

    int insertOrUpdate(ProjNetPortMapping record);

    int insertOrUpdateSelective(ProjNetPortMapping record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(ProjNetPortMapping record);

    /**
     * select by primary key
     *
     * @param portMappingId primary key
     * @return object by primary key
     */
    ProjNetPortMapping selectByPrimaryKey(Long portMappingId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(ProjNetPortMapping record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(ProjNetPortMapping record);

    int updateBatch(@Param("list") List<ProjNetPortMapping> list);

    int updateBatchSelective(@Param("list") List<ProjNetPortMapping> list);

    int batchInsert(@Param("list") List<ProjNetPortMapping> list);

    /**
     * 根据调研详情编码查询端口映射信息
     *
     * @param netSurveyDetailCode
     * @param projectInfoId
     * @return
     */
    ProjNetPortMapping selectBySurveyDetailCode(@Param("netSurveyDetailCode") String netSurveyDetailCode,
                                                @Param("projectInfoId") Long projectInfoId);

    /**
     * 根据项目ID删除端口映射信息
     *
     * @param projectInfoId
     */
    int deleteByProjectId(@Param("projectInfoId") Long projectInfoId);

    /**
     * 根据项目ID和产品ID查询端口映射信息
     *
     * @param projectInfoId
     * @return
     */
    List<ProjNetPortMapping> selectByProjectInfoId(@Param("projectInfoId") Long projectInfoId);
}
