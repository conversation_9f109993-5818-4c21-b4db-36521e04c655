package com.msun.csm.dao.entity.report.statis;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 制作方式表(config_production_method)
 *
 * <AUTHOR>
 * @version 1.0.0 2025-01-13
 */
@Data
@TableName(value = "config_production_method", schema = "csm")
@ApiModel
public class ConfigProductionMethod extends BasePO {
    /**
     * 版本号
     */
    private static final long serialVersionUID = 8789076942895049510L;

    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    @ApiModelProperty(value = "主键")
    private Long productionMethodId;

    /**
     * 字典名称
     */
    private String productionMethodName;

    /**
     * 是否开启限制裁定
     */
    private int enableFlag;
    /**
     * 排序
     */
    private Integer orderNo;


}