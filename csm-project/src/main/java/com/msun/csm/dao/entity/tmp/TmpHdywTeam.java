package com.msun.csm.dao.entity.tmp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/11/15/9:59
 */
@Data
@TableName (schema = "csm")
public class TmpHdywTeam {

    @ApiModelProperty ("主键id")
    @TableId (type = IdType.INPUT)
    private Long hdywTeamId;

    @ApiModelProperty ("业务线  1报表，2三方接口")
    private Integer businessLine;

    @ApiModelProperty ("团队成员(运营平台ID)")
    private Long teamPeople;

    @ApiModelProperty ("是否负责人")
    private Integer isHead;

    @ApiModelProperty ("是否删除 1是 0否")
    @TableLogic
    private Integer isDeleted;
}
