package com.msun.csm.dao.entity.dict;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/25
 */

/**
 * 运营平台产品字典
 */
@ApiModel(description = "运营平台产品字典")
@Data
@TableName(schema = "csm")
public class DictProduct extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 产品字典ID
     */
    @ApiModelProperty(value = "产品字典ID")
    @TableId(type = IdType.INPUT)
    private Long productDictId;
    /**
     * 运营平台产品ID
     */
    @ApiModelProperty(value = "运营平台产品ID")
    private Long yyProductId;
    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;
    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    private String productType;
    /**
     * 产品所属团队ID
     */
    @ApiModelProperty(value = "产品所属团队ID")
    private Long productTeamId;
    /**
     * 产品经理运营平台ID
     */
    @ApiModelProperty(value = "产品经理运营平台ID")
    private Long productLeaderYyId;
    /**
     * 自研产品分类
     */
    @ApiModelProperty(value = "自研产品分类")
    private Integer productDevelopType;
    /**
     * 产品解决方案ID
     */
    @ApiModelProperty(value = "产品解决方案ID")
    private Long productResolveId;

    @ApiModelProperty("运营平台产品code")
    private String yyProductCode;

    @ApiModelProperty("运营平台产品全称")
    private String yyProductFullName;

    @ApiModelProperty("是否是云产品【0：否；1：是】")
    private Integer yyIsCloud;

    @ApiModelProperty("是否含有模块【0否；1是】")
    private Integer yyIsHaveModule;

    @ApiModelProperty("排序")
    private Long orderNo;

}
