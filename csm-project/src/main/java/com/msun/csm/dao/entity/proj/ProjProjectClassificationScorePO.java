package com.msun.csm.dao.entity.proj;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 项目验收评分规则字典表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjProjectClassificationScorePO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 考核指标/分类编码（优先存储二级编码）
     */
    private String classificationCode;

    /**
     * 考核指标/分类名称（快速实施应用/云健康产品应用/满意度调查）
     */
    private String classificationName;

    /**
     * 分数权重，一级指标时为总权重，下级指标时为分项权重，仅需一次验收时此权重即是验收分值
     */
    private Integer scoreWeight;

    /**
     * 上级考核指标/分类编码，没有上级考核指标时为null
     */
    private String parentCode;

    /**
     * 首次验收分值的满分
     */
    private Integer firstScore;

    /**
     * 最终验收分值的满分
     */
    private Integer finalScore;

    /**
     * 评分标准
     */
    private String scoreStandard;

    /**
     * 实际的扣分
     */
    private BigDecimal practicalDeduction;

    /**
     * 实际的得分
     */
    private BigDecimal score;

    /**
     * 根据权重分配后的总分值
     */
    private Integer totalScore;

    /**
     * 评分说明
     */
    private String remark;

}
