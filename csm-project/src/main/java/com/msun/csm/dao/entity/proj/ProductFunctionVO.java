package com.msun.csm.dao.entity.proj;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.msun.csm.common.enums.HospitalTypeEnum;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductFunctionVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 运营平台实施产品ID
     */
    private Long yyProductId;

    /**
     * 产品名称
     */
    private String yyProductName;

    /**
     * 功能检测点
     */
    private String functionName;

    /**
     * 功能检测说明
     */
    private String functionDesc;

    /**
     * 扣分分值
     */
    private BigDecimal markingStandard;

    /**
     * 功能检测脚本
     */
    private String checkSql;

    /**
     * 启用状态：0-启用；1-禁用
     */
    private Integer isDelete;


    /**
     * 人民医院可用：0-不可用；1-可用
     */
    private Integer peoplesHospitalFlag;

    /**
     * 中医院可用：0-不可用；1-可用
     */
    private Integer chineseHospitalFlag;

    /**
     * 妇幼保健院可用：0-不可用；1-可用
     */
    private Integer maternalChildHospitalFlag;

    /**
     * 肿瘤医院可用：0-不可用；1-可用
     */
    private Integer tumorHospitalFlag;

    /**
     * 口腔医院可用：0-不可用；1-可用
     */
    private Integer stomatologyHospitalFlag;

    /**
     * 眼科医院可用：0-不可用；1-可用
     */
    private Integer eyeHospitalFlag;

    /**
     * 功能点维护人ID
     */
    private String functionMaintainerUserId;

    /**
     * 功能点维护人
     */
    private String functionMaintainer;

    /**
     * 功能点维护时间
     */
    private String functionMaintenanceTime;

    /**
     * 脚本维护人ID
     */
    private String checkSqlMaintainerUserId;

    /**
     * 脚本维护人
     */
    private String checkSqlMaintainer;

    /**
     * 脚本维护时间
     */
    private String checkSqlMaintenanceTime;

    /**
     * 适用范围
     */
    private List<String> applicableRange;


    public List<String> convertApplicableRange(ProductFunctionVO productFunctionVO) {
        List<String> applicableRange = new ArrayList<>();
        if (productFunctionVO.getPeoplesHospitalFlag() == 1) {
            applicableRange.add(HospitalTypeEnum.PEOPLES_HOSPITAL.getHospitalTypeCode());
        }
        if (productFunctionVO.getChineseHospitalFlag() == 1) {
            applicableRange.add(HospitalTypeEnum.CHINESE_HOSPITAL.getHospitalTypeCode());
        }
        if (productFunctionVO.getMaternalChildHospitalFlag() == 1) {
            applicableRange.add(HospitalTypeEnum.MATERNAL_CHILD_HOSPITAL.getHospitalTypeCode());
        }
        if (productFunctionVO.getTumorHospitalFlag() == 1) {
            applicableRange.add(HospitalTypeEnum.TUMOR_HOSPITAL.getHospitalTypeCode());
        }
        if (productFunctionVO.getStomatologyHospitalFlag() == 1) {
            applicableRange.add(HospitalTypeEnum.STOMATOLOGY_HOSPITAL.getHospitalTypeCode());
        }
        if (productFunctionVO.getEyeHospitalFlag() == 1) {
            applicableRange.add(HospitalTypeEnum.EYE_HOSPITAL.getHospitalTypeCode());
        }
        return applicableRange;
    }

}
