package com.msun.csm.dao.entity.dict;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;


@Data
@TableName(schema = "csm", value = "dict_business_status")
public class DictBusinessStatus implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private Long id;

    /**
     * 创建人id
     */
    private Long createrId;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人id
     */
    private Long updaterId;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 逻辑删除【0：否；1：是】
     */
    private Integer isDeleted;

    /**
     * 业务编码
     */
    private String businessCode;

    /**
     * 状态的数字编码
     */
    private Integer statusId;

    /**
     * 状态的字符编码
     */
    private String statusCode;

    /**
     * 状态的中文描述
     */
    private String statusDescription;

    /**
     * 状态对应的前端颜色
     */
    private String statusColor;

    /**
     * 状态整体属于哪种大状态：1-未开始/未完成；2-进行中；3-已完成
     */
    private String statusClass;

    /**
     * 排序号
     */
    private Integer sortNo;


}
