package com.msun.csm.dao.entity.proj.flowable;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@ApiModel("审批节点")
@Data
@TableName("csm.proj_backend_approve_node")
public class ProjBackendApproveNode extends BasePO {

    /**
     * 审批节点ID
     */
    @TableId
    private Long approveNodeId;

    /**
     * 项目ID
     */
    private Long projectInfoId;

    /**
     * 审批记录ID
     */
    private Long approveRecordId;

    /**
     * 序号
     */
    private Integer indexNo;

    /**
     * 审批人ID
     */
    private Long approveUserId;

    /**
     * 负责人ID
     */
    private Long leaderUserId;

    /**
     * 审批人账号
     */
    private String approveUserAccount;

    /**
     * 审批人姓名
     */
    private String approveUserName;

    /**
     * 0待审批 1审批中 2审批通过 3移交给候选人 4驳回上一步 5 审批拒绝 6撤销
     */
    private Integer status = 0;

    /**
     * 审批人填写的审批描述信息
     */
    private String details;

    /**
     * 审批时间
     */
    private Date approveAt;
}
