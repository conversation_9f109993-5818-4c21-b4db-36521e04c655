package com.msun.csm.dao.mapper.dict;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.dict.BackendTeamVsPlanItem;


@Mapper
public interface BackendTeamVsPlanItemMapper extends BaseMapper<BackendTeamVsPlanItem> {

    /**
     * 根据后端服务团队类型获取对应的项目计划节点对照
     *
     * @param teamTypeCode    后端服务团队类型
     * @param upgradationType 实施类型：-1.通用；1.老换新；2.新上线
     * @return 后端服务团队类型与负责的项目计划节点对照
     */

    List<BackendTeamVsPlanItem> getByTeamTypeCodeAndUpgradationType(@Param("teamTypeCode") String teamTypeCode, @Param("upgradationType") Integer upgradationType);
}
