package com.msun.csm.dao.entity.proj;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductFunctionOperationLogVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 字典表主键
     */
    private Long dictProductFunctionId;

    /**
     * 运营平台实施产品ID
     */
    private Long yyProductId;

    /**
     * 产品名称
     */
    private String yyProductName;

    /**
     * 功能检测点
     */
    private String functionName;

    /**
     * 功能检测说明
     */
    private String functionDesc;

    /**
     * 扣分分值
     */
    private BigDecimal markingStandard;

    /**
     * 功能检测脚本
     */
    private String checkSql;

    /**
     * 启用状态：0-启用；1-禁用
     */
    private Integer isDelete;

    /**
     * 人民医院可用：0-不可用；1-可用
     */
    private Integer peoplesHospitalFlag;

    /**
     * 中医院可用：0-不可用；1-可用
     */
    private Integer chineseHospitalFlag;

    /**
     * 妇幼保健院可用：0-不可用；1-可用
     */
    private Integer maternalChildHospitalFlag;

    /**
     * 肿瘤医院可用：0-不可用；1-可用
     */
    private Integer tumorHospitalFlag;

    /**
     * 口腔医院可用：0-不可用；1-可用
     */
    private Integer stomatologyHospitalFlag;

    /**
     * 眼科医院可用：0-不可用；1-可用
     */
    private Integer eyeHospitalFlag;

    /**
     * 操作人ID
     */
    private String operatorUserId;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 操作时间
     */
    private String operationTime;


}
