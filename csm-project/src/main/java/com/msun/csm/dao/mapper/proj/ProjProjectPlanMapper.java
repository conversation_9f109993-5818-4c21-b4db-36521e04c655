package com.msun.csm.dao.mapper.proj;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.proj.ProjProjectPlan;
import com.msun.csm.dao.entity.proj.ProjProjectPlanVO;
import com.msun.csm.dao.entity.proj.QueryProjectPlanParam;
import com.msun.csm.dao.entity.proj.UpdateProjectPlanParam;
import com.msun.csm.model.dto.ProjMilestoneInfoDTO;
import com.msun.csm.model.req.projectplan.QueryProjectPlanReq;
import com.msun.csm.model.resp.projectplan.ProjectPlanResp;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2025/1/13
 */

@Mapper
public interface ProjProjectPlanMapper extends BaseMapper<ProjProjectPlan> {
    /**
     * delete by primary key
     *
     * @param projectPlanId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long projectPlanId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(ProjProjectPlan record);


    /**
     * select by primary key
     *
     * @param projectPlanId primary key
     * @return object by primary key
     */
    ProjProjectPlan selectByPrimaryKey(Long projectPlanId);

    int updateBatchSelective(@Param("list") List<ProjProjectPlan> list);

    int batchInsert(@Param("list") List<ProjProjectPlan> list);

    /**
     * 根据参数查询项目计划
     *
     * @param dto
     * @return
     */
    List<ProjProjectPlan> getConfigProjectPlanItemByParams(ProjMilestoneInfoDTO dto);


    /**
     * 批量删除项目计划
     *
     * @param projectPlanIdList
     * @return
     */
    int deleteBatchIds(@Param("projectPlanIdList") List<Long> projectPlanIdList);

    /**
     * 根据项目ID获取
     * @param projectInfoId
     * @return
     */
    List<ProjectPlanResp> queryDataByProjectInfoId(@Param("projectInfoId") Long projectInfoId);

    /**
     * 查询项目计划
     *
     * @param req
     * @return
     */
    List<ProjectPlanResp> queryData(@Param("req") QueryProjectPlanReq req);


    /**
     * 查询项目计划最大排序
     *
     * @param projectInfoId
     * @return
     */
    Integer queryMaxSort(@Param("projectInfoId") Long projectInfoId);

    /**
     * 根据项目计划id批量查询项目计划
     *
     * @param projectPlanIdList
     * @return
     */
    List<ProjProjectPlan> selectBatchIds(List<Long> projectPlanIdList);

    /**
     * 更新任务完成状态及进度
     *
     * @param itemCode      计划项编码
     * @param status        任务状态
     * @param completeCount 任务完成数量
     * @param totalCount    任务总数量
     * @param projectInfoId 项目id
     * @return 更新数量
     */
    int updateCompleteStatus(@Param("itemCode") String itemCode, @Param("status") Integer status, @Param("completeCount") Integer completeCount,
                             @Param("totalCount") Integer totalCount, @Param("projectInfoId") Long projectInfoId);


    ///-----------------------------------------------------

    /**
     * 根据项目计划ID获取项目计划信息
     *
     * @param planId 项目计划ID
     * @return 项目工作计划
     */
    ProjProjectPlan getProjectPlanByPlanId(@Param("planId") Long planId);

    List<ProjProjectPlan> getProjectPlanByProjectAndItemIds(@Param("projectInfoId") Long projectInfoId, @Param("planItemIds") List<Long> planItemIds);


    List<ProjProjectPlan> getProjectPlanByProject(@Param("projectInfoId") Long projectInfoId);

    /**
     * 根据项目ID和项目计划工作项编码获取项目工作计划
     *
     * @param projectInfoId 项目ID
     * @param itemCode      项目计划工作项编码
     * @return 项目工作计划
     */
    ProjProjectPlan getProjectPlanByProjectInfoIdAndItemCode(@Param("projectInfoId") Long projectInfoId, @Param("itemCode") String itemCode);

    /**
     * 重点关注项目计划或者取消重点关注
     *
     * @param projectPlanId 项目计划ID
     * @param attentionFlag 0-取消重点关注；1-重点关注
     * @param updaterId     更新人ID
     * @return 更新成功的数据条数
     */
    int followOrUnfollow(@Param("projectPlanId") Long projectPlanId, @Param("attentionFlag") Integer attentionFlag, @Param("updaterId") Long updaterId);

    /**
     * 根据项目计划ID更新项目计划状态
     * <p>当状态更新为已完成时，同步更新完成时间</p>
     *
     * @param projectPlanId 项目计划ID
     * @param status        0-未完成；1-已完成；2-进行中
     * @param updaterId     更新人ID
     * @return 更新成功的数据条数
     */
    int updateStatusByProjectPlanId(@Param("projectPlanId") Long projectPlanId, @Param("status") Integer status, @Param("updaterId") Long updaterId);

    /**
     * 根据项目ID和项目计划工作项编码更新项目计划状态
     * <p>当状态更新为已完成时，同步更新完成时间</p>
     *
     * @param projectInfoId 项目ID
     * @param itemCode      项目计划工作项编码
     * @param status        0-未完成；1-已完成；2-进行中
     * @param updaterId     更新人
     * @return 更新成功的数据条数
     */
    int updateStatusByProjectInfoIdAndItemCode(@Param("projectInfoId") Long projectInfoId, @Param("itemCode") String itemCode, @Param("status") Integer status, @Param("updaterId") Long updaterId);

    /**
     * 根据项目ID和项目计划工作项编码使待办总数（任务进度分母）在原来的基础上增加指定值
     *
     * @param projectInfoId 项目ID
     * @param itemCode      项目计划工作项编码
     * @param totalCount    需要增加的待办总数（任务进度分母）
     * @return 更新成功的数据条数
     */
    int addTotalCountByProjectInfoIdAndItemCode(@Param("projectInfoId") Long projectInfoId, @Param("itemCode") String itemCode, @Param("totalCount") Integer totalCount);

    /**
     * 根据项目计划ID待办总数（任务进度分母）和待办完成数（任务进度分子）更新为指定值
     *
     * @param planId        项目计划主键
     * @param totalCount    待办总数（任务进度分母）
     * @param completeCount 待办完成数（任务进度分子）
     * @return 更新成功的数据条数
     */
    int updateTotalAndCompleteCountByPlanId(@Param("planId") Long planId, @Param("totalCount") Integer totalCount, @Param("completeCount") Integer completeCount);

    /**
     * 根据项目ID和项目计划类型查询项目计划集合
     *
     * @param param 参数
     * @return 项目计划集合
     */
    List<ProjProjectPlan> getProjectPlanByProjectInfoId(QueryProjectPlanParam param);

    /**
     * 根据项目ID和项目计划类型查询前端使用的项目计划集合
     *
     * @param param 参数
     * @return 前端使用的项目计划集合
     */
    List<ProjProjectPlanVO> getProjectPlanViewByProjectInfoId(QueryProjectPlanParam param);

    /**
     * 取消项目计划前置节点限制
     *
     * @param projectPlanId 项目计划ID
     * @param updaterId     操作人ID
     */
    int cancelPriorProjectPlanItem(@Param("projectPlanId") Long projectPlanId, @Param("updaterId") Long updaterId);

    int updatePlanInfo(UpdateProjectPlanParam updateProjectPlanParam);


}
