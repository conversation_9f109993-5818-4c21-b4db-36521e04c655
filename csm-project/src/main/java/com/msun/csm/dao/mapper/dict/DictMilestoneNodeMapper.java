package com.msun.csm.dao.mapper.dict;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.config.ConfigMilestoneNode;
import com.msun.csm.dao.entity.dict.DictMilestoneNode;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/25
 */
@Mapper
public interface DictMilestoneNodeMapper extends BaseMapper<DictMilestoneNode> {
    int deleteByPrimaryKey(Long id);
    List<DictMilestoneNode> findMilestoneNodeName(ConfigMilestoneNode configMilestoneNode);
    int insert(DictMilestoneNode record);

    int insertOrUpdate(DictMilestoneNode record);

    int insertOrUpdateSelective(DictMilestoneNode record);

    int insertSelective(DictMilestoneNode record);

    DictMilestoneNode selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DictMilestoneNode record);

    int updateByPrimaryKey(DictMilestoneNode record);

    int updateBatch(List<DictMilestoneNode> list);

    int updateBatchSelective(List<DictMilestoneNode> list);

    int batchInsert(@Param ("list") List<DictMilestoneNode> list);
}
