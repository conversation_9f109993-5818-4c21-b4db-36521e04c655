package com.msun.csm.dao.entity.dict;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/25
 */

/**
 * 里程碑节点字典表
 */
@ApiModel (description = "里程碑节点字典表")
@Data
@TableName (schema = "csm")
public class DictMilestoneNode implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty (value = "主键")
    @TableId (type = IdType.INPUT)
    private Long milestoneNodeId;
    /**
     * 里程碑节点名称
     */
    @ApiModelProperty (value = "里程碑节点名称")
    private String milestoneNodeName;
    /**
     * 里程碑节点路径，点击调整的页面路径
     */
    @ApiModelProperty (value = "里程碑节点路径，点击调整的页面路径")
    private String milestoneNodeUrl;
    /**
     * 作废标识：0.否；1.是
     */
    @ApiModelProperty (value = "作废标识：0.否；1.是")
    @TableLogic
    private String invalidFlag;
    /**
     * 创建人id
     */
    @ApiModelProperty (value = "创建人id")
    @TableField (fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    private Long createrId;
    /**
     * 创建时间
     */
    @ApiModelProperty (value = "创建时间")
    @TableField (fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    private Date createTime;
    /**
     * 修改人id
     */
    @ApiModelProperty (value = "修改人id")
    @TableField (fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    private Long updaterId;
    /**
     * 修改时间
     */
    @ApiModelProperty (value = "修改时间")
    @TableField (fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    private Date updateTime;
    /**
     * vue组件标识名称(前端使用)
     */
    @ApiModelProperty (value = "vue组件标识名称(前端使用)")
    private String isComponent;
    /**
     * 里程碑节点简称
     */
    @ApiModelProperty (value = "里程碑节点简称")
    private String simpleMilestoneNodeName;
    /**
     * 里程碑节点编码
     */
    @ApiModelProperty (value = "里程碑节点编码")
    private String milestoneNodeCode;
}
