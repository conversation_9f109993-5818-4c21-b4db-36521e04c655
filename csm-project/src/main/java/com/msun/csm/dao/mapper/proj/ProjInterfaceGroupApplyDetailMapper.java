package com.msun.csm.dao.mapper.proj;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.proj.ProjInterfaceGroupApplyDetail;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/9/12
 */

@Mapper
public interface ProjInterfaceGroupApplyDetailMapper extends BaseMapper<ProjInterfaceGroupApplyDetail> {
    /**
     * delete by primary key
     *
     * @param interfaceGroupApplyDetailId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long interfaceGroupApplyDetailId);

    int insertOrUpdate(ProjInterfaceGroupApplyDetail record);

    int insertOrUpdateSelective(ProjInterfaceGroupApplyDetail record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(ProjInterfaceGroupApplyDetail record);

    /**
     * select by primary key
     *
     * @param interfaceGroupApplyDetailId primary key
     * @return object by primary key
     */
    ProjInterfaceGroupApplyDetail selectByPrimaryKey(Long interfaceGroupApplyDetailId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(ProjInterfaceGroupApplyDetail record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(ProjInterfaceGroupApplyDetail record);

    int updateBatch(List<ProjInterfaceGroupApplyDetail> list);

    int updateBatchSelective(List<ProjInterfaceGroupApplyDetail> list);

    int batchInsert(@Param("list") List<ProjInterfaceGroupApplyDetail> list);

    /**
     * 根据第三方接口ID和环境查询
     *
     * @param thirdInterfaceId
     * @param environment
     * @return
     */
    List<ProjInterfaceGroupApplyDetail> selectByThirdIdAndEnv(@Param("thirdInterfaceId") Long thirdInterfaceId,
                                                              @Param("environment") Integer environment);

    /**
     * 根据第三方接口ID和环境删除
     *
     * @param thirdInterfaceId
     * @param environment
     * @return
     */
    int deleteByThirdIdAndEnv(@Param("thirdInterfaceId") Long thirdInterfaceId,
                              @Param("environment") Integer environment);
}
