package com.msun.csm.dao.entity.config;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/11/29
 */

/**
 * 项目问题跟进数据分类配置表
 */
@ApiModel(description = "项目问题跟进数据分类配置表")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConfigIssueClassification {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称")
    private String name;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private Long createrId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新人id
     */
    @ApiModelProperty(value = "更新人id")
    private Long updaterId;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 逻辑删除【0：否；1：是】
     */
    @ApiModelProperty(value = "逻辑删除【0：否；1：是】")
    private Integer isDeleted;
}
