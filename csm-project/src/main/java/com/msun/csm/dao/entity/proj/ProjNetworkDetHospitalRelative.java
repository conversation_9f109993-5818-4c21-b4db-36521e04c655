package com.msun.csm.dao.entity.proj;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-05-16 09:02:04
 */

@Data
@TableName(schema = "csm")
public class ProjNetworkDetHospitalRelative extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "主键")
    @TableId("id")
    private Long id;

    /**
     * 医院id
     */
    private Long hospitalInfoId;

    /**
     * 医院名称
     */
    @Schema(description = "医院名称")
    private String hospitalName;

    /**
     * 检测域名
     */
    @Schema(description = "检测域名")
    private String detectDomain;

    /**
     * 系统类型 0 windows, 1 linux
     */
    @Schema(description = "系统类型")
    private Integer sysType;

    /**
     * 检测端口
     */
    @Schema(description = "检测端口")
    private Integer detectPort;


    @Schema(description = "终端数量")
    private Long clientCount;

    /**
     * 检测情况
     */
    @Schema(description = "检测情况")
    private Integer detectStatus;

    /**
     * 前置机调用与健康结果
     */
    @Schema(description = "前置机调用与健康结果")
    private Integer callCloudStatus;

    /**
     * 云健康调用前置机结果
     */
    @Schema(description = "云健康调用前置机结果")
    private Integer callFrontStatus;

    /**
     * 前置机内网ip
     */
    @Schema(description = "前置机内网ip")
    private String frontIntranetIp;

    /**
     * 前置机外网ip
     */
    @Schema(description = "前置机外网ip")
    private String frontExternalIp;

    /**
     * 产品使用的内网端口
     */
    @Schema(description = "产品使用的内网端口（用空格分割，可填多个）")
    private String productUsedIntranetPort;

    /**
     * 产品使用的外网端口
     */
    @Schema(description = "产品使用的外网端口")
    private String productUsedExternalPort;

    /**
     * root密码
     */
    @Schema(description = "root密码")
    private String rootPwd;

    /**
     * 用途
     */
    @Schema(description = "用途")
    private Integer purpose;


    /**
     * 内网ssh端口
     */
    @Schema(description = "内网ssh端口")
    private String intranetSshPort;

    /**
     * 外网ssh端口
     */
    @Schema(description = "外网ssh端口")
    private String externalSshPort;

    /**
     * 网络检测使用的DNS服务器地址
     */
    @Schema (description = "网络检测使用的DNS服务器地址")
    private String dns;

}
