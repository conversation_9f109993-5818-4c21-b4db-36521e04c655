package com.msun.csm.dao.entity.dict;

import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/5/28
 */

/**
 * 工单产品与授权对照信息
 */
@ApiModel(description = "工单产品与授权对照信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DictProductVsEmpowerRelative extends BasePO {
    private static final long serialVersionUID = 1L;
    /**
     * 对照关系ID
     */
    @ApiModelProperty(value = "对照关系ID")
    private Long productVsEmpowerId;

    /**
     * 工单产品字典ID(运营平台产品字典ID)
     */
    @ApiModelProperty(value = "工单产品字典ID(运营平台产品字典ID)")
    private Long orderProductId;

    /**
     * 云健康产品模块授权编码
     */
    @ApiModelProperty(value = "云健康产品模块授权编码")
    private String msunHealthModuleCode;

    /**
     * 云健康产品模块授权名称
     */
    @ApiModelProperty(value = "云健康产品模块授权名称")
    private String msunHealthModule;

    /**
     * 云健康产品名称
     */
    @ApiModelProperty(value = "云健康产品名称")
    private String productName;
}
