package com.msun.csm.dao.mapper.proj;


import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.proj.CommonScoreRecordPO;
import com.msun.csm.dao.entity.proj.ProjDeductionDetailCommon;


public interface ProjDeductionDetailCommonMapper extends BaseMapper<ProjDeductionDetailCommon> {

    List<CommonScoreRecordPO> getCommonScoreRecord(@Param("projectInfoId") Long projectInfoId, @Param("classificationCode") String classificationCode);

    int updateDeductionDetailCommonById(ProjDeductionDetailCommon param);

    int updateStatusToDeletedById(@Param("id") Long id);

    int deleteDeductionDetailCommonById(@Param("id") Long id);

}
