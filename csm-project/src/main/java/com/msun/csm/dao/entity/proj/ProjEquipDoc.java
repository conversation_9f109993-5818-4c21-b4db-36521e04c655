package com.msun.csm.dao.entity.proj;

import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
* @description:
* @fileName: projEquipDoc.java
* @author: lius3
* @createAt: 2024/10/16 13:48
* @updateBy: lius3
* @remark: Copyright
*/
@ApiModel(description = "实施地客户信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjEquipDoc extends BasePO {

    /**
     * 设备文档id
     */
    @ApiModelProperty("设备文档id")
    private Long equipDocId;

    /**
     * 设备厂商id
     */
    @ApiModelProperty("设备厂商id")
    private Long equipFactoryId;

    /**
     * 设备厂商名称
     */
    @ApiModelProperty("设备厂商名称")
    private String equipFactoryName;

    /**
     * 产品id
     */
    @ApiModelProperty("产品id")
    private Long yyProductId;

    /**
     * 系统文件唯一标识
     */
    @ApiModelProperty("系统文件唯一标识")
    private String fileCode;

    /**
     * 检查模态key
     */
    @ApiModelProperty("检查模态key")
    private String modalKey;

    /**
     * 检查模态名称
     */
    @ApiModelProperty("检查模态名称")
    private String modalName;

    /**
     * 文档类型编码
     */
    @ApiModelProperty("文档类型编码")
    private String docTypeCode;

    /**
     * 文档类型名称
     */
    @ApiModelProperty("文档类型名称")
    private String docTypeName;

}
