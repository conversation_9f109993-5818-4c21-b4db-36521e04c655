package com.msun.csm.dao.entity.proj;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import lombok.Data;

/**
 * 特批/工单产品补录-明细表(ProjProductSupplementaryRecordDetail)实体类
 *
 * <AUTHOR>
 * @since 2024-07-04 16:50:30
 */
@Data
@TableName(value = "proj_product_supplementary_record_detail", schema = "csm")
public class ProjProductSupplementaryRecordDetail extends BasePO {
    /**
     * 主键
     */
    @TableId(type = IdType.NONE)
    private Long productSupplementaryRecordDetailId;
    /**
     * 特批/工单产品补录记录表id
     */
    private Long productSupplementaryRecordId;
    /**
     * 运营平台产品ID
     */
    private Long yyProductId;
}
