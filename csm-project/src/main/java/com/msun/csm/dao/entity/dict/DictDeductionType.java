package com.msun.csm.dao.entity.dict;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;


/**
 * 项目验收菜单字典表
 */
@Data
@TableName(schema = "csm", value = "dict_deduction_type")
public class DictDeductionType implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 删除标记，0-正常，1-已删除
     */
    private Integer isDeleted;

    /**
     * 创建人
     */
    private Long createrId;

    /**
     * 更新人
     */
    private Long updaterId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 扣分类型名称
     */
    private String name;

    /**
     * 扣分类型编码
     */
    private String code;

    /**
     * 适用场景
     */
    private String applicableScene;

    /**
     * 排序号
     */
    private Integer sortNo;

    /**
     * 项目阶段编码
     */
    private String projectStageCode;

    /**
     * 问题分类主键
     */
    private Long issueClassificationId;

    /**
     * 对应的后端服务团队类型：bustype-业务服务；datatype-数据服务；interfacetype-接口服务
     */
    private String serverType;

    /**
     * 默认分值
     */
    private BigDecimal defaultScore;

}
