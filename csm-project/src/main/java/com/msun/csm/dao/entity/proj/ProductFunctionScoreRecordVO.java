package com.msun.csm.dao.entity.proj;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

import javax.validation.constraints.NotNull;

@Data
public class ProductFunctionScoreRecordVO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 客户ID
     */
    private Long customInfoId;

    /**
     * 项目ID
     */
    private Long projectInfoId;

    /**
     * 菜单编码
     */
    private String menuCode;

    /**
     * 功能编码
     */
    private String functionCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 运营平台产品ID
     */
    private Long yyProductId;

    /**
     * 功能点
     */
    private String functionName;

    /**
     * 功能描述
     */
    private String functionDesc;

    /**
     * 使用次数
     */
    private String useCount;

    /**
     * 是否必选项：0-非必选，1-必选
     */
    private Integer requiredFlag;

    /**
     * 预估扣分
     */
    private BigDecimal estimatedDeduction;

    /**
     * 实际扣分
     */
    private BigDecimal practicalDeduction;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否只需要一次验收：true-只需要一次验收；false-需要两次验收
     */
    @NotNull(message = "参数【onlyOneCheckFlag】不可为null")
    private Boolean onlyOneCheckFlag;

    /**
     * 当前验收次数：1-第一次验收；2-第二次验收
     */
    @NotNull(message = "参数【currentAcceptanceTimes】不可为null")
    private Integer currentAcceptanceTimes;

    /**
     * 扣分类型字典表编码
     */
    private String deductionType;

    /**
     * 扣分类型描述
     */
    private String deductionTypeDesc;

    /**
     * 附件ID集合，多个以英文逗号分割
     */
    private String attachmentId;

    /**
     * 附件
     */
    private List<AttachmentInfoVO> attachmentInfoList;

}
