package com.msun.csm.dao.entity.proj.projform;

import java.io.Serializable;
import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import lombok.Data;

/**
 * 表单项目经理审核记录表
 *
 * @TableName proj_form_examine_log
 */
@TableName(value = "proj_form_examine_log", schema = "csm")
@Data
public class ProjFormExamineLog extends BasePO implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "form_examine_log_id")
    private Long formExamineLogId;
    /**
     * proj_survey_form表主键ID
     */
    @TableField(value = "survey_form_id")
    private Long surveyFormId;
    /**
     * 审核状态 0 驳回 1通过
     */
    @TableField(value = "examine_status")
    private Integer examineStatus;
    /**
     * 审核意见
     */
    @TableField(value = "examine_opinion")
    private String examineOpinion;
    /**
     * 逻辑删除【0：否；1：是】
     */
    @TableField(value = "is_deleted")
    private Integer isDeleted;
    /**
     * 创建人id
     */
    @TableField(value = "creater_id")
    private Long createrId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Timestamp createTime;
    /**
     * 更新人id
     */
    @TableField(value = "updater_id")
    private Long updaterId;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Timestamp updateTime;
}