package com.msun.csm.dao.entity.proj;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/7/19
 */

/**
 * 调研计划表
 */
@ApiModel(description = "调研计划表")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName (schema = "csm")
public class ProjSurveyPlan {
    /**
     * 调研计划主键
     */
    @ApiModelProperty (value = "调研计划主键")
    @TableId (type = IdType.INPUT)
    @ExcelIgnore
    private Long surveyPlanId;
    /**
     * 调研产品id，运营平台id，也可能是模块id
     */
    @ApiModelProperty(value = "调研产品id，运营平台id，也可能是模块id")
    @ExcelIgnore
    private Long yyProductId;
    /**
     * 医院id
     */
    @ApiModelProperty(value = "医院id")
    @ExcelIgnore
    private Long hospitalInfoId;
    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    @ExcelIgnore
    private Long customInfoId;
    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @ExcelIgnore
    private Long projectInfoId;
    /**
     * 科室名称  手动填写
     */
    @ApiModelProperty(value = "科室名称  手动填写")
    @ExcelProperty(value = "科室名称", index = 2)
    @ColumnWidth(20)
    private String deptName;
    /**
     * 调研人id
     */
    @ApiModelProperty(value = "调研人id")
    @ExcelIgnore
    private Long surveyUserId;
    /**
     * 计划完成时间
     */
    @ApiModelProperty(value = "计划完成时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @ExcelProperty(value = "计划完成时间", index = 4)
    @ColumnWidth(20)
    private Date planCompleteTime;
    /**
     * 审核状态
     * <p>0-未开始（数据初始状态）</p>
     * <p>1-已完成（确认完成之后的状态）</p>
     * <p>2-进行中</p>
     * <p>3-待确认（需要审核时审核通过的状态或者不需要审核时提交问卷之后的状态）</p>
     * <p>4-待审核（需要审核时提交审核之后的状态）</p>
     * <p>5-已驳回（需要审核时驳回之后的状态）</p>
     * <p>6-已保存/待提交审核（需要审核时提交问卷之后的状态）</p>
     */
    @ApiModelProperty(value = "完成状态，0未完成，1已完成,2 进行中   3 待确认")
    @ExcelIgnore
    private Integer completeStatus;
    /**
     * 实际完成时间
     */
    @ApiModelProperty(value = "实际完成时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @ExcelProperty(value = "实际完成时间", index = 6)
    @ColumnWidth(20)
    private Date actualCompTime;
    /**
     * 逻辑删除【0：否；1：是】
     */
    @ApiModelProperty(value = "逻辑删除【0：否；1：是】")
    @ExcelIgnore
    private Integer isDeleted;
    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    @ExcelIgnore
    private Long createrId;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelIgnore
    private Date createTime;
    /**
     * 更新人id
     */
    @ApiModelProperty(value = "更新人id")
    @ExcelIgnore
    private Long updaterId;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelIgnore
    private Date updateTime;

    @ApiModelProperty("排序")
    @ExcelIgnore
    private Long orderNo;

    /**
     * 后端审核人的用户id
     */
    @ExcelIgnore
    private Long auditSysUserId;

    /**
     * 计划的最晚审核时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @ExcelIgnore
    private Date planAuditTime;

    /**
     * 实际审核时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @ExcelIgnore
    private Date actualAuditTime;
}
