package com.msun.csm.dao.mapper.proj;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.msun.csm.dao.entity.proj.BackendTeamDeductionDetailInfoVO;
import com.msun.csm.dao.entity.proj.BackendTeamDeductionDetailVO2;
import com.msun.csm.dao.entity.proj.ProjDeductionDetailInfo;

public interface ProjDeductionDetailInfoMapper {
    int deleteByPrimaryKey(Long projDeductionDetailInfoId);

    int insertSelective(ProjDeductionDetailInfo record);

    ProjDeductionDetailInfo selectByPrimaryKey(Long projDeductionDetailInfoId);

    int updateByPrimaryKeySelective(ProjDeductionDetailInfo record);

    int updateByPrimaryKey(ProjDeductionDetailInfo record);

    int batchInsert(@Param("list") List<ProjDeductionDetailInfo> list);

    List<BackendTeamDeductionDetailInfoVO> getBackendTeamDeductionDetailInfo(BackendTeamDeductionDetailVO2 param);
}