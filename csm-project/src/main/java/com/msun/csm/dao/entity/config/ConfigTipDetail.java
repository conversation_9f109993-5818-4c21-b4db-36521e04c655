package com.msun.csm.dao.entity.config;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-09-25 04:06:00
 */

@Data
@TableName(schema = "csm")
public class ConfigTipDetail extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId("config_tip_detail_id")
    private Long configTipDetailId;

    /**
     * 主表ID
     */
    @Schema(description = "主表ID")
    private Long configTipId;

    /**
     * 提示内容
     */
    @Schema(description = "提示内容")
    private String tipContent;

    /**
     * 排序（相同config_tip_id进行排序）
     */
    @Schema(description = "排序（相同config_tip_id进行排序）")
    private Integer orderNo;

}
