package com.msun.csm.dao.mapper.proj;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.proj.ProjUpdateDataLog;
import com.msun.csm.model.req.projtool.ProjToolUpdateReq;

/**
 * @classDesc: 功能描述:(项目手动创建操作日志DAO)
 * @author: 侯艳军
 * @date: 2024/10/29 13:45
 * @copyright 众阳健康
 */
@Mapper
public interface ProjUpdateDataLogMapper extends BaseMapper<ProjUpdateDataLog> {

    /**
     * 根据条件查询
     * @param dto
     * @return
     */
    List<ProjUpdateDataLog> selectByCondition(ProjToolUpdateReq dto);
}
