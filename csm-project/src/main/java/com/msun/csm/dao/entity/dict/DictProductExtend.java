package com.msun.csm.dao.entity.dict;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import lombok.Data;

/**
 * 产品字典扩展信息表(DictProductExtend)实体类
 *
 * <AUTHOR>
 * @since 2024-09-02 09:16:01
 */
@Data
@TableName(value = "dict_product_extend", schema = "csm")
public class DictProductExtend extends BasePO {
    /**
     * 产品字典扩展表主键ID
     */
    @TableId(type = IdType.NONE)
    private Long productExtendId;
    /**
     * 运营平台产品id
     */
    private Long yyProductId;
    /**
     * 准备阶段数据检测标识【0:否；1是】（以实施产品为依据）
     */
    private Integer prepareDataCheckFlag;
    /**
     * 当前产品的配置是否按照医院隔离【0-按照医院隔离、1-不按照医院隔离】默认按照医院隔离
     */
    private Integer configIsolationByHospitalFlag;
    /**
     * 是否需要调研【0：否；1：是】
     */
    private Integer surveyFlag;
}
