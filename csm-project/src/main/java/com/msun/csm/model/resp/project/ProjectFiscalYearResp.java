package com.msun.csm.model.resp.project;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Author: zd
 * @Date: 2024/5/22
 */
@Data
public class ProjectFiscalYearResp {

    @ApiModelProperty(value = "云健康升级客户")
    private Integer cloudHealthCount;
    @ApiModelProperty(value = "单体")
    private Integer monomerCount;
    @ApiModelProperty(value = "区域")
    private Integer regionCount;
    @ApiModelProperty(value = "电销")
    private Integer electricSalesCount;
}
