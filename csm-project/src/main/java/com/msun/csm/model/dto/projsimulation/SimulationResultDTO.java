package com.msun.csm.model.dto.projsimulation;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SimulationResultDTO {

    /**
     * 客户ID
     */
    @ApiModelProperty("客户ID")
    @NotNull
    private Long customInfoId;

    /**
     * 项目ID
     */
    @ApiModelProperty("客户ID")
    @NotNull
    private Long projectInfoId;

    /**
     * 开始时间
     */
    @ApiModelProperty ("开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty ("结束时间")
    private String endTime;
}
