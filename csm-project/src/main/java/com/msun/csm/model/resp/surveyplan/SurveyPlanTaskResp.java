package com.msun.csm.model.resp.surveyplan;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.msun.csm.dao.entity.proj.ProjSurveyPlan;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/7/18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SurveyPlanTaskResp extends ProjSurveyPlan {

    /**
     * 父级节点id，是产品id后者模块id
     */
    @ExcelIgnore
    private Long pid;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称", index = 1)
    @ColumnWidth(20)
    private String productInfoName;

    /**
     * 医院名称
     */
    @ExcelProperty(value = "医院名称", index = 0)
    @ColumnWidth(20)
    private String hospitalInfoName;

    /**
     * 调研人名称
     */
    @ExcelProperty(value = "调研人名称", index = 3)
    @ColumnWidth(20)
    private String surveyUserName;

    /**
     * 完成进度 有子项的数据为进行中（1/3），其余数据为 进行中、未开始、已完成
     */
    @ExcelProperty(value = "完成进度", index = 5)
    @ColumnWidth(20)
    private String completionProgress;

    /**
     * 是否可以编辑-是否项目经理
     */
    @ExcelIgnore
    private boolean canEdit;

    /**
     * 是否可以调研-是否是分配的调研人
     */
    @ExcelIgnore
    private boolean canSurvey;

    /**
     * 是否有子项
     */
    @ExcelIgnore
    private Boolean hasChildren;

    /**
     * 是否需要调研【0：否；1：是】
     */
    @ExcelIgnore
    private Integer surveyFlag;

    /**
     * 最终调研结果的责任人ID
     */
    @ExcelIgnore
    private Long finalResultUserId;

    /**
     * 最终调研结果主键
     */
    @ExcelIgnore
    private Long finalResultDataId;

    /**
     * 移动端使用的产品名称
     */
    @ExcelIgnore
    private String mobileProductName;

    /**
     * 审核人姓名
     */
    @ExcelIgnore
    private String auditUserName;

    /**
     * 驳回原因
     */
    @ExcelIgnore
    private String rejectReason;

    /**
     * 计划审核时间字符串
     */
    @ExcelIgnore
    private String planAuditTimeStr;

    /**
     * main-新版产品业务调研的外层展示数据；
     * detail-新版产品业务调研的明细数据
     */
    @ExcelIgnore
    private String showType;

    /**
     * 项目计划主键，兼容老流程
     */
    @ExcelIgnore
    private Long planId;
}
