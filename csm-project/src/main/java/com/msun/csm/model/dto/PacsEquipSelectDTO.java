package com.msun.csm.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* @description:
* @fileName: PacsEquipSelectDTO.java
* @author: lius3
* @createAt: 2024/10/12 17:00
* @updateBy: lius3
* @remark: Copyright
*/
@Data
public class PacsEquipSelectDTO {

    @ApiModelProperty("客户id")
    private Long customInfoId;

    @ApiModelProperty ("项目id")
    private Long projectInfoId;

    @ApiModelProperty ("调研来源医院id")
    private Long hospitalInfoId;

    @ApiModelProperty ("设备型号、厂商、模态【查询条件】")
    private String equipModelOrFactory;

    @ApiModelProperty ("是否对接")
    private Integer requiredFlag;

    /**
     * LIS设备记录表主键id
     */
    @ApiModelProperty (value = "PACS设备记录表主键id")
    private Long equipRecordVsPacsId;

    @ApiModelProperty("是否来自移动端查询")
    private Integer isMobile;
}
