package com.msun.csm.model.req.projreport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.dto.BasePageDTO;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@ApiModel(description = "报表数据")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjSurveyReportPrintReq extends BasePageDTO {
    private static final long serialVersionUID = 1L;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户信息ID")
    private Long customInfoId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

    @ApiModelProperty("纸张类型名称")
    private String paperSize;
    @ApiModelProperty("打印机名称")
    private String printerName;
    @ApiModelProperty("打印机驱动名称")
    private String printerDriver;
    @ApiModelProperty("横纵向")
    private String orientation;
    @ApiModelProperty("科室名称")
    private String deptName;
    @ApiModelProperty("是否共享打印机1是0否")
    private String isShare;

}
