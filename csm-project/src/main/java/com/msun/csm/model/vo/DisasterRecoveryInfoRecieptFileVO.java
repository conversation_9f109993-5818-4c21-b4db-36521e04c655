package com.msun.csm.model.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-10-11 08:39:18
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DisasterRecoveryInfoRecieptFileVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "回执单模板下载链接")
    private String receiptModelUrl;

    /**
     * 系统文件id
     */
    @ApiModelProperty(value = "回执单模板文件id")
    private Long receiptModeFileId;

    @ApiModelProperty(value = "回执单下载")
    private String receiptProjectFileUrl;

    @ApiModelProperty(value = "回执单名称")
    private String receiptProjectFileName;

    @ApiModelProperty(value = "上传时间")
    private String uploadTime;

    @ApiModelProperty(value = "上传人")
    private String uploadPersonName;

}
