package com.msun.csm.model.resp.projform;

import java.util.List;

import com.github.pagehelper.PageInfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 报表表单分页查询数据
 * @param <T>
 */
@Data
public class ProjSurveyReprotFormPageResp<T> extends PageInfo<T> {

    @ApiModelProperty(value = "总数量")
    private Integer totalCount;
    @ApiModelProperty(value = "上线前必须完成数量")
    private Integer preLaunchCompletionCount;
    @ApiModelProperty(value = "未完成数量")
    private Integer incompleteCount;
    @ApiModelProperty(value = "驳回数量")
    private Integer rejectedCount;

    @ApiModelProperty(value = "是否需要分配审核人，提交审核")
    private Boolean isNeedAuditorFlag;

    public ProjSurveyReprotFormPageResp(List<T> list) {
        super(list);
    }
}