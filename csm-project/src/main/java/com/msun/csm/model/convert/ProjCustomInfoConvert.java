package com.msun.csm.model.convert;

import org.mapstruct.Mapper;

import com.msun.csm.common.model.convert.Dto2PoBaseConvert;
import com.msun.csm.common.model.convert.Dto2VoBaseConvert;
import com.msun.csm.common.model.convert.Vo2PoBaseConvert;
import com.msun.csm.dao.entity.proj.ProjCustomInfo;
import com.msun.csm.model.dto.ProjCustomInfoDTO;
import com.msun.csm.model.vo.ProjCustomInfoVO;

@Mapper (componentModel = "spring")
public interface ProjCustomInfoConvert extends
        Dto2PoBaseConvert<ProjCustomInfoDTO, ProjCustomInfo>, Dto2VoBaseConvert<ProjCustomInfoDTO, ProjCustomInfoVO>, Vo2PoBaseConvert<ProjCustomInfoVO, ProjCustomInfo> {
}
