package com.msun.csm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/10/25/17:11
 */
@Data
public class CheckAppDetailVO {

    @ApiModelProperty ("接口id")
    private Long serviceId;

    @ApiModelProperty ("接口名称")
    private String serviceName;

    @ApiModelProperty ("接口地址")
    private String url;

    @ApiModelProperty ("是否为核心接口")
    private Boolean isCore;

    @ApiModelProperty ("检测结果")
    private Boolean checkResult;

    @ApiModelProperty ("失败原因")
    private String failReason;

}
