package com.msun.csm.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FormDataDTO {

    /**
     * 答卷ID
     */
    private Long id;

    /**
     * 科室
     */
    private String deptName;

    /**
     * 数据来源：survey-调研、config-项目经理确认最终结果后生成配置的页面
     */
    private String source;


    /**
     * 保存类型：temporary-暂存、permanent-提交
     */
    private String saveType;

    /**
     * 用户ID
     */
    private String sysUserId;

    /**
     * 医院信息ID
     */
    private String hospitalInfoId;

    /**
     * 表单key
     */
    private String formKey;


}
