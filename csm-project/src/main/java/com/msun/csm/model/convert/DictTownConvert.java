package com.msun.csm.model.convert;

import org.mapstruct.Mapper;

import com.msun.csm.common.model.convert.Dto2PoBaseConvert;
import com.msun.csm.common.model.convert.Dto2VoBaseConvert;
import com.msun.csm.common.model.convert.Vo2PoBaseConvert;
import com.msun.csm.dao.entity.dict.DictTown;
import com.msun.csm.model.dto.DictTownDTO;
import com.msun.csm.model.vo.DictTownVO;

/**
 * <AUTHOR>
 */
@Mapper (componentModel = "spring")
public interface DictTownConvert extends
        Dto2PoBaseConvert<DictTownDTO, DictTown>, Dto2VoBaseConvert<DictTownDTO, DictTownVO>,
        Vo2PoBaseConvert<DictTownVO, DictTown> {
}
