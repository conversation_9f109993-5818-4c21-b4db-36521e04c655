package com.msun.csm.model;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @fileName:
 * @author:<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @updateBy:
 * @Date:Created in 14:41 2024/5/22
 * @remark:
 */
@ApiModel(description = "项目验收接收类")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectAcceptanceRecordParam {

    /**
     * 客户ID
     */
    private Long customInfoId;

    /**
     * 工单编号
     */
    private String projectNumber;

    /**
     * 开始时间，格式为 yyyy-MM-dd HH:mm:ss
     */
    @NotNull(message = "参数【startTime】不可为null")
    private String startTime;

    /**
     * 结束时间，格式为 yyyy-MM-dd HH:mm:ss
     */
    @NotNull(message = "参数【endTime】不可为null")
    private String endTime;

}
