package com.msun.csm.model.resp.project;

import java.util.List;

import com.msun.csm.common.model.BaseIdNameResp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FindProjectParamResp {

    //项目阶段
    private List<BaseIdNameResp> projectDeliverStatus;

    //工单数据
    private List<String> projectNumbers;

    //产品数据
    private List<BaseIdNameResp> orderProducts;
}
