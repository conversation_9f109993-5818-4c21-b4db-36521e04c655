package com.msun.csm.model.param;

import javax.validation.constraints.NotNull;

import lombok.Data;

@Data
public class BaseDataValidateParam {

    /**
     * 查询类型：1-药库/材料库；2-药房；3-二级库
     */
    @NotNull
    private String type;

    /**
     * 项目ID
     */
    @NotNull
    private Long projectInfoId;

    /**
     * 医院信息ID
     */
    private Long hospitalInfoId;

    /**
     * 是否只查看存在差异的数据：1-是，其他值或不传默认查看全部数据
     */
    private Boolean onlyDifference;

    /**
     * 客户信息ID
     */
    @NotNull
    private Long customInfoId;


    /**
     * 前端请求老HIS的药库材料库接口之后的结果：/api/QueryStorageTotal
     */
    private String yaoKuResult;

    /**
     *前端请求老HIS的药房接口之后的结果：/api/QueryPharmacyTotal?deptType=1
     */
    private String yaoFangResult;

    /**
     * 前端请求老HIS的二级库接口之后的结果：/api/QueryPharmacyTotal?deptType=2
     */
    private String erJiKuResult;
}
