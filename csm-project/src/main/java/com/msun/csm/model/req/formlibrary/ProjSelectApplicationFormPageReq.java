package com.msun.csm.model.req.formlibrary;

import javax.validation.constraints.NotNull;

import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.dto.BasePageDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@ApiModel(description = "表单选择查询参数")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjSelectApplicationFormPageReq extends BasePageDTO {
    private static final long serialVersionUID = 1L;
    /**
     * 医院id
     */
    @ApiModelProperty(value = "医院id")
    private Long hospitalInfoId;

    /**
     * 医院id
     */
    @ApiModelProperty(value = "项目id")
    @NotNull(message = "项目id不能为空")
    private Long projectInfoId;

    /**
     * 报表名称
     */
    @ApiModelProperty(value = "报表名称")
    private String formPatternName;
}
