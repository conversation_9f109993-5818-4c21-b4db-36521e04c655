package com.msun.csm.model.dto;

import com.msun.csm.common.model.dto.BasePageDTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/26/18:24
 */
@Data
public class ProjectMemberInsertToParamDTO extends BasePageDTO {

    @ApiModelProperty ("是否选择本团队成员【0：否；1：是】")
    private Integer isSelectTeamFlag;

    @ApiModelProperty ("人员名称")
    private String userName;
}
