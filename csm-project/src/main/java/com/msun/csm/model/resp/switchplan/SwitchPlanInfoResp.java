package com.msun.csm.model.resp.switchplan;

import java.util.List;

import com.msun.csm.common.model.BaseIdNameResp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/9/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SwitchPlanInfoResp {

    /**
     * 项目id
     */
    private Long projectInfoId;

    /**
     * 项目类型列表
     */
    private List<BaseIdNameResp> projectTypeList;

    /**
     * 升级类型(实施类型)列表
     */
    private List<BaseIdNameResp> upgradationTypeList;

    /**
     * 切换类型列表  新老并行？一刀切？
     */
    private List<BaseIdNameResp> switchTypeList;

    /**
     * 原系统停机时间
     */
    private String oldSysStopTime = "";

    /**
     * 新系统启动时间
     */
    private String newSysStartTime = "";

    /**
     * 项目类型
     */
    private Integer projectType;

    /**
     * 升级类型
     */
    private Integer upgradationType;

    /**
     * 切换类型
     */
    private Integer switchType;

    /**
     * 是否已经上传了文件
     */
    private boolean uploadStatus = false;

    /**
     * 文件链接
     */
    private String filePath = "";


}
