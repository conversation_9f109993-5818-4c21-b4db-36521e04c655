package com.msun.csm.model.dto.autotest.feign;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 批量执行测试用
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskBatchRunDTO {

    /**
     * 医院合集
     */
    @ApiModelProperty(value = "医院合集")
    private List<TaskBatchRunHospitalInfoDTO> hospitalInfoList;

}
