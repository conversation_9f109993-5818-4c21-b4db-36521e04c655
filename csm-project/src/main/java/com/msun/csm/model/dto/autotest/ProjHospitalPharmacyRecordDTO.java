package com.msun.csm.model.dto.autotest;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import com.msun.csm.common.model.BaseIdNameResp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-02-20 06:47:42
 */

@Data
public class ProjHospitalPharmacyRecordDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 门诊西药房
     */
    @ApiModelProperty(value = "门诊西药房")
    @NotNull(message = "门诊西药房不能为空")
    private BaseIdNameResp enOutPharmacy;

    /**
     * 门诊中药房
     */
    @ApiModelProperty(value = "门诊中药房")
    @NotNull(message = "门诊中药房不能为空")
    private BaseIdNameResp cnOutPharmacy;

    /**
     * 住院西药房
     */
    @ApiModelProperty(value = "住院西药房")
    @NotNull(message = "住院西药房不能为空")
    private BaseIdNameResp enInPharmacy;

    /**
     * 住院中药房
     */
    @ApiModelProperty(value = "住院中药房")
    @NotNull(message = "住院中药房不能为空")
    private BaseIdNameResp cnInPharmacy;

    /**
     * 云健康医院id
     */
    @ApiModelProperty(value = "云健康医院id")
    @NotNull(message = "云健康医院id不能为空")
    private Long cloudHospitalId;

    /**
     * 医院名称
     */
    @ApiModelProperty(value = "医院名称")
    @NotNull(message = "医院名称")
    private String hospitalName;

    /**
     * 域名
     */
    @ApiModelProperty(value = "域名")
    @NotNull(message = "域名")
    private String cloudDomain;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    @NotNull(message = "客户id不能为空")
    private Long customInfoId;
}
