package com.msun.csm.model.req.projform;

import java.util.List;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@ApiModel(description = "打印报表使用众阳设计")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class PrintReportLimitDataReq {
    private static final long serialVersionUID = 1L;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户信息ID")
    private Long customerInfoId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

    /**
     * 是否强制开启众阳设计器 1锐浪模板  2众阳模板 3手动选择
     */
    @ApiModelProperty(value = "是否强制开启众阳设计器 1锐浪模板  2众阳模板 3手动选择")
    private Integer reportLimitFlag;

    /**
     * 项目类型
     */
    @ApiModelProperty(value = "项目类型")
    private Integer projectType;

    @ApiModelProperty(value = "医院id 集合")
    private List<Long> hospitalInfoIds;
}
