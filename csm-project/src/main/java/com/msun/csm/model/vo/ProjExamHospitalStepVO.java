package com.msun.csm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ProjExamHospitalStepVO {

    /**
     * 步骤业务ID
     */
    @ApiModelProperty("步骤业务ID")
    private Long stepId;

    /**
     * 步骤业务说明
     */
    @ApiModelProperty("步骤业务说明")
    private String stepName;

    /**
     * 步骤执行顺序
     */
    @ApiModelProperty("步骤执行顺序")
    private Integer stepOrder;

    /**
     * 步骤业务编码
     */
    @ApiModelProperty("步骤业务编码")
    private String stepCode;

    /**
     * 步骤执行状态：0未执行，1执行中，2已执行
     */
    @ApiModelProperty("步骤执行状态：0未执行，1执行中，2已执行")
    private Integer stepState;
}
