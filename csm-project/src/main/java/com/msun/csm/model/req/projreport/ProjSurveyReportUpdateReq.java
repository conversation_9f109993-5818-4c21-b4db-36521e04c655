package com.msun.csm.model.req.projreport;

import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;
import com.msun.csm.dao.entity.proj.ProjProjectFile;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@ApiModel(description = "报表数据")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjSurveyReportUpdateReq extends BasePO {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    @ApiModelProperty(value = "主键")
    private Long surveyReportId;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户信息ID")
    private Long customerInfoId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

    /**
     * 医院id
     */
    @ApiModelProperty(value = "医院id")
    private Long hospitalInfoId;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private Long yyProductId;

    /**
     * 模块id
     */
    @ApiModelProperty(value = "模块id")
    private Long yyModuleId;

    /**
     * 报表名称
     */
    @ApiModelProperty(value = "报表名称")
    private String reportName;

    /**
     * 上线必备 0： 否 1: 是
     */
    @ApiModelProperty(value = "上线必备 0： 否 1: 是")
    private Integer onlineEssential;

    /**
     * 完成状态：
     */
    @ApiModelProperty(value = "完成状态：")
    private Integer finishStatus;

    /**
     * 调研收集的打印样式路径，多个样式名称应使用英文逗号（,）分隔（proj_project_file表project_file_id集）
     */
    @ApiModelProperty(value = "调研收集的打印样式路径，多个样式名称应使用英文逗号（,）分隔")
    private String surveyImgs;

    /**
     * 补充图片
     */
    @ApiModelProperty(value = "补充图片")
    private String supplementImgs;

    /**
     * 完成结果图片
     */
    @ApiModelProperty(value = "完成结果图片")
    private String finishImgs;

    /**
     * 打印节点
     */
    @ApiModelProperty(value = "打印节点")
    private String printDataCode;

    /**
     * 报表标识用于报表平台中指定具体要处理的报表
     */
    @ApiModelProperty(value = "报表标识用于报表平台中指定具体要处理的报表")
    private String reportFileTag;

    /**
     * 报表制作平台支持预览和跳转功能，当前实现中包括锐浪和众阳两种报表类型
     */
    @ApiModelProperty(value = "报表制作平台支持预览和跳转功能，当前实现中包括锐浪和众阳两种报表类型")
    private String reportMakePlatform;

    /**
     * 审核意见
     */
    @ApiModelProperty(value = "审核意见")
    private String examineOpinion;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "调研收集的打印样式路径")
    List<ProjProjectFile> surveyImgsList;
    @ApiModelProperty(value = "完成结果图片")
    List<ProjProjectFile> finishImgsList;

    @ApiModelProperty(value = "打印纸张大小")
    private String reportPaperSize;

    /**
     * 是否是默认选项 1默认 0 非默认
     */
    private Integer defaultFlag;
}
