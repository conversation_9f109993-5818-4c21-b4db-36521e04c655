package com.msun.csm.model.convert;

import org.mapstruct.Mapper;

import com.msun.csm.common.model.convert.Dto2PoBaseConvert;
import com.msun.csm.common.model.convert.Dto2VoBaseConvert;
import com.msun.csm.common.model.convert.Vo2PoBaseConvert;
import com.msun.csm.dao.entity.proj.ProjProductBacklog;
import com.msun.csm.model.dto.ProjProductBacklogDTO;
import com.msun.csm.model.vo.ProjProductBacklogVO;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/07/25/15:56
 */

@Mapper (componentModel = "spring")
public interface ProjProductBacklogConvert extends
        Dto2PoBaseConvert<ProjProductBacklogDTO, ProjProductBacklog>,
        Dto2VoBaseConvert<ProjProductBacklogDTO, ProjProductBacklogVO>,
        Vo2PoBaseConvert<ProjProductBacklogVO, ProjProductBacklog> {
}
