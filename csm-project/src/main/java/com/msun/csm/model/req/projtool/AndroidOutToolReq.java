package com.msun.csm.model.req.projtool;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 配置表(SysConfig)实体类
 *
 * <AUTHOR>
 * @since 2024-05-13 16:37:40
 */
@ApiModel(description = "叫号安卓app下载")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class AndroidOutToolReq {
    /**
     * 业务主键
     */
    @ApiModelProperty(value = "云健康医院id")
    private Long cloudHospitalId;

    @ApiModelProperty(value = "机构id")
    private Long orgId;

    @ApiModelProperty(value = "下载地址")
    private String url;

    @ApiModelProperty(value = "包版本")
    private String packVersion;

}
