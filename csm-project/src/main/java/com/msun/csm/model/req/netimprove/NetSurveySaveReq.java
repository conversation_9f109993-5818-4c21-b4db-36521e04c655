package com.msun.csm.model.req.netimprove;

import java.util.List;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NetSurveySaveReq {

    /**
     * 项目id
     */
    @NotNull(message = "projectInfoId不能为空")
    private Long projectInfoId;

    /**
     * 网络改造调研结果
     */
    @NotNull(message = "surveyResultList不能为空")
    private List<NetSurveyResult> surveyResultList;
}
