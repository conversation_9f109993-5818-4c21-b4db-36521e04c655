package com.msun.csm.model.req.projform;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.msun.csm.common.model.po.BasePO;
import com.msun.csm.dao.entity.proj.ProjProjectFile;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 表单主表
 *
 * @TableName proj_survey_form
 */
@Data
public class ProjSurveyFormAddReq extends BasePO implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "调研收集的打印样式路径")
    List<ProjProjectFile> surveyImgsList;
    @ApiModelProperty(value = "完成结果图片")
    List<ProjProjectFile> finishImgsList;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long surveyFormId;
    /**
     * 客户id(proj_customer_info)
     */
    @ApiModelProperty(value = "客户id(proj_customer_info)")
    private Long customerInfoId;
    /**
     * 项目id(proj_project_info)
     */
    @ApiModelProperty(value = "项目id(proj_project_info)")
    private Long projectInfoId;
    /**
     * 医院编号(proj_hospital_info)（交付平台id）
     */
    @ApiModelProperty(value = "医院编号(proj_hospital_info)（交付平台id）")
    private Long hospitalInfoId;
    /**
     * 产品id， 区分哪个产品(dict_product)
     */
    @ApiModelProperty(value = "产品id， 区分哪个产品(dict_product)")
    private Long yyProductId;
    /**
     * 模块id(dict_product_vs_modules)
     */
    @ApiModelProperty(value = "模块id(dict_product_vs_modules)")
    private Long yyModuleId;
    /**
     * 表单名称
     */
    @ApiModelProperty(value = "表单名称")
    private String formName;
    /**
     * 上线必备 0： 否 1: 是
     */
    @ApiModelProperty(value = "上线必备 0： 否 1: 是")
    private Integer onlineEssential;
    /**
     * 完成状态：
     * 0: 未完成
     * 1: 客服提交审核   （已提交审核）
     * 2: 项目经理审核驳回  （未通过）
     * 3: 项目经理审核通过（已通过）
     */
    @ApiModelProperty(value = "完成状态：0: 未完成1: 客服提交审核   （已提交审核）")
    private Integer finishStatus;
    /**
     * 调研收集的样式路径，多个样式名称应使用英文逗号（,）分隔（proj_project_file表project_file_id集）
     */
    @ApiModelProperty(value = "调研收集的样式路径，多个样式名称应使用英文")
    private String surveyImgs;
    /**
     * 预留后期补充调研表单图片（proj_project_file表project_file_id集）
     */
    @ApiModelProperty(value = "预留后期补充调研表单图片")
    private String supplementImgs;
    /**
     * 在客服完成操作后上传的图片路径，多个图片路径应使用英文逗号（,）分隔；（proj_project_file表project_file_id集）
     */
    @ApiModelProperty(value = "在客服完成操作后上传的图片路径，")
    private String finishImgs;
    /**
     * 表单分类（dict_form_type 表 type_code）
     */
    @ApiModelProperty(value = "表单分类（dict_form_type 表 type_code）")
    private String typeCode;
    /**
     * 调研负责人
     */
    @ApiModelProperty(value = "调研负责人")
    private Long surveyUserId;
    /**
     * 调研完成时间
     */
    @ApiModelProperty(value = "调研完成时间")
    private Timestamp surveyFinishTime;
    /**
     * 制作负责人
     */
    @ApiModelProperty(value = "制作负责人")
    private Long makeUserId;
    /**
     * 提交制作完成时间
     */
    @ApiModelProperty(value = "确认完成时间")
    private Timestamp commitFinishTime;
    /**
     * 确认完成时间
     */
    @ApiModelProperty(value = "确认完成时间")
    private Timestamp makeFinishTime;
    /**
     * 表单来源（（产品调研、老系统、新增)）
     */
    @ApiModelProperty(value = "表单来源（（cpdy: 产品调研、lxt:老系统、xz:新增)）")
    private String formSource;
    /**
     * 临时存储跳转路径
     */
    @ApiModelProperty(value = "临时存储跳转路径")
    private String formPageUrl;
    /**
     * （云健康产品编码）
     */
    @ApiModelProperty(value = "（云健康产品编码）")
    private String cloudProductCode;
    /**
     * 审核意见
     */
    @ApiModelProperty(value = "审核意见")
    private String examineOpinion;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 逻辑删除【0：否；1：是】
     */
    @ApiModelProperty(value = "逻辑删除【0：否；1：是】")
    private Integer isDeleted;
    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private Long createrId;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;
    /**
     * 更新人id
     */
    @ApiModelProperty(value = "更新")
    private Long updaterId;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;
    @ApiModelProperty(value = "制作责任人")
    private String makeUserName;
    @ApiModelProperty(value = "是否是设计制作")
    private Boolean isDesignMakeFlag;
    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "来源")
    private String formSourceStr;
}