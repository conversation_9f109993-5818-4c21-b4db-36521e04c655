package com.msun.csm.model.vo.projfileupload;

import java.util.List;

import com.msun.csm.dao.entity.proj.ProjProjectPlanFile;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ProjectPlanFileDetailVO {

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private Long dictPlanItemFileId;

    /**
     * 项目节点编码
     */
    @ApiModelProperty("项目节点编码")
    private String planItemCode;

    /**
     * 需上传资料描述
     */
    @ApiModelProperty("需上传资料描述")
    private String fileDesc;

    /**
     * 是否必传：0否1是
     */
    @ApiModelProperty("是否必传：0否1是")
    private Short needFlag;

    /**
     * 已上传资料列表
     */
    @ApiModelProperty("已上传资料列表")
    private List<ProjProjectPlanFile> fileList;
}
