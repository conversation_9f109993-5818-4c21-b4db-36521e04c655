package com.msun.csm.model.dto;

import java.util.Date;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/05/22/10:24
 */
@Data
public class ProjOnlineStepDTO {

    @ApiModelProperty("实施地客户id")
    private Long customInfoId;

    @ApiModelProperty("项目id")
    private Long projectInfoId;

    @ApiModelProperty("项目上线步骤表主键id")
    private Long projOnlineStepId;

    @ApiModelProperty("医院id")
    private Long hospitalInfoId;

    @ApiModelProperty("医院id列表")
    private List<Long> hospitalInfoIdList;

    @ApiModelProperty("新密码")
    private String newPassword;

    @ApiModelProperty("发送开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @ApiModelProperty("发送结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endTime;
    /**
     * 授权类型 0-下发权限 1-回收权限
     */
    @ApiModelProperty(value = "授权类型 0-下发权限 1-回收权限")
    private String type;

}
