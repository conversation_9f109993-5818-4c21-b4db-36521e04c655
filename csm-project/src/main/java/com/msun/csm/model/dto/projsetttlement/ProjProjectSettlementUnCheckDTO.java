package com.msun.csm.model.dto.projsetttlement;

import com.msun.csm.common.model.dto.BasePageDTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 待审核列表请求参数
 */
@Data
public class ProjProjectSettlementUnCheckDTO extends BasePageDTO {

    @ApiModelProperty(value = "客户名称")
    private String customName;

    @ApiModelProperty(value = "客户id")
    private String customInfoId;

    @ApiModelProperty(value = "审核结果：0.不通过；1.通过")
    private Integer checkResult;
}
