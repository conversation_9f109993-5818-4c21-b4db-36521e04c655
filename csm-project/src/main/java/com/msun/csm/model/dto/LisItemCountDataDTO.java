package com.msun.csm.model.dto;


import com.alibaba.fastjson.annotation.JSONField;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LisItemCountDataDTO {

    /**
     * 云健康医院ID
     */
    @JSONField(name = "hospital_id")
    private String hospitalId;

    /**
     * 医院名称
     */
    @JSONField(name = "hospital_name")
    private String hospitalName;

    /**
     * 数量
     */
    private String total;


}
