package com.msun.csm.model.dto.mainpage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DictMainpageModuleDTO {

    /**
     * 模块字典id
     */
    @ApiModelProperty("模块字典id")
    private Long dictModuleId;

    /**
     * 模块编码
     */
    @ApiModelProperty("模块编码")
    private String moduleCode;

    /**
     * 模块名称
     */
    @ApiModelProperty("模块名称")
    private String moduleName;

    /**
     * 模块前端路由
     */
    @ApiModelProperty("模块前端路由")
    private String moduleFrontRoute;

    /**
     * 模块后端url
     */
    @ApiModelProperty("模块后端url")
    private String moduleServerUrl;

    /**
     * 数据是否分页：0否1是
     */
    @ApiModelProperty("数据是否分页：0否1是")
    private Integer pageFlag;
}
