package com.msun.csm.model.param;

import javax.validation.constraints.NotNull;

import lombok.Data;

@Data
public class InpatientDataValidateParam {

    /**
     * 项目ID
     */
    @NotNull
    private Long projectInfoId;

    /**
     * 医院信息ID
     */
    private Long hospitalInfoId;

    /**
     * 是否只查看存在差异的数据：1-是，其他值或不传默认查看全部数据
     */
    private Boolean onlyDifference;

    /**
     * 客户信息ID
     */
    @NotNull
    private Long customInfoId;

    /**
     * 患者姓名
     */
    private String name;

    /**
     * 当前页码
     */
    private Integer pageNum;

    /**
     * 每页数据个数
     */
    private Integer pageSize;

    /**
     * 前端请求老HIS的在院患者费用金接口之后的结果：/api/QueryInPatDetail
     */
    private String zaiYuanResult;

}
