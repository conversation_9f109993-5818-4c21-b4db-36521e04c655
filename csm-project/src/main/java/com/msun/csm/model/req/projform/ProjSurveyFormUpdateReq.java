package com.msun.csm.model.req.projform;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@ApiModel(description = "修改删除数据")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjSurveyFormUpdateReq {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long surveyFormId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

    /**
     * 医院id
     */
    @ApiModelProperty(value = "医院id")
    private Long hospitalInfoId;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private Long yyProductId;

    /**
     * 模块id
     */
    @ApiModelProperty(value = "模块id")
    private Long yyModuleId;

    /**
     * 报表名称
     */
    @ApiModelProperty(value = "表单名称")
    private String formName;

    /**
     * 上线必备 0： 否 1: 是
     */
    @ApiModelProperty(value = "上线必备 0： 否 1: 是")
    private Integer onlineEssential;

    /**
     * 完成状态：
     */
    @ApiModelProperty(value = "完成状态：")
    private Integer finishStatus;

    /**
     * 逻辑删除【0：否；1：是】
     */
    @ApiModelProperty(value = "逻辑删除【0：否；1：是】")
    private Integer isDeleted;

    /**
     * 审核意见
     */
    @ApiModelProperty(value = "审核意见")
    private String examineOpinion;
}
