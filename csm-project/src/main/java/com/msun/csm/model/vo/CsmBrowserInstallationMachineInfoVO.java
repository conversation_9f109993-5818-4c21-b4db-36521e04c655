package com.msun.csm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version : V1.52.0
 * @ClassName: CsmBrowserInstallationVO
 * @Description:
 * @Author: Yhongmin
 * @Date: 13:58 2024/9/19
 */
@Data
public class CsmBrowserInstallationMachineInfoVO {
    @ApiModelProperty("操作系统")
    private String system;
    @ApiModelProperty("内存")
    private String memoryTotal;

    @ApiModelProperty("系统类型（32位还是64位）")
    private String systemType;

    @ApiModelProperty("机器名称")
    private String machineName;

    @ApiModelProperty("物理核心")
    private String physicalCores;

    @ApiModelProperty("逻辑核心")
    private String logicalCores;


    @ApiModelProperty("操作系统版本")
    private String systemVersion;

    @ApiModelProperty("锐浪版本")
    private String rubylongInstalledVersions;

    @ApiModelProperty("miv版本")
    private String mivInstalledVersions;


}
