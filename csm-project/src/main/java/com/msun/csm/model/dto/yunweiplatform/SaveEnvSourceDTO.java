package com.msun.csm.model.dto.yunweiplatform;

import java.util.List;

import com.msun.csm.dao.entity.proj.ProjApplyOrderProduct;
import com.msun.csm.dao.entity.proj.ProjHospitalInfoRelative;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 保存数据库查询原值
 * 首次创建用于 部署申请时对查询出的内容较完整的需要部署的医院和产品信息进行保存
 */
@Data
@AllArgsConstructor
public class SaveEnvSourceDTO {

    List<ProjHospitalInfoRelative> hospitals;

    List<ProjApplyOrderProduct> applyOrderProducts;
}
