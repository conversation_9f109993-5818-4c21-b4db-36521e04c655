package com.msun.csm.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * AI智能体场景配置删除请求参数
 * <AUTHOR> @date 2025/07/07
 */
@Data
@ApiModel(description = "AI智能体场景配置删除请求参数")
public class ConfigAgentScenarioDictDeleteReq {

    @ApiModelProperty(value = "配置ID", required = true, example = "1")
    @NotNull(message = "配置ID不能为空")
    private Long agentScenarioConfigId;
}
