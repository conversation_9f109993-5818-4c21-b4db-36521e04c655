package com.msun.csm.model.vo.product;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.msun.csm.model.vo.ProjProductSupplementaryRecordVO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version : V1.52.0
 * @ClassName: InitBackRecordProductVO
 * @Description:
 * @Author: Yhongmin
 * @Date: 15:02 2024/7/4
 */
@Data
public class InitProductSupplementaryRecordVO {
    @ApiModelProperty("表格数据")
    private PageInfo<ProjProductSupplementaryRecordVO> supplementaryRecordList;
    @ApiModelProperty("客户的选项")
    private List<DictSupplementaryRecord> customInfoList;
    @ApiModelProperty("特批类型：1:工单产品;2:特批产品;")
    private List<DictSupplementaryRecord> specialApprovalTypeList;
    @ApiModelProperty("购买模式")
    private List<DictSupplementaryRecord> purchaseModeList;
    @ApiModelProperty("特批产品")
    private List<DictSupplementaryRecord> productList;

    @Data
    public static class DictSupplementaryRecord {
        /**
         * 编码
         */
        private String id;

        /**
         * 名称
         */
        private String name;
    }
    @Data
    public static class DictSupplementaryPath {
        /**
         *
         */
        private String id;

        /**
         * 名称
         */
        private String name;
        /**
         * 名称
         */
        private String url;
    }

}
