package com.msun.csm.model.dto;

import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-05-10 11:17:54
 */

@Data
public class ProjMilestoneTaskDTO {
    private static final long serialVersionUID = 1L;

    /**
     * 项目里程碑任务计划主键
     */
    @ApiModelProperty (value = "项目里程碑任务计划主键")
    private Long milestoneTaskId;

    /**
     * 医院信息id
     */
    @ApiModelProperty (value = "医院信息id")
    private Long hospitalInfoId;

    /**
     * 客户id
     */
    @ApiModelProperty (value = "客户id")
    private String customerInfoId;

    /**
     * 项目id
     */
    @ApiModelProperty (value = "项目id")
    private String projectInfoId;

    /**
     * 里程碑节点任务code
     */
    @ApiModelProperty (value = "里程碑节点任务code")
    private String milestoneNodeCode;

    /**
     * 主负责人id
     */
    @ApiModelProperty (value = "主负责人id")
    private Long leaderId;

    /**
     * 辅助负责人id(逗号分割存储多个负责人id)
     */
    @ApiModelProperty (value = "辅助负责人id(逗号分割存储多个负责人id)")
    private String secondLeaderId;

    /**
     * 计划开始时间
     */
    @ApiModelProperty (value = "计划开始时间")
    private Date expectStartTime;

    /**
     * 计划结束时间
     */
    @ApiModelProperty (value = "计划结束时间")
    private Date expectCompTime;

    /**
     * 逻辑删除【0：否；1：是】
     */
    @ApiModelProperty (value = "逻辑删除【0：否；1：是】")
    private Integer isDeleted;

    /**
     * 创建人id
     */
    @ApiModelProperty (value = "创建人id")
    private Long createrId;

    /**
     * 创建时间
     */
    @ApiModelProperty (value = "创建时间")
    private Date createTime;

    /**
     * 更新人id
     */
    @ApiModelProperty (value = "更新人id")
    private Long updaterId;

    /**
     * 更新时间
     */
    @ApiModelProperty (value = "更新时间")
    private Date updateTime;

    /**
     * 结果来源id. 0:现场调研。
     */
    @ApiModelProperty (value = "结果来源id. 0:现场调研。")
    private Long resultSourceId;

    /**
     * 完成状态，0未完成，1已完成
     */
    @ApiModelProperty (value = "完成状态，0未完成，1已完成")
    private Integer completeStatus;

    /**
     * 项目里程碑节点ID
     */
    @ApiModelProperty (value = "项目里程碑节点ID")
    private Long milestoneInfoId;

    /**
     * 项目阶段id
     */
    @ApiModelProperty (value = "项目阶段id")
    private Long projectStageId;

    /**
     * 项目阶段code
     */
    @ApiModelProperty (value = "项目阶段code")
    private String projectStageCode;

    /**
     * 实际完成时间
     */
    @ApiModelProperty (value = "实际完成时间")
    private Date actualCompTime;

    @ApiModelProperty (value = "辅助负责人集合")
    private List<String> planSecondLeaders;
    /**
     * 调研计划范围值
     */
    @ApiModelProperty (value = "调研计划范围值")
    private List<String> planTimeRange;
    /**
     * 片区名称
     */
    @ApiModelProperty(value = "片区名称")
    private String sectionName;
    /**
     * 医院名称
     */
    @ApiModelProperty(value = "医院名称")
    private String hospitalName;

}
