package com.msun.csm.model.req.projreport.statis;

import java.sql.Timestamp;
import java.util.List;

import javax.validation.constraints.NotNull;

import com.msun.csm.dao.entity.report.statis.ProjStatisticalCalibrationEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 统计报表主表(PROJ_STATISTICAL_REPORT_MAIN)
 *
 * <AUTHOR>
 * @version 1.0.0 2025-01-13
 */

@Data
@ApiModel
public class ProjStatisticalReportMainAddReq {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long statisticalReportMainId;

    /**
     * 客户id
     */
    @NotNull(message = "客户id不能为空")
    @ApiModelProperty(value = "客户id")
    private Long customInfoId;

    /**
     * 项目id
     */
    @NotNull(message = "项目id不能为空")
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

    /**
     * 报表名称： 字符串
     */
    @ApiModelProperty(value = "报表名称")
    @NotNull(message = "报表名称不能为空")
    private String reportName;

    @ApiModelProperty(value = "统计口径")
    @NotNull(message = "统计口径不能为空")
    private List<ProjStatisticalCalibrationEntity> calibrationEntities;

    /**
     * "报表样式，存储到sys_file的主键，多个以','分割。
     * 通过file_path 获取到路径对应的图片"
     */
    @ApiModelProperty(value = "报表样式")
    private String reportStyle;

    @ApiModelProperty(value = "报表样式")
    @NotNull(message = "报表样式不能为空")
    private List<ProjFileReq> reportStyleList;

    /**
     * 制作方式： 下拉（当前4种）
     */
    @ApiModelProperty(value = "制作方式")
    private Long productionMethodId;

    /**
     * "状态（枚举）：
     * 1待申请裁定、
     * 11裁定中、12裁定驳回、13裁定通过
     * 21制作中、
     * 31已下沉"
     */
    @ApiModelProperty(value = "状态 保存传1 保存并裁定传11")
    private Integer reportStatus;

    /**
     * 使用科室 id： 关联 dict_hospital_dept 主键
     */
    @ApiModelProperty(value = "使用科室id")
    private Long hospitalDeptId;

    /**
     * 使用科室名称 ：
     */
    @ApiModelProperty(value = "使用科室名称")
    @NotNull(message = "使用科室名称不能为空")
    private String hospitalDeptName;

    /**
     * 用途分类id 关联dict_report_purpose 主键
     */
    @ApiModelProperty(value = "用途分类id")
    private Long reportPurposeId;

    /**
     * 用途分类名称
     */
    @ApiModelProperty(value = "用途分类名称")
    @NotNull(message = "用途分类名称不能为空")
    private String reportPurposeName;

    /**
     * 使用频次id dict_report_frequency 主键
     */
    @ApiModelProperty(value = "使用频次id")
    @NotNull(message = "使用频次id不能为空")
    private Long reportFrequencyId;

    /**
     * 使用频次名称
     */
    @ApiModelProperty(value = "使用频次名称")
    @NotNull(message = "使用频次名称不能为空")
    private String reportFrequencyName;

    /**
     * 上线必备（1是 0否） 默认1
     */
    @ApiModelProperty(value = "上线必备（1是 0否） 默认1")
    private Integer onlineFlag;

    /**
     * 统计报表备注
     */
    @ApiModelProperty(value = "统计报表备注")
    private String remarks;

    /**
     * 调研人
     */
    @ApiModelProperty(value = "调研人")
    private Long surveyUserId;

    /**
     * 调研时间
     */
    @ApiModelProperty(value = "调研时间")
    private Timestamp surveyTime;

    @ApiModelProperty(value = "医院信息ID")
    private Long hospitalInfoId;

    @ApiModelProperty(value = "挂载路径")
    private String mountPath;

    @ApiModelProperty(value = "运维平台唯一标识")
    private String operationStatisticsReportId;

    @ApiModelProperty(value = "保存并申请裁定 0 保存/1保存并申请裁定")
    private Integer saveAndApplyAdjudicationFlag;
}