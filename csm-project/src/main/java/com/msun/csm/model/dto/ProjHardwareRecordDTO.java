package com.msun.csm.model.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* @description:
* @fileName: ProjHardwareRecordDTO.java
* @author: lius3
* @createAt: 2024/11/14 13:57
* @updateBy: lius3
* @remark: Copyright
*/
@Data
public class ProjHardwareRecordDTO {

    /**
     * 记录ID
     */
    @ApiModelProperty("记录ID")
    private Long hardwareRecordId;

    /**
     * 客户ID
     */
    @ApiModelProperty("客户ID")
    private Long customInfoId;

    /**
     * 项目ID
     */
    @ApiModelProperty("项目ID")
    private Long projectInfoId;

    /**
     * 医院ID
     */
    @ApiModelProperty(value = "医院ID")
    private Long hospitalInfoId;

    /**
     * 小硬件字典ID
     */
    @ApiModelProperty(value = "小硬件字典ID")
    private Long hardwareInfoId;

    /**
     * 小硬件名称
     */
    @ApiModelProperty(value = "小硬件名称")
    private String hardwareName;

    /**
     * 采购属性：1需采购，2利旧，3不开展业务
     */
    @ApiModelProperty(value = "采购属性：1需采购，2利旧，3不开展业务")
    private Integer purchaseProperty;

    /**
     * 产品ID
     */
    @ApiModelProperty(value = "产品ID")
    private List<Long> yyProductIds;

    /**
     * 现有型号
     */
    @ApiModelProperty(value = "现有型号")
    private String currentModel;

    /**
     * 现有数量
     */
    @ApiModelProperty(value = "现有数量")
    private Integer currentAmount;

    /**
     * 采购型号
     */
    @ApiModelProperty(value = "采购型号")
    private String purchaseModel;

    /**
     * 采购数量
     */
    @ApiModelProperty(value = "采购数量")
    private Integer purchaseAmount;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String memo;
}
