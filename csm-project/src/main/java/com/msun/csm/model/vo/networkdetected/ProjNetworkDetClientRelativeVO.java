package com.msun.csm.model.vo.networkdetected;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-05-16 04:01:06
 */

@Data
public class ProjNetworkDetClientRelativeVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty (value = "主键")
    private String id;

    /**
     * 医院id
     */
    @ApiModelProperty (value = "医院id")
    private String hospitalInfoId;

    /**
     * 随机码
     */
    @ApiModelProperty (value = "随机码 ( 区分每一台电脑不同轮次的测试 )")
    private String randomCode;

    /**
     * 终端ip
     */
    @ApiModelProperty (value = "终端ip")
    private String localIpAddress;

    /**
     * 医院名称
     */
    @ApiModelProperty (value = "医院名称")
    private String hospitalName;

    /**
     * 主表id(表:医院网络检测情况一览表)
     */
    @ApiModelProperty (value = "主表id(表:医院网络检测情况一览表)")
    private String detHospitalId;

    /**
     * 检测情况
     */
    @ApiModelProperty (value = "检测情况")
    private int detectStatus;
}
