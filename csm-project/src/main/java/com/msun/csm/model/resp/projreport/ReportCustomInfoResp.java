package com.msun.csm.model.resp.projreport;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.msun.csm.dao.entity.report.ReportCustomInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 客户数据统计表(ReportCustomInfo)实体类
 *
 * <AUTHOR>
 * @since 2024-09-27 15:27:17
 */
@ApiModel(description = "菜单")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ReportCustomInfoResp extends ReportCustomInfo {
    @ApiModelProperty("客户类型")
    private String customTypeName;
    @ApiModelProperty("项目类型")
    private String projectTypeName;
    @ApiModelProperty("销售部门")
    private String saleDeptName;
    @ApiModelProperty("客服部门")
    private String customDeptName;
    @ApiModelProperty("客服分公司")
    private String implementationDeptName;

    @ApiModelProperty("派工时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date workTime;

    // external_accept_time
    @ApiModelProperty("外验时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date externalAcceptTime;

    @ApiModelProperty("上线时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date onlineTime;

    // apply_accept_time
    @ApiModelProperty("提交验收申请时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyAcceptTime;
}
