package com.msun.csm.model.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;
/**
 * @Description:
 * @Author: zd
 * @Date: 2024/10/10
 */

/**
 * 设备字典
 */
@ApiModel(description = "智能体场景配置表")
@Data
@TableName(schema = "csm")
public class DictAgentChatScenarioConfigResp extends BasePO {

    @ApiModelProperty(value = "主键")
    private Long agentScenarioConfigId;

    @ApiModelProperty(value = "智能体编号")
    private String agentCode;

    @ApiModelProperty(value = "应用场景编码")
    private String scenarioCode;

    @ApiModelProperty(value = "应用场景描述")
    private String scenarioDesc;

    @ApiModelProperty(value = "应用场景提示词")
    private String scenarioPrompt;

    @ApiModelProperty(value = "智能体字典表")
    private Long agentChatId;

    @ApiModelProperty(value = "智能体名称")
    private String agentName;

    @ApiModelProperty(value = "智能体测试地址")
    private String agentAddress;
    @ApiModelProperty(value = "智能体生产地址")
    private String agentAddressProduce;
    @ApiModelProperty(value = "智能体密钥")
    private String agentKey;
}
