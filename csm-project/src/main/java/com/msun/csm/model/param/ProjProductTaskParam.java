package com.msun.csm.model.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 产品待处理任务表
 *
 * @TableName proj_product_task
 */
@Data
public class ProjProductTaskParam {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long productTaskId;

    /**
     * 调研标题
     */
    @ApiModelProperty(value = "调研标题")
    private String surveyTitle;

    /**
     * 运营平台产品ID
     */
    @ApiModelProperty(value = "运营平台产品ID")
    private Long yyProductId;

    /**
     * 交付平台区分每一家医院的唯一标识 proj_hospital_info 表id
     */
    @ApiModelProperty(value = "交付平台区分每一家医院的唯一标识")
    private Long hospitalInfoId;

    /**
     * 项目信息ID
     */
    @ApiModelProperty(value = "项目信息ID")
    private Long projectInfoId;

    /**
     * 完成结果 0：未完成 1：已完成
     */
    @ApiModelProperty (value = "完成结果 0：未完成 1：已完成;2: 检测失败")
    private Integer taskStatus;

    /**
     *  完成结果， 用于不等与该值的判断逻辑
     */
    @ApiModelProperty (value = "完成结果 0：未完成 1：已完成;2: 检测失败")
    private Integer taskStatusNot;

    /**
     * 检测任务类型
     */
    @ApiModelProperty(value = "检测任务类型")
    private String taskValidateType;

    @ApiModelProperty(value = "云健康")
    private String cloudProductCode;

    @ApiModelProperty(value = "路径")
    private String taskPageUrl;

    @ApiModelProperty(value = "数据传参,用于跳转云健康数据传值")
    private String data;
}