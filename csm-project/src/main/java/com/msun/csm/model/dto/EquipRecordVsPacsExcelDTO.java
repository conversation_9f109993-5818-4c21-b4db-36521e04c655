package com.msun.csm.model.dto;

import javax.validation.constraints.NotBlank;

import com.alibaba.excel.annotation.ExcelProperty;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;



/**
* @description:
* @fileName: EquipRecordVsPacsExcelDTO.java
* @author: lius3
* @createAt: 2024/10/22 18:23
* @updateBy: lius3
* @remark: Copyright
*/
@Data
public class EquipRecordVsPacsExcelDTO {

    /**
     * 设备型号名称
     */
    @ApiModelProperty("设备型号名称")
    @ExcelProperty("设备型号")
    private String equipModelName;
    /**
     * 设备厂商名称
     */
    @ApiModelProperty ("设备厂商名称")
    @ExcelProperty("*设备厂商")
    @NotBlank(message = "设备厂商不能为空")
    private String equipFactoryName;

    /**
     * 医院名称
     */
    @ApiModelProperty("医院名称")
    @ExcelProperty("*医院名称")
    @NotBlank(message = "医院名称不能为空")
    private String hospitalName;

    /**
     * 厂商电话
     */
    @ApiModelProperty ("厂商电话")
    @ExcelProperty("厂商电话")
    private String equipFactoryPhone;

    /**
     * 设备位置
     */
    @ApiModelProperty ("设备位置")
    @ExcelProperty("设备位置")
    @NotBlank(message = "设备位置不能为空")
    private String equipPosition;

    /**
     * 是否对接
     */
    @ApiModelProperty ("是否对接")
    @ExcelProperty("*是否对接")
    @NotBlank(message = "是否对接不能为空")
    private String requiredFlagExcel;

    /**
     * 不对接原因
     */
    @ApiModelProperty ("不对接原因")
    @ExcelProperty("不对接原因")
    private String stopReason;

    /**
     * 备注说明
     */
    @ApiModelProperty ("备注说明")
    @ExcelProperty("备注说明")
    private String memo;

    /**
     * 设备ip
     */
    @ApiModelProperty(value = "设备ip")
    @ExcelProperty("设备IP")
    private String equipIp;

    /**
     * AETitle
     */
    @ApiModelProperty (value = "AETitle")
    @ExcelProperty("AETitle")
    private String aeTitle;

    /**
     * 检查模态名称
     */
    @ApiModelProperty("检查模态名称")
    @ExcelProperty("*检查模态")
    @NotBlank(message = "检查模态不能为空")
    private String equipModalName;
}
