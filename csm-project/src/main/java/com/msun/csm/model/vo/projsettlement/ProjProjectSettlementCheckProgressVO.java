package com.msun.csm.model.vo.projsettlement;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 入驻申请进度前端返回值
 * <AUTHOR>
 * @since 2024-06-17 10:49:37
 */

@Data
public class ProjProjectSettlementCheckProgressVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "当前节点名称")
    private String nodeName;

    @ApiModelProperty(value = "操作时间")
    private String operateTime;

    @ApiModelProperty(value = "审核人")
    private String operateUser;

    @ApiModelProperty(value = "审核部门")
    private String operateUserDeptName;

    @ApiModelProperty(value = "审核意见")
    private String opinion;

    @ApiModelProperty(value = "是否当前节点")
    private Boolean isCurrent;

}
