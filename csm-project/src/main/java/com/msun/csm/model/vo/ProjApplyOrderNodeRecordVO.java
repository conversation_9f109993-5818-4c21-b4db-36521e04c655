package com.msun.csm.model.vo;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-05-23 08:32:14
 */

@Data
@TableName(schema = "csm")
public class ProjApplyOrderNodeRecordVO extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long projApplyOrderNodeRecordId;

    /**
     * 节点类型编码
     */
    @ApiModelProperty(value = "节点类型编码")
    private Short nodeTypeCode;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String operator;

    private String operateContent;

    /**
     * 操作人员id
     */
    @ApiModelProperty(value = "操作人员id")
    private Long operatorId;

    /**
     * 部署申请工单id
     */
    @ApiModelProperty(value = "部署申请工单id")
    private Long applyOrderId;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String telephone;

    /**
     * 驳回原因
     */
    @ApiModelProperty(value = "驳回原因")
    private String rejectReason;

    /**
     * 拒绝原因
     */
    @ApiModelProperty(value = "拒绝原因")
    private String refusedReason;
}
