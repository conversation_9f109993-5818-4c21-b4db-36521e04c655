package com.msun.csm.model.resp.formlibrary;

import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@ApiModel(description = "表单数据")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class AimsHospitalFormResp {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "表单id")
    private Long formPatternId;
    /**
     * 表单名称
     */
    @ApiModelProperty(value = "表单名称")
    private String formPatternName;

    /**
     * 描述说明
     */
    @ApiModelProperty(value = "描述说明")
    private String description;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Long sort;

    /**
     * 表单内容
     */
    @ApiModelProperty(value = "表单内容")
    private String formContentPc;

    /**
     * 表单页面配置
     */
    @ApiModelProperty(value = "表单页面配置")
    private String formConfigurationPc;

    /**
     * 医院id
     */
    @ApiModelProperty(value = "医院id")
    private Long hospitalId;

    @ApiModelProperty(value = "医院名称")
    private String hospitalName;

    /**
     * 机构ID
     */
    @ApiModelProperty(value = "机构ID")
    private Long hisOrgId;

    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID")
    private Long hisCreaterId;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称")
    private String hisCreaterName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Timestamp hisCreateTime;

    /**
     * 更新用户ID
     */
    @ApiModelProperty(value = "更新用户ID")
    private Long hisUpdaterId;

    /**
     * 乐观锁标识
     */
    @ApiModelProperty(value = "乐观锁标识")
    private Long version;

    /**
     * 作废标识
     */
    @ApiModelProperty(value = "作废标识")
    private Integer invalidFlag;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Timestamp hisUpdateTime;

    /**
     * 流程节点code
     */
    @ApiModelProperty(value = "流程节点code")
    private String formCategoryCode;

    @ApiModelProperty(value = "流程节点名称[表单类型]")
    private String formCategoryName;

    /**
     * 平板端样式内容
     */
    @ApiModelProperty(value = "平板端样式内容")
    private String formContentPad;

    /**
     * 平板端样式格式
     */
    @ApiModelProperty(value = "平板端样式格式")
    private String formConfigurationPad;

    /**
     * 应用产品id  手麻：4050
     */
    @ApiModelProperty(value = "应用产品id  手麻：4050")
    private Long yyProductId;

    @ApiModelProperty(value = "当前项目是否已应用 0 否 1 是")
    private Integer isProjectSelect;

    /**
     * 表单内容
     */
    @ApiModelProperty(value = "表单内容web")
    private String formContentWeb;

    /**
     * 表单页面配置
     */
    @ApiModelProperty(value = "表单页面配置web")
    private String formConfigurationWeb;

}
