package com.msun.csm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* @description:
* @fileName: DeployDeviceInfoVO.java
* @author: lius3
* @createAt: 2024/11/28 14:55
* @updateBy: lius3
* @remark: Copyright
*/
@Data
public class DeployDeviceInfoVO {

    /**
     * ip
     */
    @ApiModelProperty("ip")
    private String id;

    /**
     * mac
     */
    @ApiModelProperty("mac")
    private String mac;

    /**
     * 申请人
     */
    @ApiModelProperty("申请人")
    private String applicant;

    /**
     * 申请人电话
     */
    @ApiModelProperty("申请人电话")
    private String applicantPhone;

    /**
     * 申请时间
     */
    @ApiModelProperty("申请时间")
    private String createTime;
}
