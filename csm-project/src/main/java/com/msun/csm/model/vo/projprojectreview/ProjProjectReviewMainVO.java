package com.msun.csm.model.vo.projprojectreview;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-07-17 09:02:33
 */

@Data
public class ProjProjectReviewMainVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "审核内容")
    List<ProjProjectReviewRecordVO> projectReviewRecordVOS;

    /**
     * 场景编码
     */
    @ApiModelProperty(value = "场景编码")
    private String sceneCode;

    /**
     * 项目阶段编码
     */
    @ApiModelProperty(value = "项目阶段编码")
    private String projectStageCode;

    /**
     * pmo是否可编辑
     */
    @ApiModelProperty(value = "pmo是否可编辑")
    private boolean pmoCanEdit;

    /**
     * 项目经理是否可编辑
     */
    @ApiModelProperty(value = "项目经理是否可编辑")
    private boolean proMgrCanEdit;
    /**
     * 能否确认入驻
     */
    @ApiModelProperty(value = "能否确认入驻")
    private boolean canSettlement;

    /**
     * 审核意见
     */
    @ApiModelProperty(value = "审核意见")
    private String reviewOpinion;

    /**
     * 特殊说明
     */
    @ApiModelProperty(value = "审核意见")
    private String specialInstructions;

    /**
     * 是否展示患者智能未审核通过提示。
     */
    @ApiModelProperty(value = "是否展示患者智能未审核通过提示")
    private boolean showPatientSmartNotPass;

    /**
     * 展示提示(true：提示, false:不提示)
     */
    @ApiModelProperty(value = "展示提示(true：提示, false:不提示)")
    private Boolean showAppOpenTip;
}
