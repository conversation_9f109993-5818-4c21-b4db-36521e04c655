package com.msun.csm.model.vo;

import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * @Description: 交付工单页面实体
 */

/**
 * 申请单
 *
 * <AUTHOR>
 */
@ApiModel(description = "申请单")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjApplyOrderVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 申请单号
     */
    @ApiModelProperty(value = "申请单号")
    private Long applyNum;
    /**
     * 申请单类型：1：首次环境申请、2：增加医院、3：增加产品
     */
    @ApiModelProperty(value = "申请单类型：1：首次环境申请、2：增加医院、3：增加产品")
    private Integer applyType;
    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    private Date applyTime;
    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    private String applicant;
    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectId;
    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private Long customInfoId;
    /**
     * 卫生院id
     */
    @ApiModelProperty(value = "卫生院id")
    private Long hospitalInfoId;
    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private String productIds;
    /**
     * 运维人员
     */
    @ApiModelProperty(value = "运维人员")
    private String operationPerson;
    /**
     * 结果：  0：待审核、1：已审核、2：已驳回、3：环境部署中、4：环境部署完成、5：已交付、6：已撤销
     */
    @ApiModelProperty(value = "结果：  0：待审核、1：已审核、2：已驳回、3：环境部署中、4：环境部署完成、5：已交付、6：已撤销")
    private Integer resultType;
    /**
     * 预资源规划文件地址，仅限私有云
     */
    @ApiModelProperty(value = "预资源规划文件地址，仅限私有云")
    private String resourceFilePath;
    /**
     * 逻辑删除【0：否；1：是】
     */
    @ApiModelProperty(value = "逻辑删除【0：否；1：是】")
    private Integer isDeleted;
    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private Long createrId;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新人id
     */
    @ApiModelProperty(value = "更新人id")
    private Long updaterId;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 验收时间
     */
    @ApiModelProperty(value = "验收时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date acceptanceTime;
}
