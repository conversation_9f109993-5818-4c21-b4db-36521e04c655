package com.msun.csm.model.dto.yunweiplatform;

import java.util.List;

import lombok.Data;


/**
 * 客户信息及所含医院信息
 */

@Data
public class SaveEnvCustomDTO {


    /**
     * 行政区划格式：省/市/县
     */
    private String administrativeDivisions;
    /**
     * 床位数
     */
    private Integer bedCount;
    /**
     * 日门诊量
     */
    private Integer dayClinicCount;
    /**
     * 行政区划格式编码：省/市/县
     */
    private String administrativeCode;
    /**
     * 域名
     */
    private String domainName;
    /**
     * 区域医院描述
     */
    private String remarks;
    /**
     * 终端数
     */
    private Integer terminalCount;
    /**
     * 人口数(万)
     */
    private Integer peopleCount;
    /**
     * 卫生室数量
     */
    private Integer clinicCount;

    /**
     * cdss知识库类型
     */
    private String cdssType;
    /**
     * 客服经理
     */
    private String customerManager;
    /**
     * 客服人员
     */
    private String customerPersonnel;
    /**
     * 医院等级
     */
    private String hospitalLevel;
    /**
     * 医院收入
     */
    private Double annualIncome;

    /**
     * 医院集合
     */
    private List<SaveEnvHospital> deployResourceApplyHospitalDTOList;


}
