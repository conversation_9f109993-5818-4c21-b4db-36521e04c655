package com.msun.csm.model.vo;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* @description:
* @fileName: ProjProjectFileVO.java
* @author: lius3
* @createAt: 2024/11/19 15:47
* @updateBy: lius3
* @remark: Copyright
*/
@Data
public class ProjProjectFileVO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long projectFileId;

    /**
     * 项目阶段编码
     */
    @ApiModelProperty (value = "项目阶段编码")
    private String projectStageCode;

    /**
     * 项目节点编码
     */
    @ApiModelProperty (value = "项目节点编码")
    private String milestoneNodeCode;

    /**
     * 文件名称
     */
    @ApiModelProperty (value = "文件名称")
    private String fileName;

    /**
     * 文件OBS路径
     */
    @ApiModelProperty (value = "文件OBS路径")
    private String filePath;

    /**
     * 文件简要说明
     */
    @ApiModelProperty (value = "文件简要说明")
    private String fileDesc;

    /**
     * 创建人姓名
     */
    @ApiModelProperty("创建人姓名")
    private String createrName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
