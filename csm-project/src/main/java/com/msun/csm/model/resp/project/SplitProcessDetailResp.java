package com.msun.csm.model.resp.project;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024-01-16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SplitProcessDetailResp {

    /**
     * 项目id
     */
    private Long projectInfoId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目原始产品
     */
    private String originalProducts;

    /**
     * 项目拆分产品
     */
    private String splitProducts;

    /**
     * 文件
     */
    private List<ImgResp> imgRespList;


    /**
     * 原因
     */
    private String reason;

    /**
     * pmo审核意见
     */
    private String pmoAudit;

    /**
     * 质管审核意见
     */
    private String qaAudit;

    /**
     * 进度描述
     */
    private String progressDesc;

    /**
     * 日志记录
     */
    private List<SplitProcessLogDetail> processLogList;
}
