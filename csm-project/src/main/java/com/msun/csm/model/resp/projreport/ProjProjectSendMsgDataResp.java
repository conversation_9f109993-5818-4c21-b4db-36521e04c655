package com.msun.csm.model.resp.projreport;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @TableName proj_report_examine_log
 */
@ApiModel(description = "业务审核记录")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjProjectSendMsgDataResp implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "report,form")
    private String dataType;

    private Long customInfoId;
    private String customName;
    private Long projectInfoId;
    private String projectName;
    private Integer hisFlag;
    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    private String projectNumber;
    @ApiModelProperty(value = "总数量")
    private Integer allCount;
    @ApiModelProperty(value = "通过数量")
    private Integer operCount;
    @ApiModelProperty(value = "提交数量")
    private Integer commitCount;
    @ApiModelProperty(value = "驳回数量")
    private Integer errCount;
    @ApiModelProperty(value = "审核人")
    private String userIds;
    @ApiModelProperty(value = "提交人")
    private String commitUserIds;


}
