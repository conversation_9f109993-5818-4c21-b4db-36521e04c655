package com.msun.csm.model.req.projreport;

import java.util.List;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@ApiModel(description = "报表数据调研阶段审核")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjSurveyReportReviewExamineReq {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private List<Long> ids;

    /**
     * 完成状态：
     */
    @ApiModelProperty(value = "审核状态  1通过、2驳回：")
    private Integer examineStatus;

    /**
     * 审核意见
     */
    @ApiModelProperty(value = "审核意见")
    private String examineOpinion;

    @ApiModelProperty(value = "查询日志业务主键")
    private Long id;
}
