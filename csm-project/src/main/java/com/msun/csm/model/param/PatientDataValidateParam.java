package com.msun.csm.model.param;

import javax.validation.constraints.NotNull;

import lombok.Data;

@Data
public class PatientDataValidateParam {

    /**
     * 项目ID
     */
    @NotNull
    private Long projectInfoId;

    /**
     * 医院信息ID
     */
    private Long hospitalInfoId;

    /**
     * 是否只查看存在差异的数据：1-是，其他值或不传默认查看全部数据
     */
    private Boolean onlyDifference;

    /**
     * 客户信息ID
     */
    @NotNull
    private Long customInfoId;

    /**
     * 前端请求老HIS的门诊患者信息接口之后的结果，对应老His接口：/api/QueryOutPatTotal
     */
    private String outpatientResult;

    /**
     * 前端请求老HIS的住院患者信息接口之后的结果，对应老His接口：/api/QueryInPatTotal
     */
    private String inpatientResult;
}
