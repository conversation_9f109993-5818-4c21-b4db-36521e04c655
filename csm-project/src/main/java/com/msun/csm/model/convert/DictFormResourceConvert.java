package com.msun.csm.model.convert;

import org.mapstruct.Mapper;

import com.msun.csm.common.model.convert.Dto2PoBaseConvert;
import com.msun.csm.common.model.convert.Dto2VoBaseConvert;
import com.msun.csm.common.model.convert.Vo2PoBaseConvert;
import com.msun.csm.dao.entity.dict.DictFormResource;
import com.msun.csm.model.dto.DictFormResourceDTO;
import com.msun.csm.model.vo.DictFormResourceVO;

/**
 * 表单资源库(DictFormResource)数据转换
 *
 * <AUTHOR>
 * @since 2024-05-20 15:25:22
 */
@Mapper(componentModel = "spring")
public interface DictFormResourceConvert extends Dto2PoBaseConvert<DictFormResourceDTO, DictFormResource>,
        Dto2VoBaseConvert<DictFormResourceDTO, DictFormResourceVO>, Vo2PoBaseConvert<DictFormResourceVO, DictFormResource> {

}
