package com.msun.csm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 三方接口同步数据老系统中授权信息VO
 *
 * @Author: duxu
 * @Date: 2024/09/20/17:10
 */
@Data
public class ImspInterfaceAuthorDataVO {

    @ApiModelProperty ("接口id")
    private Long dataId;

    @ApiModelProperty ("申请环境【0：测试环境；1：正式环境】")
    private Integer testProcedure;

    @ApiModelProperty ("appId")
    private String appId;

    @ApiModelProperty ("授权信息返回的唯一标志")
    private Long authApplyId;

    @ApiModelProperty ("公钥信息")
    private String publicKey;

    @ApiModelProperty ("私钥信息")
    private String privateKey;
}
