package com.msun.csm.model.vo;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/10/08/14:55
 */

@Data
public class ProjInterfaceRecordLogVo {

    /**
     * 主键id
     */
    @ApiModelProperty (value = "主键id")
    private Long interfaceRecordLogId;

    /**
     * 操作名称
     */
    @ApiModelProperty (value = "操作名称")
    private String logTitle;


    /**
     * 操作人姓名
     */
    @ApiModelProperty (value = "操作人姓名")
    private String operateUserName;

    /**
     * 操作时间
     */
    @ApiModelProperty (value = "操作时间")
    private Date operateTime;

    /**
     * 操作的接口id
     */
    @ApiModelProperty (value = "操作的接口id")
    private Long thirdInterfaceId;

    @ApiModelProperty ("评论内容")
    private String operateContent;

    @ApiModelProperty ("操作人手机号")
    private String operateUserPhone;
}
