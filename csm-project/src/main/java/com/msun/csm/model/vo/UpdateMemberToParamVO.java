package com.msun.csm.model.vo;

import java.util.List;

import com.msun.csm.dao.entity.dict.DictProjectRole;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/26/17:10
 */
@Data
public class UpdateMemberToParamVO {

    /**
     * 是否为项目经理：1-是；0-不是
     */
    @ApiModelProperty("是否是项目经理")
    private Integer isLeaderFlag;

    /**
     * 是否为分公司经理：1-是；0-不是
     */
    @ApiModelProperty("是否是分公司经理")
    private Integer isBranchManagerFlag;

    @ApiModelProperty("角色列表")
    private List<DictProjectRole> roleList;

    /**
     * 是否为后端项目经理，1-是；0-不是
     */
    private Integer isBackLeaderFlag;

    /**
     * 是否为运维经理，1-是；0-不是
     */
    private Integer isOperationsManagerFlag;
}
