package com.msun.csm.model.convert.productempower;

import org.mapstruct.Mapper;

import com.msun.csm.common.model.convert.Vo2PoBaseConvert;
import com.msun.csm.dao.entity.proj.productempower.ProjProductEmpowerAddRecord;
import com.msun.csm.model.vo.productempower.ProjProductEmpowerAddRecordVO;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface ProjProductEmpowerAddRecordConvert extends Vo2PoBaseConvert<ProjProductEmpowerAddRecordVO,
        ProjProductEmpowerAddRecord> {
}
