package com.msun.csm.model.req.project;

import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateProjectPlanReq {

    /**
     * 客户ID
     */
    @NotNull(message = "参数【customerInfoId】不可为null；")
    private Long customerInfoId;

    /**
     * 项目ID
     */
    @NotNull(message = "参数【projectInfoId】不可为null；")
    private Long projectInfoId;

    /**
     * 项目计划ID
     */
    private Long projectPlanId;

    /**
     * 更新类型
     * 1. 产品更新完成
     * 2. 产品更新未完成
     * 3. 里程碑更新成已完成、
     * 4. 里程碑更新成未完成
     * 5. 取消前置节点限制
     * 6. 项目计划更新为已完成
     * 7. 项目计划更新为未完成
     * 8. 取消项目计划前置节点限制
     * 9. 我的待办更新为已完成
     * 10.我的待办更新为未完成
     */
    @NotNull(message = "参数【updateType】不可为null；")
    private Integer updateType;

    /**
     * 操作人账号
     */
    private String operatorAccount;

    /**
     * 更新状态的时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 证明文件关联的项目文件Id
     */
    private Long projectFileId;

    /**
     * 我的待办ID集合
     */
    private List<Long> todoTaskIdList;

}
