package com.msun.csm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/06/25/15:58
 */
@Data
public class ScheduleTaskVO {

    @ApiModelProperty ("主键id")
    private Long scheduleTaskId;

    @ApiModelProperty ("任务名称")
    private String taskName;

    @ApiModelProperty ("任务code")
    private String taskCode;

    @ApiModelProperty ("是否启用【0：否；1：是】")
    private Boolean enableBool;

    @ApiModelProperty ("任务说明")
    private String taskMessage;

    @ApiModelProperty ("触发的时间数据")
    private Integer triggerTime;

    @ApiModelProperty ("间隔时间单位【minute：分钟；hour：小时；day:天；month：月；year：年】")
    private String intervalTimeUnit;

    @ApiModelProperty ("计划运行时间")
    private String planRunTime;

}
