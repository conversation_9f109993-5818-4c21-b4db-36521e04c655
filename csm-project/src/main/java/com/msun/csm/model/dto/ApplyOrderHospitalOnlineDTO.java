package com.msun.csm.model.dto;

import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * 解除限制医院手动调用试用
 */
@Data
@Builder
@ApiModel(value = "入参", description = "前端调用入参")
public class ApplyOrderHospitalOnlineDTO {
    /**
     * 医院信息ID
     */
    @ApiModelProperty("医院信息ID")
    @NotNull(message = "医院id不能为空")
    private List<Long> hospitalInfoIds;
    /**
     * 项目信息主键ID
     */
    @ApiModelProperty("项目信息主键ID")
    @NotNull(message = "项目id不能为空")
    private Long projectInfoId;

    public ApplyOrderHospitalOnlineDTO(List<Long> hospitalInfoIds, Long projectInfoId) {
        this.hospitalInfoIds = hospitalInfoIds;
        this.projectInfoId = projectInfoId;
    }

}
