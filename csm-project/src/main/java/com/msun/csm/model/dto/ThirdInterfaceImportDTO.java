package com.msun.csm.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/09/12/15:02
 */
@Data
public class ThirdInterfaceImportDTO {

    /**
     * 接口名称
     */
    @ApiModelProperty("接口名称")
    private String dictInterfaceName;

    /**
     * 业务分类
     */
    @ApiModelProperty("业务分类")
    @ExcelProperty (value = "*业务分类")
    private String interfaceTypeName;

    /**
     * 接口分类
     */
    @ApiModelProperty("接口分类")
    @ExcelProperty (value = "接口分类")
    private String interfaceClassName;

    /**
     * 数据集
     */
    @ApiModelProperty("数据集")
    @ExcelProperty (value = "数据集")
    private String dataSetName;

    /**
     * 厂商名称
     */
    @ApiModelProperty (value = "厂商名称")
    @ExcelProperty (value = "*厂商名称")
    private String dictInterfaceFirmName;

    /**
     * 接口版本
     */
    @ApiModelProperty("接口版本")
    @ExcelProperty (value = "接口版本")
    private String thirdInterfaceVersion;

    /**
     * 厂商联系人
     */
    @ApiModelProperty("厂商联系人")
    @ExcelProperty (value = "厂商联系人")
    private String firmContactName;

    /**
     * 联系人电话
     */
    @ApiModelProperty("联系人电话")
    @ExcelProperty (value = "联系人电话")
    private String firmContactPhone;

    /**
     * 期望完成时间
     */
    @ApiModelProperty("期望完成时间")
    @ExcelProperty (value = "*期望完成时间")
    private String expectTime;

    /**
     * 来源医院
     */
    @ApiModelProperty("来源医院")
    @ExcelProperty (value = "*来源医院")
    private String hospitalName;

    /**
     * 上线必备
     */
    @ApiModelProperty("上线必备")
    @ExcelProperty (value = "上线必备")
    private String onlineFlag;

    /**
     * 定价分类
     */
    @ApiModelProperty (value = "定价分类")
    @ExcelProperty (value = "*定价分类")
    private String productName;

    /**
     * 业务场景描述
     */
    @ApiModelProperty("业务场景描述")
    @ExcelProperty (value = "*业务场景描述")
    private String businessDesc;

}
