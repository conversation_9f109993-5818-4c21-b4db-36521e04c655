package com.msun.csm.model.convert;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.msun.csm.dao.entity.dict.DictAgentChat;
import com.msun.csm.model.vo.DictAgentChatVO;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;

/**
 * AI智能体配置转换工具类
 * <AUTHOR>
 * @date 2024/10/10
 */
@Component
public class DictAgentChatConvert {

    /**
     * 实体转VO
     * @param entity 实体对象
     * @return VO对象
     */
    public DictAgentChatVO entityToVO(DictAgentChat entity) {
        if (entity == null) {
            return null;
        }
        DictAgentChatVO vo = new DictAgentChatVO();
        BeanUtil.copyProperties(entity, vo);
        return vo;
    }

    /**
     * 实体列表转VO列表
     * @param entityList 实体列表
     * @return VO列表
     */
    public List<DictAgentChatVO> entityListToVOList(List<DictAgentChat> entityList) {
        if (CollectionUtil.isEmpty(entityList)) {
            return null;
        }
        return entityList.stream()
                .map(this::entityToVO)
                .collect(Collectors.toList());
    }
}
