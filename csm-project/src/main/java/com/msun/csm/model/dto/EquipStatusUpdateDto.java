package com.msun.csm.model.dto;

import java.util.List;
import java.util.Map;

import com.msun.core.component.implementation.api.imsp.dto.EquipmentStatusWrapperResult;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EquipStatusUpdateDto {
    /**
     * 获取的更新内容
     */
    private Map<Long, EquipmentStatusWrapperResult> mapData;
    /**
     * 设备所在医院
     */
    private List<ProjHospitalInfo> hospitalInfoList;
    /**
     * 项目id
     */
    private Long projectInfoId;

    /**
     * 是否更新状态 0：否，1：时
     */
    private Integer updateStatusFlag;
}