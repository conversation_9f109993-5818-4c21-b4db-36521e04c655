package com.msun.csm.model.dto;

import lombok.Data;

/**
 * @description:
 * @fileName:
 * @author:<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @updateBy:
 * @Date:Created in 16:30 2024/5/6
 * @remark:
 */
@Data
public class ProjMilestoneInfoDTO {

    private Long projectInfoId;
    /**
     * 单体是否可用：0.否；1.是
     */
    private Integer monomerFlag;
    /**
     * 区域是否可用：0.否；1.是
     */
    private Integer regionFlag;
    /**
     * 实施类型：-1.通用；1.老换新；2.新上线
     */
    private Integer upgradationType;
    /**
     * 项目首期标识：-1.通用；0.否；1.是
     */
    private Integer hisFlag;
    /**
     * 是否为软件项目：0.否；1.是
     */
    private Integer selfSoftwareFlag;
    /**
     * 是否为外采项目：0.否；1.是
     */
    private Integer externalSoftwareFlag;

    /**
     * 是否需要产出文档：0.否；1.是
     */
    private Integer docFlag;
}
