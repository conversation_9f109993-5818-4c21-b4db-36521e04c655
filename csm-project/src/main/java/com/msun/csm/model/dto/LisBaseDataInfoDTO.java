package com.msun.csm.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LisBaseDataInfoDTO {

    /**
     * 医院ID
     */
    private String hospitalInfoId;

    /**
     * 医院名称
     */
    private String hospitalName;

    /**
     * 工作组
     */
    private String lisWorkGroupCount;

    /**
     * 样本类型
     */
    private String lisSampleClassCount;

    /**
     * 报告单类型
     */
    private String lisReportTypeCount;

    /**
     * 明细项目
     */
    private String lisItemCount;

    /**
     * 组合项目
     */
    private String lisLabItemCount;

    /**
     * 设备维护
     */
    private String lisEquipmentCount;

    /**
     * 医嘱对照组合项目
     */
    private String lisCheckedMedicalOrderCount;


}
