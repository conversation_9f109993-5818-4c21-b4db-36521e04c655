package com.msun.csm.model.resp.projreport;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.msun.csm.common.model.po.BasePO;

/**
 * @TableName proj_report_examine_log
 */
@ApiModel(description = "业务审核记录")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjBusinessExamineLogResp extends BasePO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日志主键
     */
    @TableId(type = IdType.INPUT)
    private Long businessExamineLogId;

    /**
     * 审批状态：0-待审核；1-审核通过；2-驳回
     */
    private Integer examineStatus;

    /**
     * 对应的业务主键如：报表、表单、产品调研
     */
    private Long businessId;

    /**
     * 审核标题
     */
    private String operateName;

    /**
     * 审核意见
     */
    private String operateContent;

    /**
     * 操作时间/审核时间
     */
    private String operateTime;

    /**
     * 操作人电话
     */
    private String tel;

    /**
     * 操作人姓名
     */
    private String operateUserName;

    /**
     * 操作人电话
     */
    private String operateUserPhone;

    /**
     * 日志标题
     */
    private String logTitle;

    /**
     * 操作人ID
     */
    private Long operatorSysUserId;

    /**
     * 操作备注
     */
    private String operateRemark;

}
