package com.msun.csm.model.req.formlibrary;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@ApiModel(description = "表单资源库查询参数")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ToolLimitReq {
    private static final long serialVersionUID = 1L;

    /**
     * userId
     */
    @ApiModelProperty(value = "userId")
    private Long userId;

    @ApiModelProperty(value = "限制类型，如报表、表单等")
    private String typeCode;

    @ApiModelProperty(value = "医院id")
    private Long hospitalInfoId;

    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

}
