package com.msun.csm.model.vo.product;

import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 特批/工单产品补录-明细表(ProjProductSupplementaryRecordDetail)实体类
 *
 * <AUTHOR>
 * @since 2024-07-04 16:50:30
 */
@Data
@TableName(value = "proj_product_supplementary_record_detail", schema = "csm")
public class ProductSupplementaryRecordDetailVO {

    /**
     * 运营平台产品ID
     */
    private Long yyProductId;
    /**
     * 运营平台产品ID
     */
    private String productName;
    /**
     * 运营平台产品ID
     */
    private String deploymentProductName;

}
