package com.msun.csm.model.dto.mainpage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SaveRoleVsModuleDTO {

    /**
     * 角色模块关联id
     */
    @ApiModelProperty("角色模块关联id")
    private Long roleVsModuleId;

    /**
     * 角色id
     */
    @ApiModelProperty("角色id")
    private Long roleId;

    /**
     * 模块id
     */
    @ApiModelProperty("模块id")
    private Long moduleId;

    /**
     * 布局在第几行
     */
    @ApiModelProperty("布局在第几行")
    private Integer rowNum;

    /**
     * 展示顺序
     */
    @ApiModelProperty("展示顺序")
    private Integer orderSort;

    /**
     * 宽度粒度
     */
    @ApiModelProperty("宽度粒度")
    private Integer widthSize;

    /**
     * 高度粒度
     */
    @ApiModelProperty("高度粒度")
    private Integer heightSize;

}
