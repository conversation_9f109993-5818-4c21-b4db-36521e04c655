package com.msun.csm.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* @description:
* @fileName: HdEquipSelectDTO.java
* @author: lius3
* @createAt: 2024/10/14 8:49
* @updateBy: lius3
* @remark: Copyright
*/
@Data
public class HdEquipSelectDTO {

    @ApiModelProperty("客户id")
    private Long customInfoId;

    @ApiModelProperty ("项目id")
    private Long projectInfoId;

    @ApiModelProperty ("调研来源医院id")
    private Long hospitalInfoId;

    @ApiModelProperty ("设备型号、厂商【查询条件】")
    private String equipModelOrFactory;

    @ApiModelProperty ("是否对接")
    private Integer requiredFlag;

    @ApiModelProperty ("对接状态【0：未申请；1：已申请:；2：已驳回:；3：研发中；4：研发完成:；5：测试通过:；6：测试失败】")
    private Integer equipStatus;

    /**
     * LIS设备记录表主键id
     */
    @ApiModelProperty (value = "血透设备记录表主键id")
    private Long equipRecordVsHdId;
}
