package com.msun.csm.model.dto.autotest.feign;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 药房科室
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PharmacyDeptTransferDTO {

    /**
     * 门诊中药房科室
     */
    @ApiModelProperty(value = "门诊中药房科室")
    private PharmacyDeptDTO outpatientChineseMedicine;
    /**
     * 门诊西药房科室
     */
    @ApiModelProperty(value = "门诊西药房科室")
    private PharmacyDeptDTO outpatientWesternMedicine;
    /**
     * 住院中药房科室
     */
    @ApiModelProperty(value = "住院中药房科室")
    private PharmacyDeptDTO inpatientChineseMedicine;
    /**
     * 住院西药房科室
     */
    @ApiModelProperty(value = "住院西药房科室")
    private PharmacyDeptDTO inpatientWesternMedicine;

}
