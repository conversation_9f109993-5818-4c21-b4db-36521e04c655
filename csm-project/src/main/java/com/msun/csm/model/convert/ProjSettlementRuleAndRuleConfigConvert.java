package com.msun.csm.model.convert;

import org.mapstruct.Mapper;

import com.msun.csm.common.model.convert.Vo2PoBaseConvert;
import com.msun.csm.dao.entity.rule.RuleProjectRuleConfig;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementRuleVO;

@Mapper(componentModel = "spring")
public interface ProjSettlementRuleAndRuleConfigConvert extends Vo2PoBaseConvert<ProjProjectSettlementRuleVO, RuleProjectRuleConfig> {
}
