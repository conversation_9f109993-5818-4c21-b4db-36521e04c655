package com.msun.csm.model.vo.projsimulation;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class HospitalSimulatonDetaiVO {

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private Long projSimulationDetailId;

    /**
     * 模拟流程业务字典表主键
     */
    @ApiModelProperty("模拟流程业务字典表主键")
    private Long dictSimulationBusinessId;

    /**
     * 结果主表ID
     */
    @ApiModelProperty("结果主表ID")
    private Long projSimulationResultId;

    /**
     * 客户ID
     */
    @ApiModelProperty("客户ID")
    private Long customInfoId;

    /**
     * 项目ID
     */
    @ApiModelProperty("项目ID")
    private Long projectInfoId;

    /**
     * 医院ID
     */
    @ApiModelProperty("医院ID")
    private Long hospitalInfoId;

    /**
     * 校验通过标识：0未通过、1通过
     */
    @ApiModelProperty("校验通过标识：0未通过、1通过")
    private Integer passFlag;

    /**
     * 统计查询开始时间
     */
    @ApiModelProperty("统计查询开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 统计查询结束时间
     */
    @ApiModelProperty("统计查询结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 校验sql
     */
    @ApiModelProperty("校验sql")
    private String checkSql;

    /**
     * 模拟类型：1门诊、2住院
     */
    @ApiModelProperty("模拟类型：1门诊、2住院")
    private Integer simulationType;

    /**
     * 模拟用户ID
     */
    @ApiModelProperty("模拟用户ID")
    private Long simulationUserId;
}
