package com.msun.csm.model.dto;

import java.util.Date;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/07/25/14:37
 */
@Data
public class ProjProductBacklogDTO {

    @ApiModelProperty ("主键id")
    private Long productBacklogId;

    @ApiModelProperty ("项目id")
    private Long projectInfoId;

    @ApiModelProperty ("实施地客户id")
    private Long customInfoId;

    @ApiModelProperty ("医院id")
    private Long hospitalInfoId;

    @ApiModelProperty ("运营产品id")
    private Long yyProductId;

    @ApiModelProperty ("运营产品模块id")
    private Long yyProductModuleId;

    @ApiModelProperty ("产品任务节点菜单id")
    private Long productJobMenuId;

    @ApiModelProperty ("产品节点选择后,选择的完成状态")
    private Integer productJobMenuStatus;

    @ApiModelProperty ("产品名称")
    private String productName;

    @ApiModelProperty ("代办类型")
    private Long todoType;

    @ApiModelProperty ("基础数据完成状态【0：不包含基础数据；1：未完成；2：已完成】")
    private Integer baseDataStatus;

    @ApiModelProperty ("配置数据完成状态【0：不包含配置；1：未完成；2：已完成】")
    private Integer configDataStatus;

    @ApiModelProperty ("待处理任务完成状态【0：不包含待处理任务；1：未完成；2：已完成】")
    private Integer todoTaskStatus;

    @ApiModelProperty ("报表完成状态【0：不包含配置；1：未完成；2：已完成】")
    private Integer reportDataStatus;

    @ApiModelProperty ("表单完成状态【0：不包含待处理任务；1：未完成；2：已完成】")
    private Integer formDataStatus;

    @ApiModelProperty ("责任人")
    private Long userId;

    @ApiModelProperty ("责任人数组")
    private List<Long> userIdList;

    @ApiModelProperty ("后端责任人数组")
    private List<Long> backUserIdList;

    @ApiModelProperty ("里程碑节点code")
    private String milestoneNodeCode;

    @ApiModelProperty ("任务计划明细表主键id")
    private Long milestoneTaskDetailId;

    @ApiModelProperty ("产品总体完成状态")
    private Integer completeStatus;

    @ApiModelProperty ("计划完成时间")
    @DateTimeFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expectCompTime;

    @ApiModelProperty ("批量分配时 taskDetailId集合")
    private List<Long> taskDetailIdList;

    @ApiModelProperty ("里程碑节点id")
    private Long milestoneInfoId;

    @ApiModelProperty ("医院名称")
    private String hospitalName;

    @ApiModelProperty ("批量分配医院id")
    private List<Long> hospitalInfoIdList;

    @ApiModelProperty ("批量分配运营产品id")
    private List<Long> yyProductIdList;

    @ApiModelProperty ("调研阶段可用标识【0：否；1：是】")
    private Integer surveyUseFlag;

    @ApiModelProperty ("准备阶段可用标识【0：否；1：是】")
    private Integer prepareUseFlag;
    // ***************************  产品业务调研的下钻页面专属参数 ***************************

    /**
     * 科室名称
     */
    private String deptName;

    /**
     * 责任人的userId
     */
    private Long sysUserId;

    /**
     * 调研来源，只要不是来自项目经理确认阶段（config），统一认为来自调研阶段（survey）
     */
    private String source;

    /**
     * 操作阶段：survey-调研阶段；ready-准备阶段
     */
    private String operationSource;

    /**
     * 后端产品负责人
     */
    private Long backendSysUserId;

}
