package com.msun.csm.model.vo.projsettlement;

import java.io.Serializable;
import java.util.List;

import com.msun.csm.dao.entity.proj.projsettlement.ProjSettlementMiddlewareOrderInfo;
import com.msun.csm.dao.entity.proj.projsettlement.ProjSettlementOrderInfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-06-17 10:49:37
 */

@Data
public class ProjProjectSettlementCheckSaleMainVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "是否首期项目")
    private Boolean isFirstProject;

    @ApiModelProperty(value = "实施产品(逗号分割, 数据取自orderProduct工单产品表)")
    private String productNames;

    @ApiModelProperty(value = "预资源规划模板链接")
    private String preResourceModelUrl;

    @ApiModelProperty(value = "已上传预资源规划附件url")
    private String preResourceModelBackUrl;

    @ApiModelProperty(value = "已上传预资源规划附件名称")
    private String preResourceModelName;

    @ApiModelProperty(value = "小硬件清单模板url(若为空, 不需要上传到货清单)")
    private List<ShowFile> smallHardwareUrl;

    @ApiModelProperty(value = "小硬件清单模板url(若为空, obs临时路径)")
    private String smallHardwareBackUrl;

    @ApiModelProperty(value = "云资源确认单模板url(若为空, 不需要上传到货清单)")
    private String cloudFirmFormModelUrl;

    @ApiModelProperty(value = "云资源确认单上传的图片,obs下载路径")
    private String cloudFirmFormModelBackUrl;

    @ApiModelProperty(value = "已上传云资源确认单上传的图片名称")
    private String cloudFirmFormName;

    @ApiModelProperty(value = "免中间件证明文件, obs下载路径")
    private String freeMidOrderFileUrl;

    @ApiModelProperty(value = "免中间件证明文件名称")
    private String freeMidOrderFileOriginalName;

    @ApiModelProperty(value = "免中间件原因说明")
    private String freeMidOrderRemark;

    @ApiModelProperty(value = "免中间件审核驳回原因")
    private String freeMidOrderRejectReason;

    @ApiModelProperty(value = "免中间件部署服务单状态(申请状态。0 准申请(已具备申请条件), 1 已申请, 2 审核驳回, 3 审核通过)")
    private Integer freeMidOrderStatus;

    @ApiModelProperty(value = "免中间件部署服务单状态(申请状态。0 准申请(已具备申请条件), 1 已申请, 2 审核驳回, 3 审核通过)")
    private String freeMidOrderStatusDesc;

    @ApiModelProperty(value = "当前状态。入驻状态：0.提交入驻条件；1.申请入驻；11.方案分公司经理复核通过；12.方案分公司经理驳回；21.运营部审核通过；22.风控驳回；31.PMO审核通过；32"
            + ".PMO驳回；41.确认入驻")
    private Integer settlementStatus;

    @ApiModelProperty(value = "是否足额支付, false:未支付, true:已支付")
    private Boolean hasPay;

    @ApiModelProperty(value = "开通时间")
    private String subscribeStartTime;

    @ApiModelProperty(value = "工单信息（含云资源工单或中间件部署服务）")
    private ProjSettlementOrderInfo cloudServiceSale;
    /**
     * 环境编码
     */
    @ApiModelProperty(value = "主键")
    private String envirId;
    /**
     * 环境名称
     */
    @ApiModelProperty(value = "环境名称")
    private String envirName;

    @ApiModelProperty(value = "已完成审核记录")
    private List<ProjProjectSettlementCheckedProgressVO> checkedProgressVOList;

    /**
     * 是否只读
     */
    @ApiModelProperty(value = "是否只读")
    private Boolean readOnly;

    @ApiModelProperty(value = "入驻申请关键标识（是否首付款等）")
    private ProjProjectSettlementKeyFlagVO keyFlagVO;

    @ApiModelProperty(value = "云资源服务类型. 1 众阳云, 2 共享云, 3. 私有云")
    private Integer cloudServiceType;

    @ApiModelProperty(value = "云资源服务类型. 1 众阳云, 2 非众阳云")
    private Integer msunCloudFlag;

    @ApiModelProperty(value = "中间件服务工单")
    private ProjSettlementMiddlewareOrderInfo middlewareOrderInfo = new ProjSettlementMiddlewareOrderInfo();


}
