package com.msun.csm.model.convert;

import org.mapstruct.Mapper;

import com.msun.csm.common.model.convert.Vo2PoBaseConvert;
import com.msun.csm.dao.entity.proj.ProjProjectSettlementUnCheckRelative;
import com.msun.csm.model.vo.projsettlement.ProjProjectSettlementUnCheckRelativeVO;

/**
 * 待审核转换器
 */
@Mapper(componentModel = "spring")
public interface ProjProjectSettlementUnCheckRelativeConvert extends Vo2PoBaseConvert<ProjProjectSettlementUnCheckRelativeVO, ProjProjectSettlementUnCheckRelative> {
}
