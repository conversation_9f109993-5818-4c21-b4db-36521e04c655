package com.msun.csm.model.resp.projectreview;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import com.msun.csm.common.model.po.BasePO;

/**
 * 项目审核模式配置表
 *
 * <AUTHOR>
 * @TableName config_project_review
 */
@Data
public class ConfigProjectReviewResp extends BasePO {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long projectReviewId;

    /**
     * 审核类型id 取字典表 dict_project_review_type
     */
    @ApiModelProperty(value = "审核类型id 取字典表 dict_project_review_type")
    private Integer reviewTypeId;

    @ApiModelProperty(value = "审核类型名称")
    private String reviewTypeName;

    /**
     * 客户类型 -1 通用 1单体 2区域
     */
    @ApiModelProperty(value = "客户类型 -1 通用 1单体 2区域")
    private Integer customType;

    @ApiModelProperty(value = "客户类型")
    private String customTypeName;


    /**
     * 电销属性 -1 通用 1 电销 0非电销
     */
    @ApiModelProperty(value = "电销属性 -1 通用 1 电销 0非电销")
    private Integer telesalesFlag;

    @ApiModelProperty(value = "电销属性")
    private String telesalesFlagName;

    /**
     * 交付模式 -1 通用 1 前后端模式  0非前后端模式
     */
    @ApiModelProperty(value = "交付模式 -1 通用 1 前后端模式  0非前后端模式")
    private Integer deliveryModel;

    @ApiModelProperty(value = "交付模式")
    private String deliveryModelName;

    /**
     * 项目类型 -1 通用 1 首期 2 非首期
     */
    @ApiModelProperty(value = "项目类型 -1 通用 1 首期 2 非首期")
    private Integer projectType;

    @ApiModelProperty(value = "项目类型")
    private String projectTypeName;

    /**
     * 审核方式id 取字典表dict_review_method_type
     */
    @ApiModelProperty(value = "审核方式id 取字典表dict_review_method_type")
    private Long reviewMethodId;

    @ApiModelProperty(value = "审核方式名称")
    private String reviewMethodName;
    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    private String reviewTime;

    /**
     * 预警时间
     */
    @ApiModelProperty(value = "预警时间")
    private String warningTime;

    /**
     * 是否产生罚单 0.否 1.是
     */
    @ApiModelProperty(value = "是否产生罚单 0.否 1.是")
    private Integer isFineFlag;

    /**
     * 罚款金额
     */
    @ApiModelProperty(value = "罚款金额")
    private Integer fineMoney;

    @ApiModelProperty(value = "创建人")
    private String createrName;
    @ApiModelProperty(value = "更新人")
    private String updaterName;
}