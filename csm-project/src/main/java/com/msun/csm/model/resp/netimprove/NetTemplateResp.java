package com.msun.csm.model.resp.netimprove;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NetTemplateResp {

    /**
     * 改造方案总标题
     */
    @ApiModelProperty(value = "改造方案总标题")
    private String templateTitle;

    /**
     * 网络改造方案文件内容列表
     */
    @ApiModelProperty(value = "网络改造方案文件内容列表")
    private List<NetTemplateTitleResp> netTemplateTitleResp;
}
