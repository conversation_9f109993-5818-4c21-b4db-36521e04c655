package com.msun.csm.model.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetDeductionClassificationParam {

    /**
     * 项目ID
     */
    @NotNull(message = "参数【projectInfoId】不可为null")
    private Long projectInfoId;

    /**
     * 问题分类主键
     */
    private Long issueClassificationId;

}
