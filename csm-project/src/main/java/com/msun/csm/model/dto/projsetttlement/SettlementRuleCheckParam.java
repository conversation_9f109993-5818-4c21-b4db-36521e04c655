package com.msun.csm.model.dto.projsetttlement;

import com.msun.csm.common.enums.projsettlement.CheckNodeEnum;
import com.msun.csm.common.enums.projsettlement.CheckResultEnum;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class SettlementRuleCheckParam {
    /**
     * 需要新增的审核节点
     */
    private CheckNodeEnum checkNodeEnum;

    /**
     * 审核结果
     */
    private CheckResultEnum checkResultEnum;

}