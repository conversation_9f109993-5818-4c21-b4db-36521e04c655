package com.msun.csm.model.dto;


import com.alibaba.fastjson.annotation.JSONField;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PacsUserCountDataDTO {

    /**
     * 云健康医院ID
     */
    @JSONField(name = "医院id")
    private String hospitalId;

    /**
     * 医院名称
     */
    @JSONField(name = "医院名称")
    private String hospitalName;

    /**
     * 已维护人员数量
     */
    @JSONField(name = "已维护人员数量")
    private String userCount;

    /**
     * 报告医师数量
     */
    @JSONField(name = "报告医师数量")
    private String reportDoctorCount;

    /**
     * 审核医师数量
     */
    @JSONField(name = "审核医师数量")
    private String auditDoctorCount;


}
