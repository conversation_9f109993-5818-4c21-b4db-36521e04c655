package com.msun.csm.model.resp.project;

import lombok.Data;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/5/22
 */
@Data
public class ProductBase {
    /**
     * 业务主键id
     */
    private Long id;

    /**
     * 展示名称
     */
    private String name;

    /**
     * 运营平台工单Id
     */
    private Long woId;

    /**
     * 运营平台产品Id
     */
    private Long yyId;

    /**
     * 运营平台产品名称
     */
    private String yyName;

    /**
     * 运营平台工单产品表id
     */
    private String projProductId;

    /**
     * 产品解决方案
     */
    private Integer pemcusssolType;

    /**
     * 产品解决方案名称
     */
    private String pemcusssolTypeName;

    /**
     * 是否可以拆分 false:不可以 true:可以
     */
    private boolean canSplit;
}
