package com.msun.csm.model.req.projreport;

import javax.validation.constraints.NotNull;

import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.dto.BasePageDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@ApiModel(description = "医院电脑配置分页查询参数")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjHospitalTerminalConfigPageReq extends BasePageDTO {
    private static final long serialVersionUID = 1L;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @NotNull
    private Long projectInfoId;

    /**
     * 医院名称
     */
    @ApiModelProperty(value = "医院名称")
    private String hospitalName;

    /** 配置符合标识 */
    @ApiModelProperty(value = "配置符合标识")
    private String configMeetFlag;

    /** 操作系统 */
    @ApiModelProperty(value = "操作系统")
    private String operatSystem;

    /** 位数 */
    @ApiModelProperty(value = "位数")
    private String computerNumber;

    /** 内存 */
    @ApiModelProperty(value = "内存")
    private Integer memory;

    /** 计算机ip */
    @ApiModelProperty(value = "计算机ip 模糊查询")
    private String pcIp;

}
