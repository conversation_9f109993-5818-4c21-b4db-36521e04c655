package com.msun.csm.model.resp.projectreview;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 查询项目是否需要审核及人员信息
 *
 * <AUTHOR>
 * @TableName
 */
@Data
public class UserModelAllResp {
    @ApiModelProperty(value = "是否已配置审核")
    private Boolean isConfigFlag;

    @ApiModelProperty(value = "是否需要审核")
    private Boolean isNeedReview;

    @ApiModelProperty("人员集合")
    private List<UserModelResp> userModelRespList;
}