package com.msun.csm.model.vo.report;

import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 客户数据统计表(ReportCustomInfo)实体类
 *
 * <AUTHOR>
 * @since 2024-09-27 15:27:17
 */
@Data
public class ReportCustomInfoPageVO {


    /**
     * 客户数据统-主键
     */
    @ApiModelProperty("客户数据统-主键")
    @ExcelIgnore
    private Long reportCustomInfoId;

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    @ColumnWidth(20)
    @ExcelProperty(value = "客户名称", index = 1)
    private String customName;

    /**
     * 实际入驻时间
     */
    @ApiModelProperty("入驻日期")
    @ColumnWidth(20)
    @ExcelProperty(value = "入驻日期", index = 6)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date settleInTime;

    /**
     * 实际验收时间
     */
    @ApiModelProperty("验收时间（内部考核时间）")
    @ExcelProperty(value = "验收时间", index = 9)
    @ColumnWidth(20)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date acceptTime;

    /**
     * 减免工期天数
     */
    @ApiModelProperty("减免工期天数")
    @ExcelProperty(value = "减免工期天数", index = 14)
    @ColumnWidth(20)
    private String durationReduction;

    /**
     * 客户状态标识; 1:正常运维；2：终止合作
     */
    @ApiModelProperty("客户状态标识; 1:正常运维；2：终止合作")
    @ExcelIgnore
    private Integer customDeliverStatus;

    /**
     * 是否电销客户 0.否；1.是
     */
    @ApiModelProperty("是否电销客户 0.否；1.是")
    @ExcelIgnore
    private Integer telesalesFlag;

    /**
     * 实施地客户ID
     */
    @ApiModelProperty("实施地客户ID")
    @ExcelIgnore
    private Long customInfoId;

    /**
     * 单体/区域标识  1.单体 2：区域
     */
    @ApiModelProperty("单体/区域标识  1.单体 2：区域 3电销")
    @ExcelIgnore
    private Integer customType;

    @ApiModelProperty("单体/区域标识  1.单体 2：区域 3电销")
    @ExcelProperty(value = "客户类型", index = 2)
    @ColumnWidth(20)
    private String customTypeName;

    /**
     * 客户首期项目ID
     */
    @ApiModelProperty("客户首期项目ID")
    @ExcelIgnore
    private Long projectInfoId;

    /**
     * 创建者
     */
    @ApiModelProperty("创建者")
    @ExcelIgnore
    private Long createrId;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @ExcelIgnore
    private Long updaterId;

    /**
     * 是否已删除  0.否；1.是
     */
    @ApiModelProperty("是否已删除  0.否；1.是")
    @ExcelIgnore
    private Integer isDeleted;

    /**
     * 老换新标识 1:新客户;2:老换新;3:老体系;
     */
    @ApiModelProperty("老换新标识 1:新客户;2:老换新;3:老体系;")
    @ExcelIgnore
    private Integer upgradationType;

    @ExcelProperty(value = "老换新标识", index = 3)
    @ColumnWidth(20)
    @ApiModelProperty("老换新标识 1:新客户;2:老换新;3:老体系;")
    private String upgradationTypeName;

    /**
     * 最后一次申请验收时间
     */
    @ApiModelProperty("最后一次申请验收时间")
    @ExcelIgnore
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date lastApplyAcceptTime;

    @ExcelProperty(value = "实施状态", index = 4)
    @ColumnWidth(20)
    @ApiModelProperty(" 实施状态  1已派工、2已调研、3已入驻、4、准备完成、5已上线、6已验收、7已启动 、8 申请验收")
    private String projectDeliverStatus;

    @ApiModelProperty(" 实施状态  1已派工、2已调研、3已入驻、4、准备完成、5已上线、6已验收、7已启动 、8 申请验收")
    @ExcelIgnore
    private Integer projectDeliverStatusNum;

    @ApiModelProperty("销售部门id")
    @ExcelIgnore
    private Long saleDeptId;

    @ApiModelProperty("销售部门名称")
    @ExcelProperty(value = "销售部门名称", index = 11)
    @ColumnWidth(20)
    private String saleDeptName;

    @ApiModelProperty("客服部门id")
    @ExcelIgnore
    private Long deptId;

    @ApiModelProperty("客服部门名称")
    @ExcelProperty(value = "客服部门名称", index = 0)
    @ColumnWidth(20)
    private String deptName;

    @ApiModelProperty("客服实施团队id")
    @ExcelIgnore
    private Long serviceOrgId;

    @ApiModelProperty("客服实施团队名称")
    @ExcelProperty(value = "客服实施团队名称", index = 12)
    @ColumnWidth(20)
    private String serviceOrgName;

    @ApiModelProperty("备注")
    @ExcelProperty(value = "备注", index = 13)
    @ColumnWidth(20)
    private String remark;

    @ApiModelProperty("上线日期")
    @ExcelProperty(value = "上线日期", index = 7)
    @ColumnWidth(20)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date onlineTime;

    @ApiModelProperty("外部验收时间")
    @ExcelProperty(value = "外部验收日期", index = 8)
    @ColumnWidth(20)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date outAcceptTime;

    @ApiModelProperty("计划上线时间")
    @ExcelIgnore
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date planOnlineTime;

    /**
     * 派工时间
     */
    @ApiModelProperty(value = "派工时间")
    @ExcelProperty(value = "派工日期", index = 5)
    @ColumnWidth(20)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private String workTime;

    /**
     * 绑定项目标识 0.否；1.是
     */
    @ApiModelProperty("绑定项目标识 0.否；1.是")
    @ExcelIgnore
    private Integer bindProjectFlag;

    /**
     * 客户来源标识 1:运营平台 0 自定义添加
     */
    @ApiModelProperty("客户来源标识 1:运营平台 0 自定义添加")
    @ExcelIgnore
    private Integer customSourceFlag;


    @ApiModelProperty("客户经理名称")
    @ExcelProperty(value = "客户经理名称", index = 10)
    @ColumnWidth(20)
    private String customerManagerName;

    @ApiModelProperty("工期")
    @ExcelProperty(value = "工期", index = 5)
    @ColumnWidth(20)
    private String workDuration;

}
