package com.msun.csm.model.vo.browser;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version : V1.52.0
 * @ClassName: HeaderContentsVO
 * @Description:
 * @Author: Yhongmin
 * @Date: 9:26 2024/9/20
 */
@Data
public class HeaderContentsVO {
    @ApiModelProperty("终端数量")
    private Long terminalNum;

    @ApiModelProperty("实际状数量")
    private Integer actualNum;

    @ApiModelProperty("安装状态说明")
    private String installationPrompt;

    @ApiModelProperty("锐浪版本说明")
    private String ruiLangVersionPrompt;

    @ApiModelProperty("MIV版本说明")
    private String mivVersionPrompt;
}
