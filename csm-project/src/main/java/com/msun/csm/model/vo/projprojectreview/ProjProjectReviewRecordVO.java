package com.msun.csm.model.vo.projprojectreview;

import java.io.Serializable;

import com.msun.csm.model.vo.ProjMilestoneInfoVO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-07-17 09:02:33
 */

@Data
public class ProjProjectReviewRecordVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long projectReviewRecordId;

    /**
     * 项目审核信息表id
     */
    @ApiModelProperty(value = "项目审核信息表id")
    private Long projectReviewInfoId;

    /**
     * 项目信息表主键id
     */
    @ApiModelProperty(value = "项目信息表主键id")
    private Long projectInfoId;

    /**
     * 项目阶段id
     */
    @ApiModelProperty(value = "项目阶段id")
    private Long projectStageId;

    /**
     * 项目阶段编码
     */
    @ApiModelProperty(value = "项目阶段编码")
    private String projectStageCode;

    /**
     * 类型编码
     */
    @ApiModelProperty(value = "类型编码")
    private String classCode;

    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    private String className;

    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码")
    private String itemCode;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String itemName;

    /**
     * 审核要点编码
     */
    @ApiModelProperty(value = "审核要点编码")
    private String projectRuleCode;

    /**
     * 审核要点内容
     */
    @ApiModelProperty(value = "审核要点内容")
    private String projectRuleContent;

    /**
     * 是否公开查看：0.否；1.是
     */
    @ApiModelProperty(value = "是否公开查看：0.否；1.是")
    private Integer isPublic;

    /**
     * 是否有模版文件
     */
    @ApiModelProperty(value = "是否有模版文件")
    private Integer templateFlag;

    /**
     * 模版文件编码
     */
    @ApiModelProperty(value = "模版文件编码")
    private String templateFileCode;

    /**
     * 模版文件编码
     */
    @ApiModelProperty(value = "模版文件下载路径")
    private String templateFileTmpUrl;


    /**
     * 上传的凭证文件id
     */
    @ApiModelProperty(value = "上传的凭证文件id")
    private Long projectFileId;

    /**
     * 上传的凭证文件名称
     */
    @ApiModelProperty(value = "上传的凭证文件名称")
    private String projectFileName;
    /**
     * 上传的凭证文件临时下载路径
     */
    @ApiModelProperty(value = "上传的凭证文件临时下载路径")
    private String projectFileTmpUrl;

    /**
     * 上传的凭证文件名称
     */
    @ApiModelProperty(value = "上传的凭证文件下载路径")
    private String projectFileUrl;

    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer orderNo;

    /**
     * 自检结果：0.不满足；1.已满足；2.不需要
     */
    @ApiModelProperty(value = "自检结果：0.不满足；1.已满足；2.不需要")
    private Integer selfReviewResult;

    /**
     * 自检结果备注
     */
    @ApiModelProperty(value = "自检结果备注")
    private String selfReviewMemo;

    /**
     * 审核结果：0.不通过；1.通过
     */
    @ApiModelProperty(value = "审核结果：0.不通过；1.通过")
    private Integer reviewResult;

    /**
     * 审核说明
     */
    @ApiModelProperty(value = "审核说明")
    private String reviewMemo;

    /**
     * 是否关联里程碑节点
     */
    @ApiModelProperty(value = "是否关联里程碑节点")
    private Integer milestoneNodeFlag;

    /**
     * 关联的里程碑节点编码
     */
    @ApiModelProperty(value = "关联的里程碑节点编码")
    private String milestoneNodeCode;

    /**
     * 是否包含子项标识:0.否；1.是
     */
    @ApiModelProperty(value = "是否包含子项标识:0.否；1.是")
    private Integer containChildrenFlag;

    /**
     * 场景编码
     */
    @ApiModelProperty(value = "场景编码")
    private String sceneCode;

    /**
     * 是否上传文件(0 否, 1 是)
     */
    @ApiModelProperty(value = "是否上传文件(0 否, 1 是)")
    private Integer needUploadFile;

    /**
     * 是否必填(0 非必填, 1 必填)
     */
    @ApiModelProperty(value = "是否必填(0 非必填, 1 必填)")
    private Integer requiredFlag;

    /**
     * 验证方式：no 不验证；upload必须上传文件；prompt仅提示必须满足
     */
    @ApiModelProperty(value = "验证方式：no 不验证；upload必须上传文件；prompt仅提示必须满足")
    private String verityWay;

    /**
     * 里程碑节点
     */
    @ApiModelProperty(value = "里程碑节点")
    private ProjMilestoneInfoVO projMilestoneInfoVO;

    /**
     * 展示类型 1-提交pmo展示 2-pmo审核展示
     */
    @ApiModelProperty(value = "展示类型")
    private Integer displayType;
}
