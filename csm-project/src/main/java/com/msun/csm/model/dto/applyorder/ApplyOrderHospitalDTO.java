package com.msun.csm.model.dto.applyorder;

import org.springframework.http.ResponseEntity;

import com.alibaba.fastjson.JSONObject;
import com.msun.csm.dao.entity.proj.ProjHospitalInfo;
import com.msun.csm.dao.entity.proj.ProjHospitalInfoRelative;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 结果传输使用
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApplyOrderHospitalDTO {

    @ApiModelProperty(value = "是否是区域")
    private boolean isRegion;

    @ApiModelProperty(value = "调用运维平台接口返回结果")
    private ResponseEntity<JSONObject> depPlatformResult;

    @ApiModelProperty(value = "已开通的医院")
    private ProjHospitalInfoRelative hospitalInfoOpen;

    @ApiModelProperty(value = "区域主院")
    private ProjHospitalInfo regionMainHospital;


}
