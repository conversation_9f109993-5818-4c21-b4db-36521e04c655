package com.msun.csm.model.resp.issue;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class IssueExcelResp {
    @ExcelProperty(value = "序号", index = 0)
    private String indexNo;
    @ExcelProperty(value = "问题分类", index = 1)
    private String classification;
    @ExcelProperty(value = "项目计划", index = 2)
    private String planTitle;
    @ExcelProperty(value = "产品", index = 3)
    private String productName;
    @ExcelProperty(value = "状态", index = 4)
    private String statusName;
    @ExcelProperty(value = "优先级展示", index = 5)
    private String priorityName;
    @ExcelProperty(value = "问题描述", index = 6)
    private String description;
    @ExcelProperty(value = "处理结果", index = 7)
    private String result;
    @ExcelProperty(value = "提交人", index = 8)
    private String submitterName;
    @ExcelProperty(value = "负责人", index = 9)
    private String chargePersonName;
    @ExcelProperty(value = "创建时间", index = 10)
    private Date createTime;
}
