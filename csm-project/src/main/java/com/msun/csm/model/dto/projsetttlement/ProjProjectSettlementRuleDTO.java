package com.msun.csm.model.dto.projsetttlement;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotBlank;

import com.msun.csm.model.vo.projsettlement.ShowFile;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-06-17 10:49:37
 */

@Data
public class ProjProjectSettlementRuleDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String projectSettlementRuleId;

    /**
     * 项目信息id
     */
    @ApiModelProperty(value = "项目信息id")
    @NotBlank(message = "项目id不能为空")
    private String projectInfoId;

    /**
     * 项目规则内容
     */
    @ApiModelProperty(value = "项目规则内容")
    private String projectRuleContent;

    /**
     * 可以下载的文件内容
     */
    @ApiModelProperty(value = "可以下载的文件内容")
    List<ShowFile> documentList;

    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer orderNo;
    /**
     * 项目规则编码
     */
    @ApiModelProperty(value = "项目规则编码")
    private String projectRuleCode;

    /**
     * 是否必须此条件：0.否；1.是
     */
    @ApiModelProperty(value = "是否必须此条件：0.否；1.是")
    private Integer requiredFlag;

    /**
     * 项目入驻表主键
     */
    @ApiModelProperty(value = "项目入驻表主键")
    private String projectSettlementId;

    /**
     * 是否有模版文件：0.否；1.是
     */
    @ApiModelProperty(value = "是否有模版文件：0.否；1.是")
    private Integer templateFlag;

    /**
     * 是否可编辑
     */
    @ApiModelProperty(value = "是否可编辑")
    private Boolean edit;

    /**
     * 验证方式：no 不验证；upload必须上传文件；prompt仅提示必须满足；
     */
    @ApiModelProperty(value = "验证方式：no 不验证；upload必须上传文件；prompt仅提示必须满足；")
    private String verityWay;
}
