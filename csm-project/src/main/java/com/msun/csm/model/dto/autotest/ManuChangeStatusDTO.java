package com.msun.csm.model.dto.autotest;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-02-18 03:07:31
 */

@Data
public class ManuChangeStatusDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 测试记录主键. 检测状态和原因会根据此id更新
     */
    @ApiModelProperty(value = "测试记录主键. 检测状态和原因会根据此id更新")
    @NotNull(message = "测试记录主键不能为空")
    private Long projAutoTestRecordId;

    /**
     * 检测状态标识
     */
    @ApiModelProperty(value = "检测状态标识. 1通过, 0不通过")
    @NotNull(message = "检测状态标识")
    private Integer detectStatusFlag;

    /**
     * 通过原因
     */
    @ApiModelProperty(value = "通过原因")
    private String passReason;

}
