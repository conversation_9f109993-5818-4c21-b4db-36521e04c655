package com.msun.csm.model.resp.issue;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

import com.msun.csm.dao.entity.proj.ProjIssueInfo;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/12/3
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IssueDataResp extends ProjIssueInfo {

    /**
     * 优先级 -原返回值-todoPriorityName
     */
    @ApiModelProperty(value = "优先级展示")
    private String priorityName;

    /**
     * 问题状态-原返回值-todoStatusName
     */
    @ApiModelProperty(value = "问题状态展示")
    private String statusName;

    /**
     * 问题负责人 -原返回值-todoChargePersonName
     */
    @ApiModelProperty(value = "问题负责人-用户名")
    private String chargePersonName;

    /**
     * 问题所属运营平台产品id-如果不满足，-1 其他 -原返回值-todoProductName
     */
    @ApiModelProperty(value = "问题所属运营平台产品名称-如果不满足，-1 其他")
    private String productName;

    /**
     * 问题记录人
     */
    private String createrName;

    /**
     * 更新人-用户名
     */
    private String updaterName;

    /**
     * 分组查询自数据结构
     */
    private List<IssueDataResp> children;

    /**
     * 问题提出人
     */
    private String submitterName;

    /**
     * 项目计划标题
     */
    private String planTitle;



    /**
     * 扣分分类名称
     */
    private String deductionTypeName;

    /**
     * 扣分分类编码
     */
    private String deductionTypeCode;

    /**
     * 扣分分值
     */
    private BigDecimal deductionScore;

    /**
     * 问题分类主键
     */
    private Long issueClassificationId;

    /**
     * 验收分类编码
     */
    private String acceptanceClassificationCode;

    /**
     * 验收分类名称
     */
    private String acceptanceClassificationName;

}
