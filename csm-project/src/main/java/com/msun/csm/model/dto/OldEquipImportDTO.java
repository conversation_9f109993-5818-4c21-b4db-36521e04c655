package com.msun.csm.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* @description:
* @fileName: oldEquipImportDTO.java
* @author: lius3
* @createAt: 2024/10/22 14:51
* @updateBy: lius3
* @remark: Copyright
*/
@Data
public class OldEquipImportDTO {

    /**
     * 客户id
     */
    @ApiModelProperty ("客户id")
    private Long customInfoId;

    /**
     * 项目id
     */
    @ApiModelProperty ("项目id")
    private Long projectInfoId;

    /**
     * 产品编码(LIS、PACS、HD)
     */
    @ApiModelProperty("产品编码(LIS、PACS、HD)")
    private String productCode;

    /**
     * 数据json串
     */
    @ApiModelProperty("数据json串")
    private String jsonData;
}
