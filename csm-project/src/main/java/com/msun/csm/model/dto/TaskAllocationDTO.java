package com.msun.csm.model.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:准备阶段任务分配信息回传
 * @fileName:
 * @author:<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @updateBy:
 * @Date:Created in 13:49 2024/5/14
 * @remark:
 */
@Data
public class TaskAllocationDTO {


    /**
     * 里程碑节点list
     */
    @ApiModelProperty ("里程碑节点list")
    List<MilestoneInfoDTO> milestoneInfoList;

    /**
     * 医院信息list
     */
    @ApiModelProperty ("医院信息list")
    List<HospitalInfoDTO> hospitalInfoList;
    @ApiModelProperty ("项目id")
    private Long projectInfoId;
    @ApiModelProperty ("里程碑节点id")
    private Long milestoneInfoId;

    /**
     * 判断该项目是否是单体
     */
    @ApiModelProperty("true:单体 false:区域")
    private Boolean isSingle;
}
