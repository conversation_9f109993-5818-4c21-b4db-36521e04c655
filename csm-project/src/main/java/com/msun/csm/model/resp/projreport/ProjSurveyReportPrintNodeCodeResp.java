package com.msun.csm.model.resp.projreport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@ApiModel(description = "节点数据")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjSurveyReportPrintNodeCodeResp {

    /**
     * 补充图片
     */
    @ApiModelProperty(value = "使用用途")
    private String usePurposeImg;
    /**
     * 完成结果图片
     */
    @ApiModelProperty(value = "默认样式")
    private String defaultImg;
    /**
     * 打印节点
     */
    @ApiModelProperty(value = "打印节点")
    private String printDataCode;

    @ApiModelProperty(value = "打印节点名称")
    private String reportName;

    @ApiModelProperty(value = "产品名称")
    private String systemName;

    @ApiModelProperty(value = "当前项目是否已存储")
    private Boolean isSaveFlag;

    @ApiModelProperty(value = "主键")
    private Long surveyReportId;

    @ApiModelProperty(value = "是否是前后项目")
    private Boolean isBeforeAndAfterProjectsFlag;

}
