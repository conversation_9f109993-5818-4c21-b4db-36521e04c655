package com.msun.csm.model.req.projreport;

import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.model.resp.projreport.ProjSurveyReportPrintNodeCodeResp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@ApiModel(description = "批量保存")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjSurveyReportUpdateListReq {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    @ApiModelProperty(value = "主键")
    private Long surveyReportId;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户信息ID")
    private Long customerInfoId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

    /**
     * 医院id
     */
    @ApiModelProperty(value = "医院id")
    private Long hospitalInfoId;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private Long yyProductId;

    @ApiModelProperty(value = "节点内容")
    private List<ProjSurveyReportPrintNodeCodeResp> list;

}
