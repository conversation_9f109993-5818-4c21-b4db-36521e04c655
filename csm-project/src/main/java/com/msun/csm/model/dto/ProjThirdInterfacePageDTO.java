package com.msun.csm.model.dto;

import com.msun.csm.common.model.dto.BasePageDTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/09/12/9:28
 */
@Data
public class ProjThirdInterfacePageDTO extends BasePageDTO {

    /**
     * 实施地客户id
     */
    @ApiModelProperty (value = "实施地客户id")
    private Long customInfoId;

    /**
     * 项目类型【1：单体；2：区域】
     */
    @ApiModelProperty (value = "项目类型【1：单体；2：区域】")
    private Integer projectType;

    /**
     * 项目id
     */
    @ApiModelProperty (value = "项目id")
    private Long projectInfoId;

    /**
     * 医院id
     */
    @ApiModelProperty (value = "医院id")
    private Long hospitalInfoId;

    /**
     * 三方接口主键id
     */
    @ApiModelProperty (value = "三方接口主键id")
    private Long thirdInterfaceId;

    /**
     * 接口字典名称
     */
    @ApiModelProperty (value = "接口字典名称")
    private String dictInterfaceName;

    /**
     * 上线必备标识【0：否；1：是】
     */
    @ApiModelProperty (value = "上线必备标识【0：否；1：是】")
    private Integer onlineFlag;

    /**
     * 接口状态【
     * 初始阶段： 0：未申请；1：提交裁定；
     * 裁定阶段： 11：裁定驳回；12、待评审；13：裁定通过；14：接口分公司驳回
     * 测试阶段： 21、测试环境申请授权；22：测试环境授权通过；23：研发中；24、测试环境测试完成；
     * 正式阶段： 31：申请正式环境授权；32：正式环境授权通过；33：研发完成；【下载SDK操作】
     * 50:接口完成 【提交完成按钮触发：三方对接的检测正式环境测试结果；项目组对接 检测正式环境测试结果与是否下载SDK】
     * 】
     */
    @ApiModelProperty (
            value = "接口状态【\n"
                    + "初始阶段： 0：未申请；1：提交裁定；\n"
                    + "裁定阶段： 11：裁定驳回；12、待评审；13：裁定通过；14：接口分公司驳回\n"
                    + "测试阶段： 21、测试环境申请授权；22：测试环境授权通过；23：研发中；24、测试环境测试完成；\n"
                    + "正式阶段： 31：申请正式环境授权；32：正式环境授权通过；33：研发完成；【下载SDK操作】\n"
                    + "50:接口完成 【提交完成按钮触发：三方对接的检测正式环境测试结果；项目组对接 检测正式环境测试结果与是否下载SDK】\n"
                    + "】")
    private Integer status;


    /**
     * 实现方式【0：三方接口对接；1：产品对接；2：项目组对接】
     */
    @ApiModelProperty (value = "实现方式【0：三方接口对接；1：产品对接；2：项目组对接】")
    private Integer implementsType;

    @ApiModelProperty ("委托书是否上传【0：否；1：是】")
    private Integer authLetterFlag;

    /**
     * 运维平台反馈单id
     */
    @ApiModelProperty("运维平台反馈单id")
    private Long feedbackId;

    /**
     * 入口标识：0项目工具
     */
    @ApiModelProperty("入口标识：0项目工具")
    private Integer source;

    /**
     * 角色类型：1-前端角色；2-后端角色
     */
    private Integer roleType;
}
