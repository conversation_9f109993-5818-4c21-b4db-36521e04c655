package com.msun.csm.model.vo;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-05-10 02:21:38
 */

@Data
@TableName (schema = "csm")
public class ProjResearchPlanContentVO extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty (value = "调研计划内容编码")
    private int researchCode;

    @ApiModelProperty (value = "调研计划内容")
    private String researchContent;

    @ApiModelProperty (value = "调研计划内容是否设置")
    private boolean choosed;

}
