package com.msun.csm.model.vo.projfileupload;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class FindCustomFileVO {

    /**
     * 客户ID
     */
    @ApiModelProperty(value = "客户ID")
    private String customInfoId;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customName;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目团队名称
     */
    @ApiModelProperty(value = "项目团队名称")
    private String projectTeamName;

    /**
     * 项目经理
     */
    @ApiModelProperty(value = "项目经理")
    private String projectLeaderName;

    /**
     * 项目状态
     */
    @ApiModelProperty(value = "项目状态")
    private Integer projectDeliverStatus;


    /**
     * 项目状态名称
     */
    @ApiModelProperty(value = "项目状态名称")
    private String projectDeliverStatusName;

    /**
     * 派工时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "派工时间")
    private Date workTime;

    /**
     * 入驻时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "入驻时间")
    private Date settleInTime;

    /**
     * 上线时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "上线时间")
    private Date onlineTime;

    /**
     * 验收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "验收时间")
    private Date acceptTime;

    /**
     * 资料上传总数
     */
    @ApiModelProperty(value = "资料上传总数")
    private Integer fileUploadCount;
}
