package com.msun.csm.model.convert;

import org.mapstruct.Mapper;

import com.msun.csm.common.model.convert.Dto2PoBaseConvert;
import com.msun.csm.common.model.convert.Dto2VoBaseConvert;
import com.msun.csm.common.model.convert.Vo2PoBaseConvert;
import com.msun.csm.dao.entity.proj.ProjProductSupplementaryRecord;
import com.msun.csm.model.dto.ProjProductSupplementaryRecordDTO;
import com.msun.csm.model.vo.ProjProductSupplementaryRecordVO;

/**
 * 特批/工单产品补录记录表(ProjProductSupplementaryRecord)数据转换
 *
 * <AUTHOR>
 * @since 2024-07-04 16:43:18
 */
@Mapper(componentModel = "spring")
public interface ProjProductSupplementaryRecordConvert extends Dto2PoBaseConvert<ProjProductSupplementaryRecordDTO, ProjProductSupplementaryRecord>,
        Dto2VoBaseConvert<ProjProductSupplementaryRecordDTO, ProjProductSupplementaryRecordVO>, Vo2PoBaseConvert<ProjProductSupplementaryRecordVO, ProjProductSupplementaryRecord> {

}
