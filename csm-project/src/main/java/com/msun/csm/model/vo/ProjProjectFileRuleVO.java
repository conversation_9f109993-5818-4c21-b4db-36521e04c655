package com.msun.csm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* @description:
* @fileName: ProjProjectFileRuleVO.java
* @author: lius3
* @createAt: 2024/10/12 18:43
* @updateBy: lius3
* @remark: Copyright
*/
@Data
public class ProjProjectFileRuleVO {

    /**
     * 附件项目编码
     */
    @ApiModelProperty("附件项目编码")
    private String fileItemCode;

    /**
     * 附件类型编码
     */
    @ApiModelProperty("附件类型编码")
    private String fileTypeCode;

    /**
     * 附件类型名称
     */
    @ApiModelProperty("附件类型名称")
    private String fileTypeName;

    /**
     * 是否必填：0否1是
     */
    @ApiModelProperty("是否必填：0否1是")
    private Integer requiredFlag;

    /**
     * 排序号
     */
    @ApiModelProperty("排序号")
    private Integer orderNo;

    /**
     * 限制附件类型
     */
    @ApiModelProperty("限制附件类型")
    private String limitType;

    /**
     * 限制附件类型
     */
    @ApiModelProperty("限制附件类型")
    private String[] limitTypeArray;
}
