package com.msun.csm.model.resp.projreport;

import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.dao.entity.report.DictDeliverproVsPrintreportpro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 客户数据统计表(ReportCustomInfo)实体类
 *
 * <AUTHOR>
 * @since 2024-09-27 15:27:17
 */
@ApiModel(description = "菜单")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class DictDeliverproVsPrintreportproResp extends DictDeliverproVsPrintreportpro {

    @ApiModelProperty("实施产品名称")
    private String deliverProductName;

    @ApiModelProperty("创建人")
    private String createrName;

    @ApiModelProperty("修改人")
    private String updaterName;

}
