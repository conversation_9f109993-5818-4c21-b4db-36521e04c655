package com.msun.csm.model.vo;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * AI智能体配置返回对象
 * <AUTHOR>
 * @date 2024/10/10
 */
@Data
@ApiModel(description = "AI智能体配置返回对象")
public class DictAgentChatVO {

    @ApiModelProperty(value = "智能体ID")
    private Long agentChatId;

    @ApiModelProperty(value = "智能体编号")
    private String agentCode;

    @ApiModelProperty(value = "智能体名称")
    private String agentName;

    @ApiModelProperty(value = "智能体测试地址")
    private String agentAddress;

    @ApiModelProperty(value = "智能体生产地址")
    private String agentAddressProduce;

    @ApiModelProperty(value = "智能体密钥")
    private String agentKey;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
