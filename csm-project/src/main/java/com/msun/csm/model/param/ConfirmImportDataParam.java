package com.msun.csm.model.param;

import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ConfirmImportDataParam {

    /**
     * 项目上线步骤ID
     */
    @NotNull
    @ApiModelProperty(value = "项目上线步骤ID")
    private Long projOnlineStepId;

    /**
     * 批量确认完成的医院信息
     */
    private List<ConfirmImportDataItemParam> itemList;

}
