package com.msun.csm.model.convert;

import org.mapstruct.Mapper;

import com.msun.csm.common.model.convert.Vo2PoBaseConvert;
import com.msun.csm.dao.entity.proj.ProjApplyOrderProductRecordRelative;
import com.msun.csm.model.vo.ProjApplyOrderProductRecordRelativeVO;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface ProjApplyOrderProductRecordRelativeConvert extends Vo2PoBaseConvert<ProjApplyOrderProductRecordRelativeVO, ProjApplyOrderProductRecordRelative> {
}
