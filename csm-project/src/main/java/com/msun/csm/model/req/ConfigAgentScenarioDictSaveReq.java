package com.msun.csm.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * AI智能体场景配置新增/修改请求参数
 * <AUTHOR> @date 2025/07/07
 */
@Data
@ApiModel(description = "AI智能体场景配置新增/修改请求参数")
public class ConfigAgentScenarioDictSaveReq {

    @ApiModelProperty(value = "配置ID（修改时必传，新增时不传）", example = "1")
    private Long agentScenarioConfigId;

    @ApiModelProperty(value = "智能体编号", required = true, example = "AI_IMAGE_DETECTION")
    @NotBlank(message = "智能体编号不能为空")
    private String agentCode;

    @ApiModelProperty(value = "应用场景编码", required = true, example = "IMAGE_DETECTION")
    @NotBlank(message = "应用场景编码不能为空")
    private String scenarioCode;

    @ApiModelProperty(value = "应用场景描述", required = true, example = "图片检测")
    @NotBlank(message = "应用场景描述不能为空")
    private String scenarioDesc;

    @ApiModelProperty(value = "应用场景提示词", example = "请分析这张图片")
    private String scenarioPrompt;
}
