package com.msun.csm.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * AI智能体场景配置新增/修改请求参数
 *
 * <AUTHOR> @date 2025/07/07
 */
@Data
@ApiModel(description = "AI智能体场景配置新增/修改请求参数")
public class ConfigAgentScenarioDictSaveReq {

    @ApiModelProperty(value = "配置ID（修改时必传，新增时不传）", example = "1")
    private Long agentScenarioConfigId;

    @ApiModelProperty(value = "应用场景编码", required = true)
    @NotBlank(message = "应用场景编码不能为空")
    private String scenarioCode;

    @ApiModelProperty(value = "应用场景描述", required = true)
    @NotBlank(message = "应用场景描述不能为空")
    private String scenarioDesc;

    @ApiModelProperty(value = "应用场景提示词",required = true)
    @NotBlank(message = "应用场景提示词不能为空")
    private String scenarioPrompt;

    @ApiModelProperty(value = "智能体编号", required = true)
    @NotBlank(message = "智能体编号不能为空")
    private String agentCode;

    @ApiModelProperty(value = "智能体名称", required = true)
    @NotBlank(message = "智能体名称不能为空")
    private String agentName;

}
