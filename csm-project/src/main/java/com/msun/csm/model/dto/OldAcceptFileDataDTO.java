package com.msun.csm.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/11/26/15:59
 */
@Data
public class OldAcceptFileDataDTO {


    @ApiModelProperty("文件路径")
    private String fileUrl;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("文件类型【 1. 项目验收报告2. 应用调查表3. 验收自检表4. 项目节点日期修正表5. 其他自定义附件6. 质管验收通过附件】")
    private Integer fileType;

    @ApiModelProperty("老系统项目id")
    private Long oldProjectInfoId;

    @ApiModelProperty("新系统项目id")
    private Long newProjectInfoId;
}
