package com.msun.csm.model.dto.networkdetect;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-05-16 09:02:04
 */

@Api("获取域名集合传参")
@Data
public class ProjNetworkDetDomainDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotNull
    @ApiModelProperty(value = "项目id")
    private String projectInfoId;

    @NotNull
    @ApiModelProperty(value = "系统类型")
    private Integer sysType;
}
