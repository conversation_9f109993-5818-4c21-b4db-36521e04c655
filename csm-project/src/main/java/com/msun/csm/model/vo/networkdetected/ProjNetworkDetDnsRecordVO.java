package com.msun.csm.model.vo.networkdetected;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-05-14 09:09:23
 */

@Data
public class ProjNetworkDetDnsRecordVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 回传id, 也是平台定义id, 使用医院主键
     */
    @ApiModelProperty (value = "回传id, 也是平台定义id, 使用医院主键")
    private String logId;

    /**
     * 测试使用的域名
     */
    @ApiModelProperty (value = "测试使用的域名")
    private String webUrl;

    /**
     * 测试的DNS地址
     */
    @ApiModelProperty (value = "测试的DNS地址")
    private String dnsServer;

    /**
     * 本机IP地址
     */
    @ApiModelProperty (value = "本机IP地址")
    private String localIpAddress;

    /**
     * 测试的端口
     */
    @ApiModelProperty (value = "测试的端口")
    private int port;

    /**
     * 测试数量
     */
    @ApiModelProperty (value = "测试数量")
    private int count;

    /**
     * 解析较慢的个数（会在工具里写死一个评判时间，比如大约500ms或者解析失败的就算解析较慢的个数）
     */
    @ApiModelProperty (value = "解析较慢的个数（会在工具里写死一个评判时间，比如大约500ms或者解析失败的就算解析较慢的个数）")
    private int slowResolveCount;

    /**
     * 解析较快的个数（也就是总数量减去解析慢和失败的个数）
     */
    @ApiModelProperty (value = "解析较快的个数（也就是总数量减去解析慢和失败的个数）")
    private int fastResolveCount;

    /**
     * 随机码
     */
    @ApiModelProperty (value = "随机码 ( 区分每一台电脑不同轮次的测试 )")
    private String randomCode;

    /**
     * 域名解析的ip数量
     */
    @ApiModelProperty (value = "域名解析的ip数量")
    private int ipCount;

    /**
     * 成功率
     */
    @ApiModelProperty (value = "成功率")
    private String successRate;

    /**
     * 解析的ip地址
     */
    @Schema(description = "解析的ip地址")
    private String resolveIpAddress;

    /**
     * 用于工具提交给交付平台结果标识，比如 0 失败, 1 稳定 3 波动
     */
    @Schema(description = "0 失败, 1 稳定 3 波动")
    private int detectStatus;

}
