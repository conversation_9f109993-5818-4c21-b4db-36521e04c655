package com.msun.csm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 手麻设备发送云健康VO
 *
 * @Author: duxu
 * @Date: 2024/12/05/16:48
 */
@Data
public class AimsEquipSendCloudVO {

    @ApiModelProperty("手麻设备主键id")
    private Long equipRecordVsAimsId;

    @ApiModelProperty("手麻设备类型")
    private Long equipTypeId;

    @ApiModelProperty("云健康医院id")
    private Long cloudHospitalId;

    @ApiModelProperty("云健康机构id")
    private Long orgId;

    @ApiModelProperty("设备位置标识【1：手术间；2：复苏间】")
    private Integer equipPositionFlag;

    @ApiModelProperty("设备位置名称")
    private String equipPositionName;

    @ApiModelProperty("设备类型名称")
    private String equipTypeName;

    @ApiModelProperty("中央监护仪设备id")
    private Long centerMonDeviceId;

    @ApiModelProperty("监护仪配置端口")
    private Integer monDeviceConfigPort;

    @ApiModelProperty("监听ip")
    private String listenIp;

    @ApiModelProperty("监听端口")
    private Integer listenPort;

    @ApiModelProperty("监护仪编号")
    private String monitorCode;

    @ApiModelProperty("传输协议")
    private Integer protocolType;

    @ApiModelProperty("系列")
    private String seriesName;

    @ApiModelProperty("设备ip")
    private String equipIp;

    /**
     * 设备型号名称
     */
    @ApiModelProperty(value = "设备型号名称")
    private String equipModelName;

    @ApiModelProperty("是否中央监护仪. 0: 否, 1: 是")
    private Integer centerMonDeviceFlag;
}
