package com.msun.csm.model.dto.yunweiplatform;

import lombok.Data;

/**
 * 医院信息
 */
@Data
public class SaveEnvHospital {
    /**
     * 行政区划格式：省/市/县
     */
    private String administrativeDivisions;
    /**
     * 医院信息id(交付平台医院信息主键)
     */
    private String customerInfoId;
    /**
     * 行政区划编码格式：省/市/县
     */
    private String administrativeCode;
    /**
     * 医院名称
     */
    private String hospitalName;
    /**
     * 医院类型：单体医院(hospital)、卫生院(clinic)、便民门诊/分院(branch)、电销客户(electricSales)
     */
    private String hospitalType;
    /**
     * 主院Id
     */
    private Long mainHospitalId;
    /**
     * 主院名称
     */
    private String mainHospitalName;

    /**
     * 是否是主院
     */
    private String isMainHospital;

    /**
     * 医院Id
     */
    private Long hospitalId;
}
