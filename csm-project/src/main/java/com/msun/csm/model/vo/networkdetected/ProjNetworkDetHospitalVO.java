package com.msun.csm.model.vo.networkdetected;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-05-16 09:02:04
 */

@Data
public class ProjNetworkDetHospitalVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 医院id
     */
    @ApiModelProperty(value = "医院id")
    private Long hospitalInfoId;

    /**
     * 检测域名
     */
    @ApiModelProperty(value = "检测域名")
    private String detectDomain;

    /**
     * 检测端口
     */
    @ApiModelProperty(value = "检测端口")
    private Long detectPort;

    /**
     * 终端数量
     */
    @ApiModelProperty(value = "终端数量")
    private Long clientCount;

    /**
     * 检测情况
     */
    @ApiModelProperty(value = "检测情况")
    private Short detectStatus;

    /**
     * 产品使用的内网端口
     */
    @Schema(description = "产品使用的内网端口（用空格分割，可填多个）")
    private String productUsedIntranetPort;

    /**
     * 产品使用的外网端口
     */
    @Schema (description = "产品使用的外网端口")
    private String productUsedExternalPort;

    /**
     * 网络检测使用的DNS服务器地址
     */
    @Schema (description = "网络检测使用的DNS服务器地址")
    private String dns;

}
