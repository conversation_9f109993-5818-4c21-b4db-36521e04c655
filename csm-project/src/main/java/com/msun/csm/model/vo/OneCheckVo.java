package com.msun.csm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/09/27/10:48
 */
@Data
public class OneCheckVo {

    @ApiModelProperty ("医院id")
    private Long hospitalInfoId;

    @ApiModelProperty ("医院名称")
    private String hospitalName;

    @ApiModelProperty ("产品id")
    private Long yyProductId;

    @ApiModelProperty ("产品名称")
    private String productName;

    @ApiModelProperty ("待办标题")
    private String taskTitle;

    /**
     * 检测标准
     */
    @ApiModelProperty ("检测标准")
    private String validateStandards;


    @ApiModelProperty ("检测结果")
    private String validateResult;

    @ApiModelProperty ("是否检测成功【检测结论】")
    private Boolean validateSuccess;
}
