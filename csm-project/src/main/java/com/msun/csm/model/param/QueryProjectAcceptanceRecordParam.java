package com.msun.csm.model.param;

import java.util.Date;

import javax.validation.constraints.NotNull;

import org.springframework.format.annotation.DateTimeFormat;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryProjectAcceptanceRecordParam {

    /**
     * 当前页
     */
    private Integer pageNum;

    /**
     * 每页数据条数
     */
    private Integer pageSize;

    /**
     * 客户ID
     */
    private Long customInfoId;

    /**
     * 工单编号
     */
    private String projectNumber;

    /**
     * 验收状态
     */
    private String status;

    /**
     * 开始时间，格式为 yyyy-MM-dd HH:mm:ss
     */
    @NotNull(message = "参数【startTime】不可为null")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间，格式为 yyyy-MM-dd HH:mm:ss
     */
    @NotNull(message = "参数【endTime】不可为null")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    public Integer getOffsetNumber() {
        return (this.pageNum - 1) * pageSize;
    }

}
