package com.msun.csm.model.req.projreport;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;

import com.msun.csm.util.FastJsonCustomDeserializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @TableName proj_hospital_terminal_config
 */
@ApiModel(description = "医院电脑配置")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjHospitalTerminalConfigReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "客户id")
    private Long customInfoId;
    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

    /** 医院名称 */
    @ApiModelProperty(value = "医院名称")
    private String hospitalName;

    /** 总台数 */
    @ApiModelProperty(value = "总台数")
    private Integer totalUnits;

    /** 符合要求数 */
    @ApiModelProperty(value = "符合要求数")
    private Integer meetRequiredNum;

    /** 计算机ip */
    @ApiModelProperty(value = "计算机ip")
    private String pcIp;

    /** 计算机名称 */
    @ApiModelProperty(value = "计算机名称")
    private String pcName;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 操作系统 */
    @ApiModelProperty(value = "操作系统")
    private String operatSystem;

    /** 登录USER_ID" */
    @ApiModelProperty(value = "登录USER_ID")
    private String sysUserId;

    /** 位数 */
    @ApiModelProperty(value = "位数")
    private String computerNumber;

    /** 内存 */

    @ApiModelProperty(value = "内存")
    @JSONField(deserializeUsing = FastJsonCustomDeserializer.class)
    private Integer memory = 0;

    /** 登录工号 */
    @ApiModelProperty(value = "登录工号")
    private String userCode;

    /** 登录人姓名 */
    @ApiModelProperty(value = "登录人姓名")
    private String userName;

    /** 科室id */
    @ApiModelProperty(value = "科室id")
    private String deptId;

    /** 科室 */
    @ApiModelProperty(value = "科室")
    private String deptName;

    /** 配置符合标识 */
    @ApiModelProperty(value = "配置符合标识")
    private String configMeetFlag;
}
