package com.msun.csm.model.resp.projform;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@ApiModel(description = "菜单")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjSurveyFormMenuResp {
    private static final long serialVersionUID = 1L;

    /**
     * code
     */
    @ApiModelProperty(value = "code")
    private String name;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String title;
}
