package com.msun.csm.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HisBaseDataInfoDTO {

    /**
     * 医院ID
     */
    private String hospitalInfoId;

    /**
     * 医院名称
     */
    private String hospitalName;

    /**
     * 科室数量
     */
    private String departmentCount;

    /**
     * 人员总数
     */
    private String allPersonCount;

    /**
     * 未分配角色的人员数量
     */
    private String unassignedRoleCount;

    /**
     * 病区基础字典已维护数量
     */
    private String inpatientWardCount;

    /**
     * 床位数量
     */
    private String allBedCount;

    /**
     * 未维护床位费的床位数量
     */
    private String noChargesBedCount;

    /**
     * 计价数量
     */
    private String allChargeCount;

    /**
     * 药品数量
     */
    private String allDrugCount;

    /**
     * 已对照贯标码药品数量
     */
    private String checkedMedicalInsuranceDrugCount;

    /**
     * 抗菌药物数量
     */
    private String antibioticsDrugCount;

    /**
     * 材料数量
     */
    private String allMaterialsCount;

    /**
     * 已对照贯标码材料数量
     */
    private String checkedMedicalInsuranceMaterialsCount;

    /**
     * 电子签名维护数量
     */
    private String electronicSignatureCount;
}
