package com.msun.csm.model.param;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuerySimulationHospitalOptionParam {

    /**
     * 客户ID
     */
    @NotNull(message = "【customInfoId】不可为空")
    private Long customInfoId;

    /**
     * 项目ID
     */
    @NotNull(message = "【projectInfoId】不可为空")
    private Long projectInfoId;

    /**
     * 环境：prod-生产环境；test-测试环境
     */
    @NotNull(message = "【environment】不可为空")
    private String environment;

}
