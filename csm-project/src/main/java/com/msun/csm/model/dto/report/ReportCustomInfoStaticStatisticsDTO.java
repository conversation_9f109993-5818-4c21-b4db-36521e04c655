package com.msun.csm.model.dto.report;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/24/11:53
 */
@Data
public class ReportCustomInfoStaticStatisticsDTO {

    @ApiModelProperty("客户类型： 单体/区域标识  1.单体 2：区域 3电销")
    private Integer customType;
    @ApiModelProperty("老换新标识： 1:老换新;2:新客户;3:老体系;")
    private Integer upgradationType;

    @ApiModelProperty("项目状态： 0 入驻未上线  1上线未验收 ")
    private Integer projectStatus;

    @ApiModelProperty("自定义：开始时间")
    private Date selectStartTime;

    @ApiModelProperty("自定义：结束时间")
    private Date selectEndTime;
}
