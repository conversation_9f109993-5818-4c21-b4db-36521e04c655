package com.msun.csm.model.req.projreport.statis;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 统计报表主表(PROJ_STATISTICAL_REPORT_MAIN)
 *
 * <AUTHOR>
 * @version 1.0.0 2025-01-13
 */

@Data
@ApiModel
public class ProjStatisticalReportMainDeliveryReq {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotNull(message = "主键不能为空")
    private Long statisticalReportMainId;

    @ApiModelProperty(value = "菜单名称")
    private String menuName;

    @ApiModelProperty(value = "父级菜单id")
    private String parentId;

}