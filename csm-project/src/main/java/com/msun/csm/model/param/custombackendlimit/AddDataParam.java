package com.msun.csm.model.param.custombackendlimit;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class AddDataParam {

    /**
     * 客户ID
     */
    @NotNull(message = "参数【customInfoId】不可为null")
    private Long customInfoId;

    /**
     * 项目ID
     */
    @NotNull(message = "参数【projectInfoId】不可为null")
    private Long projectInfoId;

    /**
     * 业务服务团队运营平台部门ID
     */
    private Long businessTeamId;

    /**
     * 数据服务团队运营平台部门ID
     */
    private Long dataTeamId;

    /**
     * 接口服务团队运营平台部门ID
     */
    private Long interfaceTeamId;

    /**
     * 勾选的后端服务团队：bustype-业务服务团队；datatype-数据服务团队；interfacetype-接口服务团队
     */
    private List<String> serverType;

}
