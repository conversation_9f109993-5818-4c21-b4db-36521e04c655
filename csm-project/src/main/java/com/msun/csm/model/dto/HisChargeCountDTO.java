package com.msun.csm.model.dto;


import com.alibaba.fastjson.annotation.JSONField;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HisChargeCountDTO {

    /**
     * 医院ID
     */
    @JSONField(name = "hospital_id")
    private String hospitalId;

    /**
     * 医院名称
     */
    @JSONField(name = "hospital_name")
    private String hospitalName;

    /**
     * 计价数量
     */
    @JSONField(name = "total_count")
    private String allChargeCount;


}
