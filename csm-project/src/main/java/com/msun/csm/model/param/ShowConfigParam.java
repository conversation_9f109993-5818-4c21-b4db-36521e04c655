package com.msun.csm.model.param;

import javax.validation.constraints.NotNull;

import lombok.Data;

@Data
public class ShowConfigParam {

    /**
     * 医院信息ID
     */
    @NotNull(message = "参数【hospitalInfoId】不可为null")
    private Long hospitalInfoId;

    /**
     * 项目信息ID
     */
    @NotNull(message = "参数【projectInfoId】不可为null")
    private Long projectInfoId;

    /**
     * 产品ID
     */
    @NotNull(message = "参数【yyProductId】不可为null")
    private Long yyProductId;
}
