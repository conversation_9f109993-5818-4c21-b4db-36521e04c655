package com.msun.csm.model.param;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuerySimulationHospitalParam {

    /**
     * 客户ID
     */
    @NotNull(message = "【customInfoId】不可为空")
    private Long customInfoId;

    /**
     * 项目ID
     */
    @NotNull(message = "【projectInfoId】不可为空")
    private Long projectInfoId;

}
