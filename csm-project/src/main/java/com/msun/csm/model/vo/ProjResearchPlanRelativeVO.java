package com.msun.csm.model.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.msun.csm.common.model.po.BasePO;
import com.msun.csm.model.struct.Struct;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-05-10 11:17:54
 */

@Data
public class ProjResearchPlanRelativeVO extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 提供给前端列元数据集合
     */
    private List<Struct> cols;


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long projResearchPlanId;

    /**
     * 片区名称
     */
    @ApiModelProperty(value = "片区名称")
    private String sectionName;

    /**
     * 医院名称
     */
    @ApiModelProperty(value = "医院名称")
    private String hospitalName;

    /**
     * 调研内容编码
     */
    @ApiModelProperty(value = "调研内容编码")
    private int researchCode;

    /**
     * 医院id
     */
    @ApiModelProperty(value = "医院id")
    private String hospitalInfoId;

    /**
     * 调研计划开始时间
     */
    @ApiModelProperty(value = "调研计划开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expectStartTime;

    /**
     * 调研计划结束时间
     */
    @ApiModelProperty(value = "调研计划结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expectCompTime;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

    /**
     * 调研计划范围值
     */
    @ApiModelProperty(value = "调研计划范围值")
    private List<String> planTimeRange;

    /**
     * 项目负责人
     */
    @ApiModelProperty(value = "项目负责人")
    private String leaderName;

    /**
     * 调研负责人id
     */
    @Schema(description = "调研负责人id")
    private String leaderId;

    /**
     * 调研状态编码名称
     */
    @ApiModelProperty(value = "调研状态编码名称")
    private String researchStatusName;

    /**
     * 调研状态
     */
    @ApiModelProperty(value = "调研状态")
    private int researchStatus;

    /**
     * 调研内容编码连结字符串, 使用逗号分割, 编码相连
     */
    @Schema(description = "调研内容编码连结字符串, 使用逗号分割, 编码相连")
    private String researchCodeStr;

    /**
     * 结果来源内容
     */
    @ApiModelProperty(value = "结果来源内容")
    private String sourceResultContent;

    @ApiModelProperty(value = "调研内容")
    private List<ProjResearchPlanContentVO> planContents;

    @ApiModelProperty(value = "调研主负责人")
    private List<String> planLeaders;

    @ApiModelProperty(value = "调研辅助负责人")
    private List<String> planSecondLeaders;

    /**
     * 调研负责人id连结字符串, 使用逗号分割, 编码相连
     */
    @Schema(description = "调研负责人id连结字符串, 使用逗号分割, 编码相连")
    private String leaderIdStr;

    /**
     * 调研辅助负责人id连结字符串, 使用逗号分割, 编码相连
     */
    @Schema(description = "调研辅助负责人id连结字符串, 使用逗号分割, 编码相连")
    private String secondLeaderIdStr;

    /**
     * 前端字典使用
     */
    @Data
    @AllArgsConstructor
    public static class DictStruct {
        /**
         * 编码
         */
        private String id;

        /**
         * 名称
         */
        private String name;
    }
}
