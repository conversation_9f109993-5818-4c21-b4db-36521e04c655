package com.msun.csm.model.req.todotask;

import java.util.List;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2025/1/7
 */
@Data
public class QueryTodoTaskReq {

    /**
     * 项目信息id
     */
    @NotNull(message = "项目信息不能为空")
    private Long projectInfoId;

    /**
     * 医院信息id
     */
    private List<Long> hospitalInfoIdList;

    /**
     * 项目计划 id
     */
    private Long projectPlanId;

    /**
     * 关键字
     */
    private String keyWord;

    /**
     * 负责人id
     */
    private Long executorId;

    /**
     * 问题状态-0 未完成，1已完成
     */
    private Integer status;

    /**
     * 是否超期 0 未超期，1超期
     */
    private Integer overTimeFlag;

    /**
     * 是否重点关注 0 未关注，1 关注
     */
    private Integer attentionFlag;

    /**
     * 阶段ID集合
     */
    List<Long> projectPlanStageIdList;

    /**
     * 1-查询前端及前后端项目；2-查询后端及前后短信项目
     */
    private Integer frontOrBackendFlag;
}
