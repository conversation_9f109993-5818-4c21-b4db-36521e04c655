package com.msun.csm.model.dto;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @fileName: DeployResourceApplyEnvDTO.java
 * @author: duxu
 * @createAt: 2022-10-17 09:37
 * @updateBy: lihuasong
 * @remark: Copyright by 众阳健康
 */
@ApiModel
@Data
public class DeployEnvApplyDTO {

    /**
     * 部署类型：新环境上线部署(env)、新产品上线部署(product)、新医院上线部署(hospital)
     */
    @ApiModelProperty(value = "部署类型：新环境上线部署(env)、新产品上线部署(product)、新医院上线部署(hospital)",
            required = true)
    private String deployType;

    /**
     * 部署模式，hospital:单体医院；countryPlat:区县平台；cityPlat:市级平台
     */
    @ApiModelProperty(value = "部署模式，hospital:单体医院；countryPlat:区县平台；cityPlat:市级平台", required = true)
    private String deployMod;

    /**
     * 环境名称
     */
    @ApiModelProperty(value = "环境名称")
    private String envName;

    /**
     * 环境id
     */
    @ApiModelProperty(value = "环境id")
    private Long envId;

    /**
     * 部署申请单id
     */
    @ApiModelProperty("部署申请单id")
    private Long depolyApplyId;

    /**
     * 交付平台申请单id
     */
    @ApiModelProperty(value = "交付平台申请单id", required = true)
    private Long deliverPlatformApplyId;

    /**
     * 状态：0-待审核；1-已审核；2-已驳回；3-环境部署中；4-环境部署完成；5-已交付；6-已撤销
     */
    @ApiModelProperty("状态：0-待审核；1-已审核；2-已驳回；3-环境部署中；4-环境部署完成；5-已交付；6-已撤销")
    private String status;

    /**
     * 单体医院/区县数量
     */
    @ApiModelProperty(value = "单体医院/区县数量", required = true)
    private Integer countryCount;

    /**
     * 运营平台实施客户id
     */
    @ApiModelProperty(value = "运营平台实施客户id")
    private Long customerId;
    /**
     * 单体医院/区县数量
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;


    /**
     * 提交人
     */
    @ApiModelProperty(value = "提交人", required = true)
    private String submitName;

    /**
     * 是否私有云
     */
    @ApiModelProperty(value = "是否私有云", required = true)
    private String isPrivate;

//    /**
//     * 部署客服产品列表
//     */
//    @ApiModelProperty ("部署客服产品列表")
//    private List<DeployResourceApplyProductDTO> deployResourceApplyProductDTOList;

    /**
     * 部署单体医院/区县列表
     */
    @ApiModelProperty("部署单体医院/区县列表")
    private List<DeployResourceApplyCountryDTO> deployResourceApplyCountryDTOList;


    /**
     * 云资源类型  0:众阳云  1:非众阳云
     */
    @ApiModelProperty(value = "云资源类型", required = true)
    private Integer cloudType;

    /**
     * 提交申请占用域名提交id
     */
    private Long preSubmitId;
}
