package com.msun.csm.model.resp.netimprove;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/10/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NetTemplateTitleResp {

    /**
     * 网络改造方案文件内容标题配置表主键
     */
    @ApiModelProperty(value = "网络改造方案文件内容标题配置表主键")
    private Long netTemplateTitleId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

    /**
     * 网络改造方案文件内容标题配置表编码
     */
    @ApiModelProperty(value = "网络改造方案文件内容标题配置表编码")
    private String netTemplateTitleCode;

    /**
     * 网络改造方案文件内容标题配置表名称
     */
    @ApiModelProperty(value = "网络改造方案文件内容标题配置表名称")
    private String netTemplateTitleName;

    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer orderNo;

    /**
     * 网络改造方案文件内容列表
     */
    @ApiModelProperty(value = "网络改造方案文件内容列表")
    private List<NetTemplateContentResp> netTemplateContentRespList;

    /**
     * 端口映射
     */
    @ApiModelProperty(value = "端口映射")
    private List<NetPortMappingResp> netPortMappingRespList;

}
