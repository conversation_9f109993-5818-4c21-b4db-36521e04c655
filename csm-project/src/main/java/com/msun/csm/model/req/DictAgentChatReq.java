package com.msun.csm.model.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class DictAgentChatReq {

    @ApiModelProperty(value = "应用场景编码")
    private String scenarioCode;

    @ApiModelProperty(value = "图片地址集合")
    private List<String> fileUrls;

}


@Data
public class DictAgentChatConfig {

    @ApiModelProperty(value = "应用场景编码")
    private String scenarioCode;

    @ApiModelProperty(value = "图片地址集合")
    private List<String> fileUrls;

}