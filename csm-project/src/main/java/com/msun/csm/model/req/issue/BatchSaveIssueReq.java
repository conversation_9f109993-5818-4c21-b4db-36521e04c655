package com.msun.csm.model.req.issue;

import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/12/3
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BatchSaveIssueReq {

    /**
     * 项目id
     */
    @NotNull(message = "项目id不能为空")
    private Long projectInfoId;
    /**
     * 问题描述 -原参数-todoDescription
     */
    @NotEmpty(message = "问题描述不能为空")
    private List<String> description;

    /**
     * 产品模块-产品id -原参数-todoProductId
     */
    private Integer productId;

    /**
     * 问题分类-基本分类加手填数据，存储string-原参数-todoClassification
     */
    private String classification;

    /**
     * 问题科室-手填数据-原参数-todoDept
     */
    private String dept;

    /**
     * 项目计划表主键
     */
    @NotNull(message = "项目计划表主键不能为空")
    private Long projectPlanId;

    /**
     * 我的待办主键
     */
    private Long todoTaskId;

}
