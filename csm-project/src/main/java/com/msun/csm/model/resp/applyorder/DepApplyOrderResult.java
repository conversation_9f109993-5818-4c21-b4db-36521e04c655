package com.msun.csm.model.resp.applyorder;

import java.util.List;

import lombok.Data;

/**
 * 申请调用结果
 */
@Data
public class DepApplyOrderResult {
    /**
     * 单体医院/区县数量
     */
    private String countryCount;
    /**
     * 客户ID
     */
    private String customerId;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 交付平台申请单id
     */
    private String deliverPlatformApplyId;
    /**
     * 已部署客服产品列表
     */
    private List<DepApplyProductResult> deployApplyProductVOList;
    /**
     * 部署模式，hospital:单体医院；countryPlat:区县平台；cityPlat:市级平台
     */
    private String deployMod;
    /**
     * 交部署客服产品列表
     */
    private List<DepResourceApplyCountryResult> deployResourceApplyCountryVOList;
    /**
     * 部署类型：新环境上线部署(env)、新产品上线部署(product)、新医院上线部署(hospital)
     */
    private String deployType;
    /**
     * 部署申请单id
     */
    private String depolyApplyId;
    /**
     * 环境id
     */
    private String envId;
    /**
     * 环境名称
     */
    private String envName;
    /**
     * 预计完成时间 string(date-time)
     */
    private String estimateTime;
    /**
     * 审批人id
     */
    private String examId;
    /**
     * 审批人
     */
    private String examName;
    /**
     * 审批时间 string(date-time)
     */
    private String examTime;
    /**
     * 办离人id
     */
    private String executeId;
    /**
     * 办离人
     */
    private String executeName;
    /**
     * 资源规划预下载文件路径
     */
    private String filePath;
    /**
     * 主键
     */
    private String id;
    /**
     * 是否为私有云：0-否；1-是
     */
    private String isPrivate;
    /**
     * 驳回人id
     */
    private String rejectId;
    /**
     * 驳回人
     */
    private String rejectName;
    /**
     * 驳回原因
     */
    private String rejectReason;
    /**
     * 驳回时间 string(date-time)
     */
    private String rejectTime;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 状态：0-待审核；1-已审核；2-已驳回；3-环境部署中；4-环境部署完成；5-已交付；6-已撤销
     */
    private String status;
    /**
     * 提交人
     */
    private String submitName;
    /**
     * 未匹配成功的部署客服产品列表
     */
    private List<DepApplyProductResult> unCheckApplyProductVOList;
}
