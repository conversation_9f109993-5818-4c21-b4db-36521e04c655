package com.msun.csm.model.resp.device.analyse;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * lis设备明细项
 */
@Data
public class EquipLisDetailItemResp {

    @ApiModelProperty(value = "仪器项目id，主键")
    private String equipItemId;

    @ApiModelProperty(value = "仪器id")
    private String equipId;

    @ApiModelProperty(value = "明细项目id")
    private String itemId;

    @ApiModelProperty(value = "项目编码")
    private String itemNo;

    @ApiModelProperty(value = "项目英文名称")
    private String itemEname;

    @ApiModelProperty(value = "项目中文你名称")
    private String itemCname;

    @ApiModelProperty(value = "项目通道号")
    private String itemChannel;

    @ApiModelProperty(value = "结果类型，分为：text文本型、float数值型、textext文本数值型、yang阴阳性、syang特殊阴阳性")
    private String resultType;

    @ApiModelProperty(value = "单位")
    private String unit;
}
