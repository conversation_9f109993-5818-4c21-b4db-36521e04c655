package com.msun.csm.model.req.formlibrary;

import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.dto.BasePageDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@ApiModel(description = "表单资源库查询参数")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class LibFormReq extends BasePageDTO {
    private static final long serialVersionUID = 1L;

    /**
     * 医院id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

    /**
     * 医院id
     */
    @ApiModelProperty(value = "医院id")
    private Long hospitalInfoId;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private Long yyProductId;

    /**
     * 报表名称
     */
    @ApiModelProperty(value = "报表名称")
    private String formName;

    @ApiModelProperty(value = "报表类型")
    private String formType;

    @ApiModelProperty(value = "问卷表单id, 用于表单推荐页面")
    private Long surveyFormId;
}
