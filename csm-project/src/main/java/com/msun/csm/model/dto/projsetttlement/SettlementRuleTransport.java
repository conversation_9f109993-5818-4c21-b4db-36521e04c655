package com.msun.csm.model.dto.projsetttlement;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.msun.csm.common.enums.projsettlement.SettlementRuleCodeEnum;
import com.msun.csm.dao.entity.proj.ProjContractInfo;
import com.msun.csm.dao.entity.proj.ProjCustomCloudService;
import com.msun.csm.dao.entity.proj.ProjProjectInfo;
import com.msun.csm.dao.entity.proj.ProjProjectOrderRelation;
import com.msun.csm.dao.entity.proj.ProjProjectSettlement;
import com.msun.csm.dao.entity.proj.ProjProjectSettlementCheck;
import com.msun.csm.dao.entity.proj.ProjProjectSettlementRule;

import cn.hutool.core.collection.CollUtil;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class SettlementRuleTransport {
    /**
     * 项目类型
     */
    private Integer projectType;
    /**
     * 资源工单id, 云资源或中间件服务或者为空
     */
    private Long resoureYyOrderId;
    /**
     * 用户id
     */
    private Long sysUserId;
    /**
     * 是否派工云资源工单
     */
    private Boolean dispatchCloudForm;
    /**
     * 项目信息
     */
    private ProjProjectInfo projectInfo;
    /**
     * 是否是电销客户
     */
    private Boolean isTelesalesCustomer;
    /**
     * 是否是老流程
     */
    private Boolean isOldSetltlement;
    /**
     * 是否是首期项目
     */
    private Boolean isFirstProject;
    /**
     * 选择资源工单类型. 9:云资源工单, 2:中间件服务工单
     */
    private Integer chooseResourceFormType;
    /**
     * 免中间件标识, true 免, false 不免
     */
    private SettlementRuleTransportMidOrder transportMidOrder;

    /**
     * 是否派工云中间件服务
     */
    private Boolean hasMiddlewareService;
    /**
     * 是否缴纳过首付款
     */
    private Boolean payedSignage;
    /**
     * 销售提交入驻申请参数
     */
    private ProjProjectSettlementCheckSaleSaveDTO settlementCheckSaleSaveDTO;
    /**
     * 项目id
     */
    private Long projectInfoId;
    /**
     * 当前审核节点id
     */
    private Long projectSettlementId;
    /**
     * 是否是外采软件工单
     */
    private Boolean hasPurchaseSoftWareOrder;
    /**
     * 审核节点
     */
    private ProjProjectSettlementCheck settlementCheck;
    /**
     * 规则编码枚举
     */
    private SettlementRuleCodeEnum settlementRuleCodeEnum;
    /**
     * true: 创建规则
     */
    private Boolean createRule;
    /**
     * true: 创建审核节点
     */
    private Boolean createCheck;
    /**
     * 需要新增的审核节点
     */
    private Set<SettlementRuleCheckParam> checkParamsSet;
    /**
     * 需要新增的审核节点
     */
    private Set<ProjProjectSettlementCheck> deleteChecks;
    /**
     * 需要新增的入场条件规则
     */
    private List<ProjProjectSettlementRule> insertSettlementRules;
    /**
     * 需要作废的入场条件规则
     */
    private List<ProjProjectSettlementRule> deleteSettlementRules;

    /**
     * 需要更新的当前节点状态
     */
    private ProjProjectSettlement updateSettlement;
    /**
     * 已经生成的审核节点
     */
    private List<ProjProjectSettlementCheck> createdSettlementChecks;
    /**
     * 已经生成的审核节点对象对照
     */
    private Map<Integer, ProjProjectSettlementCheck> createdSettlementCheckMap;
    /**
     * 已经生成的规则节点
     */
    private List<ProjProjectSettlementRule> createdSettlementRules;
    /**
     * 客户云服务
     */
    private ProjCustomCloudService customCloudService;
    /**
     * 工单客户云关系
     */
    private ProjProjectOrderRelation projectOrderRelation;
    /**
     * 是否生成了审核记录
     */
    private Boolean hasCreateCheck;
    /**
     * 是否生成了规则记录
     */
    private Boolean hasCreateRule;
    /**
     * 合同信息
     */
    private ProjContractInfo contractInfo;
    /**
     * 详细的规则内容
     */
    private SettlementRuleParam settlementRuleParam;
    /**
     * 是否变更云类型, true:变更, false:未变更
     * <p>
     * 如, 若首次申请未私有云, 被驳回后, 又申请了共享云, 此种情况变更了云类型, true,
     * </p>
     */
    private Boolean changeCloudServiceType;

    public SettlementRuleTransport init() {
        this.checkParamsSet = CollUtil.newHashSet();
        this.insertSettlementRules = CollUtil.newArrayList();
        this.transportMidOrder = new SettlementRuleTransportMidOrder();
        return this;
    }

}