package com.msun.csm.model.req.projectreview;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 查询项目审核人员配置表
 * <AUTHOR>
 */
@Data
public class QueryInfoReq {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long projectInfoId;

    /**
     * 审核类型编码
     */
    @ApiModelProperty(value = "审核类型编码(来源于：dict_project_review_type)")
    private String reviewTypeCode;

    @ApiModelProperty(value = "审核方式名称 fwtdsh服务团队审核/ bmjlsh部门经理审核")
    private String reviewMethodCode;

    /**
     * 团队ids
     */
    @ApiModelProperty(value = "团队ids")
    private List<Long> deptIds;

}