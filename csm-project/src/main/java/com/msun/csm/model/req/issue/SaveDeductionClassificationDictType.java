package com.msun.csm.model.req.issue;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaveDeductionClassificationDictType {

    /**
     * 主键，为空时新增，不为空时修改
     */
    private Long id;

    /**
     * 问题分类主键
     */
    private Long issueClassificationId;

    /**
     * 扣分原因
     */
    private String name;

    /**
     * 默认分值
     */
    private String defaultScore;

    /**
     * 对应的后端服务团队类型：bustype-业务服务；datatype-数据服务；interfacetype-接口服务；preSales-售前服务
     */
    private String serverType;

}
