package com.msun.csm.model.dto.yunweiplatform;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 运维云容灾申请载荷
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class YunweiDisasterCloudApplyDTO {

    @ApiModelProperty(value = "运营平台实施客户id")
    private Long customerId;

    @ApiModelProperty(value = "实施客户名称")
    private String customerName;

    @ApiModelProperty(value = "域名")
    private String domainName;

    @ApiModelProperty(value = "提交人")
    private String submitName;

    @ApiModelProperty(value = "交付平台提交id")
    private Long deliverPlatformApplyId;

    @ApiModelProperty(value = "服务期限(月)")
    private Integer serviceTerm;
}
