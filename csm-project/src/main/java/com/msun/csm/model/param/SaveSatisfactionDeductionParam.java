package com.msun.csm.model.param;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaveSatisfactionDeductionParam {

    /**
     * 主键
     */
    private Long id;

    /**
     * 回访标识，true-已回访（复选框选中）；false-未回访
     */
    private Boolean revisitFlag;

    /**
     * 回访人员
     */
    private String revisitPeopleName;

    /**
     * 评价等级编码
     */
    private String levelCode;

    /**
     * 回访记录备注
     */
    private String revisitRemark;

    /**
     * 项目ID
     */
    @NotNull(message = "参数【projectInfoId】不可为null")
    private Long projectInfoId;

    /**
     * 菜单编码
     */
    @NotBlank(message = "参数【menuCode】不可为null")
    private String menuCode;

    /**
     * 是否只需要一次验收：true-只需要一次验收；false-需要两次验收
     */
    @NotNull(message = "参数【onlyOneCheckFlag】不可为null")
    private Boolean onlyOneCheckFlag;

    /**
     * 当前验收次数：1-第一次验收；2-第二次验收
     */
    @NotNull(message = "参数【currentAcceptanceTimes】不可为null")
    private Integer currentAcceptanceTimes;

}
