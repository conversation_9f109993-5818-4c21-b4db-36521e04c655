package com.msun.csm.model.dto;

import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjProjectInfoDTO {

    /**
     * 客户信息ID
     */
    @NotNull
    private Long customInfoId;

    /**
     * 项目状态 1已派工、2调研中、3已入驻、4准备中、5已上线、6已验收
     */
    @ApiModelProperty (value = "项目状态 1已派工、2调研中、3已入驻、4准备中、5已上线、6已验收")
    private Integer projectDeliverStatus;

    /**
     * 交付工单ID
     */
    @ApiModelProperty (value = "交付工单ID")
    private String projectNumber;

    /**
     * 工单产品Id
     */
    @ApiModelProperty (value = "工单产品Id")
    private Long yyOrderProductId;

    @ApiModelProperty("项目id")
    private Long projectInfoId;

    /**
     * 成员id
     */
    @ApiModelProperty
    private Long projectMemberId;

    /**
     * 指定的数据范围
     */
    private List<Long> dataRange;
}
