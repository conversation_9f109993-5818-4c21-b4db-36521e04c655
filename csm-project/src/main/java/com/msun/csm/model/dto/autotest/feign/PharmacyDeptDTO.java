package com.msun.csm.model.dto.autotest.feign;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 药房科室
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PharmacyDeptDTO {

    /**
     * 药房科室id
     */
    @ApiModelProperty(value = "药房科室id")
    private Long id;

    /**
     * 药房科室名称
     */
    @ApiModelProperty(value = "药房科室名称")
    private String name;

}
