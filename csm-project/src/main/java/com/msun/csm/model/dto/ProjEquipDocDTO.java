package com.msun.csm.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* @description:
* @fileName: ProjEquipDocDTO.java
* @author: lius3
* @createAt: 2024/10/16 14:01
* @updateBy: lius3
* @remark: Copyright
*/
@Data
public class ProjEquipDocDTO {

    /**
     * 产品id
     */
    @ApiModelProperty("产品id")
    private Long productId;

    /**
     * 文档类型编码
     */
    @ApiModelProperty("文档类型编码")
    private String docTypeCode;

    /**
     * 检查模态key
     */
    @ApiModelProperty("检查模态key")
    private String modalKey;

    /**
     * 检查模态名称
     */
    @ApiModelProperty("检查模态名称")
    private String modalName;

    /**
     * 设备厂商id
     */
    @ApiModelProperty("设备厂商id")
    private Long equipFactoryId;

    /**
     * 设备厂商名称
     */
    @ApiModelProperty("设备厂商名称")
    private String equipFactoryName;
}
