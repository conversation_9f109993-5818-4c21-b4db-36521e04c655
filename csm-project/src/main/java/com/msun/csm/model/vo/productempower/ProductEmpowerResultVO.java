package com.msun.csm.model.vo.productempower;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.msun.csm.model.vo.dict.DictValue;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 产品授权结果返回值
 */
@Data
public class ProductEmpowerResultVO {

    @ApiModelProperty("客户的选项")
    private List<DictValue> customInfoList;

    @ApiModelProperty("列表页")
    PageInfo<ProjProductEmpowerAddRecordVO> addRecordVOPageInfo;

}
