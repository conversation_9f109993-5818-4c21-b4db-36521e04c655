package com.msun.csm.model.dto.cloud;

import java.io.Serializable;

import com.msun.csm.dao.entity.proj.ProjCustomCloudService;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CloudValidateResultDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 客户云信息
     */
    private ProjCustomCloudService customCloudService;

    /**
     * 项目类型, 单体或区域
     */
    private Integer projectType;

}
