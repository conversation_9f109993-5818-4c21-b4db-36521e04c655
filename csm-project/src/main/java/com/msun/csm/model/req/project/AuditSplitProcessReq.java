package com.msun.csm.model.req.project;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024-01-16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AuditSplitProcessReq {
    /**
     * 项目id
     */
    @NotNull (message = "项目id不能为空")
    private Long projectInfoId;

    /**
     * 项目id
     */
    @NotNull (message = "processId不能为空")
    private Long processId;

    /**
     * 审核结果 0：不通过 1：通过
     */
    @NotNull (message = "审核结果不能为空")
    private int status;


    /**
     * pmo审核意见
     */
    @NotNull (message = "pmo审核意见不能为空")
    private String pmoAudit;

    /**
     * qa审核意见（质管）
     */
    private String qaAudit;
}
