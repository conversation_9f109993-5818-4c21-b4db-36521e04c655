package com.msun.csm.model.req.projreport;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 客户数据统计表(ReportCustomInfo)实体类
 *
 * <AUTHOR>
 * @since 2024-09-27 15:27:17
 */
@ApiModel(description = "报表数据")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ReportCustomInfoSaveOrUpdateReq {
    /**
     * 客户数据统-主键
     */
    private Long reportCustomInfoId;
    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    private String customName;

    /**
     * 绑定项目标识 0.否；1.是
     */
    @ApiModelProperty("绑定项目标识 0.否；1.是")
    @TableField(exist = false)
    private Integer bindProjectFlag;

    /**
     * 客户来源标识 1:运营平台 0 自定义添加
     */
    @ApiModelProperty("客户来源标识 1:运营平台 0 自定义添加")
    private Integer customSourceFlag;

    /**
     * 实际入驻时间
     */
    @ApiModelProperty("实际入驻时间")
    private Date settleInTime;
    /**
     * 实际验收时间
     */
    @ApiModelProperty("实际验收时间")
    private Date acceptTime;
    /**
     * 减免工期天数（工期减免）
     */
    @ApiModelProperty("减免工期天数（工期减免）")
    private String durationReduction;
    /**
     * 客户状态标识; 1:正常运维；2：终止合作
     */
    @ApiModelProperty("客户状态标识; 1:正常运维；2：终止合作")
    private Integer customDeliverStatus;
    /**
     * 是否电销客户 0.否；1.是
     */
    @ApiModelProperty("是否电销客户 0.否；1.是")
    private Integer telesalesFlag;
    /**
     * 实施地客户ID
     */
    @ApiModelProperty("实施地客户ID")
    private Long customInfoId;
    /**
     * 单体/区域标识  1.单体 2：区域 3 电销
     */
    @ApiModelProperty("单体/区域标识  1.单体 2：区域 3 电销")
    private Integer customType;
    /**
     * 客户首期项目ID
     */
    @ApiModelProperty("客户首期项目ID")
    private Long projectInfoId;
    /**
     * 老换新标识 1:新客户;2:老换新;3:老体系;
     */
    @ApiModelProperty("老换新标识 1:新客户;2:老换新;3:老体系;")
    private Integer upgradationType;

    /**
     * 最后一次申请验收时间
     */
    @ApiModelProperty("提交验收申请时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastApplyAcceptTime;

    /**
     * 运营客户ID
     */
    @ApiModelProperty("运营客户ID")
    private Long yyCustomId;

    /**
     * 销售部门中心ID
     */
    @ApiModelProperty("销售部门中心ID")
    private Long saleCenterId;
    /**
     * 客服部门id
     */
    @ApiModelProperty("客服部门id")
    private Long deptId;
    /**
     * 客服分公司id
     */
    @ApiModelProperty("客服分公司id")
    private Long serviceOrgId;
    /**
     * 省份
     */
    @ApiModelProperty("省份")
    private Long provinceId;
    /**
     * 城市
     */
    @ApiModelProperty("城市")
    private Long ctiyId;
    /**
     * 区县
     */
    @ApiModelProperty("区县")
    private Long townId;

    @ApiModelProperty("项目备注信息")
    private String remark;

    /**
     * 上线前减免工期
     */
    @ApiModelProperty("上线前减免工期")
    private Integer onlineBeforeDuration;

    /**
     * 上线后减免工期
     */
    @ApiModelProperty("上线后减免工期")
    private Integer onlineAfterDuration;

    @ApiModelProperty("上线时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date onlineTime;

    @ApiModelProperty("外部验收时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date outAcceptTime;

    @ApiModelProperty("计划上线时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planOnlineTime;

    @ApiModelProperty(" 实施状态  1已派工、2已调研、3已入驻、4、准备完成、5已上线、6已验收、7已启动 、8 申请验收")
    private String projectDeliverStatus;

    @ApiModelProperty(" 实施状态  1已派工、2已调研、3已入驻、4、准备完成、5已上线、6已验收、7已启动 、8 申请验收")
    private Integer projectDeliverStatusNum;

    /**
     * 派工时间
     */
    @ApiModelProperty(value = "派工时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date workTime;
}
