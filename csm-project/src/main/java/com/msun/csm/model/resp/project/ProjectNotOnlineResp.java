package com.msun.csm.model.resp.project;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * @Description:
 * @Author: zd
 * @Date: 2024/5/22
 */
@Data
public class ProjectNotOnlineResp {

    @ApiModelProperty(value = "客户id")
    private Long customInfoId;
    @ApiModelProperty(value = "客户名称")
    private String customName;
    @ApiModelProperty(value = "计划上线时间")
    private String planOnlineTime;
    @ApiModelProperty(value = "项目状态名称")
    private String projectStatusStr;
    @ApiModelProperty(value = "项目状态id")
    private Integer projectStatus;
    @ApiModelProperty(value = "入驻时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date settleInTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "上线时间")
    private Date onlineTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "最后提验收时间")
    private Date lastSubmitTime;

    @ApiModelProperty(value = "工期")
    private Integer workDurationDays;
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

    /**
     * 是否缴纳标识, 1-未通过,0-通过
     */
    @ApiModelProperty(value = "是否缴纳标识, 1-未通过,0-通过")
    private Integer conPreFlag;

    /**
     * 运营平台未通过具体情况描述
     */
    @ApiModelProperty(value = "运营平台未通过具体情况描述")
    private String conPreDesc;
}
