package com.msun.csm.model.resp.projtool;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 配置表(SysConfig)实体类
 *
 * <AUTHOR>
 * @since 2024-05-13 16:37:40
 */
@ApiModel(description = "返回数据")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjToolCustomLimitDataResp {
    /**
     * 业务主键
     */
    @ApiModelProperty(value = "客户id")
    private Long customInfoId;

    @ApiModelProperty(value = "客户名称")
    private String customName;

    @ApiModelProperty(value = "医院id")
    private Long hospitalInfoId;

    @ApiModelProperty(value = "医院名称")
    private String hospitalName;

    @ApiModelProperty(value = "开启限制状态 0 开， 1 关")
    private Integer customSwitchFlag;

    @ApiModelProperty(value = "执行状态 0 未执行， 1 已执行")
    private Integer hasDeal;

    @ApiModelProperty(value = "限制启用时间")
    private Date customStartTime;

    @ApiModelProperty(value = "执行时间")
    private Date executeTime;

    @ApiModelProperty(value = "验收时间")
    private Date acceptTime;

    //    三方接口	统计报表	打印报表	数据查询	云护理表单	手麻表单	重症表单	急诊表单
    @ApiModelProperty(value = "三方接口 0 开/ 1 关")
    private Integer interfacelimitFlag;
    @ApiModelProperty(value = "统计报表 0 开/ 1 关")
    private Integer reportlimitFlag;
    @ApiModelProperty(value = "打印报表 0 开/ 1 关")
    private Integer baseprintFlag;
    @ApiModelProperty(value = "数据查询 0 开/ 1 关")
    private Integer reportwebFlag;
    @ApiModelProperty(value = "云护理表单 0 开/ 1 关")
    private Integer hulidanyuanFlag;
    @ApiModelProperty(value = "手麻表单 0 开/ 1 关")
    private Integer aimsFormFlag;
    @ApiModelProperty(value = "重症表单 0 开/ 1 关")
    private Integer icuFormFlag;
    @ApiModelProperty(value = "急诊表单 0 开/ 1 关")
    private Integer emisFormFlag;
}
