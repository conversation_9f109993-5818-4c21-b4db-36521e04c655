package com.msun.csm.model.req.projectreview;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.dto.BasePageDTO;

/**
 * 项目审核类型字典表
 *
 * @TableName dict_project_review_type
 */
@TableName(value = "dict_project_review_type")
@Data
public class DictProjectReviewTypeParam extends BasePageDTO {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long projectReviewTypeId;

    /**
     * 类型编码
     */
    @ApiModelProperty(value = "类型编码")
    private String dictCode;

    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    private String dictName;

    /**
     * 审核业务描述
     */
    @ApiModelProperty(value = "审核业务描述")
    private String remark;
}