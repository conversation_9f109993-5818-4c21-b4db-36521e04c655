package com.msun.csm.model.req.projectreview;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import com.msun.csm.common.model.dto.BasePageDTO;

/**
 * 项目审核模式配置表
 *
 * <AUTHOR>
 * @TableName config_project_review
 */
@Data
public class ConfigProjectReviewReq extends BasePageDTO {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long projectReviewId;

    /**
     * 审核类型id 取字典表 dict_project_review_type
     */
    @ApiModelProperty(value = "审核类型id 取字典表 dict_project_review_type")
    private Long reviewTypeId;

    /**
     * 客户类型 -1 通用 1单体 2区域
     */
    @ApiModelProperty(value = "客户类型 -1 通用 1单体 2区域")
    private Integer customType;

    /**
     * 电销属性 -1 通用 1 电销 0非电销
     */
    @ApiModelProperty(value = "电销属性 -1 通用 1 电销 0非电销")
    private Integer telesalesFlag;

    /**
     * 交付模式 -1 通用 1 前后端模式  0非前后端模式
     */
    @ApiModelProperty(value = "交付模式 -1 通用 1 前后端模式  0非前后端模式")
    private Integer deliveryModel;

    /**
     * 项目类型 -1 通用 1 首期 2 非首期
     */
    @ApiModelProperty(value = "项目类型 -1 通用 1 首期 2 非首期")
    private Integer projectType;

    /**
     * 审核方式id 取字典表dict_review_method_type
     */
    @ApiModelProperty(value = "审核方式id 取字典表dict_review_method_type")
    private Long reviewMethodId;

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    private String reviewTime;

    /**
     * 预警时间
     */
    @ApiModelProperty(value = "预警时间")
    private String warningTime;

    /**
     * 是否产生罚单 0.否 1.是
     */
    @ApiModelProperty(value = "是否产生罚单 0.否 1.是")
    private Integer isFineFlag;

    /**
     * 罚款金额
     */
    @ApiModelProperty(value = "罚款金额")
    private Integer fineMoney;

    @ApiModelProperty("逻辑删除【0：否；1：是】")
    private Integer isDeleted;

}