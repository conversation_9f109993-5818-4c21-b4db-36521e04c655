package com.msun.csm.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ExamImportStepDTO {

    /**
     * 项目ID
     */
    @ApiModelProperty("项目ID")
    private Long projectInfoId;

    /**
     * 客户ID
     */
    @ApiModelProperty("客户ID")
    private Long customInfoId;

    /**
     * 培训环境医院ID
     */
    @ApiModelProperty("培训环境医院ID")
    private Long examHospitalInfoId;

    /**
     * 是否彻底清除
     */
    @ApiModelProperty("是否彻底清除")
    private Boolean destroy = false;

    /**
     * 步骤业务编码
     */
    @ApiModelProperty("步骤业务编码")
    private String stepCode;
}
