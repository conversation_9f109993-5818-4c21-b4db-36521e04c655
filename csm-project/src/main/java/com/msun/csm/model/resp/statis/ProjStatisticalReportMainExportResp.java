package com.msun.csm.model.resp.statis;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@ApiModel(description = "分页")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjStatisticalReportMainExportResp {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @ExcelIgnore
    private Long statisticalReportMainId;


    @ApiModelProperty("客户名称")
    @ExcelProperty(value = "客户名称", index = 0)
    @ColumnWidth(20)
    private String customName;

    /**
     * 项目id
     */
    @ExcelIgnore
    private Long projectInfoId;

    @ApiModelProperty(value = "项目名称")
    @ExcelProperty(value = "项目名称", index = 1)
    @ColumnWidth(20)
    private String projectName;

    /**
     * 报表名称： 字符串
     */
    @ApiModelProperty(value = "报表名称： 字符串")
    @ExcelProperty(value = "报表名称", index = 2)
    @ColumnWidth(20)
    private String reportName;

    @ApiModelProperty(value = "统计口径(全)")
    @ExcelProperty(value = "统计口径", index = 3)
    @ColumnWidth(20)
    private String statisticalCalibrationNames;

    @ApiModelProperty(value = "统计口径(截取)")
    @ExcelIgnore
    private String calibrationIdFirstFour;

    /**
     * "报表样式，存储到sys_file的主键，多个以','分割。
     * 通过file_path 获取到路径对应的图片"
     */
    @ExcelIgnore
    private String reportStyle;

    /**
     * 制作方式： 下拉（当前4种）
     */
    @ExcelIgnore
    private Long productionMethodId;
    @ExcelProperty(value = "制作方式", index = 4)
    @ColumnWidth(20)
    private String productionMethodName;

    /**
     * "状态（枚举）：
     * 1待申请裁定、
     * 11裁定中、12裁定驳回、13裁定通过
     * 21制作中、
     * 31已下沉"
     */
    @ExcelIgnore
    @ApiModelProperty(value = "状态（枚举）/1待申请裁定/11裁定中/12裁定驳回/13裁定通过/21制作中/31已下沉")
    private Integer reportStatus;

    @ExcelProperty(value = "状态", index = 5)
    @ApiModelProperty(value = "状态（枚举）/1待申请裁定/11裁定中/12裁定驳回/13裁定通过/21制作中/31已下沉")
    @ColumnWidth(20)
    private String reportStatusStr;

    /**
     * 使用科室 id： 关联 dict_hospital_dept 主键
     */
    @ExcelIgnore
    @ApiModelProperty(value = "使用科室 id： 关联 dict_hospital_dept 主键")
    private Long hospitalDeptId;

    /**
     * 使用科室名称 ：
     */
    @ExcelProperty(value = "使用科室名称 ：", index = 6)
    @ApiModelProperty(value = "使用科室名称")
    @ColumnWidth(20)
    private String hospitalDeptName;

    /**
     * 用途分类id 关联dict_report_purpose 主键
     */
    @ExcelIgnore
    @ApiModelProperty(value = "用途分类id 关联dict_report_purpose 主键")
    private Long reportPurposeId;

    /**
     * 用途分类名称
     */
    @ExcelProperty(value = "用途分类名称", index = 7)
    @ApiModelProperty(value = "用途分类名称")
    @ColumnWidth(20)
    private String reportPurposeName;

    /**
     * 使用频次id dict_report_frequency 主键
     */
    @ExcelIgnore
    @ApiModelProperty(value = "使用频次id dict_report_frequency 主键")
    private Long reportFrequencyId;

    /**
     * 使用频次名称
     */
    @ExcelProperty(value = "使用频次名称", index = 8)
    @ApiModelProperty(value = "使用频次名称")
    @ColumnWidth(20)
    private String reportFrequencyName;

    /**
     * 上线必备（1是 0否） 默认1
     */
    @ExcelIgnore
    @ApiModelProperty(value = "上线必备（1是 0否） 默认1")
    private Integer onlineFlag;

    /**
     * 上线必备（1是 0否） 默认1
     */
    @ExcelProperty(value = "上线必备（1是 0否） 默认1", index = 9)
    @ApiModelProperty(value = "上线必备（1是 0否） 默认1")
    @ColumnWidth(20)
    private String onlineFlagStr;

    @ExcelIgnore
    @ApiModelProperty(value = "上线必备false/true")
    private Boolean onlineFlagBoolean;

    /**
     * 统计报表备注
     */
    @ExcelIgnore
    @ApiModelProperty(value = "统计报表备注")
    private String remarks;

    /**
     * 调研人
     */
    @ExcelIgnore
    private Long surveyUserId;
    @ApiModelProperty(value = "调研人名称")
    @ExcelProperty(value = "调研人名称", index = 10)
    @ColumnWidth(20)
    private String surveyUserName;

    /**
     * 调研时间
     */
    @ApiModelProperty(value = "调研时间")
    @ExcelProperty(value = "调研时间", index = 11)
    @ColumnWidth(20)
    private Date surveyTime;

    /**
     * 裁定审核人
     */
    @ExcelIgnore
    @ApiModelProperty(value = "裁定审核人")
    private Long auditUserId;
    @ExcelProperty(value = "裁定审核人名称", index = 12)
    @ApiModelProperty(value = "裁定审核人名称")
    @ColumnWidth(20)
    private String auditUserName;

    /**
     * 裁定审核时间
     */
    @ExcelProperty(value = "裁定审核时间", index = 13)
    @ApiModelProperty(value = "裁定审核时间")
    @ColumnWidth(20)
    private Date auditTime;

    /**
     * 分配负责人
     */
    @ExcelIgnore
    @ApiModelProperty(value = "分配负责人")
    private Long allocateUserId;

    @ApiModelProperty(value = "分配负责人名称")
    @ExcelProperty(value = "分配负责人名称", index = 14)
    @ColumnWidth(20)
    private String allocateUserName;

    /**
     * 分配时间
     */
    @ApiModelProperty(value = "分配时间")
    @ExcelProperty(value = "分配时间", index = 15)
    @ColumnWidth(20)
    private Date allocateTime;

    /**
     * 计划完成时间
     */
    @ExcelProperty(value = "计划完成时间", index = 16)
    @ApiModelProperty(value = "计划完成时间")
    @ColumnWidth(20)
    private String planFinishTime;

    @ApiModelProperty(value = "下沉人（完成人）名称")
    @ExcelIgnore
    private String finishUserName;

    /**
     * 下沉时间（完成时间）
     */
    @ApiModelProperty(value = "下沉时间（完成时间）")
    @ExcelIgnore
    private Date finishTime;

    @ApiModelProperty(value = "统计报表主表名称")
    @ExcelIgnore
    private String reportMainName;

    @ApiModelProperty(value = "医院名称")
    @ExcelIgnore
    private String hospitalName;

    @ApiModelProperty(value = "裁定指标")
    @ExcelProperty(value = "裁定指标", index = 17)
    @ColumnWidth(20)
    private String reportTargets;

    /**
     * 裁定备注，驳回必传
     */
    @ApiModelProperty(value = "裁定备注，驳回必传")
    @ExcelProperty(value = "裁定备注", index = 18)
    @ColumnWidth(20)
    private String operContent;

    @ApiModelProperty(value = "挂载路径")
    @ExcelIgnore
    private String mountPath;

    @ApiModelProperty(value = "裁定产品名称")
    @ExcelProperty(value = "裁定产品名称", index = 19)
    @ColumnWidth(20)
    private String operationProductName;

    /**
     * 驳回次数
     */
    @ExcelProperty(value = "驳回次数")
    @ColumnWidth(20)
    private Integer rejectCount;

    /**
     * 驳回原因
     */
    @ExcelProperty(value = "驳回原因")
    @ColumnWidth(20)
    private String rejectReason;
}
