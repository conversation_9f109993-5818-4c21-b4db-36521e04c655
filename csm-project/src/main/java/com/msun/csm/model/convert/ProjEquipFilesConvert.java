package com.msun.csm.model.convert;

import org.mapstruct.Mapper;

import com.msun.csm.common.model.convert.Dto2PoBaseConvert;
import com.msun.csm.common.model.convert.Dto2VoBaseConvert;
import com.msun.csm.common.model.convert.Vo2PoBaseConvert;
import com.msun.csm.dao.entity.proj.ProjEquipFiles;
import com.msun.csm.model.dto.ProjEquipFilesDTO;
import com.msun.csm.model.vo.ProjEquipFilesVO;

/**
* @description:
* @fileName: ProjEquipFilesConvert.java
* @author: lius3
* @createAt: 2024/10/12 10:52
* @updateBy: lius3
* @remark: Copyright
*/
@Mapper(componentModel = "spring")
public interface ProjEquipFilesConvert extends Dto2PoBaseConvert<ProjEquipFilesDTO, ProjEquipFiles>,
        Dto2VoBaseConvert<ProjEquipFilesDTO, ProjEquipFilesVO>, Vo2PoBaseConvert<ProjEquipFilesVO, ProjEquipFiles> {
}
