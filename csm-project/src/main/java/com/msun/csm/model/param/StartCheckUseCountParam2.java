package com.msun.csm.model.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StartCheckUseCountParam2 {

    /**
     * 项目ID
     */
    @NotNull(message = "参数【projectInfoId】不可为null")
    private Long projectInfoId;

    /**
     * 产品ID
     */
    @NotNull(message = "参数【yyProductId】不可为null")
    private Long yyProductId;

    /**
     * 产品功能点编码
     */
    @NotNull(message = "参数【functionCode】不可为null")
    private String functionCode;

    /**
     * 实际扣分
     */
    @NotNull(message = "参数【practicalDeduction】不可为null")
    private BigDecimal practicalDeduction;

    /**
     * 扣分备注
     */
    private String remark;

    /**
     * 菜单编码
     */
    @NotNull(message = "参数【menuCode】不可为null")
    private String menuCode;

    /**
     * 是否只需要一次验收：true-只需要一次验收；false-需要两次验收
     */
    @NotNull(message = "参数【onlyOneCheckFlag】不可为null")
    private Boolean onlyOneCheckFlag;

    /**
     * 当前验收次数：1-第一次验收；2-第二次验收
     */
    @NotNull(message = "参数【currentAcceptanceTimes】不可为null")
    private Integer currentAcceptanceTimes;

    /**
     * 扣分分类
     */
    @NotNull(message = "参数【deductionType】不可为null")
    private String deductionType;

}
