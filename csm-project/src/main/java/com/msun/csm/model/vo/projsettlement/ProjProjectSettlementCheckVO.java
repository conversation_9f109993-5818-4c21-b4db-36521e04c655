package com.msun.csm.model.vo.projsettlement;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-06-18 08:29:00
 */

@Data
public class ProjProjectSettlementCheckVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 入驻审核表id，主键
     */
    @ApiModelProperty(value = "入驻审核表id，主键")
    private Long projectSettlementCheckId;

    /**
     * 入驻信息表id
     */
    @ApiModelProperty(value = "入驻信息表id")
    private Long projectSettlementId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

    /**
     * 审核节点
     */
    @ApiModelProperty(value = "审核节点")
    private Integer checkNode;

    /**
     * 审核节点名称
     */
    @ApiModelProperty(value = "审核节点名称")
    private String checkNodeName;

    /**
     * 审核人id
     */
    @ApiModelProperty(value = "审核人id")
    private Long checkUserId;

    /**
     * 审核人姓名, 多个人用逗号隔开
     */
    @ApiModelProperty(value = "审核人姓名, 多个人用逗号隔开")
    private String checkUserName;

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkTime;

    /**
     * 审核结果：0.不通过；1.通过
     */
    @ApiModelProperty(value = "审核结果：0.不通过；1.通过")
    private Integer checkResult;

    /**
     * 审核意见
     */
    @ApiModelProperty(value = "审核意见")
    private String checkContent;


}
