package com.msun.csm.model.dto.oas.form.pattern;

/**
 * <AUTHOR>
 * @date 2025/2/18 17:59
 */
public enum FormPatternTypeEnum {

    /**
     * 表单样式类型 1打印样式 2电脑样式 3平板样式
     */
    FORM_PATTERN_TYPE_1(1, "打印样式"),
    FORM_PATTERN_TYPE_2(2, "电脑样式"),
    FORM_PATTERN_TYPE_3(3, "平板样式");

    /**
     * 表单样式类型 1打印样式 2电脑样式 3平板样式
     */
    private final Integer value;
    /**
     * 表单样式类型描述 1打印样式 2电脑样式 3平板样式
     */
    private final String desc;

    FormPatternTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
