package com.msun.csm.model.dto.projfileupload;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class FindProjectFileDTO {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @NotNull
    private Long projectInfoId;

    /**
     * 阶段编码
     */
    @ApiModelProperty(value = "阶段编码")
    private String planStageCode;

    /**
     * 资料描述
     */
    @ApiModelProperty(value = "资料描述")
    private String fileDesc;
}
