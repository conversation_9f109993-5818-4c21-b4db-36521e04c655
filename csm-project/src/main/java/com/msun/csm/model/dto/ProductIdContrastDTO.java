package com.msun.csm.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 产品id对比DTO
 * @Author: MengChuAn
 * @Date: 2024/4/28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductIdContrastDTO {

    // 原产品id
    private Long originalProductId;

    // 对比产品id
    private Long contrastProductId;

    //授权菜单
    private String msunHealthModuleCode;

    //授权菜单
    private String msunHealthModule;

    public ProductIdContrastDTO(Long sameProductId) {
        this.originalProductId = sameProductId;
        this.contrastProductId = sameProductId;
    }
}
