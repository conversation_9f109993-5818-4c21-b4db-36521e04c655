package com.msun.csm.model.req.projreport;

import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.dto.BasePageDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@ApiModel(description = "报表明细数据")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjSurveyReportDetailReq extends BasePageDTO {
    private static final long serialVersionUID = 1L;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

    /**
     * 医院id
     */
    @ApiModelProperty(value = "医院id")
    private Long hospitalInfoId;

    /**
     * 打印报表节点名称
     */
    @ApiModelProperty(value = "打印报表节点名称")
    private String reportCode;

    /**
     * 完成状态：
     */
    @ApiModelProperty(value = "完成状态：")
    private Integer finishStatus;

    /**
     * 当前电脑配置的单据状态：10未开始11协助中12已协助20
     * 已通过21已跳过22已完成
     */
    @ApiModelProperty(value = "阶段名称")
    private String status;
}
