package com.msun.csm.model.dto.projreview;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 调研内容请求
 */
@Data
public class ProjReviewLogDTO {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @NotBlank(message = "项目id不能为空")
    private Long projectInfoId;

    /**
     * 场景编码
     */
    @ApiModelProperty(value = "场景编码")
    @NotBlank(message = "场景编码不能为空")
    private String sceneCode;

    /**
     * 项目阶段编码
     */
    @ApiModelProperty(value = "项目阶段编码")
    @NotBlank(message = "项目阶段编码不能为空")
    private String projectStageCode;

}
