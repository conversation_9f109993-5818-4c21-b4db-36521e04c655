package com.msun.csm.model.req.projform;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.dto.BasePageDTO;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@ApiModel(description = "表单数据")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjSurveyFormReq extends BasePageDTO {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long surveyFormId;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户信息ID")
    private Long customInfoId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

    /**
     * 医院id
     */
    @ApiModelProperty(value = "医院id")
    private Long hospitalInfoId;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private Long yyProductId;

    /**
     * 模块id
     */
    @ApiModelProperty(value = "模块id")
    private Long yyModuleId;

    /**
     * 报表名称
     */
    @ApiModelProperty(value = "表单名称")
    private String formName;

    /**
     * 上线必备 0： 否 1: 是
     */
    @ApiModelProperty(value = "上线必备 0： 否 1: 是")
    private Integer onlineEssential;

    /**
     * <p>0：未开始</p>
     * <p>4：提交调研审核</p>
     * <p>6：调研审核驳回</p>
     * <p>5：调研审核通过</p>
     * <p>1：制作完成</p>
     * <p>2：制作完成已驳回</p>
     * <p>8：制作完成验证通过</p>
     */
    @ApiModelProperty(value = "完成状态：")
    private Integer finishStatus;

    /**
     * 完成状态：
     */
    @ApiModelProperty(value = "完成状态：")
    private List<Integer> finishStatusList;

    @ApiModelProperty(value = "阶段名称")
    private String projectStageCode;

    @ApiModelProperty(value = "用于查询的责任人,多选查询")
    private List<Long> allocateUserId;

    @ApiModelProperty(value = "backend_form_design/其他")
    private String pageSource;

    @ApiModelProperty(value = "用于查询的审核人,多选查询")
    private List<Long> reviewerUserIds;

    /**
     * 查询验证人
     */
    private List<Long> identifierUserIds;

    @ApiModelProperty(value = "运维审核状态 0 待审核 1 通过 2 驳回")
    private Integer operationExamineStatusNumber;

    /**
     * 项目状态 1已派工、2已调研、3已入驻、4、准备完成、5已上线、6已验收、7已启动 、8 申请验收
     */
    private List<Integer> projectDeliverStatusList;
}
