package com.msun.csm.model.resp.issue;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/12/3
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IssueDataResp2 {

    /**
     * 优先级 -原返回值-todoPriorityName
     */
    @ApiModelProperty(value = "优先级展示")
    private String priorityName;

    /**
     * 问题状态-原返回值-todoStatusName
     */
    @ApiModelProperty(value = "问题状态展示")
    private String statusName;

    /**
     * 问题负责人 -原返回值-todoChargePersonName
     */
    @ApiModelProperty(value = "问题负责人-用户名")
    private String chargePersonName;

    /**
     * 问题所属运营平台产品id-如果不满足，-1 其他 -原返回值-todoProductName
     */
    @ApiModelProperty(value = "问题所属运营平台产品名称-如果不满足，-1 其他")
    private String productName;

    /**
     * 问题记录人
     */
    private String createrName;

    /**
     * 更新人-用户名
     */
    private String updaterName;

    /**
     * 分组查询自数据结构
     */
    private List<IssueDataResp2> children;

    /**
     * 问题提出人
     */
    private String submitterName;

    /**
     * 项目计划标题
     */
    private String planTitle;

    /**
     * 验收分类编码
     */
    private String acceptanceClassificationCode;

    /**
     * 验收分类名称
     */
    private String acceptanceClassificationName;

    /**
     * 扣分分类编码
     */
    private String deductionTypeCode;

    /**
     * 扣分分类名称
     */
    private String deductionTypeName;

    /**
     * 扣分分值
     */
    private BigDecimal deductionScore;

    /**
     * 操作来源：supervisionCenter-监管中心验收页面
     */
    private String operationSource;

    /**
     * 问题分类主键
     */
    private String issueClassificationId;


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectInfoId;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private Long priority;

    /**
     * 问题分类
     */
    @ApiModelProperty(value = "问题分类")
    private String classification;

    /**
     * 问题描述-富文本
     */
    @ApiModelProperty(value = "问题描述-富文本")
    private String description;

    /**
     * 问题状态
     */
    @ApiModelProperty(value = "问题状态")
    private Long status;

    /**
     * 问题处理结果-富文本
     */
    @ApiModelProperty(value = "问题处理结果-富文本")
    private String result;

    /**
     * 问题负责人，id
     */
    @ApiModelProperty(value = "问题负责人，id")
    private Long chargePerson;

    /**
     * 问题所属科室，手填
     */
    @ApiModelProperty(value = "问题所属科室，手填")
    private String dept;

    /**
     * 问题所属运营平台产品id-如果不满足，-1 其他
     */
    @ApiModelProperty(value = "问题所属运营平台产品id-如果不满足，-1 其他")
    private Integer productId;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private Long createrId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新人id
     */
    @ApiModelProperty(value = "更新人id")
    private Long updaterId;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 逻辑删除【0：否；1：是】
     */
    @ApiModelProperty(value = "逻辑删除【0：否；1：是】")
    private Integer isDeleted;

    /**
     * 问题提出人
     */
    @ApiModelProperty(value = "问题提出人")
    private Long submitterId;

    /**
     * 项目计划表主键
     */
    @ApiModelProperty(value = "项目计划表主键")
    private Long projectPlanId;

    /**
     * 我的待办主键
     */
    @ApiModelProperty(value = "我的待办主键")
    private Long todoTaskId;

    /**
     * 首次验收(first)/最终验收(final)
     */
    private String source;

    /**
     * 阶段名称
     */
    private String stageName;


}
