package com.msun.csm.model.param;

import lombok.Data;

@Data
public class CreateTduckFormParam {

    /**
     * 项目ID
     */
    private Long projectInfoId;

    /**
     * 运营平台产品ID
     */
    private String productId;

    /**
     * 医院信息ID
     */
    private Long hospitalInfoId;

    /**
     * 科室名称
     */
    private String deptName;

    /**
     * 责任人的userId
     */
    private Long sysUserId;

    /**
     * 调研来源，只要不是来自项目经理确认阶段（config），统一认为来自调研阶段（survey）
     */
    private String source;

    /**
     * 操作阶段：survey-调研阶段；ready-准备阶段；audit-后端审核
     */
    private String operationSource;

    /**
     * 只有一份调研计划时为true
     */
    private Boolean onlyOneSurvey;
}
