package com.msun.csm.model.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 智能体下拉选择响应对象
 * <AUTHOR> @date 2025/07/09
 */
@Data
@ApiModel(description = "智能体下拉选择响应对象")
public class DictAgentChatDropdownResp {

    @ApiModelProperty(value = "智能体ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "智能体名称", example = "图片检测智能体")
    private String name;
}
