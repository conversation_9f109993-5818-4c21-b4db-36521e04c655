package com.msun.csm.model.param;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuerySimulationStatisticalDataParam {

    /**
     * 客户ID
     */
    @NotNull(message = "【customInfoId】不可为空")
    private Long customInfoId;

    /**
     * 项目ID
     */
    @NotNull(message = "【projectInfoId】不可为空")
    private Long projectInfoId;

    /**
     * 云健康医院ID
     */
    private String cloudHospitalId;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 环境：prod-生产环境；test-测试环境
     */
    private String environment;

    /**
     * 查询开始时间
     */
    private String startTime;

    /**
     * 查询结束时间
     */
    private String endTime;

    /**
     * 业务编码，区分不同业务场景：simulation模拟练习、study学习时长统计、exam考试统计
     */
    private String businessCode;

}
