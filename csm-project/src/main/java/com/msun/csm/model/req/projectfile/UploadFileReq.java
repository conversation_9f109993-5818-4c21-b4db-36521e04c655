package com.msun.csm.model.req.projectfile;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

import org.springframework.web.multipart.MultipartFile;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/5/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UploadFileReq {
    //项目id
    @NotNull
    private Long projectInfoId;

    //文件molistoneCode-前端固定，按照阶段写死
    @NotNull
    private String milestoneCode;

    @NotNull
    private MultipartFile file;

    //业务类型-非里程碑阶段数据
    private String businessCode;

    //是否公开
    @NotNull
    private Boolean isPublic = false;

    @ApiModelProperty(value = "校验类型：dytpjc 打印报表校验； tjbbtpjc 统计报表校验； bdtpjc 表单校验")
    private String checkType;
}
