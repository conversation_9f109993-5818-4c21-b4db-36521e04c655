package com.msun.csm.model.req.projectfile;

import javax.validation.constraints.NotNull;

import org.springframework.web.multipart.MultipartFile;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/5/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UploadFileReq {
    //项目id
    @NotNull
    private Long projectInfoId;

    //文件molistoneCode-前端固定，按照阶段写死
    @NotNull
    private String milestoneCode;

    @NotNull
    private MultipartFile file;

    //业务类型-非里程碑阶段数据
    private String businessCode;

    //是否公开
    @NotNull
    private Boolean isPublic = false;

}
