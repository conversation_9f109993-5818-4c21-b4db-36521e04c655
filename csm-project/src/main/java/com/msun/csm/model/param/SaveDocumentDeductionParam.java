package com.msun.csm.model.param;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaveDocumentDeductionParam {

    /**
     * 主键
     */
    private Long id;

    /**
     * 项目ID
     */
    @NotNull(message = "参数【projectInfoId】不可为null")
    private Long projectInfoId;

    /**
     * 菜单编码
     */
    @NotBlank(message = "参数【menuCode】不可为null")
    private String menuCode;

    /**
     * 实际扣分
     */
    @NotNull(message = "参数【practicalDeduction】不可为null")
    private Integer practicalDeduction;

    /**
     * 备注
     */
    private String remark;

    /**
     * 项目阶段编码
     */
    @NotBlank(message = "参数【projectStageCode】不可为null")
    private String projectStageCode;

    /**
     * 项目里程碑节点编码
     */
    @NotBlank(message = "参数【remark】不可为null")
    private String milestoneNodeCode;

    /**
     * 项目文件表主键
     */
    private Long projectFileId;

    /**
     * 是否只需要一次验收：true-只需要一次验收；false-需要两次验收
     */
    @NotNull(message = "参数【onlyOneCheckFlag】不可为null")
    private Boolean onlyOneCheckFlag;

    /**
     * 当前验收次数：1-第一次验收；2-第二次验收
     */
    @NotNull(message = "参数【currentAcceptanceTimes】不可为null")
    private Integer currentAcceptanceTimes;

}
