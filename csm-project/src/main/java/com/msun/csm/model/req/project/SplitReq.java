package com.msun.csm.model.req.project;

import java.util.List;

import com.msun.csm.model.resp.project.ProductBase;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024-01-09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SplitReq {

    /**
     * 业务主键id
     */
    private Long id;

    /**
     * 展示名称
     */
    private String name;

    /**
     * 项目类型 单体区域   1:单体  2:区域
     */
    private String isArea;


    /**
     * 项目类型 1:老换新  2:新客户
     */
    private String customerType;

    /**
     * 客户Id
     */
    private Long customerId;

    /**
     * 项目经理Id
     */
    private Long managerId;


    /**
     * 实施团队Id
     */
    private Integer deptId;


    private List<ProductBase> productList;
}
