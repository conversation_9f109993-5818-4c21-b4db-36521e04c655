package com.msun.csm.model.resp.applyorder;

import java.util.List;

import lombok.Data;

/**
 * 部署单返回值产品实体
 */
@Data
public class DepResourceApplyCountryResult {

    /**
     * 行政区划格式编码：省/市/县
     */
    private String administrativeCode;
    /**
     * 行政区划格式：省/市/县
     */
    private String administrativeDivisions;
    /**
     * 年收入(亿元)
     */
    private Double annualIncome;
    /**
     * 床位数
     */
    private Integer bedCount;
    /**
     * 乡镇卫生院数量
     */
    private Integer clinicCount;
    /**
     * 卫生室数量
     */
    private Integer clinicRoomCount;
    /**
     * 客服经理
     */
    private String customerManager;
    /**
     * 客服人员
     */
    private String customerPersonnel;
    /**
     * 日门诊量
     */
    private Integer dayClinicCount;

    private List<DepResourceApplyHospitalResult> deployResourceApplyHospitalVOList;

}
