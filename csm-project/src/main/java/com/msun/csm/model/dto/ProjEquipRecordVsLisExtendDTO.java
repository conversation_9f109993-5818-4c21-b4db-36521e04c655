package com.msun.csm.model.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 新增、编辑内容
 */
@Data
public class ProjEquipRecordVsLisExtendDTO extends ProjEquipRecordDTO {

    /**
     * Lis设备主键id
     */
    @ApiModelProperty(value = "PACS设备主键id")
    private Long equipRecordVsLisId;

    /**
     * 是否双工【0：否；1：是；2：样本双工；3：条码双工】
     */
    @ApiModelProperty(value = "是否双工【0：否；1：是；2：样本双工；3：条码双工】")
    private Integer duplexFlag;

    /**
     * 是否急诊【0：否；1：是】
     */
    @ApiModelProperty(value = "是否急诊【0：否；1：是】")
    private Integer emergencyFlag;

    /**
     * 是否质控【0：否；1：是】
     */
    @ApiModelProperty(value = "是否质控【0：否；1：是】")
    private Integer qualityControlFlag;

    /**
     * 是否复检【0：否；1：是】
     */
    @ApiModelProperty(value = "是否复检【0：否；1：是】")
    private Integer isRecheckFlag;

    /**
     * 样本号/条码号
     */
    @ApiModelProperty(value = "样本号/条码号")
    private String barCode;

    /**
     * 检测结果02
     */
    @ApiModelProperty(value = "检测结果02")
    private String itemResult02;

    /**
     * 检测结果01
     */
    @ApiModelProperty(value = "检测结果01")
    private String itemResult01;

    /**
     * 通道号01
     */
    @ApiModelProperty(value = "通道号01")
    private String itemChannel01;

    /**
     * 通道号02
     */
    @ApiModelProperty(value = "通道号02")
    private String itemChannel02;

    /**
     * 检验项01
     */
    @ApiModelProperty(value = "检验项01")
    private String itemName01;

    /**
     * 检验项02
     */
    @ApiModelProperty(value = "检验项02")
    private String itemName02;

    /**
     * 查询脚本
     */
    @ApiModelProperty(value = "查询脚本")
    private String querySql;

    /**
     * 普通结果串
     */
    @ApiModelProperty(value = "普通结果串")
    private List<LisEquipFileExtendDTO> normalResultString;

    /**
     * 数据库文件
     */
    @ApiModelProperty(value = "数据库文件")
    private List<LisEquipFileExtendDTO> dbFile;

    /**
     * 设备全貌照片
     */
    @ApiModelProperty(value = "设备全貌照片")
    private List<LisEquipFileExtendDTO> deviceFullImg;

    /**
     * 仪器配置照片
     */
    @ApiModelProperty(value = "仪器配置照片")
    private List<LisEquipFileExtendDTO> deviceConfigImg;

    /**
     * 双工通讯数据
     */
    @ApiModelProperty(value = "双工通讯数据")
    private List<LisEquipFileExtendDTO> duplexCommunicationLog;

    /**
     * 通讯协议文档
     */
    @ApiModelProperty(value = "通讯协议文档")
    private List<LisEquipFileExtendDTO> communicationProtocolDoc;

    /**
     * 质控结果串
     */
    @ApiModelProperty(value = "质控原始结果串")
    private List<LisEquipFileExtendDTO> qualityControlResultString;

    /**
     * 急诊结果串
     */
    @ApiModelProperty(value = "急诊结果串")
    private List<LisEquipFileExtendDTO> emergencyResultString;


    /**
     * 样本结果图片
     */
    @ApiModelProperty(value = "样本结果图片")
    private List<LisEquipFileExtendDTO> sampleResult;

    /**
     * 通讯方式
     */
    @ApiModelProperty("通讯方式")
    private BaseCodeNameDTO commModeObjExtend;

    /**
     * 质控样本号
     */
    @ApiModelProperty("质控样本号")
    private String quaBarCode;

    /**
     * 质控样本结果
     */
    @ApiModelProperty("质控样本结果")
    private String quaResult;

    /**
     * 急诊样本号
     */
    @ApiModelProperty("急诊样本号")
    private String emerBarCode;

    /**
     * 报告是否带图【0：否；1：是】
     */
    @ApiModelProperty("报告是否带图【0：否；1：是】")
    private Integer reportWithImgFlag;

    /**
     * 数据库文件路径
     */
    @ApiModelProperty("数据库文件路径")
    private String dbFilePath;

    /**
     * 数据库密码
     */
    @ApiModelProperty("数据库密码")
    private String dbPassword;

}
