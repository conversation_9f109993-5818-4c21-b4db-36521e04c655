package com.msun.csm.model.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SendConfirmationParam {

    /**
     * 后端服务团队扣分记录主键集合
     */
    @NotNull(message = "参数【backendTeamDeductionRecordIdList】不可为null")
    private List<Long> backendTeamDeductionRecordIdList;

    /**
     * 质管发送验收确认单时填写的备注
     */
    private String qualityRemark;

}
