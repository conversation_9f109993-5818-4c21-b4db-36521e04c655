package com.msun.csm.model.dto;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/06/25/14:25
 */
@Data
public class ScheduleTaskDTO {

    @ApiModelProperty ("主键id")
    private Long scheduleTaskId;

    @ApiModelProperty ("任务名称")
    private String taskName;

    @ApiModelProperty ("任务code")
    private String taskCode;

    @ApiModelProperty ("=前端采用=是否启用【false：否；true：是】")
    private Boolean enableBool;

    @ApiModelProperty ("是否启用【0：否；1：是】")
    private Integer enableFlag;

    @ApiModelProperty ("任务说明")
    private String taskMessage;

    @ApiModelProperty ("计划运行时间")
    private String planRunTime;

    @ApiModelProperty ("计划运行时间")
    @JsonFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planRunTimeForPG;

    @ApiModelProperty ("触发的时间数据")
    private Integer triggerTime;

    @ApiModelProperty ("间隔时间单位【minute：分钟；hour：小时；day:天；month：月；year：年】")
    private String intervalTimeUnit;

    @ApiModelProperty ("最后执行时间更新Flag")
    private Integer lastRunTimeFlag;

    @JsonFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    private Date taskUpdateTime;
}
