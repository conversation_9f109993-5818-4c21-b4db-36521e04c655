package com.msun.csm.model.vo.projsettlement;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-06-17 10:49:37
 */

@Data
public class ProjProjectSettlementRuleHistoryVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String projectSettlementRuleId;

    /**
     * 项目信息id
     */
    @ApiModelProperty(value = "项目信息id")
    private String projectInfoId;

    /**
     * 项目规则内容
     */
    @ApiModelProperty(value = "项目规则内容")
    private String projectRuleContent;

    /**
     * 项目规则编码
     */
    @ApiModelProperty(value = "项目规则编码")
    private String projectRuleCode;

    /**
     * 可以下载的文件内容
     */
    @ApiModelProperty(value = "可以下载的文件内容")
    private List<ShowFile> documentList;
    /**
     * 模板文件
     */
    @ApiModelProperty(value = "模板文件")
    private ShowFile modelFile;

    /**
     * 入驻申请状态
     */
    @ApiModelProperty(value = "入驻申请状态")
    private Integer settlementStatus;

    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer orderNo;

    /**
     * 是否可编辑
     */
    @ApiModelProperty(value = "是否可编辑")
    private Boolean edit;

    @ApiModelProperty(value = "入驻申请关键标识（是否首付款等）")
    private ProjProjectSettlementKeyFlagVO keyFlagVO;
}
