package com.msun.csm.model.vo.networkdetected;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-05-14 09:10:11
 */

@Data
public class ProjNetworkDetDomainRecordVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 回传id, 也是平台定义id, 使用医院主键
     */
    @ApiModelProperty (value = "回传id, 也是平台定义id, 使用医院主键")
    private String logId;

    /**
     * 测试使用的域名
     */
    @ApiModelProperty (value = "测试使用的域名")
    private String webUrl;

    /**
     * 测试域名所解析出来的IP地址
     */
    @ApiModelProperty (value = "测试域名所解析出来的IP地址")
    private String ipAddress;

    /**
     * 测试的端口
     */
    @ApiModelProperty (value = "测试的端口")
    private int port;

    /**
     * 本机IP地址
     */
    @ApiModelProperty (value = "本机IP地址")
    private String localIpAddress;

    /**
     * 测试数量
     */
    @ApiModelProperty (value = "测试数量")
    private int count;

    /**
     * 测试失败数量
     */
    @ApiModelProperty (value = "测试失败数量")
    private int failed;

    /**
     * 成功率
     */
    @ApiModelProperty (value = "成功率")
    private String successRate;

    /**
     * 最小延时
     */
    @ApiModelProperty (value = "最小延时")
    private String minimum;

    /**
     * 最大延时
     */
    @ApiModelProperty (value = "最大延时")
    private String maximum;

    /**
     * 平均延时
     */
    @ApiModelProperty (value = "平均延时")
    private String average;

    /**
     * 总解析个数,当前测试为第几个
     */
    @ApiModelProperty (value = "总解析个数,当前测试为第几个")
    private String note;

    /**
     * 用于工具提交给交付平台结果标识，比如测试失败、解析失败等
     */
    @ApiModelProperty (value = "用于工具提交给交付平台结果标识，比如测试失败、解析失败等")
    private String testResult;

    /**
     * 域名解析的ip数量
     */
    @ApiModelProperty (value = "域名解析的ip数量")
    private int ipCount;

    /**
     * 网络检测状态。1 测试成功 0 失败
     */
    @ApiModelProperty (value = "网络检测状态。1 测试成功 0 失败")
    private int detectStatus;
}
