package com.msun.csm.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * AI智能体场景配置查询请求参数
 * <AUTHOR> @date 2025/07/07
 */
@Data
@ApiModel(description = "AI智能体场景配置查询请求参数")
public class ConfigAgentScenarioDictReq {

    @ApiModelProperty(value = "应用场景编码", example = "IMAGE_DETECTION")
    private String scenarioCode;

    @ApiModelProperty(value = "应用场景描述", example = "图片检测")
    private String scenarioDesc;

    @ApiModelProperty(value = "应用场景提示词", example = "请分析这张图片")
    private String scenarioPrompt;

    @ApiModelProperty(value = "智能体编号", example = "AI_IMAGE_DETECTION")
    private String agentCode;

    @ApiModelProperty(value = "智能体名称", example = "AI图片检测智能体")
    private String agentName;
}
