package com.msun.csm.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * AI智能体场景配置查询请求参数
 * <AUTHOR> @date 2025/07/07
 */
@Data
@ApiModel(description = "AI智能体场景配置查询请求参数")
public class ConfigAgentScenarioDictReq {

    @ApiModelProperty(value = "配置ID")
    private Long agentScenarioConfigId;

    @ApiModelProperty(value = "应用场景编码")
    private String scenarioCode;

    @ApiModelProperty(value = "应用场景描述")
    private String scenarioDesc;

    @ApiModelProperty(value = "应用场景提示词")
    private String scenarioPrompt;

    @ApiModelProperty(value = "智能体编号")
    private String agentCode;

    @ApiModelProperty(value = "智能体名称")
    private String agentName;
}
