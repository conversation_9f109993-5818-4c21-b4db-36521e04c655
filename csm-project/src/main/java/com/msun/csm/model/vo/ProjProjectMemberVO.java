package com.msun.csm.model.vo;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/26/14:28
 */
@Data
public class ProjProjectMemberVO {

    /**
     * 项目成员信息ID
     */
    @ApiModelProperty (value = "项目成员信息ID")
    private String projectMemberInfoId;
    /**
     * 项目信息ID
     */
    @ApiModelProperty (value = "项目信息ID")
    private Long projectInfoId;
    /**
     * 项目人员ID
     */
    @ApiModelProperty (value = "项目人员ID")
    private Long projectMemberId;
    /**
     * 项目人员名称
     */
    @ApiModelProperty (value = "项目人员名称")
    private String projectMemberName;

    @ApiModelProperty ("账号")
    private String account;


    /**
     * 项目人员团队ID
     */
    @ApiModelProperty (value = "项目人员团队ID")
    private Long projectTeamId;
    /**
     * 项目人员团队名称
     */
    @ApiModelProperty (value = "项目人员团队名称")
    private String projectTeamName;
    /**
     * 项目成员角色 不是sys_role
     */
    @ApiModelProperty (value = "项目成员角色id 不是sys_role")
    private String projectMemberRoleId;

    /**
     * 项目成员角色名称
     */
    @ApiModelProperty (value = "项目成员角色名称")
    private String projectRoleName;

    @ApiModelProperty ("手机号")
    private String phone;

    @ApiModelProperty ("项目成员角色code")
    private String projectRoleCode;

    @ApiModelProperty ("添加时间")
    @JsonFormat (pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("运营平台对应ID")
    private String userYunyingId;

    /**
     * 角色类型：1-前端角色；2-后端角色
     */
    private Integer roleType;

    /**
     * 当前人员信息是否可以编辑，true-可以编辑；false-不可编辑
     */
    private Boolean canEdit;
}
