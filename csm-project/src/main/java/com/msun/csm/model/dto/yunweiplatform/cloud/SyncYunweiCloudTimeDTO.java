package com.msun.csm.model.dto.yunweiplatform.cloud;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 同步运维时间传递参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SyncYunweiCloudTimeDTO {
    /**
     * 实施客户id
     */
    private Long customInfoId;
    /**
     * 项目类型
     */
    private Integer projectType;
    /**
     * 发函时间
     */
    private String sendLetterDate;
    /**
     * 到期时间
     */
    private String subscribeEndTime;
    /**
     * 到期提醒开关
     */
    private String mainRemindFlag;
}
