package com.msun.csm.model.dto.projreview;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 调研内容请求
 * <AUTHOR>
 */
@Data
public class ProjReviewAppDTO extends ProjReviewDTO {

    @ApiModelProperty(value = "项目经理")
    private String projectManagerName;

    @ApiModelProperty(value = "工单编码")
    private String workOrderCode;

    @ApiModelProperty(value = "客户名称")
    private String customName;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "客服名称")
    private String serviceName;

    @ApiModelProperty(value = "产品名称")
    private List<String> productName;

    @ApiModelProperty(value = "产品信息")
    private List<ProjReviewProductAppInfo> productAppInfoList;

    /**
     * 审核标识
     */
    @ApiModelProperty(value = "审核按钮是否展示 ")
    private Boolean showReviewButton;

}
