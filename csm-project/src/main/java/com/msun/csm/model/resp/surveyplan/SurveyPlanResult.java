package com.msun.csm.model.resp.surveyplan;

import lombok.Data;

@Data
public class SurveyPlanResult {

    /**
     * 产品Id
     */
    private Long yyProductId;

    /**
     * 产品名称
     */
    private String productInfoName;

    /**
     * 表单key
     */
    private String formKey;

    /**
     * 调研任务总数
     */
    private Integer total;

    /**
     * 已完成的调研任务数量
     */
    private Integer completedNum;

    /**
     * 调研状态
     */
    private String status;

    /**
     * 仅有一份调研结果时，查看原始调研结果的链接
     */
    private String detailUrl;

    /**
     * 查看最终确认的调研结果详情的链接
     */
    private String resultUrl;

    /**
     * 是否需要进入确认页面，存在超过一份调研结果时需要进入确认页面，true-需要；false-不需要
     */
    private Boolean needConfirm;

    /**
     * 调研结果答卷ID
     */
    private Long dataId;

    /**
     * 完成状态
     */
    private Boolean completeCode;

    /**
     * 是否需要调研【0：否； 1：是】
     */
    private Integer surveyFlag;

    /**
     * 排序
     */
    private Long orderNo;

}
