package com.msun.csm.model.req.projtool;

import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.dto.BasePageDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024-06-18 07:18:09
 */

@ApiModel(description = "查询数据")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(schema = "csm")
public class ProjToolConfigDataReq extends BasePageDTO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "配置code")
    private String configCode;

    @ApiModelProperty(value = "配置名称")
    private String configName;

    @ApiModelProperty(value = "配置值")
    private String configValue;
}
