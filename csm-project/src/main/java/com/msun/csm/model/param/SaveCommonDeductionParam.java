package com.msun.csm.model.param;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.msun.csm.dao.entity.proj.AttachmentInfoVO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaveCommonDeductionParam {

    /**
     * 主键
     */
    private Long id;

    /**
     * 实际扣分
     */
    private Integer practicalDeduction;

    /**
     * 扣分说明
     */
    private String remark;

    /**
     * 扣分类型字典表编码
     */
    private String deductionType;

    /**
     * 附件列表
     */
    private List<AttachmentInfoVO> attachmentInfoList;

    /**
     * 项目ID
     */
    @NotNull(message = "参数【projectInfoId】不可为null")
    private Long projectInfoId;

    /**
     * 菜单编码
     */
    @NotBlank(message = "参数【menuCode】不可为null")
    private String menuCode;

    /**
     * 是否只需要一次验收：true-只需要一次验收；false-需要两次验收
     */
    @NotNull(message = "参数【onlyOneCheckFlag】不可为null")
    private Boolean onlyOneCheckFlag;

    /**
     * 当前验收次数：1-第一次验收；2-第二次验收
     */
    @NotNull(message = "参数【currentAcceptanceTimes】不可为null")
    private Integer currentAcceptanceTimes;

}
