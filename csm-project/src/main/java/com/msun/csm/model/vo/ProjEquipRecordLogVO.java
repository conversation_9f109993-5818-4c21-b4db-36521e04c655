package com.msun.csm.model.vo;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ProjEquipRecordLogVO {

    @ApiModelProperty("运营产品id")
    private Long yyProductId;

    @ApiModelProperty ("设备记录业务id")
    private Long equipRecordBusinessId;

    @ApiModelProperty ("操作人id")
    private Long operatorId;

    @ApiModelProperty ("操作人姓名")
    private String operatorName;

    @ApiModelProperty ("电话")
    private String tel;

    @ApiModelProperty ("操作时间")
    private Date operateTime;

    @ApiModelProperty ("操作内容")
    private String operateContent;

    @ApiModelProperty ("操作名称")
    private String operateName;
}
