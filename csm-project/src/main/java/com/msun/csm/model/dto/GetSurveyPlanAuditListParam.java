package com.msun.csm.model.dto;

import java.util.List;

import lombok.Data;


@Data
public class GetSurveyPlanAuditListParam {

    /**
     * 当前页码
     */
    private Integer pageIndex;

    /**
     * 每页的数据条数
     */
    private Integer pageSize;

    /**
     * 模糊搜索的关键字
     */
    private String keyWord;

    /**
     * 客户ID
     */
    private Long customInfoId;

    /**
     * 项目ID
     */
    private Long projectInfoId;

    /**
     * 医院ID
     */
    private Long hospitalInfoId;

    /**
     * 运营平台产品ID
     */
    private Long yyProductId;

    /**
     * 审核人ID
     */
    private Long auditSysUserId;

    /**
     * 审核状态
     * <p>0-未完成（未开始）</p>
     * <p>1-已完成（确认之后的状态）</p>
     * <p>2-进行中</p>
     * <p>3-待确认（需要审核时审核通过的状态或者不需要审核时提交问卷之后的状态）</p>
     * <p>4-待审核（需要审核时提交问卷之后的状态）</p>
     * <p>5-已驳回（需要审核时驳回之后的状态）</p>
     * <p>6-已提交（需要审核时提交问卷之后的状态）</p>
     */
    private Integer completeStatus;
    /**
     * 审核状态
     * <p>0-未完成（未开始）</p>
     * <p>1-已完成（确认之后的状态）</p>
     * <p>2-进行中</p>
     * <p>3-待确认（需要审核时审核通过的状态或者不需要审核时提交问卷之后的状态）</p>
     * <p>4-待审核（需要审核时提交问卷之后的状态）</p>
     * <p>5-已驳回（需要审核时驳回之后的状态）</p>
     * <p>6-已提交（需要审核时提交问卷之后的状态）</p>
     */
    private List<Integer> completeStatusList;

    /**
     * 项目状态 1已派工、2已调研、3已入驻、4、准备完成、5已上线、6已验收、7已启动 、8 申请验收
     */
    private List<Integer> projectDeliverStatusList;
}
