package com.msun.csm.model.vo.cloudconfigresearch;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version : V1.52.0
 * @ClassName: CerifyPromptPopupVO
 * @Description:
 * @Author: Yhongmin
 * @Date: 11:19 2024/8/21
 */
@Data
public class CerifyPromptPopupVO {
    @ApiModelProperty("登录后是否弹出弹窗")
    private Boolean isPopup;
    @ApiModelProperty("调研配置量")
    private Integer surveyConfiguration;
    @ApiModelProperty("交付医院id")
    private Long  hospitalInfoId;
    @ApiModelProperty("系统管理路径和产品编码")
    private List<CloudConfigUrlVO> cloudConfigUrlVOList;
}
