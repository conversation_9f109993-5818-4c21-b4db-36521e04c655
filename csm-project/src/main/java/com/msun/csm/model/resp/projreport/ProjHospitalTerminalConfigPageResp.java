package com.msun.csm.model.resp.projreport;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.BaseCodeNameResp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 报表表单分页查询数据
 * @param <T>
 */
@Data
public class ProjHospitalTerminalConfigPageResp<T> extends PageInfo<T> {

    @ApiModelProperty(value = "医院")
    private List<BaseCodeNameResp> hospitalList;


    @ApiModelProperty(value = "符合标识")
    private List<BaseCodeNameResp> configMeetList;

    @ApiModelProperty(value = "操作系统")
    private List<BaseCodeNameResp> coperatSystemList;

    @ApiModelProperty(value = "位数")
    private List<BaseCodeNameResp> computerNumberList;

    public ProjHospitalTerminalConfigPageResp(List<T> list) {
        super(list);
    }
}