package com.msun.csm.model.dto.empower;

import com.msun.csm.service.proj.ProjOrderProductServiceImpl;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 手动授权描述产品或模块授权信息信息
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
public class EmpowerRecord {

    /**
     * 0 异常, 1 正常
     */
    private int status;

    /**
     * 授权内容描述, 可以是异常提醒
     */
    private String message;

    /**
     * 截取错误信息
     *
     * @return 自身对象
     */
    public EmpowerRecord subscribeMessage() {
        message = ProjOrderProductServiceImpl.getExMsgSubscribe(message);
        return this;
    }
}
