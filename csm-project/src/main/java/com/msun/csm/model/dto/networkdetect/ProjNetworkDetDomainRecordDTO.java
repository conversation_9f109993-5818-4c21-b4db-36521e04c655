package com.msun.csm.model.dto.networkdetect;

import java.io.Serializable;
import java.util.Date;

import javax.validation.constraints.NotNull;

import org.springframework.format.annotation.DateTimeFormat;

import com.msun.csm.common.model.dto.BasePageDTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-05-14 09:10:11
 */

@Data
public class ProjNetworkDetDomainRecordDTO extends BasePageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 回传id, 也是平台定义id, 使用医院主键
     */
    @NotNull
    @ApiModelProperty (value = "回传id, 也是平台定义id, 使用医院主键")
    private String logId;

    /**
     * 网络协议
     */
    @ApiModelProperty (value = "网络协议")
    private String protocol;

    /**
     * 测试使用的域名
     */
    @ApiModelProperty (value = "测试使用的域名")
    private String webUrl;

    /**
     * 测试域名所解析出来的IP地址
     */
    @NotNull
    @ApiModelProperty (value = "测试域名所解析出来的IP地址")
    private String ipAddress;

    /**
     * 测试的端口
     */
    @ApiModelProperty (value = "测试的端口")
    private int port;

    /**
     * 本机IP地址
     */
    @NotNull
    @ApiModelProperty (value = "本机IP地址")
    private String localIpAddress;

    /**
     * 测试数量
     */
    @ApiModelProperty (value = "测试数量")
    private int count;

    /**
     * 测试失败数量
     */
    @ApiModelProperty (value = "测试失败数量")
    private int failed;

    /**
     * 成功率
     */
    @ApiModelProperty (value = "成功率")
    private String successRate;

    /**
     * 最小延时
     */
    @ApiModelProperty (value = "最小延时")
    private String minimum;

    /**
     * 最大延时
     */
    @ApiModelProperty (value = "最大延时")
    private String maximum;

    /**
     * 平均延时
     */
    @ApiModelProperty (value = "平均延时")
    private String average;

    /**
     * 总解析个数,当前测试为第几个
     */
    @ApiModelProperty (value = "总解析个数,当前测试为第几个")
    private String note;

    /**
     * 用于工具提交给交付平台结果标识，比如测试失败、解析失败等
     */
    @ApiModelProperty (value = "用于工具提交给交付平台结果标识，比如测试失败、解析失败等")
    private String testResult;

    /**
     * 随机码
     */
    @ApiModelProperty (value = "随机码 ( 区分每一台电脑不同轮次的测试 )")
    private String randomCode;

    @ApiModelProperty (value = "测试时间")
    @DateTimeFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    private Date detectDateTime;
}
