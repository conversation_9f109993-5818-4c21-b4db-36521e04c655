package com.msun.csm.model.vo.oas;

import com.msun.core.commons.api.BaseVO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 自定义表单信息表(CustomForm)实体类
 *
 * <AUTHOR>
 * @since 2021-04-22 14:14:43
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FormPatternFindByPageVO extends BaseVO {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long formPatternId;

    /**
     * 表单名称
     */
    @ApiModelProperty(value = "表单样式名称")
    private String formPatternName;

    /**
     * 表单类型名称
     */
    @ApiModelProperty(value = "表单类型名称")
    private String formCategoryName;

    /**
     * 医院ID
     */
    @ApiModelProperty(value = "医院ID")
    private Long hospitalId;

    /**
     * 医院名称
     */
    @ApiModelProperty(value = "医院名称")
    private String hospitalName;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String hisCreaterName;
}
