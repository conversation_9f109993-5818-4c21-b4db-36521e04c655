package com.msun.csm.model.req.project;

import java.util.List;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/7/1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TransOldProjectReq {

    /**
     * 旧项目id
     */
    private List<Long> projectIds;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空")
    private Integer limitNum;

}
