package com.msun.csm.model.vo.projsettlement;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-06-17 10:49:37
 */

@Data
public class ProjProjectSettlementKeyFlagVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 是否交首付款 true:交了, false:未交
     */
    @ApiModelProperty(value = "是否交首付款")
    private Boolean paySignage;
    /**
     * 是否标准合同
     */
    @ApiModelProperty(value = "是否标准合同")
    private Boolean isStandardContract;
    /**
     * 是否首期项目
     */
    @ApiModelProperty(value = "是否首期项目")
    private Boolean isFirstProject;
    /**
     * 是否电销项目
     */
    @ApiModelProperty(value = "是否电销项目")
    private Boolean isTelSaleProject;
    /**
     * 是否派工云资源工单
     */
    @ApiModelProperty(value = "是否派工云资源工单")
    private Boolean isDispatchCloudResForm;
    /**
     * 是否派工中间件部署工单
     */
    @ApiModelProperty(value = "是否派工中间件部署工单")
    private Boolean isDispatchMiddlewareForm;


}
