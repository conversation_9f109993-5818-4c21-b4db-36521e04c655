package com.msun.csm.model.req.project;

import java.util.List;

import javax.validation.constraints.NotNull;

import com.msun.csm.common.model.BaseIdNameResp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 创建自定义项目请求参数
 * @Author: MengChuAn
 * @Date: 2024/10/11
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomProjectReq {

    /**
     * 项目名称
     */
    @NotNull(message = "项目名称不能为空")
    private String projectName;

    /**
     * 合同客户ID
     */
    @NotNull(message = "合同客户ID不能为空")
    private Long principalCustomInfoId;

    /**
     * 实施地客户ID
     */
    @NotNull(message = "实施地客户ID不能为空")
    private Long customInfoId;

    /**
     * 客服实施团队id
     */
    @NotNull(message = "客服实施团队不能为空")
    private Long projectTeamId;

    /**
     * 项目经理账号
     */
    @NotNull(message = "项目经理不能为空")
    private String projectLeaderAccount;

    /**
     * 销售人员账号
     */
    @NotNull(message = "销售人员不能为空")
    private String salesPersonAccount;

    /**
     * 项目类型 【1单体  2区域】
     */
    @NotNull(message = "项目类型不能为空")
    private Integer projectType;

    /**
     * 项目实施类型  1 老换新升级 2新客户上线
     */
    @NotNull(message = "项目实施类型不能为空")
    private Integer upgradationType;

    /**
     * 工单产品id、name
     */
    @NotNull(message = "工单产品不能为空")
    private List<BaseIdNameResp> orderProductList;

}
