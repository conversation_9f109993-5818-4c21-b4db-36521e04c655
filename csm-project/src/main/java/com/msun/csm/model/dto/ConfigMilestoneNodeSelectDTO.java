package com.msun.csm.model.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/12/06/10:45
 */
@Data
public class ConfigMilestoneNodeSelectDTO {

    @ApiModelProperty ("配置表主键id")
    private Long milestoneNodeConfigId;

    @ApiModelProperty ("里程碑节点名称")
    private String milestoneNodeName;

    @ApiModelProperty ("实施类型：-1.通用；1.老换新；2.新上线")
    private Integer upgradationType;

    @ApiModelProperty ("实施类型List")
    private List<Integer> upgradationTypeList;

    @ApiModelProperty ("项目首期标识：-1.通用；0.否（非首期可用）；1.是(首期可用)")
    private Integer hisFlag;

    @ApiModelProperty ("项目首期标识List")
    private List<Integer> hisFlagList;

    @ApiModelProperty ("单体可用Bool")
    private Boolean monomerFlagBool;

    @ApiModelProperty ("单体可用")
    private Integer monomerFlag;

    @ApiModelProperty ("区域可用Bool")
    private Boolean regionFlagBool;

    @ApiModelProperty ("区域可用")
    private Integer regionFlag;

    @ApiModelProperty ("电销可用Bool")
    private Boolean telesalesFlagBool;

    @ApiModelProperty ("电销可用")
    private Integer telesalesFlag;

    @ApiModelProperty ("自研可用Bool")
    private Boolean selfSoftwareFlagBool;

    @ApiModelProperty ("自研可用")
    private Integer selfSoftwareFlag;

    @ApiModelProperty ("外采可用Bool")
    private Boolean externalSoftwareFlagBool;

    @ApiModelProperty ("外采可用")
    private Integer externalSoftwareFlag;
}
