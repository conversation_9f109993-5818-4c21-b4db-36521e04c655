package com.msun.csm.util;

import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/12/3
 */
@Component
@Slf4j
public class CustomYunweiSignUtil {

    private static String knowledgeAppId;
    private static String knowledgePublicKey;

    @Value("${project.feign.knowledge.appId}")
    private String appId;

    @Value("${project.feign.knowledge.publicKey}")
    private String publicKey;

    private void setAppId(String appId) {
        CustomYunweiSignUtil.knowledgeAppId = appId;
    }

    private void setPublicKey(String publicKey) {
        CustomYunweiSignUtil.knowledgePublicKey = publicKey;
    }

    @PostConstruct
    public void init() {
        setPublicKey(publicKey);
        setAppId(appId);
    }


    /**
     * 请求头秘钥生成
     * @return
     */
    public static String getAuthorization() {
        // 时间戳
        Long timestamp = System.currentTimeMillis();
        // 原始串 【appId、publicKey 为运维分发】
        String old = "appid=" + knowledgeAppId + "&appsecret=" + knowledgePublicKey + "&timestamp=" + timestamp;
        // rsa工具
        RSA rsa = new RSA(null, knowledgePublicKey);
        // 加密
        String sign = rsa.encryptBase64(old, KeyType.PublicKey);
        // 拼接
        String headers = "appid=" + knowledgeAppId + ";sign=" + sign;
        return headers;
    }

}
