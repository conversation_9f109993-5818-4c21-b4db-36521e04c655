package com.msun.csm.util;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.URL;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Map;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2024/11/7
 */
@Slf4j
public class HttpClientUtil {
    public static String doPost2s(String url, String jsonstr, Map<String, Object> headerMap) {
        PrintWriter out = null;
        InputStream is = null;
        BufferedReader br = null;
        String result = "";
        HttpsURLConnection conn = null;
        StringBuffer strBuffer = new StringBuffer();
        try {
            // 创建SSLContext
            SSLContext sslContext = SSLContext.getInstance("TLSv1.2");
            TrustManager[] tm = {new X509TrustManager() {
                @Override
                public void checkClientTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
                }

                @Override
                public void checkServerTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
                }

                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    return new X509Certificate[0];
                }
            }};
            // 初始化
            sslContext.init(null, tm, new java.security.SecureRandom());
            // 获取SSLSocketFactory对象
            SSLSocketFactory ssf = sslContext.getSocketFactory();
            URL realUrl = new URL(url);
            conn = (HttpsURLConnection) realUrl.openConnection();
            // 设置通用的请求属性
            conn.setRequestMethod("POST");
            conn.setConnectTimeout(20000);
            conn.setReadTimeout(300000);
            conn.setRequestProperty("Charset", "UTF-8");
            // 传输数据为json，如果为其他格式可以进行修改
            conn.setRequestProperty("Content-Type", "application/json; charset=utf-8");
            for (Map.Entry<String, Object> entry : headerMap.entrySet()) {
                conn.setRequestProperty(entry.getKey(), String.valueOf(entry.getValue()));
            }
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setSSLSocketFactory(ssf);
            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(conn.getOutputStream());
            // 发送请求参数
            log.info(url + "post请求数据" + jsonstr);
            out.print(jsonstr);
            // flush输出流的缓冲s
            out.flush();
            is = conn.getInputStream();
            br = new BufferedReader(new InputStreamReader(is));
            String line = null;
            while ((line = br.readLine()) != null) {
                strBuffer.append(line);
            }
            result = strBuffer.toString();
            log.info("响应结果" + result);
        } catch (Exception e) {
            log.info("请求异常", e);
        }
        // 使用finally块来关闭输出流、输入流
        finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (br != null) {
                    br.close();
                }
                if (conn != null) {
                    conn.disconnect();
                }
            } catch (IOException ex) {
                log.info("调用异常", ex);
            }
        }
        log.info(url + "post请求响应结果" + result);
        return result;
    }

    @SuppressWarnings("resource")
    public static String doPost(String url, String jsonstr, Map<String, Object> headerMap) {
        HttpClient httpClient = null;
        HttpPost httpPost = null;
        String result = null;
        try {
            httpClient = new SslClient();
            httpPost = new HttpPost(url);
            httpPost.addHeader("Content-Type", "application/json");
            for (Map.Entry<String, Object> entry : headerMap.entrySet()) {
                httpPost.setHeader(entry.getKey(), String.valueOf(entry.getValue()));
            }
            httpPost.setEntity(new StringEntity(jsonstr, ContentType.create("application/json", "UTF-8")));
            HttpResponse response = httpClient.execute(httpPost);
            if (response != null) {
                HttpEntity resEntity = response.getEntity();
                if (resEntity != null) {
                    result = EntityUtils.toString(resEntity, "utf-8");
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return result;
    }

    @SuppressWarnings("resource")
    public static String doGet(String url, String jsonstr, Map<String, Object> headerMap) {
        HttpClient httpClient = null;
        HttpGet httpPost = null;
        String result = null;
        try {
            httpClient = new SslClient();
            httpPost = new HttpGet(url);
            httpPost.addHeader("Content-Type", "application/json");
            for (Map.Entry<String, Object> entry : headerMap.entrySet()) {
                httpPost.setHeader(entry.getKey(), String.valueOf(entry.getValue()));
            }
            HttpResponse response = httpClient.execute(httpPost);
            if (response != null) {
                HttpEntity resEntity = response.getEntity();
                if (resEntity != null) {
                    result = EntityUtils.toString(resEntity, "utf-8");
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return result;
    }

    public static RestTemplate createRestTemplate(int connectTimeout, int readTimeout) {
        // 创建 RequestFactory
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(connectTimeout);
        factory.setReadTimeout(readTimeout);
        // 创建 RestTemplate
        RestTemplate restTemplate = new RestTemplate(factory);
        return restTemplate;
    }


}