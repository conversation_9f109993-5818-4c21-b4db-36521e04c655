package com.msun.csm.util;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.time.DateFormatUtils;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {

    private static final String[] PATTERNS = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    private static final String DATE_FORMAT = "yyyy-MM-dd";

    private static final String TIME_FORMAT = "HH:mm:ss";

    private static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate() {
        return dateTimeNow("yyyy-MM-dd");
    }

    public static final String getTime() {
        return dateTimeNow("yyyy-MM-dd HH:mm:ss");
    }

    public static final String dateTimeNow() {
        return dateTimeNow("yyyyMMddHHmmss");
    }

    public static final String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date) {
        return parseDateToStr("yyyy-MM-dd", date);
    }

    public static final String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts) {
        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), PATTERNS);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算两个时间差
     */
    public static String getDatePoor(Date endDate, Date nowDate) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * 在日期集合中合并连续的日期，展示为 yyyy-MM-dd ~ yyyy-MM-dd
     *
     * @param dates 日期集合
     * <AUTHOR>
     * @date 2022/5/16 8:45
     * @return: java.lang.String
     */
    public static String getContinuityDate(List<Date> dates) {
        // 对集合进行排序
        dates.sort(Date::compareTo);
        StringBuilder builder = new StringBuilder();
        // 标记上一天的日期
        Date start = null;
        // 标记与上一天日期连续的日期
        Date end = null;
        // 表示当前循环的日期
        Date date;
        for (int i = 0; i < dates.size(); i++) {
            date = dates.get(i);
            if (i == 0) {
                builder.append(DateUtil.format(date, "yyyy-MM-dd"));
            } else {
                long between = DateUtil.between(start, date, DateUnit.DAY);
                if (between > 1) {
                    // 不连续
                    if (end == null) {
                        builder.append(",").append(DateUtil.format(date, "yyyy-MM-dd"));
                    } else {
                        builder.append("~").append(DateUtil.format(end, "yyyy-MM-dd")).append(",").append(DateUtil.format(date, "yyyy-MM-dd"));
                        end = null;
                    }
                } else {
                    // 连续
                    end = date;
                    if (i == dates.size() - 1) {
                        // 如果最后一天为连续日期，则结束连续
                        builder.append("~").append(DateUtil.format(end, "yyyy-MM-dd"));
                    }
                }
            }
            start = date;
        }
        return builder.toString();
    }

    /**
     * 根据field不同加减不同值
     *
     * @param date
     * @param field  Calendar.YEAR
     * @param number 1000/-1000
     */
    public static Date calculationDate(Date date, int field, int number) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(field, number);
        return calendar.getTime();
    }

    /**
     * 计算月差
     */
    public static int getMonthPoor(String beginDate, String endDate) {
        if (StringUtils.isEmpty(beginDate) || StringUtils.isEmpty(endDate)) {
            return 0;
        }
        Date begin = DateUtils.dateStrToDate(beginDate);
        Date end = DateUtils.dateStrToDate(endDate);
        return end.getMonth() - begin.getMonth() + 1;
    }

    /**
     * 日期字符串 转 Date
     *
     * @param dateStr
     * @return
     */
    public static Date dateStrToDate(String dateStr) {
        return Date.from(LocalDate.parse(dateStr, DATE_FORMATTER).atStartOfDay(ZoneId.systemDefault()).toInstant());

    }

    /**
     * 计算两个日期时间差
     *
     * @param start yyyy-MM-dd HH:mm:ss
     * @param end   yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static long dateDifference(String start, String end) {
        SimpleDateFormat format = new SimpleDateFormat(DATE_TIME_FORMAT);
        try {
            Date startTime = format.parse(start);
            Date endTime = format.parse(end);
            return endTime.getTime() - startTime.getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return -1;
    }

    /**
     * 获取当前时间的一周的开始时间和结束时间
     *
     * @param date
     * @return
     */
    public static List<Date> dateToWeek(Date date) {
        int b = date.getDay();
        Date fdate;
        List<Date> list = new ArrayList<Date>();
        Long fTime = date.getTime() - b * 24 * 3600000;
        for (int a = 1; a <= 7; a++) {
            fdate = new Date();
            fdate.setTime(fTime + (a * 24 * 3600000));
            list.add(a - 1, fdate);
        }
        return list;
    }


    public static String formatTime(String value) {
        if (StrUtil.isBlank(value)) {
            return value;
        }
        if (ReUtil.contains("\\d{4}(\\-|\\/|.|)\\d{2}\\1\\d{2}", value)) {
            try {
                Date date = DateUtil.parse(value);
                return DateUtil.format(date, "yyyy-MM-dd");
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (ReUtil.contains("\\d{4}\\u5e74\\d{1,2}\\u6708\\d{1,2}\\u65e5", value)) {
            try {
                Date date = DateUtil.parse(value, "yyyy年MM月dd");
                return DateUtil.format(date, "yyyy-MM-dd");
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (ReUtil.contains("\\d{4}(\\.)\\d{1}\\1\\d{1}", value)) {
            try {
                Date date = DateUtil.parse(value, "yyyy.M.d");
                return DateUtil.format(date, "yyyy-MM-dd");
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (ReUtil.contains("\\d{4}(\\-)\\d{1}\\1\\d{1}", value)) {
            try {
                Date date = DateUtil.parse(value, "yyyy-M-d");
                return DateUtil.format(date, "yyyy-MM-dd");
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (ReUtil.contains("\\d{4}(\\/)\\d{1}\\1\\d{1}", value)) {
            try {
                Date date = DateUtil.parse(value, "yyyy/M/d");
                return DateUtil.format(date, "yyyy-MM-dd");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return "";
    }


    // 日期加减天数
    public static LocalDate addDays(LocalDate date, int days) {
        return date.plusDays(days);
    }

    // 日期加减月份
    public static LocalDate addMonths(LocalDate date, int months) {
        return date.plusMonths(months);
    }

    public static String formatDateTime(Date date, String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(date);
    }

}
