package com.msun.csm.util;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.msun.csm.common.config.ProxyNetworkConfig;

import cn.hutool.core.util.StrUtil;


/**
 * 调用云健康之前domain统一处理
 */
@Component
public class DomainMapUtil {

    static String activeProfiles;

    public static String getActiveProfiles() {
        return activeProfiles;
    }

    @Value("${spring.profiles.active}")
    public void setActiveProfiles(String activeProfiles) {
        DomainMapUtil.activeProfiles = activeProfiles;
    }


    /**
     * 获取domain
     *
     * @param source
     * @return
     */
    public static Map<String, String> getDomainMap(Object source) {
        // 调用API时设置domain信息
        Map<String, String> domainMap = new HashMap<>();
        // 获取类的所有字段，包括私有的
        Field[] fields = source.getClass().getDeclaredFields();
        String orgId = "";
        String cloudHospitalId = "";
        String cloudDomain = "";
        for (Field field : fields) {
            // 设置访问权限，以便可以访问私有字段
            field.setAccessible(true);
            try {
                // 获取字段的值并转换为字符串
                String value = String.valueOf(field.get(source));
                if ("orgId".equals(field.getName())) {
                    orgId = value;
                }
                if ("cloudHospitalId".equals(field.getName())) {
                    cloudHospitalId = value;
                }
                if ("cloudDomain".equals(field.getName())) {
                    cloudDomain = value;
                }
            } catch (IllegalAccessException e) {
                // 处理可能的访问异常
                e.printStackTrace();
            }
        }
        if ("prod".equals(activeProfiles)) {
            cloudDomain = ProxyNetworkConfig.getProxyNetworkUrl();
        } else if ("test".equals(activeProfiles)) {
            cloudDomain = "http://************:10080";
        }
        domainMap.put(orgId + "-" + cloudHospitalId, cloudDomain);
        domainMap.put("hospitalId", cloudHospitalId);
        domainMap.put("orgId", orgId);
        domainMap.put("host", cloudDomain.replace("http://", StrUtil.EMPTY).replace("https://", StrUtil.EMPTY));
        return domainMap;
    }

    /**
     * 获取domain
     * @param orgId
     * @param cloudHospitalId
     * @param cloudDomain
     * @return
     */
    public static Map<String, String> getDomainMap(Object orgId, Object cloudHospitalId, String cloudDomain) {
        // 调用API时设置domain信息
        Map<String, String> domainMap = new HashMap<>();
        if ("prod".equals(activeProfiles)) {
            cloudDomain = ProxyNetworkConfig.getProxyNetworkUrl();
        }
        domainMap.put(orgId + "-" + cloudHospitalId, cloudDomain);
        domainMap.put("host", cloudDomain.replace("http://", StrUtil.EMPTY).replace("https://", StrUtil.EMPTY));
        return domainMap;
    }
}
