package com.msun.csm.util;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.ResponseExtractor;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.alibaba.fastjson.JSONObject;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/12/3
 */
@Component
@Slf4j
public class MsunChatMessagesUtil {

    /**
     * 开启问答
     *
     * @param urls
     * @param userId
     * @param question
     * @return
     * @throws IOException
     * @throws InterruptedException
     */
    public static Map<String, Object> sendChartMessage(List<String> urls, Long userId, String question, String aiAnswerKey, String aiAnswerUrl) throws IOException, InterruptedException {
        RestTemplate restTemplate = HttpClientUtil.createRestTemplate(5000, 180000);
        if (userId == null) {
            userId = 99999999999999999L;
        }
        //创建服务器事件推送发射器
        SseEmitter emitter = new SseEmitter(3 * 60 * 1000L);
        // 设置请求回调
        JSONObject joRquest = new JSONObject();
        JSONObject jo = new JSONObject();
        jo.put("inputs", joRquest);
        jo.put("query", question);
        jo.put("response_mode", "streaming");
        jo.put("conversation_id", "");
        jo.put("user", userId.toString());
        if (urls != null && !urls.isEmpty()) {
            List<Map<String, String>> files = new ArrayList<>();
            for (String url : urls) {
                Map<String, String> file = new HashMap<>();
                file.put("url", url);
                file.put("type", "image");
                file.put("transfer_method", "remote_url");
                files.add(file);
            }
            jo.put("files", files);
        }
        log.info("智能问答，流式输出时间1：{}，入参：{}", DateUtils.getTime(), jo);
        RequestCallback requestCallback = request -> {
            request.getHeaders().set("Content-Type", "application/json");
            request.getHeaders().set("Accept", "text/event-stream");
            request.getHeaders().set("Authorization", "Bearer " + aiAnswerKey);
            request.getBody().write(jo.toString().getBytes(StandardCharsets.UTF_8));
        };
        // 创建响应提取器处理流式响应
        StringBuilder textB = new StringBuilder();
        ResponseExtractor<String> responseExtractor = response -> {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.getBody(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    // 处理每一行流式数据
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(line)) {
                        emitter.send(SseEmitter.event().data(line));
                        if (!line.contains("event: ping")) {
                            JSONObject joLine = JSONObject.parseObject(line.replace("data: ", ""));
                            textB.append(joLine.getString("answer"));
                        }
                    }
                }
                log.info("智能问答，流式输出时间4：{}，结果：{}", DateUtils.getTime(), textB);
            }
            emitter.complete();
            return textB.toString();
        };
        // 执行请求
        log.info("智能问答，流式输出时间2：{}，请求地址：{}，入参：{}", DateUtils.getTime(), aiAnswerUrl, jo);
        restTemplate.execute(aiAnswerUrl, HttpMethod.POST, requestCallback, responseExtractor);
        if (textB.length() > 0) {
            String jsonString = textB.toString().replaceAll("```json\\n", "").replace("\\n$", "").replaceAll("```null", "");
            JSONObject jsonObject = JSONObject.parseObject(jsonString);
            return jsonObject;
        }
        return new HashMap<>();
    }
}
