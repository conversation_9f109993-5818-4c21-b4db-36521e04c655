package com.msun.csm.feign.entity.tduck.req;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DictTaskInfoVO {

    /**
     * 实施产品的运营平台产品ID
     */
    private Long yyProductId;

    /**
     * 项目编码（唯一）
     */
    private String itemCode;

    /**
     * 项目名称，前端下拉框展示的内容
     */
    private String itemName;

    /**
     * 待办检测方式：interface-接口检测；sql-SQL脚本检测
     */
    private String taskValidateType;

    /**
     * 待办检测结果脚本/接口
     */
    private String taskValidateInfo;

    /**
     * 待办跳转的页面地址
     */
    private String taskPageUrl;

    /**
     * 待办跳转页面时使用的云健康产品菜单编码
     */
    private String cloudProductCode;

    /**
     * 待办任务明细
     */
    private String taskDetail;

    /**
     * 待办说明链接
     */
    private String taskExplainLink;

    /**
     * 检测标准
     */
    private String validateStandards;

    /**
     * 检测明细脚本
     */
    private String validateDetailSql;

}
