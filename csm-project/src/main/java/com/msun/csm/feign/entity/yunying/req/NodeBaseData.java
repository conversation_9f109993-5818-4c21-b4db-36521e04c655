package com.msun.csm.feign.entity.yunying.req;

import java.util.ArrayList;
import java.util.List;

import com.msun.csm.model.vo.user.SysUserVO;

import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 节点基础信息
 * @Author: MengChuAn
 * @Date: 2023-10-28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NodeBaseData {
    /**
     * 负责人Id
     */
    private Long managerUserId;

    /**
     * 负责人操作（日志）
     * eg: ”张洋进行了发货：5G路由器*5“
     */
    private String managerOper;

    /**
     * 操作时间yyyy-MM-dd HH:mm:ss
     */
    private String managerOperDate;

    /**
     * 纸质报告时间yyyy-MM-dd HH:mm:ss,第一期设置为与操作时间一致
     */
    private String managerActualDate;

    /**
     * 文件
     */
    private List<FileReq> fileList = new ArrayList<>();


    /**
     * 本次操作涉及的产品信息
     */
    private List<ProductReq> product;


    public NodeBaseData(SysUserVO sysUser, String oper, String time) {
        this.setManagerUserId(ObjectUtil.isNotEmpty(sysUser) ? Long.valueOf(sysUser.getUserYunyingId()) : null);
        this.setManagerOper(ObjectUtil.isNotEmpty(sysUser) ? sysUser.getUserName() + oper : oper);
        this.setManagerOperDate(time);
        this.setManagerActualDate(time);
    }

    public NodeBaseData(String userYunyingId, String userName, String oper, String time) {
        this.setManagerUserId(ObjectUtil.isNotEmpty(userYunyingId) ? Long.valueOf(userYunyingId) : null);
        this.setManagerOper(ObjectUtil.isNotEmpty(userYunyingId) ? userName + oper : oper);
        this.setManagerOperDate(time);
        this.setManagerActualDate(time);
    }
}
