package com.msun.csm.feign.entity.oldimsp.req;

import java.io.Serializable;

import lombok.Data;

@Data
public class ProjectManagerConfirmDto implements Serializable {

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 老换新、新客户
     */
    private String type1;


    /**
     * 单体、区域
     */
    private String type2;

    /**
     * 首期 非首期
     * 1: 首期   2 非首期
     */
    private Integer type3;


    /**
     * 工单类型（软件、硬件、外采...）
     */
    private Integer projCate;

    /**
     * 客户id
     */
    private Integer customerId;

}
