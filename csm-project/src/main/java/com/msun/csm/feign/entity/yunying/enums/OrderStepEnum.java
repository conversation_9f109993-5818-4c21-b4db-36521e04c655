package com.msun.csm.feign.entity.yunying.enums;

import lombok.Getter;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2023-10-31
 */
@Getter
public enum OrderStepEnum {

    //orderType 1软件 2硬件 3耗材 4接口 5软件服务费 6硬件服务费 7容灾 8外采软件 9云资源
    //软件
    SOFTWARE_SURVEY(1, 2, "调研", "完成了调研"),
    SOFTWARE_ON_SITE(1, 3, "入驻", "完成了入驻"),
    SOFTWARE_TEST(1, 7, "测试", "完成了测试"),
    SOFTWARE_ONLINE(1, 9, "上线", "完成了上线"),
    SOFTWARE_CHECK(1, 10, "验收", "申请了软件验收"),
    //外采软件
    PURCHASE_SOFTWARE_SURVEY(8, 59, "调研", "完成了调研"),
    PURCHASE_SOFTWARE_ON_SITE(8, 60, "入驻", "完成了入驻"),
    PURCHASE_SOFTWARE_TEST(8, 64, "测试", "完成了测试"),
    PURCHASE_SOFTWARE_ONLINE(8, 66, "上线", "完成了上线"),
    PURCHASE_SOFTWARE_CHECK(8, 67, "验收", "申请了软件验收"),
    //硬件
    HARDWARE_APPLY(2, 11, "采购申请", "发起了采购申请"),
    HARDWARE_ACCEPT(2, 12, "采购接收", "接收了采购申请"),
    HARDWARE_SEND_OUT(2, 13, "发货", "完成了硬件发货"),
    HARDWARE_ARRIVE(2, 14, "到货", "确认了硬件到货"),
    HARDWARE_RECEIVE(2, 15, "收货", "完成了硬件收货"),
    HARDWARE_INTEGRATION(2, 16, "集成", "完成了硬件集成"),
    //耗材
    CONSUMABLE_APPLY(3, 81, "采购申请", "发起了采购申请"),
    CONSUMABLE_ACCEPT(3, 82, "采购接收", "接收了采购申请"),
    CONSUMABLE_SEND_OUT(3, 83, "发货", "完成了硬件发货"),
    CONSUMABLE_ARRIVE(3, 84, "到货", "确认了硬件到货"),
    CONSUMABLE_RECEIVE(3, 85, "收货", "完成了硬件收货"),
    //云资源
    CLOUD_APPLY(9, 68, "申请", "完成了云资源申请"),
    CLOUD_DEPLOY(9, 69, "部署", "完成了云资源部署"),
    CLOUD_OPEN(9, 70, "正式开通", "完成了云资源开通"),
    CLOUD_CHECK(9, 72, "验收", "完成了云资源验收"),
    //云容灾
    DISASTER_CLOUD_APPLY(10, 47, "申请", "完成了云容灾申请"), //
    DISASTER_CLOUD_DEPLOY(10, 48, "部署", "完成了云容灾部署"), //
    DISASTER_CLOUD_OPEN(10, 49, "正式开通", "完成了云容灾开通"), //
    DISASTER_CLOUD_CHECK(10, 50, "验收", "完成了云容灾验收"); //


    private Integer orderType;
    private Integer stepId;
    private String stepName;
    private String operDesc;

    OrderStepEnum(Integer orderType, Integer stepId, String stepName, String operDesc) {
        this.orderType = orderType;
        this.stepId = stepId;
        this.stepName = stepName;
        this.operDesc = operDesc;
    }

    public static OrderStepEnum getOrderStepEnum(Integer stepId) {
        for (OrderStepEnum orderStepEnum : OrderStepEnum.values()) {
            if (orderStepEnum.getStepId().equals(stepId)) {
                return orderStepEnum;
            }
        }
        return null;
    }
}
