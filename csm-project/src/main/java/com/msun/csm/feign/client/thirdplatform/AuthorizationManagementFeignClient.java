package com.msun.csm.feign.client.thirdplatform;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import com.msun.csm.model.dto.CreateAppAuthorizationDTO;
import com.msun.csm.model.dto.SaveDeployDeviceInfoDTO;
import com.msun.csm.model.vo.CreateAppAuthorizationReesult;

import cn.hutool.json.JSONObject;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/10/14/18:33
 */
@FeignClient (url = "${project.feign.authorizationManagement.url}", name = "AuthorizationManagementFeignClient")
public interface AuthorizationManagementFeignClient {

    /**
     * 获取国密授权
     *
     * @param dto
     * @return
     */
    @PostMapping (value = "${project.feign.authorizationManagement.createAppAuthorization-method}",
            headers = {"Content-Type=application/json;charset=UTF-8"})
    CreateAppAuthorizationReesult createAppAuthorization(@RequestHeader ("sign") String sign,
                                                         @RequestHeader ("timestamp") String timestamp,
                                                         @RequestBody CreateAppAuthorizationDTO dto);

    /**
     * 部署设备授权
     *
     * @param dto
     * @return
     */
    @PostMapping (value = "${project.feign.authorizationManagement.saveDeployDeviceInfo-method}",
            headers = {"Content-Type=application/json;charset=UTF-8"})
    JSONObject saveDeployDeviceInfo(@RequestHeader ("sign") String sign,
                                    @RequestHeader ("timestamp") String timestamp,
                                    @RequestBody SaveDeployDeviceInfoDTO dto);

    /**
     * 获取三方接口授权服务器设备信息
     * @param timestamp
     * @param appId
     * @return
     */
    @GetMapping(value = "${project.feign.authorizationManagement.getDeployDeviceInfo-method}",
            headers = {"Content-Type=application/json;charset=UTF-8"})
    JSONObject getDeployDeviceInfo(@RequestHeader ("timestamp") String timestamp, @RequestParam("appId") String appId);
}
