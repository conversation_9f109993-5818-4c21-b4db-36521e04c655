package com.msun.csm.feign.client.productauth;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.msun.csm.feign.entity.productauth.req.MrsQAAuthReq;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2023-09-26
 */
@FeignClient(url = "${product_auth.domainName}", name = "ProductAuthFeignClient")
public interface ProductAuthFeignClient {

    /**
     * 病案首页质控授权
     *
     * @param req
     * @return
     */
    @PostMapping(value = "${product_auth.mrs-qa}")
    String mrsAuthorization(@RequestBody MrsQAAuthReq req);

}
