package com.msun.csm.feign.entity.yunying.req;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 云容灾验收申请参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class YunyingDisasterCloudCheckApplyDTO {

    /**
     * 调用当前接口的密钥（安全考虑）
     * imsp
     */
    private String token;

    /**
     * 运营平台阶段配置表ID
     */
    private String stepId;

    /**
     * 合同编号（运营平台合同id, Long类型）
     */
    private Long contractNum;

    /**
     * 项目编号
     */
    private Long projectNum;

    /**
     * 工单id
     */
    private Long workOrderId;

    /**
     * 第几次提交验收申请
     */
    private Integer currentTimes;

    /**
     * 容灾回执单附件地址(可传多个)
     */
    private List<FileReq> fileList;

    /**
     * 可传多个传品
     */
    private List<ProductReq> product;

}
