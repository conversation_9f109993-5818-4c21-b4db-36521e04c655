package com.msun.csm.feign.entity.openapi.req;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/9/14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApplyAuthByGroupsReq {

    @ApiModelProperty("申请人员")
    private String applyUser;

    @ApiModelProperty("申请秘钥")
    private String appKey;

    @ApiModelProperty("申请的医院id集合")
    private List<Long> hospitalIds;

    @ApiModelProperty("申请的接口分组")
    private List<Long> groupIds;

    @ApiModelProperty("申请描述")
    private String description;

    @ApiModelProperty("申请的环境信息")
    private String env;

    @ApiModelProperty("接口类型【0：上传类；1：交互类；2：医保类；3：接口类】")
    private Integer interfaceType;

    @ApiModelProperty("是否是测试环境授权")
    private Integer isTestEnv;

}
