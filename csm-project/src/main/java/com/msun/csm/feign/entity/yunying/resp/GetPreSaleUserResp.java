package com.msun.csm.feign.entity.yunying.resp;

import java.io.Serializable;

import lombok.Data;

/**
 * 调用运营平台发送消息返回信息
 */
@Data
public class GetPreSaleUserResp implements Serializable {

    /**
     * 返回状态码 200为成功，其余失败
     */
    private Integer code;

    /**
     * 成功返回true，失败返回false
     */
    private Boolean success;

    /**
     * 返回消息提示
     */
    private String msg;

    /**
     * 发送结果
     */
    private GetPreSaleUserIdResp obj;
}
