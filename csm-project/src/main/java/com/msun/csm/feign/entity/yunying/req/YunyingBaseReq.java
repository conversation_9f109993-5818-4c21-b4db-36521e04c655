package com.msun.csm.feign.entity.yunying.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 请求运营平台状态基础数据
 * @Author: MengChuAn
 * @Date: 2023-11-03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class YunyingBaseReq extends TokenReq {


    /**
     * 运营平台阶段配置表ID
     */
    private String stepId;

    /**
     * 合同编号 必须
     */
    private Long contractNum;

    /**
     * 项目编号 必须
     */
    private Long projectNum;

    /**
     * 工单编号 必须
     */
    private Long workOrderId;
}
