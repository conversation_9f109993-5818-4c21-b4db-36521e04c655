package com.msun.csm.feign.entity.yunying.req;

import java.util.ArrayList;
import java.util.List;

import lombok.Data;

/**
 * 开通云资源
 */
@Data
public class YunyingMidOrderDTO {

    /**
     * 调用当前接口的密钥（安全考虑）
     * imsp
     */
    private String token;

    /**
     * 软件工单编号
     */
    private Long projId;

    /**
     * 免中间件原因描述
     */
    private String remark;

    /**
     * 附件
     */
    private List<FileReq> fileList = new ArrayList<>();

}
