package com.msun.csm.feign.entity.yunying.req;

import com.msun.csm.model.resp.project.ProductBase;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024-01-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductBaseReq {
    /**
     * 工单产品表ID，推工单时已同步至交付平台
     */
    private String projProductId;

    /**
     * 产品编号-运营平台产品Id
     */
    private Long productId;

    /**
     * 产品数量
     */
    private Integer num;

    /**
     * 解决方案
     */
    private String pemCusSolType;

    /**
     * 卫生院数量  默认1
     */
    private Integer hospitalNums;

    public ProductBaseReq(ProductBase product) {
        this.setPemCusSolType(product.getPemcusssolType().toString());
        this.setProductId(product.getYyId());
        this.setProjProductId(product.getProjProductId());
        this.setNum(1);
        this.setHospitalNums(1);
    }
}
