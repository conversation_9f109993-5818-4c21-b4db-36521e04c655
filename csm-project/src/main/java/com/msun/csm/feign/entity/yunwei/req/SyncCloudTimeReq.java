package com.msun.csm.feign.entity.yunwei.req;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 同步云资源开通时间和结束时间
 */
@Data
public class SyncCloudTimeReq implements Serializable {

    /**
     * 类型: 1 云资源  2 云订阅  3 云容灾
     */
    private Integer type;

    /**
     * 1 单体  2  区域
     */
    private Integer areaType;

    /**
     * 内容反馈：1、已与客户沟通且达成一致，承诺按时签回；
     * 2、已与客户沟通，尚未达成共识；
     * 3、未和医院沟通；
     * 4、其他
     */
    private Integer cloudFeedback;

    /**
     * 发函时间
     * 云资源到期提醒必填	yyyy-MM-dd
     */
    private String sendLetterTime;

    /**
     * 医院信息(包含医院id及产品的开始开始结束时间)
     */
    private List<SyncCloudTimeHospitalReq> hospitals;

    public SyncCloudTimeReq(Integer type, Integer areaType) {
        this.type = type;
        this.areaType = areaType;
    }
}
