package com.msun.csm.feign.entity.yunying.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/23/10:49
 */
@Data
public class YunYingDeptResp {

    @ApiModelProperty ("部门id")
    private Long id;

    @ApiModelProperty ("部门名称")
    private String name;

    @ApiModelProperty ("部门负责人id")
    private String orgUserId;

    @ApiModelProperty ("部门负责人登录名")
    private String orgUserLoginname;

    @ApiModelProperty ("部门层级")
    private Integer levels;

    @ApiModelProperty ("上级部门")
    private Long pid;

    @ApiModelProperty ("上级部门名称")
    private String parentName;

    @ApiModelProperty ("部门类型")
    private String orgCategory;

    @ApiModelProperty ("副职，当存在副职能是才存在")
    private YunYingDeptSubResp subfunction;
}
