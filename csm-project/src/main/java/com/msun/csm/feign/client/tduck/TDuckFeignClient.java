package com.msun.csm.feign.client.tduck;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.msun.csm.feign.entity.tduck.req.GenerateTaskReq;
import com.msun.csm.feign.entity.tduck.req.GetDictTaskInfoParam;
import com.msun.csm.feign.entity.tduck.req.SplitAndMergeReq;


/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2023-09-26
 */
@FeignClient(url = "${project.feign.tduck.url}", name = "TDuckFeignClient")
public interface TDuckFeignClient {


    /**
     * 通知生成待处理任务
     *
     * @param generateTaskReq
     * @return
     */
    @PostMapping(value = "${project.feign.tduck.generate-task}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    String generateTask(@RequestBody GenerateTaskReq generateTaskReq);

    /**
     * 项目合并
     *
     * @param splitAndMergeReq
     * @return
     */
    @PostMapping(value = "${project.feign.tduck.mergeProject}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    String mergeProject(@RequestBody SplitAndMergeReq splitAndMergeReq);

    /**
     * 项目拆分
     *
     * @param splitAndMergeReq
     * @return
     */
    @PostMapping(value = "${project.feign.tduck.splitProject}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    String splitProject(@RequestBody SplitAndMergeReq splitAndMergeReq);


    @PostMapping(value = "${project.feign.tduck.getDictTaskInfo}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = "application/json;charset=UTF-8")
    String getDictTaskInfo(@RequestBody GetDictTaskInfoParam splitAndMergeReq);
}
