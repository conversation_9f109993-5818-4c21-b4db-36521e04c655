package com.msun.csm.feign.entity.yunying.resp;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/05/28/14:11
 */
@Data
public class YunyingProductResp {

    @ApiModelProperty ("产品id")
    private Long id;

    @ApiModelProperty ("产品名称")
    private String name;

    @ApiModelProperty ("产品全称")
    private String fullName;

    @ApiModelProperty ("产品编码")
    private String code;

    @ApiModelProperty ("产品所属部门id")
    private Long orgId;

    @ApiModelProperty ("产品经理id")
    private Long productManager;

    @ApiModelProperty ("是否为云产品 【1:是；0：否】")
    private Integer isCloudProduct;

    @ApiModelProperty ("产品类型 【1、自研软件；2、硬件；3、耗材；4、接口；5、软件服务费；6、硬件服务费；7、容灾；8、外采软件；9、云资源】")
    private Integer productCategory;

    @ApiModelProperty ("所属模块")
    private List<YunYingProductModulesResp> modules;

}
