<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjMessageRecordPeopleMapper">
    <insert id="insertBatch" parameterType="java.util.List">
        insert into csm.proj_message_record_people
        (
        id,
        message_status,
        read_user_id,
        read_time,
        tip_times,
        last_tip_time,
        creater_id,
        updater_id,
        record_id,
        account,
        message_url,
        expired_flag
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=BIGINT},
            #{item.messageStatus,jdbcType=INTEGER},
            #{item.readUserId,jdbcType=BIGINT},
            #{item.readTime,jdbcType=TIMESTAMP},
            #{item.tipTimes,jdbcType=INTEGER},
            #{item.lastTipTime,jdbcType=TIMESTAMP},
            #{item.createrId,jdbcType=BIGINT},
            #{item.updaterId,jdbcType=BIGINT},
            #{item.recordId,jdbcType=BIGINT},
            #{item.account,jdbcType=VARCHAR},
            #{item.messageUrl,jdbcType=VARCHAR},
            #{item.expiredFlag,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <update id="updateBatch" parameterType="java.util.List">
        update csm.proj_message_record_people
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="last_tip_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when message_to_id = #{item.messageToId,jdbcType=BIGINT} then
                    cast(#{item.lastTipTime,jdbcType=TIMESTAMP} as timestamp)
                </foreach>
            </trim>
            <trim prefix="tip_times = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when message_to_id = #{item.messageToId,jdbcType=BIGINT} then #{item.tipTimes,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when message_to_id = #{item.messageToId,jdbcType=BIGINT} then
                    cast(#{item.updateTime,jdbcType=TIMESTAMP} as timestamp)
                </foreach>
            </trim>
        </trim>
        where record_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.recordId,jdbcType=BIGINT}
        </foreach>
    </update>
    <select id="selectByRecordId" resultType="com.msun.csm.dao.entity.proj.ProjMessageRecordPeople">
        select tip_times, message_to_id, record_id from csm.proj_message_record_people where record_id =
        #{recordId,jdbcType=BIGINT}
    </select>
    <select id="selectByMsgRecordPpRelative" resultType="com.msun.csm.dao.entity.sys.ProjMessageRecordPpRelative"
            parameterType="com.msun.csm.model.dto.ProjMessageRecordPpRelativeDTO">
        select t.message_content,t.message_title
        ,t.message_type_id
        ,t1.create_time
        ,case when t1.tip_times > 0 then 1 else 0 end isSuccess
        ,(select d.message_type_name from csm.dict_message_type d where d.id = t.message_type_id) as message_type_name
        from csm.proj_message_record t inner join csm.proj_message_record_people t1 on t.id = t1.record_id
        <where>
            <if test="startTime != null and startTime != ''">
                and t1.create_time >= cast(#{startTime,jdbcType=VARCHAR} as TIMESTAMP)
            </if>
            <if test="endTime != null and endTime != ''">
                and t1.create_time &lt;= cast(#{endTime,jdbcType=VARCHAR} as TIMESTAMP)
            </if>
            <if test="isSuccess != null and isSuccess != ''">
                and case cast(#{isSuccess,jdbcType=VARCHAR} as INTEGER) when 1 then
                t1.tip_times > 0 else t1.tip_times &lt;= 0 end
            </if>
            <if test="messageTypeId != null and messageTypeId != ''">
                and t.message_type_id = cast(#{messageTypeId,jdbcType=VARCHAR} as INTEGER)
            </if>
        </where>
        order by t.create_time desc
    </select>

    <select id="getBriefMessageReminder" resultType="com.msun.csm.model.resp.project.BriefMessageReminder">
        select
        pmrp.id as "messageId",
        pmr.message_content as "messageContent"
        from
        csm.proj_message_record_people pmrp
        left join csm.proj_message_record pmr on pmrp.record_id = pmr.id
        left join csm.dict_message_type dmt on dmt.id = pmr.message_type_id
        where
        pmrp.is_deleted = 0
        and pmrp.message_status = 1
        and dmt.business_scene_type = 1
        and pmrp.read_user_id = #{readUserId}
    </select>

    <update id="updateMessageToRead">
        update
        csm.proj_message_record_people
        set
        message_status = 2,
        read_time = now() ,
        update_time = now(),
        updater_id = #{readUserId}
        where id in
        <foreach collection="messageIdList" item="messageId" open="(" separator=", " close=")">
            #{messageId}
        </foreach>
    </update>

    <select id="getMessageList" resultType="com.msun.csm.model.resp.project.MessageResult">
        select
        pmrp.id as "messageId",
        pmr.message_title as "messageTitle",
        pmr.message_content as "messageContent",
        pmrp.message_status as "messageStatus",
        pmrp.create_time as "sendTime",
        pmrp.message_url as "messageUrl"
        from
        csm.proj_message_record_people pmrp
        left join csm.proj_message_record pmr on pmrp.record_id = pmr.id
        left join csm.dict_message_type dmt on dmt.id = pmr.message_type_id
        where
        pmrp.is_deleted = 0
        and dmt.business_scene_type = 1
        and pmrp.read_user_id = #{readUserId}
        <if test="messageStatus == 1">
            and pmrp.message_status = 1
        </if>
        <if test="messageStatus == 2">
            and pmrp.message_status in (2,3)
        </if>
        <if test="startTime != null and startTime != ''">
            and pmrp.create_time >= cast(#{startTime,jdbcType=VARCHAR} as TIMESTAMP)
        </if>
        <if test="endTime != null and endTime != ''">
            and pmrp.create_time &lt;= cast(#{endTime,jdbcType=VARCHAR} as TIMESTAMP)
        </if>
    </select>
</mapper>
