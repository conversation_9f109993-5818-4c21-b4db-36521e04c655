<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.projform.ProjSurveyFormMapper">
    <update id="updateFormStatusData">
        update csm.proj_survey_form
        set finish_status      = #{finishStatus},
            examine_opinion    = #{examineOpinion},
            commit_finish_time = #{commitFinishTime}
        where project_info_id = #{projectInfoId}
          and finish_status not in (1, 3)
        <if test="yyProductId != null">
            and yy_product_id = #{yyProductId}
            and finish_imgs is not null
            and finish_imgs != ''
        </if>
        <if test="hospitalInfoId != null">
            and hospital_info_id = #{hospitalInfoId}
        </if>
    </update>

    <select id="selectSurveyFormByPage" resultType="com.msun.csm.model.resp.projform.ProjSurveyFormResp">
        select * from (
        select distinct dp.order_no,
                        coalesce(dp.product_name, dpvm.yy_module_name) as "productName",
                        case when coalesce(dp.product_name, dpvm.yy_module_name) like '%护理%' then true else false end as "isShowRecommendBtn",

                        psr.*,
                        ci.custom_name  AS "customName",
                        pi.project_name AS "projectName",
                        phi.hospital_name AS "hospitalName",
                        su.user_name                                   as "makeUserName",
                        sure.user_name                                   as "reviewerUserName",
                        psr.identifier_user_id as "identifierUserId",
                        identifier.user_name                                   as "identifierUserName",
                        susurvey.user_name          as "surveyUserName",
                        psreview.examine_opinion as "operationExamineOpinion",
                         case when ss.open_flag = 1 and psreview.examine_status = 0 then true else false end as "isNeedAuditorFlag",
                        case when psreview.examine_status = 1 then '通过'
                                    when psreview.examine_status = 2 then '驳回'
                                    else '待审核' end
                            as "operationExamineStatusStr",
                         psreview.examine_status as "operationExamineStatus",
                        case
                            when psr.form_source = 'cpdy' then '产品调研'
                            when psr.form_source = 'lxt' then '老系统'
                            when psr.form_source = 'xz' then '新增'
                            else '其他' end                            as "formSourceStr",
                        case
                        when psr.finish_Status = 0 then '未开始'
                        when psr.finish_Status = 1 then '制作完成'
                        when psr.finish_Status = 2 then '制作完成已驳回'
                        when psr.finish_Status = 4 then '提交调研审核'
                        when psr.finish_Status = 5 then '调研审核通过'
                        when psr.finish_Status = 6 then '调研审核驳回'
                        when psr.finish_Status = 8 then '制作完成验证通过'
                        else '其他' end                            as "finishStatusStr"
        from csm.proj_survey_form psr
            left join (
                select
                 x.*
                from
                csm.proj_business_examine_log x
                inner join (
                select business_id, max(create_time) as create_time from  csm.proj_business_examine_log x
                where is_deleted = 0 and business_type = 'form'
                    group by business_id
                ) y on x.business_id = y.business_id and x.create_time = y.create_time
                where x.is_deleted = 0  and x.business_type = 'form'
            ) psreview on psr.survey_form_id = psreview.business_id
            left join csm.proj_hospital_info phi on psr.hospital_info_id = phi.hospital_info_id and phi.is_deleted = 0
                 left join csm.dict_product dp on psr.yy_product_id = dp.yy_product_id
                 left join csm.dict_product_vs_modules dpvm on dpvm.yy_module_id = psr.yy_product_id
                 left join csm.sys_user su on psr.make_user_id = su.sys_user_id and su.is_deleted = 0
                left join csm.sys_user susurvey on psr.survey_user_id = susurvey.sys_user_id and susurvey.is_deleted = 0
                left join csm.sys_user sure on psr.reviewer_user_id = sure.sys_user_id and sure.is_deleted = 0
                left join csm.sys_user identifier on psr.identifier_user_id = identifier.sys_user_id and identifier.is_deleted = 0
                left join csm.proj_custom_info ci on psr.customer_info_id = ci.custom_info_id and ci.is_deleted = 0
                left join csm.proj_project_info pi on psr.project_info_id = pi.project_info_id and pi.is_deleted = 0
            left join (
                    select ccbdl.open_flag ,
                    ccbl.project_info_id
                    from
                    csm.config_custom_backend_limit ccbl
                    inner join csm.config_custom_backend_detail_limit ccbdl on ccbdl.project_info_id = ccbl.project_info_id
                    where
                    ccbl.is_deleted =0
                    and ccbdl.is_deleted = 0
                    and ccbdl.open_flag = 1
                    and ccbdl.open_type = 12

                ) ss on psr.project_info_id = ss.project_info_id

        where psr.is_deleted = 0
        <if test="allocateUserId != null and allocateUserId.size() != 0 ">
            and psr.make_user_id in
            <foreach collection="allocateUserId" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="projectDeliverStatusList != null and projectDeliverStatusList.size() != 0 ">
            and pi.project_deliver_status in
            <foreach collection="projectDeliverStatusList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reviewerUserIds != null and reviewerUserIds.size() != 0 ">
            and psr.reviewer_user_id in
            <foreach collection="reviewerUserIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="identifierUserIds != null and identifierUserIds.size() != 0 ">
            and psr.identifier_user_id in
            <foreach collection="identifierUserIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="finishStatusList != null and finishStatusList.size > 0">
            and  psr.finish_status in
            <foreach collection="finishStatusList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="surveyFormId != null">
            and psr.survey_form_id = #{surveyFormId}
        </if>
        <if test="projectInfoId != null">
            and psr.project_info_id = #{projectInfoId}
        </if>
        <if test="hospitalInfoId != null">
            and psr.hospital_info_id = #{hospitalInfoId}
        </if>
        <if test="yyProductId != null">
            and (psr.yy_product_id = #{yyProductId} or psr.yy_module_id = #{yyProductId})
        </if>
        <if test="yyModuleId != null">
            and psr.yy_module_id = #{yyModuleId}
        </if>
        <if test="finishStatus != null">
            and case when ss.open_flag = 1 then psr.finish_status = #{finishStatus}
            when #{finishStatus} = 0 then psr.finish_status in (0,4,5,6)
            else  psr.finish_status = #{finishStatus} end
        </if>
        <if test="onlineEssential != null">
            and psr.online_essential = #{onlineEssential}
        </if>
        <if test="customInfoId != null">
            and psr.customer_info_id = #{customInfoId}
        </if>
        <if test="formName != null and formName != ''">
            and psr.form_name like concat('%', #{formName}, '%')
        </if>
        <if test="operationExamineStatusNumber != null">
            and psreview.examine_status = #{operationExamineStatusNumber}
        </if>
        ) ased
        order by customer_info_id, project_info_id, ased.order_no, ased.update_time desc, ased.form_name
    </select>
    <select id="selectSurveyFormCount" resultType="com.msun.csm.model.resp.projform.ProjSurveyReprotFormCount">
        select count(*)                                                                              "totalCount",
               sum(case when online_essential = 1 then 1 else 0 end)                                 "preLaunchCompletionCount",
               sum(case when online_essential = 1 and finish_status not in (1, 3) then 1 else 0 end) "incompleteCount",
               sum(case when online_essential = 1 and finish_status = 2 then 1 else 0 end)           "rejectedCount"
        from (
        select distinct dp.order_no,
                        coalesce(dp.product_name, dpvm.yy_module_name) as "productName",
                        psr.*,
                        su.user_name                                   as "makeUserName",
                        case
                            when psr.form_source = 'cpdy' then '产品调研'
                            when psr.form_source = 'lxt' then '老系统'
                            when psr.form_source = 'xz' then '新增'
                            else '其他' end                            as "formSourceStr"
        from csm.proj_survey_form psr
            left join csm.proj_project_info pi on pi.project_info_id=psr.project_info_id
        left join csm.sys_user sure on psr.reviewer_user_id = sure.sys_user_id and sure.is_deleted = 0
         left join csm.dict_product dp on psr.yy_product_id = dp.yy_product_id
         left join csm.dict_product_vs_modules dpvm on dpvm.yy_module_id = psr.yy_product_id
         left join csm.sys_user su on psr.make_user_id = su.sys_user_id and su.is_deleted = 0
        left join (
            select
            x.*
            from
            csm.proj_business_examine_log x
            inner join (
            select business_id, max(create_time) as create_time from  csm.proj_business_examine_log x
            where is_deleted = 0 and business_type = 'form'
            group by business_id
            ) y on x.business_id = y.business_id and x.create_time = y.create_time
            where x.is_deleted = 0  and x.business_type = 'form'
        ) psreview on psr.survey_form_id = psreview.business_id

        left join (
            select ccbdl.open_flag ,
            ccbl.project_info_id
            from
            csm.config_custom_backend_limit ccbl
            inner join csm.config_custom_backend_detail_limit ccbdl on ccbdl.project_info_id = ccbl.project_info_id
            where
            ccbl.is_deleted =0
            and ccbdl.is_deleted = 0
            and ccbdl.open_flag = 1
            and ccbdl.open_type = 12

        ) ss on psr.project_info_id = ss.project_info_id

        where psr.is_deleted = 0
        <if test="projectDeliverStatusList != null and projectDeliverStatusList.size() != 0 ">
            and pi.project_deliver_status in
            <foreach collection="projectDeliverStatusList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="finishStatusList != null and finishStatusList.size > 0">
            and  psr.finish_status in
            <foreach collection="finishStatusList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="allocateUserId != null and allocateUserId.size() != 0 ">
            and psr.make_user_id in
            <foreach collection="allocateUserId" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reviewerUserIds != null and reviewerUserIds.size() != 0 ">
            and psr.reviewer_user_id in
            <foreach collection="reviewerUserIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="identifierUserIds != null and identifierUserIds.size() != 0 ">
            and psr.identifier_user_id in
            <foreach collection="identifierUserIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="surveyFormId != null">
            and psr.survey_form_id = #{surveyFormId}
        </if>
        <if test="projectInfoId != null">
            and psr.project_info_id = #{projectInfoId}
        </if>
        <if test="hospitalInfoId != null">
            and psr.hospital_info_id = #{hospitalInfoId}
        </if>
        <if test="yyProductId != null">
            and (psr.yy_product_id = #{yyProductId} or psr.yy_module_id = #{yyProductId})
        </if>
        <if test="yyModuleId != null">
            and psr.yy_module_id = #{yyModuleId}
        </if>
        <if test="finishStatus != null">
            and case when ss.open_flag = 1 then psr.finish_status = #{finishStatus}
            when #{finishStatus} = 0 then psr.finish_status in (0,4,5,6)
                else  psr.finish_status = #{finishStatus} end
        </if>
        <if test="onlineEssential != null">
            and psr.online_essential = #{onlineEssential}
        </if>
        <if test="customInfoId != null">
            and psr.customer_info_id = #{customInfoId}
        </if>
        <if test="formName != null and formName != ''">
            and psr.form_name like concat('%', #{formName}, '%')
        </if>
        <if test="operationExamineStatusNumber != null">
            and psreview.examine_status = #{operationExamineStatusNumber}
        </if>
        ) ased
    </select>

    <update id="updateByProjectId">
        update csm.proj_survey_form
        set project_info_id = #{newProjectId}
        where project_info_id = #{oldProjectId}
        <if test="changeProductList != null and changeProductList.size() != 0">
            and (yy_product_id in
            <foreach collection="changeProductList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            or yy_module_id in
            <foreach collection="changeProductList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>)
        </if>
    </update>

    <update id="updateByCustomInfoId">
        update csm.proj_survey_form
        set customer_info_id = #{newCustomInfoId}
        where customer_info_id = #{oldCustomInfoId}
        <if test="projectInfoIds != null and projectInfoIds.size() != 0">
            and project_info_id in
            <foreach collection="projectInfoIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="deleteByProjectAndProductIds">
        update csm.proj_survey_form
        set is_deleted  = 1,
            update_time = now()
        where project_info_id = #{projectInfoId}
          and (yy_product_id in
        <foreach collection="oldProductIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        or yy_module_id in
        <foreach collection="oldProductIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
    </update>
    <update id="udpateReprotByOldId">
        update csm.proj_survey_form
        set survey_user_id = #{oldId}
        where survey_user_id = #{newOldId}
    </update>
    <update id="udpateReprotMakeByOldId">
        update csm.proj_survey_form
        set make_user_id = #{oldId}
        where make_user_id = #{newOldId}
    </update>
    <select id="selectSurveyForm" resultType="com.msun.csm.dao.entity.proj.projform.ProjSurveyForm">
        select
        *
        from
        csm.proj_survey_form
        where is_deleted = 0
        and project_info_id = #{projectInfoId}
        and hospital_info_id = #{hospitalInfoId}
        and (
        yy_product_id in
        <foreach collection="yyProductIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        or yy_module_id in
        <foreach collection="yyProductIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        )
    </select>
</mapper>
