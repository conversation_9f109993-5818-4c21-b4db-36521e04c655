<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.proj.ProjCustomInfoMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.proj.ProjCustomInfo">
        <!--@mbg.generated-->
        <!--@Table csm.proj_custom_info-->
        <id column="custom_info_id" jdbcType="BIGINT" property="customInfoId"/>
        <result column="yy_customer_id" jdbcType="BIGINT" property="yyCustomerId"/>
        <result column="custom_name" jdbcType="VARCHAR" property="customName"/>
        <result column="custom_type" jdbcType="VARCHAR" property="customType"/>
        <result column="custom_status" jdbcType="VARCHAR" property="customStatus"/>
        <result column="custom_level" jdbcType="VARCHAR" property="customLevel"/>
        <result column="custom_project_status" jdbcType="VARCHAR" property="customProjectStatus"/>
        <result column="custom_team_id" jdbcType="BIGINT" property="customTeamId"/>
        <result column="custom_head_id" jdbcType="BIGINT" property="customHeadId"/>
        <result column="custom_dept_id" jdbcType="BIGINT" property="customDeptId"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        t.custom_info_id, t.yy_customer_id, t.custom_name, t.custom_type, t.custom_status, t.custom_level,
        t.custom_project_status, t.custom_team_id, t.custom_head_id, t.custom_dept_id, t.creater_id,
        t.create_time, t.updater_id, t.update_time, t.is_deleted,t.ip,t.port,t.telesales_flag
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from csm.proj_custom_info t
        where custom_info_id = #{customInfoId,jdbcType=BIGINT}
    </select>
    <select id="findProjCustomInfoList" resultMap="BaseResultMap">
        select * from
        (select
        <include refid="Base_Column_List"/>
        from csm.proj_custom_info t
        <where>
            and t.is_deleted =0
            <if test="dto.customName != null and dto.customName != ''">
                and custom_name like concat('%', #{dto.customName}, '%')
            </if>
            <if test="dto.customTeamIdList != null and dto.customTeamIdList.size > 0">
                and custom_team_id in
                <foreach close=")" collection="dto.customTeamIdList" item="item" open="(" separator=", ">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="dto.customDeptIdList != null and dto.customDeptIdList.size > 0">
                and custom_dept_id in
                <foreach close=")" collection="dto.customDeptIdList" item="item" open="(" separator=", ">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
        union
        select
        <include refid="Base_Column_List"/>
        from csm.proj_custom_info t
        inner join csm.proj_project_info t1 on t1.custom_info_id = t.custom_info_id
        inner join csm.proj_project_member t2
        on t2.project_member_id = #{userId,jdbcType=BIGINT} and t2.project_info_id
        = t1.project_info_id
        <where>
            and t.is_deleted =0
            and t1.is_deleted =0
            and t2.is_deleted =0
            <if test="dto.customName != null and dto.customName != ''">
                and custom_name like concat('%', #{dto.customName}, '%')
            </if>
        </where>
        <if test="dto.customTeamIdList != null and dto.customTeamIdList.size > 0">
            union
            select
            <include refid="Base_Column_List"/>
            from csm.proj_project_info t2 inner join csm.proj_custom_info t on t.custom_info_id = t2.custom_info_id
            <where>
                and t.is_deleted =0 and t2.is_deleted = 0
                <if test="dto.customName != null and dto.customName != ''">
                    and t.custom_name like concat('%', #{dto.customName}, '%')
                </if>
                and t2.project_team_id in
                <foreach close=")" collection="dto.customTeamIdList" item="item" open="(" separator=", ">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </where>
        </if>
        <if test="dto.dataRange != null and dto.dataRange.size > 0">
            union
            select
            <include refid="Base_Column_List"/>
            from csm.proj_custom_info t
            <where>
                and t.is_deleted =0
                <if test="dto.customName != null and dto.customName != ''">
                    and t.custom_name like concat('%', #{dto.customName}, '%')
                </if>
                and t.custom_dept_id in
                <foreach collection="dto.dataRange" item="item" open="(" separator=", " close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </where>
        </if>

        ) as temp
        order by update_time desc
    </select>
    <update id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        update csm.proj_custom_info
        set is_deleted = 1
        where custom_info_id = #{customInfoId,jdbcType=BIGINT}
    </update>
    <insert id="inserts" parameterType="com.msun.csm.dao.entity.proj.ProjCustomInfo">
        <!--@mbg.generated-->
        insert into csm.proj_custom_info (custom_info_id, yy_customer_id, custom_name,
        custom_type, custom_status, custom_level,
        custom_project_status, custom_team_id, custom_head_id,
        custom_dept_id, creater_id, create_time,
        updater_id, update_time, is_deleted
        )
        values (#{customInfoId,jdbcType=BIGINT}, #{yyCustomerId,jdbcType=BIGINT}, #{customName,jdbcType=VARCHAR},
        #{customType,jdbcType=VARCHAR}, #{customStatus,jdbcType=VARCHAR}, #{customLevel,jdbcType=VARCHAR},
        #{customProjectStatus,jdbcType=VARCHAR}, #{customTeamId,jdbcType=BIGINT}, #{customHeadId,jdbcType=BIGINT},
        #{customDeptId,jdbcType=BIGINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.proj.ProjCustomInfo">
        <!--@mbg.generated-->
        insert into csm.proj_custom_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customInfoId != null">
                custom_info_id,
            </if>
            <if test="yyCustomerId != null">
                yy_customer_id,
            </if>
            <if test="customName != null">
                custom_name,
            </if>
            <if test="customType != null">
                custom_type,
            </if>
            <if test="customStatus != null">
                custom_status,
            </if>
            <if test="customLevel != null">
                custom_level,
            </if>
            <if test="customProjectStatus != null">
                custom_project_status,
            </if>
            <if test="customTeamId != null">
                custom_team_id,
            </if>
            <if test="customHeadId != null">
                custom_head_id,
            </if>
            <if test="customDeptId != null">
                custom_dept_id,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customInfoId != null">
                #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyCustomerId != null">
                #{yyCustomerId,jdbcType=BIGINT},
            </if>
            <if test="customName != null">
                #{customName,jdbcType=VARCHAR},
            </if>
            <if test="customType != null">
                #{customType,jdbcType=VARCHAR},
            </if>
            <if test="customStatus != null">
                #{customStatus,jdbcType=VARCHAR},
            </if>
            <if test="customLevel != null">
                #{customLevel,jdbcType=VARCHAR},
            </if>
            <if test="customProjectStatus != null">
                #{customProjectStatus,jdbcType=VARCHAR},
            </if>
            <if test="customTeamId != null">
                #{customTeamId,jdbcType=BIGINT},
            </if>
            <if test="customHeadId != null">
                #{customHeadId,jdbcType=BIGINT},
            </if>
            <if test="customDeptId != null">
                #{customDeptId,jdbcType=BIGINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.proj.ProjCustomInfo">
        <!--@mbg.generated-->
        update csm.proj_custom_info
        <set>
            <if test="yyCustomerId != null">
                yy_customer_id = #{yyCustomerId,jdbcType=BIGINT},
            </if>
            <if test="customName != null">
                custom_name = #{customName,jdbcType=VARCHAR},
            </if>
            <if test="customType != null">
                custom_type = #{customType,jdbcType=VARCHAR},
            </if>
            <if test="customStatus != null">
                custom_status = #{customStatus,jdbcType=VARCHAR},
            </if>
            <if test="customLevel != null">
                custom_level = #{customLevel,jdbcType=VARCHAR},
            </if>
            <if test="customProjectStatus != null">
                custom_project_status = #{customProjectStatus,jdbcType=VARCHAR},
            </if>
            <if test="customTeamId != null">
                custom_team_id = #{customTeamId,jdbcType=BIGINT},
            </if>
            <if test="customHeadId != null">
                custom_head_id = #{customHeadId,jdbcType=BIGINT},
            </if>
            <if test="customDeptId != null">
                custom_dept_id = #{customDeptId,jdbcType=BIGINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </set>
        where custom_info_id = #{customInfoId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.proj.ProjCustomInfo">
        <!--@mbg.generated-->
        update csm.proj_custom_info
        set yy_customer_id = #{yyCustomerId,jdbcType=BIGINT},
        custom_name = #{customName,jdbcType=VARCHAR},
        custom_type = #{customType,jdbcType=VARCHAR},
        custom_status = #{customStatus,jdbcType=VARCHAR},
        custom_level = #{customLevel,jdbcType=VARCHAR},
        custom_project_status = #{customProjectStatus,jdbcType=VARCHAR},
        custom_team_id = #{customTeamId,jdbcType=BIGINT},
        custom_head_id = #{customHeadId,jdbcType=BIGINT},
        custom_dept_id = #{customDeptId,jdbcType=BIGINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT}
        where custom_info_id = #{customInfoId,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_custom_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="yy_customer_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then #{item.yyCustomerId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="custom_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then #{item.customName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="custom_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then #{item.customType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="custom_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then
                    #{item.customStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="custom_level = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then #{item.customLevel,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="custom_project_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then
                    #{item.customProjectStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="custom_team_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then #{item.customTeamId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="custom_head_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then #{item.customHeadId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="custom_dept_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then #{item.customDeptId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then
                    #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
                </foreach>
            </trim>
        </trim>
        where custom_info_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.customInfoId,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.proj_custom_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="yy_customer_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.yyCustomerId != null">
                        when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then
                        #{item.yyCustomerId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="custom_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.customName != null">
                        when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then
                        #{item.customName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="custom_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.customType != null">
                        when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then
                        #{item.customType,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="custom_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.customStatus != null">
                        when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then
                        #{item.customStatus,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="custom_level = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.customLevel != null">
                        when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then
                        #{item.customLevel,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="custom_project_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.customProjectStatus != null">
                        when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then
                        #{item.customProjectStatus,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="custom_team_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.customTeamId != null">
                        when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then
                        #{item.customTeamId,jdbcType=BIGINT}
                    </if>
                </foreach>
                else custom_team_id
            </trim>
            <trim prefix="custom_head_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.customHeadId != null">
                        when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then
                        #{item.customHeadId,jdbcType=BIGINT}
                    </if>
                </foreach>
                else custom_head_id
            </trim>
            <trim prefix="custom_dept_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.customDeptId != null">
                        when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then
                        #{item.customDeptId,jdbcType=BIGINT}
                    </if>
                </foreach>
                else custom_dept_id
            </trim>
            <!-- <trim prefix="creater_id = case" suffix="end,">
                 <foreach collection="list" index="index" item="item">
                     <if test="item.createrId != null">
                         when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then
                         #{item.createrId,jdbcType=BIGINT}
                     </if>
                 </foreach>
             </trim>
             <trim prefix="create_time = case" suffix="end,">
                 <foreach collection="list" index="index" item="item">
                     <if test="item.createTime != null">
                         when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then
                         #{item.createTime,jdbcType=TIMESTAMP}::timestamp(6)
                     </if>
                 </foreach>
             </trim>
             <trim prefix="updater_id = case" suffix="end,">
                 <foreach collection="list" index="index" item="item">
                     <if test="item.updaterId != null">
                         when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then
                         #{item.updaterId,jdbcType=BIGINT}
                     </if>
                 </foreach>
             </trim>
             <trim prefix="update_time = case" suffix="end,">
                 <foreach collection="list" index="index" item="item">
                     <if test="item.updateTime != null">
                         when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then
                         #{item.updateTime,jdbcType=TIMESTAMP}::timestamp(6)
                     </if>
                 </foreach>
             </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when custom_info_id = #{item.customInfoId,jdbcType=BIGINT} then
                        #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>-->
        </trim>
        where custom_info_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.customInfoId,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.proj_custom_info
        (custom_info_id, yy_customer_id, custom_name, custom_type, custom_status, custom_level,
        custom_project_status, custom_team_id, custom_head_id, custom_dept_id, creater_id,
        create_time, updater_id, update_time, is_deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.customInfoId,jdbcType=BIGINT}, #{item.yyCustomerId,jdbcType=BIGINT},
            #{item.customName,jdbcType=VARCHAR},
            #{item.customType,jdbcType=VARCHAR}, #{item.customStatus,jdbcType=VARCHAR},
            #{item.customLevel,jdbcType=VARCHAR},
            #{item.customProjectStatus,jdbcType=VARCHAR}, #{item.customTeamId,jdbcType=BIGINT},
            #{item.customHeadId,jdbcType=BIGINT}, #{item.customDeptId,jdbcType=BIGINT},
            #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.isDeleted,jdbcType=SMALLINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.dao.entity.proj.ProjCustomInfo">
        <!--@mbg.generated-->
        insert into csm.proj_custom_info
        (custom_info_id, yy_customer_id, custom_name, custom_type, custom_status, custom_level,
        custom_project_status, custom_team_id, custom_head_id, custom_dept_id, creater_id,
        create_time, updater_id, update_time, is_deleted)
        values
        (#{customInfoId,jdbcType=BIGINT}, #{yyCustomerId,jdbcType=BIGINT}, #{customName,jdbcType=VARCHAR},
        #{customType,jdbcType=VARCHAR}, #{customStatus,jdbcType=VARCHAR}, #{customLevel,jdbcType=VARCHAR},
        #{customProjectStatus,jdbcType=VARCHAR}, #{customTeamId,jdbcType=BIGINT}, #{customHeadId,jdbcType=BIGINT},
        #{customDeptId,jdbcType=BIGINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT}
        )
        on duplicate key update
        custom_info_id = #{customInfoId,jdbcType=BIGINT},
        yy_customer_id = #{yyCustomerId,jdbcType=BIGINT},
        custom_name = #{customName,jdbcType=VARCHAR},
        custom_type = #{customType,jdbcType=VARCHAR},
        custom_status = #{customStatus,jdbcType=VARCHAR},
        custom_level = #{customLevel,jdbcType=VARCHAR},
        custom_project_status = #{customProjectStatus,jdbcType=VARCHAR},
        custom_team_id = #{customTeamId,jdbcType=BIGINT},
        custom_head_id = #{customHeadId,jdbcType=BIGINT},
        custom_dept_id = #{customDeptId,jdbcType=BIGINT},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.proj.ProjCustomInfo">
        <!--@mbg.generated-->
        insert into csm.proj_custom_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customInfoId != null">
                custom_info_id,
            </if>
            <if test="yyCustomerId != null">
                yy_customer_id,
            </if>
            <if test="customName != null">
                custom_name,
            </if>
            <if test="customType != null">
                custom_type,
            </if>
            <if test="customStatus != null">
                custom_status,
            </if>
            <if test="customLevel != null">
                custom_level,
            </if>
            <if test="customProjectStatus != null">
                custom_project_status,
            </if>
            <if test="customTeamId != null">
                custom_team_id,
            </if>
            <if test="customHeadId != null">
                custom_head_id,
            </if>
            <if test="customDeptId != null">
                custom_dept_id,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customInfoId != null">
                #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyCustomerId != null">
                #{yyCustomerId,jdbcType=BIGINT},
            </if>
            <if test="customName != null">
                #{customName,jdbcType=VARCHAR},
            </if>
            <if test="customType != null">
                #{customType,jdbcType=VARCHAR},
            </if>
            <if test="customStatus != null">
                #{customStatus,jdbcType=VARCHAR},
            </if>
            <if test="customLevel != null">
                #{customLevel,jdbcType=VARCHAR},
            </if>
            <if test="customProjectStatus != null">
                #{customProjectStatus,jdbcType=VARCHAR},
            </if>
            <if test="customTeamId != null">
                #{customTeamId,jdbcType=BIGINT},
            </if>
            <if test="customHeadId != null">
                #{customHeadId,jdbcType=BIGINT},
            </if>
            <if test="customDeptId != null">
                #{customDeptId,jdbcType=BIGINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="customInfoId != null">
                custom_info_id = #{customInfoId,jdbcType=BIGINT},
            </if>
            <if test="yyCustomerId != null">
                yy_customer_id = #{yyCustomerId,jdbcType=BIGINT},
            </if>
            <if test="customName != null">
                custom_name = #{customName,jdbcType=VARCHAR},
            </if>
            <if test="customType != null">
                custom_type = #{customType,jdbcType=VARCHAR},
            </if>
            <if test="customStatus != null">
                custom_status = #{customStatus,jdbcType=VARCHAR},
            </if>
            <if test="customLevel != null">
                custom_level = #{customLevel,jdbcType=VARCHAR},
            </if>
            <if test="customProjectStatus != null">
                custom_project_status = #{customProjectStatus,jdbcType=VARCHAR},
            </if>
            <if test="customTeamId != null">
                custom_team_id = #{customTeamId,jdbcType=BIGINT},
            </if>
            <if test="customHeadId != null">
                custom_head_id = #{customHeadId,jdbcType=BIGINT},
            </if>
            <if test="customDeptId != null">
                custom_dept_id = #{customDeptId,jdbcType=BIGINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>

    <select id="selectVOById" resultType="com.msun.csm.model.vo.ProjCustomInfoVO">
        select c.*,
               ci.province_id,
               ci.city_id,
               ci.town_id,
               ci.custom_bed_count,
               ci.custom_out_patient_count,
               ci.custom_annual_income,
               ci.hospital_branch_num,
               ci.region_custom_bed_count,
               ci.region_custom_out_patient_count,
               ci.region_custom_annual_income,
               ci.township_hospital_num
        from csm.proj_custom_info c
                 inner join csm.proj_custom_detail_info ci on c.custom_info_id = ci.custom_info_id
        where c.is_deleted = 0
          and ci.is_deleted = 0
          and c.custom_info_id = #{customInfoId,jdbcType=BIGINT}
          and ci.custom_info_id = #{customInfoId,jdbcType=BIGINT}
    </select>

    <select id="selectByYyCustomerId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from csm.proj_custom_info t
        where yy_customer_id = #{yyPartaId,jdbcType=BIGINT}
        and is_deleted = 0
    </select>
    <select id="getCustomerList" resultType="com.msun.csm.model.imsp.ProjCustomInfoResp">
        select distinct
        ci.yy_customer_id,
        ci.custom_name,
        ci.custom_team_id "deptId",
        sd1.dept_name,
        ci.custom_head_id "userId",
        u.user_name
        from csm.proj_custom_info ci
        inner join csm.proj_project_info p on ci.custom_info_id = p.custom_info_id
        inner join csm.sys_dept sd1 on ci.custom_team_id = sd1.dept_yunying_id
        inner join csm.sys_user u on ci.custom_head_id::text = u.user_yunying_id
        where dept_category = 'custService'
        <if test="customerId != null and customerId != 0">
            and ci.yy_customer_id = #{customerId}
        </if>
        <if test="customerName != null and customerName != ''">
            and ci.custom_name like concat('%',#{customerName},'%')
        </if>
        <if test="deptId != null and deptId != 0">
            and ci.custom_team_id = #{deptId}
        </if>
        <if test="deptName != null and deptName != ''">
            and sd1.dept_name like concat('%',#{deptName},'%')
        </if>
        <if test="userName != null and userName != ''">
            and u.user_name like concat('%',#{userName},'%')
        </if>
    </select>

    <select id="selectByKeyword" resultType="com.msun.csm.common.model.BaseIdNameResp">
        select custom_info_id "id",
        custom_name "name"
        from csm.proj_custom_info t
        where is_deleted = 0
        <if test="keyword != null">
            and t.custom_name like concat('%', #{keyword,jdbcType=VARCHAR}, '%')
        </if>
        order by t.update_time desc
    </select>

    <select id="selectByCustomInfoIds" resultMap="BaseResultMap">
        select *
        from csm.proj_custom_info t
        where is_deleted = 0
        and t.custom_info_id in
        <foreach collection="customInfoIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>
    <select id="getNotOnlineCustomer">
        select phi.custom_info_id,pci.custom_name  ,phi.hospital_info_id ,phi.hospital_name,phi.cloud_hospital_id hospital_id,phi.org_id
        from csm.proj_hospital_info phi
         inner join csm.proj_custom_info pci on phi.custom_info_id = pci.custom_info_id and pci.is_deleted =0
        where phi.is_deleted  = 0
          <if test= "isOnline = null or isOnline != 1">
              and phi.hospital_open_status in (0,2)
          </if>
          and phi.cloud_hospital_id is not null

          <if test="listHos != null and listHos.size() > 0">
              and
              <foreach close=")" collection="listHos" item="item" open="(" separator="   or  ">
                 ( phi.cloud_hospital_id = #{item.hospitalId,jdbcType=BIGINT}
                  and phi.org_id = #{item.hisOrgId} )
              </foreach>
          </if>

        <if test="customInfoId != null">
            and phi.custom_info_id = #{customInfoId}
        </if>

    </select>
</mapper>
