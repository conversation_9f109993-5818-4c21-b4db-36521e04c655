<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.msun.csm.dao.mapper.oldimsp.ImspProjectMapper">
    <select id="selectTransProjects" resultType="com.msun.csm.dao.entity.oldimsp.ImspProject">
        select *
        from platform.project
        where project_name not like '%暂不显示%'
          and project_name not like '%测试%'
          and project_name not like '%调研%'
          and id not in (select distinct (old_project_info_id) from csm.tmp_project_new_vs_old)
          and id in (SELECT project_id from comm.customer_info where project_id is not null)
        <if test="projectIds != null and projectIds.size() != 0">
            and id in
            <foreach collection="projectIds" open="(" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        order by id desc
        <if test="projectIds == null or projectIds.size() == 0">
            <if test="limitNum != null">
                limit #{limitNum}
            </if>
        </if>
    </select>

    <select id="selectOrderByProjectId" resultType="com.msun.csm.dao.entity.oldimsp.TbWorkOrder">
        select wo.*
        from platform.tb_work_order wo
                 left join platform.tr_project_work_order pwo on wo.id = pwo.wo_id
        where pwo.project_id = #{projectId}
          and wo.type != -1
    </select>

    <select id="selectContractByOrderId" resultType="com.msun.csm.dao.entity.oldimsp.TbContract">
        select tc.*
        from platform.tb_contract tc
                 left join platform.tr_contract_work_order tco on tc.id = tco.cont_id
        where tco.wo_id = #{orderId}
    </select>

    <select id="selectOrderProductsByOrderId" resultType="com.msun.csm.dao.entity.oldimsp.TbWorkOrderProduct">
        select *
        from platform.tb_work_order_product
        where jf_wo_id = #{orderId}
    </select>

    <select id="selectCustomerById" resultType="com.msun.csm.dao.entity.oldimsp.Customer">
        select *
        from platform.customer
        where id = #{customerId}
    </select>

    <select id="selectAddHospitals" resultType="com.msun.csm.dao.entity.oldimsp.OldCustomerInfo">
        select *
        from comm.customer_info
        where project_id = #{projectId}
          and customer_id = #{customerId}
          and customer_name not in
              (select distinct hospital_name from csm.proj_hospital_info where custom_info_id = #{customInfoId})
        order by customer_name asc, org_id asc
    </select>

    <select id="selectNewCustomInfoByYYId" resultType="com.msun.csm.dao.entity.proj.ProjCustomInfo">
        select *
        from csm.proj_custom_info
        where yy_customer_id = #{customerYunyingId}
          and is_deleted = 0
        limit 1
    </select>

    <select id="selectNewContract" resultType="com.msun.csm.dao.entity.proj.ProjContractInfo">
        select *
        from csm.proj_contract_info
        where contract_no = #{conName}
          and yy_contract_id = #{conId}
          and is_deleted = 0
    </select>

    <select id="selectSysUserListByOldProject" resultType="com.msun.csm.dao.entity.SysUser">
        select *
        from csm.sys_user
        where is_deleted = 0
          and user_yunying_id in
              (select distinct cast(user_yunying_id as varchar)
               from platform.sys_user
               where status = 'ENABLE'
                 and user_id in (select distinct user_id
                                 from platform.user_project_relation
                                 where project_id = #{projectId}))
    </select>

    <select id="selectStageDetailByOldProject" resultType="com.msun.csm.model.resp.oldimsp.OldProjectStageDetail">
        SELECT d.id               as detail_id,
               d.pid              as main_id,
               d.title_name       as detail_name,
               ds.id              as detail_status_id,
               ds.project_status  as status,
               ds.finish_end_time as finish_time,
               #{projectId}       as project_id,
               m.modelvalue       as sort,
               pg.enter_time      as enter_time,
               pg.on_line_time    as online_time
        FROM platform.frame_project_flow_detail d
                 LEFT JOIN platform.frame_project_flow_detail_status ds on ds.pid = d.id
                 LEFT JOIN platform.frame_project_flow_main m on d.pid = m.id
                 LEFT JOIN platform.project_group pg on pg.project_id = #{projectId}
        WHERE d.title_name IN
              ('项目管理团队核实(调研)', '确认达到入驻条件', '项目管理团队核实(准备)', '确认上线', '申请验收',
               '项目验收')
          AND (m.project_id = #{projectId} or ds.project_id = #{projectId})
          and m.invalid_flag = '0'
          AND d.invalid_flag = '0'
        order by sort
    </select>

    <select id="selectByOldProjectId" resultType="com.msun.csm.dao.entity.oldimsp.OldCustomerInfo">
        select *
        from comm.customer_info
        where project_id = #{projectId}
        order by create_time desc
        limit 1
    </select>

    <select id="selectOpenedProductByOldProject" resultType="java.lang.Long">
        select p.product_yunying_id
        from platform.project_product_relation ppr
                 left join platform.product p on p.id = ppr.product_id
        where project_id = #{projectId}
          and ppr.product_status = '1'
    </select>

    <select id="selectSysDpet" resultType="com.msun.csm.dao.entity.SysDept">
        select *
        from csm.sys_dept
        where dept_yunying_id = (select yunying_id
                                 from platform.sys_dept
                                 where dept_id = (select dept_id
                                                  from platform.project
                                                  where id = #{oldProjectId}))
    </select>

    <select id="getCloudProductByContractNum" resultType="com.msun.csm.dao.entity.oldimsp.TbWorkOrderProduct">
        select *
        from platform.tb_work_order_product
        where yy_wo_id in (select yy_wid
                           from platform.tb_work_order
                           where type = 1
                             and id in (select wo_id
                                        from platform.tr_contract_work_order
                                        where cont_id = (select id
                                                         from platform.tb_contract
                                                         where tb_contract.con_id = #{contractNum})))
          and product_name like '%升级云健康%'
          and flag = 0
    </select>

    <select id="selectArrangeProductByProjectIds" resultType="java.lang.Long">
        select pva.order_product_id
        from csm.dict_product_vs_arrange pva
        where pva.arrange_product_id in
        <foreach collection="openedProductList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectYYProductByOldProject" resultType="java.lang.Long">
        select distinct p.product_yunying_id
        from platform.project_product_relation ppr
                 left join platform.product p on p.id = ppr.product_id
        where ppr.project_id = #{projectId}
          and p.product_yunying_id > 0
    </select>

    <select id="selectContractCustomInfos" resultType="com.msun.csm.dao.entity.proj.ProjCustomInfo">
        select *
        from proj_custom_info
        where yy_customer_id in (select distinct yy_contract_custom_id
                                 from proj_contract_info
                                 where yy_contract_custom_id not in
                                       (select distinct yy_parta_id from proj_contract_custom_info))
    </select>

    <select id="selectContractByCustomId" resultType="com.msun.csm.dao.entity.proj.ProjContractInfo">
        select *
        from proj_contract_info
        where yy_contract_custom_id = #{yyContractCustomId}
        order by create_time asc
        limit 1
    </select>


    <select id="selectEmptyGeographic" resultType="com.msun.csm.dao.entity.proj.ProjHospitalInfo">
        select *
        from csm.proj_hospital_info
        where is_deleted = 0
          and (province_id = -1 or city_id = -1 or town_id = -1)
          and cloud_hospital_id is not null
    </select>

    <select id="selectCustomerInfoByHospitalId" resultType="com.msun.csm.dao.entity.oldimsp.OldCustomerInfo">
        select *
        from comm.customer_info
        where hospital_id = #{hospitalId}
          and project_id is not null
        order by province_id asc
        limit 1
    </select>

    <select id="selectOldCustomerById" resultType="com.msun.csm.dao.entity.oldimsp.OldCustomer">
        select *
        from platform.customer
        where id = #{customerId}
    </select>

    <select id="selectErrorCommCustomer" resultType="com.msun.csm.dao.entity.oldimsp.OldCustomerInfo">
        select *
        from comm.customer_info
        where (csm_hospital_info_id is null
            or csm_hospital_info_id not in
               (select hospital_info_id
                from csm.proj_hospital_info))
          and customer_name is not null
        order by customer_name
    </select>

    <select id="selectCsmHospitalByName" resultType="com.msun.csm.dao.entity.proj.ProjHospitalInfo">
        select *
        from csm.proj_hospital_info
        where hospital_name = #{hospitalName}
        order by org_id
    </select>

    <update id="updateOldCustomerInfo">
        update comm.customer_info
        set csm_hospital_info_id = #{csmHospitalInfoId}
        <if test="orgId != null">
            , org_id = #{orgId}
        </if>
        <if test="hospitalId != null">
            , hospital_id = #{hospitalId}
        </if>
        <if test="productNetwork != null">
            , product_network = #{productNetwork}
        </if>
        <if test="preProductNetwork != null">
            , pre_product_network = #{preProductNetwork}
        </if>
        <if test="host != null">
            , host = #{host}
        </if>
        <if test="preHost != null">
            , pre_host = #{preHost}
        </if>
        where id = #{id}
    </update>

    <select id="selectCustomerByYYId" resultType="com.msun.csm.dao.entity.oldimsp.Customer">
        select *
        from platform.customer
        where customer_yunying_id = #{yyCustomerId}
    </select>
    <select id="selectUserByNewUserId" resultType="com.msun.csm.model.imsp.SysLoginUser">
        select su.* from platform.sys_user su
        left join csm.sys_user sun on su.user_yunying_id = sun.user_yunying_id::int8
        where sun.sys_user_id  = #{createrId}

    </select>
</mapper>
