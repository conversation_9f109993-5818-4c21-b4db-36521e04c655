<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.config.ConfigAgentScenarioDictMapper">

    <!-- 查询AI智能体场景配置 -->
    <select id="getAgentScenarioConfig" resultType="com.msun.csm.model.resp.DictAgentChatScenarioConfigResp">
        SELECT
            dasc.agent_scenario_config_id,
            dasc.agent_code,
            dasc.scenario_code,
            dasc.scenario_desc,
            dasc.scenario_prompt,
            dasc.create_time,
            dasc.update_time,
            dac.agent_chat_id,
            dac.agent_name,
            dac.agent_address,
            dac.agent_address_produce,
            dac.agent_key
        FROM csm.dict_agent_scenario_config dasc
        INNER JOIN csm.dict_agent_chat dac ON dasc.agent_code = dac.agent_code
        WHERE dasc.is_deleted = 0
          AND dac.is_deleted = 0
        <if test="req != null and req.scenarioCode != null and req.scenarioCode != ''">
            AND dasc.scenario_code LIKE CONCAT('%', #{req.scenarioCode}, '%')
        </if>
        <if test="req != null and req.scenarioDesc != null and req.scenarioDesc != ''">
            AND dasc.scenario_desc LIKE CONCAT('%', #{req.scenarioDesc}, '%')
        </if>
        <if test="req != null and req.scenarioPrompt != null and req.scenarioPrompt != ''">
            AND dasc.scenario_prompt LIKE CONCAT('%', #{req.scenarioPrompt}, '%')
        </if>
        <if test="req != null and req.agentCode != null and req.agentCode != ''">
            AND dasc.agent_code LIKE CONCAT('%', #{req.agentCode}, '%')
        </if>
        <if test="req != null and req.agentName != null and req.agentName != ''">
            AND dac.agent_name LIKE CONCAT('%', #{req.agentName}, '%')
        </if>
        ORDER BY dasc.agent_code, dasc.scenario_code
    </select>

    <!-- 删除AI智能体场景配置（逻辑删除） -->
    <update id="deleteAgentScenarioConfig">
        UPDATE csm.dict_agent_scenario_config
        SET is_deleted = 1,
            update_time = NOW()
        WHERE agent_scenario_config_id = #{agentScenarioConfigId}
          AND is_deleted = 0
    </update>

</mapper>
