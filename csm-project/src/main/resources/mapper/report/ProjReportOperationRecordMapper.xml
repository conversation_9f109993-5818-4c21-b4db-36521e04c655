<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.report.ProjReportOperationRecordMapper">

    <select id="selectListById"
            resultType="com.msun.csm.model.req.projreport.statis.ProjReportOperationRecordSelectReq">
        select
            a.report_operation_record_id,
            a.statistical_report_main_id,
            a.oper_status,
            b.report_statis_status_name as "operStatusName",
            a.oper_content as "operateContent",
            a.oper_title as "logTitle",
            a.creater_id,
            c.user_name as "operateUserName",
            a.create_time as "operateTime",
            c.phone as "operateUserPhone"
        from csm.proj_report_operation_record a
        left join csm.dict_report_statis_status b on a.oper_status = b.report_statis_status_id
        left join csm.sys_user c on a.creater_id = c.sys_user_id
        where
            a.is_deleted = 0
            and a.statistical_report_main_id = #{statisticalReportMainId}
        order by a.create_time
    </select>
</mapper>
