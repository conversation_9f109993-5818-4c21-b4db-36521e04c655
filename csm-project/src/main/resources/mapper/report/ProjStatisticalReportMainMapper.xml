<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.report.ProjStatisticalReportMainMapper">

    <select id="findReportPage"
            resultType="com.msun.csm.model.resp.statis.ProjStatisticalReportMainSelectResp">
        SELECT
        a.statistical_report_main_id AS "statisticalReportMainId",
        a.custom_info_id AS "customInfoId",
        ci.custom_name || (case when ci.telesales_flag = 1 then '(电销)'
        when pi.project_type is not null and pi.project_type = 1 then '(单体)'
        when pi.project_type is not null and pi.project_type = 1 then '(区域)' else '' end )
        AS "customName",
        a.project_info_id AS "projectInfoId",
        a.hospital_info_id as "hospitalInfoId",
        pi.project_name AS "projectName",
        a.report_name AS "reportName",
        b.statistical_calibration_names as "statisticalCalibrationNames",
        b.calibration_id_first_four as "calibrationIdFirstFour",
        a.report_style AS "reportStyle",
        a.production_method_id ,
        c.production_method_name ,
        a.report_status ,
        a.hospital_dept_id,
        a.hospital_dept_name ,
        a.report_purpose_id ,
        a.report_purpose_name ,
        a.report_frequency_id ,
        a.report_frequency_name AS "reportFrequencyName",
        a.online_flag ,
        a.remarks,
        a.survey_user_id,
        susurvey.user_name as "surveyUserName",
        a.survey_time ,
        a.audit_user_id ,
        suaudit.user_name as "auditUserName",
        a.audit_time ,
        a.allocate_user_id ,
        suallocate.user_name as "allocateUserName",
        a.allocate_time,
        to_char(a.plan_finish_time, 'yyyy-mm-dd') "planFinishTime",
        a.finish_user_id,
        sufinish.user_name as "finishUserName",
        a.finish_time,
        a.creater_id ,
        su.user_name as "createrName",
        a.create_time,
        a.report_main_id as "reportMainId",
        a.report_main_name as "reportMainName",
        phi.hospital_name as "hospitalName",
        a.report_targets,
        a.report_targets_codes as "reportTargetsCodeSplic",
        a.remarks as "operContent",
        a.mount_path,
        a.operation_statistics_report_id,
        a.operation_product_id,
        products.name as "operationProductName"
        FROM
        csm.proj_statistical_report_main a
            left join csm.sys_user suallocate on a.allocate_user_id = suallocate.sys_user_id
        left join csm.proj_hospital_info phi on a.hospital_info_id = phi.hospital_info_id
        LEFT JOIN (
        select ss.statistical_report_main_id,
        ss.statistical_calibration_names,
        case when ss.nameno > 4
        then ss.calibration_id_first_four || '...'
        else ss.calibration_id_first_four
        end as calibration_id_first_four
        from
        (
        select statistical_report_main_id,
        array_to_string(
        (SELECT array_agg(ord||'.'||x )
        FROM unnest(string_to_array(string_agg(statistical_calibration_name, '=='), '==')) WITH
        ORDINALITY arr(x, ord)
        ),
        '=='
        ) AS statistical_calibration_names,
        array_to_string(
        (SELECT array_agg(ord||'.'||x )
        FROM unnest(string_to_array(string_agg(statistical_calibration_name, '=='), '==')) WITH
        ORDINALITY arr(x, ord)
        WHERE 4 >= arr.ord),
        '=='
        ) AS calibration_id_first_four,
        count(*) as nameno
        FROM
        csm.proj_statistical_calibration
        where is_deleted=0
        GROUP BY
        statistical_report_main_id
        ) ss

        ) b ON a.statistical_report_main_id = b.statistical_report_main_id
        left join csm.config_production_method c on a.production_method_id = c.production_method_id and c.is_deleted = 0
        left join csm.sys_user su on a.creater_id = su.sys_user_id
        left join csm.sys_user sufinish on a.finish_user_id = sufinish.sys_user_id
        left join csm.sys_user suaudit on a.audit_user_id = suaudit.sys_user_id
        left join csm.sys_user susurvey on a.survey_user_id = susurvey.sys_user_id
        left join csm.proj_custom_info ci on a.custom_info_id = ci.custom_info_id and ci.is_deleted = 0
        left join csm.proj_project_info pi on a.project_info_id = pi.project_info_id and pi.is_deleted = 0
        left join
        (
            select coalesce(dpm.yy_module_id, dp.yy_product_id) as id,
            case when dpm.yy_module_name is not null and dpm.yy_module_name != '' then
            concat(dp.product_name, '-', dpm.yy_module_name)  else dp.product_name end
            as name
            from csm.dict_product dp
            left join csm.dict_product_vs_modules dpm
            on dp.yy_product_id = dpm.yy_product_id and dpm.need_survey = 1
            where dp.is_deleted = 0
        ) products on a.operation_product_id = products.id

        where a.is_deleted = 0
        <if test="belongingToTheBackendFlag != null and belongingToTheBackendFlag == 1 ">
            and a.project_info_id in (SELECT project_info_id FROM csm.config_custom_backend_limit x
            WHERE open_flag =1 and is_deleted =0)
        </if>
        <if test="productionMethodId != null">
            and a.production_method_id = #{productionMethodId}
        </if>
        <if test="reportMainId !=null">
            and a.statistical_report_main_id = #{reportMainId}
        </if>
        <if test="projectInfoId != null">
            and a.project_info_id = #{projectInfoId}
        </if>
        <if test="customInfoId != null">
            and a.custom_info_id = #{customInfoId}
        </if>
        <if test="hospitalInfoId != null">
            and a.hospital_info_id = #{hospitalInfoId}
        </if>
        <if test="hospitalInfoIds != null">
            and a.hospital_info_id in
            <foreach collection="hospitalInfoIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reportNameORcalibration != null and reportNameORcalibration != ''">
            and ( b.statistical_calibration_names like '%' || #{reportNameORcalibration} || '%'
                or a.report_name like '%' || #{reportNameORcalibration} || '%'
            )
        </if>
        <if test="adjudicationFlag != null and adjudicationFlag == 1 ">
            and a.report_status != 1
        </if>
        <if test="onlineFlag != null and onlineFlag == 1 ">
            and a.online_flag = 1
        </if>
        <if test="reportAdjudicationStatus != null and reportAdjudicationStatus == 1 ">
            and a.report_status in (1, 11, 12)
        </if>
        <if test="reportAdjudicationPassStatus != null and reportAdjudicationPassStatus == 1 ">
            and a.report_status in (1,11,12)
        </if>
        <if test="reportStatus != null and reportStatus.size() != 0 ">
            and a.report_status in
            <foreach collection="reportStatus" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="reportAllocateStatus != null and reportAllocateStatus == 1 ">
            and a.allocate_user_id is not null
        </if>
        <if test="allocateUserId != null and allocateUserId.size() != 0 ">
            and  a.allocate_user_id in
            <foreach collection="allocateUserId" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="auditUserIds != null and auditUserIds.size() != 0 ">
            and  a.audit_user_id in
            <foreach collection="auditUserIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="reportNotFinishStatus != null and reportNotFinishStatus == 1 ">
            and a.report_status != 31
        </if>
        <if test="adjudicationFlag != null and adjudicationFlag == 1 ">
            <if test="orderType != null and orderType == 'desc'">
                order by a.audit_time desc nulls last
            </if>
            <if test="orderType != null and orderType == 'asc'">
                order by a.audit_time
            </if>
            <if test="orderType == null or orderType == ''">
                order by a.report_status,a.audit_time desc
            </if>
        </if>
        <if test="adjudicationFlag != null and adjudicationFlag == 0 ">
            order by a.create_time desc
        </if>

    </select>
    <select id="selectListByProjectIdAndStatus"
            resultType="com.msun.csm.dao.entity.report.statis.ProjStatisticalReportMainEntity">
        select *
        from csm.proj_statistical_report_main
        where project_info_id = #{projectInfoId}
        <if test="reportStatus != null and reportStatus == 13">
            and #{reportStatus} > report_status
        </if>
        <if test="reportStatus != null and reportStatus == 31">
            and (
            (report_status != 31 and (production_method_id not in (3,4) or production_method_id is null))
                or (report_status != 13 and report_status != 31 and production_method_id in (3,4) )
            )
        </if>
        and is_deleted = 0
        and online_flag = 1

    </select>
    <select id="getProductList" resultType="com.msun.csm.model.resp.statis.ProjProductResp">
        select phi.cloud_hospital_id "hospitalId",
               phi.org_id "orgId",
               x.msun_health_module_code "ptProductCode"
        from  csm.proj_hospital_info phi
                  inner join csm.proj_project_info ppr on phi.custom_info_id = ppr.custom_info_id
                  inner join csm.proj_order_product pop on ppr.project_info_id = pop.project_info_id
                  inner join csm.dict_product_vs_empower x on x.order_product_id = pop.yy_order_product_id
        where cloud_hospital_id = #{hospitalId}
        group by phi.cloud_hospital_id ,
                 phi.org_id ,
                 x.msun_health_module_code
    </select>
    <select id="selectByOperationStatisticsReportId"
            resultType="com.msun.csm.dao.entity.report.statis.ProjStatisticalReportMainEntity"
            parameterType="java.lang.String">
        select *
        from csm.proj_statistical_report_main
        where operation_statistics_report_id = #{operationStatisticsReportId}
          and is_deleted = 0
            limit 1
    </select>
</mapper>
