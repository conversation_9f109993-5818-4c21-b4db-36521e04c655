-- AI检测图片配置初始化数据
-- 插入智能体配置数据

-- 清理现有数据（可选）
-- DELETE FROM csm.dict_agent_chat WHERE agent_code LIKE 'AI_IMAGE_%';
-- DELETE FROM csm.dict_agent_scenario_config WHERE scenario_code LIKE 'IMAGE_%';

-- 插入智能体基础配置
INSERT INTO csm.dict_agent_chat (
    agent_chat_id, 
    agent_code, 
    agent_name, 
    agent_address, 
    agent_address_produce, 
    agent_key, 
    is_deleted, 
    creater_id, 
    create_time, 
    updater_id, 
    update_time
) VALUES 
(1001, 'AI_IMAGE_DETECTION', 'AI图片检测智能体', 'http://test-ai-api.example.com/detect', 'http://prod-ai-api.example.com/detect', 'test_key_123456', 0, 1, NOW(), 1, NOW()),
(1002, 'AI_IMAGE_ANALYSIS', 'AI图片分析智能体', 'http://test-ai-api.example.com/analysis', 'http://prod-ai-api.example.com/analysis', 'test_key_789012', 0, 1, NOW(), 1, NOW()),
(1003, 'AI_IMAGE_CLASSIFY', 'AI图片分类智能体', 'http://test-ai-api.example.com/classify', 'http://prod-ai-api.example.com/classify', 'test_key_345678', 0, 1, NOW(), 1, NOW());

-- 插入场景配置
INSERT INTO csm.dict_agent_scenario_config (
    agent_scenario_config_id,
    agent_code,
    scenario_code,
    scenario_desc,
    scenario_prompt,
    is_deleted,
    creater_id,
    create_time,
    updater_id,
    update_time
) VALUES 
(2001, 'AI_IMAGE_DETECTION', 'IMAGE_DETECTION', '图片检测场景', '请分析这张图片中的内容，识别其中的物体、人物或场景，并提供详细的描述。', 0, 1, NOW(), 1, NOW()),
(2002, 'AI_IMAGE_ANALYSIS', 'IMAGE_ANALYSIS', '图片分析场景', '请对这张图片进行深度分析，包括色彩、构图、风格等方面的专业评价。', 0, 1, NOW(), 1, NOW()),
(2003, 'AI_IMAGE_CLASSIFY', 'IMAGE_CLASSIFY', '图片分类场景', '请对这张图片进行分类，确定它属于哪个类别，并说明分类的依据。', 0, 1, NOW(), 1, NOW());

-- 查询验证数据
SELECT 
    dac.agent_chat_id,
    dac.agent_code,
    dac.agent_name,
    dac.agent_address,
    dasc.scenario_code,
    dasc.scenario_desc
FROM csm.dict_agent_chat dac
LEFT JOIN csm.dict_agent_scenario_config dasc ON dac.agent_code = dasc.agent_code
WHERE dac.is_deleted = 0 AND (dasc.is_deleted = 0 OR dasc.is_deleted IS NULL)
ORDER BY dac.agent_code;
