package com.msun.csm.model.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.msun.csm.common.model.dto.BasePageDTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 系统文件(SysFile)实体类
 *
 * <AUTHOR>
 * @since 2024-05-14 15:31:53
 */

@Data
public class SysFilePageDTO extends BasePageDTO {

    @ApiModelProperty("主键id")
    @TableId(type = IdType.INPUT)
    private Long sysFileId;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("文件code")
    private String fileCode;

    @ApiModelProperty("文件地址")
    private String fileUrl;
    @ApiModelProperty("文件code名称")
    private String fileCodeName;

    @ApiModelProperty("文件描述")
    private String fileUploadDesc;

    @ApiModelProperty("文件上传版本")
    private String version;
    @ApiModelProperty("文件版本更新说明")
    private String versionDesc;
}
