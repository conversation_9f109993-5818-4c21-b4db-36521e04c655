package com.msun.csm.model.convert;

import org.mapstruct.Mapper;

import com.msun.csm.common.model.convert.Dto2PoBaseConvert;
import com.msun.csm.common.model.convert.Dto2VoBaseConvert;
import com.msun.csm.common.model.convert.Vo2PoBaseConvert;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.model.dto.user.SysUserDTO;
import com.msun.csm.model.vo.user.SysUserVO;

/**
 * @classDesc: 人员
 * @author: pengxinting
 * @date: 2023/7/26 15:57
 * @copyright 众阳健康
 */
@Mapper (componentModel = "spring")
public interface SysUserConvert extends Dto2PoBaseConvert<SysUserDTO, SysUser>,
        Dto2VoBaseConvert<SysUserDTO, SysUserVO>, Vo2PoBaseConvert<SysUserVO, SysUser> {

}
