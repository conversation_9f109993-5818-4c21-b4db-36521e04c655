package com.msun.csm.model.vo.menu;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/23/19:51
 */
@Data
public class RouterMenuVO {

    @ApiModelProperty ("路由路径")
    private String path;

    @ApiModelProperty ("路由名称")
    private String name;

    @ApiModelProperty ("组件注册地址")
    private String component;

    @ApiModelProperty ("图标")
    private String icon;

    @ApiModelProperty ("菜单名称")
    private String componentName;

    @ApiModelProperty ("是否显示菜单 1 显示，0 隐藏")
    private Boolean showMenu;

    @ApiModelProperty ("排序")
    private Integer menuSort;


    @ApiModelProperty ("子集")
    private List<RouterMenuVO> children;


}
