package com.msun.csm.model.dto.intlog;

import com.msun.csm.common.model.dto.BasePageDTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 系统接口日志前端传参类
 */
@Data
public class SysIntLogDTO extends BasePageDTO {

    @ApiModelProperty ("开始时间")
    private String startTime;

    @ApiModelProperty ("结束时间")
    private String endTime;

    @ApiModelProperty ("操作用户id")
    private String createrId;

    @ApiModelProperty ("中文名称")
    private String cnIntName;

    @ApiModelProperty ("日志内容")
    private String errDetail;

    @ApiModelProperty ("接口类型")
    private int typeCode;
}
