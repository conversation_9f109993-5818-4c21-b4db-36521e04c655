package com.msun.csm.model.convert;

import org.mapstruct.Mapper;

import com.msun.csm.common.model.convert.Dto2PoBaseConvert;
import com.msun.csm.common.model.convert.Dto2VoBaseConvert;
import com.msun.csm.common.model.convert.Vo2PoBaseConvert;
import com.msun.csm.dao.entity.SysConfig;
import com.msun.csm.model.dto.SysConfigDTO;
import com.msun.csm.model.vo.SysConfigVO;

/**
 * 配置表(SysConfig)数据转换
 *
 * <AUTHOR>
 * @since 2024-05-13 16:37:39
 */
@Mapper(componentModel = "spring")
public interface SysConfigConvert extends Dto2PoBaseConvert<SysConfigDTO, SysConfig>,
        Dto2VoBaseConvert<SysConfigDTO, SysConfigVO>, Vo2PoBaseConvert<SysConfigVO, SysConfig> {

}
