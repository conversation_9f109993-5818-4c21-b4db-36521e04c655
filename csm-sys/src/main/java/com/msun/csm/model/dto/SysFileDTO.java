package com.msun.csm.model.dto;


import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.msun.csm.model.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 系统文件(SysFile)实体类
 *
 * <AUTHOR>
 * @since 2024-05-14 14:08:26
 */
@Data
@ApiModel(value = "", description = "")
public class SysFileDTO extends BaseEntity {
    @ApiModelProperty ("项目id")
    private Long projectInfoId;
    @ApiModelProperty("主键id")
    @TableId(type = IdType.INPUT)
    private Long sysFileId;
    /**
     * 文件名称
     */
    @ApiModelProperty("文件名称")
    private String fileName;

    /**
     * 文件code
     */
    @ApiModelProperty("文件code-唯一标示")
    private String fileCode;

    /**
     * 文件地址
     */
    @ApiModelProperty("文件地址")
    private String filePath;

    /**
     * 文件标题，file_code的描述。同一business_code下，同一file_name名称时，用于区分
     */
    @ApiModelProperty("主键id")
    private String fileTitle;

    /**
     * 文件描述
     */
    @ApiModelProperty("文件描述")
    private String fileDesc;

    /**
     * 文件上传版本
     */
    @ApiModelProperty("文件上传版本")
    private String version;

    /**
     * 文件版本更新说明
     */
    @ApiModelProperty("文件版本更新说明")
    private String versionDesc;

    /**
     * 文件大小-（链接没有大小）
     */
    @ApiModelProperty("文件大小-（链接没有大小）")
    private String fileSize;

    /**
     * 文件使用场景挂在方式（默认0:文件;1:系统跳转地址;2:在线链接;）
     */
    @ApiModelProperty("文件使用场景挂在方式（默认0:文件;1:系统跳转地址;2:在线链接;）")
    private Integer fileUsedMode;

    /**
     * 业务编码
     */
    @ApiModelProperty("业务编码")
    private String businessCode;

    /**
     * 业务场景描述
     */
    @ApiModelProperty("业务场景描述")
    private String businessDesc;

    @ApiModelProperty("fileCodes")
    private List<String> fileCodes;
}
