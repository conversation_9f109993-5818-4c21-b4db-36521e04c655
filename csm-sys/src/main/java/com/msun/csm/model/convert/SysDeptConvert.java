package com.msun.csm.model.convert;

import org.mapstruct.Mapper;

import com.msun.csm.common.model.convert.Dto2PoBaseConvert;
import com.msun.csm.common.model.convert.Dto2VoBaseConvert;
import com.msun.csm.common.model.convert.Vo2PoBaseConvert;
import com.msun.csm.dao.entity.SysDept;
import com.msun.csm.model.dto.dept.SysDeptDTO;
import com.msun.csm.model.vo.dept.SysDeptVO;

/**
 * @classDesc: 角色
 * @author: pengxinting
 * @date: 2023/7/26 15:57
 * @copyright 众阳健康
 */
@Mapper (componentModel = "spring")
public interface SysDeptConvert extends Dto2PoBaseConvert<SysDeptDTO, SysDept>,
        Dto2VoBaseConvert<SysDeptDTO, SysDeptVO>, Vo2PoBaseConvert<SysDeptVO, SysDept> {

}
