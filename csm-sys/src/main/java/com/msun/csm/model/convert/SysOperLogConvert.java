package com.msun.csm.model.convert;

import org.mapstruct.Mapper;

import com.msun.csm.common.model.convert.Dto2PoBaseConvert;
import com.msun.csm.common.model.convert.Dto2VoBaseConvert;
import com.msun.csm.common.model.convert.Vo2PoBaseConvert;
import com.msun.csm.dao.entity.SysOperLog;
import com.msun.csm.model.dto.operlog.SysOperLogDTO;
import com.msun.csm.model.vo.operlog.SysOperLogVO;

@Mapper (componentModel = "spring")
public interface SysOperLogConvert extends
        Dto2PoBaseConvert<SysOperLogDTO, SysOperLog>, Dto2VoBaseConvert<SysOperLogDTO, SysOperLogVO>, Vo2PoBaseConvert<SysOperLogVO, SysOperLog> {
}
