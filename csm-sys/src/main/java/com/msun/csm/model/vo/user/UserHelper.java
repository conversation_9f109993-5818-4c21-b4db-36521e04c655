package com.msun.csm.model.vo.user;

import java.util.Arrays;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.util.StrUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.alibaba.fastjson.JSONObject;
import com.msun.csm.common.enums.ResultEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.staticvariable.StaticPara;
import com.msun.csm.util.RedisUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * @DESCRIPTION:
 * @AUTHOR: mengchuan
 * @DATE: 2022/12/15
 */
@Component
@Slf4j
public class UserHelper {
    private final RedisUtil redisUtil;

    public UserHelper(RedisUtil redisUtil) {
        this.redisUtil = redisUtil;
    }

    /**
     * 获取当前登录用户的sysUserId，发生异常或者取不到数据时，默认返回-1
     *
     * @return 当前登录用户的sysUserId，发生异常或者取不到数据时，默认返回-1
     */
    public Long getCurrentSysUserIdWithDefaultValue() {
        // 当前登录用户
        SysUserVO currentUser = null;
        try {
            currentUser = this.getCurrentUser();
        } catch (Exception e) {
            log.error("获取当前登录用户，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return -1L;
        }

        if (currentUser != null && currentUser.getSysUserId() != null) {
            return currentUser.getSysUserId();
        } else {
            return -1L;
        }
    }


    /**
     * 获取当前用户的信息
     *
     * @return userVO
     */
    public SysUserVO getCurrentUser() {
        ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (sra == null) {
            throw new CustomException("获取当前登录用户失败，RequestContextHolder.getRequestAttributes()返回null");
        }
        HttpServletRequest request = sra.getRequest();
        return getCurrentUser(request);
    }

    /**
     * 通过请求获取当前用户信息
     *
     * @param request 请求
     * @return userVO
     */
    public SysUserVO getCurrentUser(HttpServletRequest request) {
        String token = request.getHeader(StaticPara.HEADER_TOKEN_NAME);
        if (StrUtil.isBlank(token)) {
            Cookie[] cookies = request.getCookies();
            Cookie ck = null;
            for (Cookie cookie : cookies) {
                if (StrUtil.equals(cookie.getName(), StaticPara.HEADER_TOKEN_NAME)) {
                    ck = cookie;
                    break;
                }
            }
            if (ObjectUtils.isEmpty(cookies) || ck == null) {
                log.error("获取当前登录用户,header中没有找到token,cookie中也没有找到token");
            }
            token = ck.getValue();
        }
        if (StrUtil.isBlank(token)) {
            log.error("获取当前用户登录信息失败，请求中不包含认证信息");
        }
        return getCurrentUserByToken(token);
    }

    /**
     * 通过token获取当前用户信息
     *
     * @param token token
     * @return userVO
     */
    public SysUserVO getCurrentUserByToken(String token) {
        String context = (String) redisUtil.get(token);
        if (StringUtils.isBlank(context)) {
            log.error("从缓存数据库中未找到认证信息");
            return null;
        }
        try {
            return JSONObject.parseObject(context, SysUserVO.class);
        } catch (Exception e) {
            log.error("通过token获取当前用户信息，发生异常，token={}，UserInfo={}，errMsg={}，stackInfo=", token, context, e.getMessage(), e);
        }
        return null;
    }
}
