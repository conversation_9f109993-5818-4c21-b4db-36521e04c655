package com.msun.csm.model.dto.role;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/18/10:45
 */
@Data
@ApiModel (value = "角色DTO")
public class SysRoleDTO {

    @ApiModelProperty ("主键id")
    private Long sysRoleId;

    @ApiModelProperty ("角色名称")
    private String roleName;

    @ApiModelProperty ("角色编码")
    private String roleCode;

    @ApiModelProperty ("修改人")
    private Long updaterId;

    @ApiModelProperty ("修改时间")
    @DateTimeFormat (pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat (pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 额外指定的查询数据范围，数据值是部门的运营平台ID，多个以逗号进行分割。查询时需要查询指定部门以及下属部门
     */
    private String dataRange;
}
