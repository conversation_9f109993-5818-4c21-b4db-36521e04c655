package com.msun.csm.model;

import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.msun.csm.config.Global;
import com.msun.csm.model.vo.user.SysUserVO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 说明: 基础Entity类，业务数据表中必须包括该类实体属性
 *
 * @author: Yhongmin
 * @createAt: 2024/5/10 8:41
 * @remark: Copyright
 */
@Data
public abstract class BaseEntity implements Serializable {


    private static final long serialVersionUID = -6607673323261286172L;
    @ApiModelProperty("逻辑删除【0：否；1：是】")
    @TableLogic
    @TableField(fill = FieldFill.UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    private Integer isDeleted;

    @ApiModelProperty("创建人员id")
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    private Long createrId;

    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("更新人员id")
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    private Long updaterId;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.NOT_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 需要手动调用
     */
    public void preInsert() {
        SysUserVO user = Global.getSysUserVO();
        if (user != null) {
            this.createrId = user.getSysUserId();
            this.updaterId = user.getSysUserId();
        }
        //setId(SnowFlakeUtil.getId());
        this.createTime = new Date();
        this.updateTime = new Date();
        this.isDeleted = 0;
    }

    /**
     * 需要手动调用
     */
    public void preUpdate() {
        SysUserVO user = Global.getSysUserVO();
        if (user != null) {
            this.updaterId = user.getSysUserId();
        }
        this.updateTime = new Date();
        this.isDeleted = 0;
    }

    /**
     * 需要手动调用
     */
    public void preInvalid() {
        SysUserVO user = Global.getSysUserVO();
        if (user != null) {
            this.updaterId = user.getSysUserId();
        }
        this.updateTime = new Date();
        this.isDeleted = 1;
    }

}
