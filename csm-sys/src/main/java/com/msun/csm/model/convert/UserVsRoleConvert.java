package com.msun.csm.model.convert;

import org.mapstruct.Mapper;

import com.msun.csm.common.model.convert.Dto2PoBaseConvert;
import com.msun.csm.common.model.convert.Dto2VoBaseConvert;
import com.msun.csm.common.model.convert.Vo2PoBaseConvert;
import com.msun.csm.dao.entity.UserVsRole;
import com.msun.csm.model.dto.user.UserVsRoleDTO;
import com.msun.csm.model.vo.user.UserVsRoleVO;

/**
 * @classDesc: 人员与角色关联表
 * @author: pengxinting
 * @date: 2023/7/26 15:57
 * @copyright 众阳健康
 */
@Mapper (componentModel = "spring")
public interface UserVsRoleConvert extends Dto2PoBaseConvert<UserVsRoleDTO, UserVsRole>,
        Dto2VoBaseConvert<UserVsRoleDTO, UserVsRoleVO>, Vo2PoBaseConvert<UserVsRoleVO, UserVsRole> {

}
