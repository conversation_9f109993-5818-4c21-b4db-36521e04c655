package com.msun.csm.model.vo.menu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/19/9:41
 */
@Data
@ApiModel(value = "菜单信息VO")
public class SysMenuVO {

    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private Long sysMenuId;

    /**
     * 菜单名称
     */
    @ApiModelProperty("菜单名称")
    private String menuName;

    /**
     * 菜单code
     */
    @ApiModelProperty("菜单Code")
    private String menuCode;

    /**
     * 菜单图标
     */
    @ApiModelProperty("菜单图标")
    private String menuIcon;

    /**
     * url地址
     */
    @ApiModelProperty("菜单地址")
    private String menuUrl;

    /**
     * 排序
     */
    @ApiModelProperty("菜单排序")
    private Integer menuSort;

    /**
     * 父节点
     */
    @ApiModelProperty("父节点")
    private Long pid;

    @ApiModelProperty("注册路径")
    private String component;

    @ApiModelProperty("路由名称")
    private String routerName;

    @ApiModelProperty("是否显示菜单 1 显示，0 隐藏")
    private Boolean showMenu;
}
