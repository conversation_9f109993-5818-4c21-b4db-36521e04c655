package com.msun.csm.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 配置表(SysConfig)实体类
 *
 * <AUTHOR>
 * @since 2024-05-13 16:37:39
 */
@Data
public class SysConfigVO {


    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 创建人id
     */
    @ApiModelProperty("创建人id")
    private Long createrId;

    /**
     * 更新人id
     */
    @ApiModelProperty("更新人id")
    private Long updaterId;

    /**
     * 逻辑删除【0：否；1：是】
     */
    @ApiModelProperty("逻辑删除【0：否；1：是】")
    private Integer isDeleted;

    /**
     * 配置编码
     */
    @ApiModelProperty("配置编码")
    private String configCode;

    /**
     * 配置名称
     */
    @ApiModelProperty("配置名称")
    private String configName;

    /**
     * 配置值
     */
    @ApiModelProperty("配置值")
    private String configValue;

    /**
     * 配置描述
     */
    @ApiModelProperty("配置描述")
    private String configDesc;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer orderNo;
    /**
     * 值类型来自于枚举
     */
    private String configType;

    /**
     * 是否显示该配置项0:不显示1：显示,默认不显示
     */
    private Integer displayFlag;
    /**
     * 多选，下拉，开关，初始化值
     */
    private String configOptionalValue;
}
