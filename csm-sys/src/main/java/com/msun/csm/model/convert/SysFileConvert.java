package com.msun.csm.model.convert;

import org.mapstruct.Mapper;

import com.msun.csm.common.model.convert.Dto2PoBaseConvert;
import com.msun.csm.common.model.convert.Dto2VoBaseConvert;
import com.msun.csm.common.model.convert.Vo2PoBaseConvert;
import com.msun.csm.dao.entity.SysFile;
import com.msun.csm.model.dto.SysFileDTO;
import com.msun.csm.model.vo.SysFileVO;

/**
 * 系统文件(SysFile)数据转换
 *
 * <AUTHOR>
 * @since 2024-05-14 14:09:14
 */
@Mapper(componentModel = "spring")
public interface SysFileConvert extends Dto2PoBaseConvert<SysFileDTO, SysFile>,
        Dto2VoBaseConvert<SysFileDTO, SysFileVO>, Vo2PoBaseConvert<SysFileVO, SysFile> {

}
