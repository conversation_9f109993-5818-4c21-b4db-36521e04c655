package com.msun.csm.model.convert;

import org.mapstruct.Mapper;

import com.msun.csm.common.model.convert.Dto2PoBaseConvert;
import com.msun.csm.common.model.convert.Dto2VoBaseConvert;
import com.msun.csm.common.model.convert.Vo2PoBaseConvert;
import com.msun.csm.dao.entity.RoleVsMenu;
import com.msun.csm.model.dto.role.RoleVsMenuDTO;
import com.msun.csm.model.vo.role.RoleVsMenuVO;

/**
 * @classDesc: 人员与角色关联表
 * @author: pengxinting
 * @date: 2023/7/26 15:57
 * @copyright 众阳健康
 */
@Mapper (componentModel = "spring")
public interface RoleVsMenuConvert extends Dto2PoBaseConvert<RoleVsMenuDTO, RoleVsMenu>,
        Dto2VoBaseConvert<RoleVsMenuDTO, RoleVsMenuVO>, Vo2PoBaseConvert<RoleVsMenuVO, RoleVsMenu> {

}
