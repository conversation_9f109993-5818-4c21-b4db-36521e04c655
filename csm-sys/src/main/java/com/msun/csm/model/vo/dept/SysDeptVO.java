package com.msun.csm.model.vo.dept;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/4/18
 */

/**
 * 部门信息表
 *
 * <AUTHOR>
 */
@Data
public class SysDeptVO {
    /**
     * 主键id
     */
    @ApiModelProperty ("主键id")
    private Long sysDeptId;

    /**
     * 运营平台对应部门id
     */
    @ApiModelProperty ("运营平台对应部门id")
    private Long deptYunyingId;

    /**
     * 部门名称
     */
    @ApiModelProperty ("部门名称")
    private String deptName;

    /**
     * 部门负责人的运营id
     */
    @ApiModelProperty ("部门负责人的运营id")
    private String deptLeaderYunyingId;

    /**
     * 部门负责人名称
     */
    @ApiModelProperty ("部门负责人名称")
    private String deptLeaderYunyingName;

    /**
     * 部门类型
     */
    @ApiModelProperty ("部门类型")
    private String deptCategory;

    /**
     * 上级部门id
     */
    @ApiModelProperty ("上级部门id")
    private Long pid;

    @ApiModelProperty("部门层级")
    private Integer deptLevel;
}
