package com.msun.csm.model.vo.user;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/18/16:58
 */
@Data
@ApiModel (value = "人员关联角色VO")
public class UserVsRoleVO {

    @ApiModelProperty ("主键id")
    private Long id;

    @ApiModelProperty ("人员id")
    private Long userId;

    @ApiModelProperty ("人员姓名")
    private String userName;

    @ApiModelProperty ("角色id")
    private Long roleId;

    @ApiModelProperty ("角色名称")
    private String roleName;

    @ApiModelProperty ("角色code")
    private String roleCode;

    @ApiModelProperty ("是否默认【0：否；1：是】")
    private Integer defaultFlag;

    @ApiModelProperty ("角色id集合")
    private List<String> roleIds;

    @ApiModelProperty ("是否主角色【0：否；1：是】")
    private Integer isMainRole;

    @ApiModelProperty ("是否删除【0：否；1：是】")
    private String isMainRoleId;
}
