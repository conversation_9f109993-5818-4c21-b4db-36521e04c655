package com.msun.csm.model.convert;

import org.mapstruct.Mapper;

import com.msun.csm.common.model.convert.Dto2PoBaseConvert;
import com.msun.csm.common.model.convert.Dto2VoBaseConvert;
import com.msun.csm.common.model.convert.Vo2PoBaseConvert;
import com.msun.csm.dao.entity.SysFile;
import com.msun.csm.model.dto.SysFilePageDTO;
import com.msun.csm.model.vo.SysFilePageVO;

/**
 * 系统文件(SysFile)分页数据转换
 *
 * <AUTHOR>
 * @since 2024-05-14 15:31:54
 */
@Mapper(componentModel = "spring")
public interface SysFilePageConvert extends Dto2PoBaseConvert<SysFilePageDTO, SysFile>,
        Dto2VoBaseConvert<SysFilePageDTO, SysFilePageVO>, Vo2PoBaseConvert<SysFilePageVO, SysFile> {

}

