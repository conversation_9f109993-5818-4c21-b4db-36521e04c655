package com.msun.csm.model.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/18/18:21
 */
@Data
@ApiModel (value = "登录DTO")
public class LoginDTO {

    @ApiModelProperty ("账号")
    private String account;

    @ApiModelProperty ("密码")
    private String password;

    /**
     * 注册token
     */
    private String token;

    /**
     * 滑动x坐标
     */
    private Integer sliceX;

    /**
     * MFA验证码
     */
    @ApiModelProperty ("MFA 6位 输入验证码")
    private String verificationCode;

    @ApiModelProperty ("密钥")
    private String secretKey;
}
