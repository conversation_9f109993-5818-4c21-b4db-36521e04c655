package com.msun.csm.model.vo.file;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @version : V1.52.0
 * @ClassName: FileCodeTitleName
 * @Description:
 * @Author: Yhongmin
 * @Date: 14:49 2024/6/11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FileCodeTitleNameVO {
    /**
     * 文件code
     */
    @ApiModelProperty("文件code-唯一标示")
    private String id;
    /**
     * 文件标题，同一business_code下，同一file_name名称时，用于区分
     */
    @ApiModelProperty("文件标题，同一business_code下，同一file_name名称时，用于区分")
    private String name;
}
