package com.msun.csm.model.convert;

import org.mapstruct.Mapper;

import com.msun.csm.common.model.convert.Dto2PoBaseConvert;
import com.msun.csm.common.model.convert.Dto2VoBaseConvert;
import com.msun.csm.common.model.convert.Vo2PoBaseConvert;
import com.msun.csm.dao.entity.SysIntLog;
import com.msun.csm.model.dto.intlog.SysIntLogDTO;
import com.msun.csm.model.vo.intlog.SysIntLogVO;

@Mapper (componentModel = "spring")
public interface SysIntLogConvert extends
        Dto2PoBaseConvert<SysIntLogDTO, SysIntLog>, Dto2VoBaseConvert<SysIntLogDTO, SysIntLogVO>, Vo2PoBaseConvert<SysIntLogVO, SysIntLog> {
}
