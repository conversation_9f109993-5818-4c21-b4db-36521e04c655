package com.msun.csm.model.convert;

import org.mapstruct.Mapper;

import com.msun.csm.common.model.convert.Dto2PoBaseConvert;
import com.msun.csm.common.model.convert.Dto2VoBaseConvert;
import com.msun.csm.common.model.convert.Vo2PoBaseConvert;
import com.msun.csm.dao.entity.SysMenu;
import com.msun.csm.model.dto.menu.SysMenuDTO;
import com.msun.csm.model.vo.menu.SysMenuVO;

/**
 * @classDesc: 角色
 * @author: pengxinting
 * @date: 2023/7/26 15:57
 * @copyright 众阳健康
 */
@Mapper (componentModel = "spring")
public interface SysMenuConvert extends Dto2PoBaseConvert<SysMenuDTO, SysMenu>,
        Dto2VoBaseConvert<SysMenuDTO, SysMenuVO>, Vo2PoBaseConvert<SysMenuVO, SysMenu> {

}
