package com.msun.csm.feign.client.knowledge;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import com.msun.csm.model.imsp.ThirdInterfaceDTO;
import com.msun.csm.model.imsp.YunweiUpdateStatisReportDTO;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/8/9
 */
@FeignClient(url = "${project.feign.knowledge.url}", name = "KnowledgeFeignClient")
public interface KnowledgeFeignClient {

    /**
     * 获取token信息
     * @param username
     * @param knowledgeSign
     * @return
     */
    @PostMapping(value = "${project.feign.knowledge.authJf}", headers = {"Content-Type=application/json;charset=UTF-8"})
    String authJf(@RequestParam("username") String username, @RequestHeader("KnowledgeSign") String knowledgeSign);

    /**
     * 回写运维平台三方接口绑定关系
     * @param dto
     * @return
     */
    @PostMapping(value = "${project.feign.knowledge.updateThirdInterfaceId-method}", headers = {"Content-Type=application/json;charset=UTF-8"})
    String updateThirdInterfaceId(@RequestBody ThirdInterfaceDTO dto,
                                  @RequestHeader("KnowledgeSign") String knowledgeSign);

    /**
     * 回写运维平台统计报告状态
     * @param dto
     * @param knowledgeSign
     * @return
     */
    @PostMapping(value = "/fromImsp/feedback/acceptReportStatus", headers = {"Content-Type=application/json;charset=UTF-8"})
    String acceptReportStatus(@RequestBody YunweiUpdateStatisReportDTO dto,
                                  @RequestHeader("KnowledgeSign") String knowledgeSign);
}
