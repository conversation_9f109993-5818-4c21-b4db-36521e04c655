package com.msun.csm.feign.client.knowledge;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import com.alibaba.fastjson.JSONObject;
import com.msun.core.commons.api.ResponseResult;
import com.msun.csm.model.imsp.ThirdInterfaceDTO;
import com.msun.csm.model.imsp.YunweiUpdateStatisReportDTO;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/8/9
 */
@FeignClient(url = "${project.feign.knowledge.url}", name = "KnowledgeFeignClient")
public interface KnowledgeFeignClient {

    /**
     * 获取token信息
     * @param username
     * @param knowledgeSign
     * @return
     */
    @PostMapping(value = "${project.feign.knowledge.authJf}", headers = {"Content-Type=application/json;charset=UTF-8"})
    String authJf(@RequestParam("username") String username, @RequestHeader("KnowledgeSign") String knowledgeSign);

    /**
     * 回写运维平台三方接口绑定关系
     * @param dto
     * @return
     */
    @PostMapping(value = "${project.feign.knowledge.updateThirdInterfaceId-method}", headers = {"Content-Type=application/json;charset=UTF-8"})
    String updateThirdInterfaceId(@RequestBody ThirdInterfaceDTO dto,
                                  @RequestHeader("KnowledgeSign") String knowledgeSign);

    /**
     * 回写运维平台统计报告状态
     * @param dto
     * @param knowledgeSign
     * @return
     */
    @PostMapping(value = "/fromImsp/feedback/acceptReportStatus", headers = {"Content-Type=application/json;charset=UTF-8"})
    String acceptReportStatus(@RequestBody YunweiUpdateStatisReportDTO dto,
                                  @RequestHeader("KnowledgeSign") String knowledgeSign);

    /**
     * 向运维平台新增三方接口绑定关系
     * @param dto
     * @return
     */
    @PostMapping(value = "${project.feign.knowledge.addThirdInter-method}", headers = {"Content-Type=application/json;charset=UTF-8"})
    ResponseResult addQuestionInfoByKFComeJf(@RequestBody JSONObject dto,
                                             @RequestHeader("KnowledgeSign") String knowledgeSign);

    /**
     * 同步医院用户到运维平台
     * @param dto
     * @param knowledgeSign
     * @return
     */
    @PostMapping(value = "${project.feign.knowledge.downloadJfHosDeptUserInfo}", headers = {"Content-Type=application/json;charset=UTF-8"})
    ResponseResult downloadJfHosDeptUserInfo(@RequestBody JSONObject dto,
                                             @RequestHeader("KnowledgeSign") String knowledgeSign);

}
