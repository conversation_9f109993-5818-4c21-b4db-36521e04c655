package com.msun.csm.service.config;

import javax.validation.constraints.NotNull;

import org.springframework.validation.annotation.Validated;

import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.SysConfigDTO;
import com.msun.csm.model.vo.SysConfigVO;

/**
 * 配置表(Config)表服务接口
 *
 * <AUTHOR>
 * @since 2024-05-13 15:02:32
 */
@Validated
public interface SysConfigService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    SysConfigVO getConfigById(@NotNull Long id);
    /**
     * 说明: 
     * @param configCode 
     * @return:com.msun.csm.dao.entity.config.entity.vo.SysConfigVO
     * @author: Yhongmin
     * @createAt: 2024/5/13 16:54
     * @remark: Copyright
      */
    SysConfigVO selectConfigByName(@NotNull String configCode);

    /**
     * 新增数据
     *
     * @param entity 实例对象
     * @return 成功为1，失败为0
     */
    Result<String> save(@NotNull SysConfigDTO entity);

    /**
     * 修改数据
     *
     * @param entity 实例对象
     * @return 成功为1，失败为0
     */
    Result<String> update(@NotNull SysConfigDTO entity);

    /**
     * 作废
     *
     * @param id 主键
     * @return 是否成功
     */
    Result<String> updateMcInvalid(@NotNull Long id);
    /**
     * 说明: 刷新缓存
     * @return:com.msun.csm.common.model.Result<java.lang.String>
     * @author: Yhongmin
     * @createAt: 2024/8/21 9:25
     * @remark: Copyright
      */
    Result<String> refreshCacheSysConfig(String configCode);
}
