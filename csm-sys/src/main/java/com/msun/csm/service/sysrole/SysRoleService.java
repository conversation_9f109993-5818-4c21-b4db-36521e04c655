package com.msun.csm.service.sysrole;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.role.RoleVsMenuDTO;
import com.msun.csm.model.dto.role.SysRoleDTO;
import com.msun.csm.model.dto.role.SysRolePageDTO;
import com.msun.csm.model.dto.user.UserVsRoleDTO;
import com.msun.csm.model.vo.menu.RouterMenuVO;
import com.msun.csm.model.vo.role.SysRoleVO;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/18/10:51
 */
public interface SysRoleService {

    /**
     * 添加角色
     *
     * @param dto
     */
    Result saveOrUpdateRole(SysRoleDTO dto);

    /**
     * 删除角色
     *
     * @param dto
     * @return
     */
    Result removeRole(SysRoleDTO dto);

    /**
     * 修改角色
     *
     * @param dto
     * @return
     */
    Result updateRole(SysRoleDTO dto);


    /**
     * 查询角色
     *
     * @param dto
     * @return
     */
    Result<SysRoleVO> getRole(SysRoleDTO dto);

    /**
     * 批量查询角色
     *
     * @param dto
     * @return
     */
    Result<PageInfo<SysRoleVO>> selectRoleListByPage(SysRolePageDTO dto);

    /**
     * 查询角色下的菜单信息
     *
     * @param dto
     * @return
     */
    Result<List<RouterMenuVO>> findMenuByRole(UserVsRoleDTO dto);

    /**
     * 角色保存菜单信息
     *
     * @param dtoList
     * @return
     */
    Result roleSaveMenu(List<RoleVsMenuDTO> dtoList);

    /**
     * 查询全部角色
     *
     * @param dto
     * @return
     */
    Result<List<SysRoleVO>> selectRoleList(SysRoleDTO dto);
}
