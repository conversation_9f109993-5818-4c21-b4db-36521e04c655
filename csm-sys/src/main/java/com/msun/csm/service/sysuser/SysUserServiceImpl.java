package com.msun.csm.service.sysuser;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.endpoint.Client;
import org.apache.cxf.jaxws.endpoint.dynamic.JaxWsDynamicClientFactory;
import org.apache.cxf.transport.http.HTTPConduit;
import org.apache.cxf.transports.http.configuration.HTTPClientPolicy;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageInfo;
import com.google.zxing.WriterException;
import com.msun.core.commons.api.ResponseResult;
import com.msun.csm.common.enums.ResultEnum;
import com.msun.csm.common.exception.CustomException;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.staticvariable.StaticPara;
import com.msun.csm.dao.entity.DictWeakPassword;
import com.msun.csm.dao.entity.SysConfig;
import com.msun.csm.dao.entity.SysDept;
import com.msun.csm.dao.entity.SysRole;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.dao.entity.UserVsRole;
import com.msun.csm.dao.entity.oldimsp.ImspSysUser;
import com.msun.csm.dao.mapper.DictWeakPasswordMapper;
import com.msun.csm.dao.mapper.config.SysConfigMapper;
import com.msun.csm.dao.mapper.oldimsp.ImspSysUserMapper;
import com.msun.csm.dao.mapper.sysdept.SysDeptMapper;
import com.msun.csm.dao.mapper.sysrole.SysRoleMapper;
import com.msun.csm.dao.mapper.sysuser.SysUserMapper;
import com.msun.csm.dao.mapper.sysuser.UserVsRoleMapper;
import com.msun.csm.feign.client.knowledge.KnowledgeFeignClient;
import com.msun.csm.mfa.GoogleAuthenticatorUtils;
import com.msun.csm.mfa.QRCodeUtils;
import com.msun.csm.model.convert.SysUserConvert;
import com.msun.csm.model.convert.UserVsRoleConvert;
import com.msun.csm.model.dto.UserVsRoleSaveDTO;
import com.msun.csm.model.dto.dept.SysDeptDTO;
import com.msun.csm.model.dto.role.SysRoleDTO;
import com.msun.csm.model.dto.user.LoginDTO;
import com.msun.csm.model.dto.user.LoginMfaDTO;
import com.msun.csm.model.dto.user.SysUserDTO;
import com.msun.csm.model.dto.user.UserVsRoleDTO;
import com.msun.csm.model.vo.dept.SysDeptVO;
import com.msun.csm.model.vo.role.SysRoleVO;
import com.msun.csm.model.vo.user.SysUserPageVO;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.model.vo.user.UserVsRoleVO;
import com.msun.csm.service.sysdept.SysDeptService;
import com.msun.csm.service.sysrole.SysRoleService;
import com.msun.csm.util.Md5Util;
import com.msun.csm.util.PageHelperUtil;
import com.msun.csm.util.RedisUtil;
import com.msun.csm.util.Sm4Util;
import com.msun.csm.util.SnowFlakeUtil;
import com.obs.services.internal.ServiceException;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024-04-17
 */
@Slf4j
@Service
public class SysUserServiceImpl implements SysUserService {
    private static final String ACCOUNT_CODE = "accountCode";
    private static final String SECRET_KEY = "secretKey";
    // 设置最大登录次数
    private static final int MAX_ATTEMPTS = 5;
    // 登录尝试次数key
    private static final String ATTEMPT_KEY_PREFIX = "login_attempts_";
    // MFA登录尝试次数锁定前缀
    private static final String MFA_KEY_PREFIX = "mfa_attempts_";
    // 超时时间分钟
    private static final Integer TIME_OUT = 5;

    private final UserHelper userHelper;
    @Value("${project.feign.oa.url}")
    private String oaUrl;
    @Value("${project.feign.oa.login-method}")
    private String oaLoginPath;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private SysUserMapper userMapper;
    @Resource
    private UserVsRoleMapper userVsRoleMapper;
    @Resource
    private SysUserConvert userConvert;
    @Resource
    private UserVsRoleConvert userVsRoleConvert;
    @Resource
    private SysDeptService deptService;
    @Resource
    @Lazy
    private SysRoleService roleService;
    @Resource
    @Lazy
    private SysRoleMapper roleMapper;
    @Resource
    private SysDeptMapper deptMapper;
    @Resource
    private ImspSysUserMapper imspSysUserMapper;
    private String deptLevelNameStr = "";
    @Resource
    private DictWeakPasswordMapper dictWeakPasswordMapper;
    @Resource
    private SysConfigMapper sysConfigMapper;
    @Resource
    private KnowledgeFeignClient knowledgeFeignClient;

    @Value("${project.feign.knowledge.appId}")
    private String knowledgeAppId;

    @Value("${project.feign.knowledge.publicKey}")
    private String knowledgePublicKey;

    public SysUserServiceImpl(UserHelper userHelper) {
        this.userHelper = userHelper;
    }

    /**
     * 生成二维码的Base64编码
     * 可以使用手机应用Google Authenticator来扫描二维码进行绑定
     *
     * @param secretKey
     * @param accountName
     * @return
     */
    public static String generateGoogleAuthQRCode(String secretKey, String accountName) {
        // 生成二维码
        String qrStr;
        String issuer = "客户服务平台";
        log.info("secretKey==>>" + secretKey);
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            String keyUri = GoogleAuthenticatorUtils.createKeyUri(secretKey, accountName, issuer);  // Demo_System 服务标识不参与运算，可任意设置
            QRCodeUtils.writeToStream(keyUri, bos);
            qrStr = Base64.encodeBase64String(bos.toByteArray());
        } catch (WriterException | IOException e) {
            throw new ServiceException("生成二维码失败", e);
        }
        if (com.msun.csm.util.StringUtils.isEmpty(qrStr)) {
            throw new ServiceException("生成二维码失败");
        }
        return "data:image/png;base64," + qrStr;
    }

    /**
     * 登录接口
     *
     * @param dto
     * @return
     */
    @Override
    public Result login(LoginDTO dto, HttpServletRequest request) {
        // 设置尝试次数
        String attemptsKey = ATTEMPT_KEY_PREFIX + dto.getAccount();
        String attemptsStr = (String) redisUtil.get(attemptsKey);
        SysUser param = new SysUser();
        param.setAccount(dto.getAccount());
        String password = Sm4Util.decrypt(dto.getPassword());
//        String password = dto.getPassword();
        log.info("登录接口密码初始：{}", dto.getPassword());
        dto.setPassword(password);
        param.setPassword(dto.getPassword());
        log.info("登录接口密码解密后：{}", dto.getPassword());
        // 1. 判断人员是否在交付平台存在
        SysUser user = userMapper.selectOne(new QueryWrapper<SysUser>().eq("account", param.getAccount()));
        if (ObjectUtil.isEmpty(user)) {
            return Result.fail(ResultEnum.USER_NOT_EXIST);
        }
        // 2. 判断人员在交付平台是否有角色，当没有角色时 禁止登录
        SysUserDTO sysUserDTO = userConvert.po2Dto(user);
        List<UserVsRoleVO> userVsRoleVOS = userMapper.findRolesByUser(sysUserDTO);
        if (CollectionUtil.isEmpty(userVsRoleVOS)) {
            return Result.fail(ResultEnum.NO_AUTH);
        }
        // 根据用户角色查询数据范围
        // 3. 调用运营平台鉴权
        JaxWsDynamicClientFactory clientFactory = JaxWsDynamicClientFactory.newInstance();
        Object[] objects;
        try {
            log.info("调用运营平台鉴权接口：{}，{}", oaUrl + oaLoginPath, dto.getAccount());
            Client client = clientFactory.createClient(oaUrl + oaLoginPath);
            HTTPConduit conduit = (HTTPConduit) client.getConduit();
            HTTPClientPolicy policy = new HTTPClientPolicy();
            policy.setConnectionTimeout(StaticPara.INTERFACE_TIMEOUT);
            policy.setReceiveTimeout(StaticPara.INTERFACE_TIMEOUT);
            conduit.setClient(policy);
            objects = client.invoke("login", dto.getAccount(), dto.getPassword());
        } catch (Exception e) {
            //调用运营平台失败
            log.error("调用运营平台鉴权接口，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            throw new CustomException(ResultEnum.SYSTEM_ERROR);
        }
        JSONObject loginResult = JSON.parseObject(objects[0].toString());
        String status = loginResult.getString("status");
        if (status.equals("success")) {
            // 密码弱口令校验
            List<DictWeakPassword> dictWeakPasswords = dictWeakPasswordMapper.selectList(new QueryWrapper<DictWeakPassword>().eq("password", dto.getPassword()));
            if (CollectionUtil.isNotEmpty(dictWeakPasswords)) {
                return Result.fail("当前登录密码安全等级低，请前往运营平台修改后重新登录。");
            }
            // 3.2 拼装返回值
            SysUserVO userInfo = userConvert.po2Vo(user);
            // 登录成功后进行判断是否需要二次验证
            userInfo = this.checkMfa(userInfo, userVsRoleVOS, user, request);
            if (ObjectUtil.isNotEmpty(userInfo) && ObjectUtil.isNotEmpty(userInfo.getIsBound()) && userInfo.getIsBound() == 1) {
                request.getSession().setAttribute(ACCOUNT_CODE, dto.getAccount());
                // 重新组装user信息，告诉客户端该账号需要进行MFA验证
                String img = userInfo.getImgSrc();
                // 创建新对象
                userInfo = new SysUserVO();
                userInfo.setIsBound(1);
                // 第一次登录，未扫码的用户，返回的是base64图片
                userInfo.setImgSrc(img);
                return Result.success(userInfo);
            }
            // 查询人员所属部门
            SysDeptDTO sysDeptDTO = new SysDeptDTO();
            sysDeptDTO.setDeptYunyingId(userInfo.getDeptId());
            SysDeptVO one = deptService.getOne(sysDeptDTO).getData();
            userInfo.setDeptName(one.getDeptName());
            String token = "CSM_T_" + Md5Util.md5(StringUtils.joinWith(StrUtil.DASHED, user.getSysUserId(), user.getAccount(), new Date().getTime()));
            userInfo.setToken(token);
            redisUtil.set(token, JSON.toJSONString(userInfo), StaticPara.REDIS_CACHE_TIME, TimeUnit.HOURS);
            // 3.3 拼装角色列表
            userInfo.setUserVsRoleVOList(userVsRoleVOS);
            // 查询老系统人员id 赋值
            ImspSysUser imspSysUser = imspSysUserMapper.selectOne(new QueryWrapper<ImspSysUser>().eq("user_yunying_id", Convert.toLong(user.getUserYunyingId())).eq("status", "ENABLE"));
            if (ObjectUtil.isNotEmpty(imspSysUser) && ObjectUtil.isNotEmpty(imspSysUser.getUserId())) {
                userInfo.setImspUserId(imspSysUser.getUserId());
            }
            // 查询客户运维平台用户角色信息
            try {
                String respStr = knowledgeFeignClient.authJf(user.getAccount(), getAuthorization());
                log.info("调用客户运维平台鉴权接口，返回结果：{}", respStr);
                ResponseResult responseResult = JSON.parseObject(respStr, ResponseResult.class);
                if (responseResult.getSuccess() && responseResult.getData() != null) {
                    userInfo.setKnowUserInfo(JSONObject.parseObject(responseResult.getData().toString()));
                }
            } catch (Exception e) {
                log.error("调用客户运维平台鉴权接口，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            }
            // 成功后清除尝试次数
            redisUtil.del(attemptsKey);
            return Result.success(userInfo);
        } else {
            if (attemptsStr != null && Integer.parseInt(attemptsStr) >= MAX_ATTEMPTS) {
                return Result.fail("您已超过最大尝试次数，请" + TIME_OUT + "分钟后重试");
            }
            int attempts = 1;
            if (attemptsStr != null) {
                attempts = Integer.parseInt(attemptsStr) + 1;
            }
            // 5分钟后过期
            redisUtil.set(attemptsKey, String.valueOf(attempts), TIME_OUT, TimeUnit.MINUTES);
            return Result.fail("用户名密码错误，您还有" + (MAX_ATTEMPTS - attempts) + "次机会");
        }
    }

    // 1、 请求头秘钥生成
    private String getAuthorization() {
        // 时间戳
        Long timestamp = System.currentTimeMillis();
        // 原始串 【appId、publicKey 为运维分发】
        String old = "appid=" + knowledgeAppId + "&appsecret=" + knowledgePublicKey + "&timestamp=" + timestamp;
        // rsa工具
        RSA rsa = new RSA(null, knowledgePublicKey);
        // 加密
        String sign = rsa.encryptBase64(old, KeyType.PublicKey);
        // 拼接
        String headers = "appid=" + knowledgeAppId + ";sign=" + sign;
        return headers;
    }

    /**
     * 校验是否需要二次验证, 并组装相应参数
     *
     * @param userInfo
     * @param userVsRoleVOS
     * @param user
     * @return
     */
    private SysUserVO checkMfa(SysUserVO userInfo, List<UserVsRoleVO> userVsRoleVOS, SysUser user, HttpServletRequest request) {
        String configCode = "mfarole";
        SysConfig sysConfig = sysConfigMapper.selectConfigByName(configCode);
        try {
            SysConfig sysConfigIp = sysConfigMapper.selectConfigByName("filterIP");
            if (ObjectUtil.isNotEmpty(sysConfig)) {
                String dbIp = sysConfigIp.getConfigValue();
                String sourceIp = getClientIp(request);
                if (StringUtils.isNotEmpty(dbIp) && StringUtils.isNotEmpty(sourceIp)) {
                    String[] sourceIps = sourceIp.split(",");
                    for (String ip : sourceIps) {
                        if (dbIp.contains(ip)) {
                            return userInfo;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取配置信息失败，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        if (ObjectUtil.isNotEmpty(sysConfig)) {
            JSONObject jsonObject = JSON.parseObject(sysConfig.getConfigValue());
            String jsonString = com.alibaba.fastjson.JSON.toJSONString(jsonObject.get("data"));
            TypeReference<List<UserVsRoleVO>> typeReference = new TypeReference<List<UserVsRoleVO>>(UserVsRoleVO.class) {
            };
            List<UserVsRoleVO> userVsRoleVOS1 = com.alibaba.fastjson.JSON.parseObject(jsonString, typeReference);
            Integer isBound = 0;
            if (userVsRoleVOS1 != null && userVsRoleVOS1.size() > 0) {
                for (UserVsRoleVO userVsRoleVO : userVsRoleVOS) {
                    for (UserVsRoleVO userVsRoleVO1 : userVsRoleVOS1) {
                        if (userVsRoleVO.getRoleCode().equals(userVsRoleVO1.getRoleCode())) {
                            // 是否校验【0：否；1：是】
                            isBound = 1;
                            break;
                        }
                    }
                }
            }
            if (isBound == 1) {
                userInfo = new SysUserVO();
                userInfo.setIsBound(1);
                if (ObjectUtil.isNotEmpty(user.getSecretKey())) {
                    request.getSession().setAttribute(SECRET_KEY, user.getSecretKey());
                    userInfo.setImgSrc(null);
                } else {
                    String secretKey = GoogleAuthenticatorUtils.createSecretKey();
                    request.getSession().setAttribute(SECRET_KEY, secretKey);
                    userInfo.setImgSrc(generateGoogleAuthQRCode(secretKey, user.getAccount()));
                }
            } else {
                userInfo.setIsBound(0);
            }

        }
        return userInfo;
    }

    /**
     * 获取客户端ip
     *
     * @param request
     * @return
     */
    private String getClientIp(HttpServletRequest request) {
        String clientIp = request.getHeader("X-Forwarded-For");
        if (clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = request.getHeader("Proxy-Client-IP");
        }
        if (clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = request.getHeader("WL-Proxy-Client-IP");
        }
        if (clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = request.getHeader("HTTP_CLIENT_IP");
        }
        if (clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = request.getRemoteAddr();
        }
        log.info("客户端ip:{}", clientIp);
        return clientIp;
    }

    /**
     * 根据条件查询个人信息
     *
     * @param dto
     * @return
     */
    @Override
    public Result<SysUser> getOne(SysUserDTO dto) {
        SysUser user = userMapper.selectOne(new QueryWrapper<SysUser>().eq(ObjectUtil.isNotEmpty(dto.getAccount()), "account", dto.getAccount()).eq(ObjectUtil.isNotEmpty(dto.getUserYunyingId()), "user_yunying_id", dto.getUserYunyingId()));
        return Result.success(user);
    }

    /**
     * 分页查询人员信息
     *
     * @param dto
     * @return
     */
    @Override
    public Result<PageInfo<SysUserVO>> findPageList(SysUserDTO dto) {
        List<SysUserVO> sysUsers = PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(),page -> userMapper.findPageList(dto));
        PageInfo<SysUserVO> pageInfo = new PageInfo<>(sysUsers);
        return Result.success(pageInfo);
    }

    /**
     * 人员添加角色
     *
     * @param dtoList
     * @return
     */
    @Override
    public Result userSaveRoles(List<UserVsRoleDTO> dtoList) {
        // 清除该人员的全部角色，重新添加
        List<UserVsRole> roleList = userVsRoleMapper.selectList(new QueryWrapper<UserVsRole>().eq("user_id", dtoList.get(0).getUserId()));
        userVsRoleMapper.delete(new QueryWrapper<UserVsRole>().eq("user_id", dtoList.get(0).getUserId()));
        List<UserVsRole> userVsRoleList = userVsRoleConvert.dto2Po(dtoList);
        List<Long> roleIdList = new ArrayList<>();
        if (roleList != null && roleList.size() > 0) {
            roleIdList = roleList.stream().map(vo -> vo.getRoleId()).collect(Collectors.toList());
        }
        List<Long> isMainList = dtoList.stream().filter(vo -> vo != null && vo.getIsMainRole() == 1).map(vo -> vo.getRoleId()).collect(Collectors.toList());
        // 主键id赋值
        for (UserVsRole userVsRole : userVsRoleList) {
            userVsRole.setId(SnowFlakeUtil.getId());
            userVsRole.setIsDeleted(0);
            userVsRole.setDefaultFlag(0);
            userVsRole.setCreaterId(ObjectUtil.isNotEmpty(userHelper.getCurrentUser()) ? userHelper.getCurrentUser().getSysUserId() : -1);
            userVsRole.setCreateTime(new Date());
            userVsRole.setUpdaterId(ObjectUtil.isNotEmpty(userHelper.getCurrentUser()) ? userHelper.getCurrentUser().getSysUserId() : -1);
            userVsRole.setUpdateTime(new Date());
            if (isMainList.isEmpty()) {
                if (roleIdList.contains(userVsRole.getRoleId())) {
                    userVsRole.setIsMainRole(1);
                } else {
                    userVsRole.setIsMainRole(0);
                }
            }
        }
        userVsRoleMapper.batchInsert(userVsRoleList);
        return Result.success();
    }

    /**
     * 人员批量保存
     *
     * @param dtoList
     * @return
     */
    @Override
    public Result userSaveList(List<SysUserDTO> dtoList) {
        try {
            List<SysUser> userList = userConvert.dto2Po(dtoList);
            // 主键id赋值
            for (SysUser user : userList) {
                user.setSysUserId(ObjectUtil.isNotEmpty(user.getSysUserId()) ? user.getSysUserId() : SnowFlakeUtil.getId());
                user.setIsDeleted(0);
                user.setCreaterId(-1L);
                user.setCreateTime(new Date());
                user.setUpdaterId(-1L);
                user.setUpdateTime(new Date());
                userMapper.insert(user);
            }
            return Result.success();
        } catch (Exception e) {
            log.error("userSaveList，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
            return Result.fail();
        }
    }

    /**
     * 批量修改人员信息
     *
     * @param dtoList
     * @return
     */
    @Override
    public Result userUpdateList(List<SysUserDTO> dtoList) {
        List<SysUser> userList = userConvert.dto2Po(dtoList);
        // 主键id赋值
        for (SysUser user : userList) {
            user.setIsDeleted(0);
            user.setUpdaterId(-1L);
            user.setUpdateTime(new Date());
        }
        userMapper.updateBatchSelective(userList);
        return Result.success();
    }

    /**
     * 修改人员默认角色
     *
     * @param dto
     * @return
     */
    @Override
    public Result updateDefaultRole(UserVsRoleDTO dto) {
        // 该人员下的角色全部设置成非默认
        UserVsRoleDTO removeDefaultRole = new UserVsRoleDTO();
        removeDefaultRole.setUserId(dto.getUserId());
        removeDefaultRole.setDefaultFlag(0);
        userVsRoleMapper.updateDefaultRole(removeDefaultRole);
        // 设置默认角色
        UserVsRoleDTO defaultRole = new UserVsRoleDTO();
        defaultRole.setId(dto.getId());
        defaultRole.setUserId(dto.getUserId());
        defaultRole.setDefaultFlag(1);
        userVsRoleMapper.updateDefaultRole(defaultRole);
        return Result.success();
    }

    /**
     * 查询人员下的角色信息
     *
     * @param dto
     * @return
     */
    @Override
    public Result<List<UserVsRoleVO>> findRoleByUser(UserVsRoleDTO dto) {
        UserVsRoleDTO userVsRoleDTO = new UserVsRoleDTO();
        userVsRoleDTO.setUserId(dto.getUserId());
        List<UserVsRoleVO> userVsRoleVOList = userVsRoleMapper.selectUserVsRoleDetail(userVsRoleDTO);
        return Result.success(userVsRoleVOList);
    }

    /**
     * 根据人员id查询角色idList
     *
     * @param dto
     * @return
     */
    @Override
    public Result<UserVsRoleVO> findRoleIdsByUser(UserVsRoleSaveDTO dto) {
        UserVsRoleDTO userVsRoleDTO = new UserVsRoleDTO();
        userVsRoleDTO.setUserId(dto.getUserId());
        List<UserVsRoleVO> userVsRoleVOList = userVsRoleMapper.selectUserVsRoleDetail(userVsRoleDTO);
        List<Long> roleIdList = new ArrayList<>();
        if (userVsRoleVOList != null && !userVsRoleVOList.isEmpty()) {
            roleIdList = userVsRoleVOList.stream().filter(vo -> vo.getIsMainRole() != null && vo.getIsMainRole() == 1).map(vo -> vo.getRoleId()).collect(Collectors.toList());
        }
        UserVsRoleVO userVsRoleVO = new UserVsRoleVO();
        // 数据处理
        userVsRoleVO.setUserId(userVsRoleVOList.get(0).getUserId());
        userVsRoleVO.setUserName(userVsRoleVOList.get(0).getUserName());
        //处理精度丢失
        List<Long> roleIds = userVsRoleVOList.stream().filter(user -> user.getRoleId() != null).map(v -> v.getRoleId()).collect(Collectors.toList());
        List<String> stringRoleIds = roleIds.stream().map(Object::toString).collect(Collectors.toList());
        userVsRoleVO.setRoleIds(stringRoleIds);
        if (!roleIdList.isEmpty() && roleIdList.size() > 0) {
            userVsRoleVO.setIsMainRoleId(String.valueOf(roleIdList.get(0)));
        } else {
            userVsRoleVO.setIsMainRoleId("");
        }
        return Result.success(userVsRoleVO);
    }

    @Override
    public List<SysUser> selectBySysUserIdList(List<Long> idList) {
        return userMapper.selectList(new QueryWrapper<SysUser>().in("sys_user_id", idList));
    }

    /**
     * 人员单个进行新增
     *
     * @param dto
     */
    @Override
    public void saveUser(SysUserDTO dto) {
        log.info("人员单个新增 参数打印 , {}", JSONUtil.toJsonStr(dto));
        userMapper.insert(userConvert.dto2Po(dto));
    }

    /**
     * 人员单个更新
     *
     * @param dto
     */
    @Override
    public void updateUser(SysUserDTO dto) {
        log.info("人员单个更新 参数打印 , {}", JSONUtil.toJsonStr(dto));
        userMapper.updateBatchSelective(Collections.singletonList(userConvert.dto2Po(dto)));
    }

    /**
     * 批量查询用户
     *
     * @param dto
     * @return
     */
    @Override
    public List<SysUser> selectList(SysUserDTO dto) {
        return userMapper.selectList(new QueryWrapper<SysUser>().eq(ObjectUtil.isNotEmpty(dto.getAccount()), "account", dto.getAccount()));
    }

    @Override
    public Result userAddRoleList(SysUserDTO dto) {
        List<UserVsRoleDTO> userAddRoleList = new ArrayList<>();
        // 查询全部人员
        List<SysUser> userList = userMapper.selectList(null);
        // 查询指定的部门 根据code
        SysDeptDTO sysDeptDTO = new SysDeptDTO();
        sysDeptDTO.setDeptCategory(dto.getDeptCategory());
        Result<List<SysDept>> listResult = deptService.selectDeptList(sysDeptDTO);
        List<Long> deptIdList = listResult.getData().stream().map(vo -> vo.getDeptYunyingId()).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(listResult.getData())) {
            // 根据code 查询角色
            SysRoleDTO sysRoleDTO = new SysRoleDTO();
            sysRoleDTO.setRoleCode(dto.getRoleCode());
            Result<SysRoleVO> role = roleService.getRole(sysRoleDTO);
            // 当新增的人员所属部门包含在客服中心时，添加角色
            for (SysUser userDTO : userList) {
                if (deptIdList.contains(userDTO.getDeptId())) {
                    UserVsRoleDTO userVsRoleDTO = new UserVsRoleDTO();
                    userVsRoleDTO.setId(SnowFlakeUtil.getId());
                    userVsRoleDTO.setUserId(userDTO.getSysUserId());
                    userVsRoleDTO.setRoleId(ObjectUtil.isNotEmpty(role.getData()) ? role.getData().getSysRoleId() : -1);
                    userVsRoleDTO.setDefaultFlag(1);
                    // 查询人员是否已经含有此角色，当已经存在时候 跳过
                    userAddRoleList.add(userVsRoleDTO);
                }
            }
            // 当人员增加角色的数据不为空时  进行添加
            if (CollectionUtil.isNotEmpty(userAddRoleList)) {
                this.userSaveRoles(userAddRoleList);
            }
        }
        return Result.success();
    }

    @Override
    public Result userAddRole(SysUserDTO dto) {
        // 根据account 查询人员id
        SysUser user = userMapper.selectOne(new QueryWrapper<SysUser>().eq("account", dto.getAccount()));
        // 根据角色code 查询角色id
        SysRole sysRole = roleMapper.selectOne(new QueryWrapper<SysRole>().eq("role_code", dto.getRoleCode()));
        // 人员添加角色
        UserVsRole userVsRole = new UserVsRole();
        userVsRole.setId(SnowFlakeUtil.getId());
        userVsRole.setUserId(user.getSysUserId());
        userVsRole.setRoleId(sysRole.getSysRoleId());
        userVsRole.setIsDeleted(0);
        userVsRoleMapper.insert(userVsRole);
        return Result.success();
    }

    /**
     * token校验超时
     *
     * @return
     */
    @Override
    public Result tokenCheck() {
        return Result.success();
    }

    /**
     * 分页查询人员信息
     *
     * @param dto
     * @return
     */
    @Override
    public Result<PageInfo<SysUserPageVO>> selectUserByPage(SysUserDTO dto) {
        // 人员列表查询
        return PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> {
            List<SysUserPageVO> userList = userMapper.selectUserByPage(dto);
            // 获取人员所属部门的层级结构 “->”  进行指定
            for (SysUserPageVO user : userList) {
                if (ObjectUtil.isNotEmpty(user.getDeptId())) {
                    // 查询人员所属部门，切一直向上查询  直到 部门的pid为0,pid对应部门表的 dept_yunying_id
                    SysDept sysDept = deptMapper.selectOne(new QueryWrapper<SysDept>().eq("dept_yunying_id", user.getDeptId()));
                    if (ObjectUtil.isNotEmpty(sysDept)) {
                        deptLevelNameStr = sysDept.getDeptName();
                        if (ObjectUtil.isNotEmpty(sysDept.getPid())) {
                            getDeptNameStr(sysDept.getPid());
                        }
                    }
                    user.setDeptLevelNameStr(deptLevelNameStr);
                    // 重置为空
                    deptLevelNameStr = "";
                }
                // 查询人员所属角色，组装成 str ， 逗号分割
                UserVsRoleDTO userVsRoleDTO = new UserVsRoleDTO();
                userVsRoleDTO.setUserId(user.getSysUserId());
                userVsRoleDTO.setRoleId(dto.getSysRoleId());
                List<UserVsRoleVO> userVsRoleVOS = userVsRoleMapper.selectUserVsRoleDetail(userVsRoleDTO);
                if (CollectionUtil.isNotEmpty(userVsRoleVOS)) {
                    user.setRoleStr(StringUtils.join(userVsRoleVOS.stream().map(vo -> vo.getRoleName()).collect(Collectors.toList()).toArray(), ","));
                } else {
                    user.setRoleStr("");
                }
            }
            return Result.success(new PageInfo<>(userList));
        });
    }

    /**
     * 递归查询部门级别  字符串组装
     *
     * @param deptYunYingId
     */
    private void getDeptNameStr(Long deptYunYingId) {
        SysDept sysDept = deptMapper.selectOne(new QueryWrapper<SysDept>().eq("dept_yunying_id", deptYunYingId));
        if (ObjectUtil.isNotEmpty(sysDept)) {
            deptLevelNameStr = sysDept.getDeptName() + "->" + deptLevelNameStr;
            if (ObjectUtil.isNotEmpty(sysDept.getPid())) {
                getDeptNameStr(sysDept.getPid());
            }
        }

    }


    /**
     * 人员添加角色 --- 单独格式
     *
     * @param dto
     * @return
     */
    @Override
    public Result saveRoleByUserId(UserVsRoleSaveDTO dto) {
        if (ObjectUtil.isEmpty(dto.getUserId())) {
            return Result.fail("人员id禁止为空");
        }
        // 可能只是清除用户角色，角色列表数据为空。为空时 只删除
        if (dto.getRoleIdList().isEmpty()) {
            userVsRoleMapper.delete(new QueryWrapper<UserVsRole>().eq("user_id", dto.getUserId()));
            return Result.success();
        }
        List<UserVsRoleDTO> dtoList = new ArrayList<>();
        for (Long roleId : dto.getRoleIdList()) {
            UserVsRoleDTO userVsRoleDTO = new UserVsRoleDTO();
            userVsRoleDTO.setUserId(dto.getUserId());
            userVsRoleDTO.setRoleId(roleId);
            if (roleId.equals(dto.getIsMainRoleId())) {
                userVsRoleDTO.setIsMainRole(1);
            } else {
                userVsRoleDTO.setIsMainRole(0);
            }
            dtoList.add(userVsRoleDTO);
        }
        return this.userSaveRoles(dtoList);
    }

    @Override
    public SysUserVO getUserById(long userId) {
        SysUser sysUser = userMapper.getUserById(userId);
        if (ObjectUtil.isEmpty(sysUser)) {
            throw new RuntimeException("未查询到用户信息.");
        }
        return userConvert.po2Vo(sysUser);
    }

    /**
     * 查询所有人信息  ，不管是否为逻辑删除
     *
     * @param dto
     * @return
     */
    @Override
    public List<SysUser> selectAllUser(SysUserDTO dto) {
        return userMapper.selectAllUser(dto);
    }

    /**
     * MFA验证及登录
     *
     * @param dto
     * @param request
     * @return
     */
    @Override
    public Result loginMfa(LoginMfaDTO dto, HttpServletRequest request) {
        String accountSession = (String) request.getSession().getAttribute(ACCOUNT_CODE);
        String secretKeySession = (String) request.getSession().getAttribute(SECRET_KEY);
        // 设置尝试次数
        String attemptsKey = MFA_KEY_PREFIX + accountSession;
        String attemptsStr = (String) redisUtil.get(attemptsKey);
        // 1. 判断人员是否在交付平台存在
        SysUser user = userMapper.selectOne(new QueryWrapper<SysUser>().eq("account", accountSession));
        if (ObjectUtil.isEmpty(user)) {
            return Result.fail(ResultEnum.USER_NOT_EXIST);
        }
        String secretKey = user.getSecretKey();
        if (ObjectUtil.isEmpty(user.getSecretKey())) {
            secretKey = secretKeySession;
            user.setSecretKey(secretKeySession);
        }
        // MFA校验
        boolean verification = GoogleAuthenticatorUtils.verification(secretKey, dto.getVerificationCode());
        if (verification) {
            userMapper.updateById(user);
            // 成功后清除尝试次数
            redisUtil.del(attemptsKey);
        } else {
            if (attemptsStr != null && Integer.parseInt(attemptsStr) >= MAX_ATTEMPTS) {
                return Result.fail("您已超过最大尝试次数，请" + TIME_OUT + "分钟后重试");
            }
            int attempts = 1;
            if (attemptsStr != null) {
                attempts = Integer.parseInt(attemptsStr) + 1;
            }
            // 5分钟后过期
            redisUtil.set(attemptsKey, String.valueOf(attempts), TIME_OUT, TimeUnit.MINUTES);
            return Result.fail("验证码错误，您还有" + (MAX_ATTEMPTS - attempts) + "次机会");
        }
        // 2. 判断人员在交付平台是否有角色，当没有角色时 禁止登录
        SysUserDTO sysUserDTO = userConvert.po2Dto(user);
        List<UserVsRoleVO> userVsRoleVOS = userMapper.findRolesByUser(sysUserDTO);
        if (CollectionUtil.isEmpty(userVsRoleVOS)) {
            return Result.fail(ResultEnum.NO_AUTH);
        }
        // 3 拼装返回值
        SysUserVO userInfo = userConvert.po2Vo(user);
        // 3.1查询人员所属部门
        SysDeptDTO sysDeptDTO = new SysDeptDTO();
        sysDeptDTO.setDeptYunyingId(userInfo.getDeptId());
        SysDeptVO one = deptService.getOne(sysDeptDTO).getData();
        userInfo.setDeptName(one.getDeptName());
        String token = "CSM_T_" + Md5Util.md5(StringUtils.joinWith(StrUtil.DASHED, user.getSysUserId(), user.getAccount(), new Date().getTime()));
        userInfo.setToken(token);
        // 放置token
        redisUtil.set(token, JSON.toJSONString(userInfo), StaticPara.REDIS_CACHE_TIME, TimeUnit.HOURS);
        // 3.2 拼装角色列表
        userInfo.setUserVsRoleVOList(userVsRoleVOS);
        // 查询老系统人员id 赋值
        ImspSysUser imspSysUser = imspSysUserMapper.selectOne(new QueryWrapper<ImspSysUser>().eq("user_yunying_id", Convert.toLong(user.getUserYunyingId())).eq("status", "ENABLE"));
        // 3.3 赋值老系统人员id
        if (ObjectUtil.isNotEmpty(imspSysUser) && ObjectUtil.isNotEmpty(imspSysUser.getUserId())) {
            userInfo.setImspUserId(imspSysUser.getUserId());
        }
        // 查询客户运维平台用户角色信息
        try {
            String respStr = knowledgeFeignClient.authJf(user.getAccount(), getAuthorization());
            log.info("调用客户运维平台鉴权接口，返回结果：{}", respStr);
            ResponseResult responseResult = JSON.parseObject(respStr, ResponseResult.class);
            if (responseResult.getSuccess() && responseResult.getData() != null) {
                userInfo.setKnowUserInfo(JSONObject.parseObject(responseResult.getData().toString()));
            }
        } catch (Exception e) {
            log.error("调用客户运维平台鉴权接口，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        return Result.success(userInfo);
    }

    /**
     * 清空登录人的mfa认证信息
     *
     * @param dto
     * @return
     */
    @Override
    public Result mfaRemoveData(LoginDTO dto) {
        userMapper.mfaRemoveData(dto.getAccount());
        return Result.success();
    }

    /**
     * 将云健康小组的人员添加运维工程师角色
     */
    @Override
    public void updateUserRole() {
        userMapper.updateUserRole();
    }


    /**
     * 退出登录
     *
     * @return
     */
    @Override
    public Result logout(HttpServletRequest request) {
        // 清除缓存
        String token = request.getHeader(StaticPara.HEADER_TOKEN_NAME);
        redisUtil.del(token);
        return Result.success("退出成功");
    }
}
