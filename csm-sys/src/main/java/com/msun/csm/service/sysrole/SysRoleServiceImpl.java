package com.msun.csm.service.sysrole;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.msun.csm.util.PageHelperUtil;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.RoleVsMenu;
import com.msun.csm.dao.entity.SysRole;
import com.msun.csm.dao.mapper.sysrole.RoleVsMenuMapper;
import com.msun.csm.dao.mapper.sysrole.SysRoleMapper;
import com.msun.csm.model.convert.RoleVsMenuConvert;
import com.msun.csm.model.convert.SysRolesConvert;
import com.msun.csm.model.dto.role.RoleVsMenuDTO;
import com.msun.csm.model.dto.role.SysRoleDTO;
import com.msun.csm.model.dto.role.SysRolePageDTO;
import com.msun.csm.model.dto.user.UserVsRoleDTO;
import com.msun.csm.model.vo.menu.RouterMenuVO;
import com.msun.csm.model.vo.menu.SysMenuVO;
import com.msun.csm.model.vo.role.SysRoleVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.model.vo.user.UserVsRoleVO;
import com.msun.csm.service.sysuser.SysUserService;
import com.msun.csm.util.SnowFlakeUtil;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/18/10:54
 */
@Service
public class SysRoleServiceImpl implements SysRoleService {


    private final UserHelper userHelper;
    @Resource
    private SysRolesConvert convert;
    @Resource
    private SysRoleMapper mapper;
    @Resource
    private SysUserService sysUserService;
    @Resource
    private RoleVsMenuMapper roleVsMenuMapper;
    @Resource
    private RoleVsMenuConvert roleVsMenuConvert;


    public SysRoleServiceImpl(UserHelper userHelper) {
        this.userHelper = userHelper;
    }


    /**
     * 增加角色
     *
     * @param dto
     * @return
     */
    @Override
    public Result saveOrUpdateRole(SysRoleDTO dto) {
        if (ObjectUtil.isNotEmpty(dto.getSysRoleId())) {
            this.updateRole(dto);
        } else {
            dto.setSysRoleId(SnowFlakeUtil.getId());
            SysRole sysRole = convert.dto2Po(dto);
            // 检测角色编码是否重复
            List<SysRole> sysRoles = mapper.selectList(new QueryWrapper<SysRole>()
                    .eq("role_code", dto.getRoleCode())
            );
            if (CollectionUtil.isNotEmpty(sysRoles)) {
                return Result.fail("该角色编码已存在,请重设编码");
            }
            mapper.insert(sysRole);
        }
        return Result.success();
    }

    /**
     * 删除角色
     *
     * @param dto
     * @return
     */
    @Override
    public Result removeRole(SysRoleDTO dto) {
        // 判断当前角色是否被用户使用
        List<Long> userIds = mapper.selectUserListByRole(dto.getSysRoleId());
        if (CollectionUtil.isNotEmpty(userIds)) {
            return Result.fail("该角色已绑定用户，无法删除");
        }
        dto.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
        dto.setUpdateTime(new Date());
        mapper.removeById(dto);
        return Result.success();
    }

    /**
     * 修改角色
     *
     * @param dto
     * @return
     */
    @Override
    public Result updateRole(SysRoleDTO dto) {
        mapper.updateById(convert.dto2Po(dto));
        return Result.success();
    }

    /**
     * 查询单个角色
     *
     * @param dto
     * @return
     */
    @Override
    public Result<SysRoleVO> getRole(SysRoleDTO dto) {
        SysRole sysRoles = mapper.selectOne(new QueryWrapper<SysRole>()
                .eq(ObjectUtil.isNotEmpty(dto.getSysRoleId()), "sys_role_id", dto.getSysRoleId())
                .like(ObjectUtil.isNotEmpty(dto.getRoleName()), "role_name", dto.getRoleName())
                .eq(ObjectUtil.isNotEmpty(dto.getRoleCode()), "role_code", dto.getRoleCode())
        );
        return Result.success(convert.po2Vo(sysRoles));
    }

    /**
     * 批量查询角色
     *
     * @param dto
     * @return
     */
    @Override
    public Result<PageInfo<SysRoleVO>> selectRoleListByPage(SysRolePageDTO dto) {
        return PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> {
            List<SysRole> sysRolesList = mapper.selectList(new QueryWrapper<SysRole>()
                    .eq(ObjectUtil.isNotEmpty(dto.getSysRoleId()), "sys_role_id", dto.getSysRoleId())
                    .like(ObjectUtil.isNotEmpty(dto.getRoleName()), "role_name", dto.getRoleName())
                    .like(ObjectUtil.isNotEmpty(dto.getRoleCode()), "role_code", dto.getRoleCode())
            );
            PageInfo<SysRoleVO> pageInfo = new PageInfo<>();
            pageInfo.setList(convert.po2Vo(sysRolesList));
            return Result.success(pageInfo);
        });
    }

    /**
     * 根据角色查询菜单
     *
     * @param dto
     * @return
     */
    @Override
    public Result<List<RouterMenuVO>> findMenuByRole(UserVsRoleDTO dto) {
        // 查询人员下 有哪些角色
        Result<List<UserVsRoleVO>> result = sysUserService.findRoleByUser(dto);
        if (result.isSuccess() && CollectionUtil.isEmpty(result.getData())) {
            throw new RuntimeException("该用户没有角色");
        }
        List<UserVsRoleVO> data = result.getData();
        // 根据人员下的角色列表查询 菜单
        List<Long> roleIdList = data.stream().map(vo -> vo.getRoleId()).collect(Collectors.toList());
        dto.setRoleList(roleIdList);
        List<RouterMenuVO> menuTree = new ArrayList<>();
        List<SysMenuVO> menuVOList = roleVsMenuMapper.findMenuByRole(dto);
        for (SysMenuVO nodeA : menuVOList) {
            if (nodeA.getPid() == 0) {
                RouterMenuVO node = new RouterMenuVO();
                node.setName(nodeA.getRouterName());
                node.setPath(nodeA.getMenuUrl());
                node.setComponent(nodeA.getComponent());
                node.setIcon(nodeA.getMenuIcon());
                node.setComponentName(nodeA.getMenuName());
                node.setName(nodeA.getRouterName());
                node.setMenuSort(nodeA.getMenuSort());
                node.setShowMenu(nodeA.getShowMenu());
                // 创建子集
                List<RouterMenuVO> childList = new ArrayList<>();
                for (SysMenuVO nodeB : menuVOList) {
                    if (nodeA.getSysMenuId().equals(nodeB.getPid())) {
                        // 子菜单数据写入
                        RouterMenuVO childNode = new RouterMenuVO();
                        childNode.setName(nodeB.getRouterName());
                        childNode.setPath(nodeB.getMenuUrl());
                        childNode.setComponent(nodeB.getComponent());
                        childNode.setIcon(nodeB.getMenuIcon());
                        childNode.setComponentName(nodeB.getMenuName());
                        childNode.setName(nodeB.getRouterName());
                        childNode.setMenuSort(nodeB.getMenuSort());
                        childNode.setShowMenu(nodeB.getShowMenu());
                        childNode.setChildren(new ArrayList<>());
                        childList.add(childNode);
                    }
                }
                // 子菜单放入父级中
                node.setChildren(childList);
                menuTree.add(node);
            }
        }
        List<RouterMenuVO> collect = menuTree.stream().sorted(Comparator.comparing(RouterMenuVO::getMenuSort)).collect(Collectors.toList());
        return Result.success(collect);
    }

    /**
     * 角色批量保存菜单
     *
     * @param dtoList
     * @return
     */
    @Override
    public Result roleSaveMenu(List<RoleVsMenuDTO> dtoList) {
        List<RoleVsMenu> roleVsMenus = roleVsMenuConvert.dto2Po(dtoList);
        // 主键id赋值
        for (RoleVsMenu roleVsMenu : roleVsMenus) {
            roleVsMenu.setId(SnowFlakeUtil.getId());
            roleVsMenu.setIsDeleted(0);
            roleVsMenu.setCreaterId(userHelper.getCurrentUser().getSysUserId());
            roleVsMenu.setCreateTime(new Date());
            roleVsMenu.setUpdaterId(userHelper.getCurrentUser().getSysUserId());
            roleVsMenu.setUpdateTime(new Date());
        }
        roleVsMenuMapper.batchInsert(roleVsMenus);
        return Result.success();
    }

    /**
     * 查询全部角色
     *
     * @param dto
     * @return
     */
    @Override
    public Result<List<SysRoleVO>> selectRoleList(SysRoleDTO dto) {
        List<SysRole> sysRoles = mapper.selectList(new QueryWrapper<SysRole>()
                .like(ObjectUtil.isNotEmpty(dto.getRoleName()), "role_name", dto.getRoleName())
        );
        return Result.success(convert.po2Vo(sysRoles));
    }
}
