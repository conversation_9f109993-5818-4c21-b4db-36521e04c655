package com.msun.csm.service.config;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysConfig;
import com.msun.csm.dao.mapper.config.SysConfigMapper;
import com.msun.csm.model.convert.SysConfigConvert;
import com.msun.csm.model.dto.RedisControlDTO;
import com.msun.csm.model.dto.SysConfigDTO;
import com.msun.csm.model.vo.SysConfigVO;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 配置表(Config)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-13 15:02:35
 */
@Slf4j
@Service("configService")
public class SysConfigServiceImpl implements SysConfigService {

    @Resource
    private SysConfigMapper sysConfigMapper;
    @Resource
    private RedisControlService redisControlService;
    @Resource
    private SysConfigConvert sysconfigConvert;


    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public SysConfigVO getConfigById(Long id) {
        //1.根据id获取详细信息
        SysConfig po = this.sysConfigMapper.selectByPrimaryKey(id);
        //2.转换为vo后返回
        SysConfigVO entity = this.sysconfigConvert.po2Vo(po);
        ////封装字符串==>数组转换
        return entity;

    }

    /**
     * 说明:
     *
     * @param configCode
     * @return:com.msun.csm.dao.entity.config.entity.vo.SysConfigVO
     * @author: Yhongmin
     * @createAt: 2024/5/13 16:53
     * @remark: Copyright
     */
    @Override
    public SysConfigVO selectConfigByName(String configCode) {
        SysConfig po = this.sysConfigMapper.selectConfigByName(configCode);
        SysConfigVO entity = this.sysconfigConvert.po2Vo(po);
        return entity;
    }

    /**
     * 新增数据
     *
     * @param entity 实例对象
     * @return 成功为1，失败为0
     */
    @Override
    public Result<String> save(SysConfigDTO entity) {
        SysConfig sysConfig = this.sysConfigMapper.selectConfigByName(entity.getConfigCode());
        if (sysConfig != null) {
            return Result.fail("该配置已存在");
        }
        entity.preInsert();
        SysConfig po = this.sysconfigConvert.dto2Po(entity);
        int insert = this.sysConfigMapper.insert(po);
        if (insert == 0) {
            return Result.fail("保存异常");
        }
        redisControlService.refreshCache(null);
        return Result.success();
    }

    /**
     * 修改数据
     *
     * @param entity 实例对象
     * @return 实例对象
     */
    @Override
    public Result<String> update(SysConfigDTO entity) {
        log.debug("查修改数据:entity={}", entity);
        //封装数组转换==>字符串
        entity.preUpdate();
        //1.将新增页面中的信息转换为po对象
        SysConfig po = this.sysconfigConvert.dto2Po(entity);
        int update = this.sysConfigMapper.updateByPrimaryKey(po);
        if (update == 0) {
            return Result.fail("保存异常");
        }
        redisControlService.refreshCache(null);
        return Result.success();
    }


    /**
     * 通过主键删除作废
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public Result<String> updateMcInvalid(Long id) {
        SysConfig po = new SysConfig();
        //调用作废工具
        po.preInvalid();
        //封装主键
        int sun = this.sysConfigMapper.updateMcInvalid(po);
        if (sun > 0) {
            redisControlService.refreshCache(null);
            return Result.success();
        } else {
            return Result.fail("保存异常");
        }
    }

    /**
     * 说明: 刷新缓存
     *
     * @param configCode
     * @return:com.msun.csm.common.model.Result<java.lang.String>
     * @author: Yhongmin
     * @createAt: 2024/8/21 9:27
     * @remark: Copyright
     */
    @Override
    public Result<String> refreshCacheSysConfig(String configCode) {
            RedisControlDTO redisControlDTO = new RedisControlDTO();
            redisControlDTO.setCacheType(configCode);
            String cacheCode = redisControlService.refreshCache(ObjectUtil.isEmpty(configCode) ? null : redisControlDTO);
            return Result.success(cacheCode);
    }
}
