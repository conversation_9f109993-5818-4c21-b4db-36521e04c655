package com.msun.csm.service.config;

import com.msun.csm.model.dto.RedisControlDTO;

/**
 * @version : V1.52.0
 * @ClassName: RedisControlService
 * @Description:
 * @Author: Yhongmin
 * @Date: 17:53 2024/5/13
 */
public interface RedisControlService {
    /**
     * 缓存刷新-根据缓存类型
     *
     * @param redisControlDTO
     * @return java.lang.String
     */
    String refreshCache(RedisControlDTO redisControlDTO);
}
