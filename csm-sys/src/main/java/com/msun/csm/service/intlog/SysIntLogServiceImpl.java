package com.msun.csm.service.intlog;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.common.staticvariable.StaticPara;
import com.msun.csm.dao.entity.SysIntLog;
import com.msun.csm.dao.mapper.intlog.IntLogMapper;
import com.msun.csm.model.convert.SysIntLogConvert;
import com.msun.csm.model.dto.intlog.SysIntLogDTO;
import com.msun.csm.model.vo.intlog.SysIntLogVO;
import com.msun.csm.util.PageHelperUtil;

/**
 * 操作日志实现类
 */
@Slf4j
@Service
public class SysIntLogServiceImpl implements SysIntLogService {

    @Resource
    private IntLogMapper intLogMapper;

    @Resource
    private SysIntLogConvert convert;

    @Override
    public int insert(SysIntLog intLog) {
        return intLogMapper.insert(intLog);
    }

    @Override
    public Result<PageInfo<SysIntLogVO>> selectIntLogList(SysIntLogDTO dto) {
        return PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> {
            Date startTime = StrUtil.isNotBlank(dto.getStartTime()) ? DateUtil.parse(
                    dto.getStartTime() + StaticPara.STANDARD_START_TIME).toJdkDate() : null;
            Date endTime =
                    StrUtil.isNotBlank(dto.getEndTime()) ? DateUtil.parse(dto.getEndTime() + StaticPara.STANDARD_END_TIME)
                            .toJdkDate() : null;
            List<SysIntLog> sysIntLogs = intLogMapper.selectList(new QueryWrapper<SysIntLog>()
                    .between(StrUtil.isAllNotBlank(dto.getStartTime(), dto.getEndTime()), "create_time", startTime, endTime)
                    .ge(StrUtil.isNotBlank(dto.getStartTime()) && StrUtil.isBlank(dto.getEndTime()), "create_time",
                            startTime)
                    .le(StrUtil.isNotBlank(dto.getEndTime()) && StrUtil.isBlank(dto.getStartTime()), "create_time", endTime)
                    .eq(dto.getTypeCode() != 0, "type_code", dto.getTypeCode())
                    .like(StrUtil.isNotBlank(dto.getCnIntName()), "cn_int_name", dto.getCnIntName())
                    .like(StrUtil.isNotBlank(dto.getErrDetail()), "err_detail", dto.getErrDetail())
                    .orderByDesc("create_time")
            );
            PageInfo<SysIntLog> pageInfo = new PageInfo<>(sysIntLogs);
            List<SysIntLogVO> vos = convert.po2Vo(sysIntLogs);
            PageInfo<SysIntLogVO> pageInfoVO = PageInfo.of(vos);
            // 转换字段类型
            vos.stream().map(e -> {
                Log.IntLogType intLogType = Log.IntLogType.getIntLogType(e.getTypeCode());
                e.setShowColor(intLogType.getColor());
                e.setTypeName(intLogType.getDesc());
                return e;
            }).collect(Collectors.toList());
            pageInfoVO.setTotal(pageInfo.getTotal());
            return Result.success(pageInfoVO);
        });
    }
}
