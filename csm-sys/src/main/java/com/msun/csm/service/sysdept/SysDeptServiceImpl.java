package com.msun.csm.service.sysdept;

import java.util.List;

import javax.annotation.Resource;

import com.msun.csm.dao.entity.SysDeptSub;
import com.msun.csm.dao.mapper.sysdept.SysDeptSubMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysDept;
import com.msun.csm.dao.mapper.sysdept.SysDeptMapper;
import com.msun.csm.model.convert.SysDeptConvert;
import com.msun.csm.model.dto.dept.SysDeptDTO;
import com.msun.csm.model.vo.dept.SysDeptVO;

import cn.hutool.core.util.ObjectUtil;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/23/11:08
 */
@Service
public class SysDeptServiceImpl implements SysDeptService {

    @Resource
    private SysDeptMapper sysDeptMapper;

    @Autowired
    private SysDeptSubMapper sysDeptSubMapper;

    @Resource
    private SysDeptConvert deptConvert;

    /**
     * 查询单个部门信息
     *
     * @param dto
     * @return
     */
    @Override
    public Result<SysDeptVO> getOne(SysDeptDTO dto) {
        SysDept sysDept = sysDeptMapper.selectOne(new QueryWrapper<SysDept>()
                .eq(ObjectUtil.isNotEmpty(dto.getDeptName()), "dept_name", dto.getDeptName())
                .eq(ObjectUtil.isNotEmpty(dto.getDeptYunyingId()), "dept_yunying_id", dto.getDeptYunyingId())
        );
        return Result.success(deptConvert.po2Vo(sysDept));
    }

    /**
     * 更新部门信息
     *
     * @param dto
     * @return
     */
    @Override
    public Result updateDept(SysDeptDTO dto) {
        sysDeptMapper.updateById(deptConvert.dto2Po(dto));
        return Result.success();
    }

    /**
     * 保存部门信息
     *
     * @param dto
     * @return
     */
    @Override
    public Result insertDept(SysDeptDTO dto) {
        sysDeptMapper.insert(deptConvert.dto2Po(dto));
        return Result.success();
    }

    /**
     * 查询部门列表
     *
     * @param dto
     * @return
     */
    @Override
    public Result<List<SysDept>> selectDeptList(SysDeptDTO dto) {
        List<SysDept> sysDepts = sysDeptMapper.selectList(new QueryWrapper<SysDept>()
                .eq(ObjectUtil.isNotEmpty(dto.getDeptCategory()), "dept_category", dto.getDeptCategory())
                .eq(ObjectUtil.isNotEmpty(dto.getPid()), "pid", dto.getPid())
                .in(ObjectUtil.isNotEmpty(dto.getPidList()), "pid", dto.getPidList())
        );
        return Result.success(sysDepts);
    }

    @Override
    public Result saveSubDept(SysDeptSub sysDeptSub) {
        try {
            // 删除原部门的所有副职
            sysDeptSubMapper.delete(new QueryWrapper<SysDeptSub>().eq("sys_dept_id", sysDeptSub.getSysDeptId()));
            // 添加副职
            sysDeptSubMapper.insert(sysDeptSub);
            return Result.success("保存部门副职成功");
        } catch (Exception e) {
            return Result.fail("保存部门副职失败");
        }
    }
}
