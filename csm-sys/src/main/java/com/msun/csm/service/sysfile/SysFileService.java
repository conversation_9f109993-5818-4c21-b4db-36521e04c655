package com.msun.csm.service.sysfile;

import java.util.List;
import java.util.Map;

import org.springframework.web.multipart.MultipartFile;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysFile;
import com.msun.csm.model.dto.SysFileDTO;
import com.msun.csm.model.dto.SysFilePageDTO;
import com.msun.csm.model.vo.SysFilePageVO;
import com.msun.csm.model.vo.SysFileVO;
import com.msun.csm.model.vo.file.FileBusinessCodeNameVO;
import com.msun.csm.model.vo.file.FileCodeTitleNameVO;
import com.msun.csm.model.vo.file.SysUploadFileVO;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/28/17:54
 */
public interface SysFileService {
    /**
     * 说明: 分页查询
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result<com.github.pagehelper.PageInfo<com.msun.csm.entity.vo.SysFilePageVO>>
     * @author: Yhongmin
     * @createAt: 2024/5/14 15:36
     * @remark: Copyright
     */
    Result<PageInfo<SysFilePageVO>> findByPage(SysFilePageDTO dto);

    /**
     * 根据条件查询文件信息
     *
     * @param sysFile
     * @return
     */
    Result<SysFile> selectFile(SysFile sysFile);

    /**
     * 说明: 页面初始化查询所有的业务编码和业务编码名称
     *
     * @return:com.msun.csm.common.model.Result<com.msun.csm.model.vo.file.FileBusinessCodeName>
     * @author: Yhongmin
     * @createAt: 2024/6/11 14:23
     * @remark: Copyright
     */
    Result<List<FileBusinessCodeNameVO>> findBusinessCode();

    /**
     * 说明: 根据业务code获取文件code
     *
     * @param businessCode
     * @return:com.msun.csm.common.model.Result<java.util.List<com.msun.csm.model.vo.file.FileCodeTitleNameVO>>
     * @author: Yhongmin
     * @createAt: 2024/6/11 14:53
     * @remark: Copyright
     */
    Result<List<FileCodeTitleNameVO>> fingCodeTitleName(String businessCode);


    List<SysFile> findBySysFileCodeBusinessCode(String businessCode);

    /**
     * @param businessCode 业务编码
     * @param needTmpUrl   是否需要将相对路径转换为临时路径, https开头的obs绝对路径, 可用于回显图片等
     * @return List<SysFile>
     */
    List<SysFile> findBySysFileCodeBusinessCode(String businessCode, boolean needTmpUrl);


    Map<String, String> findMapBySysFileCodeBusinessCode(String businessCode);

    /**
     * 说明:
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result<java.lang.String>
     * @author: Yhongmin
     * @createAt: 2024/5/14 14:18
     * @remark: Copyright
     */
    Result save(SysFileDTO dto);

    /**
     * 说明:
     *
     * @param file
     * @return:com.msun.csm.common.model.Result<java.lang.String>
     * @author: Yhongmin
     * @createAt: 2024/5/14 14:18
     * @remark: Copyright
     */
    Result<SysUploadFileVO> uploadFile(MultipartFile file);

    /**
     * 说明:
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result
     * @author: Yhongmin
     * @createAt: 2024/5/17 11:40
     * @remark: Copyright
     */
    Result uploadFileByBusiness(SysFileDTO dto, MultipartFile file);

    /**
     * 说明:
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result<java.lang.String>
     * @author: Yhongmin
     * @createAt: 2024/5/14 14:18
     * @remark: Copyright
     */
    Result<String> update(SysFileDTO dto);

    /**
     * 说明:
     *
     * @param id
     * @return:com.msun.csm.common.model.Result<java.lang.String>
     * @author: Yhongmin
     * @createAt: 2024/5/14 14:18
     * @remark: Copyright
     */
    Result<String> updateIsDelete(Long id);

    /**
     * 说明:
     *
     * @param id
     * @return:com.msun.csm.common.model.Result<com.msun.csm.dao.entity.config.entity.vo.SysFileVO>
     * @author: Yhongmin
     * @createAt: 2024/5/14 14:33
     * @remark: Copyright
     */
    Result<SysFileVO> getSysFileById(Long id);

    Result<SysUploadFileVO> uploadFilesCustomize(MultipartFile file, String pathName, Boolean isPublic);
}
