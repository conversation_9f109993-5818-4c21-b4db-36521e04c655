package com.msun.csm.service.sysfile;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.msun.csm.common.enums.NumberEnum;
import com.msun.csm.common.enums.config.SysFileCodeEnum;
import com.msun.csm.common.model.Result;
import com.msun.csm.config.Global;
import com.msun.csm.dao.entity.SysFile;
import com.msun.csm.dao.mapper.sysfile.SysFileMapper;
import com.msun.csm.model.convert.SysFileConvert;
import com.msun.csm.model.convert.SysFilePageConvert;
import com.msun.csm.model.dto.SysFileDTO;
import com.msun.csm.model.dto.SysFilePageDTO;
import com.msun.csm.model.vo.SysFilePageVO;
import com.msun.csm.model.vo.SysFileVO;
import com.msun.csm.model.vo.file.FileBusinessCodeNameVO;
import com.msun.csm.model.vo.file.FileCodeTitleNameVO;
import com.msun.csm.model.vo.file.SysUploadFileVO;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.util.DateUtil;
import com.msun.csm.util.PageHelperUtil;
import com.msun.csm.util.SnowFlakeUtil;
import com.msun.csm.util.obs.OBSClientUtils;
import com.obs.services.model.PutObjectResult;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/28/17:55
 */
@Slf4j
@Service
public class SysFileServiceImpl implements SysFileService {

    @Resource
    private SysFileMapper sysFileMapper;
    @Resource
    private SysFileConvert sysFileConvert;
    @Resource
    private SysFilePageConvert sysFilePageConvert;
    @Value("${project.obs.prePath}")
    private String prePath;


    /**
     * 说明: 分页查询
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result<com.github.pagehelper.PageInfo<com.msun.csm.entity.vo.SysFilePageVO>>
     * @author: Yhongmin
     * @createAt: 2024/5/14 15:36
     * @remark: Copyright
     */
    @Override
    public Result<PageInfo<SysFilePageVO>> findByPage(SysFilePageDTO dto) {
        return PageHelperUtil.queryPage(dto.getPageNum(), dto.getPageSize(), page -> {
            SysFile sysFile = sysFilePageConvert.dto2Po(dto);
            List<SysFile> files = sysFileMapper.selectAll(sysFile);
            return Result.success(new PageInfo<>(sysFilePageConvert.po2Vo(files)));
        });
    }

    /**
     * 根据条件查询文件
     *
     * @param sysFile
     * @return
     */
    @Override
    public Result<SysFile> selectFile(SysFile sysFile) {
        SysFile file = sysFileMapper.selectByFileCodeTopOne(sysFile);
        if (file == null) {
            return Result.fail("文件不存在");
        }
        if (NumberEnum.NO_0.num().equals(file.getFileUsedMode())) {
            //默认有效时间1小时
            file.setFilePath(OBSClientUtils.getTemporaryUrl(file.getFilePath(), 3600));
        }
        return Result.success(file);
    }

    /**
     * 说明: 页面初始化查询所有的业务编码和业务编码名称
     *
     * @return:com.msun.csm.common.model.Result<com.msun.csm.model.vo.file.FileBusinessCodeName>
     * @author: Yhongmin
     * @createAt: 2024/6/11 14:23
     * @remark: Copyright
     */
    @Override
    public Result<List<FileBusinessCodeNameVO>> findBusinessCode() {
        List<FileBusinessCodeNameVO> voList = sysFileMapper.findBusinessCode();
        return Result.success(voList.size() > 0 ? voList : new ArrayList<>());
    }

    /**
     * 说明: 根据业务code获取文件code
     *
     * @param businessCode
     * @return:com.msun.csm.common.model.Result<java.util.List<com.msun.csm.model.vo.file.FileCodeTitleNameVO>>
     * @author: Yhongmin
     * @createAt: 2024/6/11 14:53
     * @remark: Copyright
     */
    @Override
    public Result<List<FileCodeTitleNameVO>> fingCodeTitleName(String businessCode) {
        List<FileCodeTitleNameVO> voList = sysFileMapper.fingCodeTitleName(businessCode);
        return Result.success(voList.size() > 0 ? voList : new ArrayList<>());
    }

    /**
     * @param businessCode
     * @return
     */
    @Override
    public List<SysFile> findBySysFileCodeBusinessCode(String businessCode) {
        return findBySysFileCodeBusinessCode(businessCode, true);
    }

    public List<SysFile> findBySysFileCodeBusinessCode(String businessCode, boolean needTmpUrl) {
        List<SysFile> codeList = sysFileMapper.findBySysFileCodeBusinessCode(businessCode);
        if (codeList == null || codeList.size() == 0) {
            return codeList;
        }
        if (needTmpUrl) {
            codeList.stream().filter(fe -> NumberEnum.NO_0.num().equals(fe.getFileUsedMode()))
                    .forEach(fe -> fe.setFilePath(OBSClientUtils.getTemporaryUrl(fe.getFilePath(), 3600)));
        }
        return codeList;
    }

    /**
     * @param businessCode
     * @return
     */
    @Override
    public Map<String, String> findMapBySysFileCodeBusinessCode(String businessCode) {
        List<SysFile> fileList = this.findBySysFileCodeBusinessCode(businessCode);
        if (fileList == null || fileList.size() == 0) {
            return null;
        }
        Map<String, String> map = fileList.stream()
                .collect(Collectors.toMap(SysFile::getFileCode, SysFile::getFilePath));
        return map;
    }

    /**
     * 说明:
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result<java.lang.String>
     * @author: Yhongmin
     * @createAt: 2024/5/14 14:18
     * @remark: Copyright
     */
    @Override
    public Result save(SysFileDTO dto) {
        try {
            dto.preInsert();
            SysFile srcFile = sysFileConvert.dto2Po(dto);
            srcFile.setBusinessDesc(SysFileCodeEnum.getByCode(srcFile.getBusinessCode()).getCodeDesc());
            srcFile.setSysFileId(SnowFlakeUtil.getId());
            srcFile.setFileUsedMode(dto.getFileUsedMode() == null ? NumberEnum.NO_0.num() : dto.getFileUsedMode());
            int insert = sysFileMapper.insert(srcFile);
            if (insert > 0) {
                return Result.success("新增成功");
            }
        } catch (Exception e) {
            log.error("新增，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        return Result.fail("新增失败");
    }

    /**
     * 说明:
     *
     * @param file
     * @return:com.msun.csm.common.model.Result<java.lang.String>
     * @author: Yhongmin
     * @createAt: 2024/5/14 14:18
     * @remark: Copyright
     */
    @Override
    public Result<SysUploadFileVO> uploadFile(MultipartFile file) {
        return uploadFilesCustomize(file, null, null);
    }

    /**
     * 说明:
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result<java.lang.String>
     * @author: Yhongmin
     * @createAt: 2024/5/14 14:18
     * @remark: Copyright
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result uploadFileByBusiness(SysFileDTO dto, MultipartFile file) {
        try {
            SysUserVO user = Global.getSysUserVO();
            dto.preInsert();
            SysFile srcFile = sysFileConvert.dto2Po(dto);
            String pathName =
                    prePath + dto.getFileCode() + StrUtil.SLASH + user.getSysUserId() + StrUtil.SLASH + DateUtil.getTodayString() + StrUtil.SLASH
                            + DateUtil.getHourMinuteSecondString() + StrUtil.SLASH + file.getOriginalFilename();
            PutObjectResult putObjectResult = OBSClientUtils.uploadMultipartFile(file, pathName, false);
            srcFile.setFileUsedMode(0);
            srcFile.setFilePath(pathName);
            srcFile.setSysFileId(SnowFlakeUtil.getId());
            srcFile.setFileName(file.getOriginalFilename());
            sysFileMapper.insert(srcFile);
            return Result.success(OBSClientUtils.getTemporaryUrl(pathName, 3600));
        } catch (Exception e) {
            log.error("uploadFileByBusiness，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        return Result.fail("上传失败");
    }

    /**
     * 说明:
     *
     * @param dto
     * @return:com.msun.csm.common.model.Result<java.lang.String>
     * @author: Yhongmin
     * @createAt: 2024/5/14 14:18
     * @remark: Copyright
     */
    @Override
    public Result<String> update(SysFileDTO dto) {
        dto.preUpdate();
        SysFile srcFile = sysFileConvert.dto2Po(dto);
        srcFile.setBusinessDesc(SysFileCodeEnum.getByCode(srcFile.getBusinessCode()).getCodeDesc());
        int insert = sysFileMapper.updateByPrimaryKey(srcFile);
        if (insert > 0) {
            return Result.success("修改成功");
        }
        return Result.fail("修改失败");
    }

    /**
     * 说明:
     *
     * @param id
     * @return:com.msun.csm.common.model.Result<java.lang.String>
     * @author: Yhongmin
     * @createAt: 2024/5/14 14:18
     * @remark: Copyright
     */
    @Override
    public Result<String> updateIsDelete(Long id) {
        SysFile srcFile = new SysFile();
        srcFile.setSysFileId(id);
        srcFile.setIsDeleted(1);
        int insert = sysFileMapper.updateIsDelete(srcFile);
        if (insert > 0) {
            return Result.success("删除成功");
        }
        return Result.fail("删除失败");
    }

    /**
     * 说明:
     *
     * @param id
     * @return:com.msun.csm.common.model.Result<com.msun.csm.dao.entity.config.entity.vo.SysFileVO>
     * @author: Yhongmin
     * @createAt: 2024/5/14 14:18
     * @remark: Copyright
     */
    @Override
    public Result<SysFileVO> getSysFileById(Long id) {
        SysFile sysFile = sysFileMapper.selectByPrimaryKey(id);
        if (sysFile != null) {
            return Result.success(sysFileConvert.po2Vo(sysFile));
        }
        return Result.fail("查询失败");
    }

    @Override
    public Result<SysUploadFileVO> uploadFilesCustomize(MultipartFile file, String pathName, Boolean isPublic) {
        try {
            SysUserVO user = Global.getSysUserVO();
            // 若路径未指定, 则默认拼接
            if (StrUtil.isBlank(pathName)) {
                pathName =
                        prePath + user.getSysUserId() + StrUtil.SLASH + DateUtil.getTodayString() + StrUtil.SLASH
                                + DateUtil.getHourMinuteSecondString() + StrUtil.SLASH + file.getOriginalFilename();
            } else {
                if (pathName.indexOf(StrUtil.SLASH) == 0) {
                    pathName = pathName.substring(1);
                }
            }
            // 若是否永久访问标识未指定, 则默认私有
            if (ObjectUtil.isEmpty(isPublic)) {
                isPublic = false;
            }
            log.info("上传obs路径: {}, 是否永久: {}", pathName, isPublic);
            PutObjectResult putObjectResult = OBSClientUtils.uploadMultipartFile(file, pathName, isPublic);
            log.info("上传成功={}", JSON.toJSONString(putObjectResult));
            SysUploadFileVO sendFile = new SysUploadFileVO();
            sendFile.setFileName(file.getOriginalFilename());
            sendFile.setFilePath(putObjectResult.getObjectKey());
            sendFile.setFileUrl(OBSClientUtils.getTemporaryUrl(putObjectResult.getObjectKey(), 3600));
            return Result.success(sendFile);
        } catch (Exception e) {
            log.error("上传文件，发生异常，errMsg={}，stackInfo=", e.getMessage(), e);
        }
        return Result.fail("上传失败");
    }
}
