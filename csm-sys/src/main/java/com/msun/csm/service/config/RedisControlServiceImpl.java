package com.msun.csm.service.config;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.msun.csm.common.enums.ResultEnum;
import com.msun.csm.common.enums.config.RedisKeyPrefixEnum;
import com.msun.csm.config.SysConfigRedisReadUtil;
import com.msun.csm.dao.entity.SysConfig;
import com.msun.csm.dao.mapper.config.SysConfigMapper;
import com.msun.csm.model.dto.RedisControlDTO;
import com.msun.csm.util.RedisUtil;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @version : V1.52.0
 * @ClassName: RedisControlServiceImpl
 * @Description:
 * @Author: Yhongmin
 * @Date: 17:53 2024/5/13
 */
@Slf4j
@Service
public class RedisControlServiceImpl implements RedisControlService {
    @Resource
    private SysConfigMapper sysConfigMapper;
    @Resource
    RedisUtil redisUtil;

    /**
     * 缓存刷新-根据缓存类型
     *
     * @param redisControlDTO
     * @return java.lang.String
     */
    @Override
    public String refreshCache(RedisControlDTO redisControlDTO) {
        long start = System.currentTimeMillis();
        log.info("RedisControlService.refreshCache(): redisControlDTO={}", redisControlDTO);
        log.info("缓存初始化开始: ");
        if (redisControlDTO == null || ObjectUtil.isEmpty(redisControlDTO.getCacheType())) {
            List<SysConfig> resultList = this.sysConfigMapper.selectAllConfig();
            if (resultList == null || resultList.size() == 0) {
                return "系统配置暂无数据";
            }
            resultList.stream().forEach(sysConfig -> {
                initSysConfigCache(sysConfig);
            });
        } else {
            SysConfig sysConfig = this.sysConfigMapper.selectConfigByName(redisControlDTO.getCacheType());
            if (sysConfig != null) {
                initSysConfigCache(sysConfig);
            }
        }
        double cost = (System.currentTimeMillis() - start) / 1000.000;
        log.info("缓存初始化完成 所用时间 {}", cost + " seconds");
        return ResultEnum.SUCCESS.getDesc();
    }

    public void initSysConfigCache(SysConfig sysConfig) {
        if (sysConfig == null) {
            return;
        }
        // 删除全部
        redisUtil.del(RedisKeyPrefixEnum.SYS_CONFIG.getKeyPrefix() + sysConfig.getConfigCode());
        // redis新增有效
        SysConfigRedisReadUtil.putAllMap(RedisKeyPrefixEnum.SYS_CONFIG, sysConfig.getConfigCode(), BeanUtil.beanToMap(sysConfig));
    }
}
