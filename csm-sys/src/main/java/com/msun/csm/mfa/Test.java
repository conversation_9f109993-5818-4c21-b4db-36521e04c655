package com.msun.csm.mfa;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

import org.apache.commons.codec.binary.Base64;

import com.google.zxing.WriterException;
import com.msun.csm.util.StringUtils;
import com.obs.services.internal.ServiceException;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class Test {


    public static void main(String[] args) {
        // 生成密钥
        String secretKey = GoogleAuthenticatorUtils.createSecretKey();
        String qrS = "ea91a0d6-6d6a-436b-b179-13b6a184b238";
      /*  boolean verification = GoogleAuthenticatorUtils.verification(qrS, "980269");
        if (verification) {
            log.info("成功");
        } else {
            log.error("失败");
        }*/
        String str = generateGoogleAuthQRCode(qrS, "zjb");
        log.info("str==>>" + str);
    }

    /**
     * 生成二维码的Base64编码
     * 可以使用手机应用Google Authenticator来扫描二维码进行绑定
     *
     * @param secretKey
     * @param accountName
     * @return
     */
    public static String generateGoogleAuthQRCode(String secretKey, String accountName) {
        // 生成二维码
        String qrStr;
        String issuer = "交付平台MFA";
        log.info("secretKey==>>" + secretKey);
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            // Demo_System 服务标识不参与运算，可任意设置
            String keyUri = GoogleAuthenticatorUtils.createKeyUri(secretKey, accountName, issuer);
            QRCodeUtils.writeToStream(keyUri, bos);
            qrStr = Base64.encodeBase64String(bos.toByteArray());
        } catch (WriterException | IOException e) {
            throw new ServiceException("生成二维码失败", e);
        }
        if (StringUtils.isEmpty(qrStr)) {
            throw new ServiceException("生成二维码失败");
        }
        return "data:image/png;base64," + qrStr;
    }


}
