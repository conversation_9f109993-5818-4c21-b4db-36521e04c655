package com.msun.csm.config;

import java.util.Date;

import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/18/10:32
 */
@Slf4j
@Component
public class MybatisObjectHandler implements MetaObjectHandler {

    private final UserHelper userHelper;

    public MybatisObjectHandler(UserHelper userHelper) {
        this.userHelper = userHelper;
    }

    /**
     * 插入时自动填写策略
     *
     * @param metaObject
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        log.debug("插入时填充数据");
        SysUserVO sysUserVO = getUser();
        setUserCreaterId(metaObject, sysUserVO);
        setUserUpdateId(metaObject, sysUserVO);
        this.strictInsertFill(metaObject, "createTime", Date.class, new Date());
        this.strictInsertFill(metaObject, "updateTime", Date.class, new Date());
    }

    /**
     * 更新时自动填写策略
     *
     * @param metaObject
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        log.info("更新时填充数据");
        setUserUpdateId(metaObject, getUser());
        this.strictUpdateFill(metaObject, "updateTime", Date.class, new Date());
    }

    /**
     * 新增时
     *
     * @param metaObject
     * @param sysUserVO
     */
    private void setUserCreaterId(MetaObject metaObject, SysUserVO sysUserVO) {
        if (this.getFieldValByName("createrId", metaObject) != null
                && !this.getFieldValByName("createrId", metaObject).equals(0L)) {
            return;
        }
        if (ObjectUtil.isNotEmpty(sysUserVO)) {
            this.setFieldValByName("createrId", sysUserVO.getSysUserId(), metaObject);
        } else {
            this.setFieldValByName("createrId", 0L, metaObject);
        }
    }

    /**
     * 更新时
     *
     * @param metaObject
     * @param sysUserVO
     */
    private void setUserUpdateId(MetaObject metaObject, SysUserVO sysUserVO) {
        if (this.getFieldValByName("updaterId", metaObject) != null
                && !this.getFieldValByName("updaterId", metaObject).equals(0L)) {
            return;
        }
        if (ObjectUtil.isNotEmpty(sysUserVO)) {
            this.setFieldValByName("updaterId", sysUserVO.getSysUserId(), metaObject);
        } else {
            this.setFieldValByName("updaterId", 0L, metaObject);
        }

    }

    private SysUserVO getUser() {
        SysUserVO sysUserVO = null;
        try {
            sysUserVO = userHelper.getCurrentUser();
        } catch (Throwable e) {
        }
        return sysUserVO;
    }
}
