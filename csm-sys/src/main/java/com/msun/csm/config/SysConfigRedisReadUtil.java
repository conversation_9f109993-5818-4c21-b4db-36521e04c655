package com.msun.csm.config;


import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.msun.csm.common.enums.config.RedisKeyPrefixEnum;
import com.msun.csm.util.StringUtils;

/**
 * @version : V1.52.0
 * @ClassName: SysConfigRedisReadUtil
 * @Description:
 * @Author: Yhongmin
 * @Date: 17:33 2024/5/13
 */
public class SysConfigRedisReadUtil {
    /**
     * 说明: 获取体统配置参数拼接
     *
     * @param configCode
     * @return:java.lang.String
     * @author: Yhongmin
     * @createAt: 2024/5/13 17:38
     * @remark: Copyright
     */
    public static String getConfigValueByConfigCode(String configCode) {
        String resultStr = null;
        if (org.apache.commons.lang3.StringUtils.isEmpty(configCode)) {
            return resultStr;
        }
        configCode = RedisKeyPrefixEnum.SYS_CONFIG.getKeyPrefix() + configCode;
        Map configMap = Global.hmget(configCode);
        if (configMap == null) {
            return resultStr;
        } else {
            resultStr = configMap.get("configValue") != null ? configMap.get("configValue").toString() : null;
        }
        return resultStr;
    }

    /**
     * 说明: 获取整条-配置缓存信息
     *
     * @param configCode
     * @return:java.lang.String
     * @author: Yhongmin
     * @createAt: 2024/5/14 9:27
     * @remark: Copyright
     */
    public static Map getConfigByConfigCode(String configCode) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(configCode)) {
            return null;
        }
        configCode = RedisKeyPrefixEnum.SYS_CONFIG.getKeyPrefix() + configCode;
        return Global.hmget(configCode);
    }

    /**
     * 说明: 存储缓存
     *
     * @param keyPrefixEnum
     * @param keyStr
     * @param dataMap
     * @return:java.lang.Boolean
     * @author: Yhongmin
     * @createAt: 2024/5/14 9:16
     * @remark: Copyright
     */
    public static Boolean putAllMap(RedisKeyPrefixEnum keyPrefixEnum, Object keyStr, Map dataMap) {
        Boolean resultbool = false;
        if (keyPrefixEnum == null || keyStr == null) {
            return resultbool;
        }
        String cacheStr = (keyPrefixEnum.getKeyPrefix() + keyStr.toString());
        return Global.hmsetRedis(cacheStr, dataMap);
    }

    public static Object getConfigValueByConfigTypeEnum(Map dataMap) {
        if (dataMap == null || dataMap.isEmpty()) {
            return null;
        }
        String configType = dataMap.get("configType") != null ? dataMap.get("configType").toString() : null;
        switch (configType) {
            case "TYPE_BOOL":
                return dataMap.get("configValue") != null ? Boolean.valueOf(dataMap.get("configValue").toString()) : null;
            case "TYPE_JSON":
                return dataMap.get("configValue") != null && StringUtils.isJsonString(StringUtils.nvl(dataMap.get("configValue"))) ? JSONObject.parseObject(dataMap.get("configValue").toString()) : null;
            case "TYPE_TEXT":
                return dataMap.get("configValue") != null ? dataMap.get("configValue").toString() : null;
            case "TYPE_NUMBER":
                return dataMap.get("configValue") != null && StringUtils.isNumeric(StringUtils.nvl(dataMap.get("configValue"))) ? Integer.valueOf(dataMap.get("configValue").toString()) : null;
            case "TYPE_DATE":
                return dataMap.get("configValue") != null ? dataMap.get("configValue").toString() : null;
            case "TYPE_HOUR":
                return dataMap.get("configValue") != null ? dataMap.get("configValue").toString() : null;
            default:
                return null;
        }
    }
}
