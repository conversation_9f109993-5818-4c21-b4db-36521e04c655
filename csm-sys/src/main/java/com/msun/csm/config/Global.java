package com.msun.csm.config;

import java.util.Map;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.msun.csm.common.enums.config.RedisKeyPrefixEnum;
import com.msun.csm.common.enums.config.SysConfigEnum;
import com.msun.csm.model.vo.SysConfigVO;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserHelper;
import com.msun.csm.service.config.SysConfigService;
import com.msun.csm.util.RedisUtil;

import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @version : V1.52.0
 * @ClassName: Global
 * @Description:
 * @Author: Yhongmin
 * @Date: 16:02 2024/5/10
 */
@Slf4j
@Component
public class Global {
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private UserHelper userHelper;
    @Resource
    private SysConfigService sysConfigService;

    private static Global global;

    @PostConstruct
    public void initUtil() {
        log.info("初始化");
        global = this;
        global.redisUtil = this.redisUtil;
        global.userHelper = this.userHelper;
        global.sysConfigService = this.sysConfigService;
    }

    public static SysUserVO getSysUserVO() {
        log.info("getSysUserVO赋值");
        return global.userHelper.getCurrentUser();
    }

    public static Map<Object, Object> hmget(String key) {
        log.info("getSysUserVO赋值");
        return global.redisUtil.hmget(key);
    }

    public static boolean hmsetRedis(String key, Map<String, Object> map) {
        return global.redisUtil.hmset(key, map);
    }
//    /**
//     * 说明: 根据code获取配置内容未被数据处理
//     *
//     * @param sysConfigEnum
//     * @return:java.lang.String
//     * @author: Yhongmin
//     * @createAt: 2024/5/13 20:26
//     * @remark: Copyright
//     */
//    public static String getConfigValue(SysConfigEnum sysConfigEnum) {
//        String resultStr = SysConfigRedisReadUtil.getConfigValueByConfigCode(sysConfigEnum.getConfigCode());
//        // 判断redis 是否取出配置值 为空查询数据库
//        if (StringUtils.isEmpty(resultStr)) {
//            SysConfigVO sysConfig = global.sysConfigService.selectConfigByName(sysConfigEnum.getConfigCode());
//            if (sysConfig != null) {
//                resultStr = sysConfig.getConfigValue();
//                SysConfigRedisReadUtil.putAllMap(RedisKeyPrefixEnum.SYS_CONFIG, sysConfig.getConfigCode(), BeanUtil.beanToMap(sysConfig));
//            }
//        }
//        // 如果数据中也没有 调用默认值
//        if (StringUtils.isEmpty(resultStr)) {
//            resultStr = sysConfigEnum.getConfigValue();
//            log.info("获取系统默认配置 configName=【{}】, configValue=【{}】", sysConfigEnum, resultStr);
//        }
//        log.info("获取系统配置 configName=【{}】, configValue=【{}】", sysConfigEnum, resultStr);
//        return resultStr;
//    }

    /**
     * 说明: 根据code和配置类型获取配置内容被数据处理过
     *
     * @param sysConfigEnum
     * @return:java.lang.String
     * @author: Yhongmin
     * @createAt: 2024/5/13 20:27
     * @remark: Copyright
     */
    public static Object getConfigValueByConfigType(SysConfigEnum sysConfigEnum) {
        Map dataMap = SysConfigRedisReadUtil.getConfigByConfigCode(sysConfigEnum.getConfigCode());
        // 判断redis 是否取出配置值 为空查询数据库
        if (dataMap == null || dataMap.isEmpty()) {
            SysConfigVO sysConfig = global.sysConfigService.selectConfigByName(sysConfigEnum.getConfigCode());
            if (sysConfig != null) {
                SysConfigRedisReadUtil.putAllMap(RedisKeyPrefixEnum.SYS_CONFIG, sysConfig.getConfigCode(), BeanUtil.beanToMap(sysConfig));
                return SysConfigRedisReadUtil.getConfigValueByConfigTypeEnum(BeanUtil.beanToMap(sysConfig));
            }
        }
        return SysConfigRedisReadUtil.getConfigValueByConfigTypeEnum(dataMap);
    }


}
