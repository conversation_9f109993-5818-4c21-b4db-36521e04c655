//package com.msun.csm.config;
//
//import org.springframework.boot.ApplicationArguments;
//import org.springframework.boot.ApplicationRunner;
//import org.springframework.data.redis.listener.RedisMessageListenerContainer;
//import org.springframework.stereotype.Component;
//
//import lombok.Data;
//import lombok.extern.slf4j.Slf4j;
//
//import com.msun.csm.util.SpringUtils;
//
///**
// * 初始化工具类
// */
//@Slf4j
//@Data
//@Component
//public class PostApplicationRunner implements ApplicationRunner {
//
//    @Override
//    public void run(ApplicationArguments args) throws Exception {
//        addMessageListener();
//    }
//
//    public void addMessageListener() {
//        RedisMessageListenerContainer container = SpringUtils.getBean(RedisMessageListenerContainer.class);
//    }
//
//    private void initOuterApiQueue(RedisMessageListenerContainer container) {
//        container.addMessageListener(SpringUtils.getBean(ApiController.OutApiMessageListener.class), Const.OUT_API_TOPIC_UPDATE_FEE_STATUS);
//    }
//
//}