package com.msun.csm.controller.sysoperlog;

import java.util.Map;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.operlog.SysOperLogDTO;
import com.msun.csm.model.vo.operlog.SysOperLogVO;
import com.msun.csm.service.operlog.SysOperLogService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping ("sysOperLog")
@Api (tags = "操作日志")
public class SysOperLogController {

    /**
     * 初始化操作类型字典缓存
     */
    static {
        Log.LogOperType[] logOperTypes = Log.LogOperType.values();
        for (int i = 0; i < logOperTypes.length; i++) {
            Log.OPER_TYPE_MAP.put(logOperTypes[i].getCode(), logOperTypes[i].getDesc());
        }
    }

    @Resource
    private SysOperLogService sysOperLogService;

    /**
     * 分页查询操作日志
     *
     * @param dto
     * @return
     */
    @ApiOperation ("操作日志-分页查询")
    @PostMapping (value = "/selectOperLogList")
    Result<PageInfo<SysOperLogVO>> selectOperLogList(@RequestBody SysOperLogDTO dto) {
        return sysOperLogService.selectOperLogList(dto);
    }

    @ApiOperation ("操作日志-查询操作类型字典")
    @PostMapping (value = "/selectOperTypeList")
    Result<Map<Integer, String>> selectOperTypeList() {
        return Result.success(Log.OPER_TYPE_MAP);
    }
}
