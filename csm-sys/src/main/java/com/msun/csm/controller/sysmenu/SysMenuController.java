package com.msun.csm.controller.sysmenu;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.msun.csm.common.model.Result;
import com.msun.csm.dao.entity.SysMenu;
import com.msun.csm.model.dto.menu.RoleVsMenuSaveDTO;
import com.msun.csm.model.dto.role.SysRoleDTO;
import com.msun.csm.model.vo.menu.MenuListAndRoleListVO;
import com.msun.csm.model.vo.role.RoleDetailVO;
import com.msun.csm.service.sysmenu.SysMenuService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/05/14/8:53
 */
@Api (tags = "菜单")
@RestController
@RequestMapping ("/sysMenu")
public class SysMenuController {


    @Resource
    private SysMenuService menuService;

    /**
     * 菜单-新增菜单
     *
     * @param sysMenu
     */
    @ApiOperation ("菜单-新增菜单")
    @PostMapping (value = "/addMenu")
    Result addMenu(@RequestBody SysMenu sysMenu) {
        return menuService.addMenu(sysMenu);
    }


    /**
     * 查询全部菜单和全部角色
     */
    @ApiOperation ("查询全部菜单和全部角色")
    @PostMapping (value = "/selectMenuListAndRoleList")
    Result<MenuListAndRoleListVO> selectMenuListAndRoleList() {
        return menuService.selectMenuListAndRoleList();
    }

    /**
     * 根据角色id查询所属菜单
     */
    @ApiOperation ("根据角色id查询所属菜单")
    @PostMapping (value = "/selectRoleVsMenuDetail")
    Result<RoleDetailVO> selectRoleVsMenuDetail(@RequestBody SysRoleDTO dto) {
        return menuService.selectRoleVsMenuDetail(dto.getSysRoleId());
    }

    /**
     * 保存角色关联菜单数据
     */
    @ApiOperation ("保存角色关联菜单数据")
    @PostMapping (value = "/roleSaveMenuList")
    Result roleSaveMenuList(@RequestBody RoleVsMenuSaveDTO dto) {
        return menuService.roleSaveMenuList(dto);
    }
}
