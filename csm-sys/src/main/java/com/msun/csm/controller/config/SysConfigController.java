package com.msun.csm.controller.config;

import javax.annotation.Resource;

import com.msun.csm.common.exception.CustomException;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.msun.core.springcloud.api.validator.group.SaveGroup;
import com.msun.core.springcloud.api.validator.group.UpdateGroup;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.SysConfigDTO;
import com.msun.csm.service.config.SysConfigService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 配置表(SysConfig)控制器
 *
 * <AUTHOR>
 * @since 2024-05-13 17:07:12
 */

@RestController
@Api(tags = "配置表")
@RequestMapping("/sys_config")
public class SysConfigController {

    @Resource
    private SysConfigService sysConfigService;

    @ApiOperation("测试")
    @PostMapping("/test/{a}/{b}")
    public Result<Number> test(@PathVariable Integer a, @PathVariable Integer b) {
        try {
            return Result.success(a / b);
        } catch (Throwable e) {
            throw new CustomException("报错了", e);
        }
    }


    /**
     * 保存
     *
     * @param dto 数据传输对象
     */
    @ApiOperation("保存")
    @PostMapping("/save")
    public Result<String> save(@ApiParam(value = "信息", example = "信息", required = true) @Validated(SaveGroup.class) @RequestBody SysConfigDTO dto) {
        return this.sysConfigService.save(dto);
    }

    /**
     * 更新
     *
     * @param dto
     */
    @ApiOperation("更新")
    @PostMapping("/update")
    public Result<String> update(@ApiParam(value = "信息", example = "信息", required = true) @Validated(UpdateGroup.class) @RequestBody SysConfigDTO dto) {
        return this.sysConfigService.update(dto);
    }

    /**
     * 作废
     *
     * @param id 作废业务ID
     */
    @ApiOperation("作废")
    @GetMapping("/updateMcInvalid")
    public Result<String> updateMcInvalid(@ApiParam(value = "id业务主键", example = "id业务主键", required = true) Long id) {
        return this.sysConfigService.updateMcInvalid(id);
    }

    /**
     * 说明: 刷新缓存
     *
     * @param configCode
     * @return:com.msun.csm.common.model.Result<java.lang.String>
     * @author: Yhongmin
     * @createAt: 2024/8/21 9:26
     * @remark: Copyright
     */
    @ApiOperation("刷新缓存")
    @GetMapping("/refreshCacheSysConfig")
    public Result<String> refreshCacheSysConfig(@ApiParam(value = "configCode", example = "configCode", required = true) String configCode) {
        return this.sysConfigService.refreshCacheSysConfig(configCode);
    }

}
