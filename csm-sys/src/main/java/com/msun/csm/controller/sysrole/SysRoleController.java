package com.msun.csm.controller.sysrole;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.role.RoleVsMenuDTO;
import com.msun.csm.model.dto.role.SysRoleDTO;
import com.msun.csm.model.dto.role.SysRolePageDTO;
import com.msun.csm.model.dto.user.UserVsRoleDTO;
import com.msun.csm.model.vo.menu.RouterMenuVO;
import com.msun.csm.model.vo.menu.SysMenuVO;
import com.msun.csm.model.vo.role.SysRoleVO;
import com.msun.csm.service.sysrole.SysRoleService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/18/11:03
 */
@Api (tags = "角色信息")
@RestController
@RequestMapping ("/sysRoles")
public class SysRoleController {

    @Resource
    private SysRoleService sysRoleService;

    /**
     * 添加角色
     *
     * @param dto
     */
    @Log (operName = "角色信息", operDetail = "角色信息-增加", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "角色信息-增加")
    @ApiOperation ("角色信息-增加或修改")
    @PostMapping (value = "/saveOrUpdateRole")
    Result saveOrUpdateRole(@RequestBody SysRoleDTO dto) {
        return sysRoleService.saveOrUpdateRole(dto);
    }

    /**
     * 删除角色
     *
     * @param dto
     * @return
     */
    @Log (operName = "角色信息", operDetail = "角色信息-删除", operLogType = Log.LogOperType.DEL,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "角色信息-删除")
    @ApiOperation ("角色信息-删除")
    @PostMapping (value = "/removeRole")
    Result removeRole(@RequestBody SysRoleDTO dto) {
        return sysRoleService.removeRole(dto);
    }

    /**
     * 修改角色
     *
     * @param dto
     * @return
     */
    @Log (operName = "角色信息", operDetail = "角色信息-修改", operLogType = Log.LogOperType.FIX,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "角色信息-修改")
    @ApiOperation ("角色信息-修改")
    @PostMapping (value = "/updateRole")
    Result updateRole(@RequestBody SysRoleDTO dto) {
        return sysRoleService.updateRole(dto);
    }


    /**
     * 查询角色
     *
     * @param dto
     * @return
     */
    @Log (operName = "角色信息", operDetail = "单个查询", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "角色信息-单个查询")
    @ApiOperation ("角色信息-单个查询")
    @PostMapping (value = "/getRole")
    Result<SysRoleVO> getRole(@RequestBody SysRoleDTO dto) {
        return sysRoleService.getRole(dto);
    }

    /**
     * 分页查询角色
     *
     * @param dto
     * @return
     */
    @Log (operName = "角色信息", operDetail = "分页查询", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "角色信息-分页查询")
    @ApiOperation ("角色信息-分页查询")
    @PostMapping (value = "/selectRoleListByPage")
    Result<PageInfo<SysRoleVO>> selectRoleListByPage(@RequestBody SysRolePageDTO dto) {
        return sysRoleService.selectRoleListByPage(dto);
    }

    /**
     * 根据角色查询菜单列表并增加默认
     *
     * @param dto
     * @return
     */
    @Log (operName = "角色信息", operDetail = "角色信息", operLogType = Log.LogOperType.SEARCH,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "角色信息-根据角色查询菜单列表并增加默认")
    @ApiOperation ("角色信息-根据角色查询菜单列表并增加默认")
    @PostMapping (value = "/findMenuByRole")
    Result<List<RouterMenuVO>> findMenuByRole(@RequestBody UserVsRoleDTO dto) {
        return sysRoleService.findMenuByRole(dto);
    }

    /**
     * 角色添加菜单
     *
     * @param dtoList
     * @return
     */
    @Log (operName = "角色信息", operDetail = "角色信息-角色添加菜单", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "角色信息-角色添加菜单")
    @ApiOperation ("角色信息-角色添加菜单")
    @PostMapping (value = "/roleSaveMenu")
    Result<List<SysMenuVO>> roleSaveMenu(@RequestBody List<RoleVsMenuDTO> dtoList) {
        return sysRoleService.roleSaveMenu(dtoList);
    }

    /**
     * 查询全部角色
     *
     * @param dto
     * @return
     */
    @Log (operName = "角色信息", operDetail = "角色信息-查询全部角色", operLogType = Log.LogOperType.ADD,
            intLogType = Log.IntLogType.OPER_SYSTEM, cnName = "角色信息-查询全部角色")
    @ApiOperation ("角色信息-查询全部角色")
    @PostMapping (value = "/selectRoleList")
    Result<List<SysRoleVO>> selectRoleList(@RequestBody SysRoleDTO dto) {
        return sysRoleService.selectRoleList(dto);
    }
}
