package com.msun.csm.controller.sysuser;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.msun.csm.common.annotation.ACROSS;
import com.msun.csm.common.annotation.Log;
import com.msun.csm.common.model.Result;
import com.msun.csm.model.dto.UserVsRoleSaveDTO;
import com.msun.csm.model.dto.user.LoginDTO;
import com.msun.csm.model.dto.user.LoginMfaDTO;
import com.msun.csm.model.dto.user.SysUserDTO;
import com.msun.csm.model.dto.user.UserVsRoleDTO;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.service.sysuser.SysUserService;
import com.msun.csm.util.StringUtils;
import com.msun.csm.util.logincaptcha.CaptchaConstant;
import com.msun.csm.util.logincaptcha.CaptchaDTO;
import com.msun.csm.util.logincaptcha.CaptchaEnum;
import com.msun.csm.util.logincaptcha.CaptchaUtil;
import com.msun.csm.util.logincaptcha.CaptchaVO;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024-04-17
 */
@Slf4j
@Api (tags = "人员信息")
@RestController
@RequestMapping ("/sysUser")
public class SysUserController {

    @Resource
    private SysUserService userService;

    /**
     * 注册验证码
     *
     * @param request
     * @return 验证码图片信息
     */
    @ACROSS
    @GetMapping ("register")
    public Result register(HttpServletRequest request) {
        String captchaPath = System.getProperty("user.dir")
                + StrUtil.SLASH + "tmpFile" + StrUtil.SLASH + "loginImg";
        log.info("生成注册验证码" + captchaPath);
        CaptchaDTO captchaDTO = new CaptchaUtil().createCaptcha(captchaPath);
        if (captchaDTO == null) {
            return Result.fail("图片处理异常");
        }
        // 生成注册token
        String token = captchaDTO.getToken();
        // session中保存token和x坐标
        HttpSession session = request.getSession();
        session.setAttribute(CaptchaConstant.TOKEN, token);
        session.setAttribute(CaptchaConstant.X, captchaDTO.getX());
        return Result.success(new CaptchaVO(captchaDTO.getSliceImg(), captchaDTO.getBgImg(), token, captchaDTO.getY()));
    }


    /**
     * 拼图校验与登录
     *
     * @param dto
     * @return
     */
    @Log (operName = "人员信息", operDetail = "人员信息-登录",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "人员信息-登录系统")
    @ACROSS
    @ApiOperation ("人员信息-登录")
    @PostMapping (value = "/login")
    public Result login(@RequestBody @Valid LoginDTO dto, HttpServletRequest request) {
        if (StringUtils.isBlank(dto.getAccount())) {
            return Result.fail("请填写登录名");
        }
        if (StringUtils.isBlank(dto.getPassword())) {
            return Result.fail("请填写登录密码");
        }
        if (StringUtils.isBlank(dto.getToken())) {
            return Result.fail("无效的验证码，请刷新页面");
        }
        if (null == dto.getSliceX()) {
            return Result.fail(CaptchaEnum.JIGSAW_CAPTCHA_SLIDE_FAILED.msg());
        }
        // 从session中拿验证数据
        HttpSession session = request.getSession();
        String registerToken = (String) session.getAttribute(CaptchaConstant.TOKEN);
        Integer registerX = (Integer) session.getAttribute(CaptchaConstant.X);
        // 验证注册token
        if (!registerToken.equals(dto.getToken())) {
            return Result.fail(CaptchaEnum.JIGSAW_CAPTCHA_TOKEN_INVALID.msg());
        }
        // 验证滑动x坐标
        int diff = registerX - dto.getSliceX();
        if (diff < -CaptchaConstant.SLICE_DIFF_LIMIT || diff > CaptchaConstant.SLICE_DIFF_LIMIT) {
            return Result.fail(CaptchaEnum.JIGSAW_CAPTCHA_SLIDE_FAILED.msg());
        }
        return userService.login(dto, request);
    }

    /**
     * MFA校验与登录
     *
     * @param dto
     * @return
     */
    @Log (operName = "人员信息MFA", operDetail = "人员信息MFA-登录",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "人员信息MFA-登录系统")
    @ACROSS
    @ApiOperation ("人员信息MFA-登录")
    @PostMapping (value = "/loginMfa")
    public Result loginMfa(@RequestBody @Valid LoginMfaDTO dto, HttpServletRequest request) {
        if (StringUtils.isBlank(dto.getVerificationCode())) {
            return Result.fail("请填写验证码");
        }
        return userService.loginMfa(dto, request);
    }

    /**
     * 分页查询人员信息
     *
     * @param sysUserDTO
     * @return
     */
    @Log (operName = "人员信息", operDetail = "人员信息-分页查询",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "人员信息-分页查询")
    @ApiOperation ("人员信息-分页查询")
    @PostMapping (value = "/findPageList")
    public Result<PageInfo<SysUserVO>> findPageList(@RequestBody SysUserDTO sysUserDTO) {
        return userService.findPageList(sysUserDTO);
    }

    /**
     * 人员添加角色(支持批量添加)
     *
     * @param dtoList
     * @return
     */
    @Log (operName = "人员信息", operDetail = "人员信息-人员批量添加角色",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "人员信息-人员批量添加角色")
    @ApiOperation ("人员信息-人员批量添加角色")
    @PostMapping (value = "/userSaveRoles")
    public Result userSaveRoles(@RequestBody List<UserVsRoleDTO> dtoList) {
        return userService.userSaveRoles(dtoList);
    }

    /**
     * token验证
     *
     * @return
     */
    @Log (operName = "人员信息", operDetail = "人员信息-token验证",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "人员信息-token验证")
    @ApiOperation ("token验证")
    @PostMapping (value = "/validToken")
    public Result findPageList() {
        return Result.success();
    }

    /**
     * 人员增加角色批量--根据部门进行批量添加
     *
     * @return
     */
    @Log (operName = "人员信息", operDetail = "人员信息-人员增加角色批量--根据部门进行批量添加",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "人员信息-人员增加角色--根据部门进行批量添加")
    @ApiOperation ("人员增加角色批量--根据部门进行批量添加")
    @PostMapping (value = "/userAddRoleList")
    public Result userAddRoleList(SysUserDTO dto) {
        return userService.userAddRoleList(dto);
    }

    /**
     * 人员增加角色--单个
     *
     * @return
     */
    @Log (operName = "人员信息", operDetail = "人员信息-人员增加角色--单个",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "人员信息-人员增加角色--单个")
    @ApiOperation ("人员增加角色--单个")
    @PostMapping (value = "/userAddRole")
    public Result userAddRole(SysUserDTO dto) {
        return userService.userAddRole(dto);
    }

    /**
     * 校验token是否超时
     *
     * @return
     */
    @Log (operName = "校验token是否超时", operDetail = "校验token是否超时",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "校验token是否超时")
    @ApiOperation ("校验token是否超时")
    @GetMapping (value = "/tokenCheck")
    public Result tokenCheck() {
        return userService.tokenCheck();
    }

    /**
     * 分页查询人员信息
     *
     * @return
     */
    @Log (operName = "分页查询人员信息", operDetail = "分页查询人员信息",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "分页查询人员信息")
    @ApiOperation ("分页查询人员信息")
    @PostMapping (value = "/selectUserByPage")
    public Result selectUserByPage(@RequestBody SysUserDTO sysUserDTO) {
        return userService.selectUserByPage(sysUserDTO);
    }

    /**
     * 查询人员下全部角色
     *
     * @return
     */
    @Log (operName = "查询人员下全部角色", operDetail = "查询人员下全部角色",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "查询人员下全部角色")
    @ApiOperation ("查询人员下全部角色")
    @PostMapping (value = "/findRoleByUser")
    public Result findRoleByUser(@RequestBody UserVsRoleDTO dto) {
        return userService.findRoleByUser(dto);
    }

    /**
     * 查询人员下全部角色
     *
     * @return
     */
    @Log (operName = "根据人员id查询角色idList", operDetail = "根据人员id查询角色idList",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "根据人员id查询角色idList")
    @ApiOperation ("根据人员id查询角色idList")
    @PostMapping (value = "/findRoleIdsByUser")
    public Result findRoleIdsByUser(@RequestBody UserVsRoleSaveDTO dto) {
        return userService.findRoleIdsByUser(dto);
    }


    /**
     * 根据人员id添加角色
     *
     * @param dto
     * @return
     */
    @Log (operName = "根据人员id添加角色", operDetail = "根据人员id添加角色",
            intLogType = Log.IntLogType.SELF_SYS, cnName = "根据人员id添加角色")
    @ApiOperation ("根据人员id添加角色")
    @PostMapping (value = "/saveRoleByUserId")
    public Result saveRoleByUserId(@RequestBody UserVsRoleSaveDTO dto) {
        return userService.saveRoleByUserId(dto);
    }

    /**
     * 用户退出登录
     *
     * @return
     */
    @Log (operName = "用户退出登录", operDetail = "用户退出登录", intLogType = Log.IntLogType.SELF_SYS,
            cnName = "用户退出登录")
    @ApiOperation ("用户退出登录")
    @PostMapping (value = "/logout")
    public Result logout(HttpServletRequest request) {
        return userService.logout(request);
    }

    /**
     * 清空登录用户的mfa认证
     *
     * @param dto
     * @return
     */
    @Log (operName = "清空登录用户的mfa认证", operDetail = "清空登录用户的mfa认证", intLogType = Log.IntLogType.SELF_SYS,
            cnName = "清空登录用户的mfa认证")
    @ApiOperation ("清空登录用户的mfa认证")
    @PostMapping (value = "/mfaRemoveData")
    public Result mfaRemoveData(@RequestBody LoginDTO dto) {
        return userService.mfaRemoveData(dto);
    }


}
