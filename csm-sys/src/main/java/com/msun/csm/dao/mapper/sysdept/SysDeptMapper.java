package com.msun.csm.dao.mapper.sysdept;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.common.model.BaseIdNameResp;
import com.msun.csm.dao.entity.SysDept;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/23/10:57
 */
@Mapper
public interface SysDeptMapper extends BaseMapper<SysDept> {

    /**
     * 根据运营id查询数据
     *
     * @param contractSignTeamId
     * @return
     */
    SysDept selectYunYingId(@Param("deptYunyingId") Long contractSignTeamId);

    /**
     * 根据关键字查询数据
     *
     * @param keyword
     * @return
     */
    List<BaseIdNameResp> selectByKeyword(@Param("keyword") String keyword);

    /**
     * 根据部门类型查询部门信息
     * @param deptType
     * @return
     */
    List<BaseIdNameResp> queryDeptByParamer(Integer deptType);

    List<SysDept> selectDeptOptions();

    SysDept getMyDeptByUid(@Param("uid") Long uid);
}
