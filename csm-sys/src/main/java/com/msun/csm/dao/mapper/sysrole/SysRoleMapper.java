package com.msun.csm.dao.mapper.sysrole;

import java.util.List;

import com.msun.csm.dao.entity.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.SysRole;
import com.msun.csm.model.dto.role.SysRoleDTO;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/18/10:51
 */
@Mapper
public interface SysRoleMapper extends BaseMapper<SysRole> {

    /**
     * 删除角色
     *
     * @param dto
     */
    void removeById(SysRoleDTO dto);

    /**
     * 通过userid查询用户角色
     *
     * @param userId
     * @return
     */
    List<SysRoleDTO> selectRoleByUserId(@Param ("userId") Long userId);

    /**
     * 根据角色id查询该角色下的人员id
     *
     * @param roleId
     * @return
     */
    List<Long> selectUserListByRole(@Param ("roleId") Long roleId);

    List<SysUser> selectUsersByRoleCodes(@Param("roleCodes") List<String> roleCodes);
}
