package com.msun.csm.dao.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 人员表
 * @Author: MengChuAn
 * @Date: 2024-04-17
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName (schema = "csm")
public class SysUser extends BasePO implements Serializable {

    @ApiModelProperty ("主键id")
    @TableId (type = IdType.INPUT)
    private Long sysUserId;

    @ApiModelProperty ("运营平台对应人员id")
    private String userYunyingId;

    @ApiModelProperty ("用户名称")
    private String userName;

    @ApiModelProperty ("账号")
    private String account;

    @ApiModelProperty ("密码")
    private String password;

    @ApiModelProperty ("邮箱")
    private String email;

    @ApiModelProperty ("手机号")
    private String phone;

    @ApiModelProperty ("性别")
    private String sex;

    @ApiModelProperty ("部门id")
    private Long deptId;

    @ApiModelProperty ("是否为院内用户【0：否；1：是】")
    private Integer hisUserFlag;

    @ApiModelProperty ("密钥")
    private String secretKey;

    @TableField(exist = false)
    private Boolean isLeader;
    @TableField(exist = false)
    private Long roleId;
    @TableField(exist = false)
    private String roleName;

}
