package com.msun.csm.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 人员关联角色
 *
 * @Author: duxu
 * @Date: 2024/04/18/16:54
 */
@Data
@TableName (schema = "csm", value = "sys_user_vs_role")
public class UserVsRole extends BasePO {

    @ApiModelProperty ("主键id")
    @TableId (type = IdType.INPUT)
    private Long id;

    @ApiModelProperty ("人员id")
    private Long userId;

    @ApiModelProperty ("角色id")
    private Long roleId;

    @ApiModelProperty ("是否默认【0：否；1：是】")
    private Integer defaultFlag;

    @ApiModelProperty("是否为主角色【0：否；1：是】")
    private Integer isMainRole;
}
