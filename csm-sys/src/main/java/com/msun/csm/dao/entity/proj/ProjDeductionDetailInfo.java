package com.msun.csm.dao.entity.proj;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 重构后质管验收扣分统一表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjDeductionDetailInfo {
    /**
    * 主键
    */
    private Long projDeductionDetailInfoId;

    /**
    * 逻辑删除【0：否；1：是】
    */
    private Integer isDeleted;

    /**
    * 创建人id
    */
    private Long createrId;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 修改人id
    */
    private Long updaterId;

    /**
    * 修改时间
    */
    private Date updateTime;

    /**
    * 项目ID
    */
    private Long projectInfoId;

    /**
    * 产品ID
    */
    private Long yyProductId;

    /**
    * 扣分来源：首次验收(first)/最终验收(final)
    */
    private String source;

    /**
    * 考核指标/分类编码（优先存储二级编码）
    */
    private String classificationCode;

    /**
    * 扣分项类型
    */
    private String deductionType;

    /**
    * 预估扣分
    */
    private BigDecimal estimatedDeduction;

    /**
    * 实际扣分
    */
    private BigDecimal practicalDeduction;

    /**
    * 扣分备注
    */
    private String remark;

    /**
    * 后端运维确认状态：1-未确认、2-已确认、3-已驳回
    */
    private Integer detailStatus;

    /**
    * 后端运维备注
    */
    private String backendRemark;

    /**
    * 附件主键，来自sys_file或者project_file
    */
    private String attachmentId;

    /**
    * 产品应用功能点扣分的功能编码
    */
    private String functionCode;

    /**
    * 产品应用功能点的使用次数
    */
    private Integer useCount;

    /**
    * 项目阶段编码
    */
    private String projectStageCode;

    /**
    * 扣分明细数据，例如扣分的设备的厂商、型号，小硬件的名称等
    */
    private String itemDetail;

    /**
    * 扣分了明细新增的时间
    */
    private Date itemAddTime;

    /**
    * 项目里程碑节点编码
    */
    private String milestoneNodeCode;

    /**
    * 项目文件表主键
    */
    private Long projectFileId;

    /**
    * 里程碑字典表主键
    */
    private Long milestoneNodeId;
}