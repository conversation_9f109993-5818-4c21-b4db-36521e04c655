package com.msun.csm.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.model.BaseEntity;

import lombok.Data;

/**
 * 配置表(SysConfig)实体类
 *
 * <AUTHOR>
 * @since 2024-05-13 16:37:40
 */
@Data
@TableName("sys_config")
public class SysConfig extends BaseEntity {
    /**
     * 业务主键
     */
    protected Long id;
    /**
     * 配置编码
     */
    private String configCode;
    /**
     * 配置名称
     */
    private String configName;
    /**
     * 配置值
     */
    private String configValue;
    /**
     * 配置描述
     */
    private String configDesc;

    /**
     * 排序
     */
    private Integer orderNo;
    /**
     * 值类型来自于枚举
     */
    private String configType;

    /**
     * 是否显示该配置项0:不显示1：显示,默认不显示
     */
    private Integer displayFlag;
    /**
     * 多选，下拉，开关，初始化值
     */
    private String configOptionalValue;

    /**
     * 配置分类：配置分类：1系统配置、2运营平台处罚类配置、3其他待扩展
     */
    private Integer configClass;
}
