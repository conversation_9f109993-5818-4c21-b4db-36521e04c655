package com.msun.csm.dao.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.config.BooleanTypeCustomHandler;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 菜单表
 *
 * @Author: duxu
 * @Date: 2024/04/18/10:41
 */
@Data
@TableName (schema = "csm")
public class SysMenu extends BasePO implements Serializable {
    /**
     * 主键id
     */
    @ApiModelProperty ("主键id")
    @TableId (type = IdType.INPUT)
    private Long sysMenuId;

    /**
     * 菜单名称
     */
    @ApiModelProperty ("菜单名称")
    private String menuName;

    /**
     * 菜单code
     */
    @ApiModelProperty ("菜单Code")
    private String menuCode;

    /**
     * 菜单图标
     */
    @ApiModelProperty ("菜单图标")
    private String menuIcon;

    /**
     * url地址
     */
    @ApiModelProperty ("菜单地址")
    private String menuUrl;

    /**
     * 排序
     */
    @ApiModelProperty ("菜单排序")
    private Integer menuSort;

    /**
     * 父节点
     */
    @ApiModelProperty ("父节点")
    private Long pid;

    @ApiModelProperty ("注册路径")
    private String component;

    @ApiModelProperty ("路由名称")
    private String routerName;

    @ApiModelProperty ("是否显示菜单 1 显示，0 隐藏")
    @TableField(value = "", typeHandler = BooleanTypeCustomHandler.class)
    private Boolean showMenu;


}
