package com.msun.csm.dao.mapper.sysuser;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.SysUser;
import com.msun.csm.model.dto.user.SysUserDTO;
import com.msun.csm.model.vo.user.SysUserPageVO;
import com.msun.csm.model.vo.user.SysUserVO;
import com.msun.csm.model.vo.user.UserVsRoleVO;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024-04-17
 */
@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {

    /**
     * 分页查询
     *
     * @param dto
     * @return
     */
    List<SysUserVO> findPageList(SysUserDTO dto);

    /**
     * 查询人员所属角色信息
     *
     * @param dto
     * @return
     */
    List<UserVsRoleVO> findRolesByUser(SysUserDTO dto);

    /**
     * 批量添加人员
     *
     * @param list
     * @return
     */
    int batchInsert(@Param("list") List<SysUser> list);

    /**
     * 批量修改人员信息
     *
     * @param list
     */
    void updateBatchSelective(@Param("list") List<SysUser> list);

    /**
     * 查询人员列表以及部门信息
     *
     * @param dto
     * @return
     */
    List<SysUserVO> selectUserList(SysUserDTO dto);

    /**
     * 根据roleId关联查询用户信息
     *
     * @param roleId
     * @return
     */
    List<SysUser> selectUserByRoleId(@Param("roleId") long roleId);

    /**
     * 根据id查询部门负责人账号
     *
     * @param deptId
     * @return
     */
    SysUser getDeptLeaderAccountByDeptId(@Param("deptId") long deptId);

    /**
     * 根据用户id查询用户信息
     *
     * @param userId
     * @return
     */
    SysUser getUserById(@Param("userId") long userId);

    /**
     * 根据用户id查询用户信息
     *
     * @param userId
     * @return
     */
    SysUser getUserByIdByIsDeleted(@Param("userId") long userId, @Param("isDeletedList") List<Integer> isDeletedList);

    /**
     * 根据部门id查询用户
     *
     * @param deptId
     * @return
     */
    List<SysUser> selectUsersByDeptId(@Param("deptId") long deptId);

    /**
     * 根据roleId关联查询用户信息
     *
     * @param deptId
     * @return
     */
    List<SysUser> selectUserByYYDeptId(@Param("deptId") long deptId);


    /**
     * 根据运营平台的人员Id获取 人员id
     *
     * @param yunyingId
     * @return
     */
    SysUser selectUserIdByYungyingId(@Param("yunyingId") String yunyingId);

    /**
     * 根据运营平台的人员Id获取 人员id
     *
     * @param yunyingId
     * @return
     */
    SysUser selectUserIdByYungyingIdIgnoreDeleted(@Param("yunyingId") String yunyingId);

    /**
     * 根据账号查询用户信息
     *
     * @param account
     * @return
     */
    SysUser selectByAccount(String account);

    /**
     * 查询人员信息--- 角色权限管理单独查询
     *
     * @param dto
     * @return
     */
    List<SysUserPageVO> selectUserByPage(SysUserDTO dto);

    /**
     * 查询所有人信息  ，不管是否为逻辑删除
     *
     * @param dto
     * @return
     */
    List<SysUser> selectAllUser(SysUserDTO dto);

    /**
     * 查询sysuserid
     *
     * @param code
     * @return
     */
    List<Long> selectUserIdByAccount(@Param("code") String code);

    /**
     * 根据老系统人员id查询新系统人员id
     *
     * @param imspUserId
     * @return
     */
    SysUser selectUserByImspUserId(@Param("imspUserId") Long imspUserId);

    /**
     * 删除MFA数据
     *
     * @param account
     */
    void mfaRemoveData(String account);

    /**
     * 根据角色id查询人员
     *
     * @return
     */
    List<Long> selectSysUsersByRoleIds(@Param("roleIds") List<Long> roleIds);

    /**
     * 根据id更新数据
     *
     * @param oldId
     * @param newId
     */
    void updateDataById(@Param("oldId") Long oldId, @Param("newId") Long newId);

    /**
     * 将云健康小组的人员添加运维工程师角色
     */
    void updateUserRole();

    /**
     * 根据运营平台人员id查询人员信息
     * @param userYunyingId
     * @return
     */
    List<SysUser> getUserVluesByYunyingId(@Param("userYunyingId") String userYunyingId);
}
