package com.msun.csm.dao.mapper.sysfile;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.SysFile;
import com.msun.csm.model.vo.file.FileBusinessCodeNameVO;
import com.msun.csm.model.vo.file.FileCodeTitleNameVO;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/28/17:52
 */
@Mapper
public interface SysFileMapper extends BaseMapper<SysFile> {
    List<SysFile> selectAll(SysFile sysFile);

    @Override
    int insert(SysFile sysFile);

    int updateByPrimaryKey(SysFile sysFile);

    int updateIsDelete(SysFile sysFile);

    SysFile selectByPrimaryKey(Long id);

    SysFile selectByFileCodeTopOne(SysFile sysFile);

    List<FileBusinessCodeNameVO> findBusinessCode();
    List<FileCodeTitleNameVO> fingCodeTitleName(String businessCode);

    List<SysFile> findBySysFileCodeBusinessCode(@Param("businessCode") String businessCode);

}
