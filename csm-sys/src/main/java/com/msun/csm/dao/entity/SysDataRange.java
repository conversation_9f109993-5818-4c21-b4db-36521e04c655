package com.msun.csm.dao.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 *
 */

@Data
@TableName ("sys_data_range")
@Schema (name = "SysDataRange对象", description = "SysDataRange对象")
public class SysDataRange {
    /**
     * 主键id
     */
    @Schema (description = "主键id")
    @TableId ("data_range_id")
    private Long dataRangeId;

    /**
     * 范围名称
     */
    @Schema (description = "范围名称")
    @TableField ("range_name")
    private String rangeName;

    /**
     * 范围编码
     */
    @Schema (description = "范围编码")
    @TableField ("range_code")
    private Short rangeCode;

    /**
     * 是否作废【0：否；1：是】
     */
    @Schema (description = "是否作废【0：否；1：是】")
    @TableField ("is_deleted")
    private Short isDeleted;

    /**
     * 创建用户
     */
    @Schema (description = "创建用户")
    @TableField ("creater_id")
    private Long createrId;

    /**
     * 创建时间
     */
    @Schema (description = "创建时间")
    @TableField (value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新用户
     */
    @Schema (description = "更新用户")
    @TableField ("updater_id")
    private Long updaterId;

    /**
     * 更新时间
     */
    @Schema (description = "更新时间")
    @TableField ("update_time")
    private Date updateTime;
}
