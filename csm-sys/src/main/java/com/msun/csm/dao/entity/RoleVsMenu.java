package com.msun.csm.dao.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 角色关联菜单表
 *
 * @Author: duxu
 * @Date: 2024/04/18/10:41
 */
@Data
@TableName (schema = "csm", value = "sys_role_vs_menu")
public class RoleVsMenu extends BasePO implements Serializable {
    /**
     * 主键id
     */
    @ApiModelProperty ("主键id")
    @TableId (type = IdType.INPUT)
    private Long id;

    /**
     * 角色id
     */
    @ApiModelProperty ("角色id")
    private Long roleId;

    /**
     * 菜单id
     */
    @ApiModelProperty ("菜单id")
    private Long menuId;
}
