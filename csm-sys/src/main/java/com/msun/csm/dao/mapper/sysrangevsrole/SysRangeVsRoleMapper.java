package com.msun.csm.dao.mapper.sysrangevsrole;

import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.SysRangeVsRole;

/**
 * <AUTHOR>
 * @since 2024-04-24 06:25:46
 */

@Mapper
public interface SysRangeVsRoleMapper extends BaseMapper<SysRangeVsRole> {

    List<SysRangeVsRole> findSysRangeVsRoleList(@Param ("roleCodes") Set<String> roleCodes);
}
