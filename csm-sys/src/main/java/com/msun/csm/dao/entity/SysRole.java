package com.msun.csm.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 角色表
 *
 * @Author: duxu
 * @Date: 2024/04/18/10:41
 */
@Data
@TableName (schema = "csm")
public class SysRole extends BasePO {

    @ApiModelProperty ("主键id")
    @TableId (type = IdType.INPUT)
    private Long sysRoleId;

    @ApiModelProperty ("角色名称")
    private String roleName;

    @ApiModelProperty ("角色编码")
    private String roleCode;
}
