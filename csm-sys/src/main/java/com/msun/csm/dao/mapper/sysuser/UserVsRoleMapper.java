package com.msun.csm.dao.mapper.sysuser;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.common.model.BaseIdCodeNameResp;
import com.msun.csm.dao.entity.UserVsRole;
import com.msun.csm.model.dto.user.UserVsRoleDTO;
import com.msun.csm.model.vo.user.UserVsRoleVO;

/**
 * Created with IntelliJ IDEA
 *
 * @Author: duxu
 * @Date: 2024/04/18/17:05
 */
@Mapper
public interface UserVsRoleMapper extends BaseMapper<UserVsRole> {


    /**
     * 批量更新
     *
     * @param list
     * @return
     */
    int updateBatchSelective(List<UserVsRole> list);

    /**
     * 批量添加
     *
     * @param list
     * @return
     */
    int batchInsert(@Param ("list") List<UserVsRole> list);


    /**
     * 插入或更新
     *
     * @param record
     * @return
     */
    int insertOrUpdateSelective(UserVsRole record);

    /**
     * 修改人员默认角色
     *
     * @param dto
     */
    void updateDefaultRole(UserVsRoleDTO dto);

    /**
     * 查询人员所属角色明细信息
     *
     * @param dto
     * @return
     */
    List<UserVsRoleVO> selectUserVsRoleDetail(UserVsRoleDTO dto);


    List<BaseIdCodeNameResp> getAuditor();

}
