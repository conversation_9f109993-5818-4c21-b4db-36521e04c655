package com.msun.csm.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.msun.csm.common.model.po.BasePO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName (schema = "csm")
public class SysIntLog extends BasePO {
    @TableId (type = IdType.INPUT)
    private Long sysIntLogId;

    @ApiModelProperty ("错误内容")
    private String errDetail;

    @ApiModelProperty ("接口类型")
    private Integer typeCode;

    @ApiModelProperty ("接口中文名称")
    private String cnIntName;

    @ApiModelProperty ("方法英文名称")
    private String methodName;

    @ApiModelProperty ("开发人员名称")
    private String deverName;
}
