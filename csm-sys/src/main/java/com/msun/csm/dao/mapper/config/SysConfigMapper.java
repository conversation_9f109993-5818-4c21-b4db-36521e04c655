package com.msun.csm.dao.mapper.config;


import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.msun.csm.dao.entity.SysConfig;


/**
 * 配置表(Config)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-13 15:02:30
 */
@Mapper
public interface SysConfigMapper extends BaseMapper<SysConfig> {


    /**
     * 作废
     *
     * @param record 配置信息
     * @return
     */
    int updateMcInvalid(SysConfig record);

    /**
     * 新增
     *
     * @param record 配置信息
     * @return
     */
    @Override
    int insert(SysConfig record);

    /**
     * 根据id查询
     *
     * @param id 配置信息
     * @return
     */
    SysConfig selectByPrimaryKey(Long id);
    /**
     * 说明: 根据code获取配置信息
     * @param configCode
     * @return:com.msun.csm.dao.entity.SysConfig
     * @author: Yhongmin
     * @createAt: 2024/5/13 17:22
     * @remark: Copyright
      */
    SysConfig selectConfigByName(String configCode);
    /**
     * 查询所有未作废的配置
     *
     * @param record 配置信息
     * @return
     */
    List<SysConfig> selectAllConfig();
    /**
     * 多条件查询
     *
     * @param record 配置信息
     * @return
     */
    List<SysConfig> findConfig(SysConfig record);

    /**
     * 修改
     *
     * @param record 配置信息
     * @return
     */
    int updateByPrimaryKey(SysConfig record);

    /**
     *  根据code批量查询
     * @param configCodes
     * @return
     */
    List<SysConfig> selectConfigByCodes(@Param("configCodes") List<String> configCodes);
}
