<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.sysdept.SysDeptMapper">
    <select id="selectYunYingId" resultType="com.msun.csm.dao.entity.SysDept">
        select *
        from sys_dept
        where dept_yunying_id = #{deptYunyingId}
    </select>

    <select id="selectByKeyword" resultType="com.msun.csm.common.model.BaseIdNameResp">
        select sys_dept_id "id",
        dept_name "name"
        from csm.sys_dept t
        where is_deleted = 0
        <if test="keyword != null">
            and t.dept_name like concat('%', #{keyword,jdbcType=VARCHAR}, '%')
        </if>
        order by t.sys_dept_id
    </select>
    <select id="queryDeptByParamer" resultType="com.msun.csm.common.model.BaseIdNameResp">
        select dept_yunying_id "id",
        dept_name "name"
        from csm.sys_dept t
        where is_deleted = 0
        <if test="deptType == 1">
            and dept_category = 'custService' and pid = 1030
        </if>
        <if test="deptType == 2">
            and dept_category = 'sales' and (dept_name like '%区域%' or dept_name like '%中心%')
        </if>
        <if test="deptType == 3">
            and dept_category = 'custService'
        </if>
        order by t.sys_dept_id
    </select>

    <select id="selectDeptOptions" resultType="com.msun.csm.dao.entity.SysDept">
        select a.dept_yunying_id,
               a.dept_name,
               a.dept_leader_yunying_id,
               a.dept_leader_yunying_name,
               b.sys_user_id as deptLeaderId
        from csm.sys_dept as a
                 inner join csm.sys_user as b on a.dept_leader_yunying_id = b.user_yunying_id
        where a.is_deleted = 0
          and b.is_deleted = 0
    </select>

    <select id="getMyDeptByUid" resultType="com.msun.csm.dao.entity.SysDept">
        select a.dept_yunying_id,
               a.dept_name,
               a.dept_leader_yunying_id,
               a.dept_leader_yunying_name,
               b.sys_user_id as deptLeaderId
        from csm.sys_dept as a
                 inner join csm.sys_user as b on a.dept_leader_yunying_id = b.user_yunying_id
                 inner join csm.sys_user as c on a.dept_yunying_id = c.dept_id
        where a.is_deleted = 0
          and b.is_deleted = 0
          and c.is_deleted = 0
          and c.sys_user_id = #{uid}
    </select>
</mapper>
