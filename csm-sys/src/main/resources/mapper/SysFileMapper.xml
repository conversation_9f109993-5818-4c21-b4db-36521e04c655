<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.sysfile.SysFileMapper">

    <resultMap type="com.msun.csm.dao.entity.SysFile" id="BaseResultMap">
        <id column="sys_file_id" jdbcType="BIGINT" property="sysFileId" />
        <result column="file_name" jdbcType="VARCHAR" property="fileName" />
        <result column="file_code" jdbcType="VARCHAR" property="fileCode" />
        <result column="file_path" jdbcType="VARCHAR" property="filePath" />
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
        <result column="creater_id" jdbcType="BIGINT" property="createrId" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="updater_id" jdbcType="BIGINT" property="updaterId" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="file_title" jdbcType="VARCHAR" property="fileTitle" />
        <result column="file_desc" jdbcType="VARCHAR" property="fileDesc" />
        <result column="version" jdbcType="VARCHAR" property="version" />
        <result column="version_desc" jdbcType="VARCHAR" property="versionDesc" />
        <result column="file_size" jdbcType="VARCHAR" property="fileSize" />
        <result column="file_used_mode" jdbcType="SMALLINT" property="fileUsedMode" />
        <result column="business_code" jdbcType="VARCHAR" property="businessCode" />
        <result column="business_desc" jdbcType="VARCHAR" property="businessDesc" />
    </resultMap>
    <sql id="Base_Column_List">
        sys_file_id, file_name, file_code, file_path, is_deleted, creater_id, create_time,
    updater_id, update_time, file_title, file_desc, version, version_desc, file_size,
    file_used_mode, business_code, business_desc
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from csm.sys_file
        where sys_file_id = #{sysFileId,jdbcType=BIGINT}
    </select>
    <select id="selectByFileCodeTopOne" parameterType="com.msun.csm.dao.entity.SysFile" resultMap="BaseResultMap">
        select * from (
        select
        <include refid="Base_Column_List"/>
        from csm.sys_file
        where is_deleted = 0
        <if test="fileCode != null">
            and file_code = #{fileCode,jdbcType=VARCHAR}
        </if>
        <if test="version != null">
            and version = #{version,jdbcType=VARCHAR}
        </if>
        order by create_time desc limit 1
        ) as tbl
    </select>
    <select id="fingCodeTitleName" resultType="com.msun.csm.model.vo.file.FileCodeTitleNameVO">
        select distinct file_title as name,
                        file_code  as id
        from csm.sys_file
        where is_deleted = 0
          and business_code = #{businessCode}
        order by file_code asc
    </select>
    <select id="findBusinessCode" resultType="com.msun.csm.model.vo.file.FileBusinessCodeNameVO">
        select
            distinct business_code as id,business_desc as name
        from csm.sys_file
        where is_deleted = 0
        order by business_code asc
    </select>
    <select id="findBySysFileCodeBusinessCode"  resultMap="BaseResultMap">
        with files as (
        select
        *
        from
        (
        select
        *,
        row_number() over(partition by an.file_code
        order by
        an.create_time desc) rn
        from
        csm.sys_file an
        where
        an.is_deleted = 0
        <if test="businessCode != null and businessCode !=''">
            and an.business_code=#{businessCode,jdbcType=VARCHAR}
        </if>
        ) t
        where
        t.rn = 1
        )
        select
        *
        from files
        inner join  csm.sys_file csde
        on csde.sys_file_id =files.sys_file_id
        where files.is_deleted = 0
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from csm.sys_file
        where sys_file_id = #{sysFileId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.SysFile">
        insert into csm.sys_file (sys_file_id, file_name, file_code,
                                  file_path, is_deleted, creater_id,
                                  create_time, updater_id, update_time,
                                  file_title, file_desc, version,
                                  version_desc, file_size, file_used_mode,
                                  business_code, business_desc)
        values (#{sysFileId,jdbcType=BIGINT}, #{fileName,jdbcType=VARCHAR}, #{fileCode,jdbcType=VARCHAR},
                #{filePath,jdbcType=VARCHAR}, #{isDeleted,jdbcType=SMALLINT}, #{createrId,jdbcType=BIGINT},
                #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP},
                #{fileTitle,jdbcType=VARCHAR}, #{fileDesc,jdbcType=VARCHAR}, #{version,jdbcType=VARCHAR},
                #{versionDesc,jdbcType=VARCHAR}, #{fileSize,jdbcType=VARCHAR}, #{fileUsedMode,jdbcType=SMALLINT},
                #{businessCode,jdbcType=VARCHAR}, #{businessDesc,jdbcType=VARCHAR})
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.SysFile">
        update csm.sys_file
        set file_name = #{fileName,jdbcType=VARCHAR},
            file_code = #{fileCode,jdbcType=VARCHAR},
            file_path = #{filePath,jdbcType=VARCHAR},
            is_deleted = #{isDeleted,jdbcType=SMALLINT},
            creater_id = #{createrId,jdbcType=BIGINT},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            updater_id = #{updaterId,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            file_title = #{fileTitle,jdbcType=VARCHAR},
            file_desc = #{fileDesc,jdbcType=VARCHAR},
            version = #{version,jdbcType=VARCHAR},
            version_desc = #{versionDesc,jdbcType=VARCHAR},
            file_size = #{fileSize,jdbcType=VARCHAR},
            file_used_mode = #{fileUsedMode,jdbcType=SMALLINT},
            business_code = #{businessCode,jdbcType=VARCHAR},
            business_desc = #{businessDesc,jdbcType=VARCHAR}
        where sys_file_id = #{sysFileId,jdbcType=BIGINT}
    </update>

    <select id="selectAll"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from csm.sys_file
        where is_deleted = 0
        <if test="fileName != null and fileName != ''">
            and file_name like concat('%',#{fileName},'%')
        </if>
        <if test="version != null and version != ''">
            and version =#{version}
        </if>
        order by create_time desc
    </select>
    <update id="updateIsDelete" parameterType="com.msun.csm.dao.entity.SysFile">
        update csm.sys_file
        set updater_id = #{updaterId,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            is_deleted = #{isDeleted,jdbcType=SMALLINT}
        where sys_file_id = #{sysFileId,jdbcType=BIGINT}
    </update>
</mapper>
