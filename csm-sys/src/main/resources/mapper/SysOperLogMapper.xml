<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.operlog.OperLogMapper">

    <select id="getProjprojectInfoById" parameterType="java.lang.Long" resultType="string">
        select project_name from csm.proj_project_info ppi where ppi.project_info_id =#{projectInfoId};
    </select>
</mapper>
