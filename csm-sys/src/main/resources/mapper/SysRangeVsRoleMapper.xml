<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.sysrangevsrole.SysRangeVsRoleMapper">
    <resultMap type="com.msun.csm.dao.entity.SysRangeVsRole" id="SysRangeVsRoleMap">
        <result property="rangeCode" column="range_code" jdbcType="INTEGER"/>
        <result property="roleCode" column="role_code" jdbcType="INTEGER"/>
    </resultMap>

    <select id="findSysRangeVsRoleList" resultMap="SysRangeVsRoleMap">
        select range_code, role_code from csm.sys_range_vs_role
        <where>
            1=2
            <if test="roleCodes != null and roleCodes.size > 0">
                and role_code in
                <foreach collection="roleCodes" item="code" separator="," open="(" close=")">
                    #{code}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
