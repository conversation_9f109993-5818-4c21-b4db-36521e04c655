<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.sysuser.UserVsRoleMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.dao.entity.UserVsRole">
        <!--@mbg.generated-->
        <!--@Table csm.sys_user_vs_role-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="role_id" jdbcType="BIGINT" property="roleId"/>
        <result column="default_flag" jdbcType="SMALLINT" property="defaultFlag"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, user_id, role_id, default_flag, is_deleted, creater_id, create_time, updater_id,
        update_time
    </sql>

    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update csm.sys_user_vs_role
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.userId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.userId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="role_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.roleId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.roleId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="default_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.defaultFlag != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.defaultFlag,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.isDeleted,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="creater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createrId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createrId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updater_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updaterId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.sys_user_vs_role
        (id, user_id, role_id, default_flag, is_deleted, creater_id, create_time, updater_id,is_main_role,
        update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.userId,jdbcType=BIGINT}, #{item.roleId,jdbcType=BIGINT},
            #{item.defaultFlag,jdbcType=SMALLINT}, #{item.isDeleted,jdbcType=SMALLINT},
            #{item.createrId,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.isMainRole,jdbcType=SMALLINT},
            #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.dao.entity.UserVsRole">
        <!--@mbg.generated-->
        insert into csm.sys_user_vs_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="roleId != null">
                role_id,
            </if>
            <if test="defaultFlag != null">
                default_flag,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="roleId != null">
                #{roleId,jdbcType=BIGINT},
            </if>
            <if test="defaultFlag != null">
                #{defaultFlag,jdbcType=SMALLINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="roleId != null">
                role_id = #{roleId,jdbcType=BIGINT},
            </if>
            <if test="defaultFlag != null">
                default_flag = #{defaultFlag,jdbcType=SMALLINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateDefaultRole" parameterType="com.msun.csm.model.dto.user.UserVsRoleDTO">
        update csm.sys_user_vs_role
        set default_flag = #{defaultFlag}
        where
        user_id = #{userId}
        <if test="id != null and id != ''">
            and id = #{id}
        </if>
    </update>

    <select id="selectUserVsRoleDetail" parameterType="com.msun.csm.model.dto.user.UserVsRoleDTO"
            resultType="com.msun.csm.model.vo.user.UserVsRoleVO">
        select sr.role_name,
        suvr.role_id,
        su.sys_user_id as "userId",
        su.user_name,
        suvr.is_main_role as "isMainRole"
        from csm.sys_user su
        left join csm.sys_user_vs_role suvr on su.sys_user_id = suvr.user_id and suvr.is_deleted = 0
        left join csm.sys_role sr on suvr.role_id = sr.sys_role_id and sr.is_deleted = 0
        where su.sys_user_id = #{userId}
        and su.is_deleted = 0
    </select>

    <select id="getAuditor" resultType="com.msun.csm.common.model.BaseIdCodeNameResp">
        select distinct
        on (su.sys_user_id,su.account,su.user_name ) su.sys_user_id as id ,su.account as code,su.user_name as name
        from csm.sys_user_vs_role suvr
            left join csm.sys_user su on suvr.user_id = su.sys_user_id and su.is_deleted = 0
            left join csm.sys_role sr on suvr.role_id = sr.sys_role_id and sr.is_deleted = 0
        where sr.role_code in ('OperationsEngineer', 'OperationsManager')
        and su.is_deleted = 0
        and suvr.is_deleted = 0
    </select>
</mapper>
