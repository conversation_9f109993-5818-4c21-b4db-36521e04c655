<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.dao.mapper.config.SysConfigMapper">

    <resultMap type="com.msun.csm.dao.entity.SysConfig" id="BaseResultMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="createrId" column="creater_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updaterId" column="updater_id" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="configCode" column="config_code" jdbcType="VARCHAR"/>
        <result property="configName" column="config_name" jdbcType="VARCHAR"/>
        <result property="configValue" column="config_value" jdbcType="VARCHAR"/>
        <result property="configDesc" column="config_desc" jdbcType="VARCHAR"/>
        <result property="orderNo" column="order_no" jdbcType="INTEGER"/>
        <result property="configType" column="config_type" jdbcType="VARCHAR"/>
        <result property="displayFlag" column="display_flag" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, creater_id, create_time, updater_id, update_time, is_deleted, config_code, config_name,
    config_value, config_desc, order_no, config_type, display_flag
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from csm.sys_config
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from csm.sys_config
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.msun.csm.dao.entity.SysConfig">
        insert into csm.sys_config (id, creater_id, create_time,
                                    updater_id, update_time, is_deleted,
                                    config_code, config_name, config_value,
                                    config_desc, order_no, config_type,
                                    display_flag)
        values (#{id,jdbcType=BIGINT}, #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
                #{updaterId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT},
                #{configCode,jdbcType=VARCHAR}, #{configName,jdbcType=VARCHAR}, #{configValue,jdbcType=VARCHAR},
                #{configDesc,jdbcType=VARCHAR}, #{orderNo,jdbcType=INTEGER}, #{configType,jdbcType=VARCHAR},
                #{displayFlag,jdbcType=SMALLINT})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.dao.entity.SysConfig">
        insert into csm.sys_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="configCode != null">
                config_code,
            </if>
            <if test="configName != null">
                config_name,
            </if>
            <if test="configValue != null">
                config_value,
            </if>
            <if test="configDesc != null">
                config_desc,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="configType != null">
                config_type,
            </if>
            <if test="displayFlag != null">
                display_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="configCode != null">
                #{configCode,jdbcType=VARCHAR},
            </if>
            <if test="configName != null">
                #{configName,jdbcType=VARCHAR},
            </if>
            <if test="configValue != null">
                #{configValue,jdbcType=VARCHAR},
            </if>
            <if test="configDesc != null">
                #{configDesc,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="configType != null">
                #{configType,jdbcType=VARCHAR},
            </if>
            <if test="displayFlag != null">
                #{displayFlag,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.msun.csm.dao.entity.SysConfig">
        update csm.sys_config
        <set>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
            <if test="configCode != null">
                config_code = #{configCode,jdbcType=VARCHAR},
            </if>
            <if test="configName != null">
                config_name = #{configName,jdbcType=VARCHAR},
            </if>
            <if test="configValue != null">
                config_value = #{configValue,jdbcType=VARCHAR},
            </if>
            <if test="configDesc != null">
                config_desc = #{configDesc,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="configType != null">
                config_type = #{configType,jdbcType=VARCHAR},
            </if>
            <if test="displayFlag != null">
                display_flag = #{displayFlag,jdbcType=SMALLINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.msun.csm.dao.entity.SysConfig">
        update csm.sys_config
        set
            updater_id = #{updaterId,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            is_deleted = #{isDeleted,jdbcType=SMALLINT},
            config_code = #{configCode,jdbcType=VARCHAR},
            config_name = #{configName,jdbcType=VARCHAR},
            config_value = #{configValue,jdbcType=VARCHAR},
            config_desc = #{configDesc,jdbcType=VARCHAR},
            order_no = #{orderNo,jdbcType=INTEGER},
            config_type = #{configType,jdbcType=VARCHAR},
            display_flag = #{displayFlag,jdbcType=SMALLINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectConfigByName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from csm.sys_config
        where config_code = #{configCode,jdbcType=VARCHAR}
        and is_deleted=0
    </select>
    <select id="selectAllConfig"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from csm.sys_config
        where is_deleted=0
    </select>
    <select id="selectConfigByCodes" resultType="com.msun.csm.dao.entity.SysConfig">
        select
        <include refid="Base_Column_List" />
        from csm.sys_config
        where config_code in
        <foreach collection="configCodes" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and is_deleted=0
        order by config_code
    </select>
</mapper>
