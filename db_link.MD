# 1、启用postgres_fdw 扩展
`CREATE EXTENSION IF NOT EXISTS postgres_fdw;`

# 2、在本地服务器跟外部服务器创建一个映射关系
`CREATE SERVER test_chis
FOREIGN DATA WRAPPER postgres_fdw
OPTIONS (host 'remote_host', dbname 'remote_db', port '5432');`

# 3、将远程表结构定义导入到本地
`IMPORT FOREIGN SCHEMA common
FROM SERVER test_chis
INTO test_chis_common;`

# 4、跨库关联查询
`SELECT a.*, b.*
FROM csm.dept a
INNER JOIN test_chis_common.dept_user b
ON a.id = b.id;`
