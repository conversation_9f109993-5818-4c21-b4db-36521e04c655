package com.msun.csm.mobile.dao.mapper.mobile;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.msun.csm.mobile.dao.entity.mobile.MobileRoleVsMenu;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/12/20
 */

@Mapper
public interface MobileRoleVsMenuMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(MobileRoleVsMenu record);

    int insertOrUpdate(MobileRoleVsMenu record);

    int insertOrUpdateSelective(MobileRoleVsMenu record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(MobileRoleVsMenu record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    MobileRoleVsMenu selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(MobileRoleVsMenu record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(MobileRoleVsMenu record);

    int updateBatch(@Param("list") List<MobileRoleVsMenu> list);

    int updateBatchSelective(@Param("list") List<MobileRoleVsMenu> list);

    int batchInsert(@Param("list") List<MobileRoleVsMenu> list);
}
