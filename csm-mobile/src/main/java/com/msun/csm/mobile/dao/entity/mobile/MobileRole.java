package com.msun.csm.mobile.dao.entity.mobile;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/12/20
 */

@ApiModel(description = "csm.mobile_role")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MobileRole {
    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long mobileRoleId;

    /**
     * 角色名称
     */
    @ApiModelProperty(value = "角色名称")
    private String roleName;

    /**
     * 角色编码
     */
    @ApiModelProperty(value = "角色编码")
    private String roleCode;

    /**
     * 创建用户
     */
    @ApiModelProperty(value = "创建用户")
    private Long createrId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新用户
     */
    @ApiModelProperty(value = "更新用户")
    private Long updaterId;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 是否作废【0：否；1：是】
     */
    @ApiModelProperty(value = "是否作废【0：否；1：是】")
    private Integer isDeleted;
}
