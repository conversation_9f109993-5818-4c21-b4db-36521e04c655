package com.msun.csm.mobile.dao.mapper.mobile;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.msun.csm.mobile.dao.entity.mobile.MobileMenu;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/12/20
 */

@Mapper
public interface MobileMenuMapper {
    /**
     * delete by primary key
     *
     * @param mobileMenuId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long mobileMenuId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(MobileMenu record);

    int insertOrUpdate(MobileMenu record);

    int insertOrUpdateSelective(MobileMenu record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(MobileMenu record);

    /**
     * select by primary key
     *
     * @param mobileMenuId primary key
     * @return object by primary key
     */
    MobileMenu selectByPrimaryKey(Long mobileMenuId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(MobileMenu record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(MobileMenu record);

    int updateBatch(@Param("list") List<MobileMenu> list);

    int updateBatchSelective(@Param("list") List<MobileMenu> list);

    int batchInsert(@Param("list") List<MobileMenu> list);


    /**
     * 根据角色id查询菜单
     *
     * @param roleId
     * @return
     */
    List<MobileMenu> selectByRoleId(Long roleId);
}
