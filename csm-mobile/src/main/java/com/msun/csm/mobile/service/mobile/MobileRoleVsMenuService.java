package com.msun.csm.mobile.service.mobile;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.msun.csm.mobile.dao.entity.mobile.MobileRoleVsMenu;
import com.msun.csm.mobile.dao.mapper.mobile.MobileRoleVsMenuMapper;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/12/20
 */

@Service
public class MobileRoleVsMenuService {

    @Autowired
    private MobileRoleVsMenuMapper mobileRoleVsMenuMapper;


    public int deleteByPrimaryKey(Long id) {
        return mobileRoleVsMenuMapper.deleteByPrimaryKey(id);
    }


    public int insert(MobileRoleVsMenu record) {
        return mobileRoleVsMenuMapper.insert(record);
    }


    public int insertOrUpdate(MobileRoleVsMenu record) {
        return mobileRoleVsMenuMapper.insertOrUpdate(record);
    }


    public int insertOrUpdateSelective(MobileRoleVsMenu record) {
        return mobileRoleVsMenuMapper.insertOrUpdateSelective(record);
    }


    public int insertSelective(MobileRoleVsMenu record) {
        return mobileRoleVsMenuMapper.insertSelective(record);
    }


    public MobileRoleVsMenu selectByPrimaryKey(Long id) {
        return mobileRoleVsMenuMapper.selectByPrimaryKey(id);
    }


    public int updateByPrimaryKeySelective(MobileRoleVsMenu record) {
        return mobileRoleVsMenuMapper.updateByPrimaryKeySelective(record);
    }


    public int updateByPrimaryKey(MobileRoleVsMenu record) {
        return mobileRoleVsMenuMapper.updateByPrimaryKey(record);
    }


    public int updateBatch(List<MobileRoleVsMenu> list) {
        return mobileRoleVsMenuMapper.updateBatch(list);
    }


    public int updateBatchSelective(List<MobileRoleVsMenu> list) {
        return mobileRoleVsMenuMapper.updateBatchSelective(list);
    }


    public int batchInsert(List<MobileRoleVsMenu> list) {
        return mobileRoleVsMenuMapper.batchInsert(list);
    }

}
