package com.msun.csm.mobile.service.mobile;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.msun.csm.mobile.dao.entity.mobile.MobileRole;
import com.msun.csm.mobile.dao.mapper.mobile.MobileRoleMapper;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/12/20
 */

@Service
public class MobileRoleService {

    @Autowired
    private MobileRoleMapper mobileRoleMapper;


    public int insert(MobileRole record) {
        return mobileRoleMapper.insert(record);
    }


    public int insertOrUpdate(MobileRole record) {
        return mobileRoleMapper.insertOrUpdate(record);
    }


    public int insertOrUpdateSelective(MobileRole record) {
        return mobileRoleMapper.insertOrUpdateSelective(record);
    }


    public int insertSelective(MobileRole record) {
        return mobileRoleMapper.insertSelective(record);
    }


    public int batchInsert(List<MobileRole> list) {
        return mobileRoleMapper.batchInsert(list);
    }

}
