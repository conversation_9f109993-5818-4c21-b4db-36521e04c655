package com.msun.csm.mobile.service.mobile;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.msun.csm.mobile.dao.entity.mobile.MobileUserVsRole;
import com.msun.csm.mobile.dao.mapper.mobile.MobileUserVsRoleMapper;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/12/20
 */

@Service
public class MobileUserVsRoleService {

    @Autowired
    private MobileUserVsRoleMapper mobileUserVsRoleMapper;


    public int deleteByPrimaryKey(Long id) {
        return mobileUserVsRoleMapper.deleteByPrimaryKey(id);
    }


    public int insert(MobileUserVsRole record) {
        return mobileUserVsRoleMapper.insert(record);
    }


    public int insertOrUpdate(MobileUserVsRole record) {
        return mobileUserVsRoleMapper.insertOrUpdate(record);
    }


    public int insertOrUpdateSelective(MobileUserVsRole record) {
        return mobileUserVsRoleMapper.insertOrUpdateSelective(record);
    }


    public int insertSelective(MobileUserVsRole record) {
        return mobileUserVsRoleMapper.insertSelective(record);
    }


    public MobileUserVsRole selectByPrimaryKey(Long id) {
        return mobileUserVsRoleMapper.selectByPrimaryKey(id);
    }


    public int updateByPrimaryKeySelective(MobileUserVsRole record) {
        return mobileUserVsRoleMapper.updateByPrimaryKeySelective(record);
    }


    public int updateByPrimaryKey(MobileUserVsRole record) {
        return mobileUserVsRoleMapper.updateByPrimaryKey(record);
    }


    public int updateBatch(List<MobileUserVsRole> list) {
        return mobileUserVsRoleMapper.updateBatch(list);
    }


    public int updateBatchSelective(List<MobileUserVsRole> list) {
        return mobileUserVsRoleMapper.updateBatchSelective(list);
    }


    public int batchInsert(List<MobileUserVsRole> list) {
        return mobileUserVsRoleMapper.batchInsert(list);
    }

}
