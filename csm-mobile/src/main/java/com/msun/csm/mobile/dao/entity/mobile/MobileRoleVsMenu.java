package com.msun.csm.mobile.dao.entity.mobile;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/12/20
 */

@ApiModel(description = "csm.mobile_role_vs_menu")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MobileRoleVsMenu {
    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 角色id
     */
    @ApiModelProperty(value = "角色id")
    private Long roleId;

    /**
     * 菜单id
     */
    @ApiModelProperty(value = "菜单id")
    private Long menuId;

    /**
     * 是否作废【0：否；1：是】
     */
    @ApiModelProperty(value = "是否作废【0：否；1：是】")
    private Integer isDeleted;

    /**
     * 创建用户
     */
    @ApiModelProperty(value = "创建用户")
    private Long createrId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新用户
     */
    @ApiModelProperty(value = "更新用户")
    private Long updaterId;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
