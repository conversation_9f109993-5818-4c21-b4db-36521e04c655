package com.msun.csm.mobile.service.mobile;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.msun.csm.common.model.Result;
import com.msun.csm.mobile.dao.entity.mobile.MobileMenu;
import com.msun.csm.mobile.dao.mapper.mobile.MobileMenuMapper;
import com.msun.csm.mobile.model.resp.menu.MenuResp;
import com.msun.csm.mobile.model.resp.menu.MenuRouter;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/12/20
 */

@Service
public class MobileMenuService {

    @Resource
    private MobileMenuMapper mobileMenuMapper;


    public int deleteByPrimaryKey(Long mobileMenuId) {
        return mobileMenuMapper.deleteByPrimaryKey(mobileMenuId);
    }


    public int insert(MobileMenu record) {
        return mobileMenuMapper.insert(record);
    }


    public int insertOrUpdate(MobileMenu record) {
        return mobileMenuMapper.insertOrUpdate(record);
    }


    public int insertOrUpdateSelective(MobileMenu record) {
        return mobileMenuMapper.insertOrUpdateSelective(record);
    }


    public int insertSelective(MobileMenu record) {
        return mobileMenuMapper.insertSelective(record);
    }


    public MobileMenu selectByPrimaryKey(Long mobileMenuId) {
        return mobileMenuMapper.selectByPrimaryKey(mobileMenuId);
    }


    public int updateByPrimaryKeySelective(MobileMenu record) {
        return mobileMenuMapper.updateByPrimaryKeySelective(record);
    }


    public int updateByPrimaryKey(MobileMenu record) {
        return mobileMenuMapper.updateByPrimaryKey(record);
    }


    public int updateBatch(List<MobileMenu> list) {
        return mobileMenuMapper.updateBatch(list);
    }


    public int updateBatchSelective(List<MobileMenu> list) {
        return mobileMenuMapper.updateBatchSelective(list);
    }


    public int batchInsert(List<MobileMenu> list) {
        return mobileMenuMapper.batchInsert(list);
    }

    /**
     * 获取企业微信菜单
     *
     * @return
     */
    public Result getMenu() {
        //功能暂时确实，默认都是管理员，查询所有菜单
        Long roleId = 1L;
        List<MobileMenu> menuList = mobileMenuMapper.selectByRoleId(roleId);
        List<MenuResp> menuRespList = new ArrayList<>();
        menuList.stream().filter(menu -> menu.getPid() == 0).forEach(
                menu -> {
                    MenuResp menuResp = new MenuResp();
                    menuResp.setId(menu.getMobileMenuId());
                    menuResp.setName(menu.getMenuName());
                    List<MenuRouter> menuRouterList = new ArrayList<>();
                    menuList.stream().filter(subMenu -> subMenu.getPid().equals(menu.getMobileMenuId())).forEach(
                            subMenu -> {
                                MenuRouter menuRouter = new MenuRouter();
                                menuRouter.setPath(subMenu.getMenuUrl());
                                menuRouter.setName(subMenu.getRouterName());
                                menuRouter.setComponent(subMenu.getComponent());
                                menuRouter.setIcon(subMenu.getMenuIcon());
                                menuRouter.setComponentName(subMenu.getMenuName());
                                menuRouterList.add(menuRouter);
                            }
                    );
                    menuResp.setChildren(menuRouterList);
                    menuRespList.add(menuResp);
                }
        );
        return Result.success(menuRespList);
    }

}
