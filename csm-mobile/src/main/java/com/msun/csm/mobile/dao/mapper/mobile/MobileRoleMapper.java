package com.msun.csm.mobile.dao.mapper.mobile;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.msun.csm.mobile.dao.entity.mobile.MobileRole;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/12/20
 */

@Mapper
public interface MobileRoleMapper {
    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(MobileRole record);

    int insertOrUpdate(MobileRole record);

    int insertOrUpdateSelective(MobileRole record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(MobileRole record);

    int batchInsert(@Param("list") List<MobileRole> list);
}
