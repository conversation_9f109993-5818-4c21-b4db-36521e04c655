package com.msun.csm.mobile.dao.mapper.mobile;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.msun.csm.mobile.dao.entity.mobile.MobileUserVsRole;

/**
 * @Description:
 * @Author: MengChuAn
 * @Date: 2024/12/20
 */

@Mapper
public interface MobileUserVsRoleMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(MobileUserVsRole record);

    int insertOrUpdate(MobileUserVsRole record);

    int insertOrUpdateSelective(MobileUserVsRole record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(MobileUserVsRole record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    MobileUserVsRole selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(MobileUserVsRole record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(MobileUserVsRole record);

    int updateBatch(@Param("list") List<MobileUserVsRole> list);

    int updateBatchSelective(@Param("list") List<MobileUserVsRole> list);

    int batchInsert(@Param("list") List<MobileUserVsRole> list);
}
