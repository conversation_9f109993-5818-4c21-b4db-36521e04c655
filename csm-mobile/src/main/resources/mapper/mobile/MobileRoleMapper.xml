<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.msun.csm.mobile.dao.mapper.mobile.MobileRoleMapper">
    <resultMap id="BaseResultMap" type="com.msun.csm.mobile.dao.entity.mobile.MobileRole">
        <!--@mbg.generated-->
        <!--@Table csm.mobile_role-->
        <result column="mobile_role_id" jdbcType="BIGINT" property="mobileRoleId"/>
        <result column="role_name" jdbcType="VARCHAR" property="roleName"/>
        <result column="role_code" jdbcType="VARCHAR" property="roleCode"/>
        <result column="creater_id" jdbcType="BIGINT" property="createrId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater_id" jdbcType="BIGINT" property="updaterId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        mobile_role_id, role_name, role_code, creater_id, create_time, updater_id, update_time,
        is_deleted
    </sql>
    <insert id="insert" parameterType="com.msun.csm.mobile.dao.entity.mobile.MobileRole">
        <!--@mbg.generated-->
        insert into csm.mobile_role (mobile_role_id, role_name, role_code,
        creater_id, create_time, updater_id,
        update_time, is_deleted)
        values (#{mobileRoleId,jdbcType=BIGINT}, #{roleName,jdbcType=VARCHAR}, #{roleCode,jdbcType=VARCHAR},
        #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT})
    </insert>
    <insert id="insertSelective" parameterType="com.msun.csm.mobile.dao.entity.mobile.MobileRole">
        <!--@mbg.generated-->
        insert into csm.mobile_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mobileRoleId != null">
                mobile_role_id,
            </if>
            <if test="roleName != null">
                role_name,
            </if>
            <if test="roleCode != null">
                role_code,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mobileRoleId != null">
                #{mobileRoleId,jdbcType=BIGINT},
            </if>
            <if test="roleName != null">
                #{roleName,jdbcType=VARCHAR},
            </if>
            <if test="roleCode != null">
                #{roleCode,jdbcType=VARCHAR},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into csm.mobile_role
        (mobile_role_id, role_name, role_code, creater_id, create_time, updater_id, update_time,
        is_deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.mobileRoleId,jdbcType=BIGINT}, #{item.roleName,jdbcType=VARCHAR}, #{item.roleCode,jdbcType=VARCHAR},
            #{item.createrId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updaterId,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=SMALLINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.msun.csm.mobile.dao.entity.mobile.MobileRole">
        <!--@mbg.generated-->
        insert into csm.mobile_role
        (mobile_role_id, role_name, role_code, creater_id, create_time, updater_id, update_time,
        is_deleted)
        values
        (#{mobileRoleId,jdbcType=BIGINT}, #{roleName,jdbcType=VARCHAR}, #{roleCode,jdbcType=VARCHAR},
        #{createrId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updaterId,jdbcType=BIGINT},
        #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=SMALLINT})
        on duplicate key update
        mobile_role_id = #{mobileRoleId,jdbcType=BIGINT},
        role_name = #{roleName,jdbcType=VARCHAR},
        role_code = #{roleCode,jdbcType=VARCHAR},
        creater_id = #{createrId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updater_id = #{updaterId,jdbcType=BIGINT},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        is_deleted = #{isDeleted,jdbcType=SMALLINT}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.msun.csm.mobile.dao.entity.mobile.MobileRole">
        <!--@mbg.generated-->
        insert into csm.mobile_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mobileRoleId != null">
                mobile_role_id,
            </if>
            <if test="roleName != null">
                role_name,
            </if>
            <if test="roleCode != null">
                role_code,
            </if>
            <if test="createrId != null">
                creater_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updaterId != null">
                updater_id,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mobileRoleId != null">
                #{mobileRoleId,jdbcType=BIGINT},
            </if>
            <if test="roleName != null">
                #{roleName,jdbcType=VARCHAR},
            </if>
            <if test="roleCode != null">
                #{roleCode,jdbcType=VARCHAR},
            </if>
            <if test="createrId != null">
                #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="mobileRoleId != null">
                mobile_role_id = #{mobileRoleId,jdbcType=BIGINT},
            </if>
            <if test="roleName != null">
                role_name = #{roleName,jdbcType=VARCHAR},
            </if>
            <if test="roleCode != null">
                role_code = #{roleCode,jdbcType=VARCHAR},
            </if>
            <if test="createrId != null">
                creater_id = #{createrId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=SMALLINT},
            </if>
        </trim>
    </insert>
</mapper>
